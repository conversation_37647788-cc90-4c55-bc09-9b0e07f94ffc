import { defaultProps } from '@litpage/sections';

import { BlockFormConfig } from '../types';

const defaultTeamProps = defaultProps.Team;

const teamConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      tagline: {
        type: "string",
        title: "Tagline",
        // minLength: 2,
        maxLength: 50,
        default: defaultTeamProps.tagline
      },
      title: {
        type: "string",
        title: "Main Title",
        minLength: 2,
        maxLength: 70,
        default: defaultTeamProps.title
      },
      description: {
        type: "string",
        title: "Description",
        minLength: 2,
        maxLength: 200,
        default: defaultTeamProps.description
      },
      people: {
        type: "array",
        title: "Team Members",
        description: "Add team members to display in the component",
        items: {
          type: "object",
          properties: {
            name: {
              type: "string",
              title: "Name",
              minLength: 2,
              maxLength: 50,
              default: defaultTeamProps.people[0]?.name || "<PERSON>"
            },
            role: {
              type: "string",
              title: "<PERSON>",
              minLength: 2,
              maxLength: 50,
              default: defaultTeamProps.people[0]?.role || "Co-Founder / CEO"
            },
            bio: {
              type: "string",
              title: "Biography",
              minLength: 2,
              maxLength: 300,
              default: defaultTeamProps.people[0]?.bio || "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nulla gravida neque in eros egestas, vel egestas sem ultricies."
            },
            imageUrl: {
              type: "string",
              title: "Profile Image URL",
              pattern: "^(https?://.*|/.+)$",
              default: defaultTeamProps.people[0]?.imageUrl || "https://images.unsplash.com/photo-1568602471122-7832951cc4c5?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=2&w=320&h=320&q=80"
            },
            socialLinks: {
              type: "array",
              title: "Socials",
              items: {
                type: "object",
                properties: {
                  platform: {
                    type: "string",
                    title: "Platform",
                    enum: ["x", "linkedin", "github", "facebook", "instagram", "youtube"],
                    default: "linkedin"
                  },
                  url: {
                    type: "string",
                    title: "URL",
                    default: "#"
                  }
                },
                required: ["platform", "url"]
              },
              default: defaultTeamProps.people[0]?.socialLinks || [
                { platform: "x", url: "#" },
                { platform: "linkedin", url: "#" },
                { platform: "github", url: "#" }
              ]
            }
          },
          required: ["name", "role", "imageUrl"]
        },
        default: defaultTeamProps.people
      },
      hiringSection: {
        type: "object",
        title: "Hiring Section",
        properties: {
          enabled: {
            type: "boolean",
            title: "Enable Hiring Section",
            default: defaultTeamProps.hiringSection.enabled
          }
        },
        dependencies: {
          enabled: {
            oneOf: [
              {
                properties: {
                  enabled: {
                    enum: [true]
                  },
                  title: {
                    type: "string",
                    title: "Title",
                    default: defaultTeamProps.hiringSection.title
                  },
                  subtitle: {
                    type: "string",
                    title: "Subtitle",
                    default: defaultTeamProps.hiringSection.subtitle
                  },
                  description: {
                    type: "string",
                    title: "Description",
                    default: defaultTeamProps.hiringSection.description
                  },
                  imageUrl: {
                    type: "string",
                    title: "Image URL",
                    pattern: "^(https?://.*|/.+)$",
                    default: defaultTeamProps.hiringSection.imageUrl
                  },
                  button: {
                    type: "object",
                    title: "Button",
                    properties: {
                      label: {
                        type: "string",
                        title: "Label",
                        default: defaultTeamProps.hiringSection.button?.label
                      },
                      href: {
                        type: "string",
                        title: "URL",
                        default: defaultTeamProps.hiringSection.button?.href
                      },
                      urlType: {
                        type: "string",
                        title: "URL Type",
                        enum: ["internal", "external"],
                        default: defaultTeamProps.hiringSection.button?.urlType || "internal"
                      }
                    },
                    required: ["label", "href"]
                  }
                },
                required: ["title", "subtitle", "description", "button"]
              },
              {
                properties: {
                  enabled: {
                    enum: [false]
                  }
                }
              }
            ]
          }
        }
      }
    },
    required: ["title", "description", "people"]
  },
  uiSchema: {
    "ui:order": ["tagline", "title", "description", "people", "hiringSection"],
    
    people: {
      items: {
        "ui:order": ["name", "role", "bio", "imageUrl", "socialLinks"],
        imageUrl: {
          "ui:widget": "imageSelector",
          "ui:placeholder": "Select or enter image URL",
          "ui:help": "Choose an image for the team member's profile"
        }
      }
    },
    
    hiringSection: {
      "ui:options": {
        label: false
      },
      enabled: {
        "ui:widget": "switch"
      },
      title: {
        "ui:options": {
          conditional: {
            field: "hiringSection.enabled",
            value: true
          }
        }
      },
      subtitle: {
        "ui:options": {
          conditional: {
            field: "hiringSection.enabled",
            value: true
          }
        }
      },
      description: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 3,
          conditional: {
            field: "hiringSection.enabled",
            value: true
          }
        }
      },
      imageUrl: {
        "ui:widget": "imageSelector",
        "ui:placeholder": "Select or enter image URL",
        "ui:help": "Choose an image for the hiring section",
        "ui:options": {
          conditional: {
            field: "hiringSection.enabled",
            value: true
          }
        }
      },
      button: {
        "ui:options": {
          label: false,
          conditional: {
            field: "hiringSection.enabled",
            value: true
          }
        }
      }
    }
  }
};

export default teamConfig;