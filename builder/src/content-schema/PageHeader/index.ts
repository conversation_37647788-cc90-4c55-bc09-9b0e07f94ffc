import { defaultProps } from '@litpage/sections';
import { BlockFormConfig } from '../types';

const defaultPageHeaderProps = defaultProps.PageHeader;

const pageHeaderConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      tagline: {
        type: "string",
        title: "Tagline",
        // minLength: 2,
        maxLength: 50,
        default: defaultPageHeaderProps.tagline
      },
      title: {
        type: "string",
        title: "Main Title",
        minLength: 2,
        maxLength: 70,
        default: defaultPageHeaderProps.title
      },
      description: {
        type: "string",
        title: "Description",
        minLength: 2,
        maxLength: 200,
        default: defaultPageHeaderProps.description
      }
    },
    required: ["title", "description"]
  },
  uiSchema: {
    "ui:order": ["tagline", "title", "description"],
    
    tagline: {
      "ui:widget": "text",
      "ui:help": "A short phrase displayed above the main title"
    },
    title: {
      "ui:widget": "text",
      "ui:help": "The main heading of the page"
    },
    description: {
      "ui:widget": "textarea",
      "ui:help": "Supporting text that provides context or additional information"
    }
  }
};

export default pageHeaderConfig;