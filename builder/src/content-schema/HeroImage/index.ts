import { defaultProps } from '@litpage/sections';
import { BlockFormConfig } from '../types';

const defaultHeroImageProps = defaultProps.HeroImage;

const heroImageConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      title: {
        type: "object",
        title: "Title Settings",
        properties: {
          text: {
            type: "string",
            title: "Main Title",
            minLength: 2,
            maxLength: 70,
            default: defaultHeroImageProps.title.text
          },
          highlight: {
            type: "object",
            title: "Highlight Text Settings",
            properties: {
              enabled: {
                type: "boolean",
                title: "Enable Highlight",
                default: defaultHeroImageProps.title.highlight.enabled
              }
            },
            dependencies: {
              enabled: {
                oneOf: [
                  {
                    properties: {
                      enabled: { enum: [true] },
                      text: {
                        type: "string",
                        title: "Highlight Content",
                        minLength: 2,
                        maxLength: 30,
                        default: defaultHeroImageProps.title.highlight.text
                      },
                      style: {
                        type: "string",
                        title: "Highlight Style",
                        enum: ["gradient-blue", "gradient-purple", "solid-primary", "solid-secondary"],
                        default: defaultHeroImageProps.title.highlight.style
                      }
                    },
                    required: ["text", "style"]
                  },
                  {
                    properties: {
                      enabled: { enum: [false] }
                    }
                  }
                ]
              }
            }
          }
        },
        required: ["text", "highlight"]
      },
      description: {
        type: "string",
        title: "Description",
        minLength: 2,
        maxLength: 160,
        default: defaultHeroImageProps.description
      },
      image: {
        type: "object",
        title: "Image Settings",
        properties: {
          url: {
            type: "string",
            title: "Image URL",
            pattern: "^(https?://.*|/.+)$",
            default: defaultHeroImageProps.image.url
          },
          alt: {
            type: "string",
            title: "Alt Text",
            minLength: 2,
            maxLength: 100,
            default: defaultHeroImageProps.image.alt
          }
        },
        required: ["url", "alt"]
      },
      announcement: {
        type: "object",
        title: "Announcement Settings",
        properties: {
          enabled: {
            type: "boolean",
            title: "Show Announcement",
            default: defaultHeroImageProps.announcement.enabled
          }
        },
        dependencies: {
          enabled: {
            oneOf: [
              {
                properties: {
                  enabled: { enum: [true] },
                  text: {
                    type: "string",
                    title: "Announcement Text",
                    maxLength: 100,
                    default: defaultHeroImageProps.announcement.text
                  },
                  url: {
                    type: "string",
                    title: "Link URL",
                    pattern: "^(?:\/[a-zA-Z0-9-_\/]*|https?:\/\/[^\\s]+|#[a-zA-Z0-9-_]+|mailto:[^\\s]+|tel:[^\\s]+)$",
                    default: defaultHeroImageProps.announcement.url
                  },
                  urlType: {
                    type: "string",
                    title: "Link Type",
                    enum: ["internal", "external", "anchor"],
                    default: defaultHeroImageProps.announcement.urlType
                  }
                },
                required: ["text", "url", "urlType"]
              },
              {
                properties: {
                  enabled: { enum: [false] }
                }
              }
            ]
          }
        }
      },
      buttons: {
        type: "array",
        title: "Button Settings",
        items: {
          type: "object",
          title: "Button",
          properties: {
            label: {
              type: "string",
              title: "Button Text",
              minLength: 2,
              maxLength: 20,
              default: defaultHeroImageProps.buttons[0].label
            },
            url: {
              type: "string",
              title: "Button URL",
              pattern: "^(?:\/[a-zA-Z0-9-_\/]*|https?:\/\/[^\\s]+|#[a-zA-Z0-9-_]+|mailto:[^\\s]+|tel:[^\\s]+)$",
              default: defaultHeroImageProps.buttons[0].url
            },
            urlType: {
              type: "string",
              title: "URL Type",
              enum: ["internal", "external", "anchor", "email", "phone"],
              default: defaultHeroImageProps.buttons[0].urlType
            },
            style: {
              type: "string",
              title: "Button Style",
              enum: ["primary", "secondary", "outline", "text"],
              default: defaultHeroImageProps.buttons[0].variant
            },
            size: {
              type: "string",
              title: "Button Size",
              enum: ["small", "medium", "large"],
              default: defaultHeroImageProps.buttons[0].size
            },
            icon: {
              type: "object",
              title: "Button Icon",
              properties: {
                enabled: {
                  type: "boolean",
                  title: "Show Icon",
                  default: defaultHeroImageProps.buttons[0].icon.enabled
                }
              },
              dependencies: {
                enabled: {
                  oneOf: [
                    {
                      properties: {
                        enabled: { enum: [true] },
                        name: {
                          type: "string",
                          title: "Icon Name",
                          default: defaultHeroImageProps.buttons[0].icon.name || "arrow-right"
                        },
                        position: {
                          type: "string",
                          title: "Icon Position",
                          enum: ["left", "right"],
                          default: defaultHeroImageProps.buttons[0].icon.position || "right"
                        }
                      },
                      required: ["name", "position"]
                    },
                    {
                      properties: {
                        enabled: { enum: [false] }
                      }
                    }
                  ]
                }
              }
            }
          },
          required: ["label", "url", "urlType", "style", "size", "icon"]
        },
        default: [
          {
            label: defaultHeroImageProps.buttons[0].label,
            url: defaultHeroImageProps.buttons[0].url,
            urlType: defaultHeroImageProps.buttons[0].urlType,
            style: defaultHeroImageProps.buttons[0].style,
            size: defaultHeroImageProps.buttons[0].size,
            icon: {
              enabled: defaultHeroImageProps.buttons[0].icon.enabled,
              name: defaultHeroImageProps.buttons[0].icon.name,
              position: defaultHeroImageProps.buttons[0].icon.position
            }
          },
          {
            label: defaultHeroImageProps.buttons[1].label,
            url: defaultHeroImageProps.buttons[1].url,
            urlType: defaultHeroImageProps.buttons[1].urlType,
            style: defaultHeroImageProps.buttons[1].style,
            size: defaultHeroImageProps.buttons[1].size,
            icon: {
              enabled: defaultHeroImageProps.buttons[1].icon.enabled,
              name: defaultHeroImageProps.buttons[1].icon.name,
              position: defaultHeroImageProps.buttons[1].icon.position
            }
          }
        ]
      }
    },
    required: ["title", "description", "buttons", "image"]
  },
  uiSchema: {
    "ui:order": ["title", "description", "image", "announcement", "buttons"],
    
    title: {
      "ui:order": ["text", "highlight"],
      text: {
        "ui:widget": "textarea",
        "ui:placeholder": "Enter your main title here"
      },
      highlight: {
        "ui:order": ["enabled", "text", "style"],
        enabled: {
          "ui:widget": "switch"
        },
        text: {
          "ui:placeholder": "Text to highlight"
        },
        style: {
          "ui:widget": "select",
          "ui:options": {
            "gradient-blue": "Blue Gradient",
            "gradient-purple": "Purple Gradient",
            "solid-primary": "Primary Color",
            "solid-secondary": "Secondary Color"
          }
        }
      }
    },
    description: {
      "ui:widget": "textarea",
      "ui:placeholder": "Enter a compelling description that explains your value proposition"
    },
    image: {
      "ui:order": ["url", "alt"],
      url: {
        "ui:widget": "imageSelector",
        "ui:placeholder": "https://example.com/image.jpg"
      },
      alt: {
        "ui:widget": "text",
        "ui:placeholder": "Descriptive text for the image"
      }
    },
    announcement: {
      "ui:order": ["enabled", "text", "url", "urlType"],
      enabled: {
        "ui:widget": "switch"
      },
      text: {
        "ui:widget": "text",
        "ui:placeholder": "Announcement text here"
      },
      url: {
        "ui:widget": "text",
        "ui:placeholder": "/announcement-page"
      },
      urlType: {
        "ui:widget": "select",
        "ui:options": {
          "internal": "Internal Page",
          "external": "External Website",
          "anchor": "Page Section"
        }
      }
    },
    buttons: {
      "ui:options": {
        "orderable": true
      },
      items: {
        "ui:order": ["label", "url", "urlType", "style", "size", "icon"],
        label: {
          "ui:widget": "text",
          "ui:placeholder": "Button text"
        },
        url: {
          "ui:widget": "text",
          "ui:placeholder": "Button link URL"
        },
        urlType: {
          "ui:widget": "select",
          "ui:options": {
            "internal": "Internal Page",
            "external": "External Website",
            "anchor": "Page Section",
            "email": "Email Address",
            "phone": "Phone Number"
          }
        },
        style: {
          "ui:widget": "select",
          "ui:options": {
            "primary": "Primary",
            "secondary": "Secondary",
            "outline": "Outline",
            "text": "Text Only"
          }
        },
        size: {
          "ui:widget": "select",
          "ui:options": {
            "small": "Small",
            "medium": "Medium",
            "large": "Large"
          }
        },
        icon: {
          "ui:order": ["enabled", "name", "position"],
          enabled: {
            "ui:widget": "switch"
          },
          name: {
            "ui:widget": "iconSelect"
          },
          position: {
            "ui:widget": "select",
            "ui:options": {
              "left": "Left",
              "right": "Right"
            }
          }
        }
      }
    }
  }
};

export default heroImageConfig;