import { defaultProps } from '@litpage/sections';

import { BlockFormConfig } from '../types';

const defaultBentoFiveProps = defaultProps.BentoFive;

const bentoFiveConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      tagline: {
        type: "string",
        title: "Tagline",
        // minLength: 2,
        maxLength: 50,
        default: defaultBentoFiveProps.tagline
      },
      title: {
        type: "string",
        title: "Main Title",
        minLength: 2,
        maxLength: 70,
        default: defaultBentoFiveProps.title
      },
      items: {
        type: "array",
        title: "Content Items",
        description: "The feature items to display in this grid",
        minItems: 5,
        maxItems: 5,
        items: {
          type: "object",
          properties: {
            tagline: {
              type: "string",
              title: "Tagline",
              description: "Optional small text displayed above the title",
              maxLength: 30
            },
            title: {
              type: "string",
              title: "Title",
              minLength: 2,
              maxLength: 50,
              default: "Performance"
            },
            description: {
              type: "string",
              title: "Description",
              description: "A brief description of this feature",
              minLength: 2,
              maxLength: 200,
              default: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. In gravida justo et nulla efficitur, maximus egestas sem pellentesque."
            },
            img: {
              type: "string",
              title: "Image URL",
              description: "URL for the feature image (dimensions will be added in the template)",
              default: "https://images.unsplash.com/photo-1624571409412-1f253e1ecc89?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&q=80"
            },
            alt: {
              type: "string",
              title: "Image Alt Text",
              description: "Descriptive text for the image (for accessibility and SEO)",
              minLength: 2,
              maxLength: 100,
              default: "Feature visualization"
            }
          },
          required: ["title", "description", "img", "alt"]
        },
        default: defaultBentoFiveProps.items
      }
    },
    required: ["title", "items"]
  },
  uiSchema: {
    "ui:order": ["tagline", "title", "items"],
    tagline: {
      "ui:widget": "text"
    },
    title: {
      "ui:widget": "text"
    },
    items: {
      "ui:options": {
        orderable: true,
        addable: false,
        removable: false
      },
      items: {
        "ui:order": ["tagline", "title", "description", "img", "alt"],
        tagline: {
          "ui:widget": "text"
        },
        title: {
          "ui:widget": "text"
        },
        description: {
          "ui:widget": "textarea"
        },
        img: {
          "ui:widget": "imageSelector"
        },
        alt: {
          "ui:widget": "text"
        }
      }
    }
  }
};

export default bentoFiveConfig;