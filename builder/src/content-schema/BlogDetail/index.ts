import { BlockFormConfig } from '../types';

const blogDetailConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      title: {
        type: "string",
        title: "Article Title",
        minLength: 2,
        maxLength: 100,
        default: "How to Build Amazing Web Applications"
      },
      excerpt: {
        type: "string",
        title: "Article Excerpt",
        minLength: 2,
        maxLength: 200,
        default: "A comprehensive guide to building modern web applications with the latest technologies and best practices."
      },
      content: {
        type: "string",
        title: "Article Content",
        minLength: 10,
        default: "# Introduction\n\nThis is the main content of your blog post. You can use Markdown formatting to structure your content.\n\n## Key Points\n\n- Point one\n- Point two\n- Point three\n\n## Conclusion\n\nWrap up your article with a compelling conclusion."
      },
      featuredImage: {
        type: "object",
        title: "Featured Image",
        properties: {
          url: {
            type: "string",
            title: "Image URL",
            pattern: "^(https?://.*|/.+)$",
            default: "/images/blog/featured-image.jpg"
          },
          alt: {
            type: "string",
            title: "Alt Text",
            minLength: 2,
            maxLength: 100,
            default: "Featured image for the blog post"
          }
        },
        required: ["url", "alt"]
      },
      author: {
        type: "object",
        title: "Author Information",
        properties: {
          name: {
            type: "string",
            title: "Author Name",
            minLength: 2,
            maxLength: 50,
            default: "John Doe"
          },
          avatar: {
            type: "string",
            title: "Author Avatar URL",
            pattern: "^(https?://.*|/.+)$",
            default: "/images/avatars/author.jpg"
          },
          bio: {
            type: "string",
            title: "Author Bio",
            maxLength: 200,
            default: "Software engineer and technical writer passionate about web development."
          }
        },
        required: ["name"]
      },
      category: {
        type: "string",
        title: "Category",
        minLength: 2,
        maxLength: 30,
        default: "Technology"
      },
      tags: {
        type: "array",
        title: "Tags",
        items: {
          type: "string",
          minLength: 2,
          maxLength: 20
        },
        default: ["web development", "javascript", "react"],
        maxItems: 5,
        minItems: 1
      },
      readingTime: {
        type: "integer",
        title: "Reading Time (minutes)",
        minimum: 1,
        maximum: 60,
        default: 5
      },
      seo: {
        type: "object",
        title: "SEO Settings",
        properties: {
          metaTitle: {
            type: "string",
            title: "Meta Title",
            maxLength: 60,
            default: "How to Build Amazing Web Applications | Your Blog"
          },
          metaDescription: {
            type: "string",
            title: "Meta Description",
            maxLength: 160,
            default: "Learn how to build modern web applications with this comprehensive guide covering the latest technologies and best practices."
          }
        }
      },

    },
    required: ["title", "content", "featuredImage", "author", "category", "tags"]
  },
  uiSchema: {
    "ui:order": ["title", "excerpt", "content", "featuredImage", "author", "category", "tags", "readingTime", "seo"],
    
    title: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "Enter your blog post title...",
        fullWidth: true
      }
    },
    
    excerpt: {
      "ui:widget": "textarea",
      "ui:options": {
        rows: 3,
        placeholder: "Write a compelling excerpt that summarizes your post...",
        fullWidth: true
      }
    },
    
    content: {
      "ui:widget": "markdown",
      "ui:options": {
        placeholder: "Write your blog post content using Markdown...",
        fullWidth: true,
        defaultMode: "wysiwyg",
        showModeToggle: true,
        enableShortcuts: true,
        autoSave: false,
        height: 800,
        minHeight: 600
      }
    },
    
    featuredImage: {
      url: {
        "ui:widget": "imageSelector",
        "ui:options": {
          placeholder: "Select or upload an image..."
        }
      },
      alt: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "Describe the image for accessibility..."
        }
      }
    },
    
    author: {
      name: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "Author's full name"
        }
      },
      avatar: {
        "ui:widget": "imageSelector",
        "ui:options": {
          placeholder: "Select or upload an avatar image..."
        }
      },
      bio: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 2,
          placeholder: "Brief bio about the author..."
        }
      }
    },
    
    category: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "Enter article category..."
      }
    },
    
    tags: {
      "ui:widget": "tagInput",
      "ui:options": {
        placeholder: "Add tags (press Enter to add each tag)",
        addable: true,
        removable: true
      }
    },
    
    readingTime: {
      "ui:widget": "updown",
      "ui:options": {
        placeholder: "Estimated reading time in minutes"
      }
    },
    
    seo: {
      metaTitle: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "SEO-optimized title for search engines...",
          fullWidth: true
        }
      },
      metaDescription: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 2,
          placeholder: "SEO description that will appear in search results...",
          fullWidth: true
        }
      }
    },
    

  }
};

export default blogDetailConfig;