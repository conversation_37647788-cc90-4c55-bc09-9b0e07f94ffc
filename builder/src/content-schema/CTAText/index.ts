import { defaultProps } from '@litpage/sections';

import { BlockFormConfig } from '../types';

const defaultCTATextProps = defaultProps.CTAText;

const ctaTextConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      tagline: {
        type: "string",
        title: "Tagline",
        description: "Short text displayed above the title",
        // minLength: 2,
        maxLength: 50,
        default: defaultCTATextProps.tagline
      },
      title: {
        type: "object",
        title: "Title Settings",
        properties: {
          text: {
            type: "string",
            title: "Main Title",
            minLength: 2,
            maxLength: 70,
            default: defaultCTATextProps.title.text
          },
          highlight: {
            type: "object",
            title: "Highlight Text Settings",
            properties: {
              enabled: {
                type: "boolean",
                title: "Enable Highlight",
                default: defaultCTATextProps.title.highlight.enabled
              }
            },
            dependencies: {
              enabled: {
                oneOf: [
                  {
                    properties: {
                      enabled: { enum: [true] },
                      text: {
                        type: "string",
                        title: "Highlight Content",
                        minLength: 2,
                        maxLength: 30,
                        default: defaultCTATextProps.title.highlight.text
                      },
                      style: {
                        type: "string",
                        title: "Highlight Style",
                        enum: ["gradient-blue", "gradient-purple", "solid-primary", "solid-secondary"],
                        default: defaultCTATextProps.title.highlight.style
                      }
                    },
                    required: ["text", "style"]
                  },
                  {
                    properties: {
                      enabled: { enum: [false] }
                    }
                  }
                ]
              }
            }
          }
        },
        required: ["text", "highlight"]
      },
      description: {
        type: "string",
        title: "Description",
        minLength: 2,
        maxLength: 160,
        default: defaultCTATextProps.description
      },
      buttons: {
        type: "array",
        title: "Button Settings",
        items: {
          type: "object",
          title: "Button",
          properties: {
            label: {
              type: "string",
              title: "Button Text",
              minLength: 2,
              maxLength: 20,
              default: defaultCTATextProps.buttons[0].label
            },
            url: {
              type: "string",
              title: "Button URL",
              pattern: "^(?:\/[a-zA-Z0-9-_\/]*|https?:\/\/[^\\s]+|#[a-zA-Z0-9-_]+|mailto:[^\\s]+|tel:[^\\s]+)$",
              default: defaultCTATextProps.buttons[0].url
            },
            urlType: {
              type: "string",
              title: "URL Type",
              enum: ["internal", "external", "anchor", "email", "phone"],
              default: defaultCTATextProps.buttons[0].urlType
            },
            style: {
              type: "string",
              title: "Button Style",
              enum: ["primary", "secondary", "outline", "text"],
              default: defaultCTATextProps.buttons[0].style
            },
            size: {
              type: "string",
              title: "Button Size",
              enum: ["small", "medium", "large"],
              default: defaultCTATextProps.buttons[0].size
            },
            icon: {
              type: "object",
              title: "Button Icon",
              properties: {
                enabled: {
                  type: "boolean",
                  title: "Show Icon",
                  default: defaultCTATextProps.buttons[0].icon.enabled
                }
              },
              dependencies: {
                enabled: {
                  oneOf: [
                    {
                      properties: {
                        enabled: { enum: [true] },
                        name: {
                          type: "string",
                          title: "Icon Name",
                          default: defaultCTATextProps.buttons[0].icon.name
                        },
                        position: {
                          type: "string",
                          title: "Icon Position",
                          enum: ["left", "right"],
                          default: defaultCTATextProps.buttons[0].icon.position
                        }
                      },
                      required: ["name", "position"]
                    },
                    {
                      properties: {
                        enabled: { enum: [false] }
                      }
                    }
                  ]
                }
              }
            }
          },
          required: ["label", "url", "urlType", "style", "size", "icon"]
        },
        default: defaultCTATextProps.buttons,
        maxItems: 2,
        minItems: 1
      }
    },
    required: ["title", "description", "buttons"]
  },
  uiSchema: {
    "ui:order": ["tagline", "title", "description", "buttons"],
    
    tagline: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "Enter a short tagline...",
        fullWidth: true
      }
    },
    title: {
      text: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "Enter your main headline...",
          fullWidth: true
        }
      },
      highlight: {
        enabled: {
          "ui:widget": "switch",
          "ui:options": {
            trueLabel: "On",
            falseLabel: "Off"
          }
        },
        text: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Enter text to highlight...",
            fullWidth: true
          }
        },
        style: {
          "ui:widget": "radio",
          "ui:options": {
            inline: true
          }
        }
      }
    },
    description: {
      "ui:widget": "textarea",
      "ui:options": {
        rows: 3,
        placeholder: "Enter a brief description...",
        fullWidth: true
      }
    },
    buttons: {
      "ui:options": {
        addButtonLabel: "Add Button",
        fullWidth: true
      },
      items: {
        label: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Enter button text...",
            fullWidth: true
          }
        },
        url: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Enter URL...",
            fullWidth: true
          }
        },
        urlType: {
          "ui:widget": "select",
          "ui:options": {
            fullWidth: true
          }
        },
        style: {
          "ui:widget": "select",
          "ui:options": {
            fullWidth: true
          }
        },
        size: {
          "ui:widget": "select",
          "ui:options": {
            fullWidth: true
          }
        },
        icon: {
          enabled: {
            "ui:widget": "switch",
            "ui:options": {
              trueLabel: "On",
              falseLabel: "Off"
            }
          },
          name: {
            "ui:widget": "iconSelect"
          },
          position: {
            "ui:widget": "radio",
            "ui:options": {
              inline: true
            }
          }
        }
      }
    }
  }
};

export default ctaTextConfig;