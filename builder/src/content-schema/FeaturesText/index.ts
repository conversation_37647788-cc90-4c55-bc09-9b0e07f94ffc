import { defaultProps } from '@litpage/sections';
import { BlockFormConfig } from '../types';

const defaultFeaturesTextProps = defaultProps.FeaturesText;

const featuresTextConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      tagline: {
        type: "string",
        // minLength: 0,
        maxLength: 50,
        default: defaultFeaturesTextProps.tagline,
        description: "Optional tagline displayed above the title"
      },
      title: {
        type: "string",
        minLength: 2,
        maxLength: 70,
        default: defaultFeaturesTextProps.title,
        description: "Main title text"
      },
      description: {
        type: "string",
        minLength: 2,
        maxLength: 200,
        default: defaultFeaturesTextProps.description,
        description: "Detailed explanation that appears below the title"
      },
      features: {
        type: "array",
        items: {
          type: "object",
          properties: {
            title: {
              type: "string",
              minLength: 2,
              maxLength: 50,
              description: "Feature title"
            },
            description: {
              type: "string",
              minLength: 2,
              maxLength: 200,
              description: "Feature description"
            },
            icon: {
              type: "string",
              enum: [
                "CloudArrowUpIcon",
                "LockClosedIcon",
                "ArrowPathIcon",
                "FingerPrintIcon",
                "ChatBubbleOvalLeftEllipsisIcon",
                "HeartIcon",
                "PencilSquareIcon",
                "TrashIcon",
                "InboxIcon",
                "UsersIcon",
                "CogIcon",
                "ServerIcon",
                "ShieldCheckIcon",
                "CheckIcon"
              ],
              description: "Icon to display with the feature"
            },
            url: {
              type: "string",
              pattern: "^(?:\/[a-zA-Z0-9-_\/]*|https?:\/\/[^\\s]+|#[a-zA-Z0-9-_]+)$",
              description: "Optional link URL for the feature"
            },
            urlType: {
              type: "string",
              enum: ["internal", "external", "anchor"],
              description: "Type of URL link"
            },
            linkText: {
              type: "string",
              minLength: 2,
              maxLength: 30,
              description: "Text to display for the feature link"
            }
          },
          required: ["title", "description"]
        },
        default: defaultFeaturesTextProps.features,
        minItems: 1,
        maxItems: 8,
        description: "List of features to display"
      },
    },
    required: ["title", "description", "features"]
  },
  uiSchema: {
    tagline: {
      'ui:widget': 'text',
      'ui:help': 'Short text displayed above the main title (optional)'
    },
    title: {
      'ui:widget': 'text',
      'ui:help': 'Main heading for the features section'
    },
    description: {
      'ui:widget': 'textarea',
      'ui:help': 'Brief explanation of your features section'
    },
    features: {
      'ui:help': 'Add up to 8 features with icons and optional links',
      items: {
        title: {
          'ui:widget': 'text',
          'ui:help': 'Short, benefit-focused feature title'
        },
        description: {
          'ui:widget': 'textarea',
          'ui:help': 'Detailed explanation of the feature and its benefits'
        },
        icon: {
          'ui:widget': 'select',
          'ui:help': 'Icon to display with this feature',
          'ui:options': {
            'CloudArrowUpIcon': 'Cloud Upload',
            'LockClosedIcon': 'Lock/Security',
            'ArrowPathIcon': 'Refresh/Sync',
            'FingerPrintIcon': 'Fingerprint/Identity',
            'ChatBubbleOvalLeftEllipsisIcon': 'Chat/Message',
            'HeartIcon': 'Heart/Favorite',
            'PencilSquareIcon': 'Edit/Write',
            'TrashIcon': 'Delete/Remove',
            'InboxIcon': 'Inbox/Mail',
            'UsersIcon': 'Users/Team',
            'CogIcon': 'Settings/Gear',
            'ServerIcon': 'Server/Database',
            'ShieldCheckIcon': 'Shield/Protection',
            'CheckIcon': 'Checkmark'
          }
        },
        url: {
          'ui:widget': 'text',
          'ui:help': 'Link URL (optional). Use # for placeholder links'
        },
        urlType: {
          'ui:widget': 'select',
          'ui:help': 'Type of link',
          'ui:options': {
            'internal': 'Internal Page',
            'external': 'External Website',
            'anchor': 'Page Section'
          }
        },
        linkText: {
          'ui:widget': 'text',
          'ui:help': 'Text displayed for the link (e.g., "Learn more", "See details")'
        }
      }
    },
  }
};

export default featuresTextConfig;