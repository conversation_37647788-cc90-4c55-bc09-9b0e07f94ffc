import { BlockFormConfig } from '../types';

const changelogConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      version: {
        type: "string",
        title: "Version",
        pattern: "^\\d+\\.\\d+\\.\\d+(-[a-zA-Z0-9]+)?$",
        minLength: 5,
        maxLength: 20,
        default: "2.1.0"
      },
      releaseDate: {
        type: "string",
        title: "Release Date",
        format: "date",
        default: new Date().toISOString().split('T')[0]
      },
      releaseType: {
        type: "string",
        title: "Release Type",
        enum: ["major", "minor", "patch", "hotfix"],
        default: "minor"
      },
      title: {
        type: "string",
        title: "Release Title",
        minLength: 5,
        maxLength: 100,
        default: "Enhanced Dashboard & Performance Improvements"
      },
      summary: {
        type: "string",
        title: "Release Summary",
        minLength: 10,
        maxLength: 300,
        default: "This release introduces a redesigned user dashboard, significant performance improvements, and fixes for several user-reported issues."
      },
              changes: {
        type: "array",
        title: "Changes",
        items: {
          type: "object",
          properties: {
            type: {
              type: "string",
              title: "Change Type",
              enum: ["added", "improved", "fixed", "security"],
              default: "added"
            },
            description: {
              type: "string",
              title: "Description",
              minLength: 5,
              maxLength: 200,
              default: "New feature description"
            },
            details: {
              type: "string",
              title: "Details",
              maxLength: 500,
              default: ""
            }
          },
          required: ["type", "description"]
        },
        minItems: 1,
        maxItems: 20,
        default: [
          {
            type: "added",
            description: "New user dashboard",
            details: "Redesigned dashboard interface with improved data visualization and user experience"
          },
          {
            type: "improved",
            description: "40% faster page loading",
            details: "Optimized database queries and caching strategies for better performance"
          },
          {
            type: "fixed",
            description: "Fixed intermittent email notification failures",
            details: ""
          },
          {
            type: "security",
            description: "Enhanced account security verification",
            details: "Updated encryption algorithms for stronger login protection"
          }
        ]
      },
      seo: {
        type: "object",
        title: "SEO Settings",
        properties: {
          metaTitle: {
            type: "string",
            title: "Meta Title",
            maxLength: 60,
            default: "Version 2.1.0 Release Notes - New Dashboard Launch"
          },
          metaDescription: {
            type: "string",
            title: "Meta Description",
            maxLength: 160,
            default: "Discover the latest features: new dashboard, 40% performance boost, security enhancements and more in this update"
          }
        },
        required: ["metaTitle", "metaDescription"]
      }
    },
    required: ["version", "releaseDate", "releaseType", "title", "summary", "changes", "seo"]
  },
  uiSchema: {
    "ui:order": ["version", "releaseDate", "releaseType", "title", "summary", "changes", "seo"],
    
    version: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "e.g., 2.1.0 or 1.0.0-beta.1",
        help: "Use semantic versioning format"
      }
    },
    
    releaseDate: {
      "ui:widget": "date",
      "ui:options": {
        placeholder: "Select release date"
      }
    },
    
    releaseType: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "major", label: "🚀 Major - Breaking changes" },
          { value: "minor", label: "✨ Minor - New features" },
          { value: "patch", label: "🔧 Patch - Bug fixes" },
          { value: "hotfix", label: "🚨 Hotfix - Critical fixes" }
        ]
      }
    },
    
    title: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "Create an engaging title for this release...",
        help: "Briefly describe the main highlights of this update"
      }
    },
    
    summary: {
      "ui:widget": "textarea",
      "ui:options": {
        rows: 3,
        placeholder: "Introduce the main content and value of this update...",
        help: "This summary will be displayed at the top of the changelog to help users quickly understand the key points"
      }
    },
    
    changes: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "Add Change"
      },
      items: {
        "ui:order": ["type", "description", "details"],
        
        type: {
          "ui:widget": "select",
          "ui:options": {
            enumOptions: [
              { value: "added", label: "🎉 Added" },
              { value: "improved", label: "⚡ Improved" },
              { value: "fixed", label: "🔧 Fixed" },
              { value: "security", label: "🔒 Security" }
            ]
          }
        },
        
        description: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Briefly describe this change...",
            help: "Explain what this change is in one sentence"
          }
        },
        
        details: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 2,
            placeholder: "Optional: Provide more detailed explanation...",
            help: "Give users more context or usage guidance"
          }
        }
      }
    },
    
    seo: {
      "ui:options": {
        collapsible: true,
        collapsed: false
      },
      
      metaTitle: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "SEO-optimized page title...",
          help: "Include version number and key feature keywords"
        }
      },
      
      metaDescription: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 2,
          placeholder: "Compelling page description for search results...",
          help: "Briefly describe the update content to attract users"
        }
      }
    }
  }
};

export default changelogConfig; 