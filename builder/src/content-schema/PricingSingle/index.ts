import { defaultProps } from '@litpage/sections';
import { BlockFormConfig } from '../types';

// 币种映射表，用于将币种代码映射到符号和代码
const currencyMap = {
  USD: { symbol: "$", code: "USD" },
  EUR: { symbol: "€", code: "EUR" },
  GBP: { symbol: "£", code: "GBP" },
  JPY: { symbol: "¥", code: "JPY" },
  CNY: { symbol: "¥", code: "CNY" },
  CAD: { symbol: "C$", code: "CAD" },
  AUD: { symbol: "A$", code: "AUD" },
  NONE: { symbol: "", code: "" }
};

const defaultPricingSingleProps = defaultProps.PricingSingle;

const pricingSingleConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      tagline: {
        type: "string",
        title: "Tagline",
        // minLength: 2,
        maxLength: 50,
        default: defaultPricingSingleProps.tagline
      },
      title: {
        type: "string",
        title: "Main Title",
        minLength: 2,
        maxLength: 70,
        default: defaultPricingSingleProps.title
      },
      description: {
        type: "string",
        title: "Description",
        minLength: 2,
        maxLength: 160,
        default: defaultPricingSingleProps.description
      },
      tier: {
        type: "object",
        title: "Pricing Tier",
        properties: {
          // Left Column - Tier Information
          name: {
            type: "string",
            title: "Tier Name",
            minLength: 2,
            maxLength: 30,
            default: defaultPricingSingleProps.tier.name
          },
          description: {
            type: "string",
            title: "Tier Description",
            minLength: 2,
            maxLength: 200,
            default: defaultPricingSingleProps.tier.description
          },
          featuresTitle: {
            type: "string",
            title: "Features Title",
            minLength: 0,
            maxLength: 50,
            default: defaultPricingSingleProps.tier.featuresTitle
          },
          features: {
            type: "array",
            title: "Features",
            items: {
              type: "string",
              title: "Feature",
              minLength: 2,
              maxLength: 100
            },
            default: defaultPricingSingleProps.tier.features
          },
          
          // Right Column - Pricing Information
          subtitle: {
            type: "string",
            title: "Price Subtitle",
            minLength: 2,
            maxLength: 50,
            default: defaultPricingSingleProps.tier.subtitle
          },
          price: {
            type: "number",
            title: "Current Price",
            minimum: 0,
            default: defaultPricingSingleProps.tier.price
          },
          originalPrice: {
            type: "number",
            title: "Original Price (Optional)",
            minimum: 0,
            default: defaultPricingSingleProps.tier.originalPrice
          },
          currency: {
            type: "string",
            title: "Currency",
            enum: ["USD", "EUR", "GBP", "JPY", "CNY", "CAD", "AUD", "NONE"],
            default: defaultPricingSingleProps.tier.currency
          },
          billingPeriod: {
            type: "object",
            title: "Billing Period",
            properties: {
              type: {
                type: "string",
                title: "Period Type",
                enum: ["one-time", "monthly", "yearly", "per-user", "custom"],
                default: defaultPricingSingleProps.tier.billingPeriod.type
              }
            },
            dependencies: {
              type: {
                oneOf: [
                  {
                    properties: {
                      type: { enum: ["custom"] },
                      customText: {
                        type: "string",
                        title: "Custom Period Text",
                        minLength: 1,
                        maxLength: 10,
                        default: defaultPricingSingleProps.tier.billingPeriod.customText || "/week"
                      }
                    },
                    required: ["customText"]
                  },
                  {
                    properties: {
                      type: { enum: ["one-time", "monthly", "yearly", "per-user"] }
                    }
                  }
                ]
              }
            }
          },
          button: {
            type: "object",
            title: "Button Settings",
            properties: {
              label: {
                type: "string",
                title: "Button Text",
                minLength: 2,
                maxLength: 20,
                default: defaultPricingSingleProps.tier.button.label
              },
              url: {
                type: "string",
                title: "Button URL",
                pattern: "^(?:\/[a-zA-Z0-9-_\/]*|https?:\/\/[^\\s]+|#[a-zA-Z0-9-_]+|mailto:[^\\s]+|tel:[^\\s]+)$",
                default: defaultPricingSingleProps.tier.button.url
              },
              urlType: {
                type: "string",
                title: "URL Type",
                enum: ["internal", "external", "anchor", "email", "phone"],
                default: defaultPricingSingleProps.tier.button.urlType
              },
              style: {
                type: "string",
                title: "Button Style",
                enum: ["primary", "secondary", "outline", "text"],
                default: defaultPricingSingleProps.tier.button.style
              },
              size: {
                type: "string",
                title: "Button Size",
                enum: ["small", "medium", "large"],
                default: defaultPricingSingleProps.tier.button.size
              },
              icon: {
                type: "object",
                title: "Icon Settings",
                properties: {
                  enabled: {
                    type: "boolean",
                    title: "Show Icon",
                    default: defaultPricingSingleProps.tier.button.icon.enabled
                  },
                  name: {
                    type: "string",
                    title: "Icon Name",
                    default: "arrow-right"
                  },
                  position: {
                    type: "string",
                    title: "Icon Position",
                    enum: ["left", "right"],
                    default: "right"
                  }
                },
                required: ["enabled"],
                dependencies: {
                  enabled: {
                    oneOf: [
                      {
                        properties: {
                          enabled: { enum: [true] },
                          name: { type: "string" },
                          position: { type: "string" }
                        },
                        required: ["name", "position"]
                      },
                      {
                        properties: {
                          enabled: { enum: [false] }
                        }
                      }
                    ]
                  }
                }
              }
            },
            required: ["label", "url", "urlType", "style", "size", "icon"]
          },
          footnote: {
            type: "string",
            title: "Footnote",
            minLength: 0,
            maxLength: 100,
            default: defaultPricingSingleProps.tier.footnote || ""
          }
        },
        required: ["name", "description", "price", "currency", "billingPeriod", "features", "button"]
      }
    },
    required: ["title", "description", "tier"]
  },
  uiSchema: {
    "ui:order": ["tagline", "title", "description", "tier"],
    
    tagline: {
      "ui:widget": "text",
      "ui:options": {
        help: "Short text displayed above the main title",
        placeholder: "Enter a tagline...",
        fullWidth: true
      }
    },
    
    title: {
      "ui:widget": "text",
      "ui:options": {
        help: "Enter your main headline...",
        placeholder: "Enter your main headline...",
        fullWidth: true
      }
    },
    
    description: {
      "ui:widget": "textarea",
      "ui:options": {
        rows: 3,
        placeholder: "Describe your pricing offering...",
        fullWidth: true
      }
    },
    
          tier: {
        "ui:order": [
          // Left Column
          "name", "description", "featuresTitle", "features", 
          // Right Column
          "subtitle", "price", "originalPrice", "currency", "billingPeriod", "button", "footnote"
        ],
      "ui:options": {
        expandable: false
      },
      
      // Left Column - Tier Information
      name: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "e.g., Lifetime membership, Pro Plan"
        }
      },
      description: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 2,
          placeholder: "Describe this pricing tier..."
        }
      },
      featuresTitle: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "e.g., What's included, Features, Benefits"
        }
      },
      features: {
        "ui:options": {
          addable: true,
          removable: true,
          addButtonLabel: "Add Feature"
        },
        items: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "e.g., 10 users, Unlimited storage"
          }
        }
      },
      
      // Right Column - Pricing Information
      subtitle: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "e.g., Pay once, own it forever"
        }
      },
      price: {
        "ui:widget": "updown",
        "ui:options": {
          help: "Enter the current price (supports decimals, e.g., 349.5)",
          placeholder: "e.g., 349.5, 29, 13.99"
        }
      },
      originalPrice: {
        "ui:widget": "updown",
        "ui:options": {
          help: "Enter the original price to show discount (supports decimals, e.g., 499.99)",
          placeholder: "e.g., 499.99, 59.5, leave empty for no discount"
        }
      },
      currency: {
        "ui:widget": "buttonGroup",
        "ui:options": {
          help: "Select the currency for this price",
          enumOptions: [
            { value: "USD", label: "$ USD" },
            { value: "EUR", label: "€ EUR" },
            { value: "GBP", label: "£ GBP" },
            { value: "JPY", label: "¥ JPY" },
            { value: "CNY", label: "¥ CNY" },
            { value: "CAD", label: "C$ CAD" },
            { value: "AUD", label: "A$ AUD" },
            { value: "NONE", label: "None" }
          ]
        }
      },
      billingPeriod: {
        "ui:order": ["type", "customText"],
        "ui:options": {
          expandable: false
        },
        type: {
          "ui:widget": "buttonGroup",
          "ui:options": {
            help: "Select the billing period for this price",
            enumOptions: [
              { value: "one-time", label: "One-time" },
              { value: "monthly", label: "/mo" },
              { value: "yearly", label: "/yr" },
              { value: "per-user", label: "/user" },
              { value: "custom", label: "Custom" }
            ]
          }
        },
        customText: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "e.g., /week, /day",
            help: "Enter custom billing period text"
          }
        }
      },
      button: {
        "ui:order": ["label", "url", "urlType", "style", "size", "icon"],
        "ui:options": {
          expandable: true,
          expanded: false,
          title: "Button Settings"
        },
        label: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "e.g., Get started, Buy now"
          }
        },
        url: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "e.g., /checkout, #pricing"
          }
        },
        urlType: {
          "ui:widget": "buttonGroup",
          "ui:options": {
            enumOptions: [
              { value: "internal", label: "Internal" },
              { value: "external", label: "External" },
              { value: "anchor", label: "Anchor" },
              { value: "email", label: "Email" },
              { value: "phone", label: "Phone" }
            ]
          }
        },
        style: {
          "ui:widget": "buttonGroup",
          "ui:options": {
            help: "Select the button style",
            enumOptions: [
              { value: "primary", label: "Primary" },
              { value: "secondary", label: "Secondary" },
              { value: "outline", label: "Outline" },
              { value: "text", label: "Text" }
            ]
          }
        },
        size: {
          "ui:widget": "buttonGroup",
          "ui:options": {
            help: "Select the button size",
            enumOptions: [
              { value: "small", label: "Small" },
              { value: "medium", label: "Medium" },
              { value: "large", label: "Large" }
            ]
          }
        },
        icon: {
          "ui:order": ["enabled", "name", "position"],
          "ui:options": {
            expandable: true,
            expanded: false,
            title: "Icon Configuration"
          },
          enabled: {
            "ui:widget": "switch"
          },
          name: {
            "ui:widget": "iconSelect"
          },
          position: {
            "ui:widget": "select",
            "ui:options": {
              "left": "Left",
              "right": "Right"
            }
          }
        }
      },
      footnote: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 2,
          placeholder: "Additional information about the pricing..."
        }
      }
    }
  }
};

export default pricingSingleConfig;