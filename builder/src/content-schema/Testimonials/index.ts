import { BlockFormConfig } from '../types';

const testimonialsConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      tagline: {
        type: "string",
        // minLength: 2,
        default: "Get the help you need"
      },
      title: {
        type: "string",
        minLength: 2,
        default: "Support center"
      },
      description: {
        type: "string",
        minLength: 2,
        default: "Anim aute id magna aliqua ad ad non deserunt sunt. Qui irure qui lorem cupidatat commodo. Elit sunt amet fugiat veniam occaecat fugiat."
      }
    },
    required: ["title", "description"]
  },
  uiSchema: {
    tagline: {
      'ui:widget': 'text'
    },
    description: {
      'ui:widget': 'textarea'
    }
  }
};

export default testimonialsConfig;