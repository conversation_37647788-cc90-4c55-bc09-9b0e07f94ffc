import { defaultProps } from '@litpage/sections';
import { BlockFormConfig } from '../types';

// 币种映射表，用于将币种代码映射到符号和代码
const currencyMap = {
  USD: { symbol: "$", code: "USD" },
  EUR: { symbol: "€", code: "EUR" },
  GBP: { symbol: "£", code: "GBP" },
  JPY: { symbol: "¥", code: "JPY" },
  CNY: { symbol: "¥", code: "CNY" },
  CAD: { symbol: "C$", code: "CAD" },
  AUD: { symbol: "A$", code: "AUD" },
  NONE: { symbol: "", code: "" }
};

const defaultPricingThreeProps = defaultProps.PricingThree;

const pricingThreeConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      tagline: {
        type: "string",
        title: "Tagline",
        // minLength: 2,
        maxLength: 50,
        default: defaultPricingThreeProps.tagline
      },
      title: {
        type: "string",
        title: "Main Title",
        minLength: 2,
        maxLength: 70,
        default: defaultPricingThreeProps.title
      },
      description: {
        type: "string",
        title: "Description",
        minLength: 2,
        maxLength: 160,
        default: defaultPricingThreeProps.description
      },
      frequencies: {
        type: "object",
        title: "Billing Frequencies",
        properties: {
          enabled: {
            type: "boolean",
            title: "Enable Multiple Frequencies",
            default: defaultPricingThreeProps.frequencies.enabled
          }
        },
        dependencies: {
          enabled: {
            oneOf: [
              {
                properties: {
                  enabled: { enum: [true] },
                  options: {
                    type: "array",
                    title: "Frequency Options",
                    items: {
                      type: "object",
                      properties: {
                        value: {
                          type: "string",
                          title: "Value",
                          enum: ["monthly", "annually"],
                          default: "monthly",
                          readOnly: true
                        },
                        label: {
                          type: "string",
                          title: "Label",
                          minLength: 2,
                          maxLength: 20,
                          default: "Monthly"
                        },
                        priceSuffix: {
                          type: "string",
                          title: "Price Suffix",
                          minLength: 1,
                          maxLength: 10,
                          default: "/month"
                        }
                      },
                      required: ["value", "label", "priceSuffix"]
                    },
                    default: defaultPricingThreeProps.frequencies.options || [
                      { value: "monthly", label: "Monthly", priceSuffix: "/month" },
                      { value: "annually", label: "Annually", priceSuffix: "/year" }
                    ],
                    maxItems: 2
                  }
                },
                required: ["options"]
              },
              {
                properties: {
                  enabled: { enum: [false] },
                  billingPeriod: {
                    type: "object",
                    title: "Default Billing Period",
                    properties: {
                      type: {
                        type: "string",
                        title: "Period Type",
                        enum: ["one-time", "monthly", "annually", "per-user", "custom"],
                        default: defaultPricingThreeProps.frequencies.billingPeriod?.type || "monthly"
                      }
                    },
                    dependencies: {
                      type: {
                        oneOf: [
                          {
                            properties: {
                              type: { enum: ["custom"] },
                              customText: {
                                type: "string",
                                title: "Custom Period Text",
                                minLength: 1,
                                maxLength: 10,
                                default: defaultPricingThreeProps.frequencies.billingPeriod?.customText || "/week"
                              }
                            },
                            required: ["customText"]
                          },
                          {
                            properties: {
                              type: { enum: ["one-time", "monthly", "annually", "per-user"] }
                            }
                          }
                        ]
                      }
                    }
                  }
                },
                required: ["billingPeriod"]
              }
            ]
          }
        }
      },
      tiers: {
        type: "array",
        title: "Pricing Tiers",
        description: "Three pricing tiers for the PricingThree component",
        minItems: 3,
        maxItems: 3,
        items: {
          type: "object",
          properties: {
            name: {
              type: "string",
              title: "Tier Name",
              minLength: 2,
              maxLength: 30,
              default: "Hobby"
            },
            mostPopular: {
              type: "boolean",
              title: "Most Popular",
              default: false
            },
            price: {
              type: "object",
              title: "Price Information"
            },
            description: {
              type: "string",
              title: "Tier Description",
              minLength: 2,
              maxLength: 200,
              default: "The perfect plan if you're just getting started with our product."
            },
            features: {
              type: "array",
              title: "Features",
              items: {
                type: "string",
                title: "Feature",
                minLength: 2,
                maxLength: 100
              },
              default: [
                "25 products",
                "Up to 10,000 subscribers",
                "Advanced analytics",
                "24-hour support response time"
              ]
            },
            button: {
              type: "object",
              title: "Button Settings",
              properties: {
                label: {
                  type: "string",
                  title: "Button Text",
                  minLength: 2,
                  maxLength: 20,
                  default: "Get started today"
                },
                href: {
                  type: "string",
                  title: "Button URL",
                  pattern: "^(?:\/[a-zA-Z0-9-_\/]*|https?:\/\/[^\\s]+|#[a-zA-Z0-9-_]+|mailto:[^\\s]+|tel:[^\\s]+)$",
                  default: "/"
                },
                urlType: {
                  type: "string",
                  title: "URL Type",
                  enum: ["internal", "external", "anchor", "email", "phone"],
                  default: "internal"
                }
              },
              required: ["label", "href", "urlType"]
            }
          },
          required: ["name", "mostPopular", "price", "description", "features", "button"]
        },
        default: defaultPricingThreeProps.tiers
      },
      customSolution: {
        type: "object",
        title: "Custom Solution Section",
        properties: {
          enabled: {
            type: "boolean",
            title: "Enable Custom Solution Section",
            default: defaultPricingThreeProps.customSolution?.enabled || true
          }
        },
        dependencies: {
          enabled: {
            oneOf: [
              {
                properties: {
                  enabled: { enum: [true] },
                  title: {
                    type: "string",
                    title: "Section Title",
                    minLength: 2,
                    maxLength: 50,
                    default: defaultPricingThreeProps.customSolution?.title || "Need a custom solution?"
                  },
                  description: {
                    type: "string",
                    title: "Section Description",
                    minLength: 2,
                    maxLength: 200,
                    default: defaultPricingThreeProps.customSolution?.description || "Get in touch with our sales team and we'll help you find the perfect solution for your business needs."
                  },
                  button: {
                    type: "object",
                    title: "Button Settings",
                    properties: {
                      label: {
                        type: "string",
                        title: "Button Text",
                        minLength: 2,
                        maxLength: 30,
                        default: defaultPricingThreeProps.customSolution?.button?.label || "Contact sales"
                      },
                      href: {
                        type: "string",
                        title: "Button URL",
                        pattern: "^(?:\/[a-zA-Z0-9-_\/]*|https?:\/\/[^\\s]+|#[a-zA-Z0-9-_]+|mailto:[^\\s]+|tel:[^\\s]+)$",
                        default: defaultPricingThreeProps.customSolution?.button?.href || "/"
                      },
                      urlType: {
                        type: "string",
                        title: "URL Type",
                        enum: ["internal", "external", "anchor", "email", "phone"],
                        default: defaultPricingThreeProps.customSolution?.button?.urlType || "internal"
                      }
                    },
                    required: ["label", "href", "urlType"]
                  }
                },
                required: ["title", "description", "button"]
              },
              {
                properties: {
                  enabled: { enum: [false] }
                }
              }
            ]
          }
        }
      }
    },
    required: ["title", "description", "tiers"],
    dependencies: {
      frequencies: {
        oneOf: [
          {
            properties: {
              frequencies: {
                properties: {
                  enabled: { enum: [true] }
                }
              },
              tiers: {
                type: "array",
                items: {
                  properties: {
                    price: {
                      type: "object",
                      properties: {
                        monthly: {
                          type: "number",
                          title: "Monthly Price",
                          minimum: 0,
                          default: 29
                        },
                        annually: {
                          type: "number",
                          title: "Annual Price",
                          minimum: 0,
                          default: 290
                        },
                        currency: {
                          type: "string",
                          title: "Currency",
                          enum: ["USD", "EUR", "GBP", "JPY", "CNY", "CAD", "AUD", "NONE"],
                          default: "USD"
                        },
                        monthlyDescription: {
                          type: "string",
                          title: "Monthly Price Description",
                          description: "Description text shown when monthly frequency is selected",
                          default: "Billed monthly. {annually} per month if paid annually"
                        },
                        annuallyDescription: {
                          type: "string",
                          title: "Annual Price Description",
                          description: "Description text shown when annual frequency is selected",
                          default: "Billed annually. {monthly} per month if paid monthly"
                        }
                      },
                      required: ["monthly", "annually", "currency"]
                    }
                  }
                }
              }
            }
          },
          {
            properties: {
              frequencies: {
                properties: {
                  enabled: { enum: [false] }
                }
              },
              tiers: {
                type: "array",
                items: {
                  properties: {
                    price: {
                      type: "object",
                      properties: {
                        amount: {
                          type: "number",
                          title: "Price Amount",
                          minimum: 0,
                          default: 29
                        },
                        currency: {
                          type: "string",
                          title: "Currency",
                          enum: ["USD", "EUR", "GBP", "JPY", "CNY", "CAD", "AUD", "NONE"],
                          default: "USD"
                        },
                        description: {
                          type: "string",
                          title: "Price Description",
                          description: "Description text shown below the price",
                          default: "One-time payment"
                        }
                      },
                      required: ["amount", "currency"]
                    }
                  }
                }
              }
            }
          }
        ]
      }
    }
  },
  uiSchema: {
    "ui:order": ["tagline", "title", "description", "frequencies", "tiers", "customSolution"],
    
    tagline: {
      "ui:widget": "text",
      "ui:options": {
        help: "Short text displayed above the main title",
        placeholder: "Enter a tagline...",
        fullWidth: true
      }
    },
    
    title: {
      "ui:widget": "text",
      "ui:options": {
        help: "Enter your main headline...",
        placeholder: "Enter your main headline...",
        fullWidth: true
      }
    },
    
    description: {
      "ui:widget": "textarea",
      "ui:options": {
        rows: 3,
        placeholder: "Describe your pricing offering...",
        fullWidth: true
      }
    },
    
    frequencies: {
      "ui:order": ["enabled", "billingPeriod", "options"],
      "ui:options": {
        expandable: true,
        expanded: false,
        title: "Billing Frequencies"
      },
      enabled: {
        "ui:widget": "switch",
        "ui:options": {
          help: "Enable this to allow users to switch between different billing frequencies"
        }
      },
      billingPeriod: {
        "ui:order": ["type", "customText"],
        "ui:options": {
          expandable: true,
          expanded: true,
          title: "Default Billing Period"
        },
        type: {
          "ui:widget": "buttonGroup",
          "ui:options": {
            help: "Select the billing period type for your pricing"
          }
        },
        customText: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "e.g., /week, /day",
            help: "Custom text to display after the price"
          }
        }
      },
      options: {
        "ui:options": {
          addable: false,
          removable: false,
          orderable: false,
          help: "Configure billing frequencies (monthly and annually)"
        },
        items: {
          "ui:order": ["value", "label", "priceSuffix"],
          value: {
            "ui:widget": "hidden",
            "ui:options": {
              help: "Internal value (monthly/annually)"
            }
          },
          label: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "e.g., Monthly, Annually",
              help: "Display label for users"
            }
          },
          priceSuffix: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "e.g., /month, /year",
              help: "Price suffix such as /month or /year"
            }
          }
        }
      }
    },
    
    tiers: {
      "ui:options": {
        addable: false,
        removable: false,
        orderable: false,
        help: "Configure pricing tiers. Each tier represents a pricing plan."
      },
      items: {
        "ui:order": ["name", "mostPopular", "price", "description", "features", "button"],
        "ui:options": {
          expandable: true,
          expanded: false
        },
        
        name: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "e.g., Basic, Professional, Enterprise",
            help: "Name of the pricing tier"
          }
        },
        mostPopular: {
          "ui:widget": "checkbox",
          "ui:options": {
            help: "Highlight this tier as the most popular option"
          }
        },
        price: {
          "ui:order": ["monthly", "annually", "amount", "currency", "monthlyDescription", "annuallyDescription", "description"],
          "ui:options": {
            expandable: true,
            expanded: true,
            title: "Price Information"
          },
          monthly: {
            "ui:widget": "updown",
            "ui:options": {
              help: "Monthly price amount"
            }
          },
          annually: {
            "ui:widget": "updown",
            "ui:options": {
              help: "Annual price amount"
            }
          },
          amount: {
            "ui:widget": "updown",
            "ui:options": {
              help: "Price amount"
            }
          },
          currency: {
            "ui:widget": "select",
            "ui:options": {
              help: "Select the currency symbol"
            }
          },
          monthlyDescription: {
            "ui:widget": "textarea",
            "ui:options": {
              rows: 2,
              placeholder: "e.g., Billed monthly. {annually} per month if paid annually",
              help: "Use {annually} as placeholder for the annual price"
            }
          },
          annuallyDescription: {
            "ui:widget": "textarea",
            "ui:options": {
              rows: 2,
              placeholder: "e.g., Billed annually. {monthly} per month if paid monthly",
              help: "Use {monthly} as placeholder for the monthly price"
            }
          },
          description: {
            "ui:widget": "textarea",
            "ui:options": {
              rows: 2,
              placeholder: "e.g., One-time payment",
              help: "Description text shown below the price"
            }
          }
        },
        description: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 2,
            placeholder: "Describe this pricing tier...",
            help: "Brief description of this pricing tier, displayed below the tier name"
          }
        },
        features: {
          "ui:options": {
            addable: true,
            removable: true,
            addButtonLabel: "Add Feature",
            help: "List of features included in this pricing tier"
          },
          items: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "e.g., 10 users, Unlimited storage"
            }
          }
        },
        button: {
          "ui:order": ["label", "href", "urlType"],
          "ui:options": {
            expandable: true,
            expanded: false,
            title: "Button Settings"
          },
          label: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "e.g., Get started, Buy now",
              help: "Text displayed on the button"
            }
          },
          href: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "e.g., /checkout, #pricing",
              help: "URL for the button"
            }
          },
          urlType: {
            "ui:widget": "buttonGroup",
            "ui:options": {
              help: "Select the type of URL"
            }
          }
        }
      }
    },
    customSolution: {
      "ui:order": ["enabled", "title", "description", "button"],
      "ui:options": {
        expandable: true,
        expanded: false,
        title: "Custom Solution Section"
      },
      enabled: {
        "ui:widget": "switch",
        "ui:options": {
          help: "Enable this to display a custom solution section"
        }
      },
      title: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "e.g., Need a custom solution?",
          help: "Title for the custom solution section"
        }
      },
      description: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 2,
          placeholder: "e.g., Get in touch with our sales team and we'll help you find the perfect solution for your business needs.",
          help: "Description for the custom solution section"
        }
      },
      button: {
        "ui:order": ["label", "href", "urlType"],
        "ui:options": {
          expandable: true,
          expanded: false,
          title: "Button Settings"
        },
        label: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "e.g., Contact sales",
            help: "Text displayed on the button"
          }
        },
        href: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "e.g., /contact, #sales",
            help: "URL for the button"
          }
        },
        urlType: {
          "ui:widget": "buttonGroup",
          "ui:options": {
            help: "Select the type of URL"
          }
        }
      }
    }
  }
};

export default pricingThreeConfig;