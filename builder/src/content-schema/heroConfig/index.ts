import { defaultProps } from '@litpage/sections';
import { BlockFormConfig } from '../types';

const defaultHeroProps = defaultProps.Hero;

const heroConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      title: {
        type: "string",
        minLength: 2,
        default: defaultHeroProps.title
      },
      description: {
        type: "string",
        minLength: 2,
        default: defaultHeroProps.description
      },
      image: {
        type: "object",
        properties: {
          url: {
            type: "string",
            pattern: "^(https?://.*|/.+)$",
            default: defaultHeroProps.image.url
          },
          alt: {
            type: "string",
            default: defaultHeroProps.image.alt
          }
        },
        required: ["url"]
      },
      buttons: {
        type: "array",
        title: "Action Buttons",
        default: defaultHeroProps.buttons,
        items: {
          type: "object",
          required: ['text'],
          properties: {
            id: { 
              type: 'string',
              readOnly: true
            },
            text: { type: 'string', default: 'New Button' },
            buttonTarget: {
              type: 'object',
              required: ['type'],
              properties: {
                type: { 
                  type: 'string', 
                  enum: ['url', 'internal', 'email', 'phone'],
                  default: 'internal'
                },
                value: { 
                  type: 'string',
                  default: ''
                },
              },
              if: {
                properties: {
                  type: { const: 'url' }
                }
              },
              then: {
                properties: {
                  openInNewWindow: {
                    type: 'boolean',
                    default: false
                  }
                }
              },
            },
            // appearance: {
            //   type: 'object',
            //   properties: {
            //     displayType: { 
            //       type: 'string', 
            //       enum: ['link', 'button'],
            //       default: 'button'
            //     },
            //     style: { 
            //       type: 'string', 
            //       enum: ['default', 'primary', 'secondary'],
            //       default: 'primary'
            //     },
            //     size: { 
            //       type: 'string', 
            //       enum: ['small', 'medium', 'large'],
            //       default: 'medium'
            //     }
            //   },
            //   default: {
            //     displayType: 'button',
            //     style: 'primary',
            //     size: 'medium'
            //   }
            // }
          }
        },
        maxItems: 2
      }
    },
    required: ["title", "description"]
  },
  uiSchema: {
    description: {
      'ui:widget': 'textarea'
    },
    image: {
      'ui:field': 'ImageSelector'
    },
    buttons: {
      'ui:options': {
        orderable: true,
        addable: true,
        removable: true
      },
      items: {
        id: {
          "ui:widget": "hidden"
        }
      }
    }
  }
};

export default heroConfig;