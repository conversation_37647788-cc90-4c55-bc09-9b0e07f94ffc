import { defaultProps } from '@litpage/sections';
import { BlockFormConfig } from '../types';

const defaultStatsProps = defaultProps.Stats;

const statsConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      tagline: {
        type: "string",
        title: "Tagline",
        // minLength: 2,
        maxLength: 50,
        default: defaultStatsProps.tagline
      },
      title: {
        type: "string",
        title: "Main Title",
        minLength: 2,
        maxLength: 70,
        default: defaultStatsProps.title
      },
      description: {
        type: "string",
        title: "Description",
        minLength: 2,
        maxLength: 160,
        default: defaultStatsProps.description
      },
      stats: {
        type: "array",
        title: "Statistics",
        description: "Add statistics to display",
        minItems: 3,
        maxItems: 4,
        items: {
          type: "object",
          properties: {
            name: {
              type: "string",
              title: "Statistic Name",
              minLength: 2,
              maxLength: 50,
              default: "Transactions every 24 hours"
            },
            value: {
              type: "string",
              title: "Statistic Value",
              minLength: 1,
              maxLength: 20,
              default: "44 million"
            }
          },
          required: ["name", "value"]
        },
        default: defaultStatsProps.stats
      }
    },
    required: ["title", "description", "stats"]
  },
  uiSchema: {
    "ui:order": ["tagline", "title", "description", "stats"],
    tagline: {
      'ui:widget': 'text'
    },
    description: {
      'ui:widget': 'textarea'
    },
    stats: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true
      },
      items: {
        "ui:order": ["name", "value"]
      }
    }
  }
};

export default statsConfig;