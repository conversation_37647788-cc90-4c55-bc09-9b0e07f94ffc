import { defaultProps } from '@litpage/sections';
import { BlockFormConfig } from '../types';

const defaultHeroTextProps = defaultProps.HeroText;

const heroTextConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      title: {
        type: "object",
        title: "Title Settings",
        properties: {
          text: {
            type: "string",
            title: "Main Title",
            minLength: 2,
            maxLength: 70,
            default: defaultHeroTextProps.title.text
          },
          highlight: {
            type: "object",
            title: "Highlight Text Settings",
            properties: {
              enabled: {
                type: "boolean",
                title: "Enable Highlight",
                default: defaultHeroTextProps.title.highlight.enabled
              }
            },
            dependencies: {
              enabled: {
                oneOf: [
                  {
                    properties: {
                      enabled: { enum: [true] },
                      text: {
                        type: "string",
                        title: "Highlight Content",
                        minLength: 2,
                        maxLength: 30,
                        default: defaultHeroTextProps.title.highlight.text
                      },
                      style: {
                        type: "string",
                        title: "Highlight Style",
                        enum: ["gradient-blue", "gradient-purple", "solid-primary", "solid-secondary"],
                        default: defaultHeroTextProps.title.highlight.style
                      }
                    },
                    required: ["text", "style"]
                  },
                  {
                    properties: {
                      enabled: { enum: [false] }
                    }
                  }
                ]
              }
            }
          }
        },
        required: ["text", "highlight"]
      },
      description: {
        type: "string",
        title: "Description",
        minLength: 2,
        maxLength: 160,
        default: defaultHeroTextProps.description
      },
      announcement: {
        type: "object",
        title: "Announcement Settings",
        properties: {
          enabled: {
            type: "boolean",
            title: "Show Announcement",
            default: defaultHeroTextProps.announcement.enabled
          }
        },
        dependencies: {
          enabled: {
            oneOf: [
              {
                properties: {
                  enabled: { enum: [true] },
                  text: {
                    type: "string",
                    title: "Announcement Text",
                    maxLength: 100,
                    default: defaultHeroTextProps.announcement.text
                  },
                  url: {
                    type: "string",
                    title: "Link URL",
                    pattern: "^(?:\/[a-zA-Z0-9-_\/]*|https?:\/\/[^\\s]+|#[a-zA-Z0-9-_]+|mailto:[^\\s]+|tel:[^\\s]+)$",
                    default: defaultHeroTextProps.announcement.url
                  },
                  urlType: {
                    type: "string",
                    title: "Link Type",
                    enum: ["internal", "external", "anchor"],
                    default: defaultHeroTextProps.announcement.urlType
                  }
                },
                required: ["text", "url", "urlType"]
              },
              {
                properties: {
                  enabled: { enum: [false] }
                }
              }
            ]
          }
        }
      },
      buttons: {
        type: "array",
        title: "Button Settings",
        items: {
          type: "object",
          title: "Button",
          properties: {
            label: {
              type: "string",
              title: "Button Text",
              minLength: 2,
              maxLength: 20,
              default: defaultHeroTextProps.buttons[0].label
            },
            url: {
              type: "string",
              title: "Button URL",
              pattern: "^(?:\/[a-zA-Z0-9-_\/]*|https?:\/\/[^\\s]+|#[a-zA-Z0-9-_]+|mailto:[^\\s]+|tel:[^\\s]+)$",
              default: defaultHeroTextProps.buttons[0].url
            },
            urlType: {
              type: "string",
              title: "URL Type",
              enum: ["internal", "external", "anchor", "email", "phone"],
              default: defaultHeroTextProps.buttons[0].urlType
            },
            style: {
              type: "string",
              title: "Button Style",
              enum: ["primary", "secondary", "outline", "text"],
              default: defaultHeroTextProps.buttons[0].variant
            },
            size: {
              type: "string",
              title: "Button Size",
              enum: ["small", "medium", "large"],
              default: defaultHeroTextProps.buttons[0].size
            },
            icon: {
              type: "object",
              title: "Button Icon",
              properties: {
                enabled: {
                  type: "boolean",
                  title: "Show Icon",
                  default: defaultHeroTextProps.buttons[0].icon.enabled
                }
              },
              dependencies: {
                enabled: {
                  oneOf: [
                    {
                      properties: {
                        enabled: { enum: [true] },
                        name: {
                          type: "string",
                          title: "Icon Name",
                          default: defaultHeroTextProps.buttons[0].icon.name || "arrow-right"
                        },
                        position: {
                          type: "string",
                          title: "Icon Position",
                          enum: ["left", "right"],
                          default: defaultHeroTextProps.buttons[0].icon.position
                        }
                      },
                      required: ["name", "position"]
                    },
                    {
                      properties: {
                        enabled: { enum: [false] }
                      }
                    }
                  ]
                }
              }
            }
          },
          required: ["label", "url", "urlType", "style", "size", "icon"]
        },
        default: [
          {
            label: defaultHeroTextProps.buttons[0].label,
            url: defaultHeroTextProps.buttons[0].url,
            urlType: defaultHeroTextProps.buttons[0].urlType,
            style: defaultHeroTextProps.buttons[0].variant,
            size: defaultHeroTextProps.buttons[0].size,
            icon: {
              enabled: defaultHeroTextProps.buttons[0].icon.enabled,
              name: defaultHeroTextProps.buttons[0].icon.name,
              position: defaultHeroTextProps.buttons[0].icon.position
            }
          },
          {
            label: defaultHeroTextProps.buttons[1].label,
            url: defaultHeroTextProps.buttons[1].url,
            urlType: defaultHeroTextProps.buttons[1].urlType,
            style: defaultHeroTextProps.buttons[1].variant,
            size: defaultHeroTextProps.buttons[1].size,
            icon: {
              enabled: defaultHeroTextProps.buttons[1].icon.enabled,
              name: defaultHeroTextProps.buttons[1].icon.name,
              position: defaultHeroTextProps.buttons[1].icon.position
            }
          }
        ],
        maxItems: 2,
        minItems: 1
      }
    },
    required: ["title", "description", "buttons"]
  },
  uiSchema: {
    "ui:order": ["title", "description", "announcement", "buttons"],
    
    title: {
      text: {
        "ui:widget": "text",
        "ui:options": {
          help: "Enter your main headline...",
          placeholder: "Enter your main headline...",
          fullWidth: true
        }
      },
      highlight: {
        enabled: {
          "ui:widget": "switch",
          "ui:options": {
            trueLabel: "On",
            falseLabel: "Off"
          }
        },
        text: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Enter text to highlight..."
          }
        },
        style: {
          "ui:widget": "buttonGroup",
          "ui:options": {
            enumOptions: [
              { value: "gradient-blue", label: "Blue Gradient" },
              { value: "gradient-purple", label: "Purple Gradient" },
              { value: "solid-primary", label: "Primary Color" },
              { value: "solid-secondary", label: "Secondary Color" }
            ]
          }
        }
      }
    },
    
    description: {
      "ui:widget": "textarea",
      "ui:options": {
        rows: 3,
        placeholder: "Describe the main value proposition of your product or service...",
        fullWidth: true
      }
    },
    
    announcement: {
      "ui:options": {
      },
      enabled: {
        "ui:widget": "switch",
        "ui:options": {
          trueLabel: "Show",
          falseLabel: "Hide"
        }
      },
      text: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "Use emoji to make your announcement stand out, e.g., 🎉 or 🚀",
          fullWidth: true
        }
      },
      url: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "/whats-new"
        }
      },
      urlType: {
        "ui:widget": "buttonGroup",
        "ui:options": {
          enumOptions: [
            { value: "internal", label: "Internal" },
            { value: "external", label: "External" },
            { value: "anchor", label: "Anchor" }
          ]
        }
      }
    },
    
    buttons: {
      "ui:options": {
        addable: true,
        removable: true,
        addButtonLabel: "Add Button"
      },
      items: {
        "ui:options": {
        },
        label: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "e.g., Get Started, Learn More"
          }
        },
        url: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "e.g., /contact, https://example.com, #section"
          }
        },
        urlType: {
          "ui:widget": "buttonGroup",
          "ui:options": {
            enumOptions: [
              { value: "internal", label: "Internal" },
              { value: "external", label: "External" },
              { value: "anchor", label: "Anchor" },
              { value: "email", label: "Email" },
              { value: "phone", label: "Phone" }
            ]
          }
        },
        style: {
          "ui:widget": "buttonGroup",
          "ui:options": {
            enumOptions: [
              { value: "primary", label: "Primary" },
              { value: "secondary", label: "Secondary" },
              { value: "outline", label: "Outline" },
              { value: "text", label: "Text Only" }
            ]
          }
        },
        size: {
          "ui:widget": "buttonGroup",
          "ui:options": {
            enumOptions: [
              { value: "small", label: "Small" },
              { value: "medium", label: "Medium" },
              { value: "large", label: "Large" }
            ]
          }
        },
        icon: {
          "ui:options": {
          },
          enabled: {
            "ui:widget": "switch",
            "ui:options": {
              trueLabel: "Show",
              falseLabel: "Hide"
            }
          },
          name: {
            "ui:widget": "iconSelect"
          },
          position: {
            "ui:widget": "buttonGroup",
            "ui:options": {
              enumOptions: [
                { value: "left", label: "Left" },
                { value: "right", label: "Right" }
              ]
            }
          }
        }
      }
    }
  }
};

export default heroTextConfig;