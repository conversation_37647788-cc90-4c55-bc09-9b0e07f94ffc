// src/components/BlockEditor/configs/faqConfig.ts

import { defaultProps } from '@litpage/sections';
import { BlockFormConfig } from '../types';

const defaultFaqProps = defaultProps.FAQ;

const faqConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      title: {
        type: "string",
        title: "Title",
        minLength: 2,
        maxLength: 70,
        default: defaultFaqProps.title
      },
      tagline: {
        type: "string",
        title: "Tagline",
        // minLength: 2,
        maxLength: 30,
        default: defaultFaqProps.tagline
      },
      description: {
        type: "string",
        title: "Description",
        minLength: 5,
        maxLength: 160,
        default: defaultFaqProps.description
      },
      faqs: {
        type: "array",
        title: "FAQ Items",
        items: {
          type: "object",
          properties: {
            question: {
              type: "string",
              title: "Question",
              minLength: 5,
              maxLength: 200
            },
            answer: {
              type: "string",
              title: "Answer",
              minLength: 5,
              maxLength: 1000
            }
          },
          required: ["question", "answer"]
        },
        minItems: 1,
        default: defaultFaqProps.faqs
      },
      contactSection: {
        type: "object",
        title: "Contact Section",
        properties: {
          enabled: {
            type: "boolean",
            title: "Enable Contact Section",
            default: defaultFaqProps.contactSection.enabled
          }
        },
        dependencies: {
          enabled: {
            oneOf: [
              {
                properties: {
                  enabled: { enum: [true] },
                  text: {
                    type: "string",
                    title: "Contact Text",
                    minLength: 5,
                    maxLength: 100,
                    default: defaultFaqProps.contactSection.text
                  },
                  button: {
                    type: "object",
                    title: "Contact Button",
                    properties: {
                      label: {
                        type: "string",
                        title: "Button Text",
                        minLength: 2,
                        maxLength: 30,
                        default: defaultFaqProps.contactSection.button.label
                      },
                      href: {
                        type: "string",
                        title: "Button URL",
                        pattern: "^(?:\/[a-zA-Z0-9-_\/]*|https?:\/\/[^\\s]+|#[a-zA-Z0-9-_]+|mailto:[^\\s]+|tel:[^\\s]+)$",
                        default: defaultFaqProps.contactSection.button.href
                      },
                      urlType: {
                        type: "string",
                        title: "URL Type",
                        enum: ["internal", "external", "anchor", "email", "phone"],
                        default: defaultFaqProps.contactSection.button.urlType
                      }
                    },
                    required: ["label", "href", "urlType"]
                  }
                },
                required: ["text", "button"]
              },
              {
                properties: {
                  enabled: { enum: [false] }
                }
              }
            ]
          }
        }
      }
    },
    required: ["title", "description", "faqs"]
  },
  uiSchema: {
    "ui:order": ["tagline", "title", "description", "faqs", "contactSection"],
    
    tagline: {
      "ui:widget": "text",
      "ui:placeholder": "Enter a tagline",
      "ui:help": "A short tagline displayed above the title"
    },
    
    title: {
      "ui:widget": "textarea",
      "ui:placeholder": "Enter a title",
      "ui:help": "The main title for the FAQ section",
      "ui:options": {
        rows: 2
      }
    },
    
    description: {
      "ui:widget": "textarea",
      "ui:placeholder": "Enter a description",
      "ui:help": "A brief description shown below the title",
      "ui:options": {
        rows: 2
      }
    },
    
    faqs: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true
      },
      items: {
        "ui:order": ["question", "answer"],
        
        question: {
          "ui:widget": "textarea",
          "ui:placeholder": "Enter a question",
          "ui:help": "The question to be answered",
          "ui:options": {
            rows: 2
          }
        },
        
        answer: {
          "ui:widget": "textarea",
          "ui:placeholder": "Enter an answer",
          "ui:help": "The answer to the question",
          "ui:options": {
            rows: 4
          }
        }
      }
    },
    
    contactSection: {
      "ui:order": ["enabled", "text", "button"],
      "ui:options": {
        expandable: true,
        expanded: true
      },
      
      enabled: {
        "ui:widget": "switch",
        "ui:help": "Enable to show a contact section below the FAQ items"
      },
      
      text: {
        "ui:widget": "text",
        "ui:placeholder": "Enter contact text",
        "ui:help": "The text displayed before the contact button"
      },
      
      button: {
        "ui:order": ["label", "href", "urlType"],
        "ui:options": {
          expandable: true,
          expanded: false
        },
        
        label: {
          "ui:widget": "text",
          "ui:placeholder": "Enter button text",
          "ui:help": "The text displayed on the button"
        },
        
        href: {
          "ui:widget": "text",
          "ui:placeholder": "Enter button URL",
          "ui:help": "The URL the button links to"
        },
        
        urlType: {
          "ui:widget": "select",
          "ui:placeholder": "Select URL type",
          "ui:help": "The type of URL (internal, external, anchor, etc.)"
        }
      }
    }
  }
};

export default faqConfig;