import { defaultProps } from '@litpage/sections';

import { BlockFormConfig } from '../types';

const defaultCTAImageProps = defaultProps.CTAImage;

const ctaImageConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      tagline: {
        type: "string",
        title: "Tagline",
        description: "Short text displayed above the title",
        // minLength: 2,
        maxLength: 50,
        default: defaultCTAImageProps.tagline
      },
      title: {
        type: "object",
        title: "Title Settings",
        properties: {
          text: {
            type: "string",
            title: "Main Title",
            minLength: 2,
            maxLength: 70,
            default: defaultCTAImageProps.title.text
          },
          highlight: {
            type: "object",
            title: "Highlight Text Settings",
            properties: {
              enabled: {
                type: "boolean",
                title: "Enable Highlight",
                default: defaultCTAImageProps.title.highlight.enabled
              }
            },
            dependencies: {
              enabled: {
                oneOf: [
                  {
                    properties: {
                      enabled: { enum: [true] },
                      text: {
                        type: "string",
                        title: "Highlight Content",
                        minLength: 2,
                        maxLength: 30,
                        default: defaultCTAImageProps.title.highlight.text
                      },
                      style: {
                        type: "string",
                        title: "Highlight Style",
                        enum: ["gradient-blue", "gradient-purple", "solid-primary", "solid-secondary"],
                        default: defaultCTAImageProps.title.highlight.style
                      }
                    },
                    required: ["text", "style"]
                  },
                  {
                    properties: {
                      enabled: { enum: [false] }
                    }
                  }
                ]
              }
            }
          }
        },
        required: ["text", "highlight"]
      },
      description: {
        type: "string",
        title: "Description",
        minLength: 2,
        maxLength: 160,
        default: defaultCTAImageProps.description
      },
      image: {
        type: "object",
        title: "Image Settings",
        properties: {
          url: {
            type: "string",
            title: "Image URL",
            pattern: "^(https?://.*|/.+)$",
            default: defaultCTAImageProps.image.url
          },
          alt: {
            type: "string",
            title: "Alt Text",
            minLength: 2,
            maxLength: 100,
            default: defaultCTAImageProps.image.alt
          }
        },
        required: ["url", "alt"]
      },
      buttons: {
        type: "array",
        title: "Button Settings",
        items: {
          type: "object",
          title: "Button",
          properties: {
            label: {
              type: "string",
              title: "Button Text",
              minLength: 2,
              maxLength: 20,
              default: defaultCTAImageProps.buttons[0].label
            },
            url: {
              type: "string",
              title: "Button URL",
              pattern: "^(?:\/[a-zA-Z0-9-_\/]*|https?:\/\/[^\\s]+|#[a-zA-Z0-9-_]+|mailto:[^\\s]+|tel:[^\\s]+)$",
              default: defaultCTAImageProps.buttons[0].url
            },
            urlType: {
              type: "string",
              title: "URL Type",
              enum: ["internal", "external", "anchor", "email", "phone"],
              default: defaultCTAImageProps.buttons[0].urlType
            },
            style: {
              type: "string",
              title: "Button Style",
              enum: ["primary", "secondary", "outline", "text"],
              default: defaultCTAImageProps.buttons[0].style
            },
            size: {
              type: "string",
              title: "Button Size",
              enum: ["small", "medium", "large"],
              default: defaultCTAImageProps.buttons[0].size
            },
            icon: {
              type: "object",
              title: "Button Icon",
              properties: {
                enabled: {
                  type: "boolean",
                  title: "Show Icon",
                  default: defaultCTAImageProps.buttons[0].icon.enabled
                }
              },
              dependencies: {
                enabled: {
                  oneOf: [
                    {
                      properties: {
                        enabled: { enum: [true] },
                        name: {
                          type: "string",
                          title: "Icon Name",
                          default: defaultCTAImageProps.buttons[0].icon.name
                        },
                        position: {
                          type: "string",
                          title: "Icon Position",
                          enum: ["left", "right"],
                          default: defaultCTAImageProps.buttons[0].icon.position
                        }
                      },
                      required: ["name", "position"]
                    },
                    {
                      properties: {
                        enabled: { enum: [false] }
                      }
                    }
                  ]
                }
              }
            }
          },
          required: ["label", "url", "urlType", "style", "size", "icon"]
        },
        default: defaultCTAImageProps.buttons,
        maxItems: 2,
        minItems: 1
      }
    },
    required: ["title", "description", "buttons", "image"]
  },
  uiSchema: {
    "ui:order": ["tagline", "title", "description", "image", "buttons"],
    
    tagline: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "Enter a short tagline...",
        fullWidth: true
      }
    },
    
    title: {
      text: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "Enter your main headline...",
          fullWidth: true
        }
      },
      highlight: {
        enabled: {
          "ui:widget": "switch",
          "ui:options": {
            trueLabel: "On",
            falseLabel: "Off"
          }
        },
        text: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Enter text to highlight..."
          }
        },
        style: {
          "ui:widget": "buttonGroup",
          "ui:options": {
            enumOptions: [
              { value: "gradient-blue", label: "Blue Gradient" },
              { value: "gradient-purple", label: "Purple Gradient" },
              { value: "solid-primary", label: "Primary Color" },
              { value: "solid-secondary", label: "Secondary Color" }
            ]
          }
        }
      }
    },
    
    description: {
      "ui:widget": "textarea",
      "ui:options": {
        rows: 3,
        placeholder: "Describe the main value proposition of your product or service...",
        fullWidth: true
      }
    },
    
    image: {
      url: {
        "ui:widget": "imageSelector",
        "ui:options": {
          placeholder: "Select or upload an image..."
        }
      },
      alt: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "Describe the image for accessibility..."
        }
      }
    },
    
    buttons: {
      "ui:options": {
        addable: true,
        removable: true,
        addButtonLabel: "Add Button"
      },
      items: {
        "ui:options": {
        },
        label: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "e.g., Get Started, Learn More"
          }
        },
        url: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "e.g., /contact, https://example.com, #section"
          }
        },
        urlType: {
          "ui:widget": "buttonGroup",
          "ui:options": {
            enumOptions: [
              { value: "internal", label: "Internal" },
              { value: "external", label: "External" },
              { value: "anchor", label: "Anchor" },
              { value: "email", label: "Email" },
              { value: "phone", label: "Phone" }
            ]
          }
        },
        style: {
          "ui:widget": "buttonGroup",
          "ui:options": {
            enumOptions: [
              { value: "primary", label: "Primary" },
              { value: "secondary", label: "Secondary" },
              { value: "outline", label: "Outline" },
              { value: "text", label: "Text Only" }
            ]
          }
        },
        size: {
          "ui:widget": "buttonGroup",
          "ui:options": {
            enumOptions: [
              { value: "small", label: "Small" },
              { value: "medium", label: "Medium" },
              { value: "large", label: "Large" }
            ]
          }
        },
        icon: {
          "ui:options": {
          },
          enabled: {
            "ui:widget": "switch",
            "ui:options": {
              trueLabel: "Show",
              falseLabel: "Hide"
            }
          },
          name: {
            "ui:widget": "iconSelect"
          },
          position: {
            "ui:widget": "buttonGroup",
            "ui:options": {
              enumOptions: [
                { value: "left", label: "Left" },
                { value: "right", label: "Right" }
              ]
            }
          }
        }
      }
    }
  }
};

export default ctaImageConfig;