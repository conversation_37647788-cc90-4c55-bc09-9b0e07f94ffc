// src/components/BlockEditor/configs/secondaryFeaturesConfig.ts

import { BlockFormConfig } from '../types';

const secondaryFeaturesConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      title: {
        type: "string",
        minLength: 2,
        title: "Section Title"
      },
      description: {
        type: "string",
        minLength: 5,
        title: "Section Description"
      },
      features: {
        type: "array",
        title: "Features",
        items: {
          type: "object",
          properties: {
            title: {
              type: "string",
              minLength: 2,
              title: "Feature Title"
            },
            description: {
              type: "string",
              minLength: 5,
              title: "Feature Description"
            },
            image: {
              type: "object",
              title: "Feature Image",
              properties: {
                url: {
                  type: "string",
                  pattern: "^(https?://.*|/.+)$",
                  title: "Image URL"
                },
                alt: {
                  type: "string",
                  minLength: 1,
                  title: "Image Alt Text"
                }
              },
              required: ["url", "alt"]
            },
            icon: {
              type: "string",
              title: "Feature Icon",
              enum: [
                "ChartBarIcon",
                "CursorArrowRaysIcon",
                "FingerPrintIcon",
                "LockClosedIcon",
                "ServerIcon",
                "ArrowPathIcon"
              ]
            }
          },
          required: ["title", "description"]
        },
        minItems: 1,
        maxItems: 8  // Secondary features 可以显示更多项目
      }
    },
    required: ["title", "description", "features"]
  },
  uiSchema: {
    "ui:order": ["title", "description", "features"],
    title: {
      "ui:placeholder": "Enter section title"
    },
    description: {
      "ui:widget": "textarea",
      "ui:placeholder": "Enter section description",
      "ui:options": {
        rows: 3
      }
    },
    features: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true
      },
      items: {
        "ui:order": ["title", "description", "image", "icon"],
        description: {
          "ui:widget": "textarea",
          "ui:placeholder": "Enter feature description",
          "ui:options": {
            rows: 2  // Secondary features 描述通常更短
          }
        },
        image: {
          "ui:field": "ImageSelector"
        },
        icon: {
          "ui:widget": "select",
          "ui:placeholder": "Select an icon"
        }
      }
    }
  }
};

export default secondaryFeaturesConfig;