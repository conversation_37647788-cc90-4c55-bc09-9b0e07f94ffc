import { BlockFormConfig } from '../types';

const documentationConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      title: {
        type: "string",
        title: "Document Title",
        minLength: 5,
        maxLength: 100,
        default: "Getting Started with Our API"
      },
      slug: {
        type: "string",
        title: "URL Slug",
        pattern: "^[a-z0-9]+(?:-[a-z0-9]+)*$",
        minLength: 3,
        maxLength: 50,
        default: "getting-started-api"
      },
      category: {
        type: "string",
        title: "Document Category",
        minLength: 2,
        maxLength: 50,
        default: "API Documentation"
      },
      docType: {
        type: "string",
        title: "Document Type",
        enum: ["guide", "api", "tutorial", "reference", "faq"],
        default: "guide"
      },
      difficulty: {
        type: "string",
        title: "Difficulty Level",
        enum: ["beginner", "intermediate", "advanced"],
        default: "beginner"
      },
      summary: {
        type: "string",
        title: "Document Summary",
        minLength: 20,
        maxLength: 300,
        default: "Learn how to integrate with our API in just a few steps. This guide covers authentication, making your first request, and handling responses."
      },
      content: {
        type: "string",
        title: "Document Content",
        minLength: 50,
        default: `# Getting Started with Our API

## Introduction

Welcome to our API documentation. This guide will help you get up and running with our API in minutes.

## Authentication

All API requests require authentication using an API key. You can obtain your API key from your dashboard.

## Making Your First Request

Here's how to make your first API call to retrieve user information:

1. Set up your headers with authentication
2. Make a GET request to the users endpoint
3. Handle the response data

## Next Steps

Once you've made your first successful request, explore our other endpoints in the API reference.`
      },
      estimatedTime: {
        type: "integer",
        title: "Estimated Reading Time (minutes)",
        minimum: 1,
        maximum: 120,
        default: 10
      },
      prerequisites: {
        type: "array",
        title: "Prerequisites",
        items: {
          type: "string",
          minLength: 5,
          maxLength: 100
        },
        default: ["Basic knowledge of REST APIs", "API testing tool (Postman, curl)"],
        maxItems: 10,
        minItems: 0
      },
      codeExamples: {
        type: "array",
        title: "Code Examples",
        items: {
          type: "object",
          properties: {
            title: {
              type: "string",
              title: "Example Title",
              minLength: 5,
              maxLength: 100,
              default: "JavaScript Fetch Example"
            },
            description: {
              type: "string",
              title: "Example Description",
              maxLength: 200,
              default: "Basic API request using modern fetch API"
            },
            content: {
              type: "string",
              title: "Example Content",
              minLength: 10,
              default: `Here's how to make a request using JavaScript:

\`\`\`javascript
const response = await fetch('https://api.example.com/users', {
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  }
});

const data = await response.json();
console.log(data);
\`\`\``
            }
          },
          required: ["title", "content"]
        },
        default: [
          {
            title: "JavaScript Fetch Example",
            description: "Basic API request using modern fetch API",
            content: `Here's how to make a request using JavaScript:

\`\`\`javascript
const response = await fetch('https://api.example.com/users', {
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  }
});

const data = await response.json();
console.log(data);
\`\`\``
          },
          {
            title: "Python Requests Example",
            description: "API request using Python requests library",
            content: `Using Python to call the API:

\`\`\`python
import requests

headers = {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
}

response = requests.get('https://api.example.com/users', headers=headers)
data = response.json()
print(data)
\`\`\``
          }
        ],
        maxItems: 10,
        minItems: 0
      },
      relatedDocs: {
        type: "array",
        title: "Related Documents",
        items: {
          type: "string",
          minLength: 3,
          maxLength: 50
        },
        default: ["authentication-guide", "api-reference"],
        maxItems: 10,
        minItems: 0
      },
      lastUpdated: {
        type: "string",
        title: "Last Updated",
        format: "date",
        default: new Date().toISOString().split('T')[0]
      },
      version: {
        type: "string",
        title: "Document Version",
        pattern: "^\\d+\\.\\d+(\\.\\d+)?$",
        maxLength: 10,
        default: "1.0"
      },
      changelog: {
        type: "array",
        title: "Change History",
        items: {
          type: "object",
          properties: {
            version: {
              type: "string",
              title: "Version",
              pattern: "^\\d+\\.\\d+(\\.\\d+)?$",
              maxLength: 10,
              default: "1.0"
            },
            date: {
              type: "string",
              title: "Change Date",
              format: "date",
              default: new Date().toISOString().split('T')[0]
            },
            type: {
              type: "string",
              title: "Change Type",
              enum: ["added", "improved", "fixed", "deprecated", "removed"],
              default: "improved"
            },
            description: {
              type: "string",
              title: "Change Description",
              minLength: 5,
              maxLength: 200,
              default: "Updated content for better clarity"
            },
            details: {
              type: "string",
              title: "Change Details",
              maxLength: 500,
              default: ""
            }
          },
          required: ["version", "date", "type", "description"]
        },
        default: [
          {
            version: "1.0",
            date: new Date().toISOString().split('T')[0],
            type: "added",
            description: "Initial documentation created",
            details: "Created comprehensive API documentation with examples and best practices"
          },
          {
            version: "1.1",
            date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            type: "improved",
            description: "Enhanced code examples with better error handling",
            details: "Added try-catch blocks and response validation to all API examples"
          },
          {
            version: "1.2",
            date: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            type: "fixed",
            description: "Corrected authentication endpoint URL",
            details: "Fixed incorrect API endpoint reference in authentication section"
          }
        ],
        maxItems: 20,
        minItems: 0
      },
      seo: {
        type: "object",
        title: "SEO Settings",
        properties: {
          metaTitle: {
            type: "string",
            title: "Meta Title",
            maxLength: 60,
            default: "API Getting Started Guide - Complete Integration Tutorial"
          },
          metaDescription: {
            type: "string",
            title: "Meta Description",
            maxLength: 160,
            default: "Learn how to integrate with our API quickly. Step-by-step guide with code examples in JavaScript and Python."
          },
          keywords: {
            type: "array",
            title: "Keywords",
            items: {
              type: "string",
              minLength: 2,
              maxLength: 30
            },
            default: ["API", "getting started", "integration", "tutorial"],
            maxItems: 10,
            minItems: 0
          }
        },
        required: ["metaTitle", "metaDescription"]
      }
    },
    required: ["title", "slug", "category", "docType", "difficulty", "summary", "content", "lastUpdated", "seo"]
  },
  uiSchema: {
    "ui:order": [
      "title", "slug", "category", "docType", "difficulty", 
      "summary", "content", "estimatedTime", "prerequisites", 
      "codeExamples", "relatedDocs", "lastUpdated", "version", "changelog", "seo"
    ],
    
    title: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "Enter your documentation title...",
        help: "A clear, descriptive title for your documentation"
      }
    },
    
    slug: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "e.g., getting-started-api",
        help: "URL-friendly identifier (lowercase letters, numbers, and hyphens only. Cannot start or end with hyphens)"
      }
    },
    
    category: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "e.g., API Documentation, User Guides",
        help: "Category to organize your documentation"
      }
    },
    
    docType: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "guide", label: "📖 Guide - Step-by-step instructions" },
          { value: "api", label: "🔌 API - Technical API reference" },
          { value: "tutorial", label: "🎓 Tutorial - Learning-focused content" },
          { value: "reference", label: "📚 Reference - Quick lookup information" },
          { value: "faq", label: "❓ FAQ - Frequently asked questions" }
        ]
      }
    },
    
    difficulty: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "beginner", label: "🟢 Beginner - No prior experience needed" },
          { value: "intermediate", label: "🟡 Intermediate - Some experience required" },
          { value: "advanced", label: "🔴 Advanced - Expert level knowledge" }
        ]
      }
    },
    
    summary: {
      "ui:widget": "textarea",
      "ui:options": {
        rows: 3,
        placeholder: "Write a compelling summary that describes what readers will learn...",
        help: "This summary helps users understand the value and scope of your documentation"
      }
    },
    
    content: {
      "ui:widget": "markdown",
      "ui:options": {
        placeholder: "Write your documentation content using Markdown...",
        help: "Use headings (##) to create sections. Table of contents will be generated automatically from your headings.",
        defaultMode: "wysiwyg",
        showModeToggle: true,
        enableShortcuts: true,
        autoSave: false,
        height: 800,
        minHeight: 600
      }
    },
    
    estimatedTime: {
      "ui:widget": "updown",
      "ui:options": {
        placeholder: "10",
        help: "Estimated reading time in minutes"
      }
    },
    
    prerequisites: {
      "ui:widget": "tagInput",
      "ui:options": {
        placeholder: "Add prerequisites (press Enter to add each item)",
        help: "List any knowledge or tools users need before starting"
      }
    },
    
    codeExamples: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "Add Code Example"
      },
      items: {
        "ui:order": ["title", "description", "content"],
        
        title: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "e.g., JavaScript Fetch Example",
            help: "Descriptive title for this code example"
          }
        },
        
        description: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 2,
            placeholder: "Optional: Explain what this example demonstrates...",
            help: "Brief explanation of the code example"
          }
        },
        
        content: {
          "ui:widget": "markdown",
          "ui:options": {
            placeholder: "Write your code example with markdown formatting...",
            help: "Include code blocks with syntax highlighting using ```language",
            defaultMode: "wysiwyg",
            showModeToggle: true,
            enableShortcuts: true,
            autoSave: false
          }
        }
      }
    },
    
    relatedDocs: {
      "ui:widget": "tagInput",
      "ui:options": {
        placeholder: "Add related document IDs (press Enter to add each)",
        help: "Reference related documentation by their slug/ID"
      }
    },
    
    lastUpdated: {
      "ui:widget": "date",
      "ui:options": {
        placeholder: "Select last updated date"
      }
    },
    
    version: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "e.g., 1.0 or 2.1",
        help: "Document version for tracking changes"
      }
    },
    
    changelog: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "Add Change Entry"
      },
      items: {
        "ui:order": ["version", "date", "type", "description", "details"],
        
        version: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "e.g., 1.1, 2.0",
            help: "Version number for this change"
          }
        },
        
        date: {
          "ui:widget": "date",
          "ui:options": {
            placeholder: "Select change date"
          }
        },
        
        type: {
          "ui:widget": "select",
          "ui:options": {
            enumOptions: [
              { value: "added", label: "✨ Added - New content or features" },
              { value: "improved", label: "⚡ Improved - Enhanced existing content" },
              { value: "fixed", label: "🔧 Fixed - Corrected errors or issues" },
              { value: "deprecated", label: "⚠️ Deprecated - Content marked for removal" },
              { value: "removed", label: "🗑️ Removed - Content no longer available" }
            ]
          }
        },
        
        description: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Brief description of the change...",
            help: "Summarize what was changed in this version"
          }
        },
        
        details: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 2,
            placeholder: "Optional: Provide more detailed explanation...",
            help: "Additional context or migration notes"
          }
        }
      }
    },
    
    seo: {
      metaTitle: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "SEO-optimized title for search engines...",
          help: "Should be under 60 characters and include target keywords"
        }
      },
      
      metaDescription: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 2,
          placeholder: "Write a compelling description for search results...",
          help: "Should be under 160 characters and encourage clicks"
        }
      },
      
      keywords: {
        "ui:widget": "tagInput",
        "ui:options": {
          placeholder: "Add SEO keywords (press Enter to add each)",
          help: "Relevant keywords for search engine optimization"
        }
      }
    }
  }
};

export default documentationConfig;