import { defaultProps } from '@litpage/sections';
import { BlockFormConfig } from '../types';

// 币种映射表，用于将币种代码映射到符号和代码
const currencyMap = {
  USD: { symbol: "$", code: "USD" },
  EUR: { symbol: "€", code: "EUR" },
  GBP: { symbol: "£", code: "GBP" },
  JPY: { symbol: "¥", code: "JPY" },
  CNY: { symbol: "¥", code: "CNY" },
  CAD: { symbol: "C$", code: "CAD" },
  AUD: { symbol: "A$", code: "AUD" },
  NONE: { symbol: "", code: "" }
};

const defaultPricingTwoProps = defaultProps.PricingTwo;

const pricingTwoConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      tagline: {
        type: "string",
        title: "Tagline",
        // minLength: 2,
        maxLength: 50,
        default: defaultPricingTwoProps.tagline
      },
      title: {
        type: "string",
        title: "Main Title",
        minLength: 2,
        maxLength: 70,
        default: defaultPricingTwoProps.title
      },
      description: {
        type: "string",
        title: "Description",
        minLength: 2,
        maxLength: 160,
        default: defaultPricingTwoProps.description
      },
      frequencies: {
        type: "object",
        title: "Billing Frequencies",
        properties: {
          enabled: {
            type: "boolean",
            title: "Enable Multiple Frequencies",
            default: defaultPricingTwoProps.frequencies.enabled
          }
        },
        dependencies: {
          enabled: {
            oneOf: [
              {
                properties: {
                  enabled: { enum: [true] },
                  options: {
                    type: "array",
                    title: "Frequency Options",
                    items: {
                      type: "object",
                      properties: {
                        value: {
                          type: "string",
                          title: "Value",
                          enum: ["monthly", "yearly"],
                          default: "monthly",
                          readOnly: true
                        },
                        label: {
                          type: "string",
                          title: "Label",
                          minLength: 2,
                          maxLength: 20,
                          default: "Monthly"
                        },
                        priceSuffix: {
                          type: "string",
                          title: "Price Suffix",
                          minLength: 1,
                          maxLength: 10,
                          default: "/mo"
                        }
                      },
                      required: ["value", "label", "priceSuffix"]
                    },
                    default: defaultPricingTwoProps.frequencies.options || [
                      { value: "monthly", label: "Monthly", priceSuffix: "/mo" },
                      { value: "yearly", label: "Yearly", priceSuffix: "/yr" }
                    ],
                    maxItems: 2
                  }
                },
                required: ["options"]
              },
              {
                properties: {
                  enabled: { enum: [false] },
                  billingPeriod: {
                    type: "object",
                    title: "Default Billing Period",
                    properties: {
                      type: {
                        type: "string",
                        title: "Period Type",
                        enum: ["one-time", "monthly", "yearly", "per-user", "custom"],
                        default: defaultPricingTwoProps.frequencies.billingPeriod?.type || "monthly"
                      }
                    },
                    dependencies: {
                      type: {
                        oneOf: [
                          {
                            properties: {
                              type: { enum: ["custom"] },
                              customText: {
                                type: "string",
                                title: "Custom Period Text",
                                minLength: 1,
                                maxLength: 10,
                                default: defaultPricingTwoProps.frequencies.billingPeriod?.customText || "/week"
                              }
                            },
                            required: ["customText"]
                          },
                          {
                            properties: {
                              type: { enum: ["one-time", "monthly", "yearly", "per-user"] }
                            }
                          }
                        ]
                      }
                    }
                  }
                },
                required: ["billingPeriod"]
              }
            ]
          }
        }
      },
      tiers: {
        type: "array",
        title: "Pricing Tiers",
        description: "Two pricing tiers for the PricingTwo component",
        minItems: 2,
        maxItems: 2,
        items: {
          type: "object",
          properties: {
            name: {
              type: "string",
              title: "Tier Name",
              minLength: 2,
              maxLength: 30,
              default: "Hobby"
            },
            mostPopular: {
              type: "boolean",
              title: "Most Popular",
              default: false
            },
            price: {
              type: "object",
              title: "Price Information"
            },
            description: {
              type: "string",
              title: "Tier Description",
              minLength: 2,
              maxLength: 200,
              default: "The perfect plan if you're just getting started with our product."
            },
            features: {
              type: "array",
              title: "Features",
              items: {
                type: "string",
                title: "Feature",
                minLength: 2,
                maxLength: 100
              },
              default: [
                "25 products",
                "Up to 10,000 subscribers",
                "Advanced analytics",
                "24-hour support response time"
              ]
            },
            button: {
              type: "object",
              title: "Button Settings",
              properties: {
                label: {
                  type: "string",
                  title: "Button Text",
                  minLength: 2,
                  maxLength: 20,
                  default: "Get started today"
                },
                href: {
                  type: "string",
                  title: "Button URL",
                  pattern: "^(?:\/[a-zA-Z0-9-_\/]*|https?:\/\/[^\\s]+|#[a-zA-Z0-9-_]+|mailto:[^\\s]+|tel:[^\\s]+)$",
                  default: "/"
                },
                urlType: {
                  type: "string",
                  title: "URL Type",
                  enum: ["internal", "external", "anchor", "email", "phone"],
                  default: "internal"
                }
              },
              required: ["label", "href", "urlType"]
            }
          },
          required: ["name", "mostPopular", "price", "description", "features", "button"]
        },
        default: defaultPricingTwoProps.tiers
      },
      discounted: {
        type: "object",
        title: "Discounted Section",
        properties: {
          enabled: {
            type: "boolean",
            title: "Enable Discounted Section",
            default: defaultPricingTwoProps.discounted?.enabled || true
          }
        },
        dependencies: {
          enabled: {
            oneOf: [
              {
                properties: {
                  enabled: { enum: [true] },
                  title: {
                    type: "string",
                    title: "Section Title",
                    minLength: 2,
                    maxLength: 30,
                    default: defaultPricingTwoProps.discounted?.title || "Discounted"
                  },
                  description: {
                    type: "string",
                    title: "Section Description",
                    minLength: 2,
                    maxLength: 200,
                    default: defaultPricingTwoProps.discounted?.description || "Get a special discount when you sign up for an annual plan."
                  },
                  button: {
                    type: "object",
                    title: "Button Settings",
                    properties: {
                      label: {
                        type: "string",
                        title: "Button Text",
                        minLength: 2,
                        maxLength: 30,
                        default: defaultPricingTwoProps.discounted?.button?.label || "Buy discounted license"
                      },
                      href: {
                        type: "string",
                        title: "Button URL",
                        pattern: "^(?:\/[a-zA-Z0-9-_\/]*|https?:\/\/[^\\s]+|#[a-zA-Z0-9-_]+|mailto:[^\\s]+|tel:[^\\s]+)$",
                        default: defaultPricingTwoProps.discounted?.button?.url || "/"
                      },
                      urlType: {
                        type: "string",
                        title: "URL Type",
                        enum: ["internal", "external", "anchor", "email", "phone"],
                        default: defaultPricingTwoProps.discounted?.button?.urlType || "internal"
                      }
                    },
                    required: ["label", "href", "urlType"]
                  }
                },
                required: ["title", "description", "button"]
              },
              {
                properties: {
                  enabled: { enum: [false] }
                }
              }
            ]
          }
        }
      }
    },
    required: ["title", "description", "tiers"],
    dependencies: {
      frequencies: {
        oneOf: [
          {
            properties: {
              frequencies: {
                properties: {
                  enabled: { enum: [true] }
                }
              },
              tiers: {
                type: "array",
                items: {
                  properties: {
                    price: {
                      type: "object",
                      properties: {
                        monthly: {
                          type: "number",
                          title: "Monthly Price",
                          minimum: 0,
                          default: 29
                        },
                        annually: {
                          type: "number",
                          title: "Annual Price",
                          minimum: 0,
                          default: 290
                        },
                        currency: {
                          type: "string",
                          title: "Currency",
                          enum: ["USD", "EUR", "GBP", "JPY", "CNY", "CAD", "AUD", "NONE"],
                          default: "USD"
                        }
                      },
                      required: ["monthly", "annually", "currency"]
                    }
                  }
                }
              }
            }
          },
          {
            properties: {
              frequencies: {
                properties: {
                  enabled: { enum: [false] }
                }
              },
              tiers: {
                type: "array",
                items: {
                  properties: {
                    price: {
                      type: "object",
                      properties: {
                        amount: {
                          type: "number",
                          title: "Price Amount",
                          minimum: 0,
                          default: 29
                        },
                        currency: {
                          type: "string",
                          title: "Currency",
                          enum: ["USD", "EUR", "GBP", "JPY", "CNY", "CAD", "AUD", "NONE"],
                          default: "USD"
                        }
                      },
                      required: ["amount", "currency"]
                    }
                  }
                }
              }
            }
          }
        ]
      }
    }
  },
  uiSchema: {
    "ui:order": ["tagline", "title", "description", "frequencies", "tiers", "discounted"],
    
    tagline: {
      "ui:widget": "text",
      "ui:options": {
        help: "Short text displayed above the main title",
        placeholder: "Enter a tagline...",
        fullWidth: true
      }
    },
    
    title: {
      "ui:widget": "text",
      "ui:options": {
        help: "Enter your main headline...",
        placeholder: "Enter your main headline...",
        fullWidth: true
      }
    },
    
    description: {
      "ui:widget": "textarea",
      "ui:options": {
        rows: 3,
        placeholder: "Describe your pricing offering...",
        fullWidth: true
      }
    },
    
    frequencies: {
      "ui:order": ["enabled", "billingPeriod", "options"],
      "ui:options": {
        expandable: true,
        expanded: false,
        title: "Billing Frequencies"
      },
      enabled: {
        "ui:widget": "switch",
        "ui:options": {
          help: "Enable this to allow users to switch between different billing frequencies"
        }
      },
      options: {
        "ui:options": {
          addable: false,
          removable: true,
          orderable: true,
          help: "Select billing frequencies (maximum two). Monthly and yearly options are set by default."
        },
        items: {
          "ui:order": ["value", "label", "priceSuffix"],
          value: {
            "ui:widget": "hidden",
            "ui:options": {
              help: "Internal value (monthly/yearly)"
            }
          },
          label: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "e.g., Monthly, Yearly",
              help: "Display label for users"
            }
          },
          priceSuffix: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "e.g., /mo, /yr",
              help: "Price suffix such as /mo or /yr"
            }
          }
        }
      },
      billingPeriod: {
        "ui:order": ["type", "customText"],
        "ui:options": {
          expandable: true,
          expanded: true,
          title: "Default Billing Period"
        },
        type: {
          "ui:widget": "buttonGroup",
          "ui:options": {
            help: "Select the billing period type for your pricing"
          }
        },
        customText: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "e.g., /week, /day",
            help: "Custom text to display after the price"
          }
        }
      }
    },
    
    tiers: {
      "ui:options": {
        addable: false,
        removable: false,
        orderable: false,
        help: "The PricingTwo component has two fixed pricing tiers. Configure each tier's content below."
      },
      items: {
        "ui:order": ["name", "mostPopular", "price", "description", "features", "button"],
        "ui:options": {
          expandable: true,
          expanded: false
        },
        
        name: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "e.g., Basic, Professional, Enterprise",
            help: "Name of the pricing tier"
          }
        },
        mostPopular: {
          "ui:widget": "checkbox",
          "ui:options": {
            help: "Highlight this tier as the most popular option"
          }
        },
        price: {
          "ui:options": {
            expandable: true,
            expanded: true,
            title: "Price Information"
          },
          monthly: {
            "ui:widget": "updown",
            "ui:options": {
              help: "Enter the monthly price as a number only (e.g., 29)",
              placeholder: "e.g., 29, 99, 0"
            }
          },
          annually: {
            "ui:widget": "updown",
            "ui:options": {
              help: "Enter the annual price as a number only (e.g., 290)",
              placeholder: "e.g., 290, 990, 0"
            }
          },
          amount: {
            "ui:widget": "updown",
            "ui:options": {
              help: "Enter the price as a number only (e.g., 29)",
              placeholder: "e.g., 29, 99, 0"
            }
          },
          currency: {
            "ui:widget": "buttonGroup",
            "ui:options": {
              help: "Select the currency for this price",
              enumOptions: [
                { value: "USD", label: "$ USD" },
                { value: "EUR", label: "€ EUR" },
                { value: "GBP", label: "£ GBP" },
                { value: "JPY", label: "¥ JPY" },
                { value: "CNY", label: "¥ CNY" },
                { value: "CAD", label: "C$ CAD" },
                { value: "AUD", label: "A$ AUD" },
                { value: "NONE", label: "None" }
              ]
            }
          }
        },
        description: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 2,
            placeholder: "Describe this pricing tier...",
          }
        },
        features: {
          "ui:options": {
            addable: true,
            removable: true,
            addButtonLabel: "Add Feature",
          },
          items: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "e.g., 10 users, Unlimited storage"
            }
          }
        },
        button: {
          "ui:order": ["label", "href", "urlType"],
          "ui:options": {
            expandable: true,
            expanded: false,
            title: "Button Settings"
          },
          label: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "e.g., Get started, Buy now"
            }
          },
          href: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "e.g., /checkout, #pricing"
            }
          },
          urlType: {
            "ui:widget": "buttonGroup",
            "ui:options": {
              enumOptions: [
                { value: "internal", label: "Internal" },
                { value: "external", label: "External" },
                { value: "anchor", label: "Anchor" },
                { value: "email", label: "Email" },
                { value: "phone", label: "Phone" }
              ]
            }
          }
        }
      }
    },
    
    discounted: {
      "ui:order": ["enabled", "title", "description", "button"],
      "ui:options": {
        expandable: true,
        expanded: false,
        title: "Discounted Section"
      },
      enabled: {
        "ui:widget": "switch",
        "ui:options": {
          help: "Show or hide the discounted section"
        }
      },
      title: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "e.g., Discounted, Special Offer"
        }
      },
      description: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 2,
          placeholder: "Describe the discount offering..."
        }
      },
      button: {
        "ui:order": ["label", "href", "urlType"],
        "ui:options": {
          expandable: true,
          expanded: false,
          title: "Button Settings"
        },
        label: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "e.g., Get discount, Buy now"
          }
        },
        href: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "e.g., /discount, #special"
          }
        },
        urlType: {
          "ui:widget": "buttonGroup",
          "ui:options": {
            enumOptions: [
              { value: "internal", label: "Internal" },
              { value: "external", label: "External" },
              { value: "anchor", label: "Anchor" },
              { value: "email", label: "Email" },
              { value: "phone", label: "Phone" }
            ]
          }
        }
      }
    }
  }
};

export default pricingTwoConfig;