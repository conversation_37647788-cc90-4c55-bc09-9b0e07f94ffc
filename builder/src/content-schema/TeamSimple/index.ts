import { defaultProps } from '@litpage/sections';

import { BlockFormConfig } from '../types';

const defaultTeamSimpleProps = defaultProps.TeamSimple;

const teamSimpleConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      tagline: {
        type: "string",
        title: "Tagline",
        // minLength: 2,
        maxLength: 50,
        default: defaultTeamSimpleProps.tagline
      },
      title: {
        type: "string",
        title: "Main Title",
        minLength: 2,
        maxLength: 70,
        default: defaultTeamSimpleProps.title
      },
      description: {
        type: "string",
        title: "Description",
        minLength: 2,
        maxLength: 200,
        default: defaultTeamSimpleProps.description
      },
      people: {
        type: "array",
        title: "Team Members",
        description: "Add team members to display in the component",
        items: {
          type: "object",
          properties: {
            name: {
              type: "string",
              title: "Name",
              minLength: 2,
              maxLength: 50,
              default: defaultTeamSimpleProps.people[0]?.name || "<PERSON>"
            },
            role: {
              type: "string",
              title: "Role",
              minLength: 2,
              maxLength: 50,
              default: defaultTeamSimpleProps.people[0]?.role || "Co-Founder / CEO"
            },
            imageUrl: {
              type: "string",
              title: "Profile Image URL",
              pattern: "^(https?://.*|/.+)$",
              default: defaultTeamSimpleProps.people[0]?.imageUrl
            },
            socialLinks: {
              type: "array",
              title: "Socials",
              items: {
                type: "object",
                properties: {
                  platform: {
                    type: "string",
                    title: "Platform",
                    enum: ["x", "linkedin", "github", "facebook", "instagram", "youtube"],
                    default: "linkedin"
                  },
                  url: {
                    type: "string",
                    title: "URL",
                    default: "#"
                  }
                },
                required: ["platform", "url"]
              },
              default: defaultTeamSimpleProps.people[0]?.socialLinks || [
                { platform: "x", url: "#" },
                { platform: "linkedin", url: "#" },
                { platform: "github", url: "#" }
              ]
            }
          },
          required: ["name", "role", "imageUrl"]
        },
        default: defaultTeamSimpleProps.people
      },
      hiringSection: {
        type: "object",
        title: "Hiring Section",
        properties: {
          enabled: {
            type: "boolean",
            title: "Enable Hiring Section",
            default: defaultTeamSimpleProps.hiringSection.enabled
          }
        },
        dependencies: {
          enabled: {
            oneOf: [
              {
                properties: {
                  enabled: { enum: [true] },
                  title: {
                    type: "string",
                    title: "Title",
                    minLength: 2,
                    maxLength: 50,
                    default: defaultTeamSimpleProps.hiringSection.title || "We are hiring!"
                  },
                  subtitle: {
                    type: "string",
                    title: "Subtitle",
                    minLength: 2,
                    maxLength: 50,
                    default: defaultTeamSimpleProps.hiringSection.subtitle || "Join our team"
                  },
                  button: {
                    type: "object",
                    title: "Button Settings",
                    properties: {
                      label: {
                        type: "string",
                        title: "Button Text",
                        minLength: 2,
                        maxLength: 50,
                        default: defaultTeamSimpleProps.hiringSection.button?.label || "See all opening positions"
                      },
                      href: {
                        type: "string",
                        title: "Button URL",
                        default: defaultTeamSimpleProps.hiringSection.button?.href || "#"
                      },
                      urlType: {
                        type: "string",
                        title: "URL Type",
                        enum: ["internal", "external", "anchor", "email", "phone"],
                        default: defaultTeamSimpleProps.hiringSection.button?.urlType || "internal"
                      }
                    },
                    required: ["label", "href", "urlType"]
                  },
                  imageUrl: {
                    type: "string",
                    title: "Image URL",
                    pattern: "^(https?://.*|/.+)$",
                    default: defaultTeamSimpleProps.hiringSection.imageUrl
                  }
                },
                required: ["title", "subtitle", "button", "imageUrl"]
              },
              {
                properties: {
                  enabled: { enum: [false] }
                }
              }
            ]
          }
        }
      }
    },
    required: ["title", "description", "people"]
  },
  uiSchema: {
    "ui:order": ["tagline", "title", "description", "people", "hiringSection"],
    
    tagline: {
      "ui:widget": "text",
      "ui:placeholder": "Enter a tagline (optional)",
      "ui:help": "A short tagline that appears above the main title"
    },
    title: {
      "ui:widget": "text",
      "ui:placeholder": "Enter a title",
      "ui:help": "The main title of the team section"
    },
    description: {
      "ui:widget": "textarea",
      "ui:placeholder": "Enter a description",
      "ui:help": "A brief description of your team"
    },
    people: {
      "ui:options": {
        orderable: true
      },
      items: {
        "ui:order": ["name", "role", "imageUrl", "socialLinks"],
        name: {
          "ui:widget": "text",
          "ui:placeholder": "Enter team member name"
        },
        role: {
          "ui:widget": "text",
          "ui:placeholder": "Enter team member role"
        },
        imageUrl: {
          "ui:widget": "imageSelector",
          "ui:placeholder": "Select team member image",
          "ui:help": "Select an image for the team member's profile"
        },
        socialLinks: {
          "ui:options": {
            orderable: true
          },
          items: {
            "ui:order": ["platform", "url"],
            platform: {
              "ui:widget": "select",
              "ui:placeholder": "Select platform"
            },
            url: {
              "ui:widget": "text",
              "ui:placeholder": "Enter URL"
            }
          }
        }
      }
    },
    hiringSection: {
      "ui:order": ["enabled", "title", "subtitle", "button", "imageUrl"],
      enabled: {
        "ui:widget": "switch"
      },
      title: {
        "ui:widget": "text",
        "ui:placeholder": "Enter hiring section title",
        "ui:options": {
          conditional: {
            field: "hiringSection.enabled",
            value: true
          }
        }
      },
      subtitle: {
        "ui:widget": "text",
        "ui:placeholder": "Enter hiring section subtitle",
        "ui:options": {
          conditional: {
            field: "hiringSection.enabled",
            value: true
          }
        }
      },
      button: {
        "ui:order": ["label", "href", "urlType"],
        "ui:options": {
          conditional: {
            field: "hiringSection.enabled",
            value: true
          }
        },
        label: {
          "ui:widget": "text",
          "ui:placeholder": "Enter button text"
        },
        href: {
          "ui:widget": "text",
          "ui:placeholder": "Enter button URL"
        },
        urlType: {
          "ui:widget": "select",
          "ui:placeholder": "Select URL type"
        }
      },
      imageUrl: {
        "ui:widget": "imageSelector",
        "ui:placeholder": "Select hiring section image",
        "ui:help": "Select an image for the hiring section",
        "ui:options": {
          conditional: {
            field: "hiringSection.enabled",
            value: true
          }
        }
      }
    }
  }
};

export default teamSimpleConfig;