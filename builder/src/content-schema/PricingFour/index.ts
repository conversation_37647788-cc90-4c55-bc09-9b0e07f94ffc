import { defaultProps } from '@litpage/sections';
import { BlockFormConfig } from '../types';

// 币种映射表，用于将币种代码映射到符号和代码
// const currencyMap = {
//   USD: { symbol: "$", code: "USD" },
//   EUR: { symbol: "€", code: "EUR" },
//   GBP: { symbol: "£", code: "GBP" },
//   JPY: { symbol: "¥", code: "JPY" },
//   CNY: { symbol: "¥", code: "CNY" },
//   CAD: { symbol: "C$", code: "CAD" },
//   AUD: { symbol: "A$", code: "AUD" },
//   NONE: { symbol: "", code: "" }
// };

const defaultPricingFourProps = defaultProps.PricingFour;

const pricingFourConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      tagline: {
        type: "string",
        title: "Tagline",
        // minLength: 2,
        maxLength: 50,
        default: defaultPricingFourProps.tagline
      },
      title: {
        type: "string",
        title: "Main Title",
        minLength: 2,
        maxLength: 70,
        default: defaultPricingFourProps.title
      },
      description: {
        type: "string",
        title: "Description",
        minLength: 2,
        maxLength: 160,
        default: defaultPricingFourProps.description
      },
      frequencies: {
        type: "object",
        title: "Billing Frequencies",
        properties: {
          enabled: {
            type: "boolean",
            title: "Enable Multiple Frequencies",
            default: defaultPricingFourProps.frequencies.enabled
          }
        },
        dependencies: {
          enabled: {
            oneOf: [
              {
                properties: {
                  enabled: { enum: [true] },
                  options: {
                    type: "array",
                    title: "Frequency Options",
                    items: {
                      type: "object",
                      properties: {
                        value: {
                          type: "string",
                          title: "Value",
                          enum: ["monthly", "annually"],
                          default: "monthly",
                          readOnly: true
                        },
                        label: {
                          type: "string",
                          title: "Label",
                          minLength: 2,
                          maxLength: 20,
                          default: "Monthly"
                        },
                        priceSuffix: {
                          type: "string",
                          title: "Price Suffix",
                          minLength: 1,
                          maxLength: 10,
                          default: "/month"
                        }
                      },
                      required: ["value", "label", "priceSuffix"]
                    },
                    default: defaultPricingFourProps.frequencies.options,
                    maxItems: 2
                  }
                },
                required: ["options"]
              },
              {
                properties: {
                  enabled: { enum: [false] },
                  billingPeriod: {
                    type: "object",
                    title: "Default Billing Period",
                    properties: {
                      type: {
                        type: "string",
                        title: "Period Type",
                        enum: ["one-time", "monthly", "annually", "per-user", "custom"],
                        default: defaultPricingFourProps.frequencies.billingPeriod?.type || "monthly"
                      }
                    },
                    dependencies: {
                      type: {
                        oneOf: [
                          {
                            properties: {
                              type: { enum: ["custom"] },
                              customText: {
                                type: "string",
                                title: "Custom Period Text",
                                minLength: 1,
                                maxLength: 10,
                                default: "/week"
                              }
                            },
                            required: ["customText"]
                          },
                          {
                            properties: {
                              type: { enum: ["one-time", "monthly", "annually", "per-user"] }
                            }
                          }
                        ]
                      }
                    }
                  }
                },
                required: ["billingPeriod"]
              }
            ]
          }
        }
      },
      tiers: {
        type: "array",
        title: "Pricing Tiers",
        description: "Four pricing tiers for the PricingFour component",
        minItems: 4,
        maxItems: 4,
        items: {
          type: "object",
          properties: {
            name: {
              type: "string",
              title: "Tier Name",
              minLength: 2,
              maxLength: 30,
              default: "Hobby"
            },
            mostPopular: {
              type: "boolean",
              title: "Most Popular",
              default: false
            },
            price: {
              type: "object",
              title: "Price Information"
            },
            description: {
              type: "string",
              title: "Tier Description",
              minLength: 2,
              maxLength: 200,
              default: "The perfect plan if you're just getting started with our product."
            },
            features: {
              type: "array",
              title: "Features",
              items: {
                type: "string",
                title: "Feature",
                minLength: 2,
                maxLength: 100
              },
              default: [
                "25 products",
                "Up to 10,000 subscribers",
                "Advanced analytics",
                "24-hour support response time"
              ]
            },
            button: {
              type: "object",
              title: "Button Settings",
              properties: {
                label: {
                  type: "string",
                  title: "Button Text",
                  minLength: 2,
                  maxLength: 20,
                  default: "Get started today"
                },
                href: {
                  type: "string",
                  title: "Button URL",
                  pattern: "^(?:\/[a-zA-Z0-9-_\/]*|https?:\/\/[^\\s]+|#[a-zA-Z0-9-_]+|mailto:[^\\s]+|tel:[^\\s]+)$",
                  default: "#"
                },
                urlType: {
                  type: "string",
                  title: "URL Type",
                  enum: ["internal", "external", "anchor", "email", "phone"],
                  default: "internal"
                }
              },
              required: ["label", "href", "urlType"]
            }
          },
          required: ["name", "mostPopular", "price", "description", "features", "button"]
        },
        default: defaultPricingFourProps.tiers
      },
      customSolution: {
        type: "object",
        title: "Custom Solution Section",
        properties: {
          enabled: {
            type: "boolean",
            title: "Enable Custom Solution Section",
            default: defaultPricingFourProps.customSolution?.enabled || true
          }
        },
        dependencies: {
          enabled: {
            oneOf: [
              {
                properties: {
                  enabled: { enum: [true] },
                  title: {
                    type: "string",
                    title: "Section Title",
                    minLength: 2,
                    maxLength: 50,
                    default: defaultPricingFourProps.customSolution?.title || "Need a custom solution?"
                  },
                  description: {
                    type: "string",
                    title: "Section Description",
                    minLength: 2,
                    maxLength: 200,
                    default: defaultPricingFourProps.customSolution?.description || "Get in touch with our sales team and we'll help you find the perfect solution for your business needs."
                  },
                  button: {
                    type: "object",
                    title: "Button Settings",
                    properties: {
                      label: {
                        type: "string",
                        title: "Button Text",
                        minLength: 2,
                        maxLength: 30,
                        default: defaultPricingFourProps.customSolution?.button?.label || "Contact sales"
                      },
                      href: {
                        type: "string",
                        title: "Button URL",
                        pattern: "^(?:\/[a-zA-Z0-9-_\/]*|https?:\/\/[^\\s]+|#[a-zA-Z0-9-_]+|mailto:[^\\s]+|tel:[^\\s]+)$",
                        default: defaultPricingFourProps.customSolution?.button?.href || "#"
                      },
                      urlType: {
                        type: "string",
                        title: "URL Type",
                        enum: ["internal", "external", "anchor", "email", "phone"],
                        default: defaultPricingFourProps.customSolution?.button?.urlType || "internal"
                      }
                    },
                    required: ["label", "href", "urlType"]
                  }
                },
                required: ["title", "description", "button"]
              },
              {
                properties: {
                  enabled: { enum: [false] }
                }
              }
            ]
          }
        }
      }
    },
    required: ["title", "description", "tiers"],
    dependencies: {
      frequencies: {
        oneOf: [
          {
            properties: {
              frequencies: {
                properties: {
                  enabled: { enum: [true] }
                }
              },
              tiers: {
                items: {
                  properties: {
                    price: {
                      type: "object",
                      properties: {
                        monthly: {
                          type: "number",
                          title: "Monthly Price",
                          minimum: 0,
                          default: 29
                        },
                        annually: {
                          type: "number",
                          title: "Annual Price",
                          minimum: 0,
                          default: 290
                        },
                        currency: {
                          type: "string",
                          title: "Currency",
                          enum: ["USD", "EUR", "GBP", "JPY", "CNY", "CAD", "AUD", "NONE"],
                          default: "USD"
                        },
                        monthlyDescription: {
                          type: "string",
                          title: "Monthly Price Description",
                          description: "Description shown below the price when monthly frequency is selected. Use {annually} as placeholder for the annual price converted to monthly.",
                          minLength: 0,
                          maxLength: 100,
                          default: "Billed monthly. {annually} per month if paid annually"
                        },
                        annuallyDescription: {
                          type: "string",
                          title: "Annual Price Description",
                          description: "Description shown below the price when annual frequency is selected. Use {monthly} as placeholder for the monthly price.",
                          minLength: 0,
                          maxLength: 100,
                          default: "Billed annually. {monthly} per month if paid monthly"
                        }
                      },
                      required: ["monthly", "annually", "currency"]
                    }
                  }
                }
              }
            }
          },
          {
            properties: {
              frequencies: {
                properties: {
                  enabled: { enum: [false] }
                }
              },
              tiers: {
                items: {
                  properties: {
                    price: {
                      type: "object",
                      properties: {
                        amount: {
                          type: "number",
                          title: "Price Amount",
                          minimum: 0,
                          default: 29
                        },
                        currency: {
                          type: "string",
                          title: "Currency",
                          enum: ["USD", "EUR", "GBP", "JPY", "CNY", "CAD", "AUD", "NONE"],
                          default: "USD"
                        },
                        description: {
                          type: "string",
                          title: "Price Description",
                          description: "Description shown below the price.",
                          minLength: 0,
                          maxLength: 100,
                          default: "One-time payment"
                        }
                      },
                      required: ["amount", "currency"]
                    }
                  }
                }
              }
            }
          }
        ]
      }
    }
  },
  uiSchema: {
    "ui:order": ["tagline", "title", "description", "frequencies", "tiers", "customSolution"],
    
    tagline: {
      "ui:widget": "text",
      "ui:placeholder": "Enter a tagline",
      "ui:help": "A short tagline displayed above the main title"
    },
    
    title: {
      "ui:widget": "text",
      "ui:placeholder": "Enter a title",
      "ui:help": "The main title for the pricing section"
    },
    
    description: {
      "ui:widget": "textarea",
      "ui:placeholder": "Enter a description",
      "ui:help": "A brief description of your pricing options"
    },
    
    frequencies: {
      "ui:order": ["enabled", "options", "billingPeriod"],
      "ui:options": {
        expandable: true,
        expanded: true
      },
      
      enabled: {
        "ui:widget": "switch",
        "ui:help": "Enable to show a frequency toggle (monthly/annually)"
      },
      
      options: {
        "ui:options": {
          orderable: false
        },
        
        items: {
          "ui:order": ["value", "label", "priceSuffix"],
          
          value: {
            "ui:widget": "hidden"
          }
        }
      },
      
      billingPeriod: {
        "ui:order": ["type", "customText"],
        
        type: {
          "ui:widget": "select",
          "ui:placeholder": "Select a period type",
          "ui:help": "The type of billing period to display when frequency toggle is disabled"
        },
        
        customText: {
          "ui:widget": "text",
          "ui:placeholder": "Enter custom text",
          "ui:help": "Custom text to display after the price (e.g. /week)"
        }
      }
    },
    
    tiers: {
      "ui:options": {
        addable: false,
        removable: false,
        orderable: false,
        expandable: true,
        expanded: false
      },
      
      items: {
        "ui:order": ["name", "mostPopular", "price", "description", "features", "button"],
        "ui:options": {
          expandable: true,
          expanded: false
        },
        
        name: {
          "ui:widget": "text",
          "ui:placeholder": "Enter tier name",
          "ui:help": "The name of this pricing tier"
        },
        
        mostPopular: {
          "ui:widget": "checkbox",
          "ui:help": "Mark this tier as the most popular option"
        },
        
        price: {
          "ui:order": ["amount", "monthly", "annually", "currency", "description", "monthlyDescription", "annuallyDescription"],
          "ui:options": {
            expandable: true,
            expanded: true
          },
          
          amount: {
            "ui:widget": "updown",
            "ui:placeholder": "Enter price amount",
            "ui:help": "The price amount for this tier"
          },
          
          monthly: {
            "ui:widget": "updown",
            "ui:placeholder": "Enter monthly price",
            "ui:help": "The monthly price for this tier"
          },
          
          annually: {
            "ui:widget": "updown",
            "ui:placeholder": "Enter annual price",
            "ui:help": "The annual price for this tier"
          },
          
          currency: {
            "ui:widget": "select",
            "ui:placeholder": "Select currency",
            "ui:help": "The currency for this price"
          },
          
          description: {
            "ui:widget": "text",
            "ui:placeholder": "Enter price description",
            "ui:help": "Description shown below the price"
          },
          
          monthlyDescription: {
            "ui:widget": "text",
            "ui:placeholder": "Enter monthly price description",
            "ui:help": "Description shown below the price when monthly frequency is selected"
          },
          
          annuallyDescription: {
            "ui:widget": "text",
            "ui:placeholder": "Enter annual price description",
            "ui:help": "Description shown below the price when annual frequency is selected"
          }
        },
        
        description: {
          "ui:widget": "textarea",
          "ui:placeholder": "Enter tier description",
          "ui:help": "A brief description of this pricing tier"
        },
        
        features: {
          "ui:options": {
            orderable: true
          },
          
          items: {
            "ui:widget": "text",
            "ui:placeholder": "Enter feature",
            "ui:help": "A feature included in this pricing tier"
          }
        },
        
        button: {
          "ui:order": ["label", "href", "urlType"],
          "ui:options": {
            expandable: true,
            expanded: false
          },
          
          label: {
            "ui:widget": "text",
            "ui:placeholder": "Enter button text",
            "ui:help": "The text displayed on the button"
          },
          
          href: {
            "ui:widget": "text",
            "ui:placeholder": "Enter button URL",
            "ui:help": "The URL the button links to"
          },
          
          urlType: {
            "ui:widget": "select",
            "ui:placeholder": "Select URL type",
            "ui:help": "The type of URL (internal, external, anchor, etc.)"
          }
        }
      }
    },
    
    customSolution: {
      "ui:order": ["enabled", "title", "description", "button"],
      "ui:options": {
        expandable: true,
        expanded: false
      },
      
      enabled: {
        "ui:widget": "switch",
        "ui:help": "Enable to show a custom solution section below the pricing tiers"
      },
      
      title: {
        "ui:widget": "text",
        "ui:placeholder": "Enter section title",
        "ui:help": "The title for the custom solution section"
      },
      
      description: {
        "ui:widget": "textarea",
        "ui:placeholder": "Enter section description",
        "ui:help": "A brief description for the custom solution section"
      },
      
      button: {
        "ui:order": ["label", "href", "urlType"],
        "ui:options": {
          expandable: true,
          expanded: false
        },
        
        label: {
          "ui:widget": "text",
          "ui:placeholder": "Enter button text",
          "ui:help": "The text displayed on the button"
        },
        
        href: {
          "ui:widget": "text",
          "ui:placeholder": "Enter button URL",
          "ui:help": "The URL the button links to"
        },
        
        urlType: {
          "ui:widget": "select",
          "ui:placeholder": "Select URL type",
          "ui:help": "The type of URL (internal, external, anchor, etc.)"
        }
      }
    }
  }
};

export default pricingFourConfig;