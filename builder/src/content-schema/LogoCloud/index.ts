import { defaultProps } from '@litpage/sections';
import { BlockFormConfig } from '../types';

const defaultLogoCloudProps = defaultProps.LogoCloud;

const logoCloudConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      tagline: {
        type: "string",
        title: "Tagline",
        // minLength: 0,
        maxLength: 50,
        default: defaultLogoCloudProps.tagline
      },
      title: {
        type: "string",
        title: "Title",
        minLength: 0,
        maxLength: 70,
        default: defaultLogoCloudProps.title
      },
      logos: {
        type: "array",
        title: "Logos",
        minItems: 6,
        maxItems: 6,
        items: {
          type: "object",
          properties: {
            name: {
              type: "string",
              title: "Company Name"
            },
            imageUrl: {
              type: "string",
              title: "Logo URL",
              pattern: "^(https?://.*|/.+)$",
            },
            altText: {
              type: "string",
              title: "Alt Text"
            }
          },
          required: ["name", "imageUrl", "altText"]
        },
        default: defaultLogoCloudProps.logos
      }
    },
    required: ["logos"]
  },
  uiSchema: {
    "ui:order": ["tagline", "title", "logos"],
    tagline: {
      'ui:widget': 'text',
      'ui:options': {
        label: 'Tagline',
      },
    },
    title: {
      'ui:widget': 'textarea',
      'ui:options': {
        label: 'Title',
        rows: 2,
      },
    },
    logos: {
      "ui:options": {
        label: 'Logos',
        orderable: true,
        addable: false,
        removable: false,
      },
      items: {
        "ui:order": ["name", "imageUrl", "altText"],
        name: {
          'ui:widget': 'text',
          'ui:options': {
            label: 'Company Name',
          },
        },
        imageUrl: {
          "ui:widget": "imageSelector",
          'ui:options': {
            label: 'Logo URL',
            placeholder: 'Enter logo image URL',
            help: 'Upload or enter the URL for the company logo'
          },
        },
        altText: {
          'ui:widget': 'text',
          'ui:options': {
            label: 'Alt Text',
          },
        },
      },
    }
  }
};

export default logoCloudConfig;