/**
 * Structured query keys for React Query
 *
 * This file defines all query keys used in the application in a structured way,
 * making it easier to manage cache invalidation and dependencies.
 */

/**
 * Website related query keys
 */
export const websiteKeys = {
  all: () => ['websites'] as const,
  lists: () => [...websiteKeys.all(), 'list'] as const,
  list: (filters?: Record<string, unknown>) => [...websiteKeys.lists(), { ...filters }] as const,
  details: () => [...websiteKeys.all(), 'detail'] as const,
  detail: (id: string) => [...websiteKeys.details(), id] as const,
  theme: (id: string) => [...websiteKeys.detail(id), 'theme'] as const,
  customDomain: (id: string) => ['customDomain', id] as const,
  fetchWebsites: () => ['fetchWebsites'] as const,
  fetchWebsite: (id?: string) => id ? ['fetchWebsite', id] as const : ['fetchWebsite'] as const,
  // Add pages structure query key for dashboard
  pages: (websiteId?: string, layoutType?: string) =>
    layoutType
      ? ['websitePages', websiteId, { layout: layoutType }] as const
      : ['websitePages', websiteId] as const,
}

/**
 * Workspace related query keys
 */
export const workspaceKeys = {
  all: () => ['workspaces'] as const,
  lists: () => [...workspaceKeys.all(), 'list'] as const,
  list: (filters?: Record<string, unknown>) => [...workspaceKeys.lists(), { ...filters }] as const,
  details: () => [...workspaceKeys.all(), 'detail'] as const,
  detail: (id: string) => [...workspaceKeys.details(), id] as const,
  fetchWorkspaces: () => ['fetchWorkspaces'] as const,
}

/**
 * Page related query keys
 */
export const pageKeys = {
  all: () => ['pages'] as const,
  lists: () => [...pageKeys.all(), 'list'] as const,
  list: (websiteId?: string, filters?: Record<string, unknown>) =>
    [...pageKeys.lists(), websiteId, { ...filters }] as const,
  details: () => [...pageKeys.all(), 'detail'] as const,
  detail: (id: string) => [...pageKeys.details(), id] as const,
  fetchPage: (id?: string) => id ? ['fetchPage', id] as const : ['fetchPage'] as const,
  fetchPages: (websiteId?: string) => ['fetchPages', websiteId] as const,
  // 添加页面操作相关的查询键
  publish: (id: string) => [...pageKeys.detail(id), 'publish'] as const,
  archive: (id: string) => [...pageKeys.detail(id), 'archive'] as const,
  restore: (id: string) => [...pageKeys.detail(id), 'restore'] as const,
  discard: (id: string) => [...pageKeys.detail(id), 'discard'] as const,
  saveDraft: (id: string) => [...pageKeys.detail(id), 'saveDraft'] as const,
  update: (id: string) => [...pageKeys.detail(id), 'update'] as const,
  delete: (id: string) => [...pageKeys.detail(id), 'delete'] as const,
  create: () => [...pageKeys.all(), 'create'] as const,
  // 添加版本相关的查询键
  versions: (pageId: string) => ['pageVersions', pageId] as const,
  version: (pageId: string, versionId: string) => ['pageVersion', pageId, versionId] as const,
  rollback: (pageId: string, versionId: string) => ['pageVersionRollback', pageId, versionId] as const,
}

/**
 * SEO related query keys
 */
export const seoKeys = {
  all: () => ['seo'] as const,
  // Website SEO
  websiteSeo: (websiteId: string) => [...seoKeys.all(), 'website', websiteId] as const,
  websiteMetadata: (websiteId: string) => [...seoKeys.websiteSeo(websiteId), 'metadata'] as const,
  websiteRobotsSitemap: (websiteId: string) => [...seoKeys.websiteSeo(websiteId), 'robots-sitemap'] as const,
  websiteSocialDefaults: (websiteId: string) => [...seoKeys.websiteSeo(websiteId), 'social-defaults'] as const,
  websiteSecurityPerformance: (websiteId: string) => [...seoKeys.websiteSeo(websiteId), 'security-performance'] as const,
  websiteStructuredData: (websiteId: string) => [...seoKeys.websiteSeo(websiteId), 'structured-data'] as const,
  websiteHreflangSettings: (websiteId: string) => [...seoKeys.websiteSeo(websiteId), 'hreflang'] as const,
  websiteAnalyticsSettings: (websiteId: string) => [...seoKeys.websiteSeo(websiteId), 'analytics'] as const,
  websiteBrandSettings: (websiteId: string) => [...seoKeys.websiteSeo(websiteId), 'brand'] as const,
  // Page SEO
  pageSeo: (pageId: string) => [...seoKeys.all(), 'page', pageId] as const,
  pageBasicSeo: (pageId: string) => [...seoKeys.pageSeo(pageId), 'basic'] as const,
  pageSocialMedia: (pageId: string) => [...seoKeys.pageSeo(pageId), 'social-media'] as const,
  pageStructuredData: (pageId: string) => [...seoKeys.pageSeo(pageId), 'structured-data'] as const,
  pageAdvancedSeo: (pageId: string) => [...seoKeys.pageSeo(pageId), 'advanced'] as const,
  pageInheritSeo: (pageId: string) => [...seoKeys.pageSeo(pageId), 'inherit'] as const,
  // Public SEO data
  sitemap: (websiteId: string) => [...seoKeys.all(), 'sitemap', websiteId] as const,
  robots: (websiteId: string) => [...seoKeys.all(), 'robots', websiteId] as const,
}

/**
 * Auth related query keys
 */
export const authKeys = {
  all: () => ['auth'] as const,
  oauthLogin: (code: string) => ['oauthLogin', code] as const,
  currentUser: () => [...authKeys.all(), 'currentUser'] as const,
  session: () => [...authKeys.all(), 'session'] as const,
  refreshToken: () => [...authKeys.all(), 'refreshToken'] as const,
  logout: () => [...authKeys.all(), 'logout'] as const,
}

/**
 * Sidebar related query keys
 */
export const sidebarKeys = {
  all: () => ['sidebar'] as const,
  data: (siteId: string) => ['sidebarData', siteId] as const,
}

/**
 * Brand related query keys
 */
export const brandKeys = {
  all: () => ['brand'] as const,
  website: (siteId: string) => ['website', siteId] as const,
}

/**
 * Configuration related query keys
 */
export const configKeys = {
  all: () => ['config'] as const,
  page: (pageId?: string) => pageId ? ['fetchPage', pageId] as const : ['fetchPage'] as const,
  website: () => ['fetchWebsite'] as const,
}

/**
 * Template related query keys
 */
export const templateKeys = {
  all: () => ['templates'] as const,
  lists: () => [...templateKeys.all(), 'list'] as const,
  list: (filters?: Record<string, unknown>) => [...templateKeys.lists(), { ...filters }] as const,
  details: () => [...templateKeys.all(), 'detail'] as const,
  detail: (id: string) => [...templateKeys.details(), id] as const,
  fetchTemplates: () => ['fetchTemplates'] as const,
}
