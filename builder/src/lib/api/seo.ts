import { $get, $put, $patch } from '../http';
import { logger } from '@/utils/logger';

const API_BASE = '/api/v1/seo';

// 配置日志命名空间
logger.configure({ namespace: 'seoService' });

// 类型定义
export interface WebsiteSEO {
  id: string;
  websiteId: string;
  siteName?: string;
  siteDescription?: string;
  titleTemplate?: string;
  defaultDescription?: string;
  globalKeywords?: string[];
  robotsTxt?: string;
  sitemapSettings?: any;
  organizationSchema?: any;
  websiteSchema?: any;
  defaultOgImage?: string;
  ogTitleTemplate?: string;
  ogDescriptionTemplate?: string;
  defaultXCard?: 'SUMMARY' | 'SUMMARY_LARGE_IMAGE' | 'APP' | 'PLAYER';
  xUsername?: string;
  hreflangSettings?: any;
  forceHttps?: boolean;
  httpToHttpsRedirect?: boolean;
  cacheControl?: string;
  resourceCompression?: boolean;
  browserCaching?: boolean;
  lazyLoadingImages?: boolean;
  analyticsSettings?: any;
  brandSettings?: any;
  seoScores?: any;
  createdAt: string;
  updatedAt: string;
}

export interface PageSEO {
  id: string;
  pageId: string;
  title?: string;
  useCustomTitle?: boolean;
  description?: string;
  keywords?: string[];
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  xTitle?: string;
  xDescription?: string;
  xImage?: string;
  xCardType?: 'SUMMARY' | 'SUMMARY_LARGE_IMAGE' | 'APP' | 'PLAYER';
  xCardData?: any; // 存储 APP 和 PLAYER 卡片特定数据
  schemaType?: 'ARTICLE' | 'PRODUCT' | 'FAQ' | 'HOWTO' | 'EVENT' | 'ORGANIZATION' | 'PERSON' | 'WEBSITE' | 'LOCALBUSINESS' | 'RECIPE' | 'COURSE' | 'REVIEW' | 'BREADCRUMB' | 'CUSTOM';
  schemaData?: any;
  canonicalUrl?: string;
  robots?: any;
  hreflangLinks?: any;
  priority?: number;
  changeFrequency?: 'ALWAYS' | 'HOURLY' | 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'YEARLY' | 'NEVER';
  contentAnalysis?: any;
  inheritFromSite?: boolean;
  websiteSeoId?: string;
  websiteSeo?: WebsiteSEO | null;
  createdAt: string;
  updatedAt: string;
}

// DTO 类型定义
export interface WebsiteMetadataDTO {
  siteName?: string;
  siteDescription?: string;
  titleTemplate?: string;
  defaultDescription?: string;
  globalKeywords?: string[];
}

export interface RobotsSitemapDTO {
  robotsTxt?: string;
  sitemapSettings?: any;
}

export interface SocialDefaultsDTO {
  defaultOgImage?: string;
  ogTitleTemplate?: string;
  ogDescriptionTemplate?: string;
  defaultXCard?: 'SUMMARY' | 'SUMMARY_LARGE_IMAGE' | 'APP' | 'PLAYER';
  xUsername?: string;
}

export interface SecurityPerformanceDTO {
  forceHttps?: boolean;
  httpToHttpsRedirect?: boolean;
  cacheControl?: string;
  resourceCompression?: boolean;
  browserCaching?: boolean;
  lazyLoadingImages?: boolean;
}

export interface StructuredDataDTO {
  organizationSchema?: any;
  websiteSchema?: any;
}

export interface WebsiteSeoDTO {
  metadata?: WebsiteMetadataDTO;
  robotsSitemap?: RobotsSitemapDTO;
  socialDefaults?: SocialDefaultsDTO;
  securityPerformance?: SecurityPerformanceDTO;
  structuredData?: StructuredDataDTO;
}

export interface BasicSeoDTO {
  title?: string;
  useCustomTitle?: boolean;
  description?: string;
  keywords?: string[];
}

export interface SocialMediaDTO {
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  xTitle?: string;
  xDescription?: string;
  xImage?: string;
  xCardType?: 'SUMMARY' | 'SUMMARY_LARGE_IMAGE' | 'APP' | 'PLAYER';
  xCardData?: any; // 存储 APP 和 PLAYER 卡片特定数据
}

export interface PageStructuredDataDTO {
  schemaType?: 'ARTICLE' | 'PRODUCT' | 'FAQ' | 'HOWTO' | 'EVENT' | 'ORGANIZATION' | 'PERSON' | 'WEBSITE' | 'LOCALBUSINESS' | 'RECIPE' | 'COURSE' | 'REVIEW' | 'BREADCRUMB' | 'CUSTOM';
  schemaData?: any;
}

export interface AdvancedSeoDTO {
  canonicalUrl?: string;
  robots?: any;
  hreflangLinks?: any;
  priority?: number;
  changeFrequency?: 'ALWAYS' | 'HOURLY' | 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'YEARLY' | 'NEVER';
}

export interface InheritSeoDTO {
  inheritFromSite?: boolean;
  websiteSeoId?: string;
}

export interface PageSeoDTO {
  basic?: BasicSeoDTO;
  socialMedia?: SocialMediaDTO;
  structuredData?: PageStructuredDataDTO;
  advanced?: AdvancedSeoDTO;
  inherit?: InheritSeoDTO;
}

export interface HreflangSettingsDTO {
  defaultLanguage?: string;
  supportedLanguages?: Array<{
    code: string;
    region: string;
    url: string;
  }>;
}

// Website SEO 服务
/**
 * Get website SEO settings
 * @param websiteId Website ID
 * @returns Website SEO settings
 */
export async function getWebsiteSeo(websiteId: string): Promise<WebsiteSEO | null> {
  logger.info('Getting website SEO', { websiteId });
  
  try {
    const response = await $get<WebsiteSEO | null>(`${API_BASE}/website/${websiteId}`);
    
    if (!response.success) {
      logger.error('Failed to get website SEO', { websiteId, error: response.message });
      throw new Error(response.message || 'Failed to get website SEO');
    }
    
    return response.data;
  } catch (error) {
    logger.error('Error getting website SEO', { websiteId, error });
    throw error;
  }
}

/**
 * Update website SEO settings (full update)
 * @param websiteId Website ID
 * @param data Website SEO data
 * @returns Updated website SEO settings
 */
export async function updateWebsiteSeo(websiteId: string, data: WebsiteSeoDTO): Promise<WebsiteSEO> {
  logger.info('Updating website SEO', { websiteId });
  
  try {
    const response = await $put(`${API_BASE}/website/${websiteId}`, data);
    
    if (!response.success) {
      logger.error('Failed to update website SEO', { websiteId, error: response.message });
      throw new Error(response.message || 'Failed to update website SEO');
    }
    
    return response.data;
  } catch (error) {
    logger.error('Error updating website SEO', { websiteId, error });
    throw error;
  }
}

/**
 * Update website metadata (partial update)
 * @param websiteId Website ID
 * @param data Metadata
 * @returns Updated website SEO settings
 */
export async function updateWebsiteMetadata(websiteId: string, data: WebsiteMetadataDTO): Promise<WebsiteSEO> {
  logger.info('Updating website metadata', { websiteId });
  
  try {
    const response = await $patch(`${API_BASE}/website/${websiteId}/metadata`, data);
    
    if (!response.success) {
      logger.error('Failed to update website metadata', { websiteId, error: response.message });
      throw new Error(response.message || 'Failed to update website metadata');
    }
    
    return response.data;
  } catch (error) {
    logger.error('Error updating website metadata', { websiteId, error });
    throw error;
  }
}

/**
 * Update robots and sitemap settings (partial update)
 * @param websiteId Website ID
 * @param data Robots and sitemap settings
 * @returns Updated website SEO settings
 */
export async function updateRobotsSitemap(websiteId: string, data: RobotsSitemapDTO): Promise<WebsiteSEO> {
  logger.info('Updating robots and sitemap', { websiteId });
  
  try {
    const response = await $patch(`${API_BASE}/website/${websiteId}/robots-sitemap`, data);
    
    if (!response.success) {
      logger.error('Failed to update robots and sitemap', { websiteId, error: response.message });
      throw new Error(response.message || 'Failed to update robots and sitemap');
    }
    
    return response.data;
  } catch (error) {
    logger.error('Error updating robots and sitemap', { websiteId, error });
    throw error;
  }
}

/**
 * Update social media defaults (partial update)
 * @param websiteId Website ID
 * @param data Social media defaults
 * @returns Updated website SEO settings
 */
export async function updateSocialDefaults(websiteId: string, data: SocialDefaultsDTO): Promise<WebsiteSEO> {
  logger.info('Updating social media defaults', { websiteId });
  
  try {
    const response = await $patch(`${API_BASE}/website/${websiteId}/social-media`, data);
    
    if (!response.success) {
      logger.error('Failed to update social media defaults', { websiteId, error: response.message });
      throw new Error(response.message || 'Failed to update social media defaults');
    }
    
    return response.data;
  } catch (error) {
    logger.error('Error updating social media defaults', { websiteId, error });
    throw error;
  }
}

/**
 * Update security and performance settings (partial update)
 * @param websiteId Website ID
 * @param data Security and performance settings
 * @returns Updated website SEO settings
 */
export async function updateSecurityPerformance(websiteId: string, data: SecurityPerformanceDTO): Promise<WebsiteSEO> {
  logger.info('Updating security and performance', { websiteId });
  
  try {
    const response = await $patch(`${API_BASE}/website/${websiteId}/security-performance`, data);
    
    if (!response.success) {
      logger.error('Failed to update security and performance', { websiteId, error: response.message });
      throw new Error(response.message || 'Failed to update security and performance');
    }
    
    return response.data;
  } catch (error) {
    logger.error('Error updating security and performance', { websiteId, error });
    throw error;
  }
}

/**
 * Update structured data (partial update)
 * @param websiteId Website ID
 * @param data Structured data
 * @returns Updated website SEO settings
 */
export async function updateStructuredData(websiteId: string, data: StructuredDataDTO): Promise<WebsiteSEO> {
  logger.info('Updating structured data', { websiteId });
  
  try {
    const response = await $patch(`${API_BASE}/website/${websiteId}/structured-data`, data);
    
    if (!response.success) {
      logger.error('Failed to update structured data', { websiteId, error: response.message });
      throw new Error(response.message || 'Failed to update structured data');
    }
    
    return response.data;
  } catch (error) {
    logger.error('Error updating structured data', { websiteId, error });
    throw error;
  }
}

/**
 * Update hreflang settings (partial update)
 * @param websiteId Website ID
 * @param data Hreflang settings
 * @returns Updated website SEO settings
 */
export async function updateHreflangSettings(websiteId: string, data: HreflangSettingsDTO): Promise<WebsiteSEO> {
  logger.info('Updating hreflang settings', { websiteId });
  
  try {
    const response = await $patch(`${API_BASE}/website/${websiteId}/hreflang`, data);
    
    if (!response.success) {
      logger.error('Failed to update hreflang settings', { websiteId, error: response.message });
      throw new Error(response.message || 'Failed to update hreflang settings');
    }
    
    return response.data;
  } catch (error) {
    logger.error('Error updating hreflang settings', { websiteId, error });
    throw error;
  }
}

/**
 * Update analytics settings (partial update)
 * @param websiteId Website ID
 * @param data Analytics settings
 * @returns Updated website SEO settings
 */
export async function updateAnalyticsSettings(websiteId: string, data: { analyticsSettings: any }): Promise<WebsiteSEO> {
  logger.info('Updating analytics settings', { websiteId });
  
  try {
    const response = await $patch(`${API_BASE}/website/${websiteId}/analytics`, data);
    
    if (!response.success) {
      logger.error('Failed to update analytics settings', { websiteId, error: response.message });
      throw new Error(response.message || 'Failed to update analytics settings');
    }
    
    return response.data;
  } catch (error) {
    logger.error('Error updating analytics settings', { websiteId, error });
    throw error;
  }
}

/**
 * Update brand settings (partial update)
 * @param websiteId Website ID
 * @param data Brand settings
 * @returns Updated website SEO settings
 */
export async function updateBrandSettings(websiteId: string, data: any): Promise<WebsiteSEO> {
  logger.info('Updating brand settings', { websiteId, data });
  
  try {
    // 确保数据格式正确
    if (!data || typeof data !== 'object') {
      logger.error('Invalid brand settings data', { data });
      throw new Error('Invalid brand settings data');
    }
    
    const requestData = { brandSettings: data };
    logger.info('Request data for brand settings update', { requestData });
    
    const response = await $patch(`${API_BASE}/website/${websiteId}/brand`, requestData);
    
    if (!response.success) {
      logger.error('Failed to update brand settings', { websiteId, error: response.message });
      throw new Error(response.message || 'Failed to update brand settings');
    }
    
    return response.data;
  } catch (error) {
    logger.error('Error updating brand settings', { websiteId, error });
    throw error;
  }
}

// Page SEO 服务
/**
 * Get page SEO settings
 * @param pageId Page ID
 * @returns Page SEO settings
 */
export async function getPageSeo(pageId: string): Promise<PageSEO | null> {
  logger.info('Getting page SEO', { pageId });
  
  try {
    const response = await $get<PageSEO | null>(`${API_BASE}/page/${pageId}`);
    
    if (!response.success) {
      logger.error('Failed to get page SEO', { pageId, error: response.message });
      throw new Error(response.message || 'Failed to get page SEO');
    }
    
    return response.data;
  } catch (error) {
    logger.error('Error getting page SEO', { pageId, error });
    throw error;
  }
}

/**
 * Update page SEO settings (full update)
 * @param pageId Page ID
 * @param data Page SEO data
 * @returns Updated page SEO settings
 */
export async function updatePageSeo(pageId: string, data: PageSeoDTO): Promise<PageSEO> {
  logger.info('Updating page SEO', { pageId });
  
  try {
    const response = await $put(`${API_BASE}/page/${pageId}`, data);
    
    if (!response.success) {
      logger.error('Failed to update page SEO', { pageId, error: response.message });
      throw new Error(response.message || 'Failed to update page SEO');
    }
    
    return response.data;
  } catch (error) {
    logger.error('Error updating page SEO', { pageId, error });
    throw error;
  }
}

/**
 * Update basic SEO settings (partial update)
 * @param pageId Page ID
 * @param data Basic SEO settings
 * @returns Updated page SEO settings
 */
export async function updateBasicSeo(pageId: string, data: BasicSeoDTO): Promise<PageSEO> {
  logger.info('Updating basic SEO', { pageId });
  
  try {
    const response = await $patch(`${API_BASE}/page/${pageId}/basic`, data);
    
    if (!response.success) {
      logger.error('Failed to update basic SEO', { pageId, error: response.message });
      throw new Error(response.message || 'Failed to update basic SEO');
    }
    
    return response.data;
  } catch (error) {
    logger.error('Error updating basic SEO', { pageId, error });
    throw error;
  }
}

/**
 * Update social media settings (partial update)
 * @param pageId Page ID
 * @param data Social media settings
 * @returns Updated page SEO settings
 */
export async function updateSocialMedia(pageId: string, data: SocialMediaDTO): Promise<PageSEO> {
  logger.info('Updating social media', { pageId });
  
  try {
    const response = await $patch(`${API_BASE}/page/${pageId}/social-media`, data);
    
    if (!response.success) {
      logger.error('Failed to update social media', { pageId, error: response.message });
      throw new Error(response.message || 'Failed to update social media');
    }
    
    return response.data;
  } catch (error) {
    logger.error('Error updating social media', { pageId, error });
    throw error;
  }
}

/**
 * Update page structured data (partial update)
 * @param pageId Page ID
 * @param data Structured data
 * @returns Updated page SEO settings
 */
export async function updatePageStructuredData(pageId: string, data: PageStructuredDataDTO): Promise<PageSEO> {
  logger.info('Updating page structured data', { pageId });
  
  try {
    const response = await $patch(`${API_BASE}/page/${pageId}/structured-data`, data);
    
    if (!response.success) {
      logger.error('Failed to update page structured data', { pageId, error: response.message });
      throw new Error(response.message || 'Failed to update page structured data');
    }
    
    return response.data;
  } catch (error) {
    logger.error('Error updating page structured data', { pageId, error });
    throw error;
  }
}

/**
 * Update advanced SEO settings (partial update)
 * @param pageId Page ID
 * @param data Advanced SEO settings
 * @returns Updated page SEO settings
 */
export async function updateAdvancedSeo(pageId: string, data: AdvancedSeoDTO): Promise<PageSEO> {
  logger.info('Updating advanced SEO', { pageId });
  
  try {
    const response = await $patch(`${API_BASE}/page/${pageId}/advanced`, data);
    
    if (!response.success) {
      logger.error('Failed to update advanced SEO', { pageId, error: response.message });
      throw new Error(response.message || 'Failed to update advanced SEO');
    }
    
    return response.data;
  } catch (error) {
    logger.error('Error updating advanced SEO', { pageId, error });
    throw error;
  }
}

/**
 * Update SEO inheritance settings (partial update)
 * @param pageId Page ID
 * @param data Inheritance settings
 * @returns Updated page SEO settings
 */
export async function updateInheritSeo(pageId: string, data: InheritSeoDTO): Promise<PageSEO> {
  logger.info('Updating SEO inheritance', { pageId });
  
  try {
    const response = await $patch(`${API_BASE}/page/${pageId}/inherit`, data);
    
    if (!response.success) {
      logger.error('Failed to update SEO inheritance', { pageId, error: response.message });
      throw new Error(response.message || 'Failed to update SEO inheritance');
    }
    
    return response.data;
  } catch (error) {
    logger.error('Error updating SEO inheritance', { pageId, error });
    throw error;
  }
}

// 公共 SEO 数据
/**
 * Get website sitemap
 * @param websiteId Website ID
 * @returns Sitemap XML
 */
export async function getSitemap(websiteId: string): Promise<string> {
  logger.info('Getting sitemap', { websiteId });
  
  try {
    const response = await $get<string>(`${API_BASE}/public/sitemap/${websiteId}`);
    
    if (!response.success) {
      logger.error('Failed to get sitemap', { websiteId, error: response.message });
      throw new Error(response.message || 'Failed to get sitemap');
    }
    
    return response.data;
  } catch (error) {
    logger.error('Error getting sitemap', { websiteId, error });
    throw error;
  }
}

/**
 * Get website robots.txt
 * @param websiteId Website ID
 * @returns Robots.txt content
 */
export async function getRobots(websiteId: string): Promise<string> {
  logger.info('Getting robots.txt', { websiteId });
  
  try {
    const response = await $get<string>(`${API_BASE}/public/robots/${websiteId}`);
    
    if (!response.success) {
      logger.error('Failed to get robots.txt', { websiteId, error: response.message });
      throw new Error(response.message || 'Failed to get robots.txt');
    }
    
    return response.data;
  } catch (error) {
    logger.error('Error getting robots.txt', { websiteId, error });
    throw error;
  }
}
