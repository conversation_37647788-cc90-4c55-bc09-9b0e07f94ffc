import { Website } from '@/types/flow';
import { $get } from '../http';
import { logger } from '@/utils/logger';
// import { getMockWebsites, getMockPageStructure } from './mock-data';

const API_BASE = '/api/v1';
const USE_MOCK_DATA = process.env.NODE_ENV === 'development';

// 配置日志命名空间
logger.configure({ namespace: 'websiteService' });

/**
 * Get all websites for the current user
 * @returns Array of websites
 */
export async function getWebsites(): Promise<Website[]> {
  logger.info('Getting websites');
  
  try {
    const response = await $get(`${API_BASE}/websites`);
    
    if (!response.success) {
      logger.error('Failed to get websites', { error: response.message });
      throw new Error(response.message || 'Failed to get websites');
    }
    
    const websites = Array.isArray(response.data) ? response.data : [];
    
    logger.info('Successfully retrieved websites', { count: websites.length });
    
    return websites;
  } catch (error) {
    logger.error('Error getting websites', { error });
    throw error;
  }
}

/**
 * Get a specific website by ID
 * @param id Website ID
 * @returns Website details
 */
export async function getWebsite(id: string): Promise<Website> {
  logger.info('Getting website', { id });
  
  try {
    const response = await $get(`${API_BASE}/websites/${id}`);
    
    if (!response.success) {
      logger.error('Failed to get website', { id, error: response.message });
      throw new Error(response.message || 'Failed to get website');
    }
    
    // 确保返回的数据符合 Website 类型
    const website = response.data as Website;
    
    if (!website || !website.id) {
      logger.error('Invalid website data', { id, data: response.data });
      throw new Error('Invalid website data received');
    }
    
    return website;
  } catch (error) {
    logger.error('Error getting website', { id, error });
    throw error;
  }
}
