import { lastValueFrom, Subject, Observable, throwError, from } from 'rxjs';

export const $io = new Subject<{ code: number; message: string; }>();

// 添加一个标志，防止刷新令牌时出现循环
let isRefreshing = false;
// 存储等待令牌刷新的请求
const pendingRequests: Array<{
  resolve: (value: any) => void;
  reject: (reason?: any) => void;
}> = [];

export interface ApiSuccessResponse<T = any> {
  statusCode?: number;
  data: T;
  success: true;
  message: string;
}

export interface ApiErrorResponse {
  code: number;
  message: string;
  isError: true;
  success: false;
}

export type ApiResponse<T = any> = ApiSuccessResponse<T> | ApiErrorResponse;

function getHeaders() {
  return {
    "Content-Type": "application/json"
  };
}

interface FetchOptions extends RequestInit {
  credentials?: RequestCredentials;
}

// 刷新令牌的函数
async function refreshToken(): Promise<boolean> {
  if (isRefreshing) {
    // 如果已经在刷新，返回一个 Promise，等待刷新完成
    return new Promise((resolve, reject) => {
      pendingRequests.push({ resolve, reject });
    });
  }

  isRefreshing = true;

  try {
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || '';
    const response = await fetch(`${baseUrl}/api/v1/auth/refresh`, {
      method: 'POST',
      credentials: 'include',
      headers: getHeaders()
    });

    const data = await response.json();
    isRefreshing = false;

    if (response.ok && data.success) {
      // 通知所有等待的请求
      pendingRequests.forEach(request => request.resolve(true));
      pendingRequests.length = 0;
      return true;
    } else {
      // 刷新失败，通知所有等待的请求
      pendingRequests.forEach(request => request.reject(new Error('Token refresh failed')));
      pendingRequests.length = 0;
      return false;
    }
  } catch (error) {
    isRefreshing = false;
    // 通知所有等待的请求
    pendingRequests.forEach(request => request.reject(error));
    pendingRequests.length = 0;
    return false;
  }
}

function $fetch<T>(
  url: string,
  method: string = 'GET',
  data?: any,
  options: FetchOptions = {}
): Observable<ApiResponse<T>> {
  const baseUrl = process.env.NEXT_PUBLIC_API_URL || '';
  const fullUrl = url.startsWith('http') ? url : `${baseUrl}${url}`;

  const payload: RequestInit = {
    method,
    headers: getHeaders(),
    credentials: "include", // 默认包含 cookies
    ...options,
  };

  if (data) {
    payload.body = JSON.stringify(data);
  }

  return new Observable((subscriber) => {
    const makeRequest = () => {
      fetch(fullUrl, payload)
        .then(async (response) => {
          const data = await response.json();
          
          if (response.status === 401) {
            // 如果是刷新令牌的请求，不要尝试刷新
            if (url.includes('/api/v1/auth/refresh')) {
              $io.next({ code: 401, message: 'Unauthorized' });
              subscriber.next({
                success: false,
                code: 401,
                message: 'Refresh token expired',
                isError: true,
              });
              subscriber.complete();
              return;
            }
            
            // 尝试刷新令牌
            try {
              const refreshSuccess = await refreshToken();
              if (refreshSuccess) {
                // 令牌刷新成功，重试原始请求
                makeRequest();
                return;
              } else {
                // 令牌刷新失败，通知需要重新登录
                $io.next({ code: 401, message: 'Unauthorized' });
                subscriber.next({
                  success: false,
                  code: 401,
                  message: 'Session expired',
                  isError: true,
                });
                subscriber.complete();
              }
            } catch (error) {
              // 刷新令牌过程中出错
              $io.next({ code: 401, message: 'Unauthorized' });
              subscriber.next({
                success: false,
                code: 401,
                message: 'Authentication failed',
                isError: true,
              });
              subscriber.complete();
            }
            return;
          }
          
          if (!response.ok) {
            const errorMessage = data?.message || `HTTP error! status: ${response.status}`;
            subscriber.next({
              success: false,
              code: response.status,
              message: errorMessage,
              isError: true,
            });
            subscriber.complete();
            return;
          }

          if (data.success === true) {
            subscriber.next({
              success: true,
              data: data.data,
              message: data.message,
            });
          } else {
            subscriber.next({
              success: false,
              code: response.status,
              message: data.message || 'Request failed',
              isError: true,
            });
          }
          subscriber.complete();
        })
        .catch((error) => {
          subscriber.next({
            success: false,
            code: 500,
            message: error.message || 'Network error',
            isError: true,
          });
          subscriber.complete();
        });
    };

    makeRequest();
  });
}

export async function $get<T>(url: string, options?: FetchOptions): Promise<ApiResponse<T>> {
  return await lastValueFrom($fetch<T>(url, 'GET', null, options));
}

export async function $post<TData = any, TResponse = any>(
  url: string,
  data: TData | null = null,
  options?: FetchOptions
): Promise<ApiResponse<TResponse>> {
  return await lastValueFrom($fetch<TResponse>(url, 'POST', data, options));
}

export async function $put<P = any, T = any>(
  url: string,
  data: P,
  options?: FetchOptions
): Promise<ApiResponse<T>> {
  return await lastValueFrom($fetch<T>(url, 'PUT', data, options));
}

export async function $patch<P = any, T = any>(
  url: string,
  data: P,
  options?: FetchOptions
): Promise<ApiResponse<T>> {
  return await lastValueFrom($fetch<T>(url, 'PATCH', data, options));
}

export async function $delete<TData = any, TResponse = any>(
  url: string,
  options?: FetchOptions
): Promise<ApiResponse<TResponse>> {
  return await lastValueFrom($fetch<TResponse>(url, 'DELETE', null, options));
}
