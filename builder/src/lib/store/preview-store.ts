import { create } from 'zustand';

// Define ActiveTab type
export type ActiveTab = 'preview' | 'edit' | 'editor' | 'creator' | 'history';

export interface PreviewState {
  showPreview: boolean;
  activeTab: ActiveTab;
  setShowPreview: (show: boolean) => void;
  setActiveTab: (tab: ActiveTab) => void;
  togglePreview: () => void;
}

export const usePreviewStore = create<PreviewState>((set) => ({
  showPreview: true,
  activeTab: 'preview',
  setShowPreview: (show: boolean) => set({ showPreview: show }),
  setActiveTab: (tab: ActiveTab) => set({ activeTab: tab }),
  togglePreview: () => set((state) => ({ showPreview: !state.showPreview })),
}));
