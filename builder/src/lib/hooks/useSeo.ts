import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { seoKeys } from '../api/queryKeys';
import {
  getWebsiteSeo,
  updateWebsiteSeo,
  updateWebsiteMetadata,
  updateRobotsSitemap,
  updateSocialDefaults,
  updateSecurityPerformance,
  updateStructuredData,
  updateHreflangSettings,
  updateAnalyticsSettings,
  updateBrandSettings,
  getPageSeo,
  updatePageSeo,
  updateBasicSeo,
  updateSocialMedia,
  updatePageStructuredData,
  updateAdvancedSeo,
  updateInheritSeo,
  getSitemap,
  getRobots,
  WebsiteSeoDTO,
  WebsiteMetadataDTO,
  RobotsSitemapDTO,
  SocialDefaultsDTO,
  SecurityPerformanceDTO,
  StructuredDataDTO,
  HreflangSettingsDTO,
  PageSeoDTO,
  BasicSeoDTO,
  SocialMediaDTO,
  PageStructuredDataDTO,
  AdvancedSeoDTO,
  InheritSeoDTO
} from '../api/seo';

// Website SEO Hooks

/**
 * Hook to fetch website SEO settings
 */
export function useWebsiteSeo(websiteId: string) {
  return useQuery({
    queryKey: seoKeys.websiteSeo(websiteId),
    queryFn: () => getWebsiteSeo(websiteId),
    enabled: !!websiteId,
  });
}

/**
 * Hook to update website SEO settings (full update)
 */
export function useUpdateWebsiteSeo() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ websiteId, data }: { websiteId: string; data: WebsiteSeoDTO }) => 
      updateWebsiteSeo(websiteId, data),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: seoKeys.websiteSeo(variables.websiteId) });
    },
  });
}

/**
 * Hook to update website metadata (partial update)
 */
export function useUpdateWebsiteMetadata() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ websiteId, data }: { websiteId: string; data: WebsiteMetadataDTO }) => 
      updateWebsiteMetadata(websiteId, data),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: seoKeys.websiteSeo(variables.websiteId) });
      queryClient.invalidateQueries({ queryKey: seoKeys.websiteMetadata(variables.websiteId) });
    },
  });
}

/**
 * Hook to update robots and sitemap settings (partial update)
 */
export function useUpdateRobotsSitemap() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ websiteId, data }: { websiteId: string; data: RobotsSitemapDTO }) => 
      updateRobotsSitemap(websiteId, data),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: seoKeys.websiteSeo(variables.websiteId) });
      queryClient.invalidateQueries({ queryKey: seoKeys.websiteRobotsSitemap(variables.websiteId) });
      queryClient.invalidateQueries({ queryKey: seoKeys.sitemap(variables.websiteId) });
      queryClient.invalidateQueries({ queryKey: seoKeys.robots(variables.websiteId) });
    },
  });
}

/**
 * Hook to update social media defaults (partial update)
 */
export function useUpdateSocialDefaults() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ websiteId, data }: { websiteId: string; data: SocialDefaultsDTO }) => 
      updateSocialDefaults(websiteId, data),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: seoKeys.websiteSeo(variables.websiteId) });
      queryClient.invalidateQueries({ queryKey: seoKeys.websiteSocialDefaults(variables.websiteId) });
    },
  });
}

/**
 * Hook to update security and performance settings (partial update)
 */
export function useUpdateSecurityPerformance() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ websiteId, data }: { websiteId: string; data: SecurityPerformanceDTO }) => 
      updateSecurityPerformance(websiteId, data),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: seoKeys.websiteSeo(variables.websiteId) });
      queryClient.invalidateQueries({ queryKey: seoKeys.websiteSecurityPerformance(variables.websiteId) });
    },
  });
}

/**
 * Hook to update structured data (partial update)
 */
export function useUpdateStructuredData() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ websiteId, data }: { websiteId: string; data: StructuredDataDTO }) => 
      updateStructuredData(websiteId, data),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: seoKeys.websiteSeo(variables.websiteId) });
      queryClient.invalidateQueries({ queryKey: seoKeys.websiteStructuredData(variables.websiteId) });
    },
  });
}

/**
 * Hook to update hreflang settings (partial update)
 */
export function useUpdateHreflangSettings() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ websiteId, data }: { websiteId: string; data: HreflangSettingsDTO }) => 
      updateHreflangSettings(websiteId, data),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: seoKeys.websiteSeo(variables.websiteId) });
      queryClient.invalidateQueries({ queryKey: seoKeys.websiteHreflangSettings(variables.websiteId) });
    },
  });
}

/**
 * Hook to update analytics settings (partial update)
 */
export function useUpdateAnalyticsSettings() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ websiteId, data }: { websiteId: string; data: { analyticsSettings: any } }) => 
      updateAnalyticsSettings(websiteId, data),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: seoKeys.websiteSeo(variables.websiteId) });
      queryClient.invalidateQueries({ queryKey: seoKeys.websiteAnalyticsSettings(variables.websiteId) });
    },
  });
}

/**
 * Hook to update brand settings (partial update)
 */
export function useUpdateBrandSettings() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ websiteId, data }: { websiteId: string; data: any }) => {
      console.log("useUpdateBrandSettings - data received:", JSON.stringify(data));
      
      // 确保 brandSettings 存在
      if (!data || !data.brandSettings) {
        console.error("Brand settings data is missing or invalid:", data);
        throw new Error("Invalid brand settings data");
      }
      
      // 直接传递 brandSettings 对象给 API 函数
      const brandSettings = data.brandSettings;
      console.log("Passing brandSettings to API:", JSON.stringify(brandSettings));
      
      return updateBrandSettings(websiteId, brandSettings);
    },
    onSuccess: (data, variables) => {
      console.log("Brand settings updated successfully:", data);
      queryClient.invalidateQueries({ queryKey: seoKeys.websiteSeo(variables.websiteId) });
      queryClient.invalidateQueries({ queryKey: seoKeys.websiteBrandSettings(variables.websiteId) });
    },
    onError: (error) => {
      console.error("Brand settings update error:", error);
    }
  });
}

/**
 * Hook to update SEO inheritance settings (partial update)
 */
export function useUpdateInheritSeo() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ pageId, data }: { pageId: string; data: InheritSeoDTO }) => 
      updateInheritSeo(pageId, data),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: seoKeys.pageSeo(variables.pageId) });
      queryClient.invalidateQueries({ queryKey: seoKeys.pageInheritSeo(variables.pageId) });
    },
  });
}

// Page SEO Hooks

/**
 * Hook to fetch page SEO settings
 */
export function usePageSeo(pageId: string) {
  return useQuery({
    queryKey: seoKeys.pageSeo(pageId),
    queryFn: () => getPageSeo(pageId),
    enabled: !!pageId,
  });
}

/**
 * Hook to update page SEO settings (full update)
 */
export function useUpdatePageSeo() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ pageId, data }: { pageId: string; data: PageSeoDTO }) => 
      updatePageSeo(pageId, data),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: seoKeys.pageSeo(variables.pageId) });
    },
  });
}

/**
 * Hook to update basic SEO settings (partial update)
 */
export function useUpdateBasicSeo() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ pageId, data }: { pageId: string; data: BasicSeoDTO }) => 
      updateBasicSeo(pageId, data),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: seoKeys.pageSeo(variables.pageId) });
      queryClient.invalidateQueries({ queryKey: seoKeys.pageBasicSeo(variables.pageId) });
    },
  });
}

/**
 * Hook to update social media settings (partial update)
 */
export function useUpdateSocialMedia() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ pageId, data }: { pageId: string; data: SocialMediaDTO }) => 
      updateSocialMedia(pageId, data),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: seoKeys.pageSeo(variables.pageId) });
      queryClient.invalidateQueries({ queryKey: seoKeys.pageSocialMedia(variables.pageId) });
    },
  });
}

/**
 * Hook to update page structured data (partial update)
 */
export function useUpdatePageStructuredData() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ pageId, data }: { pageId: string; data: PageStructuredDataDTO }) => 
      updatePageStructuredData(pageId, data),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: seoKeys.pageSeo(variables.pageId) });
      queryClient.invalidateQueries({ queryKey: seoKeys.pageStructuredData(variables.pageId) });
    },
  });
}

/**
 * Hook to update advanced SEO settings (partial update)
 */
export function useUpdateAdvancedSeo() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ pageId, data }: { pageId: string; data: AdvancedSeoDTO }) => 
      updateAdvancedSeo(pageId, data),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: seoKeys.pageSeo(variables.pageId) });
      queryClient.invalidateQueries({ queryKey: seoKeys.pageAdvancedSeo(variables.pageId) });
    },
  });
}

// Public SEO Data Hooks

/**
 * Hook to fetch website sitemap
 */
export function useSitemap(websiteId: string) {
  return useQuery({
    queryKey: seoKeys.sitemap(websiteId),
    queryFn: () => getSitemap(websiteId),
    enabled: !!websiteId,
  });
}

/**
 * Hook to fetch website robots.txt
 */
export function useRobots(websiteId: string) {
  return useQuery({
    queryKey: seoKeys.robots(websiteId),
    queryFn: () => getRobots(websiteId),
    enabled: !!websiteId,
  });
}
