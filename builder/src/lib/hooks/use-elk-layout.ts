import { useCallback, useEffect, useState } from 'react';
import EL<PERSON>, { ElkNode, ElkExtendedEdge } from 'elkjs/lib/elk.bundled.js';
import { PageNode, PageEdge } from '@/types/flow';
import { logger } from '@/utils/logger';

// Configure logger namespace
logger.configure({ namespace: 'useElkLayout' });

// ELK layout instance
const elk = new ELK();

// Layout configuration options
export interface ElkLayoutOptions {
  algorithm?: 'layered' | 'force' | 'mrtree' | 'radial' | 'stress';
  direction?: 'DOWN' | 'UP' | 'LEFT' | 'RIGHT';
  aspectRatio?: number;
  spacing?: number;
  nodePlacement?: 'LINEAR_SEGMENTS' | 'BRANDES_KOEPF' | 'NETWORK_SIMPLEX';
  edgeRouting?: 'ORTHOGONAL' | 'POLYLINE' | 'SPLINES';
  hierarchyHandling?: 'INCLUDE_CHILDREN' | 'SEPARATE_CHILDREN';
  rankSpacing?: number;
  nodeSpacing?: number;
  padding?: [number, number, number, number];
}

// Default layout options - adjusted for larger mobile preview nodes
const defaultOptions: ElkLayoutOptions = {
  algorithm: 'layered',
  direction: 'DOWN',
  spacing: 200,         // Increased spacing between components
  nodePlacement: 'BRANDES_KOEPF',
  edgeRouting: 'ORTHOGONAL',
  hierarchyHandling: 'SEPARATE_CHILDREN', // Changed to SEPARATE_CHILDREN to avoid nested layout issues
  rankSpacing: 250,     // Increased spacing between ranks
  nodeSpacing: 150,     // Increased spacing between nodes
  padding: [40, 40, 40, 40], // Increased padding
};

/**
 * Convert PageNode to ElkNode
 */
function toElkNode(node: PageNode): ElkNode {
  return {
    id: node.id,
    width: node.width || 420,  // Increased default width for mobile preview
    height: node.height || 600, // Default height for mobile preview
    // Don't use existing positions, let ELK completely recalculate
    layoutOptions: {
      'nodeSize.fixed': 'true',
      'nodeLabels.placement': 'INSIDE',
      'elk.padding': '20,20,20,20',
    },
    labels: [{ text: node.data.title || node.id }],
  };
}

/**
 * Convert PageEdge to ElkExtendedEdge
 */
function toElkEdge(edge: PageEdge): ElkExtendedEdge {
  return {
    id: edge.id,
    sources: [edge.source],
    targets: [edge.target],
    layoutOptions: {
      'edge.thickness': '2',
      'edge.type': edge.data?.type === 'language' ? 'DASHED' : 'SOLID',
    },
  };
}

/**
 * Use ELK layout algorithm to calculate node and edge positions
 */
export function useElkLayout(
  inputNodes: PageNode[] = [],
  inputEdges: PageEdge[] = [],
  options: ElkLayoutOptions = defaultOptions,
  dependencies: any[] = []
) {
  const [nodes, setNodes] = useState<PageNode[]>(inputNodes);
  const [edges, setEdges] = useState<PageEdge[]>(inputEdges);
  const [isLayouting, setIsLayouting] = useState(false);

  // Calculate layout
  const calculateLayout = useCallback(async () => {
    if (inputNodes.length === 0) {
      return { nodes: [], edges: [] };
    }

    setIsLayouting(true);
    logger.info('Starting ELK layout calculation', { 
      nodeCount: inputNodes.length, 
      edgeCount: inputEdges.length,
      algorithm: options.algorithm
    });

    try {
      // 1. Separate language nodes and page nodes
      const languageNodes = inputNodes.filter(node => node.data.isLanguageNode);
      const pageNodes = inputNodes.filter(node => !node.data.isLanguageNode);
      
      // 2. Create language group mapping
      const languageGroups: Record<string, PageNode[]> = {};
      
      // Initialize language groups
      languageNodes.forEach(node => {
        const language = node.data.language || '';
        languageGroups[language] = [];
      });
      
      // Assign page nodes to language groups
      pageNodes.forEach(node => {
        const language = node.data.language || '';
        if (languageGroups[language]) {
          languageGroups[language].push(node);
        } else {
          // If no corresponding language group, create one
          languageGroups[language] = [node];
        }
      });

      // 3. Create ELK graph structure
      const elkGraph: ElkNode = {
        id: 'root',
        layoutOptions: {
          'elk.algorithm': options.algorithm || 'layered',
          'elk.direction': options.direction || 'DOWN',
          'elk.spacing.nodeNode': (options.nodeSpacing || 150).toString(),
          'elk.layered.spacing.nodeNodeBetweenLayers': (options.rankSpacing || 250).toString(),
          'elk.spacing.componentComponent': (options.spacing || 200).toString(),
          'elk.padding': options.padding 
            ? `[${options.padding.join(', ')}]` 
            : '[40, 40, 40, 40]',
          'elk.hierarchyHandling': options.hierarchyHandling || 'SEPARATE_CHILDREN',
          'elk.layered.nodePlacement.strategy': options.nodePlacement || 'BRANDES_KOEPF',
          'elk.edgeRouting': options.edgeRouting || 'ORTHOGONAL',
          'elk.layered.considerModelOrder.strategy': 'NODES_AND_EDGES',
          'elk.layered.crossingMinimization.forceNodeModelOrder': 'true',
          'elk.layered.crossingMinimization.strategy': 'LAYER_SWEEP',
          'elk.layered.layering.strategy': 'NETWORK_SIMPLEX',
          'elk.layered.compaction.connectedComponents': 'true',
          'elk.layered.highDegreeNodes.treatment': 'true',
          'elk.layered.wrapping.additionalEdgeSpacing': '10',
          // 增强树形结构的设置
          'elk.spacing.baseValue': '80',
          'elk.layered.mergeEdges': 'false',
          'elk.layered.thoroughness': '10',
          'elk.layered.unnecessaryBendpoints': 'true',
          'elk.layered.feedbackEdges': 'true',
        },
        children: [],
        edges: [],
      };

      // 4. Add all nodes to ELK graph
      inputNodes.forEach(node => {
        elkGraph.children = elkGraph.children || [];
        elkGraph.children.push(toElkNode(node));
      });

      // 5. Add all edges to ELK graph
      inputEdges.forEach(edge => {
        elkGraph.edges = elkGraph.edges || [];
        elkGraph.edges.push(toElkEdge(edge));
      });

      // 6. Run ELK layout algorithm
      const layoutedGraph = await elk.layout(elkGraph);
      logger.info('ELK layout completed', { 
        algorithm: options.algorithm,
        nodeCount: inputNodes.length
      });

      // 7. Update node positions
      const updatedNodes = [...inputNodes];
      
      if (layoutedGraph.children) {
        layoutedGraph.children.forEach(elkNode => {
          const node = updatedNodes.find(n => n.id === elkNode.id);
          if (node && elkNode.x !== undefined && elkNode.y !== undefined) {
            // Update node position
            node.position = { x: elkNode.x, y: elkNode.y };
            
            // Update node size
            node.width = elkNode.width;
            node.height = elkNode.height;
            
            // Update node style
            node.style = {
              ...node.style,
              width: elkNode.width,
              height: elkNode.height,
            };
          }
        });
      }

      // 8. Perform additional processing on language nodes to make them more prominent
      languageNodes.forEach(node => {
        const updatedNode = updatedNodes.find(n => n.id === node.id);
        if (updatedNode) {
          // Increase language node size
          updatedNode.width = (updatedNode.width || 375) + 40;
          updatedNode.height = (updatedNode.height || 600) + 20;
          
          // Update style
          updatedNode.style = {
            ...updatedNode.style,
            width: updatedNode.width,
            height: updatedNode.height,
            backgroundColor: 'rgba(99, 102, 241, 0.1)',
            border: '2px dashed rgba(99, 102, 241, 0.5)',
            borderRadius: '12px',
            padding: '20px',
          };
        }
      });

      setNodes(updatedNodes);
      setEdges(inputEdges);
      logger.info('ELK layout calculation completed', { 
        nodeCount: updatedNodes.length 
      });
      return { nodes: updatedNodes, edges: inputEdges };
    } catch (error) {
      logger.error('Error calculating ELK layout', { error });
      return { nodes: inputNodes, edges: inputEdges };
    } finally {
      setIsLayouting(false);
    }
  }, [inputNodes, inputEdges, options]);

  // Recalculate layout when input nodes, edges, or options change
  useEffect(() => {
    calculateLayout();
  }, [calculateLayout, ...dependencies]);

  return {
    nodes,
    edges,
    isLayouting,
    calculateLayout,
  };
}
