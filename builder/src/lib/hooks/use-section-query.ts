import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useToast } from '@/components/ui/use-toast'
import { pageKeys } from '@/lib/api/queryKeys'
import pageService from '@/modules/page/service'
import pageVersionService from '@/modules/pageVersion/service'
import Logger from '@/lib/logger'
import { clone, assocPath, findIndex, update, insert } from 'ramda'
import { nanoid } from 'nanoid'

const logger = new Logger('use-section-query')

/**
 * Change section variant
 */
export const useEditVariant = () => {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ pageId, sectionId, variant }: { pageId: string; sectionId: string; variant: string }) => {
      logger.debug('Starting variant change:', { pageId, sectionId, variant })
      // Get current page data from cache
      const currentPage = queryClient.getQueryData(pageKeys.fetchPage(pageId)) as any
      
      if (!currentPage) {
        throw new Error('Page not found in cache')
      }
      
      // Clone the page data and update the section variant
      const updatedPage = clone(currentPage)
      const sections = updatedPage.configuration?.sections
      
      if (!sections) {
        throw new Error('Sections not found in page configuration')
      }
      
      const section = sections.find((section: any) => section.id === sectionId)
      if (!section) {
        throw new Error(`Section with ID ${sectionId} not found`)
      }
      
      // Update the variant
      section.variant = variant
      
      // 使用 pageVersionService.createPageVersion 创建新版本
      const result = await pageVersionService.createPageVersion({
        pageId,
        configuration: updatedPage.configuration
      })
      logger.debug('createPageVersion result:', result)
      return result
    },
    onSuccess: (data, variables) => {
      // Invalidate page query to refresh data
      queryClient.invalidateQueries({ queryKey: pageKeys.fetchPage(variables.pageId) })
      toast({ 
        title: "Updated", 
        description: "Section style has been updated" 
      })
    },
    onError: (error) => {
      logger.error('Failed to update section variant:', error)
      toast({ 
        title: "Error", 
        description: "Failed to update section style", 
        variant: "destructive" 
      })
    }
  })
}

/**
 * Delete section
 */
export const useDeleteSection = () => {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ pageId, sectionId }: { pageId: string; sectionId: string }) => {
      // Get current page data from cache
      const currentPage = queryClient.getQueryData(pageKeys.fetchPage(pageId)) as any
      
      if (!currentPage) {
        throw new Error('Page not found in cache')
      }
      
      // Clone the page data and remove the section
      const updatedPage = clone(currentPage)
      const sections = updatedPage.configuration?.sections
      
      if (!sections) {
        throw new Error('Sections not found in page configuration')
      }
      
      // Filter out the section to delete
      updatedPage.configuration.sections = sections.filter((section: any) => section.id !== sectionId)
      
      // 使用 pageVersionService.createPageVersion 创建新版本
      const result = await pageVersionService.createPageVersion({
        pageId,
        configuration: updatedPage.configuration
      })
      logger.debug('createPageVersion result:', result)
      return result
    },
    onSuccess: (data, variables) => {
      // Invalidate page query to refresh data
      queryClient.invalidateQueries({ queryKey: pageKeys.fetchPage(variables.pageId) })
      toast({ 
        title: "Deleted", 
        description: "Section has been deleted" 
      })
    },
    onError: (error) => {
      logger.error('Failed to delete section:', error)
      toast({ 
        title: "Error", 
        description: "Failed to delete section", 
        variant: "destructive" 
      })
    }
  })
}

/**
 * Move section up
 */
export const useMoveSection = () => {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ 
      pageId, 
      sectionId, 
      direction 
    }: { 
      pageId: string; 
      sectionId: string; 
      direction: 'up' | 'down' 
    }) => {
      // Get current page data from cache
      const currentPage = queryClient.getQueryData(pageKeys.fetchPage(pageId)) as any
      
      if (!currentPage) {
        throw new Error('Page not found in cache')
      }
      
      // Clone the page data
      const updatedPage = clone(currentPage)
      const sections = updatedPage.configuration?.sections
      
      if (!sections) {
        throw new Error('Sections not found in page configuration')
      }
      
      // Find the section index
      const index = findIndex((section: any) => section.id === sectionId, sections)
      
      if (index < 0) {
        throw new Error(`Section with ID ${sectionId} not found`)
      }
      
      // Check if we can move in the requested direction
      if (direction === 'up' && index === 0) {
        throw new Error('Cannot move up the first section')
      }
      
      if (direction === 'down' && index === sections.length - 1) {
        throw new Error('Cannot move down the last section')
      }
      
      // 使用 ramda 的函数进行数组元素交换
      let updatedSections = [...sections]
      if (direction === 'up') {
        // 交换当前元素和前一个元素
        const temp = updatedSections[index]
        updatedSections[index] = updatedSections[index - 1]
        updatedSections[index - 1] = temp
      } else {
        // 交换当前元素和后一个元素
        const temp = updatedSections[index]
        updatedSections[index] = updatedSections[index + 1]
        updatedSections[index + 1] = temp
      }
      
      // 更新页面配置
      updatedPage.configuration.sections = updatedSections
      
      // 使用 pageVersionService.createPageVersion 创建新版本
      const result = await pageVersionService.createPageVersion({
        pageId,
        configuration: updatedPage.configuration
      })
      logger.debug('createPageVersion result:', result)
      return result
    },
    onSuccess: (data, variables) => {
      // Invalidate page query to refresh data
      queryClient.invalidateQueries({ queryKey: pageKeys.fetchPage(variables.pageId) })
      toast({ 
        title: "Moved", 
        description: `Section has been moved ${variables.direction}` 
      })
    },
    onError: (error) => {
      logger.error('Failed to move section:', error)
      toast({ 
        title: "Error", 
        description: "Failed to move section", 
        variant: "destructive" 
      })
    }
  })
}

/**
 * Add section
 */
export const useAddSection = () => {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ 
      pageId, 
      afterSectionId, 
      sectionData 
    }: { 
      pageId: string; 
      afterSectionId: string | null; 
      sectionData: any 
    }) => {
      // Get current page data from cache
      const currentPage = queryClient.getQueryData(pageKeys.fetchPage(pageId)) as any
      
      if (!currentPage) {
        throw new Error('Page not found in cache')
      }
      
      // Clone the page data
      const updatedPage = clone(currentPage)
      
      // Ensure configuration and sections exist
      if (!updatedPage.configuration) {
        updatedPage.configuration = {}
      }
      
      if (!updatedPage.configuration.sections) {
        updatedPage.configuration.sections = []
      }
      
      const sections = updatedPage.configuration.sections
      
      // If afterSectionId is provided, insert after that section
      if (afterSectionId) {
        const index = findIndex((section: any) => section.id === afterSectionId, sections)
        
        if (index !== -1) {
          // 使用扩展运算符在指定位置插入新区块
          updatedPage.configuration.sections = [
            ...sections.slice(0, index + 1),
            sectionData,
            ...sections.slice(index + 1)
          ]
        } else {
          // If section not found, add to the end
          updatedPage.configuration.sections = [...sections, sectionData]
        }
      } else {
        // If no afterSectionId, add to the end
        updatedPage.configuration.sections = [...sections, sectionData]
      }
      
      // 使用 pageVersionService.createPageVersion 创建新版本
      return await pageVersionService.createPageVersion({
        pageId,
        configuration: updatedPage.configuration
      })
    },
    onSuccess: (_, variables) => {
      // Invalidate page query to refresh data
      queryClient.invalidateQueries({ queryKey: pageKeys.fetchPage(variables.pageId) })
      toast({ 
        title: "Added", 
        description: "New section has been added" 
      })
    },
    onError: (error) => {
      logger.error('Failed to add section:', error)
      toast({ 
        title: "Error", 
        description: "Failed to add section", 
        variant: "destructive" 
      })
    }
  })
}

/**
 * Edit section
 */
export const useEditSection = () => {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ 
      pageId, 
      sectionId, 
      sectionData 
    }: { 
      pageId: string; 
      sectionId: string; 
      sectionData: any 
    }) => {
      // Get current page data from cache
      const currentPage = queryClient.getQueryData(pageKeys.fetchPage(pageId)) as any
      
      if (!currentPage) {
        throw new Error('Page not found in cache')
      }
      
      // Clone the page data
      const updatedPage = clone(currentPage)
      const sections = updatedPage.configuration?.sections
      
      if (!sections) {
        throw new Error('Sections not found in page configuration')
      }
      
      // Find the section index
      const index = findIndex((section: any) => section.id === sectionId, sections)
      
      if (index < 0) {
        throw new Error(`Section with ID ${sectionId} not found`)
      }
      
      // Update the section with new data
      updatedPage.configuration.sections = update(
        index,
        { ...sections[index], ...sectionData },
        sections
      )
      
      // 使用 pageVersionService.createPageVersion 创建新版本
      return await pageVersionService.createPageVersion({
        pageId,
        configuration: updatedPage.configuration
      })
    },
    onSuccess: (_, variables) => {
      // Invalidate page query to refresh data
      queryClient.invalidateQueries({ queryKey: pageKeys.fetchPage(variables.pageId) })
      toast({ 
        title: "Updated", 
        description: "Section has been updated" 
      })
    },
    onError: (error) => {
      logger.error('Failed to update section:', error)
      toast({ 
        title: "Error", 
        description: "Failed to update section", 
        variant: "destructive" 
      })
    }
  })
}

/**
 * Clone section
 */
export const useCloneSection = () => {
  const queryClient = useQueryClient()
  const { toast } = useToast()
  const logger = new Logger('useCloneSection')

  return useMutation({
    mutationFn: async ({ 
      pageId, 
      sectionId 
    }: { 
      pageId: string; 
      sectionId: string; 
    }) => {
      // Get current page data from cache
      const currentPage = queryClient.getQueryData(pageKeys.fetchPage(pageId)) as any
      
      if (!currentPage) {
        throw new Error('Page not found in cache')
      }
      
      // Clone the page data
      const updatedPage = clone(currentPage)
      
      // Ensure configuration and sections exist
      if (!updatedPage.configuration) {
        updatedPage.configuration = {}
      }
      
      if (!updatedPage.configuration.sections) {
        updatedPage.configuration.sections = []
      }
      
      const sections = updatedPage.configuration.sections
      
      // Find the section to clone
      const index = findIndex((section: any) => section.id === sectionId, sections)
      
      if (index === -1) {
        throw new Error('Section not found')
      }
      
      // Clone the section and generate a new ID
      const sectionToClone = clone(sections[index])
      sectionToClone.id = nanoid(7) // Generate a new ID for the cloned section
      
      // Insert the cloned section after the original section
      updatedPage.configuration.sections = [
        ...sections.slice(0, index + 1),
        sectionToClone,
        ...sections.slice(index + 1)
      ]
      
      // 使用 pageVersionService.createPageVersion 创建新版本
      return await pageVersionService.createPageVersion({
        pageId,
        configuration: updatedPage.configuration
      })
    },
    onSuccess: (_, variables) => {
      // Invalidate page query to refresh data
      queryClient.invalidateQueries({ queryKey: pageKeys.fetchPage(variables.pageId) })
      toast({ 
        title: "Cloned", 
        description: "Section has been cloned" 
      })
    },
    onError: (error) => {
      logger.error('Failed to clone section:', error)
      toast({ 
        title: "Error", 
        description: "Failed to clone section", 
        variant: "destructive" 
      })
    }
  })
}
