import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { validateDomain, createWebsite, CreateWebsiteData } from '../api'
import { useToast } from '@/components/ui/use-toast'
import { websiteKeys } from '../api/queryKeys'
import websiteService from '@/modules/website/service'
import { WebsiteModel } from '@/modules/website/model'
import { CustomDomain, CustomDomainCreateDto } from '@/modules/website/types'
import Logger from '@/lib/logger'

const logger = new Logger('WebsiteHooks')

/**
 * Validate domain
 */
export const useValidateDomain = () => {
  return useMutation({
    mutationFn: validateDomain,
  })
}

/**
 * Create website
 */
export const useCreateWebsite = () => {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: createWebsite,
    onSuccess: (newWebsite) => {
      // Update website list cache
      queryClient.invalidateQueries({ queryKey: websiteKeys.lists() })
      toast({
        title: 'Success',
        description: 'Website created successfully',
      })
    },
    onError: (error) => {
      console.error('Failed to create website:', error)
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to create website',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Fetch website list
 */
export const useFetchWebsites = () => {
  const { toast } = useToast()

  return useQuery({
    queryKey: websiteKeys.fetchWebsites(),
    queryFn: async () => {
      try {
        return await websiteService.getWebsites()
      } catch (error) {
        logger.error('Failed to fetch websites', error)
        toast({
          title: 'Error',
          description: 'Failed to fetch website list',
          variant: 'destructive',
        })
        throw error
      }
    },
  })
}

/**
 * Fetch single website
 */
export const useFetchWebsite = (websiteId?: string) => {
  const { toast } = useToast()

  return useQuery({
    queryKey: websiteKeys.fetchWebsite(websiteId),
    queryFn: async () => {
      if (!websiteId) return null
      try {
        return await websiteService.getWebsite(websiteId)
      } catch (error) {
        logger.error('Failed to fetch website', { websiteId, error })
        toast({
          title: 'Error',
          description: 'Failed to fetch website information',
          variant: 'destructive',
        })
        throw error
      }
    },
    enabled: !!websiteId,
  })
}

/**
 * Update website
 */
export const useUpdateWebsite = () => {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ websiteId, data }: { websiteId: string; data: Partial<WebsiteModel> }) => {
      return await websiteService.updateWebsiteConfig(websiteId, data)
    },
    onSuccess: (updatedWebsite, variables) => {
      // 直接更新缓存，而不是使缓存失效
      // 更新网站详情缓存
      queryClient.setQueryData(websiteKeys.detail(variables.websiteId), (oldData: WebsiteModel | undefined) => {
        return oldData ? { ...oldData, ...variables.data } : undefined;
      });
      
      queryClient.setQueryData(websiteKeys.fetchWebsite(variables.websiteId), (oldData: WebsiteModel | undefined) => {
        return oldData ? { ...oldData, ...variables.data } : undefined;
      });
      
      // 更新网站列表缓存
      queryClient.setQueryData(websiteKeys.all(), (oldData: WebsiteModel[] | undefined) => {
        if (!oldData) return undefined;
        return oldData.map(site => 
          site.id === variables.websiteId ? { ...site, ...variables.data } : site
        );
      });
      
      queryClient.setQueryData(websiteKeys.fetchWebsites(), (oldData: WebsiteModel[] | undefined) => {
        if (!oldData) return undefined;
        return oldData.map(site => 
          site.id === variables.websiteId ? { ...site, ...variables.data } : site
        );
      });
      
      // 成功提示已在组件中处理，这里不再显示
    },
    onError: (error) => {
      logger.error('Failed to update website', error)
      toast({
        title: 'Error',
        description: 'Failed to update website',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Delete website
 */
export const useDeleteWebsite = () => {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (websiteId: string) => {
      return await websiteService.deleteWebsite(websiteId)
    },
    onSuccess: (_, websiteId) => {
      // Update website list cache
      queryClient.invalidateQueries({ queryKey: websiteKeys.lists() })
      toast({
        title: 'Success',
        description: 'Website deleted successfully',
      })
    },
    onError: (error) => {
      logger.error('Failed to delete website', error)
      toast({
        title: 'Error',
        description: 'Failed to delete website',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Save website configuration
 */
export const useSaveWebsiteConfig = () => {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ websiteId, config }: { websiteId: string; config: any }) => {
      return await websiteService.setWebsiteConfig(websiteId, config)
    },
    onSuccess: (_, variables) => {
      // Update website cache
      queryClient.invalidateQueries({ queryKey: websiteKeys.fetchWebsite(variables.websiteId) })
      toast({
        title: 'Success',
        description: 'Configuration saved successfully',
      })
    },
    onError: (error) => {
      logger.error('Failed to save website config', error)
      toast({
        title: 'Error',
        description: 'Failed to save configuration',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Get custom domain
 */
export const useCustomDomain = (websiteId?: string) => {
  return useQuery({
    queryKey: websiteKeys.customDomain(websiteId || ''),
    queryFn: () => websiteId ? websiteService.getCustomDomain(websiteId) : null,
    enabled: !!websiteId,
  })
}

/**
 * Set custom domain
 */
export const useSetCustomDomain = () => {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ websiteId, domain }: { websiteId: string; domain: string }) => {
      return await websiteService.setCustomDomain(websiteId, domain)
    },
    onSuccess: (_, variables) => {
      // Update custom domain cache
      queryClient.invalidateQueries({ queryKey: websiteKeys.customDomain(variables.websiteId) })
      toast({
        title: 'Success',
        description: 'Custom domain set successfully',
      })
    },
    onError: (error) => {
      logger.error('Failed to set custom domain', error)
      toast({
        title: 'Error',
        description: 'Failed to set custom domain',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Verify custom domain
 */
export const useVerifyCustomDomain = () => {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (websiteId: string) => {
      return await websiteService.verifyCustomDomain(websiteId)
    },
    onSuccess: (_, websiteId) => {
      // Update custom domain cache
      queryClient.invalidateQueries({ queryKey: websiteKeys.customDomain(websiteId) })
      toast({
        title: 'Success',
        description: 'Domain verified successfully',
      })
    },
    onError: (error) => {
      logger.error('Failed to verify custom domain', error)
      toast({
        title: 'Error',
        description: 'Failed to verify domain',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Delete custom domain
 */
export const useDeleteCustomDomain = () => {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (websiteId: string) => {
      return await websiteService.deleteCustomDomain(websiteId)
    },
    onSuccess: (_, websiteId) => {
      // Update custom domain cache
      queryClient.invalidateQueries({ queryKey: websiteKeys.customDomain(websiteId) })
      toast({
        title: 'Success',
        description: 'Custom domain deleted successfully',
      })
    },
    onError: (error) => {
      logger.error('Failed to delete custom domain', error)
      toast({
        title: 'Error',
        description: 'Failed to delete custom domain',
        variant: 'destructive',
      })
    },
  })
}
