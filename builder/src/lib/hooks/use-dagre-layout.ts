import { useCallback, useEffect, useState } from 'react';
import dagre from '@dagrejs/dagre';
import { Position } from '@xyflow/react';
import { PageNode, PageEdge } from '@/types/flow';
import { logger } from '@/lib/utils/logger';

// 配置日志命名空间
logger.configure({ namespace: 'useDagreLayout' });

// 布局配置选项
export interface DagreLayoutOptions {
  direction?: 'TB' | 'BT' | 'LR' | 'RL';
  nodeWidth?: number;
  nodeHeight?: number;
  rankSeparation?: number;
  nodeSeparation?: number;
  edgeSeparation?: number;
  marginX?: number;
  marginY?: number;
  align?: 'UL' | 'UR' | 'DL' | 'DR';
  ranker?: 'network-simplex' | 'tight-tree' | 'longest-path';
}

// 默认布局选项
const defaultOptions: DagreLayoutOptions = {
  direction: 'TB',
  nodeWidth: 320,      // 默认页面节点宽度，接近真实手机尺寸
  nodeHeight: 640,     // 默认页面节点高度
  rankSeparation: 300, // 层级间距
  nodeSeparation: 100, // 同级节点间距
  edgeSeparation: 50,  // 边间距
  marginX: 50,         // 水平边距
  marginY: 50,         // 垂直边距
  align: 'UL',         // 左上对齐
  ranker: 'network-simplex', // 使用网络单纯形算法
};

/**
 * 使用 dagre 布局算法计算节点和边的位置
 */
export function useDagreLayout(
  inputNodes: PageNode[] = [],
  inputEdges: PageEdge[] = [],
  options: DagreLayoutOptions = defaultOptions,
  dependencies: any[] = []
) {
  const [nodes, setNodes] = useState<PageNode[]>(inputNodes);
  const [edges, setEdges] = useState<PageEdge[]>(inputEdges);
  const [isLayouting, setIsLayouting] = useState(false);

  // 计算布局
  const calculateLayout = useCallback(async () => {
    if (inputNodes.length === 0) {
      return { nodes: [], edges: [] };
    }

    setIsLayouting(true);
    logger.info('Starting dagre layout calculation', {
      nodeCount: inputNodes.length,
      edgeCount: inputEdges.length,
      direction: options.direction
    });

    try {
      // 创建新的 dagre 图
      const dagreGraph = new dagre.graphlib.Graph().setDefaultEdgeLabel(() => ({}));

      // 设置图的方向和其他选项
      const mergedOptions = { ...defaultOptions, ...options };
      dagreGraph.setGraph({
        rankdir: mergedOptions.direction,
        nodesep: mergedOptions.nodeSeparation,
        ranksep: mergedOptions.rankSeparation,
        edgesep: mergedOptions.edgeSeparation,
        marginx: mergedOptions.marginX,
        marginy: mergedOptions.marginY,
        align: mergedOptions.align,
        ranker: mergedOptions.ranker,
      });

      // 分离语言节点和页面节点
      const languageNodes = inputNodes.filter(node => node.data.isLanguageNode);
      const pageNodes = inputNodes.filter(node => !node.data.isLanguageNode);

      // 为每个节点设置尺寸
      inputNodes.forEach(node => {
        // 语言节点和页面节点使用不同的尺寸
        const width = node.data.isLanguageNode ? 180 : (mergedOptions.nodeWidth || 420);
        const height = node.data.isLanguageNode ? 80 : (mergedOptions.nodeHeight || 600);

        dagreGraph.setNode(node.id, { width, height });
      });

      // 添加所有边
      inputEdges.forEach(edge => {
        dagreGraph.setEdge(edge.source, edge.target);
      });

      // 运行 dagre 布局算法
      dagre.layout(dagreGraph);

      // 更新节点位置
      const isHorizontal = mergedOptions.direction === 'LR' || mergedOptions.direction === 'RL';

      // 首先处理所有非语言节点
      const processedNodes = new Map<string, PageNode>();
      const languageChildrenMap = new Map<string, string[]>();

      // 第一步：处理所有页面节点，并收集语言节点的子节点信息
      inputNodes.forEach(node => {
        if (!node.data.isLanguageNode) {
          const nodeWithPosition = dagreGraph.node(node.id);

          if (!nodeWithPosition) {
            logger.warn('Node position not found', { nodeId: node.id });
            processedNodes.set(node.id, node);
            return;
          }

          // 获取节点尺寸
          const width = mergedOptions.nodeWidth || 320;
          const height = mergedOptions.nodeHeight || 640;

          // 设置连接点位置 - 始终保持上下连接以保持树形结构
          const targetPosition = Position.Top;
          const sourcePosition = Position.Bottom;

          // 创建更新后的节点
          const updatedNode: PageNode = {
            ...node,
            // 将 dagre 节点位置（中心锚点）转换为 React Flow 节点锚点（左上角）
            position: {
              x: nodeWithPosition.x - width / 2,
              y: nodeWithPosition.y - height / 2,
            },
            targetPosition,
            sourcePosition,
            width,
            height,
          };

          processedNodes.set(node.id, updatedNode);

          // 收集语言节点的子节点信息
          if (node.data.language) {
            const languageId = `language-${node.data.language}`;
            if (!languageChildrenMap.has(languageId)) {
              languageChildrenMap.set(languageId, []);
            }
            languageChildrenMap.get(languageId)!.push(node.id);
          }
        }
      });

      // 第二步：处理语言节点，使其与子节点形成正三角形分布
      inputNodes.forEach(node => {
        if (node.data.isLanguageNode) {
          const nodeWithPosition = dagreGraph.node(node.id);

          if (!nodeWithPosition) {
            logger.warn('Node position not found', { nodeId: node.id });
            processedNodes.set(node.id, node);
            return;
          }

          // 获取节点尺寸
          const width = 180;
          const height = 80;

          // 设置连接点位置 - 始终保持上下连接以保持树形结构
          const targetPosition = Position.Top;
          const sourcePosition = Position.Bottom;

          // 获取该语言节点的所有子节点
          const childrenIds = languageChildrenMap.get(node.id) || [];

          // 如果有子节点，将语言节点放在子节点的中心上方，形成正三角形分布
          let x = nodeWithPosition.x;
          let y = nodeWithPosition.y;

          if (childrenIds.length > 0) {
            // 计算所有子节点的中心位置
            let totalX = 0;
            let minY = Infinity;

            childrenIds.forEach(childId => {
              const childNode = processedNodes.get(childId);
              if (childNode && childNode.position) {
                totalX += childNode.position.x + (childNode.width || 0) / 2;
                minY = Math.min(minY, childNode.position.y);
              }
            });

            // 将语言节点放在子节点的中心上方
            if (childrenIds.length > 0) {
              x = totalX / childrenIds.length;
              // 放在子节点上方一定距离
              y = minY - height - 40;
            }
          }

          // 创建更新后的节点
          const updatedNode: PageNode = {
            ...node,
            position: {
              x: x - width / 2,
              y: y - height / 2,
            },
            targetPosition,
            sourcePosition,
            width,
            height,
          };

          processedNodes.set(node.id, updatedNode);
        }
      });

      // 将处理后的节点转换为数组
      const updatedNodes = Array.from(processedNodes.values());

      setNodes(updatedNodes);
      setEdges(inputEdges);
      logger.info('Dagre layout calculation completed', {
        nodeCount: updatedNodes.length
      });
      return { nodes: updatedNodes, edges: inputEdges };
    } catch (error) {
      logger.error('Error calculating dagre layout', { error });
      return { nodes: inputNodes, edges: inputEdges };
    } finally {
      setIsLayouting(false);
    }
  }, [inputNodes, inputEdges, options]);

  // 当输入节点、边或选项变化时重新计算布局
  useEffect(() => {
    calculateLayout();
  }, [calculateLayout, ...dependencies]);

  return {
    nodes,
    edges,
    isLayouting,
    calculateLayout,
  };
}
