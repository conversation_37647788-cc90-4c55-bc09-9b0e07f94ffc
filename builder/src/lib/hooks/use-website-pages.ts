import { useQuery } from '@tanstack/react-query';
import { websiteKeys } from '../api/queryKeys';
import { logger } from '@/utils/logger';
import { PageNode, PageEdge, PageStructure } from '@/types/flow';
import { useFetchPages } from './use-page-query';
import { useFetchWebsite } from './use-website';

// 配置日志命名空间
logger.configure({ namespace: 'useWebsitePages' });

// 语言名称映射
const languageNames: Record<string, string> = {
  'EN': 'English',
  'CN': '简体中文',
  'ZH': '繁體中文',
  'ES': 'Español',
  'HI': 'हिन्दी',
  'FR': 'Français',
  'DE': 'Deutsch',
  'RU': 'Русский',
  'PT': 'Português',
  'AR': 'العربية',
  'JP': '日本語',
  'KR': '한국어',
  'IT': 'Italiano',
  'TR': 'Türkçe',
  'PL': 'Polski',
  'NL': 'Nederlands',
  'ID': 'Bahasa Indonesia',
  'TH': 'ไทย',
  'VI': 'Tiếng Việt',
  'SV': 'Svenska',
};

/**
 * Hook to fetch and transform website pages into a graph structure
 */
export function useWebsitePages(websiteId?: string, layoutType: 'tree' | 'grid' = 'tree') {
  const { data: pages, isLoading, error } = useFetchPages(websiteId);
  const { data: website } = useFetchWebsite(websiteId);
  
  return useQuery({
    queryKey: websiteKeys.pages(websiteId, layoutType),
    queryFn: () => {
      if (!pages) return { nodes: [], edges: [] };
      
      logger.info('Transforming pages to structure', { 
        pageCount: pages.length, 
        defaultLanguage: website?.defaultLanguage,
        layoutType
      });
      
      return transformPagesToStructure(
        pages, 
        website?.defaultLanguage || 'EN',
        layoutType,
        websiteId
      );
    },
    enabled: !!pages,
  });
}

/**
 * Transform the list of pages into nodes and edges for the graph
 */
function transformPagesToStructure(
  pages: any[], 
  defaultLanguage: string, 
  layoutType: 'tree' | 'grid' = 'tree',
  websiteId: string = ''
): PageStructure {
  if (!pages || pages.length === 0) {
    return { nodes: [], edges: [] };
  }
  
  const nodes: PageNode[] = [];
  const edges: PageEdge[] = [];
  
  // Group pages by language
  const pagesByLanguage: Record<string, any[]> = {};
  
  pages.forEach(page => {
    const language = page.language || defaultLanguage;
    if (!pagesByLanguage[language]) {
      pagesByLanguage[language] = [];
    }
    pagesByLanguage[language].push(page);
  });
  
  // Get languages and sort them (default language first, then alphabetically)
  const languages = Object.keys(pagesByLanguage).sort((a, b) => {
    if (a === defaultLanguage) return -1;
    if (b === defaultLanguage) return 1;
    return a.localeCompare(b);
  });
  
  // 构建页面的父子关系树
  const buildPageTree = (pages: any[]) => {
    const pageMap: Record<string, any> = {};
    const rootPages: any[] = [];
    
    // 首先创建页面映射
    pages.forEach(page => {
      pageMap[page.id] = { ...page, children: [] };
    });
    
    // 构建树结构
    pages.forEach(page => {
      if (page.parentId && pageMap[page.parentId]) {
        pageMap[page.parentId].children.push(pageMap[page.id]);
      } else {
        rootPages.push(pageMap[page.id]);
      }
    });
    
    return { pageMap, rootPages };
  };
  
  // 递归计算树形布局的节点位置
  const calculateTreePositions = (
    node: any, 
    x: number, 
    y: number, 
    level: number,
    horizontalSpacing: number,
    verticalSpacing: number
  ): { node: any, width: number } => {
    // 节点的最小宽度 - 增加到与卡片宽度匹配
    const minNodeWidth = 384; // 对应 w-96 (24rem = 384px)
    
    // 如果没有子节点，返回最小宽度
    if (!node.children || node.children.length === 0) {
      node.x = x + minNodeWidth / 2;
      node.y = y;
      return { node, width: minNodeWidth };
    }
    
    // 先计算所有子节点的位置和宽度
    let childrenTotalWidth = 0;
    const childrenInfo = node.children.map((child: any) => {
      const childY = y + verticalSpacing;
      const childInfo = calculateTreePositions(
        child,
        x + childrenTotalWidth,
        childY,
        level + 1,
        horizontalSpacing,
        verticalSpacing
      );
      
      childrenTotalWidth += childInfo.width;
      return childInfo;
    });
    
    // 确保节点宽度至少为最小宽度
    const nodeWidth = Math.max(childrenTotalWidth, minNodeWidth);
    
    // 如果子节点总宽度小于最小宽度，需要重新调整子节点位置
    if (childrenTotalWidth < minNodeWidth) {
      // 计算每个子节点需要额外移动的距离
      const extraSpace = (minNodeWidth - childrenTotalWidth) / node.children.length;
      
      // 重新调整子节点位置
      let currentX = x;
      node.children.forEach((child: any, index: number) => {
        const childWidth = childrenInfo[index].width;
        child.x = currentX + childWidth / 2 + extraSpace * index;
        currentX += childWidth + extraSpace;
      });
    }
    
    // 更新当前节点位置为子节点的中心
    node.x = x + nodeWidth / 2;
    node.y = y;
    
    return { node, width: nodeWidth };
  };
  
  // 计算网格布局的节点位置
  const calculateGridPositions = (
    pages: any[],
    startX: number,
    startY: number,
    columns: number = 3,
    horizontalSpacing: number = 300,
    verticalSpacing: number = 350
  ) => {
    // 节点宽度约为 250px，确保有足够的水平间距
    const nodeWidth = 250;
    const nodeHeight = 300;
    
    // 确保水平间距至少比节点宽度大 50px
    const actualHorizontalSpacing = Math.max(horizontalSpacing, nodeWidth + 50);
    
    // 确保垂直间距至少比节点高度大 50px
    const actualVerticalSpacing = Math.max(verticalSpacing, nodeHeight + 50);
    
    pages.forEach((page, index) => {
      const col = index % columns;
      const row = Math.floor(index / columns);
      
      page.x = startX + col * actualHorizontalSpacing;
      page.y = startY + row * actualVerticalSpacing;
    });
  };
  
  // 为每种语言创建节点和边
  languages.forEach((language, languageIndex) => {
    const languagePages = pagesByLanguage[language];
    const displayLanguage = languageNames[language] || language;
    
    // 计算语言组的起始位置 - 增加语言组之间的间距
    const languageStartX = languageIndex * 2000; // 进一步增加语言组之间的水平间距
    const languageStartY = 100;
    
    // 创建语言分组节点
    const languageNode: PageNode = {
      id: `language-${language}`,
      type: 'languageGroup', // 确保节点类型与注册的类型匹配
      position: { 
        x: languageStartX, 
        y: languageStartY 
      },
      data: {
        id: `language-${language}`,
        title: languageNames[language] || language,
        slug: '',
        pageType: 'language',
        status: 'published',
        previewUrl: '',
        language,
        websiteId: websiteId || '',
        isLanguageNode: true
      },
      // 确保节点有明确的尺寸
      width: 300,
      height: 150,
      style: {
        width: 300,
        height: 150,
        padding: '20px',
        borderRadius: '8px',
        backgroundColor: 'rgba(99, 102, 241, 0.1)',
        border: '1px solid rgba(99, 102, 241, 0.3)',
      }
    };
    
    nodes.push(languageNode);
    
    // 构建该语言下的页面树
    const { pageMap, rootPages } = buildPageTree(languagePages);
    
    // 根据布局类型应用不同的布局算法
    if (layoutType === 'tree') {
      // 树形布局
      const horizontalSpacing = 450; // 进一步增加水平间距
      const verticalSpacing = 600;   // 进一步增加垂直间距
      
      // 为根页面计算位置
      let totalWidth = 0;
      
      // 计算每个根页面的布局，并确保它们之间有足够的间距
      rootPages.forEach(rootPage => {
        const { width } = calculateTreePositions(
          rootPage,
          languageStartX + totalWidth + 150, // 增加额外的起始偏移
          languageStartY + verticalSpacing + 50, // 添加额外的垂直偏移
          0,
          horizontalSpacing,
          verticalSpacing
        );
        
        // 在根页面之间添加额外的间距
        totalWidth += width + 200; // 增加根页面之间的间距
      });
    } else {
      // 网格布局 - 增加列数和间距
      calculateGridPositions(
        languagePages,
        languageStartX + 100, // 增加额外的起始偏移
        languageStartY + 300, // 增加语言节点和页面之间的垂直间距
        2, // 减少列数，使页面更大
        500, // 增加水平间距
        700  // 进一步增加垂直间距
      );
    }
    
    // 为每个页面创建节点
    const createPageNode = (page: any, position: { x: number, y: number }): PageNode => {
      return {
        id: page.id,
        type: 'pageNode', // 确保节点类型与注册的类型匹配
        position, // 设置节点位置
        data: {
          id: page.id,
          title: page.title,
          slug: page.slug,
          pageType: page.pageType,
          status: page.status,
          previewUrl: generatePreviewUrl(page.slug, page.language, defaultLanguage),
          updatedAt: page.updatedAt,
          language: page.language || defaultLanguage,
          websiteId: websiteId,
          pageId: page.id,
        },
        // 确保节点有明确的尺寸
        width: 240,
        height: 120,
      };
    };
    
    const pageNodes: PageNode[] = languagePages.map((page: any) => {
      // 生成预览URL
      const previewUrl = generatePreviewUrl(page.slug, page.language, defaultLanguage);
      
      // 获取页面在 pageMap 中的位置信息
      const pageInMap = pageMap[page.id];
      const position = pageInMap ? { x: pageInMap.x, y: pageInMap.y } : { x: 0, y: 0 };
      
      return createPageNode(page, position);
    });
    
    nodes.push(...pageNodes);
    
    // 连接页面到语言节点
    languagePages.forEach(page => {
      const pageNode = pageNodes.find(node => node.id === page.id);
      if (pageNode) {
        const languageEdge: PageEdge = {
          id: `edge-language-${language}-to-${page.id}`,
          source: languageNode.id,
          target: page.id,
          type: 'smoothstep',
          data: {
            type: 'language'
          }
        };
        
        edges.push(languageEdge);
      }
    });
    
    // 连接页面到父页面
    languagePages.forEach(page => {
      if (page.parentId && pageMap[page.parentId]) {
        const navigationEdge: PageEdge = {
          id: `edge-${page.parentId}-${page.id}`,
          source: page.parentId,
          target: page.id,
          type: 'smoothstep',
          data: {
            type: 'navigation'
          }
        };
        
        edges.push(navigationEdge);
      }
    });
  });
  
  return { nodes, edges };
}

// 生成预览 URL
function generatePreviewUrl(slug: string, language: string, defaultLanguage: string): string {
  // 基础URL - 在生产环境中应该使用环境变量
  const baseUrl = process.env.NEXT_PUBLIC_PREVIEW_URL || 'http://localhost:3001';
  let previewPath = '';
  
  // 根据语言生成路径
  if (language === defaultLanguage) {
    previewPath = `/${slug}`;
  } else {
    const langCode = language.toLowerCase();
    previewPath = `/${langCode}${slug}`;
  }
  
  return `${baseUrl}${previewPath}`;
}
