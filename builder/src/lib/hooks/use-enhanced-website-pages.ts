import { useCallback, useMemo, useState, useEffect } from 'react';
import { useWebsitePages } from './use-website-pages';
import { useDagreLayout, DagreLayoutOptions } from './use-dagre-layout';
import { PageStructure } from '@/types/flow';
import { logger } from '@/lib/utils/logger';

// 配置日志命名空间
logger.configure({ namespace: 'useEnhancedWebsitePages' });

// 语言名称映射
export const languageNames: Record<string, string> = {
  'EN': 'English',
  'CN': '简体中文',
  'ZH': '繁體中文',
  'ES': 'Español',
  'HI': 'हिन्दी',
  'FR': 'Français',
  'DE': 'Deutsch',
  'RU': 'Русский',
  'PT': 'Português',
  'AR': 'العربية',
  'JP': '日本語',
  'KR': '한국어',
  'IT': 'Italiano',
  'TR': 'Türkçe',
  'PL': 'Polski',
  'NL': 'Nederlands',
  'ID': 'Bahasa Indonesia',
  'TH': 'ไทย',
  'VI': 'Tiếng Việt',
  'SV': 'Svenska',
};

export interface EnhancedWebsitePagesOptions {
  layoutType?: 'tree' | 'grid';
  dagreOptions?: DagreLayoutOptions;
  initialSelectedLanguages?: string[];
}

const defaultOptions: EnhancedWebsitePagesOptions = {
  layoutType: 'tree',
  dagreOptions: {
    direction: 'TB',
    nodeWidth: 420,
    nodeHeight: 600,
    rankSeparation: 200,
    nodeSeparation: 80,
    edgeSeparation: 40,
    marginX: 40,
    marginY: 40,
    ranker: 'network-simplex'
  },
  initialSelectedLanguages: [],
};

/**
 * Enhanced hook for website pages with ELK layout and filtering
 */
export function useEnhancedWebsitePages(
  websiteId?: string,
  options: EnhancedWebsitePagesOptions = defaultOptions
) {
  const mergedOptions = { ...defaultOptions, ...options };
  const { layoutType = 'tree', dagreOptions, initialSelectedLanguages = [] } = mergedOptions;

  // 获取原始页面数据
  const { data: pageStructure, isLoading: isLoadingPages, error } = useWebsitePages(websiteId, layoutType);

  // 语言筛选状态
  const [selectedLanguages, setSelectedLanguages] = useState<string[]>(initialSelectedLanguages);

  // 布局选项状态
  const [currentDagreOptions, setCurrentDagreOptions] = useState<DagreLayoutOptions>(dagreOptions || defaultOptions.dagreOptions!);

  // 当 websiteId 变化时重置选中的语言
  useEffect(() => {
    logger.info('Website ID changed, resetting selected languages', { websiteId });
    setSelectedLanguages([]);
  }, [websiteId]);

  // 从页面结构中提取所有可用语言
  const availableLanguages = useMemo(() => {
    if (!pageStructure?.nodes) return [];

    const languages = new Set<string>();
    pageStructure.nodes.forEach(node => {
      if (node.data.language) {
        languages.add(node.data.language);
      }
    });

    // 按照语言名称映射排序（英语优先）
    return Array.from(languages).sort((a, b) => {
      if (a === 'EN') return -1;
      if (b === 'EN') return 1;
      return (languageNames[a] || a).localeCompare(languageNames[b] || b);
    });
  }, [pageStructure?.nodes]);

  // 如果没有选择语言，默认选择所有语言
  useEffect(() => {
    if (selectedLanguages.length === 0 && availableLanguages.length > 0) {
      logger.info('Auto-selecting all available languages', { availableLanguages });
      setSelectedLanguages(availableLanguages);
    }
  }, [availableLanguages, selectedLanguages]);

  // 根据选中的语言筛选节点和边
  const filteredStructure = useMemo((): PageStructure => {
    if (!pageStructure) return { nodes: [], edges: [] };

    // 如果没有选择语言，返回所有节点
    if (selectedLanguages.length === 0) {
      return pageStructure;
    }

    logger.info('Filtering nodes and edges by selected languages', {
      selectedLanguages,
      totalNodes: pageStructure.nodes.length,
      totalEdges: pageStructure.edges.length
    });

    // 筛选节点
    const filteredNodes = pageStructure.nodes.filter(node => {
      // 保留语言节点
      if (node.data.isLanguageNode) {
        // 从节点ID中提取语言代码（格式为 language-XX）
        const nodeLanguage = node.data.language || node.id.replace('language-', '');
        return selectedLanguages.includes(nodeLanguage);
      }

      // 保留选中语言的页面节点
      return node.data.language ? selectedLanguages.includes(node.data.language) : true;
    });

    // 获取筛选后节点的 ID 列表
    const nodeIds = new Set(filteredNodes.map(node => node.id));

    // 筛选边（只保留连接筛选后节点的边）
    const filteredEdges = pageStructure.edges.filter(edge =>
      nodeIds.has(edge.source) && nodeIds.has(edge.target)
    );

    logger.info('Filtering results', {
      filteredNodes: filteredNodes.length,
      filteredEdges: filteredEdges.length
    });

    return { nodes: filteredNodes, edges: filteredEdges };
  }, [pageStructure, selectedLanguages]);

  // 应用 dagre 布局
  const { nodes, edges, isLayouting, calculateLayout } = useDagreLayout(
    filteredStructure.nodes,
    filteredStructure.edges,
    currentDagreOptions,
    [filteredStructure, currentDagreOptions]
  );

  // 切换语言选择
  const toggleLanguage = useCallback((language: string) => {
    setSelectedLanguages(prev => {
      if (prev.includes(language)) {
        // 如果当前只有一个语言被选中，不允许取消选择
        if (prev.length === 1) {
          logger.info('At least one language must be selected', { language });
          return prev;
        }

        logger.info('Deselecting language', { language });
        return prev.filter(lang => lang !== language);
      } else {
        logger.info('Selecting language', { language });
        return [...prev, language];
      }
    });
  }, []);

  // 选择所有语言
  const selectAllLanguages = useCallback(() => {
    logger.info('Selecting all languages', { availableLanguages });
    setSelectedLanguages(availableLanguages);
  }, [availableLanguages]);

  // 清除所有语言选择，但保留一个默认语言（英语或第一个可用语言）
  const clearLanguageSelection = useCallback(() => {
    const defaultLanguage = availableLanguages.includes('EN')
      ? 'EN'
      : (availableLanguages[0] || '');

    if (defaultLanguage) {
      logger.info('Clearing language selection, keeping default language', { defaultLanguage });
      setSelectedLanguages([defaultLanguage]);
    } else {
      logger.warn('No available languages, cannot clear selection');
    }
  }, [availableLanguages]);

  // 更新布局选项
  const updateLayoutOptions = useCallback((newOptions: Partial<DagreLayoutOptions>) => {
    logger.info('Updating layout options', { newOptions });

    // 更新布局选项状态
    setCurrentDagreOptions(prev => ({
      ...prev,
      ...newOptions
    }));
  }, []);

  // 手动触发重新计算布局
  const recalculateLayout = useCallback(() => {
    logger.info('Manually recalculating layout');
    calculateLayout();
  }, [calculateLayout]);

  return {
    nodes,
    edges,
    isLoading: isLoadingPages || isLayouting,
    error,
    availableLanguages,
    selectedLanguages,
    toggleLanguage,
    selectAllLanguages,
    clearLanguageSelection,
    updateLayoutOptions,
    calculateLayout: recalculateLayout,
    currentLayoutOptions: currentDagreOptions,
  };
}
