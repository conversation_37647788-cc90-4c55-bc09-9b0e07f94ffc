// hooks/useImageSearch.ts

import { useState, useCallback } from 'react';
import { $get, $post } from '@/lib';

// Export SearchParams interface
export interface SearchParams {
  q?: string;
  lang?: string;
  image_type?: string;
  orientation?: string;
  category?: string;
  min_width?: number;
  min_height?: number;
  colors?: string[];
  editors_choice?: boolean;
  safesearch?: boolean;
  order?: string;
  page?: number;
  per_page?: number;
}

interface PixabayImage {
  id: number;
  pageURL: string;
  type: string;
  tags: string;
  previewURL: string;
  previewWidth: number;
  previewHeight: number;
  webformatURL: string;
  webformatWidth: number;
  webformatHeight: number;
  largeImageURL?: string;
  fullHDURL?: string;
  vectorURL?: string;
  imageURL?: string;
  views: number;
  downloads: number;
  collections: number;
  likes: number;
  comments: number;
  user_id: number;
  user: string;
  userImageURL: string;
}

export interface ImageMetadata {
  id: string;
  url: string;
  previewUrl: string;
  tags: string;
  user: string;
  source: string;
  width: number;
  height: number;
  likes: number;
  downloads: number;
}

interface UseImageSearchReturn {
  images: ImageMetadata[];
  loading: boolean;
  error: string | null;
  total: number;
  totalHits: number;
  currentPage: number;
  searchImages: (params: SearchParams) => Promise<void>;
  loadMore: () => Promise<void>;
  selectAndUploadImage: (image: ImageMetadata) => Promise<void>;
}

export function useImageSearch(
  domain?: string,
  onUploadSuccess?: (result: any, editPos?: any) => void
): UseImageSearchReturn {
  const [images, setImages] = useState<ImageMetadata[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);
  const [totalHits, setTotalHits] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [lastSearchParams, setLastSearchParams] = useState<SearchParams | null>(null);

  const buildSearchUrl = (params: SearchParams): string => {
    const searchParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '' && value !== 'all') {
        if (Array.isArray(value)) {
          if (value.length > 0) {
            searchParams.append(key, value.join(','));
          }
        } else {
          searchParams.append(key, String(value));
        }
      }
    });

    return `/api/v1/images/search?${searchParams.toString()}`;
  };

  const transformImage = (pixabayImage: PixabayImage): ImageMetadata => {
    const getImageUrl = (): string => {
      return pixabayImage.fullHDURL || 
             pixabayImage.largeImageURL || 
             pixabayImage.imageURL || 
             pixabayImage.webformatURL || 
             pixabayImage.vectorURL || 
             pixabayImage.previewURL;
    };

    return {
      id: String(pixabayImage.id),
      url: getImageUrl(),
      previewUrl: pixabayImage.previewURL,
      tags: pixabayImage.tags,
      user: pixabayImage.user,
      source: 'pixabay',
      width: pixabayImage.webformatWidth,
      height: pixabayImage.webformatHeight,
      likes: pixabayImage.likes,
      downloads: pixabayImage.downloads,
    };
  };

  const searchImages = useCallback(async (params: SearchParams) => {
    setLoading(true);
    setError(null);
    setLastSearchParams(params);
    
    try {
      const url = buildSearchUrl({ ...params, page: 1 });
      const result: any = await $get(url);
      
      if (result.success && result.data) {
        const transformedImages = result.data.hits.map(transformImage);
        setImages(transformedImages);
        setTotal(result.data.total || 0);
        setTotalHits(result.data.totalHits || result.data.total || 0);
        setCurrentPage(1);
      } else {
        throw new Error(result.message || 'Failed to fetch images');
      }
    } catch (err: any) {
      console.error('Search error:', err);
      setError(err.message || 'Failed to search images. Please try again.');
      setImages([]);
      setTotal(0);
      setTotalHits(0);
      setCurrentPage(1);
    } finally {
      setLoading(false);
    }
  }, []);

  const loadMore = useCallback(async () => {
    if (loading || images.length >= totalHits || !lastSearchParams) return;
    
    setLoading(true);
    try {
      const nextPage = currentPage + 1;
      const url = buildSearchUrl({ ...lastSearchParams, page: nextPage });
      const result: any = await $get(url);
      
      if (result.success && result.data) {
        const newImages = result.data.hits.map(transformImage);
        setImages(prev => [...prev, ...newImages]);
        setCurrentPage(nextPage);
      } else {
        throw new Error(result.message || 'Failed to load more images');
      }
    } catch (err: any) {
      console.error('Load more error:', err);
      setError(err.message || 'Failed to load more images. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [loading, images.length, totalHits, currentPage, lastSearchParams]);

  const selectAndUploadImage = useCallback(async (image: ImageMetadata) => {
    try {
      // Build upload data according to imgpipe.io API format
      const uploadData = {
        url: image.url,
        key: `pixabay-${image.id}`, // Use image ID as unique identifier
        metadata: {
          id: image.id,
          tags: image.tags,
          user: image.user,
          source: image.source,
          width: image.width,
          height: image.height,
          likes: image.likes,
          downloads: image.downloads,
          uploadedAt: new Date().toISOString(),
        }
      };

      // Use external upload service
      const isDevelopment = process.env.NODE_ENV === 'development';
      const pushService = isDevelopment 
        ? `http://localhost:7001/push/json` 
        : `https://${domain || 'default'}.imgpipe.io/push/json`;

      console.log('Uploading image to:', pushService, uploadData);

      const response = await fetch(pushService, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(uploadData),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Upload failed: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const result = await response.json();
      console.log('Upload result:', result);
      
      // Build return result
      // const uploadResult = {
      //   url: result.result.id || image.url, // Use URL returned by upload service, fallback to original URL if not available
      //   metadata: uploadData.metadata
      // };
      
      if (onUploadSuccess) {
        onUploadSuccess(result);
      }
      
    } catch (err: any) {
      console.error('Upload error:', err);
      throw new Error(err.message || 'Failed to upload image. Please try again.');
    }
  }, [domain, onUploadSuccess]);

  return {
    images,
    loading,
    error,
    total,
    totalHits,
    currentPage,
    searchImages,
    loadMore,
    selectAndUploadImage,
  };
}