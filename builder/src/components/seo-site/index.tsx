"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, Activity, FileText, PieChart } from "lucide-react";
import { Save, Settings, Globe, Bot, Code, Share, Shield, BarChart, Bookmark, Languages } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

// 导入 React Query hooks
import { 
  useWebsiteSeo, 
  useUpdateWebsiteSeo, 
  useUpdateWebsiteMetadata, 
  useUpdateRobotsSitemap, 
  useUpdateSocialDefaults, 
  useUpdateSecurityPerformance, 
  useUpdateStructuredD<PERSON>,
  useUpdateHreflangSettings,
  useUpdateAnalyticsSettings,
  useUpdateBrandSettings
} from "@/lib/hooks/useSeo";

// 导入组件
import SiteSEOOverview from "./components/site-seo-overview";
import GlobalMetaForm from "./components/global-meta-form";
import RobotsSitemapForm from "./components/robots-sitemap-form";
import StructuredDataForm from "./components/structured-data-form";
import SocialMediaDefaultsForm from "./components/social-media-defaults-form";
import HreflangSettingsForm from "./components/hreflang-settings-form";
import SecurityPerformanceForm from "./components/security-performance-form";
import AnalyticsTrackingForm from "./components/analytics-tracking-form";
import BrandIdentityForm from "./components/brand-identity-form";

// 导入骨架屏组件
import { Skeleton } from "@/components/ui/skeleton";

interface SiteSEOPageProps { 
  websiteId: string;
  defaultTab?: string;
  onTabChange?: (tab: string) => void;
}

export default function SiteSEOPage({ 
  websiteId,
  defaultTab = "overview",
  onTabChange
}: SiteSEOPageProps) {
  const [activeTab, setActiveTab] = useState(defaultTab);
  const { toast } = useToast();
  
  // 使用 React Query 获取网站 SEO 数据
  const { 
    data: seoData, 
    isLoading, 
    isError, 
    error,
    refetch 
  } = useWebsiteSeo(websiteId);
  
  // 使用 React Query mutations
  const updateWebsiteSeo = useUpdateWebsiteSeo();
  const updateMetadata = useUpdateWebsiteMetadata();
  const updateRobotsSitemap = useUpdateRobotsSitemap();
  const updateSocialDefaults = useUpdateSocialDefaults();
  const updateSecurityPerformance = useUpdateSecurityPerformance();
  const updateStructuredData = useUpdateStructuredData();
  const updateHreflangSettings = useUpdateHreflangSettings();
  const updateAnalyticsSettings = useUpdateAnalyticsSettings();
  const updateBrandSettings = useUpdateBrandSettings();

  // 处理表单提交
  const handleSave = (section: string, data: any) => {
    console.log(`Saving ${section}:`, data);
    
    // 修复 section 为字符串的情况
    if (typeof data === 'string') {
      console.error(`Invalid data format for section ${section}:`, data);
      toast({
        title: "Error",
        description: "Invalid data format",
        variant: "destructive",
      });
      return;
    }
    
    switch (section) {
      case "metadata":
        updateMetadata.mutate(
          { 
            websiteId, 
            data 
          },
          {
            onSuccess: () => {
              toast({
                title: "Success",
                description: "Metadata has been updated",
              });
            },
            onError: (error) => {
              toast({
                title: "Error",
                description: error.message || "Failed to update metadata",
                variant: "destructive",
              });
            }
          }
        );
        break;
      case "robots-sitemap":
        updateRobotsSitemap.mutate(
          { 
            websiteId, 
            data 
          },
          {
            onSuccess: () => {
              toast({
                title: "Success",
                description: "Robots.txt and sitemap settings have been updated",
              });
            },
            onError: (error) => {
              toast({
                title: "Error",
                description: error.message || "Failed to update robots.txt and sitemap settings",
                variant: "destructive",
              });
            }
          }
        );
        break;
      case "social-defaults":
        updateSocialDefaults.mutate(
          { 
            websiteId, 
            data 
          },
          {
            onSuccess: () => {
              toast({
                title: "Success",
                description: "Social media defaults have been updated",
              });
            },
            onError: (error) => {
              toast({
                title: "Error",
                description: error.message || "Failed to update social media defaults",
                variant: "destructive",
              });
            }
          }
        );
        break;
      case "security-performance":
        updateSecurityPerformance.mutate(
          { 
            websiteId, 
            data 
          },
          {
            onSuccess: () => {
              toast({
                title: "Success",
                description: "Security and performance settings have been updated",
              });
            },
            onError: (error) => {
              toast({
                title: "Error",
                description: error.message || "Failed to update security and performance settings",
                variant: "destructive",
              });
            }
          }
        );
        break;
      case "structured-data":
        updateStructuredData.mutate(
          { 
            websiteId, 
            data 
          },
          {
            onSuccess: () => {
              toast({
                title: "Success",
                description: "Structured data has been updated",
              });
            },
            onError: (error) => {
              toast({
                title: "Error",
                description: error.message || "Failed to update structured data",
                variant: "destructive",
              });
            }
          }
        );
        break;
      case "hreflang-settings":
        updateHreflangSettings.mutate(
          { 
            websiteId, 
            data 
          },
          {
            onSuccess: () => {
              toast({
                title: "Success",
                description: "Hreflang settings have been updated",
              });
            },
            onError: (error: Error) => {
              toast({
                title: "Error",
                description: error.message || "Failed to update hreflang settings",
                variant: "destructive",
              });
            }
          }
        );
        break;
      case "analytics-settings":
        updateAnalyticsSettings.mutate(
          { 
            websiteId, 
            data 
          },
          {
            onSuccess: () => {
              toast({
                title: "Success",
                description: "Analytics settings have been updated",
              });
            },
            onError: (error) => {
              toast({
                title: "Error",
                description: error.message || "Failed to update analytics settings",
                variant: "destructive",
              });
            }
          }
        );
        break;
      case "brand-identity":  
        console.log("Brand settings data (detailed):", JSON.stringify(data));
        // 确保数据格式正确
        if (data && data.brandSettings) {
          updateBrandSettings.mutate(
            { 
              websiteId, 
              data 
            },
            {
              onSuccess: () => {
                toast({
                  title: "Success",
                  description: "Brand settings have been updated",
                });
              },
              onError: (error) => {
                toast({
                  title: "Error",
                  description: error.message || "Failed to update brand settings",
                  variant: "destructive",
                });
                console.error("Brand settings update error:", error);
              }
            }
          );
        } else {
          console.error("Invalid brand settings data format:", data);
          toast({
            title: "Error",
            description: "Invalid brand settings data format",
            variant: "destructive",
          });
        }
        break;
      // 其他部分更新...
      default:
        console.warn(`未知的部分更新类型: ${section}`);
    }
  };

  // 处理加载状态
  if (isLoading) {
    return (
      <div className="container py-6 space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-96 mt-2" />
          </div>
          <Skeleton className="h-10 w-40" />
        </div>
        <Separator />
        <div className="space-y-4">
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-[400px] w-full" />
        </div>
      </div>
    );
  }

  // 处理错误状态
  if (isError) {
    return (
      <div className="container py-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            {error instanceof Error ? error.message : "Failed to load SEO settings"}
            <Button 
              variant="outline" 
              size="sm" 
              className="ml-2"
              onClick={() => refetch()}
            >
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // 格式化最后保存时间
  const formatLastSaved = () => {
    if (!seoData?.updatedAt) return "";
    
    return new Intl.DateTimeFormat("en-US", {
      hour: "numeric",
      minute: "numeric",
      second: "numeric",
      hour12: true,
    }).format(new Date(seoData.updatedAt));
  };

  const isSaving = updateWebsiteSeo.isPending || 
                   updateMetadata.isPending || 
                   updateRobotsSitemap.isPending || 
                   updateSocialDefaults.isPending || 
                   updateSecurityPerformance.isPending || 
                   updateStructuredData.isPending || 
                   updateHreflangSettings.isPending || 
                   updateAnalyticsSettings.isPending || 
                   updateBrandSettings.isPending;

  return (
    <div className="container py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Site SEO Settings</h1>
          <p className="text-muted-foreground mt-1">
            Configure global SEO settings that apply to your entire website
          </p>
        </div>
        {seoData?.updatedAt && (
          <span className="text-sm text-muted-foreground">
            Last saved: {formatLastSaved()}
          </span>
        )}
      </div>

      <Separator />

      <Tabs value={activeTab} onValueChange={(tab) => {
        setActiveTab(tab);
        if (onTabChange) {
          onTabChange(tab);
        }
      }} className="space-y-4">
        <div className="overflow-x-auto">
          <TabsList className="h-auto p-1 w-full grid grid-cols-9 gap-1">
            <TabsTrigger value="overview" className="flex items-center gap-1 py-2">
              <PieChart className="h-4 w-4" />
              <span>Overview</span>
            </TabsTrigger>
            <TabsTrigger value="global-meta" className="flex items-center gap-1 py-2">
              <Globe className="h-4 w-4" />
              <span>Global Meta</span>
            </TabsTrigger>
            <TabsTrigger value="robots-sitemap" className="flex items-center gap-1 py-2">
              <Bot className="h-4 w-4" />
              <span>Robots/Sitemap</span>
            </TabsTrigger>
            <TabsTrigger value="structured-data" className="flex items-center gap-1 py-2">
              <Code className="h-4 w-4" />
              <span>Structured Data</span>
            </TabsTrigger>
            <TabsTrigger value="social-defaults" className="flex items-center gap-1 py-2">
              <Share className="h-4 w-4" />
              <span>Social Media</span>
            </TabsTrigger>
            <TabsTrigger value="hreflang" className="flex items-center gap-1 py-2">
              <Languages className="h-4 w-4" />
              <span>Hreflang</span>
            </TabsTrigger>
            <TabsTrigger value="security-performance" className="flex items-center gap-1 py-2">
              <Shield className="h-4 w-4" />
              <span>Security</span>
            </TabsTrigger>
            <TabsTrigger value="analytics" className="flex items-center gap-1 py-2">
              <Activity className="h-4 w-4" />
              <span>Analytics</span>
            </TabsTrigger>
            <TabsTrigger value="brand-identity" className="flex items-center gap-1 py-2">
              <Bookmark className="h-4 w-4" />
              <span>Brand</span>
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="overview" className="space-y-4">
          <SiteSEOOverview seoData={seoData || null} />
          
          <Card>
            <CardHeader>
              <CardTitle>Getting Started with Site SEO</CardTitle>
              <CardDescription>
                Follow these steps to optimize your website&apos;s SEO
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert className="bg-primary/10 text-primary border-primary/20">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>What are Site SEO Settings?</AlertTitle>
                <AlertDescription>
                  Site SEO settings apply globally to your entire website, while page-level SEO settings apply to individual pages.
                  Configure these settings to establish a strong SEO foundation for your website.
                </AlertDescription>
              </Alert>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="border border-border rounded-md p-4 space-y-2">
                  <h3 className="font-medium flex items-center">
                    <Settings className="h-4 w-4 mr-2" />
                    Step 1: Configure Basic Settings
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    Set up your global metadata, including site name, description, and title templates.
                  </p>
                  <Button variant="outline" size="sm" onClick={() => setActiveTab("global-meta")}>
                    Go to Global Meta
                  </Button>
                </div>
                
                <div className="border border-border rounded-md p-4 space-y-2">
                  <h3 className="font-medium flex items-center">
                    <FileText className="h-4 w-4 mr-2" />
                    Step 2: Configure Robots
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    Set up robots.txt and sitemap.xml to control how search engines crawl your site.
                  </p>
                  <Button variant="outline" size="sm" onClick={() => setActiveTab("robots-sitemap")}>
                    Go to Robots
                  </Button>
                </div>
                
                <div className="border border-border rounded-md p-4 space-y-2">
                  <h3 className="font-medium flex items-center">
                    <Code className="h-4 w-4 mr-2" />
                    Step 3: Add Structured Data
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    Implement structured data to help search engines understand your content better.
                  </p>
                  <Button variant="outline" size="sm" onClick={() => setActiveTab("structured-data")}>
                    Go to Structured Data
                  </Button>
                </div>
                
                <div className="border border-border rounded-md p-4 space-y-2">
                  <h3 className="font-medium flex items-center">
                    <Share className="h-4 w-4 mr-2" />
                    Step 4: Set Social Media Defaults
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    Configure how your content appears when shared on social media platforms.
                  </p>
                  <Button variant="outline" size="sm" onClick={() => setActiveTab("social-defaults")}>
                    Go to Social Defaults
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="global-meta">
          <Card>
            <CardHeader>
              <CardTitle>Global Metadata Settings</CardTitle>
              <CardDescription>
                Configure global metadata settings that apply to your entire website
              </CardDescription>
            </CardHeader>
            <CardContent>
              <GlobalMetaForm 
                initialData={{
                  siteName: seoData?.siteName,
                  siteDescription: seoData?.siteDescription,
                  titleTemplate: seoData?.titleTemplate,
                  defaultDescription: seoData?.defaultDescription,
                  globalKeywords: seoData?.globalKeywords,
                }} 
                onSave={(data) => handleSave("metadata", data)} 
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="robots-sitemap">
          <Card>
            <CardHeader>
              <CardTitle>Robots.txt & Sitemap</CardTitle>
              <CardDescription>
                Configure robots.txt and sitemap settings for search engines
              </CardDescription>
            </CardHeader>
            <CardContent>
              <RobotsSitemapForm 
                initialData={{
                  robotsTxt: seoData?.robotsTxt,
                  sitemapSettings: seoData?.sitemapSettings,
                }} 
                onSave={(data) => handleSave("robots-sitemap", data)} 
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="structured-data">
          <Card>
            <CardHeader>
              <CardTitle>Structured Data</CardTitle>
              <CardDescription>
                Configure structured data for better search engine understanding
              </CardDescription>
            </CardHeader>
            <CardContent>
              <StructuredDataForm 
                initialData={{
                  organizationSchema: seoData?.organizationSchema,
                  websiteSchema: seoData?.websiteSchema,
                }} 
                onSave={(data) => handleSave("structured-data", data)} 
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="social-defaults">
          <Card>
            <CardHeader>
              <CardTitle>Social Media Defaults</CardTitle>
              <CardDescription>
                Configure default settings for social media sharing
              </CardDescription>
            </CardHeader>
            <CardContent>
              <SocialMediaDefaultsForm 
                initialData={{
                  defaultOgImage: seoData?.defaultOgImage,
                  ogTitleTemplate: seoData?.ogTitleTemplate,
                  ogDescriptionTemplate: seoData?.ogDescriptionTemplate,
                  defaultXCard: seoData?.defaultXCard,
                  xUsername: seoData?.xUsername,
                }} 
                onSave={(data) => handleSave("social-defaults", data)} 
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="hreflang">
          <Card>
            <CardHeader>
              <CardTitle>Hreflang Settings</CardTitle>
              <CardDescription>
                Configure hreflang settings for international targeting
              </CardDescription>
            </CardHeader>
            <CardContent>
              <HreflangSettingsForm 
                initialData={seoData} 
                onSave={(data) => handleSave("hreflang-settings", data)}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security-performance">
          <Card>
            <CardHeader>
              <CardTitle>Security & Performance</CardTitle>
              <CardDescription>
                Configure security and performance settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <SecurityPerformanceForm 
                initialData={{
                  forceHttps: seoData?.forceHttps,
                  httpToHttpsRedirect: seoData?.httpToHttpsRedirect,
                  cacheControl: seoData?.cacheControl,
                  resourceCompression: seoData?.resourceCompression,
                  browserCaching: seoData?.browserCaching,
                  lazyLoadingImages: seoData?.lazyLoadingImages,
                }} 
                onSave={(data) => handleSave("security-performance", data)} 
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics">
          <Card>
            <CardHeader>
              <CardTitle>Analytics & Tracking</CardTitle>
              <CardDescription>
                Configure analytics and tracking settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AnalyticsTrackingForm 
                initialData={{
                  analyticsSettings: seoData?.analyticsSettings,
                }} 
                onSave={(data) => handleSave("analytics-settings", data)} 
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="brand-identity">
          <Card>
            <CardHeader>
              <CardTitle>Brand Identity</CardTitle>
              <CardDescription>
                Configure brand identity settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <BrandIdentityForm 
                initialData={seoData || undefined}
                onSave={(section, data) => {
                  console.log("BrandIdentityForm onSave called with:", section, data);
                  handleSave(section, data);
                }}
                isSaving={updateBrandSettings.isPending}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
