// import { SiteSEOData } from "../types";

// export const mockSiteSEOData: SiteSEOData = {
//   // 网站基本信息
//   siteName: "Example Website",
//   siteDescription: "A comprehensive example website for demonstration",
  
//   // 全局元数据
//   titleTemplate: "%s | Example Website",
//   defaultDescription: "Default description for pages without specific descriptions",
//   globalKeywords: ["example", "website", "demo"],
  
//   // Robots 和 Sitemap
//   robotsTxt: "User-agent: *\nAllow: /\nDisallow: /admin/\nSitemap: https://example.com/sitemap.xml",
//   sitemapSettings: {
//     enabled: true,
//     autoGenerate: true,
//     changeFrequency: "daily",
//     lastGenerated: "2025-04-26T10:00:00Z",
//     dynamicRoutes: [
//       {
//         pattern: "/blog/:slug",
//         priority: 0.8,
//         changeFrequency: "weekly"
//       },
//       {
//         pattern: "/products/:id",
//         priority: 0.9,
//         changeFrequency: "daily"
//       }
//     ],
//     excludedPatterns: ["/admin/*", "/private/*", "/api/*"],
//     includeImages: true,
//     alternateLanguagePages: true,
//     defaultPriority: 0.5,
//     defaultChangeFrequency: "monthly",
//     cacheExpiration: 3600,
//     pingSearchEngines: true,
//     searchEnginesToPing: ["google", "bing", "baidu"]
//   },
  
//   // 结构化数据
//   organizationSchema: {
//     name: "Example Organization",
//     logo: "https://example.com/logo.png",
//     url: "https://example.com",
//     contactPoint: {
//       telephone: "******-123-4567",
//       contactType: "customer service"
//     },
//     sameAs: [
//       "https://facebook.com/exampleorg",
//       "https://twitter.com/exampleorg",
//       "https://linkedin.com/company/exampleorg"
//     ]
//   },
//   websiteSchema: {
//     "@context": "https://schema.org",
//     "@type": "WebSite",
//     "name": "Example Website",
//     "url": "https://example.com",
//     "potentialAction": {
//       "@type": "SearchAction",
//       "target": "https://example.com/search?q={search_term_string}",
//       "query-input": "required name=search_term_string"
//     }
//   },
  
//   // 社交媒体默认设置
//   socialDefaults: {
//     ogImage: "https://example.com/default-og-image.jpg",
//     ogTitleTemplate: "%s - Example Website",
//     ogDescriptionTemplate: "Check out %s on our website",
//     xCard: "summary_large_image",
//     xUsername: "@examplewebsite"
//   },
  
//   // 多语言设置
//   hreflangSettings: {
//     defaultLanguage: "en",
//     supportedLanguages: [
//       { code: "en", region: "us", url: "https://example.com" },
//       { code: "es", region: "", url: "https://example.com/es" },
//       { code: "fr", region: "ca", url: "https://example.com/fr-ca" }
//     ]
//   },
  
//   // 性能与安全
//   performanceAndSecurity: {
//     forceHttps: true,
//     httpToHttpsRedirect: true,
//     cacheControl: "public, max-age=3600",
//     resourceCompression: true,
//     browserCaching: true,
//     lazyLoadingImages: true
//   },
  
//   // 分析和跟踪
//   analyticsSettings: {
//     googleAnalyticsId: "UA-123456789-1",
//     googleTagManagerId: "GTM-ABCDEF",
//     searchConsoleVerification: "google-site-verification=abcdefghijklmnopqrstuvwxyz",
//     customHeadScripts: "<script>console.log('Custom tracking script loaded');</script>"
//   },
  
//   // 品牌身份
//   brandSettings: {
//     brandName: "Example Corporation",
//     brandTagline: "Innovation for Everyone",
//     brandDescription: "Example Corporation is a leading provider of innovative solutions for businesses and consumers worldwide.",
//     brandLogoUrl: "https://example.com/logo.svg",
//     brandFaviconUrl: "https://example.com/favicon.ico",
//     brandColors: {
//       primary: "#4F46E5",
//       secondary: "#10B981"
//     },
//     socialProfiles: [
//       { platform: "Facebook", url: "https://facebook.com/examplecorp" },
//       { platform: "Twitter", url: "https://twitter.com/examplecorp" },
//       { platform: "LinkedIn", url: "https://linkedin.com/company/examplecorp" }
//     ]
//   },
  
//   // SEO 评分
//   seoScores: {
//     overall: 78,
//     categories: {
//       technical: 85,
//       content: 72,
//       security: 90,
//       performance: 65,
//       social: 80
//     },
//     issues: {
//       critical: 1,
//       warnings: 4,
//       passed: 23
//     }
//   }
// };
