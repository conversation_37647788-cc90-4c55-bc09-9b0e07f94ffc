// 匹配服务端 WebsiteSEO 模型的前端类型定义
export interface SiteSEOData {
  id?: string;
  websiteId?: string;
  
  // 全局元数据
  siteName?: string;
  siteDescription?: string;
  titleTemplate?: string;
  defaultDescription?: string;
  globalKeywords?: string[];
  
  // Robots 和 Sitemap 设置
  robotsTxt?: string;
  sitemapSettings?: {
    enabled?: boolean;
    autoGenerate?: boolean;
    changeFrequency?: "ALWAYS" | "HOURLY" | "DAILY" | "WEEKLY" | "MONTHLY" | "YEARLY" | "NEVER";
    lastGenerated?: string;
    dynamicRoutes?: Array<{
      pattern: string;
      priority: number;
      changeFrequency: "ALWAYS" | "HOURLY" | "DAILY" | "WEEKLY" | "MONTHLY" | "YEARLY" | "NEVER";
    }>;
    excludedPatterns?: string[];
    includeImages?: boolean;
    alternateLanguagePages?: boolean;
    defaultPriority?: number;
    defaultChangeFrequency?: "ALWAYS" | "HOURLY" | "DAILY" | "WEEKLY" | "MONTHLY" | "YEARLY" | "NEVER";
    cacheExpiration?: number;
    pingSearchEngines?: boolean;
    searchEnginesToPing?: string[];
  };
  
  // 结构化数据
  organizationSchema?: any;
  websiteSchema?: any;
  
  // 社交媒体默认设置
  defaultOgImage?: string;
  ogTitleTemplate?: string;
  ogDescriptionTemplate?: string;
  defaultXCard?: "SUMMARY" | "SUMMARY_LARGE_IMAGE" | "APP" | "PLAYER";
  xUsername?: string;
  
  // 多语言设置
  hreflangSettings?: {
    defaultLanguage?: string;
    supportedLanguages?: Array<{
      code: string;
      region?: string;
      url: string;
    }>;
  };
  
  // 安全和性能设置
  forceHttps?: boolean;
  httpToHttpsRedirect?: boolean;
  cacheControl?: string;
  resourceCompression?: boolean;
  browserCaching?: boolean;
  lazyLoadingImages?: boolean;
  
  // 分析跟踪
  analyticsSettings?: {
    googleAnalyticsId?: string;
    googleTagManagerId?: string;
    searchConsoleVerification?: string;
    customHeadScripts?: string;
    otherAnalyticsTools?: Array<{
      name: string;
      scriptUrl?: string;
      scriptContent?: string;
      position?: "head" | "body";
    }>;
  };
  
  // 品牌标识
  brandSettings?: {
    brandName?: string;
    brandTagline?: string;
    brandDescription?: string;
    brandLogoUrl?: string;
    brandFaviconUrl?: string;
    brandColors?: {
      primary?: string;
      secondary?: string;
      accent?: string;
    };
    socialProfiles?: Array<{
      platform: string;
      url: string;
    }>;
  };
  
  // SEO 评分
  seoScores?: {
    overall?: number;
    categories?: {
      content?: number;
      technical?: number;
      onPage?: number;
      offPage?: number;
      user?: number;
    };
    lastUpdated?: string;
    issues?: {
      critical?: number;
      warnings?: number;
      passed?: number;
    };
  };
  
  createdAt?: string;
  updatedAt?: string;
}
