"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { SiteSEOData } from "../types";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Switch } from "@/components/ui/switch";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { InfoCircledIcon } from "@radix-ui/react-icons";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Zap, Shield, Clock, ExternalLink, Save, Link2, CheckCircle2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { useState } from "react";

const securityPerformanceFormSchema = z.object({
  forceHttps: z.boolean(),
  httpToHttpsRedirect: z.boolean(),
  cacheControl: z.string().optional(),
  resourceCompression: z.boolean(),
  browserCaching: z.boolean(),
  lazyLoadingImages: z.boolean(),
});

type SecurityPerformanceFormValues = z.infer<typeof securityPerformanceFormSchema>;

interface SecurityPerformanceFormProps {
  initialData?: {
    forceHttps?: boolean;
    httpToHttpsRedirect?: boolean;
    cacheControl?: string;
    resourceCompression?: boolean;
    browserCaching?: boolean;
    lazyLoadingImages?: boolean;
  };
  onSave?: (data: Partial<SiteSEOData>) => void;
}

export default function SecurityPerformanceForm({ initialData, onSave }: SecurityPerformanceFormProps) {
  const [isSaving, setIsSaving] = useState(false);
  
  const form = useForm<SecurityPerformanceFormValues>({
    resolver: zodResolver(securityPerformanceFormSchema),
    defaultValues: {
      forceHttps: initialData?.forceHttps ?? true,
      httpToHttpsRedirect: initialData?.httpToHttpsRedirect ?? true,
      cacheControl: initialData?.cacheControl ?? "",
      resourceCompression: initialData?.resourceCompression ?? true,
      browserCaching: initialData?.browserCaching ?? true,
      lazyLoadingImages: initialData?.lazyLoadingImages ?? true,
    },
  });

  // Calculate performance score based on form values
  const calculatePerformanceScore = () => {
    const values = form.getValues();
    let score = 0;
    
    if (values.forceHttps) score += 20;
    if (values.httpToHttpsRedirect) score += 15;
    if (values.resourceCompression) score += 20;
    if (values.browserCaching) score += 20;
    if (values.lazyLoadingImages) score += 15;
    
    return Math.min(100, score + 10); // Base score of 10
  };

  // Calculate security score based on form values
  const calculateSecurityScore = () => {
    const values = form.getValues();
    let score = 0;
    
    if (values.forceHttps) score += 50;
    if (values.httpToHttpsRedirect) score += 30;
    
    return Math.min(100, score + 20); // Base score of 20
  };

  function onSubmit(values: SecurityPerformanceFormValues) {
    if (onSave) {
      setIsSaving(true);
      
      // 准备提交数据，确保与 SiteSEOData 类型兼容
      const submitData: Partial<SiteSEOData> = {
        forceHttps: values.forceHttps,
        httpToHttpsRedirect: values.httpToHttpsRedirect,
        cacheControl: values.cacheControl,
        resourceCompression: values.resourceCompression,
        browserCaching: values.browserCaching,
        lazyLoadingImages: values.lazyLoadingImages,
      };
      
      // 调用保存方法
      onSave(submitData);
      
      // 模拟保存延迟，提供更好的用户体验
      setTimeout(() => {
        setIsSaving(false);
      }, 500);
    } else {
      console.log({
        forceHttps: values.forceHttps,
        httpToHttpsRedirect: values.httpToHttpsRedirect,
        cacheControl: values.cacheControl,
        resourceCompression: values.resourceCompression,
        browserCaching: values.browserCaching,
        lazyLoadingImages: values.lazyLoadingImages,
      });
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-4">
        <div className="flex items-start justify-between">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Security & Performance Settings</h2>
            <p className="text-sm text-muted-foreground mt-1">
              Configure security and performance settings to improve your site&apos;s SEO ranking and user experience
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={() => window.open("https://developers.google.com/search/docs/crawling-indexing/https", "_blank")}>
              <ExternalLink className="h-4 w-4 mr-2" />
              Google HTTPS Guidelines
            </Button>
            <Button variant="outline" size="sm" onClick={() => window.open("https://web.dev/articles/performance-scoring", "_blank")}>
              <ExternalLink className="h-4 w-4 mr-2" />
              Core Web Vitals
            </Button>
          </div>
        </div>
        
        <Alert className="bg-primary/10 text-primary dark:text-primary-foreground border-primary/20">
          <InfoCircledIcon className="h-4 w-4" />
          <AlertDescription>
            Security and performance are critical ranking factors. Google gives preference to secure (HTTPS) websites and those with good Core Web Vitals scores.
          </AlertDescription>
        </Alert>
      </div>
      
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center">
                    <Shield className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400" />
                    Security Score
                  </CardTitle>
                  <div className="text-2xl font-bold">{calculateSecurityScore()}%</div>
                </div>
              </CardHeader>
              <CardContent>
                <Progress value={calculateSecurityScore()} className="h-2 mb-4" />
                <p className="text-sm text-muted-foreground">
                  Security settings directly impact your SEO as search engines prioritize secure websites.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center">
                    <Zap className="h-5 w-5 mr-2 text-amber-600 dark:text-amber-400" />
                    Performance Score
                  </CardTitle>
                  <div className="text-2xl font-bold">{calculatePerformanceScore()}%</div>
                </div>
              </CardHeader>
              <CardContent>
                <Progress value={calculatePerformanceScore()} className="h-2 mb-4" />
                <p className="text-sm text-muted-foreground">
                  Page speed is a ranking factor. Faster sites provide better user experience and rank higher.
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="border rounded-md p-4 space-y-4">
            <h3 className="font-medium text-lg flex items-center">
              <Shield className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400" />
              Security Settings
            </h3>
            
            <FormField
              control={form.control}
              name="forceHttps"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Force HTTPS</FormLabel>
                    <FormDescription>
                      Serve your website over secure HTTPS connections
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="httpToHttpsRedirect"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">HTTP to HTTPS Redirect</FormLabel>
                    <FormDescription>
                      Automatically redirect HTTP requests to HTTPS
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      disabled={!form.watch("forceHttps")}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="cacheControl"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Cache Control</FormLabel>
                    <FormDescription>
                      Set cache headers for resources (e.g., &quot;max-age=31536000, public&quot;)
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Input
                      placeholder="max-age=31536000, public"
                      value={field.value || ""}
                      onChange={field.onChange}
                      className="max-w-xs"
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <Alert className="bg-primary/10 text-primary dark:text-primary-foreground border-primary/20">
              <InfoCircledIcon className="h-4 w-4" />
              <AlertDescription>
                Google uses HTTPS as a ranking signal. Secure websites are more likely to rank higher in search results.
              </AlertDescription>
            </Alert>
          </div>

          <div className="border rounded-md p-4 space-y-4">
            <h3 className="font-medium text-lg flex items-center">
              <Zap className="h-5 w-5 mr-2 text-amber-600 dark:text-amber-400" />
              Performance Settings
            </h3>
            
            <FormField
              control={form.control}
              name="resourceCompression"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Resource Compression</FormLabel>
                    <FormDescription>
                      Compress CSS, JavaScript, and HTML to reduce file sizes
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="browserCaching"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Browser Caching</FormLabel>
                    <FormDescription>
                      Enable browser caching for faster repeat visits
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="lazyLoadingImages"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Lazy Loading Images</FormLabel>
                    <FormDescription>
                      Load images only when they enter the viewport
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <Alert className="bg-primary/10 text-primary dark:text-primary-foreground border-primary/20">
              <InfoCircledIcon className="h-4 w-4" />
              <AlertDescription>
                Page speed is a critical ranking factor for both mobile and desktop search. These optimizations can significantly improve your Core Web Vitals scores.
              </AlertDescription>
            </Alert>
          </div>

          <Alert className="bg-muted/50 text-foreground dark:text-foreground border-border">
            <Clock className="h-4 w-4" />
            <AlertTitle>Implementation Note</AlertTitle>
            <AlertDescription>
              <p className="mb-2">
                These settings require server-level configuration. After saving, please ensure your hosting provider or server administrator implements:
              </p>
              <ul className="list-disc pl-5 space-y-1">
                <li>SSL certificate installation and configuration</li>
                <li>Proper HTTP to HTTPS redirects</li>
                <li>GZIP/Brotli compression for resources</li>
                <li>Appropriate cache headers</li>
                <li>Image loading attribute configuration</li>
              </ul>
            </AlertDescription>
          </Alert>
          
          <div className="border-t pt-4 flex justify-between items-center">
            <div className="space-y-1">
              <h3 className="text-sm font-medium">Best Practices</h3>
              <ul className="text-sm text-muted-foreground space-y-1">
                                    <li className="flex items-center">
                      <CheckCircle2 className="h-3.5 w-3.5 mr-1 text-green-600 dark:text-green-400" />
                      Always use HTTPS for all pages
                    </li>
                    <li className="flex items-center">
                      <CheckCircle2 className="h-3.5 w-3.5 mr-1 text-green-600 dark:text-green-400" />
                      Set appropriate cache headers for static resources
                    </li>
                    <li className="flex items-center">
                      <CheckCircle2 className="h-3.5 w-3.5 mr-1 text-green-600 dark:text-green-400" />
                      Enable compression for all text-based resources
                    </li>
              </ul>
            </div>
            
            <Button type="submit" disabled={isSaving}>
              {isSaving ? (
                <>Saving...</>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Security & Performance Settings
                </>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
