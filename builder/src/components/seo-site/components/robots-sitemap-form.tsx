"use client";

import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { SiteSEOData } from "../types";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { X } from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { InfoCircledIcon } from "@radix-ui/react-icons";
import RobotsPreview from "./preview/robots-preview";
import { But<PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { Trash2 } from "lucide-react";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { PlusCircle, HelpCircle } from "lucide-react";

const robotsSitemapFormSchema = z.object({
  robotsTxt: z.string().min(1, { message: "Robots.txt content is required" }),
  sitemapEnabled: z.boolean().default(true),
  sitemapAutoGenerate: z.boolean().default(true),
  sitemapIncludeImages: z.boolean().default(true),
  sitemapAlternateLanguagePages: z.boolean().default(true),
  sitemapChangeFrequency: z.enum([
    "ALWAYS", "HOURLY", "DAILY", "WEEKLY", "MONTHLY", "YEARLY", "NEVER"
  ]).default("WEEKLY"),
  sitemapDefaultPriority: z.number().min(0).max(1).default(0.7),
  sitemapPingSearchEngines: z.boolean().default(true),
  sitemapCacheExpiration: z.number().min(0).default(3600),
  sitemapExcludedPatterns: z.array(z.string()).optional(),
  sitemapDynamicRoutes: z.array(z.object({
    pattern: z.string(),
    priority: z.number().min(0).max(1),
    changeFrequency: z.enum(["ALWAYS", "HOURLY", "DAILY", "WEEKLY", "MONTHLY", "YEARLY", "NEVER"])
  })).optional(),
});

type RobotsSitemapFormValues = z.infer<typeof robotsSitemapFormSchema>;

interface RobotsSitemapFormProps {
  initialData?: {
    robotsTxt?: string;
    sitemapSettings?: any;
  };
  onSave?: (data: Partial<SiteSEOData>) => void;
}

export default function RobotsSitemapForm({ initialData, onSave }: RobotsSitemapFormProps) {
  const [excludedPatterns, setExcludedPatterns] = useState<string[]>(
    initialData?.sitemapSettings?.excludedPatterns || []
  );
  const [pathInput, setPathInput] = useState("");
  const [activeTab, setActiveTab] = useState("settings");

  // 预定义的 robots.txt 模板
  const robotsTemplates = {
    basic: `User-agent: *
Allow: /
Disallow: /admin/
Disallow: /private/
Sitemap: https://example.com/sitemap.xml`,
    
    allowAll: `User-agent: *
Allow: /
Sitemap: https://example.com/sitemap.xml`,
    
    restrictAll: `User-agent: *
Disallow: /
Sitemap: https://example.com/sitemap.xml`,
    
    restrictBots: `User-agent: *
Allow: /
Disallow: /admin/
Disallow: /private/
Disallow: /api/

User-agent: Googlebot
Allow: /

User-agent: Bingbot
Allow: /

Sitemap: https://example.com/sitemap.xml`,
  };

  // 常见的 robots.txt 规则
  const commonRules = {
    disallowAdmin: `Disallow: /admin/`,
    disallowPrivate: `Disallow: /private/`,
    disallowApi: `Disallow: /api/`,
    disallowLogin: `Disallow: /login/`,
    disallowSearch: `Disallow: /search`,
    disallowTemp: `Disallow: /tmp/`,
    googlebot: `User-agent: Googlebot
Allow: /`,
    bingbot: `User-agent: Bingbot
Allow: /`,
    sitemapLink: `Sitemap: https://example.com/sitemap.xml`,
  };

  const defaultSitemapSettings = initialData?.sitemapSettings || {
    enabled: false,
    autoGenerate: true,
    changeFrequency: "WEEKLY",
    dynamicRoutes: [],
    excludedPatterns: [],
    includeImages: true,
    alternateLanguagePages: false,
    defaultPriority: 0.5,
    defaultChangeFrequency: "MONTHLY",
    pingSearchEngines: true,
    searchEnginesToPing: ["google", "bing"]
  };

  const form = useForm<RobotsSitemapFormValues>({
    resolver: zodResolver(robotsSitemapFormSchema),
    defaultValues: {
      robotsTxt: initialData?.robotsTxt,
      sitemapEnabled: defaultSitemapSettings.enabled,
      sitemapAutoGenerate: defaultSitemapSettings.autoGenerate,
      sitemapIncludeImages: defaultSitemapSettings.includeImages,
      sitemapAlternateLanguagePages: defaultSitemapSettings.alternateLanguagePages,
      sitemapChangeFrequency: defaultSitemapSettings.changeFrequency,
      sitemapDefaultPriority: defaultSitemapSettings.defaultPriority,
      sitemapPingSearchEngines: defaultSitemapSettings.pingSearchEngines,
      sitemapCacheExpiration: defaultSitemapSettings.cacheExpiration || 3600,
      sitemapExcludedPatterns: defaultSitemapSettings.excludedPatterns,
      sitemapDynamicRoutes: Array.isArray(defaultSitemapSettings.dynamicRoutes) 
        ? defaultSitemapSettings.dynamicRoutes.map((route: any) => ({
            ...route,
            // 确保 changeFrequency 是大写的
            changeFrequency: route.changeFrequency?.toUpperCase() || "WEEKLY"
          }))
        : [],
    },
  });

  const addPath = () => {
    if (pathInput.trim() && !excludedPatterns.includes(pathInput.trim())) {
      setExcludedPatterns([...excludedPatterns, pathInput.trim()]);
      setPathInput("");
    }
  };

  const removePath = (pathToRemove: string) => {
    setExcludedPatterns(excludedPatterns.filter((path) => path !== pathToRemove));
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      addPath();
    }
  };

  function onSubmit(values: RobotsSitemapFormValues) {
    if (onSave) {
      onSave({
        robotsTxt: values.robotsTxt,
        sitemapSettings: {
          enabled: values.sitemapEnabled,
          autoGenerate: values.sitemapAutoGenerate,
          includeImages: values.sitemapIncludeImages,
          alternateLanguagePages: values.sitemapAlternateLanguagePages,
          changeFrequency: values.sitemapChangeFrequency,
          defaultPriority: values.sitemapDefaultPriority,
          defaultChangeFrequency: values.sitemapChangeFrequency,
          pingSearchEngines: values.sitemapPingSearchEngines,
          cacheExpiration: values.sitemapCacheExpiration,
          excludedPatterns: excludedPatterns,
          dynamicRoutes: values.sitemapDynamicRoutes,
        },
      });
    } else {
      console.log({
        robotsTxt: values.robotsTxt,
        sitemapSettings: {
          enabled: values.sitemapEnabled,
          autoGenerate: values.sitemapAutoGenerate,
          includeImages: values.sitemapIncludeImages,
          alternateLanguagePages: values.sitemapAlternateLanguagePages,
          changeFrequency: values.sitemapChangeFrequency,
          defaultPriority: values.sitemapDefaultPriority,
          defaultChangeFrequency: values.sitemapChangeFrequency,
          pingSearchEngines: values.sitemapPingSearchEngines,
          cacheExpiration: values.sitemapCacheExpiration,
          excludedPatterns: excludedPatterns,
          dynamicRoutes: values.sitemapDynamicRoutes,
        },
      });
    }
  }

  const handleAddDynamicRoute = () => {
    const currentRoutes = form.getValues("sitemapDynamicRoutes") || [];
    form.setValue("sitemapDynamicRoutes", [
      ...currentRoutes,
      {
        pattern: "",
        priority: 0.5,
        changeFrequency: "WEEKLY"
      }
    ]);
  };

  return (
    <div>
      <Tabs defaultValue="settings" onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="settings">Settings</TabsTrigger>
          <TabsTrigger value="preview">Preview</TabsTrigger>
        </TabsList>
        
        <TabsContent value="settings">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="robotsTxt"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex justify-between items-center">
                      <FormLabel>Robots.txt Content</FormLabel>
                      <div className="flex space-x-2">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="outline" size="sm">
                              <PlusCircle className="mr-2 h-4 w-4" />
                              Add Rule
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Common Rules</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => {
                              const currentValue = field.value || '';
                              field.onChange(currentValue + (currentValue ? '\n' : '') + commonRules.disallowAdmin);
                            }}>
                              Disallow Admin Area
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => {
                              const currentValue = field.value || '';
                              field.onChange(currentValue + (currentValue ? '\n' : '') + commonRules.disallowPrivate);
                            }}>
                              Disallow Private Content
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => {
                              const currentValue = field.value || '';
                              field.onChange(currentValue + (currentValue ? '\n' : '') + commonRules.disallowApi);
                            }}>
                              Disallow API Endpoints
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => {
                              const currentValue = field.value || '';
                              field.onChange(currentValue + (currentValue ? '\n' : '') + commonRules.disallowLogin);
                            }}>
                              Disallow Login Pages
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => {
                              const currentValue = field.value || '';
                              field.onChange(currentValue + (currentValue ? '\n' : '') + commonRules.disallowSearch);
                            }}>
                              Disallow Search Results
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => {
                              const currentValue = field.value || '';
                              field.onChange(currentValue + (currentValue ? '\n\n' : '') + commonRules.googlebot);
                            }}>
                              Add Googlebot Rule
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => {
                              const currentValue = field.value || '';
                              field.onChange(currentValue + (currentValue ? '\n\n' : '') + commonRules.bingbot);
                            }}>
                              Add Bingbot Rule
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => {
                              const currentValue = field.value || '';
                              field.onChange(currentValue + (currentValue ? '\n' : '') + commonRules.sitemapLink);
                            }}>
                              Add Sitemap Link
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>

                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="outline" size="sm">
                              Templates
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Choose a Template</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => field.onChange(robotsTemplates.basic)}>
                              Basic Template
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => field.onChange(robotsTemplates.allowAll)}>
                              Allow All Access
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => field.onChange(robotsTemplates.restrictAll)}>
                              Restrict All Access
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => field.onChange(robotsTemplates.restrictBots)}>
                              Advanced (Multiple Bots)
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>

                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => {
                            window.open('https://developers.google.com/search/docs/crawling-indexing/robots/create-robots-txt', '_blank');
                          }}
                        >
                          <HelpCircle className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <FormControl>
                      <Textarea 
                        placeholder="User-agent: *\nAllow: /\nDisallow: /admin/" 
                        className="font-mono h-40"
                        {...field} 
                      />
                    </FormControl>
                    <FormDescription>
                      The robots.txt file tells search engines which pages they can and cannot crawl. Use the templates and rule buttons above to easily configure your robots.txt file.
                    </FormDescription>
                    <FormMessage />
                    
                    <Alert className="mt-4 bg-primary/10 text-primary dark:text-primary-foreground border-primary/20">
                      <HelpCircle className="h-4 w-4" />
                      <AlertTitle>Robots.txt Guide</AlertTitle>
                      <AlertDescription className="text-sm">
                        <ul className="list-disc pl-4 space-y-1">
                          <li><strong>User-agent:</strong> Specifies which search engine the rule applies to (e.g., * for all bots)</li>
                          <li><strong>Allow:</strong> Tells search engines which pages they can crawl</li>
                          <li><strong>Disallow:</strong> Tells search engines which pages they cannot crawl</li>
                          <li><strong>Sitemap:</strong> Points search engines to your sitemap location</li>
                        </ul>
                      </AlertDescription>
                    </Alert>
                  </FormItem>
                )}
              />

              <div className="border rounded-md p-4 space-y-4">
                <h3 className="font-medium text-lg">XML Sitemap Settings</h3>
                
                <FormField
                  control={form.control}
                  name="sitemapEnabled"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between">
                      <div className="space-y-0.5">
                        <FormLabel>Enable XML Sitemap</FormLabel>
                        <FormDescription>
                          Generate an XML sitemap for search engines
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                {form.watch("sitemapEnabled") && (
                  <>
                    <FormField
                      control={form.control}
                      name="sitemapAutoGenerate"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between">
                          <div className="space-y-0.5">
                            <FormLabel>Auto Generate Sitemap</FormLabel>
                            <FormDescription>
                              Automatically generate the sitemap
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="sitemapIncludeImages"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between">
                          <div className="space-y-0.5">
                            <FormLabel>Include Images</FormLabel>
                            <FormDescription>
                              Add image information to the sitemap
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="sitemapAlternateLanguagePages"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between">
                          <div className="space-y-0.5">
                            <FormLabel>Include Alternate Language Pages</FormLabel>
                            <FormDescription>
                              Add alternate language pages to the sitemap
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <FormField
                        control={form.control}
                        name="sitemapChangeFrequency"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Default Change Frequency</FormLabel>
                            <Select 
                              onValueChange={field.onChange} 
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select frequency" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="ALWAYS">Always</SelectItem>
                                <SelectItem value="HOURLY">Hourly</SelectItem>
                                <SelectItem value="DAILY">Daily</SelectItem>
                                <SelectItem value="WEEKLY">Weekly</SelectItem>
                                <SelectItem value="MONTHLY">Monthly</SelectItem>
                                <SelectItem value="YEARLY">Yearly</SelectItem>
                                <SelectItem value="NEVER">Never</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormDescription>
                              How frequently pages typically change
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="sitemapDefaultPriority"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Default Priority</FormLabel>
                            <FormControl>
                              <Input 
                                type="number" 
                                min="0" 
                                max="1" 
                                step="0.1"
                                {...field}
                                onChange={(e) => field.onChange(parseFloat(e.target.value))}
                              />
                            </FormControl>
                            <FormDescription>
                              Default priority value (0.0 to 1.0) for pages
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="sitemapPingSearchEngines"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between">
                          <div className="space-y-0.5">
                            <FormLabel>Ping Search Engines</FormLabel>
                            <FormDescription>
                              Notify search engines of sitemap updates
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="sitemapCacheExpiration"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Cache Expiration (seconds)</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              min="0" 
                              {...field}
                              onChange={(e) => field.onChange(parseInt(e.target.value))}
                            />
                          </FormControl>
                          <FormDescription>
                            Time in seconds before the sitemap cache expires
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div>
                      <FormLabel htmlFor="excludedPatterns">Excluded Patterns</FormLabel>
                      <div className="flex mt-1.5 mb-1.5">
                        <Input
                          id="excludedPatterns"
                          placeholder="/path-to-exclude"
                          value={pathInput}
                          onChange={(e) => setPathInput(e.target.value)}
                          onKeyDown={handleKeyDown}
                          className="flex-1"
                        />
                      </div>
                      <FormDescription>
                        Patterns to exclude from the sitemap (e.g., /admin, /temp)
                      </FormDescription>

                      <div className="flex flex-wrap gap-2 mt-3">
                        {excludedPatterns.map((path) => (
                          <Badge key={path} variant="secondary" className="px-2 py-1 text-sm">
                            {path}
                            <X
                              className="ml-1 h-3 w-3 cursor-pointer"
                              onClick={() => removePath(path)}
                            />
                          </Badge>
                        ))}
                        {excludedPatterns.length === 0 && (
                          <span className="text-sm text-muted-foreground">No patterns excluded</span>
                        )}
                      </div>
                    </div>

                    <div className="mt-6 border rounded-lg p-4 bg-muted/30">
                      <div className="flex items-center justify-between mb-4">
                        <div>
                          <h4 className="text-sm font-medium">Dynamic Routes Configuration</h4>
                          <p className="text-xs text-muted-foreground mt-1">
                            Configure how dynamic routes should appear in your sitemap
                          </p>
                        </div>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={handleAddDynamicRoute}
                        >
                          <Plus className="h-4 w-4 mr-2" /> Add Route Pattern
                        </Button>
                      </div>

                      {(form.watch("sitemapDynamicRoutes") || []).length === 0 ? (
                        <div className="flex flex-col items-center justify-center py-6 border border-dashed rounded-md bg-card">
                          <div className="rounded-full bg-primary/10 p-3 mb-3">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary h-6 w-6"><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path><polyline points="15 3 21 3 21 9"></polyline><line x1="10" y1="14" x2="21" y2="3"></line></svg>
                          </div>
                          <h3 className="text-sm font-medium">No Dynamic Routes</h3>
                          <p className="text-xs text-muted-foreground text-center mt-1 max-w-xs">
                            Add dynamic route patterns like &quot;/blog/:slug&quot; or &quot;/products/:id&quot; to include them in your sitemap
                          </p>
                          <Button
                            type="button"
                            variant="secondary"
                            size="sm"
                            className="mt-4"
                            onClick={() => {
                              form.setValue("sitemapDynamicRoutes", [
                                {
                                  pattern: "/blog/:slug",
                                  priority: 0.8,
                                  changeFrequency: "WEEKLY"
                                }
                              ]);
                            }}
                          >
                            <Plus className="h-4 w-4 mr-2" /> Add Example Route
                          </Button>
                        </div>
                      ) : (
                        <div className="space-y-3">
                          {(form.watch("sitemapDynamicRoutes") || []).map((route, index) => (
                            <div key={index} className="bg-card border rounded-md p-3">
                              <div className="flex items-center justify-between mb-2">
                                <div className="flex items-center">
                                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-muted-foreground h-4 w-4 mr-2"><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path><polyline points="15 3 21 3 21 9"></polyline><line x1="10" y1="14" x2="21" y2="3"></line></svg>
                                  <span className="text-sm font-medium">Route Pattern {index + 1}</span>
                                </div>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="icon"
                                  className="h-8 w-8"
                                  onClick={() => {
                                    const currentRoutes = form.getValues("sitemapDynamicRoutes") || [];
                                    form.setValue("sitemapDynamicRoutes", 
                                      currentRoutes.filter((_, i) => i !== index)
                                    );
                                  }}
                                >
                                  <Trash2 className="h-4 w-4 text-muted-foreground hover:text-destructive" />
                                </Button>
                              </div>
                              
                              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-3">
                                <div className="space-y-2">
                                  <FormLabel className="text-xs">Pattern</FormLabel>
                                  <FormControl>
                                    <Input 
                                      type="text" 
                                      placeholder="/blog/:slug"
                                      className="h-9"
                                      {...form.register(`sitemapDynamicRoutes.${index}.pattern`)}
                                    />
                                  </FormControl>
                                  <FormDescription className="text-xs">
                                    Route pattern with parameters
                                  </FormDescription>
                                </div>
                                
                                <div className="space-y-2">
                                  <FormLabel className="text-xs">Priority</FormLabel>
                                  <FormControl>
                                    <Input 
                                      type="number" 
                                      min="0" 
                                      max="1" 
                                      step="0.1"
                                      placeholder="0.8"
                                      className="h-9"
                                      {...form.register(`sitemapDynamicRoutes.${index}.priority`)}
                                      onChange={(e) => form.setValue(`sitemapDynamicRoutes.${index}.priority`, parseFloat(e.target.value))}
                                    />
                                  </FormControl>
                                  <FormDescription className="text-xs">
                                    Value between 0 and 1
                                  </FormDescription>
                                </div>
                                
                                <div className="space-y-2">
                                  <FormLabel className="text-xs">Change Frequency</FormLabel>
                                  <Select 
                                    onValueChange={(value: "ALWAYS" | "HOURLY" | "DAILY" | "WEEKLY" | "MONTHLY" | "YEARLY" | "NEVER") => 
                                      form.setValue(`sitemapDynamicRoutes.${index}.changeFrequency`, value)
                                    }
                                    defaultValue={route.changeFrequency}
                                  >
                                    <FormControl>
                                      <SelectTrigger className="h-9">
                                        <SelectValue placeholder="Select frequency" />
                                      </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                      <SelectItem value="ALWAYS">Always</SelectItem>
                                      <SelectItem value="HOURLY">Hourly</SelectItem>
                                      <SelectItem value="DAILY">Daily</SelectItem>
                                      <SelectItem value="WEEKLY">Weekly</SelectItem>
                                      <SelectItem value="MONTHLY">Monthly</SelectItem>
                                      <SelectItem value="YEARLY">Yearly</SelectItem>
                                      <SelectItem value="NEVER">Never</SelectItem>
                                    </SelectContent>
                                  </Select>
                                  <FormDescription className="text-xs">
                                    How often this route changes
                                  </FormDescription>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </>
                )}
              </div>

              <Alert className="bg-primary/10 text-primary dark:text-primary-foreground border-primary/20">
                <InfoCircledIcon className="h-4 w-4" />
                <AlertTitle>Best Practice</AlertTitle>
                <AlertDescription>
                  A well-configured robots.txt file and XML sitemap help search engines discover and index your content efficiently. Make sure to include your sitemap URL in your robots.txt file.
                </AlertDescription>
              </Alert>

              {onSave && (
                <div className="flex justify-end mt-6">
                  <Button type="submit">
                    Save Changes
                  </Button>
                </div>
              )}
            </form>
          </Form>
        </TabsContent>
        
        <TabsContent value="preview">
          <RobotsPreview robotsTxt={form.watch("robotsTxt")} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
