"use client";

import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { SiteSEOData } from "../types";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { InfoCircledIcon } from "@radix-ui/react-icons";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import SocialPreview from "./preview/social-preview";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Facebook, Twitter, HelpCircle, ExternalLink, Image } from "lucide-react";
import { 
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>rigger,
} from "@/components/ui/tooltip";

const socialMediaDefaultsFormSchema = z.object({
  ogImage: z.string().url({ message: "Must be a valid URL" }),
  ogTitleTemplate: z
    .string()
    .min(5, { message: "Title template must be at least 5 characters" })
    .max(70, { message: "Title template should not exceed 70 characters" })
    .includes("%page_title%", { message: "Title template must include %page_title% placeholder" }),
  ogDescriptionTemplate: z
    .string()
    .min(10, { message: "Description template must be at least 10 characters" })
    .max(200, { message: "Description template should not exceed 200 characters" }),
  xCard: z.enum(["summary", "summary_large_image", "app", "player"]),
  xUsername: z.string().startsWith("@", { message: "X username must start with @" }),
});

type SocialMediaDefaultsFormValues = z.infer<typeof socialMediaDefaultsFormSchema>;

interface SocialMediaDefaultsFormProps {
  initialData?: {
    defaultOgImage?: string;
    ogTitleTemplate?: string;
    ogDescriptionTemplate?: string;
    defaultXCard?: "SUMMARY" | "SUMMARY_LARGE_IMAGE" | "APP" | "PLAYER";
    xUsername?: string;
  };
  onSave?: (data: Partial<SiteSEOData>) => void;
}

export default function SocialMediaDefaultsForm({ initialData, onSave }: SocialMediaDefaultsFormProps) {
  const [activeTab, setActiveTab] = useState("settings");
  const [previewType, setPreviewType] = useState<"facebook" | "x">("facebook");

  const form = useForm<SocialMediaDefaultsFormValues>({
    resolver: zodResolver(socialMediaDefaultsFormSchema),
    defaultValues: {
      ogImage: initialData?.defaultOgImage || "",
      ogTitleTemplate: initialData?.ogTitleTemplate || "",
      ogDescriptionTemplate: initialData?.ogDescriptionTemplate || "",
      xCard: (initialData?.defaultXCard?.toLowerCase() as "summary" | "summary_large_image" | "app" | "player") || "summary_large_image",
      xUsername: initialData?.xUsername || "",
    },
  });

  const handlePreview = (type: "facebook" | "x") => {
    setPreviewType(type);
    setActiveTab("preview");
  };

  function onSubmit(values: SocialMediaDefaultsFormValues) {
    if (onSave) {
      onSave({
        defaultOgImage: values.ogImage,
        ogTitleTemplate: values.ogTitleTemplate,
        ogDescriptionTemplate: values.ogDescriptionTemplate,
        defaultXCard: values.xCard.toUpperCase() as "SUMMARY" | "SUMMARY_LARGE_IMAGE" | "APP" | "PLAYER",
        xUsername: values.xUsername,
      });
    } else {
      console.log({
        defaultOgImage: values.ogImage,
        ogTitleTemplate: values.ogTitleTemplate,
        ogDescriptionTemplate: values.ogDescriptionTemplate,
        defaultXCard: values.xCard.toUpperCase() as "SUMMARY" | "SUMMARY_LARGE_IMAGE" | "APP" | "PLAYER",
        xUsername: values.xUsername,
      });
    }
  }

  // 生成预览数据
  const getPreviewData = () => {
    const values = form.getValues();
    const title = values.ogTitleTemplate.replace("%page_title%", "Example Page");
    const description = values.ogDescriptionTemplate.replace("%page_title%", "Example Page");
    
    return {
      title,
      description,
      image: values.ogImage,
      url: "https://example.com/example-page",
      siteName: "",
      xUsername: values.xUsername,
    };
  };

  return (
    <div>
      <Tabs defaultValue="settings" onValueChange={setActiveTab} value={activeTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="settings">Settings</TabsTrigger>
          <TabsTrigger value="preview">Preview</TabsTrigger>
        </TabsList>
        
        <TabsContent value="settings">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="border rounded-md p-4 space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium text-lg">Open Graph Settings</h3>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => window.open('https://ogp.me/', '_blank')}
                        >
                          <ExternalLink className="h-4 w-4 mr-2" />
                          Open Graph Docs
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Learn more about Open Graph protocol</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <p className="text-sm text-muted-foreground mb-4">
                  These settings control how your content appears when shared on Facebook, LinkedIn, and other platforms that support Open Graph.
                </p>
                
                <FormField
                  control={form.control}
                  name="ogImage"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex items-center justify-between">
                        <FormLabel>Default OG Image URL</FormLabel>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button 
                                variant="ghost" 
                                size="sm"
                                onClick={() => window.open('https://developers.facebook.com/tools/debug/', '_blank')}
                              >
                                <HelpCircle className="h-4 w-4 mr-2" />
                                Test Tool
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Test your OG image with Facebook Sharing Debugger</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                      <FormControl>
                        <div className="flex">
                          <Input 
                            placeholder="https://example.com/default-og-image.jpg" 
                            {...field} 
                            className="flex-1"
                          />
                          {field.value && (
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button 
                                    type="button" 
                                    variant="outline" 
                                    size="icon" 
                                    className="ml-2"
                                    onClick={() => window.open(field.value, '_blank')}
                                  >
                                    <Image className="h-4 w-4" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>Preview image</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          )}
                        </div>
                      </FormControl>
                      <FormDescription>
                        This image will be used when a page doesn&apos;t have its own Open Graph image (recommended size: 1200×630 pixels)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="ogTitleTemplate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>OG Title Template</FormLabel>
                      <FormControl>
                        <Input placeholder="%page_title% - Your Website Name" {...field} />
                      </FormControl>
                      <FormDescription>
                        Use %page_title% as a placeholder for individual page titles
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="ogDescriptionTemplate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>OG Description Template</FormLabel>
                      <FormControl>
                        <Input placeholder="Check out %page_title% on our website" {...field} />
                      </FormControl>
                      <FormDescription>
                        Default description for social sharing when a page doesn&apos;t have its own
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex justify-end">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => handlePreview("facebook")}
                    className="flex items-center"
                  >
                    <Facebook className="h-4 w-4 mr-2" />
                    Preview on Facebook
                  </Button>
                </div>
              </div>

              <div className="border rounded-md p-4 space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium text-lg">X Card Settings</h3>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => window.open('https://developer.twitter.com/en/docs/twitter-for-websites/cards/overview/abouts-cards', '_blank')}
                        >
                          <ExternalLink className="h-4 w-4 mr-2" />
                          X Cards Docs
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Learn more about X Card types</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <p className="text-sm text-muted-foreground mb-4">
                  These settings control how your content appears when shared on X (formerly Twitter).
                </p>
                
                <FormField
                  control={form.control}
                  name="xCard"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Default X Card Type</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select card type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="summary">Summary Card</SelectItem>
                          <SelectItem value="summary_large_image">Summary Card with Large Image</SelectItem>
                          <SelectItem value="app">App Card</SelectItem>
                          <SelectItem value="player">Player Card</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        <div className="flex items-center">
                          <span className="mr-2">The type of card to use when sharing content on X</span>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-pointer" />
                              </TooltipTrigger>
                              <TooltipContent className="w-80 p-2">
                                <p className="font-medium mb-1">Card Types:</p>
                                <ul className="list-disc pl-4 text-xs space-y-1">
                                  <li><strong>Summary Card:</strong> Title, description, and small thumbnail</li>
                                  <li><strong>Summary Card with Large Image:</strong> Title, description, and large image</li>
                                  <li><strong>App Card:</strong> Special format for mobile apps with download links</li>
                                  <li><strong>Player Card:</strong> Embeds video, audio, or media player</li>
                                </ul>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="xUsername"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex items-center justify-between">
                        <FormLabel>X Username</FormLabel>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button 
                                variant="ghost" 
                                size="sm"
                                onClick={() => window.open('https://cards-dev.twitter.com/validator', '_blank')}
                              >
                                <HelpCircle className="h-4 w-4 mr-2" />
                                Card Validator
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Test your X cards with the Card Validator</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                      <FormControl>
                        <div className="flex">
                          <Input 
                            placeholder="@yourbrand" 
                            {...field} 
                            className="flex-1"
                            onChange={(e) => {
                              // 确保用户名以 @ 开头
                              const value = e.target.value;
                              if (value && !value.startsWith('@')) {
                                field.onChange('@' + value);
                              } else {
                                field.onChange(value);
                              }
                            }}
                          />
                          {field.value && field.value.startsWith('@') && (
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button 
                                    type="button" 
                                    variant="outline" 
                                    size="icon" 
                                    className="ml-2"
                                    onClick={() => window.open(`https://x.com/${field.value.substring(1)}`, '_blank')}
                                  >
                                    <Twitter className="h-4 w-4" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>View profile on X</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          )}
                        </div>
                      </FormControl>
                      <FormDescription>
                        Your brand&apos;s X username (starting with @)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex justify-end">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => handlePreview("x")}
                    className="flex items-center"
                  >
                    <Twitter className="h-4 w-4 mr-2" />
                    Preview on X
                  </Button>
                </div>
              </div>

              <Alert className="bg-primary/10 text-primary dark:text-primary-foreground border-primary/20">
                <InfoCircledIcon className="h-4 w-4" />
                <AlertTitle>Social Media Optimization Tips</AlertTitle>
                <AlertDescription>
                  <ul className="list-disc pl-4 space-y-1 text-sm">
                    <li>Use high-quality images (1200×630px for Facebook, 1200×675px for X)</li>
                    <li>Include your brand name in title templates for better recognition</li>
                    <li>Keep descriptions concise and compelling to increase click-through rates</li>
                    <li>Test your social cards regularly using the validator tools</li>
                  </ul>
                </AlertDescription>
              </Alert>

              <div className="flex justify-end">
                {onSave && (
                  <Button type="submit" className="flex items-center">
                    Save Changes
                  </Button>
                )}
              </div>
            </form>
          </Form>
        </TabsContent>
        
        <TabsContent value="preview">
          <SocialPreview 
            type={previewType}
            data={getPreviewData()}
            onBack={() => setActiveTab("settings")}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
