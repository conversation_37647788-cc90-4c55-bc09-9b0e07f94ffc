"use client";

import { useState } from "react";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { SiteSEOData } from "../types";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { X, Plus, HelpCircle, ExternalLink, Facebook, Twitter, Instagram, Linkedin, Youtube, Check } from "lucide-react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import SchemaPreview from "./preview/schema-preview";
import { 
  <PERSON><PERSON><PERSON>,
  Too<PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>Provider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

const structuredDataFormSchema = z.object({
  organizationName: z.string().min(1, { message: "Organization name is required" }),
  organizationLogo: z.string().url({ message: "Must be a valid URL" }),
  organizationUrl: z.string().url({ message: "Must be a valid URL" }),
  contactTelephone: z.string().optional(),
  contactType: z.string().optional(),
  socialProfiles: z.array(z.string().url({ message: "Must be a valid URL" })).optional(),
});

type StructuredDataFormValues = z.infer<typeof structuredDataFormSchema>;

interface StructuredDataFormProps {
  initialData?: {
    organizationSchema?: any;
    websiteSchema?: any;
  };
  onSave?: (data: Partial<SiteSEOData>) => void;
}

export default function StructuredDataForm({ initialData, onSave }: StructuredDataFormProps) {
  const [socialProfiles, setSocialProfiles] = useState<string[]>(
    initialData?.organizationSchema?.sameAs || []
  );
  const [profileInput, setProfileInput] = useState("");
  const [activeTab, setActiveTab] = useState("settings");
  const [jsonLd, setJsonLd] = useState<string>("");

  const form = useForm<StructuredDataFormValues>({
    resolver: zodResolver(structuredDataFormSchema),
    defaultValues: {
      organizationName: initialData?.organizationSchema?.name || "",
      organizationLogo: initialData?.organizationSchema?.logo || "",
      organizationUrl: initialData?.organizationSchema?.url || "",
      contactTelephone: initialData?.organizationSchema?.contactPoint?.telephone || "",
      contactType: initialData?.organizationSchema?.contactPoint?.contactType || "",
      socialProfiles: initialData?.organizationSchema?.sameAs || [],
    },
  });

  const addProfile = () => {
    if (profileInput.trim() && !socialProfiles.includes(profileInput.trim())) {
      // 确保 URL 格式正确
      let url = profileInput.trim();
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        url = 'https://' + url;
      }
      setSocialProfiles([...socialProfiles, url]);
      setProfileInput("");
    }
  };

  // 智能识别社交媒体类型并添加适当的图标
  const getSocialIcon = (url: string) => {
    const lowerUrl = url.toLowerCase();
    if (lowerUrl.includes('facebook.com')) return <Facebook className="h-3 w-3 mr-1" />;
    if (lowerUrl.includes('twitter.com') || lowerUrl.includes('x.com')) return <Twitter className="h-3 w-3 mr-1" />;
    if (lowerUrl.includes('instagram.com')) return <Instagram className="h-3 w-3 mr-1" />;
    if (lowerUrl.includes('linkedin.com')) return <Linkedin className="h-3 w-3 mr-1" />;
    if (lowerUrl.includes('youtube.com')) return <Youtube className="h-3 w-3 mr-1" />;
    return null;
  };

  // 获取社交媒体域名的简短显示名称
  const getSocialDisplayName = (url: string) => {
    try {
      const urlObj = new URL(url);
      const domain = urlObj.hostname.replace('www.', '');
      const path = urlObj.pathname !== '/' ? urlObj.pathname : '';
      return domain + path;
    } catch (e) {
      return url;
    }
  };

  const removeProfile = (profileToRemove: string) => {
    setSocialProfiles(socialProfiles.filter((profile) => profile !== profileToRemove));
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      addProfile();
    }
  };

  // 生成 JSON-LD
  const generateJsonLd = (values: StructuredDataFormValues) => {
    const schema = {
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": values.organizationName,
      "url": values.organizationUrl,
      "logo": values.organizationLogo,
    };

    if (values.contactTelephone && values.contactType) {
      Object.assign(schema, {
        "contactPoint": {
          "@type": "ContactPoint",
          "telephone": values.contactTelephone,
          "contactType": values.contactType
        }
      });
    }

    if (socialProfiles.length > 0) {
      Object.assign(schema, {
        "sameAs": socialProfiles
      });
    }

    return JSON.stringify(schema, null, 2);
  };

  const handlePreview = () => {
    const values = form.getValues();
    setJsonLd(generateJsonLd(values));
    setActiveTab("preview");
  };

  function onSubmit(values: StructuredDataFormValues) {
    const jsonLdString = generateJsonLd(values);
    setJsonLd(jsonLdString);

    if (onSave) {
      onSave({
        organizationSchema: {
          name: values.organizationName,
          logo: values.organizationLogo,
          url: values.organizationUrl,
          contactPoint: values.contactTelephone && values.contactType ? {
            telephone: values.contactTelephone,
            contactType: values.contactType
          } : undefined,
          sameAs: socialProfiles.length > 0 ? socialProfiles : undefined
        }
      });
    } else {
      console.log({
        organizationSchema: {
          name: values.organizationName,
          logo: values.organizationLogo,
          url: values.organizationUrl,
          contactPoint: values.contactTelephone && values.contactType ? {
            telephone: values.contactTelephone,
            contactType: values.contactType
          } : undefined,
          sameAs: socialProfiles.length > 0 ? socialProfiles : undefined
        }
      });
    }
  }

  return (
    <div>
      <Tabs defaultValue="settings" onValueChange={setActiveTab} value={activeTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="settings">Settings</TabsTrigger>
          <TabsTrigger value="preview">Preview</TabsTrigger>
        </TabsList>
        
        <TabsContent value="settings">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="border rounded-md p-4 space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium text-lg">Organization Information</h3>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => window.open('https://schema.org/Organization', '_blank')}
                        >
                          <ExternalLink className="h-4 w-4 mr-2" />
                          Schema.org Reference
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>View Organization schema documentation</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                
                <p className="text-sm text-muted-foreground mb-4">
                  This information will be used to create structured data for your organization, which helps search engines understand your business identity and may enhance your appearance in search results.
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormField
                    control={form.control}
                    name="organizationName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Organization Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Your Organization Name" {...field} />
                        </FormControl>
                        <FormDescription>
                          The official name of your organization
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="organizationUrl"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Website URL</FormLabel>
                        <FormControl>
                          <Input placeholder="https://example.com" {...field} />
                        </FormControl>
                        <FormDescription>
                          The primary URL of your organization
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="organizationLogo"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Logo URL</FormLabel>
                      <FormControl>
                        <Input placeholder="https://example.com/logo.png" {...field} />
                      </FormControl>
                      <FormDescription>
                        URL to your organization&apos;s logo (recommended size: 112x112px, at least 100x100px)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="border rounded-md p-4 space-y-4">
                <h3 className="font-medium text-lg">Contact Information</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormField
                    control={form.control}
                    name="contactTelephone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Contact Phone</FormLabel>
                        <FormControl>
                          <Input placeholder="******-123-4567" {...field} />
                        </FormControl>
                        <FormDescription>
                          Primary contact phone number with country code
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="contactType"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Contact Type</FormLabel>
                        <FormControl>
                          <Input placeholder="customer service" {...field} />
                        </FormControl>
                        <FormDescription>
                          Type of contact (e.g., customer service, technical support)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div className="border rounded-md p-4 space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium text-lg">Social Profiles</h3>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => window.open('https://developers.google.com/search/docs/appearance/structured-data/logo', '_blank')}
                        >
                          <HelpCircle className="h-4 w-4 mr-2" />
                          Learn More
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Learn about social profiles in structured data</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                
                <p className="text-sm text-muted-foreground mb-2">
                  Add your organization&apos;s social media profiles to help search engines connect your website with your social presence.
                </p>
                
                <div>
                  <FormLabel htmlFor="socialProfiles">Social Media URLs</FormLabel>
                  <div className="flex mt-1.5 mb-1.5">
                    <Input
                      id="socialProfiles"
                      placeholder="Enter social media URL or username"
                      value={profileInput}
                      onChange={(e) => setProfileInput(e.target.value)}
                      onKeyDown={handleKeyDown}
                      className="flex-1"
                    />
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button 
                            type="button" 
                            variant="default" 
                            size="icon" 
                            className="ml-2"
                            onClick={addProfile}
                            disabled={!profileInput.trim()}
                          >
                            <Plus className="h-4 w-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent side="top">
                          <p>Add social profile</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <FormDescription>
                    Add complete URLs to your organization&apos;s social media profiles (e.g., https://facebook.com/yourcompany)
                  </FormDescription>

                  <div className="flex flex-wrap gap-2 mt-3 mb-4 min-h-[40px]">
                    {socialProfiles.map((profile) => (
                      <Badge 
                        key={profile} 
                        variant="secondary" 
                        className="px-2 py-1 text-sm flex items-center group hover:bg-secondary/80 transition-colors"
                      >
                        {getSocialIcon(profile)}
                        {getSocialDisplayName(profile)}
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <X
                                className="ml-1 h-3 w-3 cursor-pointer opacity-70 group-hover:opacity-100"
                                onClick={() => removeProfile(profile)}
                              />
                            </TooltipTrigger>
                            <TooltipContent side="top">
                              <p>Remove profile</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </Badge>
                    ))}
                    {socialProfiles.length === 0 && (
                      <div className="text-sm text-muted-foreground flex items-center border border-dashed border-gray-300 rounded-md p-2 w-full justify-center">
                        <span>No social profiles added yet</span>
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <div className="text-sm font-medium">Quick Add</div>
                    <div className="flex flex-wrap gap-2">
                      <Button 
                        type="button" 
                        variant="outline" 
                        size="sm" 
                        onClick={() => setProfileInput("https://facebook.com/")}
                        className="flex items-center"
                      >
                        <Facebook className="h-4 w-4 mr-2" />
                        Facebook
                      </Button>
                      <Button 
                        type="button" 
                        variant="outline" 
                        size="sm" 
                        onClick={() => setProfileInput("https://x.com/")}
                        className="flex items-center"
                      >
                        <Twitter className="h-4 w-4 mr-2" />
                        Twitter/X
                      </Button>
                      <Button 
                        type="button" 
                        variant="outline" 
                        size="sm" 
                        onClick={() => setProfileInput("https://instagram.com/")}
                        className="flex items-center"
                      >
                        <Instagram className="h-4 w-4 mr-2" />
                        Instagram
                      </Button>
                      <Button 
                        type="button" 
                        variant="outline" 
                        size="sm" 
                        onClick={() => setProfileInput("https://linkedin.com/company/")}
                        className="flex items-center"
                      >
                        <Linkedin className="h-4 w-4 mr-2" />
                        LinkedIn
                      </Button>
                      <Button 
                        type="button" 
                        variant="outline" 
                        size="sm" 
                        onClick={() => setProfileInput("https://youtube.com/")}
                        className="flex items-center"
                      >
                        <Youtube className="h-4 w-4 mr-2" />
                        YouTube
                      </Button>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      Click a button to pre-fill the URL, then add your username and click the + button
                    </p>
                  </div>
                </div>
              </div>

              <Alert className="bg-primary/10 text-primary dark:text-primary-foreground border-primary/20">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>What is Structured Data?</AlertTitle>
                <AlertDescription>
                  <p className="mb-2">Structured data (schema markup) helps search engines understand your content better and can enhance your appearance in search results with rich snippets.</p>
                  <ul className="list-disc pl-4 space-y-1 text-sm">
                    <li>Organization schema tells search engines about your business identity</li>
                    <li>Adding social profiles helps connect your website with your social presence</li>
                    <li>Contact information makes it easier for users to reach you directly from search results</li>
                    <li>All information should be accurate and up-to-date</li>
                  </ul>
                </AlertDescription>
              </Alert>

              <div className="flex justify-end space-x-2">
                <Button type="button" variant="outline" onClick={handlePreview}>
                  Preview JSON-LD
                </Button>
                {onSave && (
                  <Button type="submit">
                    Save Changes
                  </Button>
                )}
              </div>
            </form>
          </Form>
        </TabsContent>
        
        <TabsContent value="preview">
          <SchemaPreview 
            organizationSchema={form.getValues()}
            websiteSchema={initialData?.websiteSchema}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
