"use client";

import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { SiteSEOData } from "../types";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { X } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button"; // Add this line

const globalMetaFormSchema = z.object({
  siteName: z
    .string()
    .min(2, { message: "Site name must be at least 2 characters" })
    .max(50, { message: "Site name should not exceed 50 characters" }),
  siteDescription: z
    .string()
    .min(10, { message: "Site description must be at least 10 characters" })
    .max(160, { message: "Site description should not exceed 160 characters" }),
  titleTemplate: z
    .string()
    .min(5, { message: "Title template must be at least 5 characters" })
    .max(70, { message: "Title template should not exceed 70 characters" })
    .includes("%page_title%", { message: "Title template must include %page_title% placeholder" }),
  defaultDescription: z
    .string()
    .min(50, { message: "Default description must be at least 50 characters" })
    .max(160, { message: "Default description should not exceed 160 characters" }),
  globalKeywords: z.array(z.string()).optional(),
});

type GlobalMetaFormValues = z.infer<typeof globalMetaFormSchema>;

interface GlobalMetaFormProps {
  initialData?: Partial<SiteSEOData>;
  onSave?: (data: Partial<SiteSEOData>) => void;
}

export default function GlobalMetaForm({ initialData, onSave }: GlobalMetaFormProps) {
  const [keywords, setKeywords] = useState<string[]>(initialData?.globalKeywords || []);
  const [keywordInput, setKeywordInput] = useState("");

  const form = useForm<GlobalMetaFormValues>({
    resolver: zodResolver(globalMetaFormSchema),
    defaultValues: {
      siteName: initialData?.siteName,
      siteDescription: initialData?.siteDescription,
      titleTemplate: initialData?.titleTemplate,
      defaultDescription: initialData?.defaultDescription,
      globalKeywords: initialData?.globalKeywords,
    },
  });

  const addKeyword = () => {
    if (keywordInput.trim() && !keywords.includes(keywordInput.trim())) {
      setKeywords([...keywords, keywordInput.trim()]);
      setKeywordInput("");
    }
  };

  const removeKeyword = (keywordToRemove: string) => {
    setKeywords(keywords.filter((keyword) => keyword !== keywordToRemove));
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      addKeyword();
    }
  };

  function onSubmit(values: GlobalMetaFormValues) {
    if (onSave) {
      onSave({
        ...values,
        globalKeywords: keywords,
      });
    } else {
      console.log({ ...values, globalKeywords: keywords });
    }
  }

  return (
    <div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="siteName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Site Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Your Website Name" {...field} />
                  </FormControl>
                  <FormDescription>
                    The name of your website, used in various SEO elements
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="titleTemplate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Title Template</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="%page_title% | Your Website Name" 
                      {...field} 
                    />
                  </FormControl>
                  <FormDescription>
                    Use %page_title% as a placeholder for individual page titles
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="siteDescription"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Site Description</FormLabel>
                <FormControl>
                  <Textarea 
                    placeholder="A brief description of your website" 
                    className="resize-none h-20"
                    {...field} 
                  />
                </FormControl>
                <FormDescription>
                  A general description of your website for SEO purposes
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="defaultDescription"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Default Meta Description</FormLabel>
                <FormControl>
                  <Textarea 
                    placeholder="Default description for pages without specific descriptions" 
                    className="resize-none h-20"
                    {...field} 
                  />
                </FormControl>
                <FormDescription>
                  This will be used when a page doesn&apos;t have its own meta description
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <div>
            <FormLabel htmlFor="keywords">Global Keywords</FormLabel>
            <div className="flex mt-1.5 mb-1.5">
              <Input
                id="keywords"
                placeholder="Add a keyword and press Enter"
                value={keywordInput}
                onChange={(e) => setKeywordInput(e.target.value)}
                onKeyDown={handleKeyDown}
                className="flex-1"
              />
            </div>
            <FormDescription>
              These keywords will be used as a fallback for pages without specific keywords
            </FormDescription>

            <div className="flex flex-wrap gap-2 mt-3">
              {keywords.map((keyword) => (
                <Badge key={keyword} variant="secondary" className="px-2 py-1 text-sm">
                  {keyword}
                  <X
                    className="ml-1 h-3 w-3 cursor-pointer"
                    onClick={() => removeKeyword(keyword)}
                  />
                </Badge>
              ))}
              {keywords.length === 0 && (
                <span className="text-sm text-muted-foreground">No keywords added yet</span>
              )}
            </div>
          </div>

          <Alert className="bg-primary/10 text-primary dark:text-primary-foreground border-primary/20">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Best Practice</AlertTitle>
            <AlertDescription>
              While meta keywords are less important for SEO today, they can still be useful for internal site search and organization. Focus on your title template and descriptions for better SEO results.
            </AlertDescription>
          </Alert>

          {onSave && (
            <div className="flex justify-end mt-6">
              <Button type="submit">
                Save Changes
              </Button>
            </div>
          )}
        </form>
      </Form>
    </div>
  );
}
