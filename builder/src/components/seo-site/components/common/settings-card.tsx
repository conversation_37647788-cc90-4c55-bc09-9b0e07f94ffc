"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ReactNode } from "react";

interface SettingsCardProps {
  title: string;
  description: string;
  icon?: ReactNode;
  children: ReactNode;
  onSave?: () => void;
  saveButtonText?: string;
  footerContent?: ReactNode;
  className?: string;
}

export default function SettingsCard({
  title,
  description,
  icon,
  children,
  onSave,
  saveButtonText = "Save Settings",
  footerContent,
  className = "",
}: SettingsCardProps) {
  return (
    <Card className={`shadow-sm ${className}`}>
      <CardHeader className="border-b border-border">
        <div className="flex items-start justify-between">
          <div>
            <CardTitle className="text-xl">{title}</CardTitle>
            <CardDescription>{description}</CardDescription>
          </div>
          {icon && (
            <div className="flex items-center justify-center h-8 w-8 rounded-full bg-primary/10 text-primary">
              {icon}
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent className="p-6">{children}</CardContent>
      <CardFooter className="border-t border-border bg-muted/30 py-4">
        <div className="flex justify-between w-full">
          {footerContent}
          {onSave && (
            <Button onClick={onSave} className="ml-auto">
              {saveButtonText}
            </Button>
          )}
        </div>
      </CardFooter>
    </Card>
  );
}
