"use client";

import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { AlertCircle, AlertTriangle, Check } from "lucide-react";

interface SEOScoreCardProps {
  score: number;
  label: string;
  icon: React.ReactNode;
  description: string;
}

export default function SEOScoreCard({ score, label, icon, description }: SEOScoreCardProps) {
  // 获取状态图标和颜色
  const getScoreStatus = (score: number) => {
    if (score >= 80) return { 
      color: "text-green-600 dark:text-green-400", 
      bgColor: "bg-green-500/10", 
      icon: <Check className="h-4 w-4" /> 
    };
    if (score >= 60) return { 
      color: "text-amber-600 dark:text-amber-400", 
      bgColor: "bg-amber-500/10", 
      icon: <AlertTriangle className="h-4 w-4" /> 
    };
    return { 
      color: "text-red-600 dark:text-red-400", 
      bgColor: "bg-red-500/10", 
      icon: <AlertCircle className="h-4 w-4" /> 
    };
  };

  const status = getScoreStatus(score);

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="flex flex-col p-4 border border-border rounded-lg bg-card text-card-foreground shadow-sm">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                {icon}
                <span className="font-medium ml-2">{label}</span>
              </div>
              <Badge className={`${status.color} ${status.bgColor} border-0`}>
                {score}
              </Badge>
            </div>
            <Progress value={score} className="h-1.5" />
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p>{description}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
