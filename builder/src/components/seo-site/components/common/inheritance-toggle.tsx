"use client";

import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { InfoCircledIcon } from "@radix-ui/react-icons";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface InheritanceToggleProps {
  label: string;
  checked: boolean;
  onCheckedChange: (checked: boolean) => void;
  description?: string;
}

export default function InheritanceToggle({
  label,
  checked,
  onCheckedChange,
  description,
}: InheritanceToggleProps) {
  return (
    <div className="flex items-center justify-between py-2 border-b border-border last:border-0">
      <div className="flex items-center space-x-2">
        <Label htmlFor={`inherit-${label.toLowerCase().replace(/\s+/g, '-')}`} className="font-medium">
          {label}
        </Label>
        {description && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <InfoCircledIcon className="h-4 w-4 text-muted-foreground" />
              </TooltipTrigger>
              <TooltipContent>
                <p className="max-w-xs text-sm">{description}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>
      <Switch
        id={`inherit-${label.toLowerCase().replace(/\s+/g, '-')}`}
        checked={checked}
        onCheckedChange={onCheckedChange}
      />
    </div>
  );
}
