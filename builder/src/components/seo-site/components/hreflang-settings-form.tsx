"use client";

import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { SiteSEOData } from "../types";
import { WebsiteSEO } from "@/lib/api/seo";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { InfoCircledIcon } from "@radix-ui/react-icons";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Trash2, Plus, ExternalLink, HelpCircle, Globe, Check } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";

const languageFormSchema = z.object({
  defaultLanguage: z.string().min(2, { message: "Default language code is required" }),
});

type LanguageFormValues = z.infer<typeof languageFormSchema>;

interface HreflangSettingsFormProps {
  initialData?: WebsiteSEO | null;
  onSave?: (data: Partial<SiteSEOData>) => void;
}

// 语言代码和名称的映射
const languageOptions = [
  { code: "en", name: "English" },
  { code: "zh", name: "Chinese" },
  { code: "es", name: "Spanish" },
  { code: "fr", name: "French" },
  { code: "de", name: "German" },
  { code: "ja", name: "Japanese" },
  { code: "ko", name: "Korean" },
  { code: "ru", name: "Russian" },
  { code: "pt", name: "Portuguese" },
  { code: "it", name: "Italian" },
  { code: "ar", name: "Arabic" },
  { code: "nl", name: "Dutch" },
  { code: "sv", name: "Swedish" },
  { code: "th", name: "Thai" },
  { code: "vi", name: "Vietnamese" },
];

// 区域代码和名称的映射
const regionOptions = [
  { code: "global", name: "None (Global)" },
  { code: "us", name: "United States" },
  { code: "gb", name: "United Kingdom" },
  { code: "ca", name: "Canada" },
  { code: "au", name: "Australia" },
  { code: "de", name: "Germany" },
  { code: "fr", name: "France" },
  { code: "jp", name: "Japan" },
  { code: "cn", name: "China" },
  { code: "br", name: "Brazil" },
  { code: "mx", name: "Mexico" },
  { code: "es", name: "Spain" },
  { code: "it", name: "Italy" },
  { code: "ru", name: "Russia" },
  { code: "in", name: "India" },
];

export default function HreflangSettingsForm({ initialData, onSave }: HreflangSettingsFormProps) {
  // 简单获取 hreflangSettings 对象
  const hreflangSettings = initialData?.hreflangSettings || {};
  
  // 确保 supportedLanguages 是一个数组
  const initialLanguages = Array.isArray(hreflangSettings.supportedLanguages) 
    ? hreflangSettings.supportedLanguages 
    : [];
  
  const [languages, setLanguages] = useState<Array<{
    code: string;
    region: string;
    url: string;
  }>>(initialLanguages);

  const [newLanguage, setNewLanguage] = useState({
    code: "en",
    region: "global",
    url: "",
  });

  // 确保 defaultLanguage 是一个字符串
  const initialDefaultLanguage = typeof hreflangSettings.defaultLanguage === 'string'
    ? hreflangSettings.defaultLanguage
    : "en";

  const form = useForm<LanguageFormValues>({
    resolver: zodResolver(languageFormSchema),
    defaultValues: {
      defaultLanguage: initialDefaultLanguage,
    },
  });

  const addLanguage = () => {
    if (newLanguage.code && newLanguage.url) {
      // 检查是否已存在相同的语言和区域组合
      const exists = languages.some(
        (lang) => lang.code === newLanguage.code && lang.region === newLanguage.region
      );

      if (!exists) {
        // 确保URL格式正确
        let url = newLanguage.url;
        if (url && !url.startsWith('http://') && !url.startsWith('https://')) {
          url = 'https://' + url;
        }

        setLanguages([...languages, { ...newLanguage, url }]);
        setNewLanguage({
          code: "en",
          region: "global",
          url: "",
        });
      }
    }
  };

  const removeLanguage = (index: number) => {
    const updatedLanguages = [...languages];
    updatedLanguages.splice(index, 1);
    setLanguages(updatedLanguages);
  };

  function onSubmit(values: LanguageFormValues) {
    // 确保所有语言条目都有必要的字段
    const validLanguages = languages.filter(lang => 
      lang.code && (lang.region || lang.region === "global") && lang.url
    );

    // 构建符合后端期望格式的数据
    const hreflangData = {
      defaultLanguage: values.defaultLanguage,
      supportedLanguages: validLanguages.map(lang => ({
        code: lang.code,
        region: lang.region,
        url: lang.url
      }))
    };

    if (onSave) {
      onSave({
        hreflangSettings: hreflangData
      });
    } else {
      console.log({
        hreflangSettings: hreflangData
      });
    }
  }

  // 获取语言名称
  const getLanguageName = (code: string) => {
    const language = languageOptions.find((lang) => lang.code === code);
    return language ? language.name : code;
  };

  // 获取区域名称
  const getRegionName = (code: string) => {
    if (code === "global") return "Global";
    const region = regionOptions.find((reg) => reg.code === code);
    return region ? region.name : code;
  };

  // 格式化 hreflang 属性值
  const formatHreflang = (language: string, region: string) => {
    return region && region !== "global" ? `${language}-${region}` : language;
  };

  // 检查是否为默认语言
  const isDefaultLanguage = (code: string) => {
    return code === form.watch("defaultLanguage");
  };

  // 快速添加常用语言组合
  const addCommonLanguage = (code: string, region: string = "global", domain: string = "") => {
    const exists = languages.some(
      (lang) => lang.code === code && lang.region === region
    );

    if (!exists) {
      // 如果已有其他语言，尝试使用相同的域名格式
      let url = "";
      if (languages.length > 0 && domain) {
        const existingUrl = languages[0].url;
        const urlParts = existingUrl.split('/');
        if (urlParts.length >= 3) {
          const protocol = urlParts[0];
          const host = urlParts[2];
          url = `${protocol}//${host}/${code}`;
        }
      }

      setLanguages([...languages, { 
        code, 
        region, 
        url: url || `https://example.com/${code}` 
      }]);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h2 className="text-xl font-semibold">Hreflang Settings</h2>
          <p className="text-sm text-muted-foreground">
            Configure language and region variants of your website for international SEO
          </p>
        </div>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => window.open('https://developers.google.com/search/docs/specialty/international/localized-versions', '_blank')}
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                Google Docs
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Learn more about hreflang tags from Google</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="defaultLanguage"
            render={({ field }) => (
              <FormItem>
                <div className="flex items-center justify-between">
                  <FormLabel>Default Language</FormLabel>
                  <Badge variant="outline" className="text-xs">Required</Badge>
                </div>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select default language" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {languageOptions.map((option) => (
                      <SelectItem key={option.code} value={option.code}>
                        {option.name} ({option.code})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormDescription className="flex items-center">
                  <span className="mr-2">The default language of your website. This will be used for the x-default hreflang tag.</span>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <HelpCircle className="h-4 w-4 text-muted-foreground cursor-pointer" />
                      </TooltipTrigger>
                      <TooltipContent className="w-80 p-2">
                        <p>The x-default tag tells search engines which version to show when a user&apos;s language doesn&apos;t match any of your language versions.</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="border rounded-md p-4 space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="font-medium text-lg">Supported Languages</h3>
              <div className="flex space-x-2">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => {
                          addCommonLanguage("en", "us");
                          addCommonLanguage("en", "gb");
                          addCommonLanguage("fr");
                          addCommonLanguage("de");
                          addCommonLanguage("es");
                        }}
                      >
                        <Globe className="h-4 w-4 mr-2" />
                        Add Common Set
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Add common language set (EN-US, EN-GB, FR, DE, ES)</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <FormLabel>Language</FormLabel>
                <Select
                  value={newLanguage.code}
                  onValueChange={(value) => setNewLanguage({ ...newLanguage, code: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select language" />
                  </SelectTrigger>
                  <SelectContent>
                    {languageOptions.map((option) => (
                      <SelectItem key={option.code} value={option.code}>
                        {option.name} ({option.code})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <FormLabel>Region (Optional)</FormLabel>
                <Select
                  value={newLanguage.region}
                  onValueChange={(value) => setNewLanguage({ ...newLanguage, region: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select region" />
                  </SelectTrigger>
                  <SelectContent>
                    {regionOptions.map((option) => (
                      <SelectItem key={option.code} value={option.code}>
                        {option.name} {option.code ? `(${option.code})` : ""}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <FormLabel>URL</FormLabel>
                <div className="flex">
                  <Input
                    placeholder="https://example.com/fr"
                    value={newLanguage.url}
                    onChange={(e) => setNewLanguage({ ...newLanguage, url: e.target.value })}
                    className="flex-1"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        addLanguage();
                      }
                    }}
                  />
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          type="button"
                          onClick={addLanguage}
                          className="ml-2"
                          disabled={!newLanguage.code || !newLanguage.url}
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Add language variant</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </div>
            </div>

            <div className="flex flex-wrap gap-2 mt-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button 
                      type="button" 
                      variant="outline" 
                      size="sm"
                      onClick={() => addCommonLanguage("en", "us")}
                      className="text-xs"
                    >
                      EN-US
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Add English (United States)</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button 
                      type="button" 
                      variant="outline" 
                      size="sm"
                      onClick={() => addCommonLanguage("en", "gb")}
                      className="text-xs"
                    >
                      EN-GB
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Add English (United Kingdom)</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button 
                      type="button" 
                      variant="outline" 
                      size="sm"
                      onClick={() => addCommonLanguage("fr")}
                      className="text-xs"
                    >
                      FR
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Add French</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button 
                      type="button" 
                      variant="outline" 
                      size="sm"
                      onClick={() => addCommonLanguage("de")}
                      className="text-xs"
                    >
                      DE
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Add German</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button 
                      type="button" 
                      variant="outline" 
                      size="sm"
                      onClick={() => addCommonLanguage("es")}
                      className="text-xs"
                    >
                      ES
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Add Spanish</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button 
                      type="button" 
                      variant="outline" 
                      size="sm"
                      onClick={() => addCommonLanguage("zh", "cn")}
                      className="text-xs"
                    >
                      ZH-CN
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Add Chinese (China)</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>

            {languages.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Language</TableHead>
                    <TableHead>Region</TableHead>
                    <TableHead>hreflang</TableHead>
                    <TableHead>URL</TableHead>
                    <TableHead className="w-[100px]">Default</TableHead>
                    <TableHead className="w-[50px]"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {languages.map((lang, index) => (
                    <TableRow key={index}>
                      <TableCell>{getLanguageName(lang.code)}</TableCell>
                      <TableCell>{getRegionName(lang.region)}</TableCell>
                      <TableCell>
                        <code className="bg-muted px-1 py-0.5 rounded text-xs">{formatHreflang(lang.code, lang.region)}</code>
                      </TableCell>
                      <TableCell className="font-mono text-sm truncate max-w-[200px]">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <span className="cursor-help">{lang.url}</span>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>{lang.url}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </TableCell>
                      <TableCell>
                        {isDefaultLanguage(lang.code) && (
                          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 dark:bg-green-950/30 dark:text-green-400 dark:border-green-800">
                            <Check className="h-3 w-3 mr-1" /> Default
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => removeLanguage(index)}
                          className="h-8 w-8 text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <div className="text-center py-8 text-muted-foreground border border-dashed rounded-md">
                <Globe className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>No languages added yet. Add your first language above.</p>
              </div>
            )}
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span>hreflang Implementation Preview</span>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => window.open('https://ahrefs.com/webmaster-tools/hreflang-validator', '_blank')}
                        className="ml-2"
                      >
                        <ExternalLink className="h-4 w-4 mr-2" />
                        Validator
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Validate your hreflang tags with Ahrefs Webmaster Tools</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-muted/50 p-4 rounded-md border border-border font-mono text-sm overflow-auto">
                {languages.length > 0 ? (
                  <>
                    <p className="text-slate-500 mb-2">{'<!-- Add these tags to your <head> section -->'}</p>
                    {languages.map((lang, index) => (
                      <div key={index} className="mb-1">
                        &lt;link rel=&quot;alternate&quot; hreflang=&quot;{formatHreflang(lang.code, lang.region)}&quot; href=&quot;{lang.url}&quot; /&gt;
                      </div>
                    ))}
                    {form.watch("defaultLanguage") && (
                      <div className="mt-2 pt-2 border-t border-dashed border-slate-200">
                        &lt;link rel=&quot;alternate&quot; hreflang=&quot;x-default&quot; href=&quot;{languages.find(l => l.code === form.watch('defaultLanguage'))?.url || 'https://example.com'}&quot; /&gt;
                      </div>
                    )}
                  </>
                ) : (
                  <div className="text-muted-foreground">No hreflang tags to preview</div>
                )}
              </div>
            </CardContent>
          </Card>

          <Alert className="bg-primary/10 text-primary dark:text-primary-foreground border-primary/20">
            <InfoCircledIcon className="h-4 w-4" />
            <AlertTitle>Hreflang Best Practices</AlertTitle>
            <AlertDescription>
              <ul className="list-disc pl-5 space-y-1 mt-1">
                <li>Always include an <code className="bg-primary/20 px-1 py-0.5 rounded">x-default</code> hreflang attribute pointing to your default language version.</li>
                <li>Make sure all language versions link to each other with the appropriate hreflang tags.</li>
                <li>Use the same URL structure across all language versions for better consistency.</li>
                <li>For language-specific regions (e.g., French in Canada), use both language and region codes.</li>
                <li>Test your implementation with Google&apos;s URL Inspection tool or a hreflang validator.</li>
                <li>Ensure your server returns the correct <code className="bg-primary/20 px-1 py-0.5 rounded">Content-Language</code> HTTP header.</li>
              </ul>
            </AlertDescription>
          </Alert>

          <div className="flex justify-end">
            {onSave && (
              <Button type="submit" className="flex items-center">
                Save Hreflang Settings
              </Button>
            )}
          </div>
        </form>
      </Form>
    </div>
  );
}
