"use client";

import { useState, useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { SiteSEOData } from "../types";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { InfoCircledIcon } from "@radix-ui/react-icons";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Trash2, Plus, Upload, Save, ExternalLink, HelpCircle, Link, Info, Pa<PERSON> } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { CheckCircle2 } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { TwitterPicker } from 'react-color';
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";

// 简化类型定义，避免类型错误
type ColorResult = {
  hex: string;
  rgb: { r: number; g: number; b: number; a?: number };
};

const brandIdentityFormSchema = z.object({
  brandName: z.string().min(1, { message: "Brand name is required" }),
  brandTagline: z.string().optional(),
  brandDescription: z.string().min(10, { message: "Description should be at least 10 characters" }).max(500, { message: "Description should not exceed 500 characters" }).optional(),
  brandLogoUrl: z.string().url({ message: "Please enter a valid URL" }).optional().or(z.literal("")),
  brandFaviconUrl: z.string().url({ message: "Please enter a valid URL" }).optional().or(z.literal("")),
  brandColors: z.object({
    primary: z.string(),
    secondary: z.string().optional(),
  }),
  socialProfiles: z.array(
    z.object({
      platform: z.string(),
      url: z.string().url({ message: "Please enter a valid URL" })
    })
  ).optional(),
});

type BrandIdentityFormValues = z.infer<typeof brandIdentityFormSchema>;

interface BrandIdentityFormProps {
  initialData?: SiteSEOData;
  onSave?: (section: string, data: any) => void;
  isSaving?: boolean;
}

export default function BrandIdentityForm({ initialData, onSave, isSaving: externalIsSaving }: BrandIdentityFormProps) {
  const [socialProfiles, setSocialProfiles] = useState<Array<{ platform: string; url: string }>>(
    initialData?.brandSettings?.socialProfiles || []
  );
  
  const [newProfile, setNewProfile] = useState({ platform: "", url: "" });
  const [isSaving, setIsSaving] = useState(externalIsSaving || false);
  const [activeTab, setActiveTab] = useState("settings");
  const [brandColors, setBrandColors] = useState({
    primary: initialData?.brandSettings?.brandColors?.primary || "#3b82f6",
    secondary: initialData?.brandSettings?.brandColors?.secondary || "#f97316",
  });

  const form = useForm<BrandIdentityFormValues>({
    resolver: zodResolver(brandIdentityFormSchema),
    defaultValues: {
      brandName: initialData?.brandSettings?.brandName || "",
      brandTagline: initialData?.brandSettings?.brandTagline || "",
      brandDescription: initialData?.brandSettings?.brandDescription || "",
      brandLogoUrl: initialData?.brandSettings?.brandLogoUrl || "",
      brandFaviconUrl: initialData?.brandSettings?.brandFaviconUrl || "",
      brandColors: {
        primary: initialData?.brandSettings?.brandColors?.primary || "#3b82f6",
        secondary: initialData?.brandSettings?.brandColors?.secondary || "#f97316",
      },
      socialProfiles: initialData?.brandSettings?.socialProfiles || [],
    },
  });

  // Update form values when brandColors state changes
  useEffect(() => {
    form.setValue("brandColors.primary", brandColors.primary);
    form.setValue("brandColors.secondary", brandColors.secondary);
  }, [brandColors, form]);

  // 当表单值变化时自动切换到预览标签
  useEffect(() => {
    const subscription = form.watch(() => {
      // 如果用户正在编辑表单，不要自动切换到预览
      if (activeTab === "settings") return;
    });
    return () => subscription.unsubscribe();
  }, [form, activeTab]);

  const addSocialProfile = () => {
    if (newProfile.platform && newProfile.url) {
      setSocialProfiles([...socialProfiles, { ...newProfile }]);
      setNewProfile({ platform: "", url: "" });
    }
  };

  const removeSocialProfile = (index: number) => {
    const updatedProfiles = [...socialProfiles];
    updatedProfiles.splice(index, 1);
    setSocialProfiles(updatedProfiles);
  };

  // Calculate brand identity completion score
  const calculateCompletionScore = (): number => {
    const values = form.getValues();
    let score = 0;
    let totalFields = 0;
    
    // Core brand elements
    if (values.brandName) score += 1;
    if (values.brandTagline) score += 1;
    if (values.brandDescription && values.brandDescription.length > 20) score += 1;
    totalFields += 3;
    
    // Visual elements
    if (values.brandLogoUrl) score += 1;
    if (values.brandFaviconUrl) score += 1;
    if (values.brandColors.primary && values.brandColors.primary !== "#000000") score += 1;
    if (values.brandColors.secondary && values.brandColors.secondary !== "#000000") score += 1;
    totalFields += 4;
    
    // Social profiles
    if (socialProfiles.length > 0) score += Math.min(socialProfiles.length, 3);
    totalFields += 3;
    
    return Math.round((score / totalFields) * 100);
  };

  const onSubmit = (data: BrandIdentityFormValues) => {
    // Ensure colors from state are used
    data.brandColors = brandColors;
    
    // Add social profiles from state
    data.socialProfiles = socialProfiles;
    
    // 构建正确的数据结构
    const brandSettingsData = {
      brandName: data.brandName,
      brandTagline: data.brandTagline,
      brandDescription: data.brandDescription,
      brandLogoUrl: data.brandLogoUrl || "",
      brandFaviconUrl: data.brandFaviconUrl,
      brandColors: data.brandColors,
      socialProfiles,
    };
    
    // Call the onSave function with the section identifier and form data
    if (onSave) {
      setIsSaving(true);
      
      // 调用主页面的 handleSave 函数，传递正确的参数
      console.log("Submitting brand settings:", brandSettingsData);
      
      // 确保传递的是对象而不是字符串
      const dataToSave = {
        brandSettings: brandSettingsData
      };
      
      onSave("brand-identity", dataToSave);
      
      // 模拟保存延迟，提供更好的用户体验
      setTimeout(() => {
        setIsSaving(false);
      }, 500);
    } else {
      console.log({
        brandSettings: brandSettingsData
      });
    }
  }

  return (
    <div className="space-y-6">
      <Tabs defaultValue="settings" onValueChange={setActiveTab}>
        <div className="flex justify-between items-center mb-4">
          <TabsList>
            <TabsTrigger value="settings">Brand Settings</TabsTrigger>
            <TabsTrigger value="preview">Brand Preview</TabsTrigger>
          </TabsList>
          
          <div className="flex items-center space-x-2">
            <div className="text-sm font-medium">Brand Completion:</div>
            <div className="w-40">
              <Progress value={calculateCompletionScore()} className="h-2" />
            </div>
            <Badge variant={calculateCompletionScore() > 70 ? "default" : "outline"}>
              {calculateCompletionScore()}%
            </Badge>
          </div>
        </div>
        
        <TabsContent value="settings">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <span>Brand Information</span>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button variant="ghost" size="icon" className="ml-2">
                            <HelpCircle className="h-4 w-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="max-w-xs">Your brand information helps search engines understand your identity and improves your presence in search results.</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </CardTitle>
                  <CardDescription>
                    Define your brand&apos;s core identity elements
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="brandName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Brand Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Your Brand Name" {...field} />
                        </FormControl>
                        <FormDescription>
                          The official name of your brand or company
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="brandTagline"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Brand Tagline</FormLabel>
                        <FormControl>
                          <Input placeholder="Your brand&apos;s slogan or tagline" {...field} />
                        </FormControl>
                        <FormDescription>
                          A short, memorable phrase that represents your brand
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="brandDescription"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Brand Description</FormLabel>
                        <FormControl>
                          <Textarea 
                            placeholder="A brief description of your brand, its mission, and values" 
                            className="h-24"
                            {...field} 
                          />
                        </FormControl>
                        <FormDescription>
                          This description may be used in various SEO contexts, including schema markup
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Visual Identity</CardTitle>
                  <CardDescription>
                    Define your brand&apos;s visual elements
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="brandLogoUrl"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Brand Logo URL</FormLabel>
                          <div className="flex gap-2">
                            <FormControl>
                              <Input placeholder="https://example.com/logo.png" {...field} />
                            </FormControl>
                            <Button type="button" variant="outline" size="icon">
                              <Upload className="h-4 w-4" />
                            </Button>
                          </div>
                          <FormDescription>
                            URL to your brand logo (recommended size: 1200×630 pixels)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="brandFaviconUrl"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Favicon URL</FormLabel>
                          <div className="flex gap-2">
                            <FormControl>
                              <Input placeholder="https://example.com/favicon.ico" {...field} />
                            </FormControl>
                            <Button type="button" variant="outline" size="icon">
                              <Upload className="h-4 w-4" />
                            </Button>
                          </div>
                          <FormDescription>
                            URL to your favicon (recommended size: 32×32 pixels)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="brandColors.primary"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Primary Brand Color</FormLabel>
                          <div className="flex items-center gap-2">
                            <div 
                              className="w-8 h-8 rounded-md border cursor-pointer" 
                              style={{ backgroundColor: brandColors.primary }}
                            />
                            <FormControl>
                              <Input 
                                value={brandColors.primary} 
                                onChange={(e) => setBrandColors({ ...brandColors, primary: e.target.value })}
                                className="w-32"
                              />
                            </FormControl>
                            <Popover>
                              <PopoverTrigger asChild>
                                <Button variant="outline" size="icon" className="h-8 w-8">
                                  <Palette className="h-4 w-4" />
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent className="w-auto p-0 border-none">
                                <TwitterPicker 
                                  color={brandColors.primary} 
                                  onChange={(color: ColorResult) => setBrandColors({ ...brandColors, primary: color.hex })}
                                  triangle="hide"
                                />
                              </PopoverContent>
                            </Popover>
                          </div>
                          <FormDescription>
                            Primary color for your brand
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="brandColors.secondary"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Secondary Brand Color</FormLabel>
                          <div className="flex items-center gap-2">
                            <div 
                              className="w-8 h-8 rounded-md border cursor-pointer" 
                              style={{ backgroundColor: brandColors.secondary }}
                            />
                            <FormControl>
                              <Input 
                                value={brandColors.secondary} 
                                onChange={(e) => setBrandColors({ ...brandColors, secondary: e.target.value })}
                                className="w-32"
                              />
                            </FormControl>
                            <Popover>
                              <PopoverTrigger asChild>
                                <Button variant="outline" size="icon" className="h-8 w-8">
                                  <Palette className="h-4 w-4" />
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent className="w-auto p-0 border-none">
                                <TwitterPicker 
                                  color={brandColors.secondary} 
                                  onChange={(color: ColorResult) => setBrandColors({ ...brandColors, secondary: color.hex })}
                                  triangle="hide"
                                />
                              </PopoverContent>
                            </Popover>
                          </div>
                          <FormDescription>
                            Secondary color for your brand
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Social Media Profiles</CardTitle>
                  <CardDescription>
                    Add your social media profiles to improve your brand&apos;s online presence
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div>
                      <FormLabel>Platform</FormLabel>
                      <Input
                        placeholder="Facebook, Twitter, LinkedIn, etc."
                        value={newProfile.platform}
                        onChange={(e) => setNewProfile({ ...newProfile, platform: e.target.value })}
                      />
                    </div>
                    
                    <div className="md:col-span-2">
                      <FormLabel>Profile URL</FormLabel>
                      <div className="flex">
                        <Input
                          placeholder="https://facebook.com/yourbrand"
                          value={newProfile.url}
                          onChange={(e) => setNewProfile({ ...newProfile, url: e.target.value })}
                          className="flex-1"
                        />
                        <Button
                          type="button"
                          onClick={addSocialProfile}
                          className="ml-2"
                          disabled={!newProfile.platform || !newProfile.url}
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>

                  {socialProfiles.length > 0 ? (
                    <div className="space-y-2">
                      {socialProfiles.map((profile, index) => (
                        <div key={index} className="flex items-center justify-between p-3 border rounded-md">
                          <div className="flex items-center">
                            <div className="font-medium">{profile.platform}</div>
                            <div className="ml-4 text-sm text-muted-foreground truncate max-w-[300px]">
                              {profile.url}
                            </div>
                          </div>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => removeSocialProfile(index)}
                            className="h-8 w-8 text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-4 text-muted-foreground">
                      No social profiles added yet. Add your first profile above.
                    </div>
                  )}

                  <Alert className="bg-primary/10 text-primary dark:text-primary-foreground border-primary/20">
                    <InfoCircledIcon className="h-4 w-4" />
                    <AlertDescription>
                      Social profiles help search engines understand your brand identity and can appear in knowledge panels and rich results.
                    </AlertDescription>
                  </Alert>
                </CardContent>
              </Card>
              <div className="border-t pt-4 flex justify-between items-center">
                <div className="space-y-1">
                  <h3 className="text-sm font-medium">Best Practices</h3>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li className="flex items-center">
                      <CheckCircle2 className="h-3.5 w-3.5 mr-1 text-green-600 dark:text-green-400" />
                      Use consistent brand colors across your website
                    </li>
                    <li className="flex items-center">
                      <CheckCircle2 className="h-3.5 w-3.5 mr-1 text-green-600 dark:text-green-400" />
                      Provide high-quality logo images for better recognition
                    </li>
                    <li className="flex items-center">
                      <CheckCircle2 className="h-3.5 w-3.5 mr-1 text-green-600 dark:text-green-400" />
                      Link all your social profiles for improved brand presence
                    </li>
                  </ul>
                </div>
                
                <Button 
                  type="submit" 
                  disabled={isSaving}
                >
                  {isSaving ? (
                    <>Saving...</>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Save Brand Identity Settings
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </TabsContent>
        
        <TabsContent value="preview">
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Brand Preview</CardTitle>
                <CardDescription>
                  See how your brand will appear across the web
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-6">
                <div className="flex flex-col items-center space-y-4 md:flex-row md:space-y-0 md:space-x-6">
                  <div className="relative w-24 h-24 rounded-full overflow-hidden border-4" style={{ borderColor: brandColors.primary }}>
                    {form.watch("brandLogoUrl") ? (
                      <img 
                        src={form.watch("brandLogoUrl")} 
                        alt={`${form.watch("brandName")} logo`}
                        className="object-cover w-full h-full"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-muted">
                        <span className="text-2xl font-bold text-muted-foreground">
                          {form.watch("brandName")?.charAt(0) || "B"}
                        </span>
                      </div>
                    )}
                  </div>
                  
                  <div className="text-center md:text-left">
                    <h2 className="text-2xl font-bold" style={{ color: brandColors.primary }}>
                      {form.watch("brandName") || "Your Brand Name"}
                    </h2>
                    {form.watch("brandTagline") && (
                      <p className="text-sm mt-1" style={{ color: brandColors.secondary }}>
                        {form.watch("brandTagline")}
                      </p>
                    )}
                    <p className="mt-2 text-sm text-muted-foreground max-w-md">
                      {form.watch("brandDescription") || "Your brand description will appear here. Make sure to provide a clear and concise description of your brand."}
                    </p>
                    
                    {socialProfiles.length > 0 && (
                      <div className="mt-4 flex flex-wrap gap-2">
                        {socialProfiles.map((profile, index) => (
                          <a 
                            key={index} 
                            href={profile.url} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-muted hover:bg-muted/80 transition-colors"
                            style={{ backgroundColor: `${brandColors.primary}20` }}
                          >
                            <span className="text-xs font-bold" style={{ color: brandColors.primary }}>
                              {profile.platform.charAt(0)}
                            </span>
                          </a>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Search Result Preview</CardTitle>
                  <CardDescription>
                    How your brand might appear in search results
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="border rounded-md p-4 space-y-2 bg-card">
                    <div className="text-blue-600 dark:text-blue-400 text-xl font-medium hover:underline cursor-pointer">
                      {form.watch("brandName") || "Your Brand Name"} {form.watch("brandTagline") ? `- ${form.watch("brandTagline")}` : ""}
                    </div>
                    <div className="text-green-700 dark:text-green-400 text-sm">
                      https://www.{form.watch("brandName")?.toLowerCase().replace(/\s+/g, "") || "yourbrand"}.com
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {form.watch("brandDescription")?.substring(0, 160) || "Your brand description will appear here. Make sure to provide a clear and concise description of your brand."}
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>Brand Assets</CardTitle>
                  <CardDescription>
                    Visual elements of your brand identity
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-4">
                    <div className="text-sm font-medium">Logo:</div>
                    {form.watch("brandLogoUrl") ? (
                      <div className="w-12 h-12 rounded overflow-hidden border">
                        <img 
                          src={form.watch("brandLogoUrl")} 
                          alt="Brand logo"
                          className="object-cover w-full h-full"
                        />
                      </div>
                    ) : (
                      <div className="text-sm text-muted-foreground">No logo provided</div>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-4">
                    <div className="text-sm font-medium">Favicon:</div>
                    {form.watch("brandFaviconUrl") ? (
                      <div className="w-6 h-6 rounded overflow-hidden border">
                        <img 
                          src={form.watch("brandFaviconUrl")} 
                          alt="Favicon"
                          className="object-cover w-full h-full"
                        />
                      </div>
                    ) : (
                      <div className="text-sm text-muted-foreground">No favicon provided</div>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-4">
                    <div className="text-sm font-medium">Primary Color:</div>
                    <div className="flex items-center space-x-2">
                      <div 
                        className="w-6 h-6 rounded border" 
                        style={{ backgroundColor: brandColors.primary }}
                      />
                      <code className="text-xs">{brandColors.primary}</code>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-4">
                    <div className="text-sm font-medium">Secondary Color:</div>
                    <div className="flex items-center space-x-2">
                      <div 
                        className="w-6 h-6 rounded border" 
                        style={{ backgroundColor: brandColors.secondary }}
                      />
                      <code className="text-xs">{brandColors.secondary}</code>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
            
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <span>JSON-LD Schema Preview</span>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="ghost" size="icon" className="ml-2">
                          <HelpCircle className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="max-w-xs">This structured data helps search engines understand your brand identity and can improve your presence in search results.</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </CardTitle>
                <CardDescription>
                  Structured data that will be added to your website
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="bg-muted/50 p-4 rounded-md border border-border font-mono text-sm overflow-auto">
                  {JSON.stringify({
                    "@context": "https://schema.org",
                    "@type": "Organization",
                    "name": form.watch("brandName") || "Your Brand Name",
                    "description": form.watch("brandDescription") || "Your brand description",
                    "logo": form.watch("brandLogoUrl") || "https://example.com/logo.png",
                    "url": "https://example.com",
                    "sameAs": socialProfiles.map(profile => profile.url)
                  }, null, 2)}
                </div>
                <div className="mt-4 flex justify-end">
                  <Button variant="outline" size="sm" className="text-xs">
                    <ExternalLink className="h-3 w-3 mr-1" />
                    Test in Google Rich Results
                  </Button>
                </div>
              </CardContent>
            </Card>
            
            <Alert className="bg-primary/10 text-primary dark:text-primary-foreground border-primary/20">
              <InfoCircledIcon className="h-4 w-4" />
              <AlertTitle>SEO Impact</AlertTitle>
              <AlertDescription>
                <p className="mb-2">
                  A consistent brand identity across your website and social profiles helps search engines understand your brand and can lead to:
                </p>
                <ul className="list-disc pl-5 space-y-1">
                  <li>Enhanced knowledge panels in search results</li>
                  <li>Improved brand recognition in SERPs</li>
                  <li>Better integration with social sharing features</li>
                  <li>More accurate representation in rich results</li>
                </ul>
              </AlertDescription>
            </Alert>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
