"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Facebook } from "lucide-react";
import { SiX } from "react-icons/si";

interface SocialPreviewProps {
  type: "facebook" | "x";
  data: {
    title: string;
    description: string;
    image: string;
    url: string;
    siteName: string;
    xUsername: string;
  };
  onBack?: () => void;
}

export default function SocialPreview({ type, data, onBack }: SocialPreviewProps) {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          {type === "facebook" ? (
            <Facebook className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400" />
          ) : (
            <SiX className="h-5 w-5 mr-2 text-foreground" />
          )}
          <h3 className="font-medium text-lg">
            {type === "facebook" ? "Facebook" : "X"} Preview
          </h3>
        </div>
        {onBack && (
          <Button variant="ghost" size="sm" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Settings
          </Button>
        )}
      </div>

      <Card className="overflow-hidden max-w-lg mx-auto">
        <CardContent className="p-0">
          {type === "facebook" ? (
            <div className="border border-border rounded-md overflow-hidden">
              {/* Facebook Preview */}
              <div className="bg-muted border-b border-border">
                <img 
                  src={data.image || "https://placehold.co/1200x630/e5e7eb/a3a3a3?text=No+Image"} 
                  alt="Preview" 
                  className="w-full h-[261px] object-cover"
                  onError={(e) => {
                    e.currentTarget.src = "https://placehold.co/1200x630/e5e7eb/a3a3a3?text=Image+Error";
                  }}
                />
              </div>
              <div className="p-3 bg-card text-card-foreground">
                <div className="text-xs text-muted-foreground uppercase">{data.siteName}</div>
                                  <div className="font-bold text-blue-600 dark:text-blue-400 hover:underline mt-1 line-clamp-2">
                  {data.title || "No title provided"}
                </div>
                <div className="text-sm text-muted-foreground mt-1 line-clamp-3">
                  {data.description || "No description provided"}
                </div>
                <div className="text-xs text-muted-foreground/70 mt-2">{data.url}</div>
              </div>
            </div>
          ) : (
            <div className="border border-border rounded-md overflow-hidden">
              {/* X Preview */}
              <div className="p-3 bg-card text-card-foreground">
                <div className="flex items-start">
                  <div className="rounded-full bg-muted h-10 w-10 flex items-center justify-center text-muted-foreground font-bold">
                    {data.siteName.charAt(0)}
                  </div>
                  <div className="ml-2">
                    <div className="font-bold">{data.siteName}</div>
                    <div className="text-muted-foreground text-sm">@{data.xUsername}</div>
                  </div>
                </div>
                <div className="mt-2 text-sm">
                  Sharing: <span className="text-blue-600 dark:text-blue-400">{data.url}</span>
                </div>
              </div>
              <div className="border border-border rounded overflow-hidden mx-3 mb-3">
                <img 
                  src={data.image || "https://placehold.co/1200x600/e5e7eb/a3a3a3?text=No+Image"} 
                  alt="Preview" 
                  className="w-full h-[200px] object-cover"
                  onError={(e) => {
                    e.currentTarget.src = "https://placehold.co/1200x600/e5e7eb/a3a3a3?text=Image+Error";
                  }}
                />
                <div className="p-3 bg-card text-card-foreground">
                  <div className="font-bold line-clamp-1">
                    {data.title || "No title provided"}
                  </div>
                  <div className="text-sm text-muted-foreground mt-1 line-clamp-2">
                    {data.description || "No description provided"}
                  </div>
                  <div className="text-xs text-muted-foreground/70 mt-2">{data.url}</div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <div className="bg-primary/10 text-primary p-3 rounded-md text-sm border border-primary/20">
        <p className="font-medium mb-1">Preview Notes:</p>
        <ul className="list-disc pl-5 space-y-1">
          <li>This is an approximation of how your content might appear when shared.</li>
          <li>Actual appearance may vary based on platform updates and device types.</li>
          <li>
            {type === "facebook" 
              ? "Facebook typically crops images to maintain a 1.91:1 aspect ratio."
              : "X summary_large_image cards display images in a 2:1 aspect ratio."}
          </li>
          <li>For best results, test actual sharing on each platform.</li>
        </ul>
      </div>
    </div>
  );
}
