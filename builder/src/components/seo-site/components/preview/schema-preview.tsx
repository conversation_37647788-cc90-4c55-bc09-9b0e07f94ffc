"use client";

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";

interface SchemaPreviewProps {
  organizationSchema?: any;
  websiteSchema?: any;
}

export default function SchemaPreview({ organizationSchema, websiteSchema }: SchemaPreviewProps) {
  // 生成完整的结构化数据
  const generateFullSchema = () => {
    const schemas = [];
    
    if (organizationSchema) {
      schemas.push({
        "@context": "https://schema.org",
        "@type": "Organization",
        ...organizationSchema
      });
    }
    
    if (websiteSchema) {
      schemas.push({
        "@context": "https://schema.org",
        "@type": "WebSite",
        ...websiteSchema
      });
    }
    
    return schemas;
  };

  const fullSchema = generateFullSchema();

  if (fullSchema.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Structured Data Preview</CardTitle>
          <CardDescription>
            No structured data configured yet
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            Configure your organization and website schema to see the preview here.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Structured Data Preview</CardTitle>
          <CardDescription>
            This is how your structured data will appear in the HTML head section
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">JSON-LD Script Tag</h4>
              <div className="bg-muted/50 p-4 rounded-md border border-border font-mono text-sm whitespace-pre-wrap overflow-auto max-h-96">
                {`<script type="application/ld+json">
${JSON.stringify(fullSchema, null, 2)}
</script>`}
              </div>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">Schema Types Included</h4>
              <div className="flex flex-wrap gap-2">
                {organizationSchema && (
                  <span className="px-2 py-1 bg-primary/10 text-primary rounded text-sm border border-primary/20">
                    Organization
                  </span>
                )}
                {websiteSchema && (
                  <span className="px-2 py-1 bg-primary/10 text-primary rounded text-sm border border-primary/20">
                    WebSite
                  </span>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Alert className="bg-primary/10 text-primary dark:text-primary-foreground border-primary/20">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Testing Your Structured Data</AlertTitle>
        <AlertDescription>
          Use Google&apos;s Rich Results Test tool to validate your structured data: 
          <a 
            href="https://search.google.com/test/rich-results" 
            target="_blank" 
            rel="noopener noreferrer"
            className="underline ml-1"
          >
            Rich Results Test
          </a>
        </AlertDescription>
      </Alert>
    </div>
  );
}
