"use client";

import { <PERSON>, CardContent, CardD<PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { InfoCircledIcon } from "@radix-ui/react-icons";

interface RobotsPreviewProps {
  robotsTxt: string;
}

export default function RobotsPreview({ robotsTxt }: RobotsPreviewProps) {
  // 检查 robots.txt 内容是否包含常见的必要指令
  const hasSitemapDirective = robotsTxt.toLowerCase().includes("sitemap:");
  const hasUserAgent = robotsTxt.toLowerCase().includes("user-agent:");
  const hasDisallowDirective = robotsTxt.toLowerCase().includes("disallow:");

  // 提取 sitemap URL (如果存在)
  const sitemapMatch = robotsTxt.match(/Sitemap:\s*(.+)/i);
  const sitemapUrl = sitemapMatch ? sitemapMatch[1].trim() : null;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Robots.txt Preview</CardTitle>
        <CardDescription>
          This is how your robots.txt file will appear to search engines
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="bg-muted/50 p-4 rounded-md border border-border font-mono text-sm whitespace-pre-wrap overflow-auto max-h-96">
          {robotsTxt || "# No robots.txt content configured"}
        </div>
        <p className="text-xs text-muted-foreground mt-2">
          This file will be accessible at: yoursite.com/robots.txt
        </p>
      </CardContent>
    </Card>
  );
}
