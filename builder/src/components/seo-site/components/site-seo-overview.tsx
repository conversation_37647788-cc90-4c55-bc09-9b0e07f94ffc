"use client";

import { WebsiteSEO } from "@/lib/api/seo";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { TooltipProvider } from "@/components/ui/tooltip";
import { AlertCircle, AlertTriangle, Check, Code, FileText, Globe, Search, Settings, Share2, Shield, Smartphone, Zap } from "lucide-react";
import SEOScoreCard from "./common/seo-score-card";

interface SiteSEOOverviewProps {
  seoData: WebsiteSEO | null;
}

export default function SiteSEOOverview({ seoData }: SiteSEOOverviewProps) {
  if (!seoData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>SEO Overview</CardTitle>
          <CardDescription>No SEO data available</CardDescription>
        </CardHeader>
        <CardContent>
          <p>No SEO settings have been configured for this website yet.</p>
        </CardContent>
      </Card>
    );
  }
  
  // 确保 seoScores 存在，如果不存在则使用默认值
  const seoScores = seoData.seoScores || {
    overall: 0,
    categories: {
      technical: 0,
      content: 0,
      security: 0,
      performance: 0,
      social: 0
    },
    issues: {
      critical: 0,
      warnings: 0,
      passed: 0
    }
  };

  // 获取状态图标和颜色
  const getScoreStatus = (score: number) => {
    if (score >= 80) return { 
      color: "text-green-600 dark:text-green-400", 
      bgColor: "bg-green-500/10", 
      icon: <Check className="h-4 w-4" /> 
    };
    if (score >= 60) return { 
      color: "text-amber-600 dark:text-amber-400", 
      bgColor: "bg-amber-500/10", 
      icon: <AlertTriangle className="h-4 w-4" /> 
    };
    return { 
      color: "text-red-600 dark:text-red-400", 
      bgColor: "bg-red-500/10", 
      icon: <AlertCircle className="h-4 w-4" /> 
    };
  };

  return (
    <Card className="border-primary/20 shadow-sm">
      <CardHeader className="pb-2">
        <CardTitle>Website SEO Performance Analysis</CardTitle>
        <CardDescription>
          A comprehensive analysis of your website&apos;s SEO performance across key dimensions
        </CardDescription>
      </CardHeader>
      <CardContent className="p-6">
        <div className="grid grid-cols-1 lg:grid-cols-7 gap-6">
          {/* 左侧：总体评分 */}
          <div className="lg:col-span-2">
            <div className="flex flex-col items-center justify-center h-full p-6 border border-border rounded-lg bg-muted/30">
              <div className="relative">
                <svg className="w-32 h-32">
                  <circle
                    className="text-muted-foreground/30"
                    strokeWidth="8"
                    stroke="currentColor"
                    fill="transparent"
                    r="56"
                    cx="64"
                    cy="64"
                  />
                  <circle
                    className={`${seoScores.overall >= 80 ? 'text-green-600 dark:text-green-400' : seoScores.overall >= 60 ? 'text-amber-600 dark:text-amber-400' : 'text-red-600 dark:text-red-400'}`}
                    strokeWidth="8"
                    strokeDasharray={360}
                    strokeDashoffset={360 - (360 * seoScores.overall) / 100}
                    strokeLinecap="round"
                    stroke="currentColor"
                    fill="transparent"
                    r="56"
                    cx="64"
                    cy="64"
                  />
                </svg>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center">
                  <div className="text-4xl font-bold">{seoScores.overall}</div>
                  <div className="text-sm text-muted-foreground">Overall Score</div>
                </div>
              </div>
              
              <div className="mt-4 flex items-center gap-2">
                <div className="flex items-center">
                  <span className="inline-block w-3 h-3 rounded-full bg-green-600 dark:bg-green-400 mr-1"></span>
                  <span className="text-xs">Passed: {seoScores.issues.passed}</span>
                </div>
                <div className="flex items-center">
                  <span className="inline-block w-3 h-3 rounded-full bg-amber-600 dark:bg-amber-400 mr-1"></span>
                  <span className="text-xs">Warnings: {seoScores.issues.warnings}</span>
                </div>
                <div className="flex items-center">
                  <span className="inline-block w-3 h-3 rounded-full bg-red-600 dark:bg-red-400 mr-1"></span>
                  <span className="text-xs">Critical: {seoScores.issues.critical}</span>
                </div>
              </div>
            </div>
          </div>
          
          {/* 右侧：分类评分 */}
          <div className="lg:col-span-5">
            <div className="space-y-6">
              {/* 主要 SEO 类别评分 */}
              <div>
                <h3 className="text-sm font-medium mb-3">SEO Categories</h3>
                <div className="grid grid-cols-1 sm:grid-cols-3 lg:grid-cols-5 gap-4">
                  <TooltipProvider>
                    <SEOScoreCard 
                      score={seoScores.categories.content}
                      label="Content"
                      icon={<FileText className="h-4 w-4 mr-2" />}
                      description="Quality, relevance and structure of your website content"
                    />
                    
                    <SEOScoreCard 
                      score={seoScores.categories.technical}
                      label="Technical"
                      icon={<Code className="h-4 w-4 mr-2" />}
                      description="Technical aspects like structured data, robots.txt and sitemaps"
                    />
                    
                    <SEOScoreCard 
                      score={seoScores.categories.security}
                      label="Security"
                      icon={<Shield className="h-4 w-4 mr-2" />}
                      description="Website security settings like HTTPS and secure connections"
                    />
                    
                    <SEOScoreCard 
                      score={seoScores.categories.performance}
                      label="Performance"
                      icon={<Zap className="h-4 w-4 mr-2" />}
                      description="Website performance metrics like loading speed and resource optimization"
                    />
                    
                    <SEOScoreCard 
                      score={seoScores.categories.social}
                      label="Social"
                      icon={<Share2 className="h-4 w-4 mr-2" />}
                      description="Social media presence and sharing optimization"
                    />
                  </TooltipProvider>
                </div>
              </div>
              
              <div className="mt-4 flex items-center gap-2 p-3 bg-primary/10 text-primary rounded-md text-sm border border-primary/20">
                <Zap className="h-4 w-4 flex-shrink-0" />
                <span>
                  These site-wide SEO settings provide the foundation for all pages. Complete all settings in the tabs below to improve your overall SEO score.
                </span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
