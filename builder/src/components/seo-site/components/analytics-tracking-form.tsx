"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { SiteSEOData } from "../types";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { InfoCircledIcon } from "@radix-ui/react-icons";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ExternalLink, Save, BarChart, Code, CheckCircle2, AlertTriangle } from "lucide-react";
import { useState } from "react";

const analyticsTrackingFormSchema = z.object({
  googleAnalyticsId: z.string().regex(/^(UA|G|AW|GTM)-[A-Z0-9-]+$/i, {
    message: "Must be a valid Google Analytics, Google Tag Manager, or Google Ads ID",
  }).optional(),
  googleTagManagerId: z.string().regex(/^GTM-[A-Z0-9]+$/i, {
    message: "Must be a valid Google Tag Manager ID (e.g., GTM-XXXXXX)",
  }).optional(),
  searchConsoleVerification: z.string().optional(),
  customHeadScripts: z.string().optional(),
});

type AnalyticsTrackingFormValues = z.infer<typeof analyticsTrackingFormSchema>;

interface AnalyticsTrackingFormProps {
  initialData?: {
    analyticsSettings?: any;
  };
  onSave?: (data: Partial<SiteSEOData>) => void;
}

export default function AnalyticsTrackingForm({ initialData, onSave }: AnalyticsTrackingFormProps) {
  const [isSaving, setIsSaving] = useState(false);
  
  const form = useForm<AnalyticsTrackingFormValues>({
    resolver: zodResolver(analyticsTrackingFormSchema),
    defaultValues: {
      googleAnalyticsId: initialData?.analyticsSettings?.googleAnalyticsId || "",
      googleTagManagerId: initialData?.analyticsSettings?.googleTagManagerId || "",
      searchConsoleVerification: initialData?.analyticsSettings?.searchConsoleVerification || "",
      customHeadScripts: initialData?.analyticsSettings?.customHeadScripts || "",
    },
  });

  function onSubmit(values: AnalyticsTrackingFormValues) {
    if (onSave) {
      setIsSaving(true);
      
      // 准备提交数据
      const submitData: Partial<SiteSEOData> = {
        analyticsSettings: {
          googleAnalyticsId: values.googleAnalyticsId || "",
          googleTagManagerId: values.googleTagManagerId || "",
          searchConsoleVerification: values.searchConsoleVerification || "",
          customHeadScripts: values.customHeadScripts || "",
        }
      };
      
      // 调用保存方法
      onSave(submitData);
      
      // 模拟保存延迟，提供更好的用户体验
      setTimeout(() => {
        setIsSaving(false);
      }, 500);
    } else {
      console.log({
        analyticsSettings: {
          googleAnalyticsId: values.googleAnalyticsId || "",
          googleTagManagerId: values.googleTagManagerId || "",
          searchConsoleVerification: values.searchConsoleVerification || "",
          customHeadScripts: values.customHeadScripts || "",
        }
      });
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-4">
        <div className="flex items-start justify-between">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Analytics &amp; Tracking Settings</h2>
            <p className="text-sm text-muted-foreground mt-1">
              Configure analytics and tracking tools to monitor your website&apos;s performance and user behavior
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={() => window.open("https://analytics.google.com/", "_blank")}>
              <ExternalLink className="h-4 w-4 mr-2" />
              Google Analytics
            </Button>
            <Button variant="outline" size="sm" onClick={() => window.open("https://search.google.com/search-console", "_blank")}>
              <ExternalLink className="h-4 w-4 mr-2" />
              Search Console
            </Button>
          </div>
        </div>
        
        <Alert className="bg-primary/10 text-primary dark:text-primary-foreground border-primary/20">
          <BarChart className="h-4 w-4" />
          <AlertDescription>
            Analytics tools help you understand your audience, track conversions, and identify opportunities for SEO improvement.
          </AlertDescription>
        </Alert>
      </div>
      
      <Tabs defaultValue="settings">
        <TabsList className="mb-4">
          <TabsTrigger value="settings">Settings</TabsTrigger>
          <TabsTrigger value="implementation">Implementation</TabsTrigger>
        </TabsList>
        
        <TabsContent value="settings">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="border rounded-md p-4 space-y-4">
                <h3 className="font-medium text-lg flex items-center">
                  <BarChart className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400" />
                  Google Services
                </h3>
                
                <FormField
                  control={form.control}
                  name="googleAnalyticsId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Google Analytics ID</FormLabel>
                      <FormControl>
                        <Input placeholder="UA-XXXXXXXXX-X or G-XXXXXXXXXX" {...field} />
                      </FormControl>
                      <FormDescription>
                        Your Google Analytics tracking ID (UA-XXXXXXXXX-X for Universal Analytics or G-XXXXXXXXXX for GA4)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="googleTagManagerId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Google Tag Manager ID</FormLabel>
                      <FormControl>
                        <Input placeholder="GTM-XXXXXXX" {...field} />
                      </FormControl>
                      <FormDescription>
                        Your Google Tag Manager container ID (GTM-XXXXXXX)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="searchConsoleVerification"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Google Search Console Verification</FormLabel>
                      <FormControl>
                        <Input placeholder="google-site-verification=XXXXXXXXXXXXXXXXXXX" {...field} />
                      </FormControl>
                      <FormDescription>
                        The meta tag content value for Google Search Console verification
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Alert className="bg-primary/10 text-primary dark:text-primary-foreground border-primary/20">
                  <InfoCircledIcon className="h-4 w-4" />
                  <AlertDescription>
                    Google Search Console is essential for monitoring your site&apos;s performance in search results and identifying SEO issues.
                  </AlertDescription>
                </Alert>
              </div>

              <div className="border rounded-md p-4 space-y-4">
                <h3 className="font-medium text-lg flex items-center">
                  <Code className="h-5 w-5 mr-2 text-amber-600 dark:text-amber-400" />
                  Custom Tracking
                </h3>
                
                <FormField
                  control={form.control}
                  name="customHeadScripts"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Custom Head Scripts</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="<script>...</script>" 
                          className="font-mono h-40"
                          {...field} 
                        />
                      </FormControl>
                      <FormDescription>
                        Custom scripts to be added to the &lt;head&gt; section of your website (e.g., Facebook Pixel, Microsoft Clarity)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Alert className="bg-destructive/10 text-destructive dark:text-destructive-foreground border-destructive/20">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertTitle>Security Warning</AlertTitle>
                  <AlertDescription>
                    Only add scripts from trusted sources. Malicious scripts can compromise your website&apos;s security and harm your SEO.
                  </AlertDescription>
                </Alert>
              </div>
              
              <div className="border-t pt-4 flex justify-between items-center">
                <div className="space-y-1">
                  <h3 className="text-sm font-medium">Best Practices</h3>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li className="flex items-center">
                      <CheckCircle2 className="h-3.5 w-3.5 mr-1 text-green-600 dark:text-green-400" />
                      Use Google Tag Manager to manage all tracking scripts
                    </li>
                    <li className="flex items-center">
                      <CheckCircle2 className="h-3.5 w-3.5 mr-1 text-green-600 dark:text-green-400" />
                      Implement cookie consent for GDPR compliance
                    </li>
                    <li className="flex items-center">
                      <CheckCircle2 className="h-3.5 w-3.5 mr-1 text-green-600 dark:text-green-400" />
                      Verify ownership in Search Console for SEO insights
                    </li>
                  </ul>
                </div>
                
                <Button type="submit" disabled={isSaving}>
                  {isSaving ? (
                    <>Saving...</>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Save Analytics Settings
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </TabsContent>
        
        <TabsContent value="implementation">
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Google Analytics Implementation</CardTitle>
              </CardHeader>
              <CardContent>
                <h4 className="font-medium mb-2">Google Analytics 4 (GA4)</h4>
                <div className="bg-muted/50 p-4 rounded-md border border-border font-mono text-sm overflow-auto mb-4">
                  {`<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=${form.watch("googleAnalyticsId") || 'G-XXXXXXXXXX'}"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', '${form.watch("googleAnalyticsId") || 'G-XXXXXXXXXX'}');
</script>`}
                </div>

                <h4 className="font-medium mb-2">Universal Analytics (Legacy)</h4>
                <div className="bg-muted/50 p-4 rounded-md border border-border font-mono text-sm overflow-auto">
                  {`<!-- Global site tag (gtag.js) - Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=${form.watch("googleAnalyticsId") || 'UA-XXXXXXXXX-X'}"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', '${form.watch("googleAnalyticsId") || 'UA-XXXXXXXXX-X'}');
</script>`}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Google Tag Manager Implementation</CardTitle>
              </CardHeader>
              <CardContent>
                <h4 className="font-medium mb-2">Head Section</h4>
                <div className="bg-muted/50 p-4 rounded-md border border-border font-mono text-sm overflow-auto mb-4">
                  {`<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','${form.watch("googleTagManagerId") || 'GTM-XXXXXXX'}');</script>
<!-- End Google Tag Manager -->`}
                </div>

                <h4 className="font-medium mb-2">Body (After Opening Tag)</h4>
                <div className="bg-muted/50 p-4 rounded-md border border-border font-mono text-sm overflow-auto">
                  {`<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=${form.watch("googleTagManagerId") || 'GTM-XXXXXXX'}"
height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->`}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Google Search Console Verification</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-muted/50 p-4 rounded-md border border-border font-mono text-sm overflow-auto">
                  {`<meta name="google-site-verification" content="${form.watch("searchConsoleVerification") || 'google-site-verification=XXXXXXXXXXXXXXXXXXX'}" />`}
                </div>
              </CardContent>
            </Card>

            <Alert className="bg-primary/10 text-primary dark:text-primary-foreground border-primary/20">
              <InfoCircledIcon className="h-4 w-4" />
              <AlertTitle>Implementation Tips</AlertTitle>
              <AlertDescription>
                <ul className="list-disc pl-5 space-y-1 mt-1">
                  <li>Place Google Analytics and Search Console verification tags in the &lt;head&gt; section</li>
                  <li>Place the Google Tag Manager noscript tag immediately after the opening &lt;body&gt; tag</li>
                  <li>Consider implementing a cookie consent solution to comply with privacy regulations</li>
                  <li>Test your implementation using tag assistant tools provided by Google</li>
                </ul>
              </AlertDescription>
            </Alert>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
