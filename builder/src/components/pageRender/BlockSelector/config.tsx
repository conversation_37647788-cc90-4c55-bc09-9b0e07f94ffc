import React from 'react';
import { BlockGroup, BlockMainType } from './types';
import { HeroTextPreview, HeroImagePreview } from './previews/blocks/HeroPreview';
import { PricingSinglePreview, PricingTwoPreview } from './previews/blocks/PricingPreview';
import { FAQPreview, CTAPreview, CTAIconPreview, CTAImagePreview, CTATextPreview, NewsletterPreview } from './previews/blocks/ContentPreview';
import { TeamPreview, StatsPreview, TestimonialsPreview, LogoCloudPreview } from './previews/blocks/DisplayPreview';
import { FeaturesImagePreview, FeaturesTextPreview, IconLinksPreview, BentoPreview, BentoFivePreview } from './previews/blocks/FeaturesPreview';
import {
  LayoutDashboard,
  Star,
  User,
  Phone,
  Mail,
  DollarSign,
  Image,
  Type,
  Users,
  MessageSquare,
  Megaphone,
  BarChart,
  FileText,
  Link,
  Layout,
  Grid,
  Layers,
  HelpCircle,
  Lightbulb
} from 'lucide-react';

// For backward compatibility, export the old configuration format
export const blockOptions = [
  { type: 'PageHeader', label: 'Page Header', icon: <Mail size={20} /> },
  { type: 'HeroText', label: 'Hero Text', icon: <LayoutDashboard size={20} /> },
  { type: 'HeroImage', label: 'Hero Image', icon: <LayoutDashboard size={20} /> },
  { type: 'IconLinks', label: 'Icon Links', icon: <Mail size={20} /> },
  { type: 'LogoCloud', label: 'Logo Cloud', icon: <Star size={20} /> },
  { type: 'FAQ', label: 'FAQ', icon: <LayoutDashboard size={20} /> },
  { type: 'CTA', label: 'Call to Action', icon: <Mail size={20} /> },
  { type: 'CTAIcon', label: 'CTA Icon', icon: <Mail size={20} /> },
  { type: 'CTAImage', label: 'CTA Image', icon: <Mail size={20} /> },
  { type: 'CTAText', label: 'CTA Text', icon: <Mail size={20} /> },
  { type: 'Newsletter', label: 'Newsletter', icon: <Star size={20} /> },
  { type: 'Bento', label: 'Bento', icon: <Phone size={20} /> },
  { type: 'BentoFive', label: 'Bento Five', icon: <Grid size={20} /> },
  { type: 'Team', label: 'Team', icon: <User size={20} /> },
  { type: 'TeamSimple', label: 'Team Simple', icon: <User size={20} /> },
  { type: 'Testimonials', label: 'Testimonials', icon: <User size={20} /> },
  { type: 'Stats', label: 'Statistics', icon: <User size={20} /> },
  { type: 'PricingSingle', label: 'Pricing Single', icon: <User size={20} /> },
  { type: 'PricingTwo', label: 'Pricing Two', icon: <User size={20} /> },
  { type: 'PricingThree', label: 'Pricing Three', icon: <User size={20} /> },
  { type: 'PricingFour', label: 'Pricing Four', icon: <User size={20} /> },
  { type: 'FeaturesImage', label: 'Features Image', icon: <User size={20} /> },
  { type: 'FeaturesText', label: 'Features Text', icon: <User size={20} /> },
];

// New grouped configuration, arranged based on conversion funnel
export const blockGroups: BlockGroup[] = [
  // 1. Attention Grabbers
  {
    type: BlockMainType.Attention,
    label: 'Attention Grabbers',
    icon: <LayoutDashboard size={20} />,
    description: 'Sections that capture visitor attention at the top of your page',
    variants: [
      {
        id: 'PageHeader',
        label: 'Page Title',
        previewSVG: {
          layout: <HeroTextPreview />,
          width: 240,
          height: 120,
          aspectRatio: '2:1'
        },
        description: 'Clean page title section',
        popular: true
      },
      {
        id: 'HeroText',
        label: 'Text Hero',
        previewSVG: {
          layout: <HeroTextPreview />,
          width: 240,
          height: 120,
          aspectRatio: '2:1'
        },
        description: 'Hero section highlighting text content',
        popular: true
      },
      {
        id: 'HeroImage',
        label: 'Image Hero',
        previewSVG: {
          layout: <HeroImagePreview />,
          width: 240,
          height: 120,
          aspectRatio: '2:1'
        },
        description: 'Hero section with image and text',
        popular: true
      }
    ]
  },
  
  // 2. Trust Builders
  {
    type: BlockMainType.Trust,
    label: 'Trust Builders',
    icon: <Star size={20} />,
    description: 'Establish brand credibility and social proof',
    variants: [
      {
        id: 'LogoCloud',
        label: 'Logo Cloud',
        previewSVG: {
          layout: <LogoCloudPreview />,
          width: 240,
          height: 120,
          aspectRatio: '2:1'
        },
        description: 'Display partner brand logos',
        popular: true
      },
      {
        id: 'Testimonials',
        label: 'Testimonials',
        previewSVG: {
          layout: <TestimonialsPreview />,
          width: 240,
          height: 120,
          aspectRatio: '2:1'
        },
        description: 'Display user reviews and feedback'
      }
    ]
  },
  
  // 3. Value Proposition
  {
    type: BlockMainType.Value,
    label: 'Value Proposition',
    icon: <Layout size={20} />,
    description: 'Showcase core benefits and features of your product or service',
    variants: [
      {
        id: 'Bento',
        label: 'Bento Grid',
        previewSVG: {
          layout: <BentoPreview />,
          width: 240,
          height: 120,
          aspectRatio: '2:1'
        },
        description: 'Display features in a grid layout',
        popular: true
      },
      {
        id: 'BentoFive',
        label: 'Bento Five',
        previewSVG: {
          layout: <BentoFivePreview />,
          width: 240,
          height: 120,
          aspectRatio: '2:1'
        },
        description: 'Five-cell layout Bento component'
      },
      {
        id: 'FeaturesImage',
        label: 'Features with Image',
        previewSVG: {
          layout: <FeaturesImagePreview />,
          width: 240,
          height: 120,
          aspectRatio: '2:1'
        },
        description: 'Feature display with images'
      },
      {
        id: 'FeaturesText',
        label: 'Text Features',
        previewSVG: {
          layout: <FeaturesTextPreview />,
          width: 240,
          height: 120,
          aspectRatio: '2:1'
        },
        description: 'Text-based feature display',
        popular: true
      },
      {
        id: 'IconLinks',
        label: 'Icon Links',
        previewSVG: {
          layout: <IconLinksPreview />,
          width: 240,
          height: 120,
          aspectRatio: '2:1'
        },
        description: 'Link list with icons'
      }
    ]
  },
  
  // 4. Proof & Credibility
  {
    type: BlockMainType.Proof,
    label: 'Proof & Credibility',
    icon: <BarChart size={20} />,
    description: 'Provide evidence and data to back up your claims',
    variants: [
      {
        id: 'Stats',
        label: 'Statistics',
        previewSVG: {
          layout: <StatsPreview />,
          width: 240,
          height: 120,
          aspectRatio: '2:1'
        },
        description: 'Display key metrics',
        popular: true
      },
      {
        id: 'Team',
        label: 'Team Members',
        previewSVG: {
          layout: <TeamPreview />,
          width: 240,
          height: 120,
          aspectRatio: '2:1'
        },
        description: 'Display team member information'
      },
      {
        id: 'TeamSimple',
        label: 'Team Members Simple',
        previewSVG: {
          layout: <TeamPreview />,
          width: 240,
          height: 120,
          aspectRatio: '2:1'
        },
        description: 'Display team member information'
      }
    ]
  },
  
  // 5. Pricing Plans
  {
    type: BlockMainType.Pricing,
    label: 'Pricing Plans',
    icon: <DollarSign size={20} />,
    description: 'Display pricing options for your product or service',
    variants: [
      {
        id: 'PricingSingle',
        label: 'Single Plan',
        previewSVG: {
          layout: <PricingSinglePreview />,
          width: 240,
          height: 160,
          aspectRatio: '3:4'
        },
        description: 'Single pricing plan'
      },
      {
        id: 'PricingTwo',
        label: 'Two Plans',
        previewSVG: {
          layout: <PricingTwoPreview />,
          width: 240,
          height: 160,
          aspectRatio: '3:4'
        },
        description: 'Two pricing plans comparison',
        popular: true
      },
      {
        id: 'PricingThree',
        label: 'Three Plans',
        previewSVG: {
          layout: <PricingTwoPreview />,
          width: 240,
          height: 160,
          aspectRatio: '3:4'
        },
        description: 'Three pricing plans',
        popular: true
      },
      {
        id: 'PricingFour',
        label: 'Four Plans',
        previewSVG: {
          layout: <PricingTwoPreview />,
          width: 240,
          height: 160,
          aspectRatio: '3:4'
        },
        description: 'Four pricing plans'
      }
    ]
  },
  
  // 6. Objection Handling
  {
    type: BlockMainType.Objection,
    label: 'Objection Handling',
    icon: <MessageSquare size={20} />,
    description: 'Address concerns and remove purchase barriers',
    variants: [
      {
        id: 'FAQ',
        label: 'FAQ',
        previewSVG: {
          layout: <FAQPreview />,
          width: 240,
          height: 120,
          aspectRatio: '2:1'
        },
        description: 'Frequently asked questions list',
        popular: true
      }
    ]
  },
  
  // 7. Call to Action
  {
    type: BlockMainType.Action,
    label: 'Call to Action',
    icon: <Megaphone size={20} />,
    description: 'Guide visitors to take the next step',
    variants: [
      {
        id: 'CTA',
        label: 'Call to Action',
        previewSVG: {
          layout: <CTAPreview />,
          width: 240,
          height: 120,
          aspectRatio: '2:1'
        },
        description: 'Standard call to action block',
        popular: true
      },
      {
        id: 'CTAIcon',
        label: 'CTA with Icon',
        previewSVG: {
          layout: <CTAIconPreview />,
          width: 240,
          height: 120,
          aspectRatio: '2:1'
        },
        description: 'Call to action block with icon'
      },
      {
        id: 'CTAImage',
        label: 'CTA with Image',
        previewSVG: {
          layout: <CTAImagePreview />,
          width: 240,
          height: 120,
          aspectRatio: '2:1'
        },
        description: 'Call to action block with image',
        popular: true
      },
      {
        id: 'CTAText',
        label: 'CTA with Text',
        previewSVG: {
          layout: <CTATextPreview />,
          width: 240,
          height: 120,
          aspectRatio: '2:1'
        },
        description: 'Call to action block with text'
      }
    ]
  },
  
  // 8. Support & Additional Info
  {
    type: BlockMainType.Support,
    label: 'Support & Additional Info',
    icon: <HelpCircle size={20} />,
    description: 'Provide additional support and information',
    variants: [
      {
        id: 'Newsletter',
        label: 'Newsletter',
        previewSVG: {
          layout: <NewsletterPreview />,
          width: 240,
          height: 120,
          aspectRatio: '2:1'
        },
        description: 'Email subscription form'
      }
    ]
  }
];
