import { ReactNode } from 'react';

export enum BlockMainType {
  Attention = 'Attention',
  Trust = 'Trust',
  Problem = 'Problem',
  Value = 'Value',
  Proof = 'Proof',
  Pricing = 'Pricing',
  Objection = 'Objection',
  Action = 'Action',
  Support = 'Support'
}

export interface BlockPreviewSVG {
  layout: ReactNode;
  width: number;
  height: number;
  aspectRatio: string;
}

export interface BlockVariant {
  id: string;
  label: string;
  previewSVG: BlockPreviewSVG;
  description: string;
  popular?: boolean; 
}

export interface BlockGroup {
  type: BlockMainType;
  label: string;
  icon: ReactNode;
  description: string;
  variants: BlockVariant[];
}

export interface BlockSelectorProps {
  sectionId: string;
  handleAddBlock: (type: string) => void;
}
