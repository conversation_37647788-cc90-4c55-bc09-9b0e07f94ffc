import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ChevronDown, ChevronRight, Grid2X2, Search, X, Star, LayoutGrid, Sparkles, List } from 'lucide-react';
import { BlockSelectorProps, BlockMainType, BlockVariant } from './types';
import { blockGroups, blockOptions } from './config';
import { cn } from '@/lib/utils';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';

const BlockVariantPreview: React.FC<{
  variant: BlockVariant;
  onSelect: () => void;
}> = ({ variant, onSelect }) => {
  return (
    <div 
      className="block-variant-preview group cursor-pointer relative"
      onClick={onSelect}
      role="button"
      tabIndex={0}
      aria-label={`Select ${variant.label} block`}
    >
      <div className="preview-wrapper rounded-lg border border-border p-2 group-hover:border-primary transition-colors">
        <div style={{ aspectRatio: variant.previewSVG.aspectRatio }}>
          {variant.previewSVG.layout}
        </div>
      </div>
      <div className="mt-2 text-center">
        <span className="block font-medium text-sm">{variant.label}</span>
        <span className="text-xs text-muted-foreground">{variant.description}</span>
      </div>
      {variant.popular && (
        <Badge variant="secondary" className="absolute top-0 right-0 translate-x-1/4 -translate-y-1/4">
          <Star size={12} className="text-yellow-500" />
        </Badge>
      )}
    </div>
  );
};

// Helper function to find preview for a block type
const findPreviewForBlockType = (type: string): BlockVariant | undefined => {
  for (const group of blockGroups) {
    const variant = group.variants.find(v => v.id === type);
    if (variant) return variant;
  }
  return undefined;
};

const BlockSelector: React.FC<{ onSelect: (type: string) => void }> = ({ onSelect }) => {
  const [expandedGroup, setExpandedGroup] = useState<BlockMainType | null>(BlockMainType.Attention);
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'modern' | 'classic' | 'popular'>('modern');
  const [recentlyUsed, setRecentlyUsed] = useState<string[]>([]);

  // Load recently used blocks from localStorage on component mount
  useEffect(() => {
    const stored = localStorage.getItem('recentlyUsedBlocks');
    if (stored) {
      try {
        setRecentlyUsed(JSON.parse(stored));
      } catch (e) {
        console.error('Failed to parse recently used blocks', e);
      }
    }
  }, []);

  // Update recently used blocks when a block is selected
  const handleBlockSelect = (type: string) => {
    // Update recently used blocks
    const updated = [type, ...recentlyUsed.filter(t => t !== type)].slice(0, 5);
    setRecentlyUsed(updated);
    localStorage.setItem('recentlyUsedBlocks', JSON.stringify(updated));
    
    // Call the original onSelect function
    onSelect(type);
  };

  const filteredGroups = blockGroups.filter(group => {
    const matchGroup = group.label.toLowerCase().includes(searchTerm.toLowerCase());
    const matchVariants = group.variants.some(variant => 
      variant.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
      variant.description.toLowerCase().includes(searchTerm.toLowerCase())
    );
    return matchGroup || matchVariants;
  });

  const filteredOptions = blockOptions.filter(option => 
    option.label.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Get all popular blocks across all groups
  const popularBlocks = blockGroups.flatMap(group => 
    group.variants.filter(variant => variant.popular)
  );

  // Get blocks that match recently used IDs
  const recentBlocks = recentlyUsed.map(id => {
    for (const group of blockGroups) {
      const found = group.variants.find(variant => variant.id === id);
      if (found) return found;
    }
    return null;
  }).filter(Boolean) as BlockVariant[];

  return (
    <div className="space-y-4 p-4">
      <div className="sticky top-0 bg-background z-10 pb-4 space-y-4">
        <div className="relative w-full">
          <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
          <input
            type="text"
            placeholder="Search blocks..."
            className="flex-1 pl-9 pr-4 py-2 w-full rounded-md border border-input bg-background"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          {searchTerm && (
            <button 
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
              onClick={() => setSearchTerm('')}
              aria-label="Clear search"
            >
              <X size={14} />
            </button>
          )}
        </div>
        
        <Tabs defaultValue="modern" className="w-full" onValueChange={(v) => setViewMode(v as any)}>
          <TabsList className="grid grid-cols-3 w-full">
            <TabsTrigger value="modern" className="flex items-center gap-1.5" title="Modern view">
              <LayoutGrid size={14} />
              <span>Modern</span>
            </TabsTrigger>
            <TabsTrigger value="popular" className="flex items-center gap-1.5" title="Popular blocks">
              <Sparkles size={14} />
              <span>Popular</span>
            </TabsTrigger>
            <TabsTrigger value="classic" className="flex items-center gap-1.5" title="Classic view">
              <List size={14} />
              <span>Classic</span>
            </TabsTrigger>
          </TabsList>
        </Tabs>

        {recentBlocks.length > 0 && !searchTerm && (
          <div className="mt-4">
            <h3 className="text-sm font-medium mb-2">Recently Used</h3>
            <div className="grid grid-cols-3 gap-3">
              {recentBlocks.map(block => (
                <BlockVariantPreview
                  key={block.id}
                  variant={block}
                  onSelect={() => handleBlockSelect(block.id)}
                />
              ))}
            </div>
          </div>
        )}
      </div>

      {viewMode === 'classic' && (
        <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 dark:bg-gray-900 rounded-lg border border-border">
          {filteredOptions.map((block) => {
            // Find the preview for this block type if available
            const preview = findPreviewForBlockType(block.type);
            
            return (
              <div 
                key={block.type}
                className="block-variant-preview group cursor-pointer"
                onClick={() => handleBlockSelect(block.type)}
                role="button"
                tabIndex={0}
                aria-label={`Select ${block.label} block`}
              >
                <div className="preview-wrapper rounded-lg border border-border p-2 group-hover:border-primary transition-colors bg-background">
                  <div className="aspect-[2/1] flex items-center justify-center">
                    {preview ? (
                      preview.previewSVG.layout
                    ) : (
                      <div className="w-10 h-10 rounded-md flex items-center justify-center bg-primary/10 text-primary">
                        {block.icon}
                      </div>
                    )}
                  </div>
                </div>
                <div className="mt-2 text-center">
                  <span className="block font-medium text-sm">{block.label}</span>
                </div>
              </div>
            );
          })}
          {filteredOptions.length === 0 && (
            <div className="col-span-full text-center py-8 text-muted-foreground">
              No matching blocks found
            </div>
          )}
        </div>
      )}

      {viewMode === 'popular' && (
        <div className="grid grid-cols-2 gap-4 p-4">
          {popularBlocks.map(variant => (
            <BlockVariantPreview
              key={variant.id}
              variant={variant}
              onSelect={() => handleBlockSelect(variant.id)}
            />
          ))}
          {popularBlocks.length === 0 && (
            <div className="col-span-full text-center py-8 text-muted-foreground">
              No popular blocks found
            </div>
          )}
        </div>
      )}

      {viewMode === 'modern' && (
        <div className="space-y-2">
          {filteredGroups.map(group => (
            <div key={group.type} className="block-group">
              <button
                className={cn(
                  "w-full flex items-center justify-between p-2 rounded-md hover:bg-accent hover:text-accent-foreground transition-colors",
                  expandedGroup === group.type && "bg-accent text-accent-foreground"
                )}
                onClick={() => setExpandedGroup(
                  expandedGroup === group.type ? null : group.type
                )}
                aria-expanded={expandedGroup === group.type}
              >
                <div className="flex items-center gap-2">
                  {group.icon}
                  <span className="font-medium">{group.label}</span>
                  <span className="text-xs text-muted-foreground">({group.variants.length})</span>
                </div>
                {expandedGroup === group.type ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
              </button>
              
              {expandedGroup === group.type && (
                <div className="grid grid-cols-2 gap-4 p-4">
                  {group.variants
                    .filter(variant => 
                      variant.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
                      variant.description.toLowerCase().includes(searchTerm.toLowerCase())
                    )
                    .map(variant => (
                      <BlockVariantPreview
                        key={variant.id}
                        variant={variant}
                        onSelect={() => handleBlockSelect(variant.id)}
                      />
                    ))
                  }
                  {group.variants.filter(variant => 
                    variant.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    variant.description.toLowerCase().includes(searchTerm.toLowerCase())
                  ).length === 0 && (
                    <div className="col-span-full text-center py-4 text-muted-foreground">
                      No matching blocks in this category
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
          {filteredGroups.length === 0 && (
            <div className="text-center text-muted-foreground py-8">
              No matching categories found
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default BlockSelector;
