import React, { useState, useRef, useCallback, useMemo } from 'react';
import { PencilIcon, Plus, ArrowUp, ArrowDown, X, Wand, Loader2, Co<PERSON> } from 'lucide-react';
import clsx from 'clsx';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useParams } from 'next/navigation';
import { updateWebsiteNav } from '@/modules/website/service';
import { configKeys } from '@/lib/api/queryKeys';
import { useBlockEditor } from '@/context/BlockEditorContext';
import { useSectionUpdate } from '../hooks/useSectionUpdate';
import { usePreviewStore } from '@/lib/store/preview-store';
import { useFetchPage } from '@/lib/hooks/use-page-query';
import { useFetchWebsite } from '@/lib/hooks/use-website';
import { useEditVariant, useDeleteSection, useMoveSection, useCloneSection } from '@/lib/hooks/use-section-query';
import Logger from '@/lib/logger';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

const logger = new Logger('BlockContainer');

type BlockContainerProps = {
  id: string;
  type: string;
  editable: boolean;
  children: React.ReactNode;
  variants: string[];
  onVariantChange: (variant: string) => void;
};

const useSectionActions = (siteId: string, pageId: string) => {
  const queryClient = useQueryClient();

  const updateNavConfig = useMutation({
    mutationFn: updateWebsiteNav,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: configKeys.website() });
      queryClient.invalidateQueries({ queryKey: configKeys.page(pageId) });
      logger.debug(`Header/Footer variant updated, invalidating page cache for: ${pageId}`);
    },
  });

  return { 
    updateNavConfig,
    isNavUpdatePending: updateNavConfig.isPending 
  };
};

export const BlockContainer: React.FC<BlockContainerProps> = ({ 
  id, 
  type, 
  editable, 
  children, 
  variants, 
  onVariantChange 
}) => {
  const [variant, setVariant] = useState('default');
  const containerRef = useRef<HTMLDivElement>(null);
  const { curBlock, setCurBlock, setEditorOpened, setThemeOpened, setCreateSectionOpened, setScrollToBlockId } = useBlockEditor();
  const setActiveTab = usePreviewStore((state) => state.setActiveTab);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  
  // 使用 React Query 获取数据，替代 Zustand store
  const { siteId, pageId } = useParams<{ siteId: string; pageId: string }>() || { siteId: '', pageId: '' };
  const { data: currentPage } = useFetchPage(pageId);
  const { data: currentWebsite } = useFetchWebsite(siteId);
  
  // 获取 queryClient 实例，用于手动更新缓存
  const queryClient = useQueryClient();
  
  // 使用 React Query mutations 替代 Zustand store 操作
  const editVariantMutation = useEditVariant();
  const deleteSectionMutation = useDeleteSection();
  const moveSectionMutation = useMoveSection();
  const cloneSectionMutation = useCloneSection();
  const { updateNavConfig, isNavUpdatePending } = useSectionActions(siteId, pageId);

  // 合并所有操作的 pending 状态
  const isStyleChangePending = editVariantMutation.isPending || isNavUpdatePending;
  const isMovePending = moveSectionMutation.isPending;
  const isDeletePending = deleteSectionMutation.isPending;
  const isClonePending = cloneSectionMutation.isPending;

  const { handleUpdateAllSection } = useSectionUpdate();

  const sectionIndex = useMemo(() => 
    currentPage?.configuration?.sections?.findIndex((item: any) => item.id === id), 
    [currentPage, id]
  );
  
  const sectionCount = useMemo(() => 
    currentPage?.configuration?.sections?.length, 
    [currentPage]
  );

  const handleEditClick = useCallback(() => {
    setCurBlock(id);
    setEditorOpened(true);
    setActiveTab('editor'); // Switch to editor tab in preview area
    setScrollToBlockId(id); // Scroll to this block
  }, [setCurBlock, setEditorOpened, id, setActiveTab, setScrollToBlockId]);

  const handleCreateSectionClick = useCallback(() => {
    setCurBlock(id);
    setCreateSectionOpened(true);
    setActiveTab('creator'); // Switch to creator tab in preview area
  }, [setCurBlock, setCreateSectionOpened, id, setActiveTab]);

  const handleStyleChange = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    
    // 如果已经有操作在进行中，则忽略新的点击
    if (isStyleChangePending) {
      logger.debug(`Style change operation in progress, ignoring click for ${id}`);
      return;
    }
    
    const index = variants.indexOf(variant);
    const newIndex = (index + 1) % variants.length;
    const newVariant = variants[newIndex];

    setVariant(newVariant);
    onVariantChange(newVariant);

    if (type === 'Header' || type === 'Footer') {
      if (!currentWebsite?.id) {
        logger.error('Website ID is missing', new Error('Website ID is missing'));
        return;
      }

      if (!currentPage?.language) {
        logger.error('Page language is missing', new Error('Page language is missing'));
        return;
      }

      // 从 currentPage 中获取当前组件的配置数据
      const currentConfig = type === 'Header' 
        ? currentPage.header?.configuration || {}
        : currentPage.footer?.configuration || {};

      // 移除可能导致冲突的字段
      const { id: configId, ...safeConfig } = currentConfig;

      // 构造符合 NavConfig 类型的参数
      updateNavConfig.mutate({
        websiteId: currentWebsite.id,
        config: {
          type: type as 'Header' | 'Footer',
          lang: currentPage.language,
          data: {
            ...safeConfig,
            variant: newVariant, // 这个值将用于更新现有记录的 variant 字段
          }
        },
      });
    } else {
      // 使用 React Query mutation 替代 Zustand store 操作
      editVariantMutation.mutate({
        pageId,
        sectionId: id,
        variant: newVariant
      }, {
        onSuccess: () => {
          // 更新 UI
          if (currentPage?.configuration) {
            handleUpdateAllSection(currentPage.configuration);
          }
          // 确保页面缓存被更新，这样页面头部组件也会反映变化
          queryClient.invalidateQueries({ queryKey: configKeys.page(pageId) });
          logger.debug(`Section variant updated, invalidating page cache for: ${pageId}`);
          // Scroll to the block after variant change
          setScrollToBlockId(id);
        },
        onError: (error) => {
          logger.error(`Error changing variant for ${id}`, error);
        }
      });
    }
  }, [variant, variants, onVariantChange, id, type, editVariantMutation, currentPage, currentWebsite, updateNavConfig, setScrollToBlockId, pageId, queryClient, isStyleChangePending]);

  const handleMove = useCallback((direction: 'up' | 'down') => {
    // 如果已经有操作在进行中，则忽略新的点击
    if (isMovePending) {
      logger.debug(`Move operation in progress, ignoring click for ${id}`);
      return;
    }
    
    // 使用 React Query mutation 替代 Zustand store 操作
    moveSectionMutation.mutate({
      pageId,
      sectionId: id,
      direction
    }, {
      onSuccess: () => {
        // 更新 UI
        if (currentPage?.configuration) {
          handleUpdateAllSection(currentPage.configuration);
        }
        // Scroll to the block after moving
        setScrollToBlockId(id);
      },
      onError: (error) => {
        logger.error(`Error moving section ${id} ${direction}`, error);
      }
    });
  }, [id, pageId, moveSectionMutation, handleUpdateAllSection, setScrollToBlockId, isMovePending, currentPage?.configuration]);

  const handleDeleteSection = useCallback(() => {
    // 如果已经有操作在进行中，则忽略新的点击
    if (isDeletePending) {
      logger.debug(`Delete operation in progress, ignoring click for ${id}`);
      return;
    }
    
    // 使用 React Query mutation 替代 Zustand store 操作
    deleteSectionMutation.mutate({
      pageId,
      sectionId: id
    }, {
      onSuccess: () => {
        // 更新 UI
        if (currentPage?.configuration) {
          handleUpdateAllSection(currentPage.configuration);
        }
        // Reset scroll state after deletion
        setScrollToBlockId(null);
        // 关闭删除对话框
        setIsDeleteDialogOpen(false);
      },
      onError: (error) => {
        logger.error(`Error deleting section ${id}`, error);
        // 关闭删除对话框
        setIsDeleteDialogOpen(false);
      }
    });
  }, [id, pageId, deleteSectionMutation, handleUpdateAllSection, setScrollToBlockId, isDeletePending, currentPage?.configuration, setIsDeleteDialogOpen]);

  const handleCloneSection = useCallback(() => {
    // 如果已经有操作在进行中，则忽略新的点击
    if (isClonePending) {
      logger.debug(`Clone operation in progress, ignoring click for ${id}`);
      return;
    }
    
    // 使用 React Query mutation 进行区块克隆
    cloneSectionMutation.mutate({
      pageId,
      sectionId: id
    }, {
      onSuccess: () => {
        // 更新 UI
        if (currentPage?.configuration) {
          handleUpdateAllSection(currentPage.configuration);
        }
        // Scroll to the cloned block (which will be after the current block)
        setScrollToBlockId(id);
      },
      onError: (error) => {
        logger.error(`Error cloning section ${id}`, error);
      }
    });
  }, [id, pageId, cloneSectionMutation, handleUpdateAllSection, setScrollToBlockId, isClonePending, currentPage?.configuration]);

  if (!editable) return <>{children}</>;

  return (
    <div
      ref={containerRef}
      className={clsx(
        'group relative isolate', 
        'border-2 border-dashed transition-colors duration-300',
        {
          'hover:border-gray-400 rounded-md': true, 
          'border-blue-500 rounded-md': id === curBlock,
          'border-transparent': id !== curBlock,
        }
      )}
    >
      <div className={clsx(
        'absolute top-2 right-2 z-[9999] pointer-events-auto',
        'transition-opacity duration-300 flex items-center',
        id === curBlock ? 'opacity-85' : 'opacity-0 group-hover:opacity-85'
      )}>
        <TooltipProvider>
          {variants.length > 1 && (
            <Tooltip>
              <TooltipTrigger asChild>
                <button 
                  onClick={handleStyleChange}
                  className={clsx(
                    "mr-2 w-10 h-10 flex items-center justify-center rounded-full transition-all duration-300 z-[9999]",
                    "bg-blue-500 text-white shadow-sm",
                    isStyleChangePending ? "opacity-70 cursor-not-allowed" : "hover:bg-blue-600 hover:shadow-md"
                  )}
                  disabled={isStyleChangePending}
                  aria-label="Switch style variant"
                >
                  {isStyleChangePending ? (
                    <Loader2 size={20} className="animate-spin" />
                  ) : (
                    <Wand size={20} />
                  )}
                </button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Switch style variant</p>
              </TooltipContent>
            </Tooltip>
          )}
          
          <Tooltip>
            <TooltipTrigger asChild>
              <button
                className={clsx(
                  "w-10 h-10 flex items-center justify-center rounded-full transition-all duration-300 z-[9999]",
                  "bg-blue-500 text-white shadow-sm hover:bg-blue-600 hover:shadow-md"
                )}
                onClick={handleEditClick}
                aria-label="Edit block"
              >
                <PencilIcon size={20} />
              </button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Edit block</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      <div className="relative z-0 px-4">
        {children}
      </div>

      <div className={clsx(
        'flex absolute bottom-2 left-1/2 transform -translate-x-1/2',
        'space-x-2 z-[9999] pointer-events-auto transition-opacity duration-300',
        id === curBlock ? 'opacity-85' : 'opacity-0 group-hover:opacity-85'
      )}>
        <TooltipProvider>
          {type !== 'Footer' && (
            <>
              <Tooltip>
                <TooltipTrigger asChild>
                  <button 
                    className={clsx(
                      "w-9 h-9 flex items-center justify-center rounded-full",
                      "bg-gray-100 text-gray-700 shadow-sm hover:bg-gray-200 hover:shadow-md",
                      "transition-all duration-200"
                    )}
                    onClick={() => handleCreateSectionClick()}
                    aria-label="Add new block"
                  >
                    <Plus size={20} />
                  </button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Add new block</p>
                </TooltipContent>
              </Tooltip>
              
              {type !== 'Header' && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button 
                      disabled={isClonePending}
                      className={clsx(
                        "w-9 h-9 flex items-center justify-center rounded-full",
                        "bg-gray-100 text-gray-700 shadow-sm hover:bg-gray-200 hover:shadow-md",
                        "transition-all duration-200",
                        "disabled:opacity-50 disabled:cursor-not-allowed disabled:shadow-none"
                      )}
                      onClick={handleCloneSection}
                      aria-label="Clone block"
                    >
                      {isClonePending ? (
                        <Loader2 size={20} className="animate-spin" />
                      ) : (
                        <Copy size={20} />
                      )}
                    </button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Clone block</p>
                  </TooltipContent>
                </Tooltip>
              )}
            </>
          )}
          {type !== 'Header' && type !== 'Footer' && (
            <>
              <Tooltip>
                <TooltipTrigger asChild>
                  <button 
                    disabled={sectionIndex === 0 || isMovePending}
                    className={clsx(
                      "w-9 h-9 flex items-center justify-center rounded-full",
                      "bg-gray-100 text-gray-700 shadow-sm hover:bg-gray-200 hover:shadow-md",
                      "transition-all duration-200",
                      "disabled:opacity-50 disabled:cursor-not-allowed disabled:shadow-none"
                    )}
                    onClick={() => handleMove('up')}
                    aria-label="Move block up"
                  >
                    {isMovePending ? (
                      <Loader2 size={20} className="animate-spin" />
                    ) : (
                      <ArrowUp size={20} />
                    )}
                  </button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Move up</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <button 
                    disabled={sectionIndex === sectionCount - 1 || isMovePending}
                    className={clsx(
                      "w-9 h-9 flex items-center justify-center rounded-full",
                      "bg-gray-100 text-gray-700 shadow-sm hover:bg-gray-200 hover:shadow-md",
                      "transition-all duration-200",
                      "disabled:opacity-50 disabled:cursor-not-allowed disabled:shadow-none"
                    )}
                    onClick={() => handleMove('down')}
                    aria-label="Move block down"
                  >
                    {isMovePending ? (
                      <Loader2 size={20} className="animate-spin" />
                    ) : (
                      <ArrowDown size={20} />
                    )}
                  </button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Move down</p>
                </TooltipContent>
              </Tooltip>

              <AlertDialog open={isDeleteDialogOpen} onOpenChange={(open) => !open && setIsDeleteDialogOpen(false)}>
                <AlertDialogTrigger asChild>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button 
                        disabled={isDeletePending}
                        className={clsx(
                          "w-9 h-9 flex items-center justify-center rounded-full",
                          "bg-red-50 text-red-600 shadow-sm hover:bg-red-100 hover:shadow-md",
                          "transition-all duration-200",
                          "disabled:opacity-50 disabled:cursor-not-allowed disabled:shadow-none"
                        )}
                        onClick={() => setIsDeleteDialogOpen(true)}
                        aria-label="Delete block"
                      >
                        {isDeletePending ? (
                          <Loader2 size={20} className="animate-spin" />
                        ) : (
                          <X size={20} />
                        )}
                      </button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Delete block</p>
                    </TooltipContent>
                  </Tooltip>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Delete Block</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to delete this block? This action cannot be undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction onClick={handleDeleteSection} className="bg-red-600 hover:bg-red-700">
                      Delete
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </>
          )}
        </TooltipProvider>
      </div>
    </div>
  );
};
