import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm, useFieldArray } from "react-hook-form";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"

import LogoMaker from "@/components/logo-maker";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card, CardHeader, CardContent, CardTitle } from "@/components/ui/card";
import { XIcon, PlusIcon, SaveIcon } from 'lucide-react';
import { use, useState } from "react";
import { useSectionContext } from "@litpage/sections";

interface CustomField {
  name: string;
  slug: string;
}

interface HeaderFormProps {
  domain: string;
  logo?: string;
  links: CustomField[];
  actions: CustomField[];
  onSave: (data: any) => void;
}

const formSchema = z.object({
  logo: z.string().optional(),
  links: z.array(z.object({
    name: z.string().min(2, "Name should be at least 2 characters long"),
    slug: z.string().min(1, "Slug is required"),
  })),
  actions: z.array(z.object({
    name: z.string().min(2, "Name should be at least 2 characters long"),
    slug: z.string().min(1, "Slug is required"),
  })),
});

export function Header({ logo, links, actions, onSave }: HeaderFormProps) {
  let [isOpen, setIsOpen] = useState(false)
  const ctx = useSectionContext();
  // const [logoUrl, setLogoUrl] = useState(logo || 'https://litpage.imgpipe.net/logo.svg?w=100');
  
  const defaultValues = {
    logo: logo || `/logo.svg`,
    links,
    actions,
  };

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues,
  });

  const {
    fields: linkFields,
    append: appendLink,
    remove: removeLink,
  } = useFieldArray({
    control: form.control,
    name: "links",
  });

  const {
    fields: actionFields,
    append: appendAction,
    remove: removeAction,
  } = useFieldArray({
    control: form.control,
    name: "actions",
  });

  function onSubmit(values: any) {
    onSave(values);
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <Card className="shadow-lg rounded-lg">
          <CardHeader>
            <CardTitle>Website Logo</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <img src={form.watch('logo')}></img>
            <FormField
              control={form.control}
              name="logo"
              render={({ field }) => (
                <FormItem className="flex-1">
                  <FormControl>
                    <Input placeholder="Enter logo URL" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button onClick={() => setIsOpen(true)}>Design logo</Button>
            <Dialog open={isOpen} onOpenChange={setIsOpen}>
              <DialogContent className=" max-w-7xl">
                <LogoMaker domain={ctx.domain} onChange={(key: string) => { 
                  form.setValue('logo', `/${key}`);
                  setIsOpen(false) }}></LogoMaker>
              </DialogContent>
            </Dialog>
          </CardContent>

        </Card>
        <Card className="shadow-lg rounded-lg">
          <CardHeader>
            <CardTitle>Links</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {linkFields.map((field, index) => (
              <div key={field.id} className="flex items-center space-x-4 p-4 border border-dashed rounded-md">
                <FormField
                  control={form.control}
                  name={`links.${index}.name`}
                  render={({ field }) => (
                    <FormItem className="flex-1">
                      <FormControl>
                        <Input placeholder="Enter name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name={`links.${index}.slug`}
                  render={({ field }) => (
                    <FormItem className="flex-1">
                      <FormControl>
                        <Input placeholder="Enter slug" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <Button type="button" variant="ghost" onClick={() => removeLink(index)} size="icon">
                  <XIcon className="h-4 w-4" />
                </Button>
              </div>
            ))}
            <Button type="button" variant="outline" onClick={() => appendLink({ name: "", slug: "" })}>
              <PlusIcon className="mr-2" /> Add Link
            </Button>
          </CardContent>
        </Card>

        <Card className="shadow-lg rounded-lg">
          <CardHeader>
            <CardTitle>Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {actionFields.map((field, index) => (
              <div key={field.id} className="flex items-center space-x-4 p-4 border border-dashed rounded-md">
                <FormField
                  control={form.control}
                  name={`actions.${index}.name`}
                  render={({ field }) => (
                    <FormItem className="flex-1">
                      <FormControl>
                        <Input placeholder="Enter name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name={`actions.${index}.slug`}
                  render={({ field }) => (
                    <FormItem className="flex-1">
                      <FormControl>
                        <Input placeholder="Enter slug" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <Button type="button" variant="ghost" onClick={() => removeAction(index)} size="icon">
                  <XIcon className="h-4 w-4" />
                </Button>
              </div>
            ))}
            <Button type="button" variant="outline" onClick={() => appendAction({ name: "", slug: "" })}>
              <PlusIcon className="mr-2" /> Add Action
            </Button>
          </CardContent>
        </Card>

        <Button disabled={!form.formState.isDirty} type="submit" className="mt-4">
          <SaveIcon className="mr-2" /> Save
        </Button>
      </form>
    </Form>
  );
}
