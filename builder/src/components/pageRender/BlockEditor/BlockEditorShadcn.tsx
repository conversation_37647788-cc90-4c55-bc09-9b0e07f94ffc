/**
 * BlockEditorShadcn Component
 * 
 * Block editor component using Shadcn UI theme for editing various blocks in the page
 * 
 * @component
 */

import React, { useCallback, useEffect, useMemo } from 'react';
import { withTheme } from '@rjsf/core';
import validator from '@rjsf/validator-ajv8';
import Logger from '@/lib/logger';
import ShadcnTheme from './shadcn-theme';
// import ShadcnArrayField from './shadcn-theme/fields/ArrayField';
// import ShadcnArrayFieldTemplate from './shadcn-theme/templates/ArrayFieldTemplate';
import { 
  BlockEditorThemeConfig, 
  generateThemeClasses, 
  getAccessibilityConfig,
  // watchThemeChange,
  // detectThemeMode
} from './shadcn-theme/utils';

const logger = new Logger('BlockEditorShadcn');

// Create a form component with Shadcn theme using withTheme HOC
const ThemedForm = withTheme(ShadcnTheme);

interface BlockFormConfig {
  schema: Record<string, any>;
  uiSchema?: Record<string, any>;
}

interface BlockEditorProps {
  /** Block configuration */
  config: BlockFormConfig;
  /** Initial data */
  initialData?: any;
  /** Save callback */
  onSave: (data: any) => void;
  /** Theme configuration */
  themeConfig?: Partial<BlockEditorThemeConfig>;
}

export const BlockEditorShadcn: React.FC<BlockEditorProps> = ({
  config,
  initialData,
  onSave,
  themeConfig,
}) => {
  // // 主题状态管理
  // const [currentTheme, setCurrentTheme] = useState<'light' | 'dark'>(detectThemeMode);
  
  // // 监听主题变化
  // useEffect(() => {
  //   const cleanup = watchThemeChange(setCurrentTheme);
  //   return cleanup;
  // }, []);
  
  // 合并主题配置
  const mergedThemeConfig = useMemo(() => ({
    ...getAccessibilityConfig(),
    ...themeConfig
  }), [themeConfig]);
  
  // 生成主题类名
  const themeClasses = useMemo(() => 
    generateThemeClasses(mergedThemeConfig), 
    [mergedThemeConfig]
  );
  // Check if configuration is valid
  const isConfigValid = !!config?.schema;

  // Form submission handler
  const handleSubmit = useCallback(({ formData }: any) => {
    logger.info(`Form submitted`);
    try {
      onSave(formData);
    } catch (error) {
      logger.error(`Error saving form data`, error);
    }
  }, [onSave]);

  // Add debug logs to check array fields in schema
  useEffect(() => {
    if (config?.schema?.properties) {
      const arrayProperties = Object.entries(config.schema.properties)
        .filter(([_, prop]) => (prop as any)?.type === 'array')
        .map(([key]) => key);
      
      console.log('Array properties in schema:', {
        arrayProperties,
        hasArrayProperties: arrayProperties.length > 0,
      });
    }
  }, [config?.schema]);

  // Ensure uiSchema includes configuration for array fields
  const enhancedUiSchema = useMemo(() => {
    if (!config?.schema) {
      return {};
    }
    
    const uiSchema = { ...config.uiSchema };
    
    // Check array fields in schema
    if (config.schema.properties) {
      Object.entries(config.schema.properties).forEach(([key, prop]) => {
        if ((prop as any)?.type === 'array') {
          // Ensure array fields have ui:options configuration
          if (!uiSchema[key]) {
            uiSchema[key] = {};
          }
          
          if (!uiSchema[key]['ui:options']) {
            uiSchema[key]['ui:options'] = {};
          }
          
          // Set ui:options for array fields
          uiSchema[key]['ui:options'] = {
            ...uiSchema[key]['ui:options'],
            orderable: true,
            removable: true,
            addable: true,
          };
        }
      });
    }
    
    return uiSchema;
  }, [config]);

  // If configuration is invalid, return error message
  if (!isConfigValid) {
    logger.error(`Invalid block configuration`, new Error('Missing schema'));
    return null;
  }

  // Create custom fields and templates
  // const customFields = {
  //   ArrayField: ShadcnArrayField,
  // };
  
  // const customTemplates = {
  //   ArrayFieldTemplate: ShadcnArrayFieldTemplate,
  // };

  return (
    <div className={`w-full overflow-hidden ${themeClasses}`}>
      <ThemedForm
        schema={config.schema}
        uiSchema={enhancedUiSchema}
        formData={initialData}
        validator={validator}
        onSubmit={handleSubmit}
        className="w-full max-w-full"
        liveValidate={true}
        showErrorList={false}
        // fields={customFields}
        // templates={customTemplates}
      />
    </div>
  );
};

export default BlockEditorShadcn;
