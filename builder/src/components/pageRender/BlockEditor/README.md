# BlockEditor 组件使用指南

## 概述

BlockEditor 是一个基于 React JSON Schema Form (RJSF) 和 Shadcn UI 的表单编辑器组件，提供了完整的明暗主题适配支持。

## 特性

- ✅ **完整的明暗主题支持** - 自动适配系统主题，支持手动切换
- ✅ **语义化颜色系统** - 使用 CSS 变量，确保主题一致性
- ✅ **可访问性友好** - 支持减少动画偏好，良好的对比度
- ✅ **灵活的主题配置** - 支持间距、圆角、动画等自定义配置
- ✅ **响应式设计** - 适配各种屏幕尺寸
- ✅ **拖拽排序** - 数组字段支持拖拽重排序

## 基础使用

```tsx
import { BlockEditorShadcn } from '@/components/pageRender/BlockEditor';

const MyForm = () => {
  const config = {
    schema: {
      type: 'object',
      properties: {
        title: {
          type: 'string',
          title: '标题'
        }
      }
    }
  };

  const handleSave = (data: any) => {
    console.log('保存数据:', data);
  };

  return (
    <BlockEditorShadcn
      config={config}
      initialData={{ title: '默认标题' }}
      onSave={handleSave}
    />
  );
};
```

## 主题配置

### 基础主题配置

```tsx
import { BlockEditorShadcn, BlockEditorThemeConfig } from '@/components/pageRender/BlockEditor';

const themeConfig: Partial<BlockEditorThemeConfig> = {
  spacing: 'normal',      // 'compact' | 'normal' | 'relaxed'
  borderRadius: 'md',     // 'none' | 'sm' | 'md' | 'lg'
  animations: true,       // 是否启用动画
  colorScheme: 'auto'     // 'light' | 'dark' | 'auto'
};

<BlockEditorShadcn
  config={config}
  initialData={data}
  onSave={handleSave}
  themeConfig={themeConfig}
/>
```

### 主题检测和监听

```tsx
import { detectThemeMode, watchThemeChange } from '@/components/pageRender/BlockEditor/shadcn-theme/utils';

// 检测当前主题
const currentTheme = detectThemeMode(); // 'light' | 'dark'

// 监听主题变化
useEffect(() => {
  const cleanup = watchThemeChange((theme) => {
    console.log('主题已切换到:', theme);
  });
  
  return cleanup; // 清理监听器
}, []);
```

## 高级配置

### 自定义颜色选择器

```tsx
const uiSchema = {
  color: {
    'ui:widget': 'color',
    'ui:options': {
      predefinedColors: [
        '#ff0000', '#00ff00', '#0000ff',
        '#ffff00', '#ff00ff', '#00ffff'
      ]
    }
  }
};
```

### 数组字段配置

```tsx
const uiSchema = {
  items: {
    'ui:options': {
      addButtonText: '添加新项目',
      orderable: true,        // 启用拖拽排序
      removable: true,        // 启用删除
      addable: true          // 启用添加
    }
  }
};
```

### 帮助提示配置

```tsx
const uiSchema = {
  title: {
    'ui:options': {
      help: {
        iconType: 'info',           // 'help' | 'info' | 'alert'
        position: 'icon',           // 'icon' | 'inline' | 'both'
        tooltipSide: 'right',       // 'top' | 'right' | 'bottom' | 'left'
        tooltipAlign: 'start'       // 'start' | 'center' | 'end'
      }
    }
  }
};
```

## 样式自定义

### CSS 变量覆盖

```css
/* 自定义表单主题变量 */
:root {
  --form-border: var(--border);
  --form-accent: var(--primary);
  --form-background: var(--card);
  --form-text: var(--card-foreground);
  --form-shadow: rgba(0, 0, 0, 0.05);
  --form-separator: rgba(2, 132, 199, 0.3);
}

.dark {
  --form-shadow: rgba(255, 255, 255, 0.05);
  --form-separator: rgba(59, 130, 246, 0.4);
}
```

### 自定义类名

```tsx
const uiSchema = {
  title: {
    'ui:options': {
      classNames: 'my-custom-field-class'
    }
  }
};
```

## 可访问性

### 减少动画偏好

组件会自动检测用户的减少动画偏好设置：

```tsx
import { prefersReducedMotion, getAccessibilityConfig } from '@/components/pageRender/BlockEditor/shadcn-theme/utils';

// 检查用户是否偏好减少动画
const shouldReduceMotion = prefersReducedMotion();

// 获取可访问性友好的配置
const accessibilityConfig = getAccessibilityConfig();
```

### 键盘导航

- 所有交互元素都支持键盘导航
- 拖拽排序支持键盘操作
- 焦点管理符合 WCAG 标准

## 最佳实践

### 1. 主题一致性

```tsx
// ✅ 推荐：使用语义化类名
<div className="bg-card text-card-foreground border-border">

// ❌ 避免：硬编码颜色
<div className="bg-white text-gray-900 border-gray-200">
```

### 2. 响应式设计

```tsx
const uiSchema = {
  description: {
    'ui:widget': 'textarea',
    'ui:options': {
      classNames: 'w-full min-h-[100px] resize-y'
    }
  }
};
```

### 3. 性能优化

```tsx
// 使用 useMemo 缓存配置
const enhancedConfig = useMemo(() => ({
  ...baseConfig,
  uiSchema: {
    ...baseConfig.uiSchema,
    ...customUiSchema
  }
}), [baseConfig, customUiSchema]);
```

## 故障排除

### 主题不生效

1. 确保 HTML 根元素有正确的主题类名
2. 检查 CSS 变量是否正确定义
3. 验证 Tailwind CSS 配置

### 样式冲突

1. 使用 CSS 特异性规则
2. 检查全局样式覆盖
3. 使用 `!important` 作为最后手段

### 性能问题

1. 避免在渲染函数中创建新对象
2. 使用 `useMemo` 和 `useCallback` 优化
3. 考虑懒加载大型表单

## 示例

查看 `ThemePreview.tsx` 文件获取完整的使用示例和主题预览功能。

## 更新日志

### v2.0.0
- ✅ 完整重构主题系统
- ✅ 添加 CSS 变量支持
- ✅ 优化暗色模式适配
- ✅ 增强可访问性支持
- ✅ 添加主题配置接口

### v1.0.0
- 基础表单编辑器功能
- Shadcn UI 集成
- 基础主题支持 