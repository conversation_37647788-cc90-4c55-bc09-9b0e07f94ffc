/**
 * Shadcn 主题
 * 
 * 为 React JSON Schema Form 提供 Shadcn UI 主题
 */

import { ThemeProps } from '@rjsf/core';
// import { FormContextType, RJSFSchema, StrictRJSFSchema } from '@rjsf/utils';

// 导入样式
import './styles/form-styles.css';

// 导入模板
import ShadcnFieldTemplate from './templates/FieldTemplate';
import ShadcnObjectFieldTemplate from './templates/ObjectFieldTemplate';
import ShadcnArrayFieldTemplate from './templates/ArrayFieldTemplate';
import ShadcnErrorListTemplate from './templates/ErrorListTemplate';
import ShadcnDescriptionFieldTemplate from './templates/DescriptionFieldTemplate';
import ShadcnTitleFieldTemplate from './templates/TitleFieldTemplate';
import ShadcnSubmitButtonTemplate from './templates/SubmitButtonTemplate';

// 导入小部件
import {
  ShadcnTextWidget,
  ShadcnTextareaWidget,
  ShadcnMarkdownWidget,
  ShadcnSelectWidget,
  ShadcnCheckboxWidget,
  ShadcnRadioWidget,
  ShadcnDateWidget,
  ShadcnDateTimeWidget,
  ShadcnAltDateWidget,
  ShadcnAltDateTimeWidget,
  ShadcnEmailWidget,
  ShadcnURLWidget,
  ShadcnFileWidget,
  ShadcnUpDownWidget,
  ShadcnRangeWidget,
  ShadcnColorWidget,
  ShadcnPasswordWidget,
  ShadcnSwitchWidget,
  ShadcnButtonGroupWidget,
  ShadcnIconSelectWidget,
  ShadcnImageSelectorWidget,
} from './widgets';

// 导入字段
import { ShadcnArrayField } from './fields/ArrayField';

// 添加调试日志
console.log('ShadcnTheme initialization:', {
  hasArrayFieldTemplate: !!ShadcnArrayFieldTemplate,
  hasArrayField: !!ShadcnArrayField,
});

// 创建 Shadcn 主题 
// todo 
const ShadcnTheme: ThemeProps = {


  // 按钮模板
  // ButtonTemplates: {
  //   SubmitButton: ShadcnSubmitButtonTemplate,
  // },

  templates: {
    // 模板
    ArrayFieldTemplate: ShadcnArrayFieldTemplate,
    ObjectFieldTemplate: ShadcnObjectFieldTemplate,
    FieldTemplate: ShadcnFieldTemplate,
    ErrorListTemplate: ShadcnErrorListTemplate,
    DescriptionFieldTemplate: ShadcnDescriptionFieldTemplate,
    TitleFieldTemplate: ShadcnTitleFieldTemplate,
    ButtonTemplates: {
      SubmitButton: ShadcnSubmitButtonTemplate,
    },
  },

  // 小部件
  widgets: {
    // 基础小部件
    text: ShadcnTextWidget,
    textarea: ShadcnTextareaWidget,
    markdown: ShadcnMarkdownWidget,
    select: ShadcnSelectWidget,
    checkbox: ShadcnCheckboxWidget,
    radio: ShadcnRadioWidget,
    switch: ShadcnSwitchWidget,
    buttonGroup: ShadcnButtonGroupWidget,
    iconSelect: ShadcnIconSelectWidget,
    imageSelector: ShadcnImageSelectorWidget,

    // 日期相关小部件
    date: ShadcnDateWidget,
    datetime: ShadcnDateTimeWidget,
    'alt-date': ShadcnAltDateWidget,
    'alt-datetime': ShadcnAltDateTimeWidget,

    // 特殊输入小部件
    email: ShadcnEmailWidget,
    uri: ShadcnURLWidget,
    url: ShadcnURLWidget,
    file: ShadcnFileWidget,
    updown: ShadcnUpDownWidget,
    range: ShadcnRangeWidget,
    color: ShadcnColorWidget,
    password: ShadcnPasswordWidget,
  },

  // 字段
  fields: {
    ArrayField: ShadcnArrayField,
  },
};

// 添加调试日志
console.log('ShadcnTheme created:', {
  hasTemplates: !!ShadcnTheme.templates,
  hasFields: !!ShadcnTheme.fields,
  hasWidgets: !!ShadcnTheme.widgets,
  fieldsKeys: ShadcnTheme.fields ? Object.keys(ShadcnTheme.fields) : [],
  widgetsKeys: ShadcnTheme.widgets ? Object.keys(ShadcnTheme.widgets) : [],
  templatesKeys: Object.keys(ShadcnTheme),
});

export default ShadcnTheme;
