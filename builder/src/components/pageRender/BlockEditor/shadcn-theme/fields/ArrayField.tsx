import React, { useCallback, useEffect, useRef, useMemo } from 'react';
import { FieldProps, ArrayFieldTemplateProps, FormContextType, RJSFSchema, StrictRJSFSchema, getUiOptions, ArrayFieldTemplateItemType, ErrorSchema } from '@rjsf/utils';
import { ExtendedArrayFieldTemplateProps } from '../templates/ArrayFieldTemplate';

/**
 * Shadcn UI Array Field Component
 *
 * Used for rendering array type fields, using custom ArrayFieldTemplate
 */
export function ShadcnArrayField<
  T = any,
  S extends StrictRJSFSchema = RJSFSchema,
  F extends FormContextType = any
>({
  schema,
  uiSchema,
  idSchema,
  formData,
  errorSchema,
  registry,
  required,
  disabled,
  readonly,
  // autofocus,
  name,
  onChange,
  onBlur,
  onFocus,
}: FieldProps<T, S, F>) {
  const { schemaUtils, templates, formContext } = registry;
  const { ArrayFieldTemplate } = templates;
  const uiOptions = getUiOptions<T, S, F>(uiSchema);
  const { items } = schema;
  const itemsSchema = Array.isArray(items) ? items : [items || {}];
  const title = schema.title === undefined ? name : schema.title;
  const schemaArrayLength = Array.isArray(schema.items) ? schema.items.length : 0;
  
  // Save previous formData for comparison
  const prevFormDataRef = useRef<any[]>([]);
  
  // Use useMemo to wrap arrayData initialization to avoid recreation on every render
  const arrayData = useMemo(() => {
    return Array.isArray(formData) ? formData : [];
  }, [formData]);

  // Add debug log to check component initialization
  useEffect(() => {
    console.log('ShadcnArrayField initialized:', {
      title,
      arrayLength: arrayData.length,
      hasMinItems: !!schema.minItems,
      minItems: schema.minItems,
      hasMaxItems: !!schema.maxItems,
      maxItems: schema.maxItems,
      schemaType: schema.type,
      registryKeys: Object.keys(registry),
      hasArrayFieldTemplate: !!ArrayFieldTemplate,
      templatesKeys: Object.keys(templates),
    });
    
    // Print detailed array data information
    console.log('Initial array data:', JSON.stringify(arrayData, null, 2));
    
    // Save deep copy of initial data
    prevFormDataRef.current = JSON.parse(JSON.stringify(arrayData));
  }, [title, arrayData, schema.minItems, schema.maxItems, schema.type, registry, templates, ArrayFieldTemplate]);
  
  // Create delete item handler function
  const onDropIndexClick = useCallback((index: number) => (event: React.MouseEvent) => {
    console.log('Dropping index:', index, 'from array of length:', arrayData.length);
    console.log('Before drop - Array data:', JSON.stringify(arrayData, null, 2));
    event.preventDefault();
    
    // Check if deletion is allowed (minimum item count restriction)
    if (schema.minItems && arrayData.length <= schema.minItems) {
      console.warn('Cannot remove item: minimum items constraint violated');
      return;
    }
    
    const newFormData = JSON.parse(JSON.stringify(arrayData)); // Deep copy
    newFormData.splice(index, 1);
    console.log('After drop - New array data:', JSON.stringify(newFormData, null, 2));
    
    // Update reference
    prevFormDataRef.current = newFormData;
    
    onChange(newFormData as unknown as T);
  }, [arrayData, onChange, schema.minItems]);

  // Create reorder handler function
  const onReorderClick = useCallback((oldIndex: number, newIndex: number) => {
    console.log('Reordering from', oldIndex, 'to', newIndex, 'in array of length:', arrayData.length);
    console.log('Before reorder - Array data:', JSON.stringify(arrayData, null, 2));
    
    if (oldIndex < 0 || oldIndex >= arrayData.length || newIndex < 0 || newIndex >= arrayData.length) {
      console.error('Invalid indices for reordering:', { oldIndex, newIndex, arrayLength: arrayData.length });
      return;
    }
    
    const newFormData = JSON.parse(JSON.stringify(arrayData)); // Deep copy
    const item = newFormData[oldIndex];
    newFormData.splice(oldIndex, 1);
    newFormData.splice(newIndex, 0, item);
    console.log('After reorder - New array data:', JSON.stringify(newFormData, null, 2));
    
    // Update reference
    prevFormDataRef.current = newFormData;
    
    onChange(newFormData as unknown as T);
  }, [arrayData, onChange]);

  // Create handler function for onReorderClick in ArrayFieldTemplateItemType interface
  const itemReorderClick = useCallback((index: number, newIndex: number) => (event?: any) => {
    if (event) {
      event.preventDefault();
    }
    onReorderClick(index, newIndex);
  }, [onReorderClick]);
  
  // Create onAddIndexClick and onCopyIndexClick functions
  const onAddIndexClick = useCallback((index: number) => (event?: any) => {
    if (event) {
      event.preventDefault();
    }
    
    // Check if addition is allowed (maximum item count restriction)
    if (schema.maxItems && arrayData.length >= schema.maxItems) {
      console.warn('Cannot add item: maximum items constraint violated');
      return;
    }
    
    console.log('Adding item after index:', index);
    console.log('Before add - Array data:', JSON.stringify(arrayData, null, 2));
    
    const newFormData = JSON.parse(JSON.stringify(arrayData)); // Deep copy
    newFormData.splice(index + 1, 0, undefined as any);
    console.log('After add - New array data:', JSON.stringify(newFormData, null, 2));
    
    // Update reference
    prevFormDataRef.current = newFormData;
    
    onChange(newFormData as unknown as T);
  }, [arrayData, onChange, schema.maxItems]);
  
  const onCopyIndexClick = useCallback((index: number) => (event?: any) => {
    if (event) {
      event.preventDefault();
    }
    
    // Check if addition is allowed (maximum item count restriction)
    if (schema.maxItems && arrayData.length >= schema.maxItems) {
      console.warn('Cannot copy item: maximum items constraint violated');
      return;
    }
    
    console.log('Copying item at index:', index);
    console.log('Before copy - Array data:', JSON.stringify(arrayData, null, 2));
    console.log('Item to copy:', JSON.stringify(arrayData[index], null, 2));
    
    const newFormData = JSON.parse(JSON.stringify(arrayData)); // Deep copy entire array
    const itemToCopy = JSON.parse(JSON.stringify(arrayData[index])); // Deep copy item to be copied
    
    console.log('Deep copied item:', JSON.stringify(itemToCopy, null, 2));
    
    newFormData.splice(index + 1, 0, itemToCopy);
    console.log('After copy - New array data:', JSON.stringify(newFormData, null, 2));
    
    // Update reference
    prevFormDataRef.current = newFormData;
    
    onChange(newFormData as unknown as T);
  }, [arrayData, onChange, schema.maxItems]);

  // Create array items
  const itemElements = arrayData.map((item, index) => {
    const itemSchema = schemaArrayLength > 0 ? itemsSchema[index] : itemsSchema[0];
    const itemIdPrefix = `${idSchema.$id}_${index}`;
    const itemIdSchema = schemaUtils.toIdSchema(
      itemSchema as S,
      itemIdPrefix,
      arrayData[index],
      undefined
    );
    
    // Use Field component to render each array item
    const { fields } = registry;
    const Field = fields.SchemaField;
    
    // Determine if deletion and copying are allowed
    const canRemove = schema.minItems ? arrayData.length > schema.minItems : true;
    const canCopy = schema.maxItems ? arrayData.length < schema.maxItems : true;
    
    console.log(`Creating item element ${index}:`, {
      canRemove,
      canCopy,
      hasMinItems: !!schema.minItems,
      hasMaxItems: !!schema.maxItems,
      arrayLength: arrayData.length,
    });
    
    const itemProps: ArrayFieldTemplateItemType<T, S, F> = {
      key: itemIdPrefix,
      index,
      hasMoveUp: index > 0,
      hasMoveDown: index < arrayData.length - 1,
      hasRemove: canRemove,
      hasCopy: canCopy,
      disabled: !!disabled,
      readonly: !!readonly,
      hasToolbar: true,
      registry,
      schema,
      children: (
        <Field
          key={itemIdPrefix}
          name={index.toString()}
          required={required}
          schema={itemSchema as S}
          uiSchema={uiSchema?.items}
          errorSchema={errorSchema ? (errorSchema as any)[index] : undefined}
          idSchema={itemIdSchema}
          formData={arrayData[index]}
          onChange={(value: any) => {
            console.log(`Updating item at index ${index}:`, JSON.stringify(value, null, 2));
            console.log(`Current item at index ${index} before update:`, JSON.stringify(arrayData[index], null, 2));
            
            // Check if value is the same as current value
            const currentValueStr = JSON.stringify(arrayData[index]);
            const newValueStr = JSON.stringify(value);
            if (currentValueStr === newValueStr) {
              console.log(`No change detected for item at index ${index}, skipping update`);
              return;
            }
            
            // Get deep copy of previous array data
            const newFormData = JSON.parse(JSON.stringify(prevFormDataRef.current));
            
            // Only update item at specified index
            newFormData[index] = JSON.parse(JSON.stringify(value));
            
            console.log(`Updated item at index ${index}:`, JSON.stringify(newFormData[index], null, 2));
            console.log('Full array after update:', JSON.stringify(newFormData, null, 2));
            
            // Update reference
            prevFormDataRef.current = newFormData;
            
            onChange(newFormData as unknown as T);
          }}
          onBlur={onBlur}
          onFocus={onFocus}
          registry={registry}
          disabled={disabled}
          readonly={readonly}
        />
      ),
      onDropIndexClick,
      onReorderClick: itemReorderClick,
      onAddIndexClick,
      onCopyIndexClick,
      uiSchema: uiSchema?.items || {},
      totalItems: arrayData.length,
      canAdd: schema.maxItems ? arrayData.length < schema.maxItems : true,
      className: '',
    };
    
    // Add debug log to check itemProps
    console.log(`Item props for index ${index}:`, {
      hasRemove: itemProps.hasRemove,
      hasCopy: itemProps.hasCopy,
      hasToolbar: itemProps.hasToolbar,
      hasOnDropIndexClick: !!itemProps.onDropIndexClick,
      hasOnReorderClick: !!itemProps.onReorderClick,
      hasOnAddIndexClick: !!itemProps.onAddIndexClick,
      hasOnCopyIndexClick: !!itemProps.onCopyIndexClick,
    });
    
    return itemProps;
  });

  // Get className, ensure it's a string type
  const className = typeof uiOptions.className === 'string' ? uiOptions.className : '';

  // Create add button handler function
  const handleAddClick = useCallback((event: React.MouseEvent) => {
    if (event) {
      event.preventDefault();
    }
    
    // Check if addition is allowed (maximum item count restriction)
    if (schema.maxItems && arrayData.length >= schema.maxItems) {
      console.warn('Cannot add item: maximum items constraint violated');
      return;
    }
    
    console.log('Adding new item to the end of array');
    console.log('Before add - Array data:', JSON.stringify(arrayData, null, 2));
    
    const newFormData = JSON.parse(JSON.stringify(arrayData)); // Deep copy
    newFormData.push(undefined as any);
    
    console.log('After add - New array data:', JSON.stringify(newFormData, null, 2));
    
    // Update reference
    prevFormDataRef.current = newFormData;
    
    onChange(newFormData as unknown as T);
  }, [arrayData, onChange, schema.maxItems]);

  // Update reference whenever arrayData changes
  useEffect(() => {
    // Only update reference when array length or content changes
    const prevDataStr = JSON.stringify(prevFormDataRef.current);
    const currentDataStr = JSON.stringify(arrayData);
    
    if (prevDataStr !== currentDataStr) {
      console.log('Array data changed, updating reference');
      prevFormDataRef.current = JSON.parse(JSON.stringify(arrayData));
    }
  }, [arrayData]);

  // Create extended array field template properties
  const arrayProps: ExtendedArrayFieldTemplateProps<T, S, F> = {
    canAdd: schema.maxItems ? arrayData.length < schema.maxItems : true,
    items: itemElements,
    className,
    disabled: !!disabled,
    idSchema,
    formData: arrayData as T,
    onAddClick: handleAddClick,
    readonly: !!readonly,
    required: !!required,
    schema,
    title: typeof title === 'string' ? title : String(title || ''),
    uiSchema: uiSchema || {},
    registry,
    onDropIndexClick,
    onReorderClick,
  };
  
  // Add debug log to check properties passed to template
  console.log('Passing props to ArrayFieldTemplate:', {
    hasOnDropIndexClick: !!arrayProps.onDropIndexClick,
    hasOnReorderClick: !!arrayProps.onReorderClick,
    arrayPropsKeys: Object.keys(arrayProps),
    itemsCount: itemElements.length,
    canAdd: arrayProps.canAdd,
    templateType: ArrayFieldTemplate ? ArrayFieldTemplate.name || typeof ArrayFieldTemplate : 'undefined',
  });
  
  return <ArrayFieldTemplate {...arrayProps} />;
}

// Default export ShadcnArrayField component
export default ShadcnArrayField;
