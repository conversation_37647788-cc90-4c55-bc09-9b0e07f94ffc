import React from 'react';
import { TitleFieldProps } from '@rjsf/utils';
import { cn } from '@/lib/utils';

/**
 * Shadcn UI Title Field Template
 *
 * Responsible for rendering form and field titles
 */
const TitleFieldTemplate = ({ id, title, required, uiSchema }: TitleFieldProps) => {
  // Check if title should be displayed
  const showTitle = uiSchema?.["ui:options"]?.showTitle !== false;
  
  // Check if it's a first-level title
  const isFirstLevel = id && id.split('_').length === 2 && id.startsWith('root_');
  
  if (!title || !showTitle) {
    return null;
  }

  return (
    <div className={cn(
      "mb-6 mt-2",
      isFirstLevel && "border-b border-primary/20 pb-2"
    )}>
      <h3 
        id={id} 
        className={cn(
          "text-lg font-medium",
          isFirstLevel && "text-primary",
          required && "after:content-['*'] after:ml-0.5 after:text-red-500"
        )}
      >
        {title}
      </h3>
    </div>
  );
};

export default TitleFieldTemplate;
