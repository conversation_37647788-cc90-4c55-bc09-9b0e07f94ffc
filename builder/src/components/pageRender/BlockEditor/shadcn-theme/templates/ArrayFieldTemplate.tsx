import React, { useCallback, useState } from 'react';
import { ArrayFieldTemplateProps, ArrayFieldTemplateItemType, FormContextType, RJSFSchema, StrictRJSFSchema, getUiOptions } from '@rjsf/utils';
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors, DragEndEvent } from '@dnd-kit/core';
import { SortableContext, sortableKeyboardCoordinates, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Copy, Trash, PlusCircle, GripVertical, ChevronDown, ChevronRight, Edit, MoreHorizontal } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

// Extended ArrayFieldTemplateProps interface with additional properties
export interface ExtendedArrayFieldTemplateProps<
  T = any,
  S extends StrictRJSFSchema = RJSFSchema,
  F extends FormContextType = any
> extends ArrayFieldTemplateProps<T, S, F> {
  onDropIndexClick?: (index: number) => (event: React.MouseEvent) => void;
  onReorderClick?: (oldIndex: number, newIndex: number) => void;
}

// Item summary information interface
interface ItemSummary {
  key: string;
  title?: string;
  description?: string;
  badges?: string[];
}

// Sortable item component
function SortableArrayItem<
  T = any,
  S extends StrictRJSFSchema = RJSFSchema,
  F extends FormContextType = any
>({
  item,
  index,
  isExpanded,
  toggleExpand,
  summary,
  disabled,
  readonly,
  formData,
}: {
  item: ArrayFieldTemplateItemType<T, S, F>;
  index: number;
  isExpanded: boolean;
  toggleExpand: (key: string) => void;
  summary: ItemSummary;
  disabled?: boolean;
  readonly?: boolean;
  formData?: any;
}) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: item.key });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    zIndex: isDragging ? 10 : 'auto',
  };

  // Handle remove and copy operations
  const handleRemove = item.onDropIndexClick ? 
    item.onDropIndexClick(index) : undefined;
    
  const handleCopy = item.onCopyIndexClick ? 
    item.onCopyIndexClick(index) : undefined;

  return (
    <div 
      ref={setNodeRef} 
      style={style} 
      className={cn(
        "relative",
        isDragging ? "z-10" : ""
      )}
    >
      <Card className={cn(
        "mb-2 w-full overflow-hidden transition-all duration-200",
        "border-border bg-card text-card-foreground",
        "hover:shadow-md hover:border-primary/50",
        isExpanded ? "border-primary shadow-sm" : "",
        isDragging ? "shadow-lg border-primary" : ""
      )}>
        <CardHeader className="py-2 px-3 flex flex-row items-center space-x-0 space-y-0 flex-wrap">
          <div 
            {...attributes} 
            {...listeners}
            className="cursor-grab mr-2 text-muted-foreground hover:text-foreground active:cursor-grabbing transition-colors duration-200"
          >
            <GripVertical size={16} />
          </div>
          
          <div 
            className="flex-1 flex items-center cursor-pointer py-1 px-1 rounded-md hover:bg-accent/50 min-w-0 overflow-hidden transition-colors duration-200"
            onClick={() => toggleExpand(item.key)}
          >
            <div className="mr-1.5 text-muted-foreground flex-shrink-0">
              {isExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
            </div>
            <div className="flex-1 min-w-0 overflow-hidden">
              <div className="flex items-center flex-wrap">
                <CardTitle className="text-sm font-medium truncate mr-2 max-w-full">
                  {summary.title}
                </CardTitle>
                <div className="flex gap-1.5 flex-wrap">
                  {summary.badges && summary.badges.map((badge, i) => (
                    <Badge key={i} variant="secondary" className="text-xs px-1.5 py-0 h-5 max-w-full truncate">
                      {badge}
                    </Badge>
                  ))}
                </div>
              </div>
              {!isExpanded && summary.description && (
                <p className="text-xs text-muted-foreground mt-0.5 truncate max-w-full">
                  {summary.description}
                </p>
              )}
            </div>
          </div>
          
          <div className="flex space-x-1">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => toggleExpand(item.key)}
              title={isExpanded ? "Collapse" : "Expand"}
              className="h-7 w-7 p-0"
            >
              <Edit className="h-3.5 w-3.5" />
            </Button>
            
            {item.hasCopy && (
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  if (handleCopy) handleCopy();
                }}
                disabled={disabled || readonly || !handleCopy}
                title="Copy"
                className="h-7 w-7 p-0"
              >
                <Copy className="h-3.5 w-3.5" />
              </Button>
            )}
            
            {item.hasRemove && (
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  if (handleRemove) handleRemove(e);
                }}
                disabled={disabled || readonly || !handleRemove}
                title="Delete"
                className="h-7 w-7 p-0 text-destructive hover:text-destructive/80 hover:bg-destructive/10"
              >
                <Trash className="h-3.5 w-3.5" />
              </Button>
            )}
          </div>
        </CardHeader>
        
        {isExpanded && (
          <CardContent className="pt-2 pb-3 px-4 border-t border-border/50 bg-muted/20">
            <div className="flex flex-col w-full">
              {item.children}
            </div>
          </CardContent>
        )}
      </Card>
    </div>
  );
}

// Extract summary information from array item data
const getItemSummary = (item: any): ItemSummary => {
  if (!item) return { key: 'unknown', badges: [] };
  
  // For stable key, use id or generate random string
  const key = item.id ? `item-${item.id}` : `item-${Math.random().toString(36).substring(2, 11)}`;
  
  // 1. Extract title - try in priority order
  const titleFields = ['label', 'title', 'name', 'text', 'question', 'heading'];
  let title = '';
  for (const field of titleFields) {
    if (typeof item[field] === 'string' && item[field]) {
      title = item[field];
      break;
    }
  }
  
  // If no title found, use default title
  if (!title) {
    title = 'Item';
  }
  
  // 2. Extract description - try in priority order
  const descFields = ['description', 'desc', 'summary', 'answer', 'content'];
  let description = '';
  for (const field of descFields) {
    if (typeof item[field] === 'string' && item[field]) {
      description = item[field];
      break;
    }
  }
  
  // 3. Extract badges - try in priority order, display at most 1 (button label width is limited)
  const badgeFields = ['style', 'type', 'status', 'variant', 'size', 'category', 'priority'];
  const badges: string[] = [];
  
  // Display label mapping for common enum values
  const enumDisplayLabels: Record<string, Record<string, string>> = {
    style: {
      'primary': 'Primary',
      'secondary': 'Secondary',
      'outline': 'Outline',
      'text': 'Text Only'
    },
    size: {
      'small': 'Small',
      'medium': 'Medium',
      'large': 'Large'
    }
    // More mappings can be added...
  };
  
  // Only extract the first valid badge field
  for (const field of badgeFields) {
    if (typeof item[field] === 'string' && item[field]) {
      // Try to apply mapping, if no mapping use original value with first letter capitalized
      const value = item[field];
      const displayValue = enumDisplayLabels[field]?.[value] || capitalizeFirstLetter(value);
      badges.push(displayValue);
      break; // Only take the first valid badge
    }
  }
  
  return {
    key,
    title,
    description,
    badges
  };
};

// Helper function: capitalize first letter
const capitalizeFirstLetter = (str: string): string => {
  return str.charAt(0).toUpperCase() + str.slice(1);
};

/**
 * Array Field Template Component
 * 
 * Used to render array type fields, supports drag-and-drop sorting, adding, deleting, etc.
 * Supports expand/collapse functionality, collapsed by default
 */
export default function ArrayFieldTemplate<
  T = any,
  S extends StrictRJSFSchema = RJSFSchema,
  F extends FormContextType = any
>(props: ExtendedArrayFieldTemplateProps<T, S, F>) {
  // Add state to track expanded/collapsed state of each item
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>({});
  
  const {
    canAdd,
    className,
    disabled,
    // idSchema,
    items,
    onAddClick,
    readonly,
    required,
    // schema,
    title,
    uiSchema,
    // registry,
    formData,
  } = props;

  // Get onReorderClick function
  const onReorderClick = props.onReorderClick;

  // Get custom add button label, use default if not provided
  const uiOptions = getUiOptions(uiSchema);
  const addButtonLabel = uiOptions.addButtonLabel || "Add Item";

  // Create sensors for drag-and-drop sorting
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5, // Activate after dragging 5px
      }
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Handle drag end event
  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event;
    
    if (over && active.id !== over.id && onReorderClick) {
      const oldIndex = items.findIndex(item => item.key === active.id);
      const newIndex = items.findIndex(item => item.key === over.id);
      
      if (oldIndex !== -1 && newIndex !== -1) {
        onReorderClick(oldIndex, newIndex);
      }
    }
  }, [items, onReorderClick]);

  // Handle expand/collapse toggle
  const toggleExpand = useCallback((key: string) => {
    setExpandedItems(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  }, []);

  // Expand all items
  const expandAll = useCallback(() => {
    const newExpandedItems: Record<string, boolean> = {};
    items.forEach(item => {
      newExpandedItems[item.key] = true;
    });
    setExpandedItems(newExpandedItems);
  }, [items]);

  // Collapse all items
  const collapseAll = useCallback(() => {
    const newExpandedItems: Record<string, boolean> = {};
    items.forEach(item => {
      newExpandedItems[item.key] = false;
    });
    setExpandedItems(newExpandedItems);
  }, [items]);

  // Render title bar and actions
  const renderTitleBar = () => {
    return (
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center">
          {title && (
            <h3 className="text-lg font-medium mr-2">
              {title}
              {required && <span className="text-red-500 ml-1">*</span>}
            </h3>
          )}
          {items && items.length > 0 && (
            <Badge variant="outline" className="ml-2">
              {items.length} items
            </Badge>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          {items && items.length > 1 && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 px-2">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={expandAll}>
                  <ChevronDown className="mr-2 h-4 w-4" />
                  <span>Expand All</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={collapseAll}>
                  <ChevronRight className="mr-2 h-4 w-4" />
                  <span>Collapse All</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </div>
    );
  };

  // Render array items
  const renderArrayItems = () => {
    if (!items || items.length === 0) {
      return (
        <div className="text-gray-500 italic border border-dashed border-gray-200 rounded-md p-6 text-center">
          No items yet. Click the &quot;{String(addButtonLabel)}&quot; button to add one
        </div>
      );
    }
    
    return (
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
        modifiers={[restrictToVerticalAxis]}
      >
        <SortableContext
          items={items.map(item => item.key)}
          strategy={verticalListSortingStrategy}
        >
          <div className="space-y-1 w-full">
            {items.map((item, index) => {
              // Get expanded state for current item
              const isExpanded = !!expandedItems[item.key];
              
              // Get data summary for current item
              const itemData = Array.isArray(formData) ? formData[index] : undefined;
              const summary = getItemSummary(itemData);
              
              return (
                <SortableArrayItem<T, S, F>
                  key={item.key}
                  item={item}
                  index={index}
                  isExpanded={isExpanded}
                  toggleExpand={toggleExpand}
                  summary={summary}
                  disabled={disabled}
                  readonly={readonly}
                  formData={formData}
                />
              );
            })}
          </div>
        </SortableContext>
      </DndContext>
    );
  };

  // Render add button
  const renderAddButton = () => {
    if (!canAdd) return null;
    
    return (
      <div className="mt-3 flex justify-center">
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={onAddClick}
          disabled={disabled || readonly}
          className="w-full h-10 border-dashed"
        >
          <PlusCircle className="mr-2 h-4 w-4" />
          {String(addButtonLabel)}
        </Button>
      </div>
    );
  };

  return (
    <div className={cn("w-full", className)}>
      {renderTitleBar()}
      {renderArrayItems()}
      {renderAddButton()}
    </div>
  );
}
