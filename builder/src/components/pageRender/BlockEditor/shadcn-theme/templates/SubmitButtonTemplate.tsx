import React from 'react';
import { getSubmitButtonOptions, SubmitButtonProps } from '@rjsf/utils';

/**
 * Custom Submit Button Template
 *
 * Submit button using Shadcn UI styles
 */
const SubmitButtonTemplate = (props: SubmitButtonProps) => {
  const { uiSchema } = props;
  const { norender, submitText, props: submitButtonProps = {} } = getSubmitButtonOptions(uiSchema);

  if (norender) {
    return null;
  }

  return (
    <div className="flex justify-end mt-6 mb-2 pr-2 pb-2">
      <button
        type="submit"
        className="px-4 py-2 bg-primary text-primary-foreground hover:bg-primary/90 rounded-md font-medium shadow-sm transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
        {...submitButtonProps}
      >
        {submitText || 'Save'}
      </button>
    </div>
  );
};

export default SubmitButtonTemplate;
