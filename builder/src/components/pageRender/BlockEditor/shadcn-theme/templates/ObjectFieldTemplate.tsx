import React from 'react';
import { ObjectFieldTemplateProps, getUiOptions, getTemplate } from '@rjsf/utils';
import { cn } from '@/lib/utils';

/**
 * Shadcn UI Object Field Template
 *
 * Responsible for rendering object field layout, including title, description and properties
 */
const ObjectFieldTemplate = ({
  description,
  title,
  properties,
  required,
  uiSchema,
  idSchema,
  schema,
  // formData,
  // onAddClick,
  // disabled,
  // readonly,
  registry,
}: ObjectFieldTemplateProps) => {
  const uiOptions = getUiOptions(uiSchema);
  // const TitleFieldTemplate = getTemplate("TitleFieldTemplate", registry);
  const DescriptionFieldTemplate = getTemplate("DescriptionFieldTemplate", registry);

  // Check if it's a first-level object
  const isFirstLevel = idSchema?.$id && idSchema.$id.split('_').length === 2 && idSchema.$id.startsWith('root_');

  // Check if title should be displayed
  const showTitle = uiOptions?.showTitle !== false && title;

  // Get layout type
  const layoutType = uiOptions?.layout || 'default';

  // Group properties based on layout type
  const groupedProperties = () => {
    if (layoutType === 'grid') {
      // Grid layout - divide properties into multiple columns
      const columns = uiOptions?.gridColumns || 2;
      const result: Array<typeof properties> = [];
      for (let i = 0; i < properties.length; i = i + Number(columns)) {
        result.push(properties.slice(i, i + Number(columns)));
      }
      return result;
    } else {
      // Default layout - single column
      return [properties];
    }
  };

  return (
    <fieldset 
      className={cn(
        isFirstLevel ? "form-first-level" : "",
        "w-full min-w-0 overflow-hidden"
      )}
      id={idSchema?.$id}
    >
      {showTitle && (
        <legend id={`${idSchema?.$id}__title`} className={cn(
          isFirstLevel ? "form-first-level-title" : "",
          "truncate max-w-full"
        )}>
          {title}
          {required && <span className="required">*</span>}
        </legend>
      )}

      {description && (
        <DescriptionFieldTemplate
          id={`${idSchema?.$id}-description`}
          description={description}
          schema={schema}
          uiSchema={uiSchema}
          registry={registry}
        />
      )}

      <div className={cn(
        "space-y-4 w-full min-w-0 overflow-hidden",
        layoutType === 'grid' ? "grid-layout" : ""
      )}>
        {groupedProperties().map((group, index) => (
          <div key={index} className={cn(
            layoutType === 'grid' ? "grid grid-cols-1 md:grid-cols-2 gap-4 w-full min-w-0" : "w-full min-w-0"
          )}>
            {group.map((prop) => (
              <div key={prop.name} className="w-full min-w-0 overflow-hidden">
                {prop.content}
              </div>
            ))}
          </div>
        ))}
      </div>
    </fieldset>
  );
};

export default ObjectFieldTemplate;
