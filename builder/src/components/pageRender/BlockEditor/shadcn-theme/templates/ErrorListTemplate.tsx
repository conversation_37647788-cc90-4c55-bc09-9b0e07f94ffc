import React from 'react';
import { ErrorListProps } from '@rjsf/utils';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';

/**
 * Shadcn UI Error List Template
 *
 * Responsible for rendering list of form validation errors
 */
const ErrorListTemplate = ({ errors }: ErrorListProps) => {
  if (errors.length === 0) {
    return null;
  }

  return (
    <Alert variant="destructive" className="mb-6">
      <AlertCircle className="h-4 w-4" />
      <AlertTitle>Form Validation Errors</AlertTitle>
      <AlertDescription>
        <ul className="list-disc pl-5 mt-2 space-y-1">
          {errors.map((error, i) => (
            <li key={i}>
              {error.stack}
              {error.property && <span className="font-medium"> (Location: {error.property.replace(/^\./, '')})</span>}
            </li>
          ))}
        </ul>
      </AlertDescription>
    </Alert>
  );
};

export default ErrorListTemplate;
