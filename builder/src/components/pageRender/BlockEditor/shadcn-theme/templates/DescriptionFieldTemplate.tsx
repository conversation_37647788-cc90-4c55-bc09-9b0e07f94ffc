import React from 'react';
import { DescriptionFieldProps } from '@rjsf/utils';

/**
 * Shadcn UI Description Field Template
 *
 * Responsible for rendering form and field description information
 */
const DescriptionFieldTemplate = ({ id, description }: DescriptionFieldProps) => {
  if (!description) {
    return null;
  }

  return (
    <p id={id} className="text-sm text-gray-500 mb-4">
      {description}
    </p>
  );
};

export default DescriptionFieldTemplate;
