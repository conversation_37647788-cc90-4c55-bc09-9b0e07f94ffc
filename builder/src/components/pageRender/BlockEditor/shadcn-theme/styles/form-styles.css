/* 表单样式 */
:root {
  --form-border: var(--border);
  --form-accent: var(--primary);
  --form-background: var(--card);
  --form-text: var(--card-foreground);
  --form-shadow: rgba(0, 0, 0, 0.05);
  --form-separator: rgba(2, 132, 199, 0.3);
}

.dark {
  --form-shadow: rgba(255, 255, 255, 0.05);
  --form-separator: rgba(59, 130, 246, 0.4);
}

fieldset[id^="root_"]:not([id="root"]) {
  margin-bottom: 1.5rem;
  border-radius: 0.5rem;
  border: 1px solid hsl(var(--form-border));
  border-left: 2px solid hsl(var(--form-accent));
  box-shadow: 0 1px 3px var(--form-shadow);
  padding: 1rem;
  background-color: hsl(var(--form-background));
  color: hsl(var(--form-text));
  position: relative;
}

/* 为第一层级之间添加分隔线 */
fieldset[id^="root_"]:not([id="root"]):not(:last-child)::after {
  content: "";
  position: absolute;
  bottom: -1rem;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right, transparent, var(--form-separator), transparent);
  margin: 0 1rem;
}

/* 为第一层级之间添加间距 */
fieldset[id^="root_"]:not([id="root"]) {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
}

fieldset[id^="root_"]:not([id="root"]) legend {
  font-weight: 600;
  color: hsl(var(--form-accent));
  font-size: 1.125rem;
  line-height: 1.75rem;
  padding: 0 0.5rem;
}

.form-first-level-title {
  color: hsl(var(--form-accent));
  font-weight: 600;
  font-size: 1.125rem;
  line-height: 1.75rem;
  margin-bottom: 0.5rem;
}

/* 增加表单元素之间的间距 */
.form-group {
  margin-bottom: 1rem;
}

/* 表单组之间的分隔 */
fieldset {
  margin-bottom: 1rem;
}

/* 表单标题样式 */
legend {
  margin-bottom: 0.5rem;
}

/* 根级表单容器 */
.root-form-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* 必填字段标记 */
.required {
  color: hsl(var(--destructive));
  margin-left: 0.25rem;
}

/* 为表单添加更多间距 */
form.space-y-6 {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* 为第一层级字段添加更明显的分隔 */
form > .form-group > fieldset {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
}

/* 提交按钮样式 */
button[type="submit"] {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  border: none;
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 1px 2px var(--form-shadow);
}

button[type="submit"]:hover {
  background-color: hsl(var(--primary) / 0.9);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px var(--form-shadow);
}

button[type="submit"]:focus {
  outline: none;
  box-shadow: 0 0 0 2px hsl(var(--background)), 0 0 0 4px hsl(var(--ring));
}

button[type="submit"]:active {
  transform: translateY(0);
}

/* 暗色模式下的按钮增强 */
.dark button[type="submit"]:hover {
  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);
}
