import { getUiOptions } from '@rjsf/utils';
import { cn } from '@/lib/utils';

// Export theme-related utilities
export * from './theme';

/**
 * Extract custom class names from uiSchema
 */
export const getCustomClassNames = (uiSchema: any = {}, defaultClassName: string = '') => {
  const uiOptions = getUiOptions(uiSchema);
  const customClassName = uiOptions.classNames || '';
  
  return cn(defaultClassName, customClassName);
};

/**
 * Extract whether it's a full-width field from uiSchema
 */
export const isFullWidthField = (uiSchema: any = {}) => {
  const uiOptions = getUiOptions(uiSchema);
  return !!uiOptions.fullWidth;
};

/**
 * Extract custom placeholder from uiSchema
 */
export const getCustomPlaceholder = (uiSchema: any = {}, options: any = {}, defaultPlaceholder: string = '') => {
  const uiOptions = getUiOptions(uiSchema);
  return uiOptions.placeholder || options.placeholder || defaultPlaceholder;
};

/**
 * Extract custom label from uiSchema
 */
export const getCustomLabel = (uiSchema: any = {}, label: string = '') => {
  const uiOptions = getUiOptions(uiSchema);
  return uiOptions.label !== undefined ? uiOptions.label : label;
};

/**
 * Extract whether field is disabled from uiSchema
 */
export const isFieldDisabled = (uiSchema: any = {}, disabled: boolean = false) => {
  const uiOptions = getUiOptions(uiSchema);
  return uiOptions.disabled !== undefined ? uiOptions.disabled : disabled;
};

/**
 * Extract whether field is readonly from uiSchema
 */
export const isFieldReadOnly = (uiSchema: any = {}, readonly: boolean = false) => {
  const uiOptions = getUiOptions(uiSchema);
  return uiOptions.readonly !== undefined ? uiOptions.readonly : readonly;
};

/**
 * Get array item title
 */
export const getArrayItemTitle = (uiSchema: any = {}, defaultTitle: string = 'Item') => {
  const uiOptions = getUiOptions(uiSchema);
  return uiOptions.itemTitle || defaultTitle;
};

/**
 * Get array add button text
 */
export const getArrayAddButtonText = (uiSchema: any = {}, itemTitle: string = 'Item') => {
  const uiOptions = getUiOptions(uiSchema);
  return uiOptions.addButtonText || `Add ${itemTitle}`;
};

/**
 * Get form grouping information
 */
export const getFormGroups = (uiSchema: any = {}, properties: any[] = []) => {
  const uiOptions = getUiOptions(uiSchema);
  const groups = uiOptions.groups || [{ title: '', fields: properties.map(p => p.name) }];
  
  return groups;
};
