/**
 * Theme configuration utility functions
 *
 * Provides theme detection, configuration and style generation functionality
 */

export interface BlockEditorThemeConfig {
  colorScheme: 'light' | 'dark' | 'auto';
  accentColor?: string;
  borderRadius?: 'none' | 'sm' | 'md' | 'lg';
  spacing?: 'compact' | 'normal' | 'relaxed';
  animations?: boolean;
}

/**
 * Detect current theme mode
 */
export const detectThemeMode = (): 'light' | 'dark' => {
  if (typeof window === 'undefined') return 'light';
  
  // Check for dark class on HTML element
  if (document.documentElement.classList.contains('dark')) {
    return 'dark';
  }
  
  // Check system preference
  if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
    return 'dark';
  }
  
  return 'light';
};

/**
 * Listen for theme changes
 */
export const watchThemeChange = (callback: (theme: 'light' | 'dark') => void) => {
  if (typeof window === 'undefined') return () => {};
  
  // Listen for system theme changes
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
  const handleChange = () => callback(detectThemeMode());
  
  mediaQuery.addEventListener('change', handleChange);
  
  // Listen for DOM changes (addition/removal of dark class)
  const observer = new MutationObserver(() => {
    callback(detectThemeMode());
  });
  
  observer.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['class']
  });
  
  // Return cleanup function
  return () => {
    mediaQuery.removeEventListener('change', handleChange);
    observer.disconnect();
  };
};

/**
 * Generate theme-related CSS class names
 */
export const generateThemeClasses = (config: Partial<BlockEditorThemeConfig> = {}) => {
  const {
    spacing = 'normal',
    borderRadius = 'md',
    animations = true
  } = config;
  
  const classes = [];
  
  // Spacing classes
  switch (spacing) {
    case 'compact':
      classes.push('space-y-2');
      break;
    case 'relaxed':
      classes.push('space-y-8');
      break;
    default:
      classes.push('space-y-6');
  }
  
  // Border radius classes
  switch (borderRadius) {
    case 'none':
      classes.push('[&_.form-field]:rounded-none');
      break;
    case 'sm':
      classes.push('[&_.form-field]:rounded-sm');
      break;
    case 'lg':
      classes.push('[&_.form-field]:rounded-lg');
      break;
    default:
      classes.push('[&_.form-field]:rounded-md');
  }
  
  // Animation classes
  if (animations) {
    classes.push('transition-all duration-200');
  }
  
  return classes.join(' ');
};

/**
 * Get theme-aware color configuration
 */
export const getThemeColors = (theme: 'light' | 'dark' = detectThemeMode()) => {
  const baseColors = {
    light: {
      background: 'hsl(var(--background))',
      foreground: 'hsl(var(--foreground))',
      card: 'hsl(var(--card))',
      cardForeground: 'hsl(var(--card-foreground))',
      primary: 'hsl(var(--primary))',
      primaryForeground: 'hsl(var(--primary-foreground))',
      secondary: 'hsl(var(--secondary))',
      secondaryForeground: 'hsl(var(--secondary-foreground))',
      muted: 'hsl(var(--muted))',
      mutedForeground: 'hsl(var(--muted-foreground))',
      accent: 'hsl(var(--accent))',
      accentForeground: 'hsl(var(--accent-foreground))',
      destructive: 'hsl(var(--destructive))',
      destructiveForeground: 'hsl(var(--destructive-foreground))',
      border: 'hsl(var(--border))',
      input: 'hsl(var(--input))',
      ring: 'hsl(var(--ring))',
    },
    dark: {
      background: 'hsl(var(--background))',
      foreground: 'hsl(var(--foreground))',
      card: 'hsl(var(--card))',
      cardForeground: 'hsl(var(--card-foreground))',
      primary: 'hsl(var(--primary))',
      primaryForeground: 'hsl(var(--primary-foreground))',
      secondary: 'hsl(var(--secondary))',
      secondaryForeground: 'hsl(var(--secondary-foreground))',
      muted: 'hsl(var(--muted))',
      mutedForeground: 'hsl(var(--muted-foreground))',
      accent: 'hsl(var(--accent))',
      accentForeground: 'hsl(var(--accent-foreground))',
      destructive: 'hsl(var(--destructive))',
      destructiveForeground: 'hsl(var(--destructive-foreground))',
      border: 'hsl(var(--border))',
      input: 'hsl(var(--input))',
      ring: 'hsl(var(--ring))',
    }
  };
  
  return baseColors[theme];
};

/**
 * Get theme-aware predefined colors
 */
export const getThemeAwarePredefinedColors = (theme: 'light' | 'dark' = detectThemeMode()) => {
  const colors = {
    light: [
      '#000000', '#ffffff', '#f44336', '#e91e63', '#9c27b0', '#673ab7', 
      '#3f51b5', '#2196f3', '#03a9f4', '#00bcd4', '#009688', '#4caf50', 
      '#8bc34a', '#cddc39', '#ffeb3b', '#ffc107', '#ff9800', '#ff5722',
      '#795548', '#9e9e9e', '#607d8b'
    ],
    dark: [
      '#ffffff', '#f8fafc', '#e2e8f0', '#cbd5e1', '#94a3b8', '#64748b',
      '#475569', '#334155', '#1e293b', '#0f172a', '#000000', '#ef4444',
      '#f97316', '#eab308', '#22c55e', '#06b6d4', '#3b82f6', '#8b5cf6',
      '#ec4899', '#f43f5e', '#84cc16'
    ]
  };
  
  return colors[theme];
};

/**
 * Check if reduced motion preference is supported
 */
export const prefersReducedMotion = (): boolean => {
  if (typeof window === 'undefined') return false;
  
  return window.matchMedia && 
    window.matchMedia('(prefers-reduced-motion: reduce)').matches;
};

/**
 * Get accessibility-friendly configuration
 */
export const getAccessibilityConfig = (): Partial<BlockEditorThemeConfig> => {
  return {
    animations: !prefersReducedMotion(),
    spacing: 'normal', // Maintain standard spacing for better readability
    borderRadius: 'md' // Moderate border radius helps with visual recognition
  };
};