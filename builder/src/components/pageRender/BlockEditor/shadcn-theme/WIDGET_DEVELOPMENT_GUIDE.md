# Widget 开发指南

本文档提供了在 Shadcn Theme 中开发自定义 Widget 的完整指南，基于最新优化的 ImageSelectorWidget、IconSelectWidget 和 ImageGrid 组件实现总结而成。

## 📋 目录

- [Widget 基础概念](#widget-基础概念)
- [Widget 架构](#widget-架构)
- [开发步骤](#开发步骤)
- [最佳实践](#最佳实践)
- [常用模式](#常用模式)
- [布局优化策略](#布局优化策略)
- [用户体验优化](#用户体验优化)
- [组件复用策略](#组件复用策略)
- [国际化与文案优化](#国际化与文案优化)
- [工具函数](#工具函数)
- [示例实现](#示例实现)
- [测试指南](#测试指南)
- [重构经验总结](#重构经验总结)

## Widget 基础概念

### 什么是 Widget

Widget 是 React JSON Schema Form (RJSF) 中用于渲染特定类型表单字段的组件。每个 Widget 负责：

- 渲染用户界面
- 处理用户交互
- 管理字段状态
- 验证输入数据
- 与表单系统集成

### Widget 类型

根据功能复杂度，Widget 可以分为：

1. **简单 Widget**：如 TextWidget、CheckboxWidget
2. **复合 Widget**：如 SelectWidget、DateWidget
3. **复杂 Widget**：如 ImageSelectorWidget、IconSelectWidget、FileWidget
4. **可视化选择器 Widget**：如 IconSelectWidget、ColorPickerWidget
5. **瀑布流布局 Widget**：如优化后的 ImageGrid 组件

### Widget 设计原则

1. **单一职责**：每个 Widget 专注于一个特定功能
2. **组件复用**：优先复用现有组件而非重新实现
3. **用户体验**：提供直观的视觉反馈和交互
4. **类型安全**：使用 TypeScript 确保类型正确性
5. **可扩展性**：设计时考虑未来功能扩展
6. **一致性**：保持与整体设计系统的一致性
7. **性能优化**：合理使用懒加载、缓存等技术
8. **可访问性**：支持键盘导航和屏幕阅读器

## 🎨 布局优化策略

### 1. 瀑布流布局 (Masonry Layout)

基于 ImageGrid 组件的优化经验，瀑布流布局适用于展示不同尺寸的内容：

```css
/* 瀑布流核心样式 */
.masonry-container {
  columns: 2;                    /* 移动端 2 列 */
  column-gap: 1rem;             /* 列间距 */
  break-inside: avoid;          /* 防止内容被分割 */
}

@media (min-width: 768px) {
  .masonry-container {
    columns: 3;                 /* 平板 3 列 */
  }
}

@media (min-width: 1024px) {
  .masonry-container {
    columns: 4;                 /* 桌面 4 列 */
  }
}

.masonry-item {
  break-inside: avoid;          /* 防止项目被分割 */
  margin-bottom: 1rem;          /* 项目间距 */
}
```

### 2. 响应式网格布局

```css
/* 自适应网格 */
.responsive-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

/* 固定比例网格 */
.aspect-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.aspect-grid > * {
  aspect-ratio: 1 / 1;          /* 正方形 */
}
```

### 3. 布局选择指南

| 内容类型 | 推荐布局 | 适用场景 |
|----------|----------|----------|
| **不同尺寸图片** | 瀑布流 | 保持原始比例，视觉自然 |
| **相同尺寸内容** | 固定网格 | 整齐统一，易于浏览 |
| **卡片内容** | 响应式网格 | 内容长度不一，需要灵活布局 |
| **图标选择** | 固定比例网格 | 统一尺寸，便于比较 |

## 🚀 用户体验优化

### 1. 加载状态优化

```typescript
// 智能骨架屏 - 模拟真实内容布局
const LoadingSkeleton = () => (
  <div className="columns-2 md:columns-3 lg:columns-4 gap-4 space-y-4">
    {Array.from({ length: 12 }).map((_, index) => (
      <div key={index} className="break-inside-avoid mb-4">
        <Skeleton className={`w-full rounded-lg ${
          index % 3 === 0 ? 'h-48' : index % 3 === 1 ? 'h-64' : 'h-56'
        }`} />
      </div>
    ))}
  </div>
);

// 懒加载图片
<img
  src={imageUrl}
  loading="lazy"
  className="transition-all duration-300"
  onLoad={() => setIsLoading(false)}
  onError={() => setHasError(true)}
/>
```

### 2. 交互反馈优化

```typescript
// 多层次的交互反馈
const InteractiveCard = ({ onClick, isSelected, isLoading }) => (
  <div className={`
    relative group cursor-pointer
    transition-all duration-300 ease-out
    hover:scale-105 hover:shadow-lg
    ${isSelected ? 'ring-2 ring-primary' : ''}
    ${isLoading ? 'opacity-50' : ''}
  `}>
    {/* 内容 */}
    
    {/* 悬停遮罩 */}
    <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
    
    {/* 加载状态 */}
    {isLoading && (
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="bg-white/90 backdrop-blur-sm rounded-full p-3">
          <Loader2 className="h-6 w-6 animate-spin text-primary" />
        </div>
      </div>
    )}
  </div>
);
```

### 3. 空状态优化

```typescript
// 友好的空状态设计
const EmptyState = ({ title, description, action }) => (
  <div className="flex flex-col items-center justify-center h-64 text-muted-foreground">
    <ImageIcon className="w-16 h-16 mb-4 opacity-50" />
    <p className="text-center text-base font-medium mb-2">{title}</p>
    <p className="text-center text-sm mb-4">{description}</p>
    {action && (
      <Button variant="outline" onClick={action.onClick}>
        {action.label}
      </Button>
    )}
  </div>
);
```

### 4. 错误处理优化

```typescript
// 结构化错误处理
interface ErrorState {
  type: 'network' | 'validation' | 'permission' | 'unknown';
  message: string;
  retryable: boolean;
}

const ErrorDisplay = ({ error, onRetry }: { error: ErrorState; onRetry?: () => void }) => (
  <Alert variant="destructive" className="mb-4">
    <AlertCircle className="h-4 w-4" />
    <AlertTitle>
      {error.type === 'network' ? '网络错误' : 
       error.type === 'validation' ? '验证错误' : 
       error.type === 'permission' ? '权限错误' : '未知错误'}
    </AlertTitle>
    <AlertDescription className="flex items-center justify-between">
      <span>{error.message}</span>
      {error.retryable && onRetry && (
        <Button variant="outline" size="sm" onClick={onRetry}>
          重试
        </Button>
      )}
    </AlertDescription>
  </Alert>
);
```

## 标准属性

| 属性 | 类型 | 描述 |
|------|------|------|
| `id` | string | 字段唯一标识符 |
| `value` | any | 当前字段值 |
| `onChange` | function | 值变化回调 |
| `onBlur` | function | 失焦回调 |
| `onFocus` | function | 聚焦回调 |
| `disabled` | boolean | 是否禁用 |
| `readonly` | boolean | 是否只读 |
| `required` | boolean | 是否必填 |
| `schema` | object | JSON Schema 定义 |
| `uiSchema` | object | UI Schema 配置 |
| `options` | object | 额外选项 |
| `label` | string | 字段标签 |
| `placeholder` | string | 占位符文本 |

## 开发步骤

### 1. 创建 Widget 文件

在 `widgets/` 目录下创建新的 Widget 文件：

```typescript
// widgets/MyCustomWidget.tsx
import React, { useState, useEffect } from 'react';
import { WidgetProps } from '@rjsf/utils';
import { getCustomClassNames, getCustomPlaceholder } from '../utils';

/**
 * 自定义 Widget
 * 
 * 描述 Widget 的功能和用途
 */
interface MyCustomWidgetProps extends WidgetProps {
  // 自定义属性
}

const MyCustomWidget = ({
  id,
  value,
  onChange,
  onBlur,
  onFocus,
  disabled,
  readonly,
  options,
  schema,
  uiSchema,
  label,
  required,
  placeholder,
}: MyCustomWidgetProps) => {
  // Widget 实现
  return (
    <div>
      {/* Widget UI */}
    </div>
  );
};

export default MyCustomWidget;
```

### 2. 实现核心功能

#### 状态管理

```typescript
const [localState, setLocalState] = useState(value);
const [isLoading, setIsLoading] = useState(false);
const [error, setError] = useState<string | null>(null);

// 同步外部值变化
useEffect(() => {
  setLocalState(value);
}, [value]);
```

#### 事件处理

```typescript
const handleChange = (newValue: any) => {
  setLocalState(newValue);
  onChange && onChange(newValue);
};

const handleBlur = () => {
  onBlur && onBlur(id, localState);
};

const handleFocus = () => {
  onFocus && onFocus(id, localState);
};
```

### 3. 样式和主题

```typescript
const wrapperClassName = getCustomClassNames(uiSchema, 'default-classes');
const customPlaceholder = getCustomPlaceholder(uiSchema, options, placeholder);
```

### 4. 注册 Widget

在 `widgets/index.ts` 中导出：

```typescript
export { default as MyCustomWidget } from './MyCustomWidget';
```

在主题配置中注册：

```typescript
// index.ts
import { MyCustomWidget } from './widgets';

export const ShadcnMyCustomWidget = MyCustomWidget;
```

## 最佳实践

### 1. 错误处理

```typescript
const [error, setError] = useState<string | null>(null);

const handleError = (errorMessage: string) => {
  setError(errorMessage);
  console.error('Widget Error:', errorMessage);
};

// 在 UI 中显示错误
{error && (
  <div className="text-red-500 text-sm mt-1">
    {error}
  </div>
)}
```

### 2. 加载状态

```typescript
const [isLoading, setIsLoading] = useState(false);

// 异步操作
const handleAsyncOperation = async () => {
  setIsLoading(true);
  try {
    // 执行操作
  } catch (error) {
    handleError('操作失败');
  } finally {
    setIsLoading(false);
  }
};
```

### 3. 可访问性

```typescript
<input
  id={id}
  aria-describedby={`${id}-description ${id}-error`}
  aria-required={required}
  aria-invalid={!!error}
  disabled={disabled || readonly}
/>
```

### 4. 性能优化

```typescript
// 使用 useCallback 优化回调函数
const handleChange = useCallback((newValue: any) => {
  onChange && onChange(newValue);
}, [onChange]);

// 使用 useMemo 优化计算
const computedValue = useMemo(() => {
  return expensiveComputation(value);
}, [value]);
```

## 常用模式

### 1. 简单输入 Widget

```typescript
const SimpleWidget = ({ id, value, onChange, disabled, readonly }: WidgetProps) => {
  return (
    <Input
      id={id}
      value={value || ''}
      onChange={(e) => onChange(e.target.value)}
      disabled={disabled || readonly}
    />
  );
};
```

### 2. 选择器 Widget

```typescript
const SelectorWidget = ({ id, value, onChange, options }: WidgetProps) => {
  const { enumOptions } = options;
  
  return (
    <Select value={value} onValueChange={onChange}>
      <SelectTrigger>
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        {enumOptions?.map((option) => (
          <SelectItem key={option.value} value={option.value}>
            {option.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};
```

### 3. 复杂交互 Widget

```typescript
const ComplexWidget = ({ id, value, onChange }: WidgetProps) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  
  const handleSelect = (selectedValue: any) => {
    onChange(selectedValue);
    setIsDialogOpen(false);
  };
  
  return (
    <>
      <Button onClick={() => setIsDialogOpen(true)}>
        选择内容
      </Button>
      
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          {/* 复杂选择界面 */}
        </DialogContent>
      </Dialog>
    </>
  );
};
```

### 4. 可视化选择器模式（基于 IconSelectWidget 经验）

```typescript
const VisualSelectorWidget = ({ id, value, onChange, disabled, readonly }: WidgetProps) => {
  const [isPickerOpen, setIsPickerOpen] = useState(false);
  
  const handleSelect = (selectedValue: string) => {
    onChange(selectedValue);
    setIsPickerOpen(false);
  };
  
  const handleRemove = () => {
    onChange('');
  };
  
  const handleButtonClick = () => {
    if (!disabled && !readonly) {
      setIsPickerOpen(true);
    }
  };
  
  return (
    <div className="w-full">
      {value ? (
        <div className="space-y-2">
          {/* 已选择内容的预览 */}
          <div className="flex items-center justify-between p-3 border rounded-lg bg-muted/50">
            <div className="flex items-center gap-3">
              <div className="flex items-center justify-center w-10 h-10 bg-background border rounded-md">
                {/* 预览组件 */}
              </div>
              <div>
                <p className="text-sm font-medium">{value}</p>
                <p className="text-xs text-muted-foreground">Selected item</p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleButtonClick}
                disabled={disabled || readonly}
              >
                Change
              </Button>
              
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleRemove}
                disabled={disabled || readonly}
                className="text-destructive hover:text-destructive"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      ) : (
        <Button
          type="button"
          variant="outline"
          onClick={handleButtonClick}
          disabled={disabled || readonly}
          className="w-full justify-center gap-2 h-20 border-dashed border-2 hover:border-primary/50 hover:bg-muted/50 transition-all duration-200"
        >
          <div className="text-center">
            <Search className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
            <p className="text-base font-medium mb-1">Select Item</p>
            <p className="text-sm text-muted-foreground">Click to choose an item</p>
          </div>
        </Button>
      )}
      
      {/* 选择器对话框 */}
      <SelectorDialog
        isOpen={isPickerOpen}
        onOpenChange={setIsPickerOpen}
        onSelect={handleSelect}
        currentValue={value}
      />
    </div>
  );
};
```

## 组件复用策略

### 1. 识别可复用组件

在开发新 Widget 前，首先检查是否存在可复用的组件：

- **IconPicker**：用于图标选择功能
- **ImageSelector**：用于图片选择功能
- **ColorPicker**：用于颜色选择功能
- **FileUploader**：用于文件上传功能

### 2. 组件复用原则

```typescript
// ✅ 好的做法：复用现有组件
const IconSelectWidget = ({ value, onChange }: WidgetProps) => {
  return (
    <IconPicker
      isOpen={isPickerOpen}
      onOpenChange={setIsPickerOpen}
      onSelect={onChange}
      currentIcon={value}
    />
  );
};

// ❌ 避免：重新实现相同功能
const IconSelectWidget = ({ value, onChange }: WidgetProps) => {
  // 重新实现图标选择逻辑...
};
```

### 3. 组件适配策略

当现有组件不完全满足需求时，优先考虑：

1. **扩展组件接口**：添加新的 props 支持
2. **包装组件**：创建适配层
3. **配置化**：通过配置参数调整行为

## 国际化与文案优化

### 1. 文案设计原则

- **简洁明了**：使用简短、清晰的表述
- **用户友好**：提供清晰的操作指引
- **一致性**：保持整体文案风格统一
- **国际化**：考虑多语言支持

### 2. 文案最佳实践

```typescript
// ✅ 好的文案
const MESSAGES = {
  selectIcon: 'Select Icon',
  changeIcon: 'Change',
  removeIcon: 'Remove',
  selectedIcon: 'Selected icon',
  clickToChoose: 'Click to choose a Lucide icon'
};

// ❌ 避免的文案
const BAD_MESSAGES = {
  selectIcon: 'Please click here to select an icon from the available options',
  changeIcon: 'Modify Current Selection',
  removeIcon: 'Delete Selected Item',
};
```

### 3. 多语言支持

```typescript
interface WidgetMessages {
  [key: string]: string;
}

const getMessages = (locale: string): WidgetMessages => {
  const messages = {
    'en': {
      selectIcon: 'Select Icon',
      change: 'Change',
      remove: 'Remove'
    },
    'zh': {
      selectIcon: '选择图标',
      change: '更换',
      remove: '移除'
    }
  };
  
  return messages[locale] || messages['en'];
};
```

## 工具函数

### 样式工具

```typescript
import { getCustomClassNames, getCustomPlaceholder } from '../utils';

// 获取自定义样式类
const className = getCustomClassNames(uiSchema, 'default-class');

// 获取自定义占位符
const placeholder = getCustomPlaceholder(uiSchema, options, '默认占位符');
```

### 验证工具

```typescript
const validateValue = (value: any, schema: any): boolean => {
  // 实现验证逻辑
  return true;
};

const formatValue = (value: any): string => {
  // 实现格式化逻辑
  return String(value);
};
```

## 示例实现

### 颜色选择器 Widget

```typescript
import React, { useState } from 'react';
import { WidgetProps } from '@rjsf/utils';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { getCustomClassNames } from '../utils';

const ColorPickerWidget = ({
  id,
  value,
  onChange,
  disabled,
  readonly,
  uiSchema,
}: WidgetProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const wrapperClassName = getCustomClassNames(uiSchema);
  
  const colors = [
    '#FF0000', '#00FF00', '#0000FF',
    '#FFFF00', '#FF00FF', '#00FFFF',
  ];
  
  const handleColorSelect = (color: string) => {
    onChange(color);
    setIsOpen(false);
  };
  
  return (
    <div className={wrapperClassName}>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            disabled={disabled || readonly}
            className="w-full justify-start"
          >
            <div
              className="w-4 h-4 rounded mr-2 border"
              style={{ backgroundColor: value || '#000000' }}
            />
            {value || '选择颜色'}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-2">
          <div className="grid grid-cols-3 gap-2">
            {colors.map((color) => (
              <button
                key={color}
                className="w-8 h-8 rounded border hover:scale-110 transition-transform"
                style={{ backgroundColor: color }}
                onClick={() => handleColorSelect(color)}
              />
            ))}
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default ColorPickerWidget;
```

## 测试指南

### 1. 单元测试

```typescript
import { render, fireEvent, screen } from '@testing-library/react';
import MyCustomWidget from './MyCustomWidget';

describe('MyCustomWidget', () => {
  const defaultProps = {
    id: 'test-widget',
    value: '',
    onChange: jest.fn(),
    schema: {},
    uiSchema: {},
  };
  
  it('应该正确渲染', () => {
    render(<MyCustomWidget {...defaultProps} />);
    expect(screen.getByRole('textbox')).toBeInTheDocument();
  });
  
  it('应该处理值变化', () => {
    const onChange = jest.fn();
    render(<MyCustomWidget {...defaultProps} onChange={onChange} />);
    
    fireEvent.change(screen.getByRole('textbox'), {
      target: { value: 'new value' }
    });
    
    expect(onChange).toHaveBeenCalledWith('new value');
  });
});
```

### 2. 集成测试

```typescript
// 测试 Widget 在表单中的集成
const schema = {
  type: 'object',
  properties: {
    myField: {
      type: 'string',
      title: '我的字段'
    }
  }
};

const uiSchema = {
  myField: {
    'ui:widget': 'MyCustomWidget'
  }
};
```

## 重构经验总结

### IconSelectWidget 重构案例分析

#### 重构前的问题

1. **功能局限**：只支持预定义的图标列表
2. **用户体验差**：纯文本下拉选择，无视觉预览
3. **可扩展性差**：添加新图标需要修改代码
4. **不一致性**：与其他选择器组件体验不统一

#### 重构策略

1. **组件复用**：利用现有的 `IconPicker` 组件
2. **体验升级**：从文本选择升级为可视化选择
3. **功能完善**：支持搜索、预览、完整图标库
4. **类型安全**：修复 TypeScript 类型错误

#### 重构成果

```typescript
// 重构前：简单下拉选择
const OldIconSelectWidget = ({ value, onChange, options }: WidgetProps) => {
  const { enumOptions } = options;
  
  return (
    <Select value={value} onValueChange={onChange}>
      <SelectTrigger>
        <SelectValue placeholder="选择图标" />
      </SelectTrigger>
      <SelectContent>
        {enumOptions?.map((option) => (
          <SelectItem key={option.value} value={option.value}>
            {option.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};

// 重构后：可视化选择器
const NewIconSelectWidget = ({ value, onChange, disabled, readonly }: WidgetProps) => {
  const [isPickerOpen, setIsPickerOpen] = useState(false);
  
  // 双状态界面：已选择 vs 未选择
  // 可视化预览：使用 IconRenderer 显示图标
  // 完整功能：支持选择、更换、移除
  // 组件复用：集成 IconPicker 组件
  
  return (
    <div className="w-full">
      {/* 实现细节... */}
    </div>
  );
};
```

### ImageSelectorWidget 全面优化案例

#### 优化前的问题

1. **类型安全问题**：使用 `any` 类型，缺乏类型检查
2. **错误处理不完善**：缺少结构化的错误处理机制
3. **加载状态缺失**：没有图片加载状态反馈
4. **用户体验不佳**：文案不够友好，空状态样式简陋
5. **可访问性不足**：缺少 ARIA 属性和语义化标签

#### 优化策略

1. **类型安全改进**：定义严格的 TypeScript 接口
2. **错误处理增强**：添加 try-catch 和用户友好的错误提示
3. **加载状态管理**：完整的加载生命周期处理
4. **用户体验优化**：改进文案、样式和交互反馈
5. **可访问性提升**：添加完整的 ARIA 支持

#### 优化成果

```typescript
// 优化后的类型定义
interface ImageSelectData {
  result?: {
    id: string;
    url?: string;
  };
}

// 优化后的错误处理
const handleImageSelect = (data: ImageSelectData) => {
  try {
    if (data?.result?.id) {
      onChange(convertImageIdToUrl(data.result.id));
      setIsDialogOpen(false);
    } else {
      throw new Error('Invalid image data received');
    }
  } catch (error) {
    console.error('Image selection error:', error);
    setError('Failed to select image. Please try again.');
  }
};

// 优化后的加载状态
{isImageLoading && (
  <div className="absolute inset-0 flex items-center justify-center bg-muted/50">
    <div className="bg-white/90 backdrop-blur-sm rounded-full p-3 shadow-lg">
      <Loader2 className="h-6 w-6 animate-spin text-primary" />
    </div>
  </div>
)}
```

### ImageGrid 瀑布流布局优化案例

#### 优化前的问题

1. **布局限制**：强制正方形布局，图片被裁剪
2. **视觉效果差**：固定网格显得生硬
3. **加载状态单一**：统一高度的骨架屏不真实
4. **交互反馈简单**：缺少丰富的视觉反馈

#### 优化策略

1. **布局革新**：从固定网格改为瀑布流布局
2. **保持比例**：移除强制正方形，保持图片原始比例
3. **加载优化**：变高度骨架屏模拟真实布局
4. **交互增强**：添加缩放、遮罩、尺寸信息等效果

#### 优化成果

```typescript
// 优化前：固定网格
<div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
  {images?.map((image) => (
    <img
      className="w-full aspect-square object-cover"
      src={image.previewURL}
    />
  ))}
</div>

// 优化后：瀑布流布局
<div className="columns-2 md:columns-3 lg:columns-4 gap-4 space-y-4">
  {images?.map((image) => (
    <div className="relative group break-inside-avoid mb-4">
      <div className="relative overflow-hidden rounded-lg bg-muted">
        <img
          src={image.previewURL}
          className="w-full h-auto object-cover transition-all duration-300 group-hover:scale-105"
          loading="lazy"
        />
        
        {/* 悬停遮罩和按钮 */}
        <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        
        {/* 尺寸信息标签 */}
        {image.webformatWidth && image.webformatHeight && (
          <div className="absolute top-2 right-2 bg-black/60 text-white text-xs px-2 py-1 rounded-full">
            {image.webformatWidth} × {image.webformatHeight}
          </div>
        )}
      </div>
    </div>
  ))}
</div>
```

#### 关键改进点

1. **用户体验**
   - 可视化图标预览
   - 搜索功能
   - 直观的操作按钮
   - 响应式设计

2. **技术架构**
   - 组件复用而非重新实现
   - 状态管理优化
   - 类型安全保证
   - 错误处理完善

3. **可维护性**
   - 代码结构清晰
   - 注释完善
   - 易于扩展
   - 符合设计规范

### 重构最佳实践

#### 1. 重构前评估

- **功能分析**：识别现有功能的局限性
- **用户需求**：了解用户的实际使用场景
- **技术债务**：评估代码质量和维护成本
- **复用机会**：寻找可复用的现有组件

#### 2. 重构策略

- **渐进式重构**：分步骤进行，降低风险
- **向后兼容**：保持 API 接口的稳定性
- **测试驱动**：确保重构不破坏现有功能
- **文档同步**：及时更新相关文档

#### 3. 重构验证

- **功能测试**：验证所有功能正常工作
- **性能测试**：确保性能没有退化
- **用户测试**：收集用户反馈
- **代码审查**：确保代码质量

#### 4. 最新优化经验总结

基于最近的组件优化工作，我们总结出以下关键经验：

##### 布局优化
- **瀑布流布局**：适用于不同尺寸内容的展示
- **响应式设计**：确保在各种设备上的良好体验
- **性能考虑**：使用懒加载和虚拟滚动优化大量内容

##### 用户体验
- **加载状态**：提供真实感的加载反馈
- **错误处理**：友好的错误提示和恢复机制
- **交互反馈**：丰富的视觉和动画效果

##### 技术实现
- **类型安全**：严格的 TypeScript 类型定义
- **组件复用**：最大化利用现有组件
- **可访问性**：完整的 ARIA 支持和键盘导航

## 总结

开发高质量的 Widget 需要注意：

1. **遵循标准接口**：实现完整的 WidgetProps 接口
2. **处理边界情况**：空值、错误状态、加载状态
3. **保证可访问性**：正确的 ARIA 属性和键盘导航
4. **优化性能**：避免不必要的重渲染
5. **编写测试**：确保功能正确性和稳定性
6. **文档完善**：清晰的注释和使用说明
7. **组件复用**：优先使用现有组件，避免重复开发
8. **用户体验**：提供直观、一致的交互体验
9. **国际化支持**：考虑多语言和文案优化
10. **持续改进**：基于用户反馈和技术发展持续优化
11. **布局优化**：选择合适的布局策略提升视觉效果
12. **错误处理**：提供结构化的错误处理和用户反馈

### 开发流程建议

1. **需求分析** → 明确功能需求和用户场景
2. **技术调研** → 评估现有组件和技术方案
3. **设计方案** → 制定技术架构和实现方案
4. **原型开发** → 快速实现核心功能
5. **完善功能** → 添加错误处理、边界情况处理
6. **优化体验** → 改进交互和视觉效果
7. **测试验证** → 进行全面的功能和性能测试
8. **文档更新** → 更新开发文档和使用说明
9. **部署发布** → 发布到生产环境
10. **持续维护** → 收集反馈，持续改进

通过遵循本指南和重构经验，你可以开发出功能完善、用户体验良好、易于维护的自定义 Widget。