import React from 'react';
import { WidgetProps } from '@rjsf/utils';
import { Textarea } from '@/components/ui/textarea';
import { getCustomPlaceholder, getCustomClassNames } from '../utils';

/**
 * Shadcn UI Textarea Widget
 * 
 * Used for rendering multi-line text input fields
 */
const TextareaWidget = ({
  id,
  value,
  onChange,
  onBlur,
  onFocus,
  disabled,
  readonly,
  autofocus,
  options,
  // schema,
  uiSchema,
  placeholder,
  required,
}: WidgetProps) => {
  const handleChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = event.target.value;
    
    // 允许所有字段（包括必填字段）保持空字符串状态
    // 这样可以防止RJSF自动填充默认值
    if (newValue === '') {
      onChange(''); // 始终传递空字符串而不是 undefined
    } else {
      onChange(newValue);
    }
  };
  
  const customPlaceholder = getCustomPlaceholder(uiSchema, options, placeholder);
  const textareaClassName = getCustomClassNames(uiSchema);
  const rows = options.rows || uiSchema?.['ui:options']?.rows || 5;
  
  return (
    <Textarea
      id={id}
      name={id}
      value={value || ''}
      placeholder={customPlaceholder}
      disabled={disabled || readonly}
      readOnly={readonly}
      autoFocus={autofocus}
      onChange={handleChange}
      onBlur={onBlur && ((event) => onBlur(id, event.target.value))}
      onFocus={onFocus && ((event) => onFocus(id, event.target.value))}
      className={textareaClassName}
      rows={rows}
      aria-describedby={`${id}-description ${id}-error`}
      required={required}
    />
  );
};

export default TextareaWidget;
