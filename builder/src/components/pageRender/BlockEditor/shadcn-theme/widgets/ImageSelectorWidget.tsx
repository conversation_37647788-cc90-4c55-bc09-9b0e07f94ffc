import React, { useState, useEffect } from 'react';
import { WidgetProps } from '@rjsf/utils';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import ImageEditor, { ImageSelectData } from '@/components/image-editor';
import { ImageUploadWidget } from '@/components/form-upload';
import { ImageIcon, X, Loader2 } from 'lucide-react';
import { useParams } from 'next/navigation';
import { useWebsite } from '@/hooks/useWebsite';
import {
  getCustomClassNames
} from '../utils';

/**
 * Image Selector Widget
 * 
 * A widget for selecting images with preview functionality
 */
interface ImageSelectorWidgetProps extends WidgetProps {
  domain?: string;
}

const ImageSelectorWidget = ({
  id,
  value,
  onChange,
  onBlur,
  onFocus,
  disabled,
  readonly,
  options: _options,
  schema: _schema,
  uiSchema,
  label: _label,
  required: _required,
  // domain,
}: ImageSelectorWidgetProps) => {
  // Get current website information directly from route
  const { siteId } = useParams<{ siteId: string }>() || { siteId: '' };
  const { data: website } = useWebsite(siteId);
  
  // Use website domain or fallback to prop domain
  const domain = website?.domain;
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [imageLoadError, setImageLoadError] = useState(false);
  const [isImageLoading, setIsImageLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Reset error state when value changes
  useEffect(() => {
    setImageLoadError(false);
    setIsImageLoading(false);
    setError(null);
  }, [value]);
  
  const wrapperClassName = getCustomClassNames(uiSchema);
  
  const handleButtonClick = () => {
    if (!disabled && !readonly) {
      setIsDialogOpen(true);
    }
  };
  
  const handleDialogClose = () => {
    setIsDialogOpen(false);
  };
  


  // Generate image URL with specific dimensions for different use cases
  const getImageUrlWithSize = (src: string, width?: number, height?: number): string => {
    if (!src) return '';
    
    // If it's already a complete URL, handle parameters
    if (src.startsWith('http')) {
      if (!src.includes('imgpipe.net')) return src;
      
      let url = src;
      const separator = src.includes('?') ? '&' : '?';
      if (width) url += `${separator}w=${width}`;
      if (height) url += `&h=${height}`;
      return url;
    }
    
    // If it's a relative path, build complete URL with domain
    if (src.startsWith('/')) {
      const isDevelopment = process.env.NODE_ENV === 'development';
      const currentDomain = domain || 'default'; // Fallback to 'default' if domain is not available
      const baseURL = `https://${isDevelopment ? 'imgpipe-test' : currentDomain}.imgpipe.net`;
      let url = `${baseURL}${src}?fm=webp`;
      if (width) url += `&w=${width}`;
      if (height) url += `&h=${height}`;
      return url;
    }
    
    // Fallback for other formats
    return src;
  };

  const handleImageSelect = (data: ImageSelectData) => {
    try {
      if (data?.result?.id) {
        // 新逻辑：仅存储相对路径
        let imagePath = '';
        
        if (data.result.id.includes('/')) {
          // 格式: domain/filename -> /filename  
          const parts = data.result.id.split('/');
          imagePath = `/${parts[1]}`;
        } else if (data.result.id.startsWith('http')) {
          // 格式: https://domain.imgpipe.net/filename -> /filename
          try {
            const url = new URL(data.result.id);
            imagePath = url.pathname;
          } catch {
            // URL解析失败，尝试提取文件名
            imagePath = data.result.id.startsWith('/') ? data.result.id : `/${data.result.id}`;
          }
        } else {
          // 其他情况处理
          imagePath = data.result.id.startsWith('/') ? data.result.id : `/${data.result.id}`;
        }
        
        onChange?.(imagePath); // 存储相对路径
        setIsDialogOpen(false);
        setError(null); // Clear any previous errors
      } else if (data?.error) {
        setError(data.error);
        console.error('Image selection error:', data.error);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to process image selection';
      setError(errorMessage);
      console.error('Failed to process image selection:', error);
    }
  };

  // Handle local upload callback
  const handleLocalUpload = (imageUrl: string) => {
    // Convert local upload result to unified format
    const imageData: ImageSelectData = {
      result: {
        id: imageUrl, // For local uploads, use URL as ID
        url: imageUrl
      }
    };
    handleImageSelect(imageData);
  };

  // Handle local upload error callback
  const handleLocalUploadError = (error: string) => {
    setError(error);
  };
  
  const handleRemoveImage = () => {
    onChange?.('');
  };

  const isValidImageUrl = (url: string) => {
    return url && (url.startsWith('http') || url.startsWith('data:image') || url.startsWith('/'));
  };

  const handleImageLoadStart = () => {
    setIsImageLoading(true);
    setImageLoadError(false);
  };

  const handleImageLoad = () => {
    setIsImageLoading(false);
    setImageLoadError(false);
  };

  const handleImageError = () => {
    setIsImageLoading(false);
    setImageLoadError(true);
  };

  return (
    <div className={wrapperClassName}>
      {value && isValidImageUrl(value) ? (
        <div className="space-y-2">
          {/* Image Preview */}
          <div className="relative group">
            <div className="relative overflow-hidden rounded-lg border bg-muted">
              {!imageLoadError ? (
                <div className="relative">
                  <img
                    src={getImageUrlWithSize(value, 800, 600)} // Apply dimensions for preview
                    alt="Selected image preview"
                    className="w-full h-48 object-cover transition-all duration-200 group-hover:brightness-75"
                    onLoadStart={handleImageLoadStart}
                    onLoad={handleImageLoad}
                    onError={handleImageError}
                    loading="lazy"
                  />
                  
                  {/* Loading overlay */}
                  {isImageLoading && (
                    <div className="absolute inset-0 bg-muted/80 flex items-center justify-center">
                      <div className="text-center text-muted-foreground">
                        <Loader2 className="h-8 w-8 mx-auto mb-2 animate-spin" />
                        <p className="text-sm">Loading image...</p>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="w-full h-48 bg-muted flex items-center justify-center">
                  <div className="text-center text-muted-foreground">
                    <ImageIcon className="h-12 w-12 mx-auto mb-2" />
                    <p className="text-sm">Image preview unavailable</p>
                    <p className="text-xs mt-1">Please try selecting a different image</p>
                  </div>
                </div>
              )}
              
              {/* Overlay Controls */}
              <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-all duration-200 flex items-center justify-center gap-2">
                {/* Change Image Button */}
                <Button
                  type="button"
                  variant="secondary"
                  size="sm"
                  onClick={handleButtonClick}
                  disabled={disabled || readonly}
                  className="bg-white/90 hover:bg-white text-black border-0 shadow-lg backdrop-blur-sm"
                  onFocus={onFocus && (() => onFocus(id, value))}
                  onBlur={onBlur && (() => onBlur(id, value))}
                  aria-label="Change selected image"
                >
                  <ImageIcon className="h-4 w-4 mr-2" />
                  Browse Images
                </Button>
                
                {/* Remove Button */}
                <Button
                  type="button"
                  variant="destructive"
                  size="sm"
                  onClick={handleRemoveImage}
                  disabled={disabled || readonly}
                  className="bg-red-500/90 hover:bg-red-600 text-white border-0 shadow-lg backdrop-blur-sm h-8 w-8 p-0"
                  aria-label="Remove selected image"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
          
          {/* Error Message for image state */}
          {error && (
            <div className="text-sm text-red-600 bg-red-50 border border-red-200 rounded-md p-2">
              {error}
            </div>
          )}
        </div>
      ) : (
        <Button
          type="button"
          variant="outline"
          onClick={handleButtonClick}
          disabled={disabled || readonly}
          className="w-full justify-center gap-2 min-h-[192px] h-auto py-8 px-6 border-dashed border-2 hover:border-primary/50 hover:bg-muted/50 transition-all duration-200"
          onFocus={onFocus && (() => onFocus(id, value))}
          onBlur={onBlur && (() => onBlur(id, value))}
          aria-label="Select an image"
          aria-describedby={`${id}-description`}
        >
          <div className="text-center">
            <ImageIcon className="h-12 w-12 mx-auto mb-3 text-muted-foreground" />
            <p className="text-base font-medium mb-1">Choose an Image</p>
            <p className="text-sm text-muted-foreground" id={`${id}-description`}>
              Upload or browse from our gallery
            </p>
          </div>
        </Button>
      )}
       
      {/* Error Message for no image state */}
      {!value && error && (
        <div className="mt-2 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md p-2">
          {error}
        </div>
      )}
      
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-6xl max-h-[95vh] w-[90vw] overflow-hidden">
          <DialogHeader>
            <DialogTitle>Choose an Image</DialogTitle>
            <DialogDescription>
              Search, upload, or browse from our image gallery to find the perfect image for your content
            </DialogDescription>
          </DialogHeader>
          
          <div className="flex-1 min-h-[600px] overflow-hidden">
            <Tabs defaultValue="local" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="local">Upload</TabsTrigger>
                <TabsTrigger value="search">Search Images</TabsTrigger>
              </TabsList>
              <TabsContent value="local" className="mt-4">
                <ImageUploadWidget 
                  onUploadSuccess={handleLocalUpload}
                  onUploadError={handleLocalUploadError}
                  domain={domain}
                />
              </TabsContent>
              <TabsContent value="search" className="mt-4">
                <ImageEditor
                  onChange={handleImageSelect}
                  domain={domain}
                />
              </TabsContent>
            </Tabs>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ImageSelectorWidget;