import React, { useState } from 'react';
import { WidgetProps } from '@rjsf/utils';
import { Button } from '@/components/ui/button';
import { getCustomClassNames } from '../utils';
import IconPicker from '@/components/IconPicker/IconPicker';
import { IconRenderer } from '@litpage/sections';
import { Search, X } from 'lucide-react';

/**
 * Shadcn UI Icon Selection Widget
 * 
 * A visual icon picker for selecting Lucide React icons
 */
const IconSelectWidget = ({
  id,
  value,
  onChange,
  onBlur,
  onFocus,
  disabled,
  readonly,
  // options,
  // schema,
  uiSchema,
  placeholder,
  autofocus,
}: WidgetProps) => {
  const [isPickerOpen, setIsPickerOpen] = useState(false);
  
  const handleIconSelect = (iconName: string) => {
    onChange(iconName);
    setIsPickerOpen(false);
  };
  
  const handleRemoveIcon = () => {
    onChange('');
  };
  
  const handleButtonClick = () => {
    if (!disabled && !readonly) {
      setIsPickerOpen(true);
    }
  };
  
  const wrapperClassName = getCustomClassNames(uiSchema, "w-full");
  
  return (
    <div className={wrapperClassName}>
      {value ? (
        <div className="space-y-3">
          {/* Selected icon preview */}
          <div className="relative p-4 border rounded-lg bg-muted/50">
            {/* Remove button in top-right corner */}
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={handleRemoveIcon}
              disabled={disabled || readonly}
              className="absolute top-2 right-2 h-6 w-6 p-0 text-muted-foreground hover:text-destructive hover:bg-destructive/10 rounded-full"
            >
              <X className="h-3 w-3" />
            </Button>
            
            <div className="flex flex-col items-center space-y-3">
              {/* Large icon preview */}
              <div className="flex items-center justify-center w-16 h-16 bg-background border rounded-lg shadow-sm">
                <IconRenderer 
                  name={value} 
                  size={32} 
                  color="currentColor" 
                />
              </div>
              
              {/* Icon name and description */}
              <div className="text-center">
                <p className="text-sm font-medium text-foreground">{value}</p>
                <p className="text-xs text-muted-foreground mt-1">Current icon</p>
              </div>
              
              {/* Change button */}
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleButtonClick}
                disabled={disabled || readonly}
                onFocus={onFocus && (() => onFocus(id, value))}
                onBlur={onBlur && (() => onBlur(id, value))}
                className="w-full"
              >
                <Search className="h-4 w-4 mr-2" />
                Browse Icons
              </Button>
            </div>
          </div>
        </div>
      ) : (
        /* Icon selection button when no icon is selected */
        <Button
          type="button"
          variant="outline"
          onClick={handleButtonClick}
          disabled={disabled || readonly}
          className="w-full justify-center gap-2 min-h-[120px] h-auto py-8 px-6 border-dashed border-2 hover:border-primary/50 hover:bg-muted/50 transition-all duration-200"
          onFocus={onFocus && (() => onFocus(id, value))}
          onBlur={onBlur && (() => onBlur(id, value))}
          autoFocus={autofocus}
        >
          <div className="text-center">
            <Search className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
            <p className="text-base font-medium mb-1">Choose an Icon</p>
            <p className="text-sm text-muted-foreground">{placeholder || "Browse from hundreds of beautiful icons"}</p>
          </div>
        </Button>
      )}
      
      {/* Icon picker dialog */}
      <IconPicker
        isOpen={isPickerOpen}
        onOpenChange={setIsPickerOpen}
        onSelect={handleIconSelect}
        currentIcon={value}
        size={24}
        color="currentColor"
      />
    </div>
  );
};

export default IconSelectWidget;
