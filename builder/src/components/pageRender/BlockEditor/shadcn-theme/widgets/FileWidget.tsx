import React, { useState, useCallback, useRef } from 'react';
import { WidgetProps } from '@rjsf/utils';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { getCustomClassNames } from '../utils';
import { UploadIcon, XIcon } from 'lucide-react';

/**
 * File Upload Widget
 * 
 * Widget for handling file uploads
 */
const FileWidget = ({
  id,
  value,
  onChange,
  onBlur,
  onFocus,
  disabled,
  readonly,
  autofocus,
  options,
  schema,
  uiSchema,
  required,
}: WidgetProps) => {
  const [fileName, setFileName] = useState<string>('');
  const fileInputRef = useRef<HTMLInputElement>(null);
  const wrapperClassName = getCustomClassNames(uiSchema);
  
  // Handle file selection
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) {
      setFileName('');
      onChange(undefined);
      return;
    }
    
    const file = files[0];
    setFileName(file.name);
    
    // Decide how to handle files based on uiSchema configuration
    const fileFormat = uiSchema?.['ui:options']?.fileFormat;
    
    if (fileFormat === 'base64') {
      const reader = new FileReader();
      reader.onload = (e) => {
        const base64 = e.target?.result;
        onChange(base64);
      };
      reader.readAsDataURL(file);
    } else if (fileFormat === 'binary') {
      onChange(file);
    } else {
      // Default handling method, only pass file information
      onChange({
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: file.lastModified,
      });
    }
  };
  
  // Clear selected file
  const handleClearFile = useCallback(() => {
    setFileName('');
    onChange(undefined);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [onChange]);
  
  // Trigger file selection dialog
  const handleSelectFile = useCallback(() => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  }, []);
  
  // Handle drag and drop
  const handleDrop = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    
    if (disabled || readonly) return;
    
    const files = event.dataTransfer.files;
    if (!files || files.length === 0) return;
    
    const file = files[0];
    setFileName(file.name);
    
    // Decide how to handle files based on uiSchema configuration
    const fileFormat = uiSchema?.['ui:options']?.fileFormat;
    
    if (fileFormat === 'base64') {
      const reader = new FileReader();
      reader.onload = (e) => {
        const base64 = e.target?.result;
        onChange(base64);
      };
      reader.readAsDataURL(file);
    } else if (fileFormat === 'binary') {
      onChange(file);
    } else {
      // Default handling method, only pass file information
      onChange({
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: file.lastModified,
      });
    }
  }, [disabled, readonly, onChange, uiSchema]);
  
  // Prevent default drag and drop behavior
  const handleDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
  }, []);
  
  // Get file name display
  const getDisplayFileName = () => {
    if (fileName) return fileName;
    
    if (value) {
      if (typeof value === 'string') {
        // Could be base64 or file path
        if (value.startsWith('data:')) {
          return 'Uploaded file';
        }
        return value.split('/').pop() || 'Uploaded file';
      } else if (typeof value === 'object') {
        // File object or file info object
        return value.name || 'Uploaded file';
      }
    }
    
    return '';
  };
  
  // Get file accept types
  const getAcceptTypes = (): string | undefined => {
    if (options.accept) return options.accept as string;
    
    const acceptOptions = uiSchema?.['ui:options']?.accept;
    if (acceptOptions) return acceptOptions as string;
    
    // Infer type from schema
    if (schema.contentMediaType) {
      return schema.contentMediaType as string;
    }
    
    return undefined;
  };
  
  const displayFileName = getDisplayFileName();
  const acceptTypes = getAcceptTypes();
  
  return (
    <div className={cn("space-y-2", wrapperClassName)}>
      <div
        className={cn(
          "flex flex-col items-center justify-center w-full p-4 border-2 border-dashed rounded-md",
          "hover:bg-muted/50 transition-colors",
          disabled || readonly ? "opacity-50 cursor-not-allowed" : "cursor-pointer",
          displayFileName ? "border-primary" : "border-border"
        )}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onClick={handleSelectFile}
        tabIndex={disabled || readonly ? -1 : 0}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            handleSelectFile();
          }
        }}
        role="button"
        aria-disabled={disabled || readonly}
        aria-describedby={`${id}-description ${id}-error`}
      >
        <input
          ref={fileInputRef}
          id={id}
          type="file"
          onChange={handleFileChange}
          onBlur={onBlur && ((event) => onBlur(id, event.target.value))}
          onFocus={onFocus && ((event) => onFocus(id, event.target.value))}
          disabled={disabled || readonly}
          autoFocus={autofocus}
          required={required}
          accept={acceptTypes}
          className="hidden"
          aria-hidden="true"
        />
        
        <div className="flex flex-col items-center justify-center py-4">
          <UploadIcon className="w-8 h-8 mb-2 text-muted-foreground" />
          <p className="mb-1 text-sm text-muted-foreground">
            {displayFileName ? displayFileName : 'Click or drag files here to upload'}
          </p>
          <p className="text-xs text-muted-foreground">
            {options.maxSize && `Maximum file size: ${options.maxSize}`}
          </p>
        </div>
      </div>
      
      {displayFileName && (
        <div className="flex items-center justify-between">
          <span className="text-sm truncate max-w-[80%]">{displayFileName}</span>
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              handleClearFile();
            }}
            disabled={disabled || readonly}
            aria-label="Clear file"
          >
            <XIcon className="w-4 h-4" />
          </Button>
        </div>
      )}
    </div>
  );
};

export default FileWidget;
