import React from 'react';
import { WidgetProps } from '@rjsf/utils';
import { Button } from '@/components/ui/button';
import { getCustomClassNames } from '../utils';

/**
 * Shadcn UI Button Group Widget
 *
 * Used for rendering a group of mutually exclusive button options, suitable for enum types
 */
const ButtonGroupWidget = ({
  id,
  value,
  onChange,
  onBlur,
  onFocus,
  disabled,
  readonly,
  options,
  // schema,
  uiSchema,
  autofocus,
}: WidgetProps) => {
  const { enumOptions = [] } = options;
  
  const handleChange = (newValue: string) => {
    onChange(newValue);
  };
  
  const wrapperClassName = getCustomClassNames(uiSchema, "flex flex-wrap gap-2 my-1");
  
  return (
    <div className={wrapperClassName}>
      {enumOptions.map((option: any, index: number) => {
        const optionValue = option.value;
        const isSelected = value === optionValue;
        
        return (
          <Button
            key={`${id}-${index}`}
            type="button"
            variant={isSelected ? "default" : "outline"}
            size="sm"
            onClick={() => handleChange(optionValue)}
            disabled={disabled || readonly}
            onBlur={onBlur && (() => onBlur(id, value))}
            onFocus={onFocus && (() => onFocus(id, value))}
            autoFocus={autofocus && index === 0}
            className="min-w-20"
          >
            {option.label || optionValue}
          </Button>
        );
      })}
    </div>
  );
};

export default ButtonGroupWidget;
