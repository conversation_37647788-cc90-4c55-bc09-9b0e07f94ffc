import React from 'react';
import { WidgetProps } from '@rjsf/utils';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { getCustomClassNames } from '../utils';

/**
 * Shadcn UI Switch Widget
 *
 * Used for rendering single switch control, suitable for boolean types
 * Supports trueLabel and falseLabel options for custom label text
 */
const SwitchWidget = ({
  id,
  value,
  onChange,
  onBlur,
  onFocus,
  disabled,
  readonly,
  label,
  schema,
  uiSchema,
  autofocus,
}: WidgetProps) => {
  const handleChange = (checked: boolean) => {
    onChange(checked);
  };
  
  // Get custom label text
  const options = uiSchema?.["ui:options"] || {};
  const trueLabel = options.trueLabel || "";
  const falseLabel = options.falseLabel || "";
  
  // Build display label
  const displayLabel = label || schema.title;
  const statusLabel = value ? trueLabel : falseLabel;
  const fullLabel = displayLabel + (statusLabel ? ` (${statusLabel})` : "");
  
  const wrapperClassName = getCustomClassNames(uiSchema, "flex items-center space-x-3 transition-colors duration-200");
  
  return (
    <div className={wrapperClassName}>
      <Switch
        id={id}
        checked={value === undefined ? false : value}
        onCheckedChange={handleChange}
        disabled={disabled || readonly}
        aria-describedby={`${id}-description ${id}-error`}
        onBlur={onBlur && (() => onBlur(id, value))}
        onFocus={onFocus && (() => onFocus(id, value))}
        autoFocus={autofocus}
        className="transition-all duration-200"
      />
      <Label 
        htmlFor={id}
        className="text-sm font-normal cursor-pointer text-foreground transition-colors duration-200 hover:text-foreground/80"
      >
        {fullLabel}
      </Label>
    </div>
  );
};

export default SwitchWidget;
