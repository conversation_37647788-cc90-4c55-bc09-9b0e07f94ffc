import React from 'react';
import { WidgetProps } from '@rjsf/utils';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { getCustomClassNames } from '../utils';

/**
 * Shadcn UI Radio Widget
 *
 * Used for rendering radio button groups
 */
const RadioWidget = ({
  id,
  options,
  value,
  onChange,
  onBlur,
  onFocus,
  disabled,
  readonly,
  autofocus,
  // schema,
  uiSchema,
}: WidgetProps) => {
  const { enumOptions, enumDisabled } = options;
  const wrapperClassName = getCustomClassNames(uiSchema, "space-y-2");
  
  const handleChange = (newValue: string) => {
    onChange(newValue);
  };
  
  return (
    <RadioGroup
      value={value || ''}
      onValueChange={handleChange}
      className={wrapperClassName}
      disabled={disabled || readonly}
      aria-describedby={`${id}-description ${id}-error`}
    >
      {enumOptions && enumOptions.map(({ value: optionValue, label }, index) => {
        const itemId = `${id}_${index}`;
        const isDisabled = enumDisabled && enumDisabled.indexOf(optionValue) !== -1;
        
        return (
          <div key={itemId} className="flex items-center space-x-2">
            <RadioGroupItem 
              id={itemId} 
              value={optionValue}
              disabled={isDisabled || disabled || readonly}
              autoFocus={autofocus && index === 0}
              onBlur={onBlur && (() => onBlur(id, optionValue))}
              onFocus={onFocus && (() => onFocus(id, optionValue))}
            />
            <Label 
              htmlFor={itemId}
              className="text-sm font-normal cursor-pointer"
            >
              {label}
            </Label>
          </div>
        );
      })}
    </RadioGroup>
  );
};

export default RadioWidget;
