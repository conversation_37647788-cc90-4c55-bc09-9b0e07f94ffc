import React from 'react';
import { WidgetProps } from '@rjsf/utils';
import { format } from 'date-fns';
import { Calendar as CalendarIcon, Clock } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Input } from '@/components/ui/input';
import { getCustomClassNames, getCustomPlaceholder } from '../utils';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';

/**
 * Shadcn UI DateTime Picker Widget
 * 
 * Used for rendering date and time picker
 */
const DateTimeWidget = ({
  id,
  value,
  onChange,
  onBlur,
  onFocus,
  disabled,
  readonly,
  autofocus,
  options,
  // schema,
  uiSchema,
  placeholder,
  // required,
}: WidgetProps) => {
  const customPlaceholder = getCustomPlaceholder(uiSchema, options, placeholder || 'Select date and time');
  const wrapperClassName = getCustomClassNames(uiSchema);
  
  // Handle date time conversion
  const handleDateTimeChange = (date: Date | undefined, timeString?: string) => {
    if (!date) {
      onChange(options.emptyValue);
      return;
    }
    
    // If there's a time string, merge date and time
    if (timeString) {
      const [hours, minutes] = timeString.split(':').map(Number);
      date.setHours(hours);
      date.setMinutes(minutes);
    }
    
    // Convert to ISO format date time string
    const isoDateTime = date.toISOString();
    onChange(isoDateTime);
  };
  
  // Handle date change
  const handleDateChange = (date: Date | undefined) => {
    if (!date && !value) {
      return;
    }
    
    if (!date) {
      onChange(options.emptyValue);
      return;
    }
    
    // If there's an existing value, preserve the time part
    if (value) {
      const existingDate = new Date(value);
      date.setHours(existingDate.getHours());
      date.setMinutes(existingDate.getMinutes());
      date.setSeconds(existingDate.getSeconds());
    }
    
    handleDateTimeChange(date);
  };
  
  // Handle time change
  const handleTimeChange = (timeString: string) => {
    // If special empty placeholder is selected, do nothing
    if (timeString === "__no_time__") {
      return;
    }
    
    if (!value) {
      // If there's no date, use today
      const today = new Date();
      handleDateTimeChange(today, timeString);
    } else {
      // Use existing date
      const date = new Date(value);
      handleDateTimeChange(date, timeString);
    }
  };
  
  // Handle direct input
  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = event.target.value;
    
    if (!inputValue) {
      onChange(options.emptyValue);
      return;
    }
    
    // Validate date time format
    try {
      const date = new Date(inputValue);
      if (!isNaN(date.getTime())) {
        onChange(date.toISOString());
      }
    } catch (e) {
      // Invalid date, don't update
    }
  };
  
  // Convert ISO date time string to Date object
  const dateValue = value ? new Date(value) : undefined;
  
  // Generate time options
  const generateTimeOptions = () => {
    const options = [];
    for (let hour = 0; hour < 24; hour++) {
      for (let minute = 0; minute < 60; minute += 15) {
        const hourStr = hour.toString().padStart(2, '0');
        const minuteStr = minute.toString().padStart(2, '0');
        options.push(`${hourStr}:${minuteStr}`);
      }
    }
    return options;
  };
  
  // Get current time string
  const getCurrentTimeString = () => {
    if (!dateValue) return '__no_time__';
    const hours = dateValue.getHours().toString().padStart(2, '0');
    const minutes = dateValue.getMinutes().toString().padStart(2, '0');
    
    // Find the closest time option
    const timeStr = `${hours}:${minutes}`;
    const allOptions = generateTimeOptions();
    if (allOptions.includes(timeStr)) {
      return timeStr;
    }
    
    // If no exact match, return the first option
    return allOptions[0] || '__no_time__';
  };
  
  return (
    <div className={cn("space-y-2", wrapperClassName)}>
      {/* Date picker */}
      <div className="relative">
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                "w-full justify-start text-left font-normal",
                !dateValue && "text-muted-foreground"
              )}
              disabled={disabled || readonly}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {dateValue ? format(dateValue, 'yyyy-MM-dd') : 'Select date'}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              mode="single"
              selected={dateValue}
              onSelect={handleDateChange}
              disabled={disabled || readonly}
              initialFocus
            />
          </PopoverContent>
        </Popover>
      </div>
      
      {/* Time picker */}
      <div className="relative">
        <Select
          value={getCurrentTimeString()}
          onValueChange={handleTimeChange}
          disabled={disabled || readonly || !dateValue}
        >
          <SelectTrigger className="w-full">
            <Clock className="mr-2 h-4 w-4" />
            <SelectValue placeholder="Select time" />
          </SelectTrigger>
          <SelectContent>
            {!dateValue && (
              <SelectItem value="__no_time__">Please select date first</SelectItem>
            )}
            {generateTimeOptions().map((time) => (
              <SelectItem key={time} value={time}>
                {time}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      
      {/* Hidden input for direct input and form submission */}
      <Input
        type="hidden"
        id={id}
        name={id}
        value={value || ''}
        disabled={disabled || readonly}
        onChange={handleInputChange}
        onBlur={onBlur && ((event) => onBlur(id, event.target.value))}
        onFocus={onFocus && ((event) => onFocus(id, event.target.value))}
        autoFocus={autofocus}
      />
    </div>
  );
};

export default DateTimeWidget;
