import React from 'react';
import { WidgetProps } from '@rjsf/utils';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { getCustomPlaceholder, getCustomClassNames } from '../utils';

/**
 * Shadcn UI Select Widget
 * 
 * Used for rendering dropdown select boxes
 */
const SelectWidget = ({
  id,
  options,
  value,
  required,
  disabled,
  readonly,
  onChange,
  // onBlur,
  // onFocus,
  placeholder,
  // autofocus,
  multiple,
  // schema,
  uiSchema,
}: WidgetProps) => {
  const { enumOptions, enumDisabled } = options;
  const customPlaceholder = getCustomPlaceholder(uiSchema, options, placeholder || 'Please select...');
  const selectClassName = getCustomClassNames(uiSchema);
  
  const handleChange = (newValue: string) => {
    // If "__empty__" special value is selected, convert it to empty string
    if (newValue === "__empty__") {
      onChange("");
    } else {
      onChange(newValue);
    }
  };
  
  // Handle multiple selection case (if needed)
    if (multiple) {
      // Multiple selection functionality needs to be implemented with other components, not implemented here
      return (
        <div className="text-red-500">Multiple selection not supported, please use CheckboxesWidget</div>
      );
    }

    // Convert empty string value to special value "__empty__"
  const displayValue = value === "" ? "__empty__" : value || "";
  
  return (
    <Select
      value={displayValue}
      onValueChange={handleChange}
      disabled={disabled || readonly}
    >
      <SelectTrigger 
        id={id}
        className={selectClassName}
        aria-describedby={`${id}-description ${id}-error`}
      >
        <SelectValue placeholder={customPlaceholder} />
      </SelectTrigger>
      <SelectContent>
        {!required && (
          <SelectItem value="__empty__">
            {customPlaceholder}
          </SelectItem>
        )}
        
        {enumOptions && enumOptions.map(({ value, label }) => {
          const isDisabled = enumDisabled && enumDisabled.indexOf(value) !== -1;
          return (
            <SelectItem 
              key={value} 
              value={value}
              disabled={isDisabled}
            >
              {label}
            </SelectItem>
          );
        })}
      </SelectContent>
    </Select>
  );
};

export default SelectWidget;
