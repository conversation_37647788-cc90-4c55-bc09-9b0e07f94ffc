import React from 'react';
import { WidgetProps } from '@rjsf/utils';
import { format } from 'date-fns';
import { Calendar as CalendarIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Input } from '@/components/ui/input';
import { getCustomClassNames, getCustomPlaceholder } from '../utils';

/**
 * Shadcn UI Date Picker Widget
 * 
 * Used for rendering date picker
 */
const DateWidget = ({
  id,
  value,
  onChange,
  onBlur,
  onFocus,
  disabled,
  readonly,
  autofocus,
  options,
  // schema,
  uiSchema,
  placeholder,
  required,
}: WidgetProps) => {
  const customPlaceholder = getCustomPlaceholder(uiSchema, options, placeholder || 'Select date');
  const wrapperClassName = getCustomClassNames(uiSchema);
  
  // Handle date conversion
  const handleDateChange = (date: Date | undefined) => {
    if (!date) {
      onChange(options.emptyValue);
      return;
    }
    
    // Convert to ISO format date string (YYYY-MM-DD)
    const isoDate = date.toISOString().split('T')[0];
    onChange(isoDate);
  };
  
  // Handle direct input
  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = event.target.value;
    
    if (!inputValue) {
      onChange(options.emptyValue);
      return;
    }
    
    // Validate date format (YYYY-MM-DD)
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (dateRegex.test(inputValue)) {
      onChange(inputValue);
    }
  };
  
  // Convert ISO date string to Date object
  const dateValue = value ? new Date(value) : undefined;
  
  return (
    <div className={cn("relative", wrapperClassName)}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              "w-full justify-start text-left font-normal",
              !value && "text-muted-foreground"
            )}
            disabled={disabled || readonly}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {value ? format(dateValue!, 'yyyy-MM-dd') : customPlaceholder}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            mode="single"
            selected={dateValue}
            onSelect={handleDateChange}
            disabled={disabled || readonly}
            initialFocus
          />
        </PopoverContent>
      </Popover>
      
      {/* Hidden input for direct input and form submission */}
      <Input
        id={id}
        type="date"
        value={value || ''}
        onChange={handleInputChange}
        onBlur={onBlur && ((event) => onBlur(id, event.target.value))}
        onFocus={onFocus && ((event) => onFocus(id, event.target.value))}
        disabled={disabled || readonly}
        readOnly={readonly}
        autoFocus={autofocus}
        required={required}
        className="sr-only"
        aria-hidden="true"
      />
    </div>
  );
};

export default DateWidget;
