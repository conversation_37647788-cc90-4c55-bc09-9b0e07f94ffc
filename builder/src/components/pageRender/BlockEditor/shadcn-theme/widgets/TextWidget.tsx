import React from 'react';
import { WidgetProps } from '@rjsf/utils';
import { Input } from '@/components/ui/input';
import { getCustomPlaceholder, getCustomClassNames } from '../utils';

/**
 * Shadcn UI Text Input Widget
 * 
 * Used for rendering text input fields
 */
const TextWidget = ({
  id,
  value,
  onChange,
  onBlur,
  onFocus,
  disabled,
  readonly,
  autofocus,
  options,
  // schema,
  uiSchema,
  placeholder,
  // label,
  required,
}: WidgetProps) => {
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.value;
    
    // 允许所有字段（包括必填字段）保持空字符串状态
    // 这样可以防止RJSF自动填充默认值
    if (newValue === '') {
      onChange(''); // 始终传递空字符串而不是 undefined
    } else {
      onChange(newValue);
    }
  };
  
  const inputType = options.inputType || 'text';
  const customPlaceholder = getCustomPlaceholder(uiSchema, options, placeholder);
  const inputClassName = getCustomClassNames(uiSchema);
  
  return (
    <Input
      id={id}
      name={id}
      type={inputType}
      value={value || ''}
      placeholder={customPlaceholder}
      disabled={disabled || readonly}
      readOnly={readonly}
      autoFocus={autofocus}
      onChange={handleChange}
      onBlur={onBlur && ((event) => onBlur(id, event.target.value))}
      onFocus={onFocus && ((event) => onFocus(id, event.target.value))}
      className={inputClassName}
      aria-describedby={`${id}-description ${id}-error`}
      required={required}
    />
  );
};

export default TextWidget;
