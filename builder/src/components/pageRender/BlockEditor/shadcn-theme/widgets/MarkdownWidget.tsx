import React, { useState, useRef, useEffect } from 'react';
import { WidgetProps } from '@rjsf/utils';
import dynamic from 'next/dynamic';
import { type EditorRef } from '@/components/MilkdownEditor';
import { getCustomPlaceholder, getCustomClassNames } from '../utils';

// 动态导入 MilkdownEditor 避免 SSR 问题
const MilkdownEditor = dynamic(
  () => import('@/components/MilkdownEditor/MilkdownEditorWrapper'),
  {
    ssr: false,
    loading: () => (
      <div className="border rounded-md flex items-center justify-center bg-background" style={{ height: '400px' }}>
        <div className="text-center space-y-3">
          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary mx-auto"></div>
          <div className="space-y-1">
            <p className="text-sm font-medium">Loading Markdown Editor</p>
            <p className="text-xs text-muted-foreground">Initializing rich text editor...</p>
          </div>
        </div>
      </div>
    )
  }
);

/**
 * Shadcn UI Markdown Widget
 * 
 * 集成 MilkdownEditor 的 Markdown 编辑器 Widget
 * 支持 WYSIWYG 和 Markdown 双模式编辑
 */
const MarkdownWidget = ({
  id,
  value,
  onChange,
  onBlur,
  onFocus,
  disabled,
  readonly,
  autofocus,
  options,
  uiSchema,
  placeholder,
  required,
}: WidgetProps) => {
  const editorRef = useRef<EditorRef | null>(null);
  const [editorRefState, setEditorRefState] = useState<EditorRef | null>(null);

  // 获取自定义配置
  const customPlaceholder = getCustomPlaceholder(uiSchema, options, placeholder);
  const wrapperClassName = getCustomClassNames(uiSchema, "w-full");
  
  // 从 ui:options 获取配置，设置默认值
  const uiOptions = uiSchema?.['ui:options'] || {};
  const defaultMode = (uiOptions.defaultMode === 'markdown' ? 'markdown' : 'wysiwyg') as 'wysiwyg' | 'markdown';
  const showModeToggle = uiOptions.showModeToggle !== false; // 默认显示
  const enableShortcuts = uiOptions.enableShortcuts !== false; // 默认启用
  const autoSave = Boolean(uiOptions.autoSave) || false;
  const autoSaveDelay = typeof uiOptions.autoSaveDelay === 'number' ? uiOptions.autoSaveDelay : 3000;
  
  // 高度配置 - 根据字段路径智能判断默认高度
  const isCodeExample = id.includes('codeExamples') || id.includes('examples') || uiOptions.compact;
  const defaultHeight = isCodeExample ? 300 : 600; // 代码示例用较小高度，主内容用较大高度
  
  // 类型安全的高度值处理
  const height = (typeof uiOptions.height === 'number' || typeof uiOptions.height === 'string') ? uiOptions.height : defaultHeight;
  const minHeight = (typeof uiOptions.minHeight === 'number' || typeof uiOptions.minHeight === 'string') ? uiOptions.minHeight : undefined;
  const maxHeight = (typeof uiOptions.maxHeight === 'number' || typeof uiOptions.maxHeight === 'string') ? uiOptions.maxHeight : undefined;

  // 处理内容变化
  const handleContentChange = (newContent: string) => {
    if (onChange) {
      // 允许空字符串，防止 RJSF 自动填充默认值
      onChange(newContent || '');
    }
  };



  // 处理编辑器引用
  const handleEditorRef = (ref: EditorRef | null) => {
    editorRef.current = ref;
    setEditorRefState(ref);
  };

  // 处理保存事件（用于触发 onBlur）
  const handleSave = (content: string) => {
    if (onBlur) {
      onBlur(id, content);
    }
  };

  // 自动聚焦
  useEffect(() => {
    if (autofocus && editorRefState) {
      setTimeout(() => {
        editorRefState.focus();
      }, 100);
    }
  }, [autofocus, editorRefState]);

  // 处理焦点事件（通过点击事件模拟）
  const handleWrapperClick = () => {
    if (onFocus && !disabled && !readonly) {
      onFocus(id, value || '');
    }
  };

  return (
    <div 
      className={wrapperClassName}
      onClick={handleWrapperClick}
      onFocus={() => onFocus && onFocus(id, value || '')}
    >
      {(disabled || readonly) ? (
        <div 
          className="border rounded-md p-4 bg-muted text-muted-foreground overflow-auto" 
          style={{ 
            height: typeof height === 'number' ? `${height}px` : height, 
            minHeight: minHeight ? (typeof minHeight === 'number' ? `${minHeight}px` : minHeight) : undefined, 
            maxHeight: maxHeight ? (typeof maxHeight === 'number' ? `${maxHeight}px` : maxHeight) : undefined 
          }}
        >
          <div className="prose prose-sm max-w-none">
            <pre className="whitespace-pre-wrap font-mono text-sm">
              {value || customPlaceholder || "No content"}
            </pre>
          </div>
        </div>
      ) : (
        <MilkdownEditor
          onRef={handleEditorRef}
          value={value}
          onChange={handleContentChange}
          onSave={autoSave ? handleSave : undefined}
          placeholder={customPlaceholder || "Write your content using Markdown..."}
          defaultMode={defaultMode}
          showModeToggle={showModeToggle}
          enableShortcuts={enableShortcuts}
          autoSave={autoSave}
          autoSaveDelay={autoSaveDelay}
          className="w-full"
          height={height}
          minHeight={minHeight}
          maxHeight={maxHeight}
        />
      )}
    </div>
  );
};

export default MarkdownWidget;