import React, { useState, useEffect } from 'react';
import { WidgetProps } from '@rjsf/utils';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { getCustomClassNames, getThemeAwarePredefinedColors, detectThemeMode } from '../utils';

/**
 * Shadcn UI Color Picker Widget
 * 
 * Used for rendering color picker
 */
const ColorWidget = ({
  id,
  value,
  onChange,
  onBlur,
  onFocus,
  disabled,
  readonly,
  autofocus,
  // options,
  // schema,
  uiSchema,
  required,
}: WidgetProps) => {
  const [color, setColor] = useState(value || '#000000');
  const wrapperClassName = getCustomClassNames(uiSchema);
  
  // Predefined color options - using theme-aware colors
  const predefinedColors = uiSchema?.['ui:options']?.predefinedColors || 
    getThemeAwarePredefinedColors(detectThemeMode());
  
  // Sync external value changes
  useEffect(() => {
    if (value !== undefined && value !== color) {
      setColor(value);
    }
  }, [value, color]);
  
  // Handle color changes
  const handleColorChange = (newColor: string) => {
    setColor(newColor);
    handleChange(newColor);
  };
  
  // Handle input change
  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newColor = event.target.value;
    handleColorChange(newColor);
  };
  
  // Handle predefined color selection
  const handlePredefinedColorClick = (predefinedColor: string) => {
    handleColorChange(predefinedColor);
  };
  
  const handleChange = (color: string) => {
    // 允许所有字段（包括必填字段）保持空字符串状态
    // 这样可以防止RJSF自动填充默认值
    if (color === '') {
      onChange(''); // 始终传递空字符串而不是 undefined
    } else {
      onChange(color);
    }
  };
  
  return (
    <div className={cn("flex items-center space-x-2", wrapperClassName)}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            type="button"
            variant="outline"
            className="w-10 p-0 border-2"
            style={{ backgroundColor: color }}
            disabled={disabled || readonly}
          >
            <span className="sr-only">Select color</span>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-64 p-3">
          <div className="space-y-2">
            <div className="flex flex-wrap gap-1">
              {Array.isArray(predefinedColors) && predefinedColors.map((predefinedColor: string) => (
                <button
                  key={predefinedColor}
                  type="button"
                  className={cn(
                    "w-6 h-6 rounded-md border border-border transition-all duration-200",
                    "hover:scale-110 hover:shadow-md",
                    color === predefinedColor && "ring-2 ring-primary ring-offset-2 ring-offset-background"
                  )}
                  style={{ backgroundColor: predefinedColor }}
                  onClick={() => handlePredefinedColorClick(predefinedColor)}
                  aria-label={`Select color ${predefinedColor}`}
                />
              ))}
            </div>
            
            <Input
              type="color"
              value={color}
              onChange={handleInputChange}
              className="w-full h-8"
              disabled={disabled || readonly}
            />
          </div>
        </PopoverContent>
      </Popover>
      
      <Input
        id={id}
        type="text"
        value={color}
        onChange={handleInputChange}
        onBlur={onBlur && ((event) => onBlur(id, event.target.value))}
        onFocus={onFocus && ((event) => onFocus(id, event.target.value))}
        disabled={disabled || readonly}
        readOnly={readonly}
        autoFocus={autofocus}
        required={required}
        pattern="^#[0-9A-Fa-f]{6}$"
        className="w-28 font-mono"
        aria-describedby={`${id}-description ${id}-error`}
      />
    </div>
  );
};

export default ColorWidget;
