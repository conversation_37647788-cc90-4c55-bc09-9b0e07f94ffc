import React, { useState } from 'react';
import { WidgetProps } from '@rjsf/utils';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { EyeIcon, EyeOffIcon } from 'lucide-react';
import { getCustomPlaceholder, getCustomClassNames } from '../utils';

/**
 * Shadcn UI Password Input Widget
 * 
 * Used for rendering password input with show/hide functionality
 */
const PasswordWidget = ({
  id,
  value,
  onChange,
  onBlur,
  onFocus,
  disabled,
  readonly,
  autofocus,
  options,
  // schema,
  uiSchema,
  placeholder,
  required,
}: WidgetProps) => {
  const [showPassword, setShowPassword] = useState(false);
  
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.value;
    
    // 允许所有字段（包括必填字段）保持空字符串状态
    // 这样可以防止RJSF自动填充默认值
    if (newValue === '') {
      onChange(''); // 始终传递空字符串而不是 undefined
    } else {
      onChange(newValue);
    }
  };
  
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };
  
  const customPlaceholder = getCustomPlaceholder(uiSchema, options, placeholder || 'Enter password');
  const wrapperClassName = getCustomClassNames(uiSchema, "relative");
  
  return (
    <div className={wrapperClassName}>
      <Input
        id={id}
        name={id}
        type={showPassword ? 'text' : 'password'}
        value={value || ''}
        placeholder={customPlaceholder}
        disabled={disabled || readonly}
        readOnly={readonly}
        autoFocus={autofocus}
        onChange={handleChange}
        onBlur={onBlur && ((event) => onBlur(id, event.target.value))}
        onFocus={onFocus && ((event) => onFocus(id, event.target.value))}
        className="pr-10"
        aria-describedby={`${id}-description ${id}-error`}
        required={required}
      />
      
      <Button
        type="button"
        variant="ghost"
        size="sm"
        className="absolute right-0 top-0 h-full px-3 text-gray-400 hover:text-gray-600"
        onClick={togglePasswordVisibility}
        tabIndex={-1}
        disabled={disabled || readonly}
        aria-label={showPassword ? 'Hide password' : 'Show password'}
      >
        {showPassword ? <EyeOffIcon className="h-4 w-4" /> : <EyeIcon className="h-4 w-4" />}
      </Button>
    </div>
  );
};

export default PasswordWidget;
