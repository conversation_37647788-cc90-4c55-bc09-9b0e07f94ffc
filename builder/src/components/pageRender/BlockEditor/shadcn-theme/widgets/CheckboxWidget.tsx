import React from 'react';
import { WidgetProps } from '@rjsf/utils';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { getCustomClassNames } from '../utils';

/**
 * Shadcn UI Checkbox Widget
 *
 * Used for rendering single checkbox
 */
const CheckboxWidget = ({
  id,
  value,
  onChange,
  onBlur,
  onFocus,
  disabled,
  readonly,
  label,
  // schema,
  uiSchema,
  autofocus,
}: WidgetProps) => {
  const handleChange = (checked: boolean) => {
    onChange(checked);
  };
  
  const wrapperClassName = getCustomClassNames(uiSchema, "flex items-center space-x-2");
  
  return (
    <div className={wrapperClassName}>
      <Checkbox
        id={id}
        checked={value === undefined ? false : value}
        onCheckedChange={handleChange}
        disabled={disabled || readonly}
        aria-describedby={`${id}-description ${id}-error`}
        onBlur={onBlur && (() => onBlur(id, value))}
        onFocus={onFocus && (() => onFocus(id, value))}
        autoFocus={autofocus}
      />
      <Label 
        htmlFor={id}
        className="text-sm font-normal cursor-pointer"
      >
        {label}
      </Label>
    </div>
  );
};

export default CheckboxWidget;
