// src/components/BlockEditor/useBlockForm.ts

import { useState, useCallback, useEffect, useRef, useMemo } from 'react';
import { useDebounceValue } from 'usehooks-ts';
import { equals, mergeDeepWith, is, isNil } from 'ramda';
import { splitData } from '@/components/DynamicFormGenerator';
// import { BlockEditorProps, SplitData, UseBlockFormReturn } from './types';

type MergeFunction = <T>(left: T, right: T) => T;

const customMerge: MergeFunction = mergeDeepWith((left: any, right: any) => {
  if (is(Array, left) && is(Array, right)) {
    return left.map((leftItem, index) => {
      const rightItem = right[index];
      if (isNil(rightItem)) {
        return leftItem;
      }
      if (is(Object, leftItem) && is(Object, rightItem)) {
        return customMerge(leftItem, rightItem);
      }
      return rightItem;
    });
  }
  if (is(Object, left) && is(Object, right)) {
    return customMerge(left, right);
  }
  return right;
});


export const useBlockForm = ({
  id,
  variant,
  config,
  initialData,
  onSave
}: any): any => {


  const [splitFormData, setSplitFormData] = useState<any>(() => {
    const init = customMerge(initialData, initialData.variants?.[variant] || {});
    return splitData(init, config.schemaConfig, variant);
  });

  const [debouncedSplitFormData] = useDebounceValue(splitFormData, 700);
  const lastSavedDataRef = useRef<any>(splitFormData);
  const isInitialMount = useRef(true);

const formData = useMemo(() => {
  // 使用 customMerge 来合并基础数据和变体数据
  const mergedData = customMerge(splitFormData.baseData, splitFormData.variantData[variant] || {});
  // console.log('Merged form data:', mergedData);
  return mergedData;
}, [splitFormData, variant]);

  useEffect(() => {
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }

    if (!equals(debouncedSplitFormData, lastSavedDataRef.current)) {
      onSave({
        id,
        variant,
        baseData: debouncedSplitFormData.baseData,
        variantData: {
          [variant]: debouncedSplitFormData.variantData[variant] || {}
        }
      });
      lastSavedDataRef.current = debouncedSplitFormData;
    }
  }, [debouncedSplitFormData, id, variant, onSave]);

  const handleChange = useCallback((newData: any) => {
    setSplitFormData((prevSplitData: any) => {
      const newSplitData = splitData(newData, config.schemaConfig, variant);
      return {
        baseData: newSplitData.baseData,
        variantData: {
          ...prevSplitData.variantData,
          [variant]: newSplitData.variantData[variant] || {}
        }
      };
    });
  }, [variant, config.schemaConfig]);

  const handleSubmit = useCallback((data: any) => {
    const newSplitData = splitData(data, config.schemaConfig, variant);
    setSplitFormData((prevSplitData: any) => ({
      baseData: newSplitData.baseData,
      variantData: {
        ...prevSplitData.variantData,
        [variant]: newSplitData.variantData[variant] || {}
      }
    }));
    onSave({
      id,
      variant,
      baseData: newSplitData.baseData,
      variantData: {
        [variant]: newSplitData.variantData[variant] || {}
      }
    });
    lastSavedDataRef.current = newSplitData;
  }, [id, variant, config.schemaConfig, onSave]);

  return {
    formData,
    handleChange,
    handleSubmit
  };
};