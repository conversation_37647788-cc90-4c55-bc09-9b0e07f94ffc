/**
 * ThemePreview Component
 * 
 * 用于预览和测试 BlockEditor 的主题适配效果
 */

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Sun, Moon, Palette } from 'lucide-react';
import { BlockEditorShadcn } from './BlockEditorShadcn';
import { BlockEditorThemeConfig } from './shadcn-theme/utils';

interface ThemePreviewProps {
  /** 示例配置 */
  sampleConfig?: any;
  /** 示例数据 */
  sampleData?: any;
}

const ThemePreview: React.FC<ThemePreviewProps> = ({
  sampleConfig,
  sampleData
}) => {
  const [isDark, setIsDark] = useState(false);
  const [themeConfig, setThemeConfig] = useState<Partial<BlockEditorThemeConfig>>({
    spacing: 'normal',
    borderRadius: 'md',
    animations: true
  });

  // 切换主题模式
  const toggleTheme = () => {
    setIsDark(!isDark);
    if (!isDark) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  };

  // 示例表单配置
  const defaultConfig = {
    schema: {
      type: 'object',
      properties: {
        title: {
          type: 'string',
          title: '标题',
          description: '请输入标题'
        },
        description: {
          type: 'string',
          title: '描述',
          description: '请输入描述信息'
        },
        enabled: {
          type: 'boolean',
          title: '启用状态',
          description: '是否启用此功能'
        },
        color: {
          type: 'string',
          title: '主题色',
          description: '选择主题颜色'
        },
        items: {
          type: 'array',
          title: '项目列表',
          items: {
            type: 'object',
            properties: {
              name: {
                type: 'string',
                title: '名称'
              },
              value: {
                type: 'string',
                title: '值'
              }
            }
          }
        }
      }
    },
    uiSchema: {
      title: {
        'ui:placeholder': '请输入标题...'
      },
      description: {
        'ui:widget': 'textarea',
        'ui:placeholder': '请输入描述...'
      },
      color: {
        'ui:widget': 'color'
      },
      items: {
        'ui:options': {
          addButtonText: '添加项目',
          orderable: true,
          removable: true
        }
      }
    }
  };

  const defaultData = {
    title: '示例标题',
    description: '这是一个示例描述，用于展示主题适配效果。',
    enabled: true,
    color: '#3b82f6',
    items: [
      { name: '项目1', value: '值1' },
      { name: '项目2', value: '值2' }
    ]
  };

  const handleSave = (data: any) => {
    console.log('保存数据:', data);
  };

  const updateThemeConfig = (key: keyof BlockEditorThemeConfig, value: any) => {
    setThemeConfig(prev => ({
      ...prev,
      [key]: value
    }));
  };

  return (
    <div className="min-h-screen bg-background text-foreground transition-colors duration-300">
      <div className="container mx-auto p-6 space-y-6">
        {/* 主题控制面板 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Palette className="w-5 h-5" />
              主题预览控制面板
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* 明暗模式切换 */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {isDark ? <Moon className="w-4 h-4" /> : <Sun className="w-4 h-4" />}
                <Label>主题模式</Label>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant={isDark ? "secondary" : "default"}>
                  {isDark ? '暗色' : '亮色'}
                </Badge>
                <Switch
                  checked={isDark}
                  onCheckedChange={toggleTheme}
                />
              </div>
            </div>

            {/* 间距配置 */}
            <div className="flex items-center justify-between">
              <Label>间距</Label>
              <div className="flex gap-2">
                {(['compact', 'normal', 'relaxed'] as const).map(spacing => (
                  <Button
                    key={spacing}
                    variant={themeConfig.spacing === spacing ? "default" : "outline"}
                    size="sm"
                    onClick={() => updateThemeConfig('spacing', spacing)}
                  >
                    {spacing === 'compact' ? '紧凑' : spacing === 'normal' ? '标准' : '宽松'}
                  </Button>
                ))}
              </div>
            </div>

            {/* 圆角配置 */}
            <div className="flex items-center justify-between">
              <Label>圆角</Label>
              <div className="flex gap-2">
                {(['none', 'sm', 'md', 'lg'] as const).map(radius => (
                  <Button
                    key={radius}
                    variant={themeConfig.borderRadius === radius ? "default" : "outline"}
                    size="sm"
                    onClick={() => updateThemeConfig('borderRadius', radius)}
                  >
                    {radius === 'none' ? '无' : radius.toUpperCase()}
                  </Button>
                ))}
              </div>
            </div>

            {/* 动画配置 */}
            <div className="flex items-center justify-between">
              <Label>动画效果</Label>
              <Switch
                checked={themeConfig.animations}
                onCheckedChange={(checked) => updateThemeConfig('animations', checked)}
              />
            </div>
          </CardContent>
        </Card>

        {/* BlockEditor 预览 */}
        <Card>
          <CardHeader>
            <CardTitle>BlockEditor 主题预览</CardTitle>
          </CardHeader>
          <CardContent>
            <BlockEditorShadcn
              config={sampleConfig || defaultConfig}
              initialData={sampleData || defaultData}
              onSave={handleSave}
              themeConfig={themeConfig}
            />
          </CardContent>
        </Card>

        {/* 主题适配检查清单 */}
        <Card>
          <CardHeader>
            <CardTitle>主题适配检查清单</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium">✅ 已适配项目</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• CSS 变量主题系统</li>
                  <li>• 语义化颜色类名</li>
                  <li>• 暗色模式支持</li>
                  <li>• 动画过渡效果</li>
                  <li>• 可访问性配置</li>
                </ul>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">🎯 优化效果</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• 主题切换流畅</li>
                  <li>• 颜色对比度良好</li>
                  <li>• 组件状态清晰</li>
                  <li>• 交互反馈及时</li>
                  <li>• 视觉层次分明</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ThemePreview; 