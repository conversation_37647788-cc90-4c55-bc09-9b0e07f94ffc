/**
 * BlockEditor Component
 * 
 * Block editor component for editing various blocks in the page
 * 
 * @component
 */

import React, { useCallback } from 'react';
// import Form from '@rjsf/core';
import { withTheme } from '@rjsf/core';
import validator from '@rjsf/validator-ajv8';
import Logger from '@/lib/logger';
import ShadcnTheme from './shadcn-theme';

const logger = new Logger('BlockEditor');

// Create a form with Shadcn theme using withTheme HOC
const ShadcnForm = withTheme(ShadcnTheme);

interface BlockFormConfig {
  schema: Record<string, any>;
  uiSchema?: Record<string, any>;
}

interface BlockEditorProps {
  /** Block configuration */
  config: BlockFormConfig;
  /** Initial data */
  initialData?: any;
  /** Save callback */
  onSave: (data: any) => void;
}

export const BlockEditor: React.FC<BlockEditorProps> = ({
  config,
  initialData,
  onSave,
}) => {
  logger.debug(`Initializing BlockEditor`, {
    hasInitialData: !!initialData
  });

  const handleSubmit = useCallback(({ formData }: any) => {
    logger.info(`Form submitted`);
    try {
      onSave(formData);
    } catch (error) {
      logger.error(`Error saving form data`, error);
    }
  }, [onSave]);

  if (!config?.schema) {
    logger.error(`Invalid block configuration`, new Error('Missing schema'));
    return null;
  }

  return (
    <ShadcnForm
      schema={config.schema}
      uiSchema={config.uiSchema}
      formData={initialData}
      validator={validator}
      onSubmit={handleSubmit}
      className="space-y-6"
    />
  );
};