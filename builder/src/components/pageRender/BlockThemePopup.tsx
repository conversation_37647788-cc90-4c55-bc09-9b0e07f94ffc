import { Fragment, useCallback, useEffect, useMemo } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { X } from 'lucide-react'
import { useBlockEditor } from '@/context/BlockEditorContext';
import { usePageStore } from '@/modules/page/store';

// import { formFactory } from './BlockEditor';
import { useWebsiteStore } from '@/modules/website/store';
import Panel from '../BoxDesigner/Panel';

export function BlockThemePopup({ domain, editable }: any) {
  const {isThemeOpened, setThemeOpened, curBlock, setCurBlock} = useBlockEditor();

  const headers = useWebsiteStore((state) => state.currentWebsite?.headers);
  const footers = useWebsiteStore((state) => state.currentWebsite?.footers);
  const currentPage = usePageStore((state) => state.currentPage);
  const configuration = usePageStore((state) => state.currentPage.configuration);

  const handleClose = useCallback(() => {
    setCurBlock('');
    setThemeOpened(false);
  },[setThemeOpened, setCurBlock ])

  const section = useMemo(() => {
    if (curBlock === headers?.[currentPage.language]?.id) {
      return { type: 'Header', ...headers?.[currentPage.language] };
    }
    if (curBlock === footers?.[currentPage.language]?.id) {
      return { type: 'Footer', ...footers?.[currentPage.language] };
    }
    return configuration?.sections?.find((item: any) => item.id === curBlock)
  }, [curBlock, configuration, headers, footers, currentPage.language])

  if (!editable) return null;

  return (
    <Transition.Root show={isThemeOpened} as={Fragment}>
      <Dialog as="div" className="relative z-20" onClose={handleClose}>
        <div className="fixed inset-0" />

        <div className="fixed inset-0 overflow-hidden">
          <div className="absolute inset-0 overflow-hidden">
            <div className="pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10">
              <Transition.Child
                as={Fragment}
                enter="transform transition ease-in-out duration-500 sm:duration-700"
                enterFrom="translate-x-full"
                enterTo="translate-x-0"
                leave="transform transition ease-in-out duration-500 sm:duration-700"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-full"
              >
                <Dialog.Panel className="pointer-events-auto w-[680px]">
                  <div className="flex h-full flex-col overflow-y-scroll bg-background py-6 shadow-xl border-l border-border">
                    <div className="px-4 sm:px-6">
                      <div className="flex items-start justify-between">
                        <Dialog.Title className="text-base font-semibold leading-6 text-foreground">
                          {curBlock} Panel
                        </Dialog.Title>
                        <div className="ml-3 flex h-7 items-center">
                          <button
                            type="button"
                            className="relative rounded-md bg-background text-muted-foreground hover:text-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                            onClick={handleClose}
                          >
                            <span className="absolute -inset-2.5" />
                            <span className="sr-only">Close panel</span>
                            <X className="h-6 w-6" aria-hidden="true" />
                          </button>
                        </div>
                      </div>
                    </div>
                    <div className="relative mt-6 flex-1 px-4 sm:px-6">
                      <Panel id={curBlock} section={section}></Panel>
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  )
}
