'use client'

import React, { useCallback, useEffect, useState } from 'react';
import { SectionProvider, PictureProvider, Format } from '@litpage/sections';
import { BlockThemePopup } from './BlockThemePopup';
import ImageEditor from '../image-editor';
import { ListEditorProvider } from "@litpage/sections";
import { EditableDialog } from './EditableDialog';
import { useDebounceCallback } from 'usehooks-ts'

import {
  Dialog,
  DialogContent,
} from "@/components/ui/dialog"
import usePageVersionStore from '@/modules/pageVersion/store';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useParams } from 'next/navigation';
import { usePageStore } from '@/modules/page/store';
import { useUserStore } from '@/modules/user/store';
import { clone, mergeDeepRight } from 'ramda';
import { PageThemePopup } from './PageThemePopup';
import { IconPicker } from '../IconPicker';
// import { createPageVersion } from '@/lib/api/page';
import { usePreviewStore } from '@/lib/store/preview-store';
import Logger from '@/lib/logger';
// import { fetchCurrentUser } from '@/lib/api/auth';
import { configKeys } from '@/lib/api/queryKeys';

const logger = new Logger('PageRenderProviders');

const PageRenderProviders = ({
  editable = false,
  domain,
  children,
}: any) => {

  const [isOpen, setIsOpen] = useState(false);
  const [editPos, setEditPos] = useState<string>('');

  const [isIconPickerOpen, setIsIconPickerOpen] = useState(false);
  const [iconEditPos, setIconEditPos] = useState<string | null>(null);

  const currentPage = usePageStore((state) => state.currentPage);
  const createPageVersion = usePageVersionStore((state) => state.createPageVersion);
  const { fetchCurrentUser } = useUserStore();

  let { slug } = useParams<any>();

  const queryClient = useQueryClient();

  useEffect(() => {
    if (editable) {
      logger.debug('Fetching current user data');
      fetchCurrentUser().catch(error => {
        logger.error('Failed to fetch user data', error as Error);
      });
    }
  }, [editable, fetchCurrentUser]);

  const createVersionMutation = useMutation({
    mutationFn: createPageVersion,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: configKeys.page() })
    },
  });

  const handleCreateVersion = (configuration: any) => {
    createVersionMutation.mutate({ pageId: slug, configuration });
  };

  const onImageEditClick = useCallback((at: string) => {
    setEditPos(at);
    setIsOpen(true)
  }, [])

  const onIconEditClick = useCallback((at: string) => {
    setIconEditPos(at);
    setIsIconPickerOpen(true);
  }, []);

  const onChange = useCallback((image: any, editPos: string | undefined) => {
    if (!editPos) return;

    const config: string[] = editPos.split('.');

    // 克隆当前页面配置，并根据需要进行修改
    const configuration = clone(currentPage.configuration);
    const sectionIndex = configuration.sections.findIndex((s: any) => s.id === config[0]);

    if (sectionIndex === -1) {
      // 处理未找到对应 section 的情况
      console.error('Section not found for id:', config[0]);
      return;
    }

    const section = configuration.sections[sectionIndex];

    const variant = section.variant;

    const url = `/${image.result.filename}`;

    let variantEdit: any = {};

    // object
    if (config[1].indexOf('[') === -1) {
      variantEdit = {
        [config[1]]: { url },
      };
    }

    if (config[1] && config[1].indexOf('[') > 0) {
      variantEdit = configuration.sections[sectionIndex]?.variants?.[section.variant] ?? {};
      const index = config[1]?.match(/\[(\d+)\]/)?.[1];
      variantEdit.images = variantEdit?.images ?? [];

      if (index && variantEdit.images[+index]) {
        variantEdit.images[index].url = url;
      } else {
        if (index) {
          variantEdit.images[index] = {
            url,
            alt: '',
          }
        }
      }
    }

    section.variants = {
      ...(configuration.sections[sectionIndex]?.variants || {}),
      [variant]: mergeDeepRight(configuration.sections[sectionIndex]?.variants?.[variant] || {}, variantEdit),
    };

    configuration.sections[sectionIndex] = section;
    handleCreateVersion(configuration);
    setIsOpen(false);
  }, [currentPage, handleCreateVersion, domain]);




  const debouncedCreateVersion = useDebounceCallback(handleCreateVersion, 500);
  const onTextChange = useCallback((text: string, at: string, conf: any) => {
    const config = at.split('.');
    const [sectionId, ...rest] = config;

    // 克隆当前页面配置
    const configuration = clone(currentPage.configuration);
    const sectionIndex = configuration.sections.findIndex((s: any) => s.id === sectionId);

    if (sectionIndex === -1) {
      console.error('Section not found for id:', sectionId);
      return;
    }

    const section = configuration.sections[sectionIndex];

    // 更新文本内容
    const updateText = (obj: any, keys: string[], value: string): boolean => {
      const [key, ...remainingKeys] = keys;
      if (remainingKeys.length === 0) {
        if (obj[key] !== value) {
          obj[key] = value;
          return true;
        }
        return false;
      }
      if (key.includes('@')) {
        const [arrayKey, id] = key.split('@');
        const target = obj[arrayKey].find((item: any) => item.id === id);
        if (target) {
          return updateText(target, remainingKeys, value);
        }
      } else {
        if (!obj[key]) obj[key] = {};
        return updateText(obj[key], remainingKeys, value);
      }
      return false;
    };

    updateText(section, rest, text);

    // 更新自定义配置
    section.customize = {
      ...section.customize,
      [rest.join('.')]: {
        ...section.customize?.[rest.join('.')],
        ...conf,
      }
    };

    configuration.sections[sectionIndex] = section;
    debouncedCreateVersion(configuration);

  }, [currentPage, handleCreateVersion]);

  const onIconChange = useCallback((iconName: string) => {
    if (!iconEditPos) return;

    const [sectionId, ...pathParts] = iconEditPos.split('.');

    const updatedConfiguration = clone(currentPage.configuration);
    const sectionIndex = updatedConfiguration.sections.findIndex((s: any) => s.id === sectionId);

    if (sectionIndex === -1) {
      console.error('Section not found for id:', sectionId);
      return;
    }

    let section = updatedConfiguration.sections[sectionIndex];

    // Ensure variants exists
    if (!section.variants) {
      section.variants = {};
    }

    // Ensure basic variant exists
    if (!section.variants.basic) {
      section.variants.basic = {};
    }

    // Ensure features array exists in basic variant
    if (!section.variants.basic.features) {
      section.variants.basic.features = [];
    }

    // Extract index from the path
    const featureIndex = parseInt((pathParts[pathParts.length - 2] as any).match(/\d+/)[0], 10);

    // Ensure the feature at the specified index exists
    while (section.variants.basic.features.length <= featureIndex) {
      section.variants.basic.features.push({});
    }

    // Update the icon for the correct feature
    section.variants.basic.features[featureIndex] = {
      ...section.variants.basic.features[featureIndex],
      icon: iconName
    };

    // Update the section in the configuration
    updatedConfiguration.sections[sectionIndex] = section;

    handleCreateVersion(updatedConfiguration);
    setIsIconPickerOpen(false);
    setIconEditPos(null);
  }, [currentPage, handleCreateVersion, iconEditPos]);



  const isDevelopment = process.env.NODE_ENV === 'development';
  // Picture组件配置
  const pictureConfig = {
    baseURL: `https://${isDevelopment ? 'imgpipe-test' : domain}.imgpipe.net`,
    defaultFormats: ['avif', 'webp', 'jpeg'] as Format[],
    defaultQuality: 75,
    // sign: (url: string) => `${url}`
  };

  return (
    <ListEditorProvider isEditing={editable}>
      <PictureProvider config={pictureConfig}>
        <SectionProvider initialIsEditMode={editable} domain={domain} editConfig={{ defaultImage: '', guideText: 'aaaaaaa' }} onImageEditClick={onImageEditClick} onIconEditClick={onIconEditClick} onTextChange={onTextChange}>
          {children}
        
        <BlockThemePopup editable></BlockThemePopup>
        <PageThemePopup editable></PageThemePopup>
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogContent className="max-w-7xl p-12 max-h-[90vh]">
            <ImageEditor editPos={editPos} onChange={onChange}></ImageEditor>
          </DialogContent>
        </Dialog>
        <EditableDialog></EditableDialog>
        <IconPicker
          onSelect={onIconChange}
          currentIcon=""
          isOpen={isIconPickerOpen}
          onOpenChange={setIsIconPickerOpen}
        />
        </SectionProvider>
      </PictureProvider>
    </ListEditorProvider>
  );
};

export default PageRenderProviders;
