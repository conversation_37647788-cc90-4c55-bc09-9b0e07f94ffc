import { useCallback } from 'react'
import { ArrowLeft } from 'lucide-react'
import { useParams } from 'next/navigation'
import { nanoid } from 'nanoid'
import { useQueryClient } from '@tanstack/react-query'
import { useBlockEditor } from '@/context/BlockEditorContext'
import { useToast } from "@/components/ui/use-toast"
import { useCreateWaitlistForm } from '@/modules/litform/hooks'
import { useAddSection } from '@/lib/hooks/use-section-query'
import { useSectionUpdate } from './hooks/useSectionUpdate'
import { pageKeys } from '@/lib/api/queryKeys'
import BlockSelector from './BlockSelector'
import Logger from '@/lib/logger'
import { usePreviewStore } from '@/lib/store/preview-store'

const logger = new Logger('CreateSectionPopup')

interface CreateSectionPopupProps {
  domain: string
  editable: boolean
  children?: React.ReactNode
}

export function CreateSectionPopup({ domain, editable, children }: CreateSectionPopupProps) {
  const { isCreateSectionOpened, setCreateSectionOpened, curBlock, setCurBlock } = useBlockEditor()
  const setActiveTab = usePreviewStore((state) => state.setActiveTab)
  const { handleUpdateAllSection } = useSectionUpdate()
  const createWaitlistForm = useCreateWaitlistForm()
  const { toast } = useToast()
  const queryClient = useQueryClient()
  const { pageId } = useParams<{ pageId: string }>() || { pageId: '' }
  
  // 使用 React Query mutation 替代 Zustand store 操作
  const addSectionMutation = useAddSection()

  const handleClose = useCallback(() => {
    setCurBlock('')
    setCreateSectionOpened(false)
    setActiveTab('edit') // Return to block list
  }, [setCreateSectionOpened, setCurBlock, setActiveTab])

  const handleCreateSectionClick = useCallback(async (type: string) => {
    logger.info('Creating new section', { type, blockId: pageId, afterSectionId: curBlock })

    try {
      // 确定 afterSectionId
      // 如果 curBlock 是 'end'，则添加到末尾
      // 否则，添加到当前区块之后
      const afterSectionId = curBlock === 'end' ? null : curBlock

      if (type === 'Waitlist') {
        logger.debug('Creating waitlist form')
        const formData = await createWaitlistForm.mutateAsync({ pageId })
        
        logger.debug('Adding waitlist section', { formId: formData.formId, afterSectionId })
        await addSectionMutation.mutateAsync({
          pageId,
          afterSectionId,
          sectionData: {
            id: nanoid(7),
            type: 'Waitlist',
            variant: 'default',
            data: {
              formId: formData.formId,
              embedCode: formData.embedCode
            }
          }
        })

        toast({
          title: "Waitlist Created",
          description: "Waitlist form has been created successfully."
        })
      } else {
        await addSectionMutation.mutateAsync({
          pageId,
          afterSectionId,
          sectionData: {
            id: nanoid(7),
            type,
            variant: 'default',
            data: {}
          }
        })
      }

      // 从缓存中获取最新的页面配置
      const currentPage = queryClient.getQueryData(pageKeys.fetchPage(pageId)) as any
      if (currentPage?.configuration) {
        handleUpdateAllSection(currentPage.configuration)
      }
      
      handleClose()
    } catch (error) {
      logger.error('Error creating section', error as Error)
      toast({
        title: "Error",
        description: "Failed to create section",
        variant: "destructive"
      })
    }
  }, [pageId, addSectionMutation, handleUpdateAllSection, createWaitlistForm, toast, handleClose, queryClient, curBlock])

  if (!editable) return null

  // No longer using popup windows, rendering content directly
  return (
    <div className="h-full flex flex-col bg-background">
      <div className="flex items-center justify-between border-b border-border py-3 px-4">
        <button
          onClick={handleClose}
          className="inline-flex items-center text-muted-foreground hover:text-foreground transition-colors"
        >
          <ArrowLeft size={18} />
        </button>
        <h2 className="text-sm font-medium text-center text-foreground">Add New Block</h2>
        <div className="w-6"></div> {/* Placeholder to keep title centered */}
      </div>

      <div className="flex-1 overflow-auto p-4">
        <BlockSelector onSelect={handleCreateSectionClick} />
      </div>
    </div>
  )
}
