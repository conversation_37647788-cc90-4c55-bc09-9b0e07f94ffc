import React, { useCallback, useEffect, useState } from 'react';
import { PencilIcon, ArrowUp, ArrowDown, X, Wand, Plus, Copy } from 'lucide-react';
import { useBlockEditor } from '@/context/BlockEditorContext';
import { useSectionUpdate } from '../hooks/useSectionUpdate';
import { cn } from '@/lib/utils';
import Logger from '@/lib/logger';
import { sectionComponents, variants } from '@litpage/sections';
import { usePreviewStore } from '@/lib/store/preview-store';
import { useParams } from 'next/navigation';
import { useFetchPage } from '@/lib/hooks/use-page-query';
import { useFetchWebsite } from '@/lib/hooks/use-website';
import { useEditVariant, useDeleteSection, useMoveSection, useAddSection, useEditSection, useCloneSection } from '@/lib/hooks/use-section-query';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

const logger = new Logger('BlockList');

interface BlockListProps {
  domain?: string;
}

export const BlockList: React.FC<BlockListProps> = ({ domain }) => {
  const { siteId, pageId } = useParams<{ siteId: string; pageId: string }>() || { siteId: '', pageId: '' };
  const { setCurBlock, setEditorOpened, setCreateSectionOpened, setScrollToBlockId } = useBlockEditor();
  const setActiveTab = usePreviewStore((state) => state.setActiveTab);
  const { handleUpdateAllSection } = useSectionUpdate();
  const [sectionVariants, setSectionVariants] = useState<Record<string, string[]>>({});
  const [sectionToDelete, setSectionToDelete] = useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // React Query hooks
  const { data: currentPage } = useFetchPage(pageId);
  const { data: currentWebsite } = useFetchWebsite(siteId);
  const editVariantMutation = useEditVariant();
  const deleteSectionMutation = useDeleteSection();
  const moveSectionMutation = useMoveSection();
  const cloneSectionMutation = useCloneSection();

  // Get variant list from @litpage/sections
  useEffect(() => {
    try {
      // Set variant list for each block
      const blockVariants: Record<string, string[]> = {};
      if (currentPage?.configuration?.sections) {
        currentPage.configuration.sections.forEach((section: any) => {
          // Use variants object imported from @litpage/sections to get variant list
          blockVariants[section.id] = variants[section.type] || ['default'];
        });
      }

      setSectionVariants(blockVariants);
    } catch (error) {
      logger.error('Error setting section variants', error as Error);
    }
  }, [currentPage?.configuration?.sections]);

  const handleEditClick = useCallback((id: string) => {
    setCurBlock(id);
    setEditorOpened(true);
    setActiveTab('editor'); // Switch to editor tab
    setScrollToBlockId(id); // Set block ID to scroll to
  }, [setCurBlock, setEditorOpened, setActiveTab, setScrollToBlockId]);

  const handleCreateSectionClick = useCallback((id: string) => {
    setCurBlock(id);
    setCreateSectionOpened(true);
    setActiveTab('creator'); // Switch to creator tab
  }, [setCurBlock, setCreateSectionOpened, setActiveTab]);

  const handleStyleChange = useCallback((id: string, currentVariant: string, type: string) => {
    try {
      // Get variant list for current block
      const blockVariants = sectionVariants[id] || ['default'];
      const index = blockVariants.indexOf(currentVariant);
      const newIndex = (index + 1) % blockVariants.length;
      const newVariant = blockVariants[newIndex];

      logger.info(`Changing variant for ${id} from ${currentVariant} to ${newVariant}`);

      if (type === 'Header' || type === 'Footer') {
        if (!currentWebsite?.id) {
          logger.error('Website ID is missing', new Error('Website ID is missing'));
          return;
        }

        if (!currentPage?.language) {
          logger.error('Page language is missing', new Error('Page language is missing'));
          return;
        }

        // Use the same logic as BlockContainer to update navigation config
        const updateNavConfig = {
          websiteId: currentWebsite.id,
          config: {
            ...currentWebsite[type === 'Header' ? 'headers': 'footers'][currentPage.language],
            type,
            lang: currentPage.language,
            logo: { url: currentWebsite.logo ?? '', alt: '' },
            variant: newVariant,
          },
        };

        // Use React Query mutation for variant change
        editVariantMutation.mutateAsync({
          pageId,
          sectionId: id,
          variant: newVariant
        }).then(() => {
          // Update the UI with the new configuration
          if (currentPage?.configuration) {
            handleUpdateAllSection(currentPage.configuration);
          }
          // Scroll to the block after variant change
          setScrollToBlockId(id);
        });
      } else {
        // Use React Query mutation for variant change
        editVariantMutation.mutateAsync({
          pageId,
          sectionId: id,
          variant: newVariant
        }).then(() => {
          // Update the UI with the new configuration
          if (currentPage?.configuration) {
            handleUpdateAllSection(currentPage.configuration);
          }
          // Scroll to the block after variant change
          setScrollToBlockId(id);
        });
      }
    } catch (error) {
      logger.error(`Error changing variant for ${id}`, error as Error);
    }
  }, [sectionVariants, editVariantMutation, handleUpdateAllSection, currentWebsite, currentPage, pageId, setScrollToBlockId]);

  const handleMove = useCallback((id: string, type: string, direction: 'up' | 'down') => {
    moveSectionMutation.mutateAsync({
      pageId,
      sectionId: id,
      direction
    }).then(() => {
      // Update the UI with the new configuration
      if (currentPage?.configuration) {
        handleUpdateAllSection(currentPage.configuration);
      }

      // Calculate the ID of the block to scroll to after moving
      // When moving up, we should scroll to the current block
      // When moving down, we should also scroll to the current block
      setScrollToBlockId(id);
    });
  }, [moveSectionMutation, pageId, handleUpdateAllSection, currentPage, setScrollToBlockId]);

  const confirmDeleteSection = useCallback((id: string) => {
    setSectionToDelete(id);
    setIsDeleteDialogOpen(true);
  }, []);

  const handleDeleteSection = useCallback((id: string) => {
    deleteSectionMutation.mutateAsync({
      pageId,
      sectionId: id
    }).then(() => {
      // Update the UI with the new configuration
      if (currentPage?.configuration) {
        handleUpdateAllSection(currentPage.configuration);
      }

      // After deletion, no need to scroll as the block no longer exists
      // But we should reset the scroll state to avoid unexpected behavior
      setScrollToBlockId(null);
      setSectionToDelete(null);
      setIsDeleteDialogOpen(false);
    });
  }, [deleteSectionMutation, pageId, handleUpdateAllSection, currentPage, setScrollToBlockId]);

  const handleCloneSection = useCallback((id: string) => {
    cloneSectionMutation.mutateAsync({
      pageId,
      sectionId: id
    }).then(() => {
      // Update the UI with the new configuration
      if (currentPage?.configuration) {
        handleUpdateAllSection(currentPage.configuration);
      }

      // Scroll to the cloned block (which will be after the current block)
      setScrollToBlockId(id);
    });
  }, [cloneSectionMutation, pageId, handleUpdateAllSection, currentPage, setScrollToBlockId]);

  // Get all blocks from the page
  const sections = currentPage?.configuration?.sections || [];
  const headers = currentWebsite?.headers || {};
  const footers = currentWebsite?.footers || {};

  return (
    <div className="h-full overflow-auto p-4">
      <h2 className="text-lg font-semibold mb-4 text-foreground">Page Blocks</h2>

      <div className="space-y-3">
        {/* Header */}
        {currentPage?.language && headers[currentPage.language]?.id && (
          <div className="bg-card rounded-lg shadow-sm border border-border p-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-10 h-10 bg-primary/10 rounded-md flex items-center justify-center mr-3">
                  <span className="text-primary font-semibold">H</span>
                </div>
                <div>
                  <h3 className="font-medium text-card-foreground">Header</h3>
                  <p className="text-sm text-muted-foreground">Page navigation</p>
                </div>
              </div>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      className="p-2 text-muted-foreground hover:text-primary transition-colors"
                      onClick={() => currentPage?.language && handleEditClick(headers[currentPage.language]?.id)}
                    >
                      <PencilIcon size={18} />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Edit header</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
        )}

        {/* Content Sections */}
        {sections.map((section: any, index: number) => {
          const blockVariants = sectionVariants[section.id] || ['default'];
          const hasMultipleVariants = blockVariants.length > 1;

          return (
            <div key={section.id} className="bg-card rounded-lg shadow-sm border border-border p-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-primary/10 rounded-md flex items-center justify-center mr-3">
                    <span className="text-primary font-semibold">{section.type.charAt(0)}</span>
                  </div>
                  <div>
                    <h3 className="font-medium text-card-foreground">{section.type}</h3>
                    <p className="text-sm text-muted-foreground">
                      Variant: {section.variant}
                      {hasMultipleVariants && (
                        <span className="text-xs text-muted-foreground/70 ml-1">
                          ({blockVariants.indexOf(section.variant) + 1}/{blockVariants.length})
                        </span>
                      )}
                    </p>
                    {section.title && (
                      <p className="text-sm text-primary mt-1 font-medium truncate max-w-[180px]">
                        {typeof section.title === 'object' && section.title.text 
                          ? section.title.text 
                          : section.title}
                      </p>
                    )}
                    {!section.title && section.content?.title && (
                      <p className="text-sm text-primary mt-1 font-medium truncate max-w-[180px]">
                        {typeof section.content.title === 'object' && section.content.title.text 
                          ? section.content.title.text 
                          : section.content.title}
                      </p>
                    )}
                    {!section.title && !section.content?.title && section.properties?.title && (
                      <p className="text-sm text-primary mt-1 font-medium truncate max-w-[180px]">
                        {typeof section.properties.title === 'object' && section.properties.title.text 
                          ? section.properties.title.text 
                          : section.properties.title}
                      </p>
                    )}
                  </div>
                </div>
                <div className="flex space-x-1">
                  <TooltipProvider>
                    {hasMultipleVariants && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <button
                            className="p-2 text-muted-foreground hover:text-primary transition-colors"
                            onClick={() => handleStyleChange(section.id, section.variant, section.type)}
                          >
                            <Wand size={18} />
                          </button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Switch style variant</p>
                        </TooltipContent>
                      </Tooltip>
                    )}
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <button
                          className="p-2 text-muted-foreground hover:text-primary transition-colors"
                          onClick={() => handleEditClick(section.id)}
                        >
                          <PencilIcon size={18} />
                        </button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Edit block</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </div>

              <div className="mt-3 pt-3 border-t border-border flex justify-between">
                <div className="flex space-x-2">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <button
                          className={cn(
                            "w-8 h-8 flex items-center justify-center rounded-full transition-colors",
                            "bg-accent text-accent-foreground hover:bg-accent/80",
                            "disabled:opacity-50 disabled:cursor-not-allowed"
                          )}
                          onClick={() => handleCreateSectionClick(section.id)}
                        >
                          <Plus size={16} />
                        </button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Add new block</p>
                      </TooltipContent>
                    </Tooltip>

                    <Tooltip>
                      <TooltipTrigger asChild>
                        <button
                          className={cn(
                            "w-8 h-8 flex items-center justify-center rounded-full transition-colors",
                            "bg-accent text-accent-foreground hover:bg-accent/80",
                            "disabled:opacity-50 disabled:cursor-not-allowed"
                          )}
                          onClick={() => handleCloneSection(section.id)}
                        >
                          <Copy size={16} />
                        </button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Clone block</p>
                      </TooltipContent>
                    </Tooltip>

                    <Tooltip>
                      <TooltipTrigger asChild>
                        <button
                          disabled={index === 0}
                          className={cn(
                            "w-8 h-8 flex items-center justify-center rounded-full transition-colors",
                            "bg-accent text-accent-foreground hover:bg-accent/80",
                            "disabled:opacity-50 disabled:cursor-not-allowed"
                          )}
                          onClick={() => handleMove(section.id, section.type, 'up')}
                        >
                          <ArrowUp size={16} />
                        </button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Move up</p>
                      </TooltipContent>
                    </Tooltip>

                    <Tooltip>
                      <TooltipTrigger asChild>
                        <button
                          disabled={index === sections.length - 1}
                          className={cn(
                            "w-8 h-8 flex items-center justify-center rounded-full transition-colors",
                            "bg-accent text-accent-foreground hover:bg-accent/80",
                            "disabled:opacity-50 disabled:cursor-not-allowed"
                          )}
                          onClick={() => handleMove(section.id, section.type, 'down')}
                        >
                          <ArrowDown size={16} />
                        </button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Move down</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>

                <TooltipProvider>
                  <AlertDialog open={sectionToDelete === section.id && isDeleteDialogOpen} onOpenChange={(open) => {
                    if (!open) {
                      setIsDeleteDialogOpen(false);
                    }
                  }}>
                    <AlertDialogTrigger asChild>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <button
                            className={cn(
                              "w-8 h-8 flex items-center justify-center rounded-full transition-colors",
                              "bg-destructive/10 text-destructive hover:bg-destructive/20",
                            )}
                            onClick={() => confirmDeleteSection(section.id)}
                          >
                            <X size={16} />
                          </button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Delete block</p>
                        </TooltipContent>
                      </Tooltip>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                          This action cannot be undone. This will permanently delete the
                          &quot;{section.type}&quot; block and remove it from the page.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel onClick={() => setIsDeleteDialogOpen(false)}>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={() => handleDeleteSection(section.id)} className="bg-red-600 hover:bg-red-700">
                          Delete
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </TooltipProvider>
              </div>
            </div>
          );
        })}

        {/* Footer */}
        {currentPage?.language && footers[currentPage.language]?.id && (
          <div className="bg-card rounded-lg shadow-sm border border-border p-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-10 h-10 bg-primary/10 rounded-md flex items-center justify-center mr-3">
                  <span className="text-primary font-semibold">F</span>
                </div>
                <div>
                  <h3 className="font-medium text-card-foreground">Footer</h3>
                  <p className="text-sm text-muted-foreground">Page footer</p>
                </div>
              </div>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      className="p-2 text-muted-foreground hover:text-primary transition-colors"
                      onClick={() => currentPage?.language && handleEditClick(footers[currentPage.language]?.id)}
                    >
                      <PencilIcon size={18} />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Edit footer</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
        )}

        {/* Add section at the end */}
        <div className="mt-6 flex justify-center">
          <button
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
            onClick={() => handleCreateSectionClick('end')}
          >
            <Plus size={18} className="mr-2 inline" />
            Add New Block
          </button>
        </div>
      </div>
    </div>
  );
};
