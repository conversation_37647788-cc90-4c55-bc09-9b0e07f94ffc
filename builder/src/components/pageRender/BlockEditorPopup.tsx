import { useCallback, useMemo, useEffect } from 'react'
import { ArrowLeft } from 'lucide-react'
import { useParams } from 'next/navigation'
import { useBlockEditor } from '@/context/BlockEditorContext';
import { usePreviewStore } from '@/lib/store/preview-store';
import { formFactory } from './BlockEditor'
import { useFetchPage } from '@/lib/hooks/use-page-query'
import Logger from '@/lib/logger'

// 语言映射，将不标准的语言代码映射到标准代码
const LANGUAGE_MAP: Record<string, string> = {
  'CN': 'zh', // 将 CN 映射到 zh
  'EN': 'en', // 将 EN 映射到 en
  'ZH': 'zh'  // 将 ZH 映射到 zh
};

// 获取标准化的语言代码
function getNormalizedLanguage(langCode: string): string {
  return LANGUAGE_MAP[langCode] || langCode;
}

const logger = new Logger('BlockEditorPopup');

export function BlockEditorPopup({ domain, editable }: any) {
  const { isEditorOpened, setEditorOpened, curBlock, setCurBlock, setScrollToBlockId } = useBlockEditor();
  const setActiveTab = usePreviewStore((state) => state.setActiveTab);
  const { pageId } = useParams<{ pageId: string }>() || { pageId: '' };
  
  // 使用 React Query 获取页面数据
  const { data: currentPage } = useFetchPage(pageId);
  const configuration = currentPage?.configuration;

  // 添加调试日志
  useEffect(() => {
    if (!currentPage) return;
    
    logger.debug(`BlockEditorPopup rendered`, {
      curBlock,
      currentPageLanguage: currentPage.language,
      normalizedLanguage: getNormalizedLanguage(currentPage.language || 'en'),
      isHeaderSelected: curBlock === 'Header' || (currentPage.header?.id === curBlock),
      isFooterSelected: curBlock === 'Footer' || (currentPage.footer?.id === curBlock)
    });
  }, [curBlock, currentPage]);

  const handleClose = useCallback(() => {
    setCurBlock('');
    setEditorOpened(false);
    setActiveTab('edit'); // Return to block list
    setScrollToBlockId(null); // Reset scroll state
  }, [setEditorOpened, setCurBlock, setActiveTab, setScrollToBlockId]);

  const section = useMemo(() => {
    if (!currentPage) return null;
    
    logger.debug(`Computing section for block: ${curBlock}`, {
      currentLanguage: currentPage.language,
      normalizedLanguage: getNormalizedLanguage(currentPage.language || 'en')
    });

    // 处理 curBlock 直接是 "Header" 或 "Footer" 的情况
    if (curBlock === 'Header') {
      // 直接使用 currentPage.header 数据
      const headerSection = { 
        type: 'Header', 
        variant: currentPage.header?.variant || 'default',
        id: currentPage.header?.id || 'Header',
        ...currentPage.header?.configuration
      };
      logger.debug(`Created header section from string match`, headerSection);
      return headerSection;
    }

    if (curBlock === 'Footer') {
      // 直接使用 currentPage.footer 数据
      const footerSection = { 
        type: 'Footer', 
        variant: currentPage.footer?.variant || 'default',
        id: currentPage.footer?.id || 'Footer',
        ...currentPage.footer?.configuration
      };
      logger.debug(`Created footer section from string match`, footerSection);
      return footerSection;
    }

    // 检查 curBlock 是否匹配 header 或 footer 的 ID
    if (currentPage.header && curBlock === currentPage.header.id) {
      const headerSection = { 
        type: 'Header', 
        variant: currentPage.header.variant || 'default',
        id: currentPage.header.id,
        ...currentPage.header.configuration
      };
      logger.debug(`Created header section from ID match`, headerSection);
      return headerSection;
    }
    
    if (currentPage.footer && curBlock === currentPage.footer.id) {
      const footerSection = { 
        type: 'Footer', 
        variant: currentPage.footer.variant || 'default',
        id: currentPage.footer.id,
        ...currentPage.footer.configuration
      };
      logger.debug(`Created footer section from ID match`, footerSection);
      return footerSection;
    }
    
    const configSection = configuration?.sections?.find((item: any) => item.id === curBlock);
    logger.debug(`Found config section for ID: ${curBlock}`, configSection ? true : false);
    return configSection;
  }, [curBlock, configuration, currentPage]);

  if (!editable) return null;

  // No longer using popup windows, rendering content directly
  return (
    <div className="h-full flex flex-col bg-background">
      <div className="flex items-center justify-between border-b border-border py-3 px-4">
        <button
          onClick={handleClose}
          className="inline-flex items-center text-muted-foreground hover:text-foreground transition-colors"
        >
          <ArrowLeft size={18} />
        </button>
        <h2 className="text-sm font-medium text-center text-foreground">
          {section?.type ? `Edit ${section.type}` : 'Edit Block'}
        </h2>
        <div className="w-6"></div> {/* Placeholder to keep title centered */}
      </div>

      <div className="flex-1 overflow-x-hidden overflow-y-auto p-4">
        <div className="w-full max-w-full">
          {!currentPage && (
            <div className="p-4 text-center">
              <p className="text-muted-foreground">Loading...</p>
            </div>
          )}
          
          {currentPage && !section && (
            <div className="p-4 border border-destructive rounded bg-destructive/10 text-destructive">
              <h3 className="font-bold mb-2">Error: Block not found</h3>
              <p>ID: {curBlock}</p>
            </div>
          )}
          
          {section && formFactory(curBlock, section, domain)}
        </div>
      </div>
    </div>
  );
}
