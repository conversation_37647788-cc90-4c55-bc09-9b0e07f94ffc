import React from 'react';
import { IconItemProps } from './types';
import { IconRenderer } from '@litpage/sections';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

const IconItem: React.FC<IconItemProps> = ({ name, isSelected, onSelect, size, color }) => {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div 
            className={`p-2 flex flex-col items-center justify-center cursor-pointer rounded-md transition-colors ${
              isSelected ? 'bg-blue-100 dark:bg-blue-900' : 'hover:bg-gray-100 dark:hover:bg-gray-800'
            }`}
            onClick={onSelect}
          >
            <IconRenderer name={name} size={size} color={color} />
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p>{name}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default IconItem;