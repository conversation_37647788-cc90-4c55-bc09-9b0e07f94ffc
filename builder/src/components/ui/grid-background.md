# Grid Background Components

这个文件包含了多个支持明暗主题的网格背景组件，用于不同的场景。

## 组件列表

### 1. GridBackground (基础网格组件)

基础的网格背景组件，支持多种变体和自定义选项。

```tsx
import { GridBackground } from '@/components/ui/grid-background';

<GridBackground 
  gridSize={20} 
  opacity={0.1} 
  variant="default"
  animated={false}
>
  <YourContent />
</GridBackground>
```

**Props:**
- `gridSize`: 网格大小 (默认: 20px)
- `opacity`: 网格透明度 (默认: 0.1)
- `variant`: 网格变体 ('default' | 'dots' | 'cross' | 'diagonal')
- `animated`: 是否启用动画效果 (默认: false)

### 2. MobilePreviewGrid (移动预览点阵)

专门用于移动预览区域的点阵背景，使用双层点阵营造深度感，更适合移动设备预览场景。

```tsx
import { MobilePreviewGrid } from '@/components/ui/grid-background';

// 默认点阵模式 - 推荐用于移动预览
<MobilePreviewGrid variant="dots" gridSize={16} opacity={0.12}>
  <div className="p-4 w-full max-w-sm mx-auto">
    <div className="h-[600px] w-full overflow-auto bg-white/90 shadow-lg rounded-lg">
      <MobileComponent />
    </div>
  </div>
</MobilePreviewGrid>

// 精细点阵模式 - 更细腻的效果
<MobilePreviewGrid variant="fine-dots" gridSize={12} opacity={0.1}>
  {children}
</MobilePreviewGrid>

// 传统网格模式 - 经典线条网格
<MobilePreviewGrid variant="grid" gridSize={20} opacity={0.08}>
  {children}
</MobilePreviewGrid>
```

**特点：**
- 双层点阵设计，营造视觉深度
- 极低透明度 (0.08-0.15)，不干扰内容
- 渐变背景增强立体感
- 微妙内阴影效果
- 完美适配移动预览场景

### 3. DesignerGrid (设计器网格)

用于设计器界面的网格背景。

```tsx
import { DesignerGrid } from '@/components/ui/grid-background';

<DesignerGrid gridSize={24} opacity={0.08}>
  <DesignerContent />
</DesignerGrid>
```

### 4. CanvasGrid (画布网格)

用于画布区域的多层次网格背景，包含细网格和粗网格。

```tsx
import { CanvasGrid } from '@/components/ui/grid-background';

<CanvasGrid>
  <CanvasContent />
</CanvasGrid>
```

### 5. PreviewGrid (预览网格)

用于一般预览区域的网格背景。

```tsx
import { PreviewGrid } from '@/components/ui/grid-background';

<PreviewGrid gridSize={16} opacity={0.12}>
  <PreviewContent />
</PreviewGrid>
```

## 主题适应

所有网格组件都使用 CSS 变量来实现主题适应：

- **亮色模式**: 使用浅色网格线
- **暗色模式**: 使用深色网格线
- **自动切换**: 根据系统主题或用户设置自动切换

## CSS 类支持

除了 React 组件，还提供了对应的 CSS 类：

```css
.mobile-preview-grid
.designer-grid
.canvas-grid
.dots-grid
.diagonal-grid
.cross-grid
```

## 可访问性

- 支持 `prefers-contrast: high` 媒体查询
- 支持 `prefers-reduced-motion: reduce` 媒体查询
- 响应式网格大小适配移动设备

## 使用场景

1. **MobilePreviewGrid**: 移动预览区域
2. **DesignerGrid**: 设计器界面
3. **CanvasGrid**: 画布编辑区域
4. **PreviewGrid**: 一般预览区域
5. **GridBackground**: 自定义场景

## 性能优化

- 使用 CSS 渐变而非图片，减少资源加载
- 支持硬件加速
- 响应式设计，适配不同屏幕尺寸 