import React from 'react';
import { cn } from '@/lib/utils';

interface GridBackgroundProps {
  children?: React.ReactNode;
  className?: string;
  gridSize?: number;
  opacity?: number;
  variant?: 'default' | 'dots' | 'cross' | 'diagonal' | 'fine-dots' | 'grid';
  animated?: boolean;
}

export const GridBackground: React.FC<GridBackgroundProps> = ({
  children,
  className,
  gridSize = 20,
  opacity = 0.1,
  variant = 'default',
  animated = false
}) => {
  const getGridPattern = () => {
    switch (variant) {
      case 'dots':
        return {
          backgroundImage: `radial-gradient(circle, hsl(var(--border) / ${opacity}) 1px, transparent 1px)`,
          backgroundSize: `${gridSize}px ${gridSize}px`
        };
      case 'cross':
        return {
          backgroundImage: `
            linear-gradient(to right, hsl(var(--border) / ${opacity}) 1px, transparent 1px),
            linear-gradient(to bottom, hsl(var(--border) / ${opacity}) 1px, transparent 1px),
            linear-gradient(45deg, hsl(var(--border) / ${opacity * 0.5}) 1px, transparent 1px),
            linear-gradient(-45deg, hsl(var(--border) / ${opacity * 0.5}) 1px, transparent 1px)
          `,
          backgroundSize: `${gridSize}px ${gridSize}px, ${gridSize}px ${gridSize}px, ${gridSize * 2}px ${gridSize * 2}px, ${gridSize * 2}px ${gridSize * 2}px`
        };
      case 'diagonal':
        return {
          backgroundImage: `
            linear-gradient(45deg, hsl(var(--border) / ${opacity}) 1px, transparent 1px),
            linear-gradient(-45deg, hsl(var(--border) / ${opacity}) 1px, transparent 1px)
          `,
          backgroundSize: `${gridSize}px ${gridSize}px`
        };
      default:
        return {
          backgroundImage: `
            linear-gradient(to right, hsl(var(--border) / ${opacity}) 1px, transparent 1px),
            linear-gradient(to bottom, hsl(var(--border) / ${opacity}) 1px, transparent 1px)
          `,
          backgroundSize: `${gridSize}px ${gridSize}px`
        };
    }
  };

  return (
    <div 
      className={cn(
        "relative",
        animated && "animate-pulse",
        className
      )}
      style={getGridPattern()}
    >
      {children}
    </div>
  );
};

// 专门用于移动预览的网格背景组件
export const MobilePreviewGrid: React.FC<GridBackgroundProps> = ({
  children,
  className,
  gridSize = 16,
  opacity = 0.15,
  variant = 'dots'
}) => {
  // 检测当前主题模式
  const [isDarkMode, setIsDarkMode] = React.useState(false);
  
  React.useEffect(() => {
    // 检测初始主题
    const checkTheme = () => {
      const isDark = document.documentElement.classList.contains('dark') ||
                    window.matchMedia('(prefers-color-scheme: dark)').matches;
      setIsDarkMode(isDark);
    };
    
    checkTheme();
    
    // 监听主题变化
    const observer = new MutationObserver(checkTheme);
    observer.observe(document.documentElement, { 
      attributes: true, 
      attributeFilter: ['class'] 
    });
    
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    mediaQuery.addEventListener('change', checkTheme);
    
    return () => {
      observer.disconnect();
      mediaQuery.removeEventListener('change', checkTheme);
    };
  }, []);

  const getDotPattern = () => {
    // 亮色模式：slate-700 with higher opacity
    const lightModeColor = `rgba(51, 65, 85, ${Math.min(opacity * 3, 0.6)})`;
    const lightModeLighterColor = `rgba(51, 65, 85, ${Math.min(opacity * 1.5, 0.3)})`;
    
    // 暗色模式：slate-300 with enhanced opacity for better visibility
    const darkModeColor = `rgba(203, 213, 225, ${Math.min(opacity * 2.5, 0.5)})`;  // slate-300, 更亮
    const darkModeLighterColor = `rgba(203, 213, 225, ${Math.min(opacity * 1.2, 0.25)})`;
    
    // 根据当前主题选择颜色
    const lightColor = isDarkMode ? darkModeColor : lightModeColor;
    const lighterColor = isDarkMode ? darkModeLighterColor : lightModeLighterColor;
    
    switch (variant) {
      case 'dots':
        // 双层点阵，营造深度感
        return {
          backgroundImage: `
            radial-gradient(circle, ${lightColor} 0.8px, transparent 0.8px),
            radial-gradient(circle, ${lighterColor} 0.4px, transparent 0.4px)
          `,
          backgroundSize: `${gridSize}px ${gridSize}px, ${gridSize / 2}px ${gridSize / 2}px`,
          backgroundPosition: `0 0, ${gridSize / 2}px ${gridSize / 2}px`
        };
      case 'fine-dots':
        // 精细单层点阵
        return {
          backgroundImage: `
            radial-gradient(circle, ${lightColor} 0.4px, transparent 0.4px)
          `,
          backgroundSize: `${gridSize / 2}px ${gridSize / 2}px`
        };
      case 'grid':
        // 传统网格线
        return {
          backgroundImage: `
            linear-gradient(to right, ${lightColor} 0.5px, transparent 0.5px),
            linear-gradient(to bottom, ${lightColor} 0.5px, transparent 0.5px)
          `,
          backgroundSize: `${gridSize}px ${gridSize}px`
        };
      default:
        // 默认使用适合移动预览的点阵
        return {
          backgroundImage: `
            radial-gradient(circle, ${lightColor} 0.8px, transparent 0.8px),
            radial-gradient(circle, ${lighterColor} 0.4px, transparent 0.4px)
          `,
          backgroundSize: `${gridSize}px ${gridSize}px, ${gridSize / 2}px ${gridSize / 2}px`,
          backgroundPosition: `0 0, ${gridSize / 2}px ${gridSize / 2}px`
        };
    }
  };

  return (
    <div 
      className={cn(
        "h-full w-full flex items-center justify-center relative",
        // 亮色模式：更明显的渐变背景
        "bg-gradient-to-br from-gray-100/40 via-gray-50/20 to-gray-100/40",
        // 暗色模式：增强的渐变背景
        "dark:from-gray-800/50 dark:via-gray-900/30 dark:to-gray-800/50",
        className
      )}
      style={{
        ...getDotPattern(),
        // 根据主题调整阴影效果
        boxShadow: isDarkMode 
          ? 'inset 0 0 30px rgba(255, 255, 255, 0.03)' // 暗色模式用白色阴影
          : 'inset 0 0 30px rgba(0, 0, 0, 0.05)'       // 亮色模式用黑色阴影
      }}
    >
      {children}
    </div>
  );
};

// 用于设计器的网格背景组件
export const DesignerGrid: React.FC<GridBackgroundProps> = ({
  children,
  className,
  gridSize = 24,
  opacity = 0.08,
  variant = 'default'
}) => {
  return (
    <GridBackground
      className={cn("bg-background", className)}
      gridSize={gridSize}
      opacity={opacity}
      variant={variant}
    >
      {children}
    </GridBackground>
  );
};

// 用于画布的网格背景组件
export const CanvasGrid: React.FC<GridBackgroundProps> = ({
  children,
  className,
  gridSize = 32,
  opacity = 0.06,
  variant = 'default'
}) => {
  return (
    <div 
      className={cn(
        "relative bg-background",
        className
      )}
      style={{
        backgroundImage: `
          linear-gradient(to right, hsl(var(--muted-foreground) / ${opacity}) 1px, transparent 1px),
          linear-gradient(to bottom, hsl(var(--muted-foreground) / ${opacity}) 1px, transparent 1px),
          linear-gradient(to right, hsl(var(--muted-foreground) / ${opacity * 2}) 1px, transparent 1px),
          linear-gradient(to bottom, hsl(var(--muted-foreground) / ${opacity * 2}) 1px, transparent 1px)
        `,
        backgroundSize: `${gridSize}px ${gridSize}px, ${gridSize}px ${gridSize}px, ${gridSize * 4}px ${gridSize * 4}px, ${gridSize * 4}px ${gridSize * 4}px`
      }}
    >
      {children}
    </div>
  );
};

// 用于预览区域的网格背景组件
export const PreviewGrid: React.FC<GridBackgroundProps> = ({
  children,
  className,
  gridSize = 16,
  opacity = 0.12
}) => {
  return (
    <div 
      className={cn(
        "relative",
        // 使用主题变量实现自适应背景
        "bg-gradient-to-br from-accent/10 to-accent/5",
        className
      )}
      style={{
        backgroundImage: `
          linear-gradient(to right, hsl(var(--border) / ${opacity}) 1px, transparent 1px),
          linear-gradient(to bottom, hsl(var(--border) / ${opacity}) 1px, transparent 1px)
        `,
        backgroundSize: `${gridSize}px ${gridSize}px`
      }}
    >
      {children}
    </div>
  );
}; 