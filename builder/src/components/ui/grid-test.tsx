import React from 'react';
import { MobilePreviewGrid } from './grid-background';

export const GridTest: React.FC = () => {
  return (
    <div className="w-full h-screen p-4">
      <h1 className="text-2xl font-bold mb-4">Grid Background Test</h1>
      
      {/* 测试 MobilePreviewGrid - 点阵模式 */}
      <div className="mb-8">
        <h2 className="text-lg font-semibold mb-2">MobilePreviewGrid - Enhanced for Light Mode</h2>
        <div className="w-full h-64 border border-red-500">
          <MobilePreviewGrid variant="dots" gridSize={16} opacity={0.15}>
            <div className="bg-white/90 p-4 rounded shadow-lg">
              <p className="text-gray-800">亮色模式优化 - 点阵更明显</p>
              <p className="text-sm text-gray-600 mt-2">使用 slate-700 颜色，透明度提升至 3x</p>
            </div>
          </MobilePreviewGrid>
        </div>
      </div>

      {/* 对比测试 - 原始透明度 */}
      <div className="mb-8">
        <h2 className="text-lg font-semibold mb-2">对比：原始透明度</h2>
        <div className="w-full h-64 border border-yellow-500">
          <MobilePreviewGrid variant="dots" gridSize={16} opacity={0.05}>
            <div className="bg-white/90 p-4 rounded shadow-lg">
              <p className="text-gray-800">低透明度 (0.05) - 几乎看不见</p>
            </div>
          </MobilePreviewGrid>
        </div>
      </div>

      {/* 测试暗色模式增强 */}
      <div className="mb-8">
        <h2 className="text-lg font-semibold mb-2">暗色模式增强测试</h2>
        <div className="w-full h-64 border border-orange-500 dark:border-orange-400">
          <MobilePreviewGrid variant="dots" gridSize={16} opacity={0.15}>
            <div className="bg-white/90 dark:bg-gray-800/90 p-4 rounded shadow-lg">
              <p className="text-gray-800 dark:text-gray-200">主题自适应点阵</p>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                亮色模式：slate-700 | 暗色模式：slate-300
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                透明度自动调整，背景渐变增强
              </p>
            </div>
          </MobilePreviewGrid>
        </div>
      </div>

      {/* 测试精细点阵 */}
      <div className="mb-8">
        <h2 className="text-lg font-semibold mb-2">Fine Dots Pattern</h2>
        <div className="w-full h-64 border border-purple-500">
          <MobilePreviewGrid variant="fine-dots" gridSize={12} opacity={0.12}>
            <div className="bg-white/80 dark:bg-gray-800/80 p-4 rounded shadow">
              <p className="dark:text-gray-200">精细点阵 - 更细腻的效果</p>
            </div>
          </MobilePreviewGrid>
        </div>
      </div>

      {/* 测试传统网格 */}
      <div className="mb-8">
        <h2 className="text-lg font-semibold mb-2">Traditional Grid</h2>
        <div className="w-full h-64 border border-green-500">
          <MobilePreviewGrid variant="grid" gridSize={20} opacity={0.1}>
            <div className="bg-white/80 p-4 rounded shadow">
              <p>传统网格线</p>
            </div>
          </MobilePreviewGrid>
        </div>
      </div>

      {/* 测试直接 CSS 网格 */}
      <div className="mb-8">
        <h2 className="text-lg font-semibold mb-2">Direct CSS Grid Test</h2>
        <div 
          className="w-full h-64 border border-blue-500 flex items-center justify-center"
          style={{
            backgroundImage: `
              linear-gradient(to right, #ccc 1px, transparent 1px),
              linear-gradient(to bottom, #ccc 1px, transparent 1px)
            `,
            backgroundSize: '20px 20px'
          }}
        >
          <div className="bg-white/80 p-4 rounded shadow">
            <p>This should definitely have a grid background</p>
          </div>
        </div>
      </div>

      {/* 测试 CSS 变量 */}
      <div className="mb-8">
        <h2 className="text-lg font-semibold mb-2">CSS Variables Test</h2>
        <div 
          className="w-full h-64 border border-green-500 flex items-center justify-center"
          style={{
            backgroundImage: `
              linear-gradient(to right, hsl(var(--border)) 1px, transparent 1px),
              linear-gradient(to bottom, hsl(var(--border)) 1px, transparent 1px)
            `,
            backgroundSize: '20px 20px'
          }}
        >
          <div className="bg-white/80 p-4 rounded shadow">
            <p>This tests CSS variables</p>
          </div>
        </div>
      </div>

      {/* 测试高对比度网格 */}
      <div className="mb-8">
        <h2 className="text-lg font-semibold mb-2">High Contrast Grid Test</h2>
        <div 
          className="w-full h-64 border border-purple-500 flex items-center justify-center bg-gray-50"
          style={{
            backgroundImage: `
              linear-gradient(to right, rgba(0,0,0,0.3) 1px, transparent 1px),
              linear-gradient(to bottom, rgba(0,0,0,0.3) 1px, transparent 1px)
            `,
            backgroundSize: '20px 20px'
          }}
        >
          <div className="bg-white/80 p-4 rounded shadow">
            <p>This should be very visible</p>
          </div>
        </div>
      </div>
    </div>
  );
}; 