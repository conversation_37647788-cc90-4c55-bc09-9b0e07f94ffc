'use client'

import React, { useEffect, useCallback } from 'react';
import { usePageBuilder } from '@/context/PageBuilderContext';
import { Skeleton } from "@/components/ui/skeleton"
import PageRender from '@/components/pageRender';
import clsx from 'clsx';
import PageRenderProviders from '@/components/pageRender/Providers';
import { EmptyState } from '@/components/ui/empty-state';
import { LayoutTemplate, Plus, Wand2, HelpCircle } from 'lucide-react';
import { useBlockEditor } from '@/context/BlockEditorContext';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { useParams } from 'next/navigation';
import { useFetchPage } from '@/lib/hooks/use-page-query';
import { useFetchWebsite } from '@/lib/hooks/use-website';
import { usePreviewStore } from '@/lib/store/preview-store';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ontent,
  Too<PERSON><PERSON><PERSON><PERSON>ider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

const CREATE_METHODS = [
  {
    id: 'blank',
    title: 'Add Block',
    icon: Plus,
    description: 'Start with a blank page and build your page by adding blocks.',
    disabled: false
  },
  {
    id: 'template',
    title: 'Use Template',
    icon: LayoutTemplate,
    description: 'Choose from pre-designed templates to quickly create professional pages.',
    disabled: true
  },
  {
    id: 'ai',
    title: 'AI Creation',
    icon: Wand2,
    description: 'Let AI automatically generate page content based on your requirements.',
    disabled: true
  }
];

const PageCanva = () => {
  const { pageId } = useParams<{ pageId: string }>() || { pageId: '' };
  const { siteId } = useParams<{ siteId: string }>() || { siteId: '' };
  const { data: currentPage, isLoading: isPageLoading } = useFetchPage(pageId || '');
  const { data: website } = useFetchWebsite(siteId);
  const domain = website?.domain;
  const logo = website?.logo;
  const { setCreateSectionOpened, setCurBlock } = useBlockEditor();
  const setActiveTab = usePreviewStore((state) => state.setActiveTab);

  // 处理添加区块的函数，与 BlockList 中的 handleCreateSectionClick 保持一致
  const handleCreateSectionClick = useCallback((id: string) => {
    setCurBlock(id);
    setCreateSectionOpened(true);
    setActiveTab('creator'); // 切换到创建器标签页
  }, [setCurBlock, setCreateSectionOpened, setActiveTab]);

  // 加载中状态
  if (isPageLoading) {
    return (
      <div className="flex items-center justify-center h-full bg-gray-50/50">
        <Skeleton className="w-full h-full" />
      </div>
    );
  }

  // 加载完成后，如果没有版本ID，显示空状态
  if (currentPage?.status === 'INITIALIZED' || currentPage?.configuration?.sections?.length === 0) {
    return (
      <div className="flex items-center justify-center h-full bg-gray-50/50">
        <div className="w-full max-w-4xl p-6">
          <EmptyState
            icon={<LayoutTemplate className="w-6 h-6 text-gray-400" />}
            title="Start Creating Your Page"
            description="Choose any of the following methods to start creating your page. You can add blocks from scratch, more features coming soon."
            action={
              <div className="grid grid-cols-1 gap-4 mt-6 md:grid-cols-3">
                {CREATE_METHODS.map((method) => {
                  const Icon = method.icon;
                  return (
                    <Card
                      key={method.id}
                      className={cn(
                        'group relative cursor-pointer border-2 p-4 transition-colors',
                        method.disabled
                          ? 'cursor-not-allowed opacity-60'
                          : 'hover:bg-accent/50',
                        !method.disabled && 'border-muted bg-background'
                      )}
                      onClick={() => {
                        if (!method.disabled) {
                          if (method.id === 'blank') {
                            // 使用与 BlockList 中相同的创建区块逻辑
                            handleCreateSectionClick('end');
                          }
                        }
                      }}
                    >
                      <div className="flex h-[88px] flex-col items-center justify-center gap-2">
                        <Icon className="h-8 w-8 text-primary" />
                        <div className="flex flex-col items-center">
                          <span className="font-medium">{method.title}</span>
                          {method.disabled && (
                            <Badge variant="secondary" className="mt-1">
                              Coming Soon
                            </Badge>
                          )}
                        </div>
                      </div>
                      <TooltipProvider delayDuration={300}>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <button
                              type="button"
                              className="absolute right-2 top-2 opacity-0 transition-opacity group-hover:opacity-100"
                              onClick={(e) => e.stopPropagation()}
                            >
                              <HelpCircle className="h-4 w-4 text-muted-foreground" />
                            </button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="max-w-xs text-sm">{method.description}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </Card>
                  );
                })}
              </div>
            }
          />
        </div>
      </div>
    );
  }

  // 添加语言切换处理函数
  const handleLanguageChange = (langCode: string) => {
    console.log('[ NewHeader ] Language change requested:', langCode);
    
    // 如果有语言版本信息，尝试切换到对应的语言版本
    if (currentPage?.languageVersions && currentPage.languageVersions[langCode.toUpperCase()]) {
      const targetVersion = currentPage.languageVersions[langCode.toUpperCase()];
      console.log('[ NewHeader ] Switching to language version:', targetVersion);
      
      // 这里可以实现跳转到对应语言版本的页面编辑
      // 例如：router.push(`/page/${targetVersion.id}/build`);
      
      // 或者通过 API 获取对应语言版本的内容
      // 这部分逻辑可以根据实际需求实现
    } else {
      console.log('[ NewHeader ] No language version found for:', langCode);
    }
  };

  const headerProps = {
    variant: currentPage?.header?.variant || 'default',
    id: currentPage?.header?.id || 'default-header',
    logo: currentPage?.header?.configuration?.logo || logo || 'LitPage',
    links: currentPage?.header?.configuration?.links || [],
    actions: currentPage?.header?.configuration?.actions || [],
    // 使用服务端返回的语言数据，如果没有则使用默认值
    languages: [
      { code: 'en', name: 'English', flag: '' },
      { code: 'zh', name: '中文', flag: '' }
    ],
    currentLanguage: currentPage?.language?.toLowerCase() || 'en',
    theme: currentPage?.header?.configuration?.theme || { enabled: false, defaultTheme: 'system' },
    // 添加语言切换回调
    onLanguageChange: handleLanguageChange,
    // 添加多语言相关属性
    languageInfo: currentPage?.languageInfo,
    languageVersions: currentPage?.languageVersions,
    languageUrls: currentPage?.languageUrls,
    seo: currentPage?.seo
  };

  const footerProps = {
    variant: currentPage?.footer?.variant || 'default',
    id: currentPage?.footer?.id || 'default-footer',
    logo: currentPage?.footer?.configuration?.logo || { url: '', alt: 'Footer Logo' },
    links: currentPage?.footer?.configuration?.links || [],
    copyright: currentPage?.footer?.configuration?.copyright || `© ${new Date().getFullYear()} ${logo || 'LitPage'}. All rights reserved.`,
    socialMedia: currentPage?.footer?.configuration?.socialMedia || []
  };

  console.log('[ NewHeader ] Original header data:', currentPage?.header);
  console.log('[ NewHeader ] Processed header props:', headerProps);
  console.log('[ NewHeader ] Original footer data:', currentPage?.footer);
  console.log('[ NewHeader ] Processed footer props:', footerProps);

  // 显示页面内容
  return (
    <div 
      className={clsx('relative border border-gray-100 overflow-scroll w-full')}
      style={{ height: 'calc(100vh - 128px)' }}
      data-theme={currentPage?.theme}
    >
      <div className={clsx('relative flex justify-center')} style={{ minHeight: '100%' }}>
        <div className={clsx('md:col-span-2 p-6 min-h-fit w-full')}>
          <PageRenderProviders editable={true} domain={domain}>
            <PageRender
              editable={true}
              domain={domain}
              headers={headerProps}
              schema={currentPage?.configuration}
              footers={footerProps}
            />
          </PageRenderProviders>
        </div>
      </div>
    </div>
  );
};

export default PageCanva;
