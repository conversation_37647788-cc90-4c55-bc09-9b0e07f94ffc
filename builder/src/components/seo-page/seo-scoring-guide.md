# SEO Scoring System Documentation

This document explains the methodology behind our SEO scoring system, detailing how each metric is calculated and weighted to provide a comprehensive assessment of page SEO performance.

## Overall Score Calculation

The overall SEO score is a weighted average of five main categories:

| Category | Weight | Description |
|----------|--------|-------------|
| Content Quality | 30% | Quality and relevance of page content |
| Technical Optimization | 25% | Technical implementation quality |
| On-Page Optimization | 20% | Internal SEO elements |
| Off-Page Optimization | 15% | Social media and external factors |
| User Experience | 10% | Page usability and performance |

This weighting reflects current search engine algorithms, which prioritize content quality and technical implementation.

## Main Category Scores

### 1. Content Quality Score

Content quality is evaluated based on:

- **Basic SEO Elements (40%)**: Title, description, and keywords quality
- **Content Structure (60%)**: Heading hierarchy and semantic structure

**Formula**: `contentScore = (basicScore * 0.4) + (structureScore * 0.6)`

### 2. Technical Optimization Score

Technical optimization focuses on:

- **Structured Data (50%)**: JSON-LD completeness and correctness
- **Advanced Settings (50%)**: Canonical URLs, robots directives, etc.

**Formula**: `technicalScore = (structuredScore * 0.5) + (advancedScore * 0.5)`

### 3. On-Page Optimization Score

On-page optimization combines:

- **Basic SEO Elements (30%)**: Title tags, meta descriptions, etc.
- **Content Structure (30%)**: H1-H6 tag usage
- **Technical Factors (40%)**: Internal technical implementation

**Formula**: `onPageScore = (basicScore * 0.3) + (structureScore * 0.3) + (technicalScore * 0.4)`

### 4. Off-Page Optimization Score

Off-page optimization primarily focuses on:

- **Social Media Settings (100%)**: Open Graph and X (Twitter) card completeness

**Formula**: `offPageScore = socialScore`

### 5. User Experience Score

User experience is based on:

- **Page Speed**: Loading time performance
- **Mobile-Friendliness**: Responsive design quality
- **Interactivity**: Smooth page interactions

## Page Settings Scores

### 1. Basic SEO Score

Basic SEO score is calculated from:

| Factor | Maximum Points | Criteria |
|--------|----------------|----------|
| Title Quality | 30 | Optimal length (40-60 chars): 30 points<br>Good length (30-70 chars): 20 points<br>Other lengths: 10 points |
| Description Quality | 30 | Optimal length (120-160 chars): 30 points<br>Good length (80-200 chars): 20 points<br>Other lengths: 10 points |
| Keywords Quality | 20 | Optimal count (3-8 keywords): 20 points<br>Any keywords: 10 points |
| Content Readability | 20 | Based on readability score: up to 20 points |

### 2. Structure Score

Structure score starts with a base of 70 points, adjusted by:

| Factor | Points Adjustment |
|--------|-------------------|
| H1 Heading | One H1: +15 points<br>Multiple H1s: -10 points<br>No H1: -20 points |
| H2 Headings | 2-6 H2 headings: +10 points |
| H3 Headings | 3+ H3 headings: +5 points |

### 3. Social Media Score

Social media score is divided between:

| Platform | Maximum Points | Breakdown |
|----------|----------------|-----------|
| Open Graph | 50 | Title: 15 points<br>Description: 15 points<br>Image: 20 points |
| X (Twitter) | 50 | Title: 15 points<br>Description: 15 points<br>Image: 20 points |

### 4. Structured Data Score

Structured data scoring includes:

| Factor | Points |
|--------|--------|
| Base Score | 50 points for having structured data |
| Required Fields | Up to 30 points for name, description, etc. |
| Additional Fields | 5 points per extra field (max 20 points) |

### 5. Advanced Settings Score

Advanced settings start with a base of 50 points:

| Factor | Points |
|--------|--------|
| Canonical URL | Present: +25 points |
| Robots Directives | Present: +25 points |

## Issue Tracking

The system also tracks SEO issues across three severity levels:

| Issue Type | Description | Examples |
|------------|-------------|----------|
| Critical Issues | Severely impact SEO | Missing H1, no meta description |
| Warnings | May impact SEO | Title too long, images missing alt attributes |
| Passed Items | Meet SEO best practices | Proper heading structure, optimized images |

## Score Interpretation

| Score Range | Status | Recommendation |
|-------------|--------|----------------|
| 80-100 | Good | Minor improvements may still be beneficial |
| 60-79 | Needs Improvement | Address warnings and some critical issues |
| 0-59 | Poor | Immediate attention required for critical issues |

---

This multi-dimensional scoring system provides a comprehensive assessment of page SEO performance based on search engine best practices and industry standards. By addressing the issues identified by the system, users can optimize their pages for better search rankings.
