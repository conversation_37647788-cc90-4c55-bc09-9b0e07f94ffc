"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardFooter } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { 
  AlertCircle, 
  FileText, 
  <PERSON><PERSON><PERSON>,
  Layers, 
  Share2, 
  Code, 
  Settings,
  Save
} from "lucide-react";

// Import components
import BasicSEOForm from "./components/basic-seo-form";
import SocialMediaSEOForm from "./components/social-media-form";
import StructuredDataForm from "./components/structured-data-form";
import AdvancedSEOForm from "./components/advanced-seo-form";
import { HeadingStructureForm } from "./components/heading-structure-form";
import PageSEOOverview from "./components/page-seo-overview";

// Import mock data
import { mockSEOData } from "./data/mock-data";

interface SEOPageProps {
  pageId: string;
  siteId: string;
  defaultTab?: string;
  onTabChange?: (tab: string) => void;
}

export default function SEOPage({ pageId, siteId, defaultTab = "overview", onTabChange }: SEOPageProps) {
  const [activeTab, setActiveTab] = useState(defaultTab);
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  
  // SEO 评分计算
  const [seoScores, setSeoScores] = useState({
    overall: 0,
    categories: {
      content: 0,
      technical: 0,
      onPage: 0,
      offPage: 0,
      user: 0
    },
    sections: {
      basic: 0,
      structure: 0,
      social: 0,
      structured: 0,
      advanced: 0
    },
    issues: {
      critical: 2,
      warnings: 5,
      passed: 18
    }
  });

  // 模拟 SEO 评分计算
  useEffect(() => {
    // 从 mockSEOData 中提取数据
    const { contentAnalysis, title, description, keywords, ogTitle, ogDescription, ogImage, 
            schemaType, schemaData, canonicalUrl, robots } = mockSEOData;
    
    // 获取 X (Twitter) 相关字段
    const xTitle = mockSEOData.xTitle;
    const xDescription = mockSEOData.xDescription;
    const xImage = mockSEOData.xImage;
    
    // 计算基础 SEO 评分
    const basicScore = calculateBasicScore(title, description, keywords);
    
    // 计算结构评分
    const structureScore = calculateStructureScore(contentAnalysis?.headings);
    
    // 计算社交媒体评分
    const socialScore = calculateSocialScore(ogTitle, ogDescription, ogImage, 
                                           xTitle, xDescription, xImage);
    
    // 计算结构化数据评分
    const structuredScore = calculateStructuredDataScore(schemaType, schemaData);
    
    // 计算高级设置评分
    const advancedScore = calculateAdvancedScore(canonicalUrl, robots);
    
    // 计算主要类别评分
    const contentScore = Math.round((basicScore * 0.4) + (structureScore * 0.6));
    const technicalScore = Math.round((structuredScore * 0.5) + (advancedScore * 0.5));
    const onPageScore = Math.round((basicScore * 0.3) + (structureScore * 0.3) + (technicalScore * 0.4));
    const offPageScore = Math.round(socialScore);
    const userScore = 78; // 用户体验评分，实际应基于页面速度、移动友好性等
    
    // 计算总体评分
    const overallScore = Math.round(
      (contentScore * 0.3) + 
      (technicalScore * 0.25) + 
      (onPageScore * 0.2) + 
      (offPageScore * 0.15) + 
      (userScore * 0.1)
    );
    
    // 更新状态
    setSeoScores({
      overall: overallScore,
      categories: {
        content: contentScore,
        technical: technicalScore,
        onPage: onPageScore,
        offPage: offPageScore,
        user: userScore
      },
      sections: {
        basic: basicScore,
        structure: structureScore,
        social: socialScore,
        structured: structuredScore,
        advanced: advancedScore
      },
      issues: {
        critical: 2,
        warnings: 5,
        passed: 18
      }
    });
  }, []);
  
  // 辅助函数：计算基础 SEO 评分
  const calculateBasicScore = (title: string | undefined, description: string | undefined, keywords: string[] | undefined) => {
    let score = 0;
    
    // 标题评分 (最高 30 分)
    if (title) {
      const titleLength = title.length;
      if (titleLength >= 40 && titleLength <= 60) score += 30;
      else if (titleLength > 30 && titleLength < 70) score += 20;
      else score += 10;
    }
    
    // 描述评分 (最高 30 分)
    if (description) {
      const descLength = description.length;
      if (descLength >= 120 && descLength <= 160) score += 30;
      else if (descLength > 80 && descLength < 200) score += 20;
      else score += 10;
    }
    
    // 关键词评分 (最高 20 分)
    if (keywords && keywords.length) {
      if (keywords.length >= 3 && keywords.length <= 8) score += 20;
      else if (keywords.length > 0) score += 10;
    }
    
    // 内容质量评分 (最高 20 分)
    const readabilityScore = mockSEOData.contentAnalysis?.readabilityScore || 0;
    score += Math.round(readabilityScore * 0.2);
    
    return score;
  };
  
  // 辅助函数：计算结构评分
  const calculateStructureScore = (headings: any) => {
    let score = 70; // 默认分数
    
    if (!headings) return score;
    
    // 检查 H1 标题
    if (headings.h1 && headings.h1.length === 1) score += 15;
    else if (headings.h1 && headings.h1.length > 1) score -= 10;
    else score -= 20;
    
    // 检查 H2 标题
    if (headings.h2 && headings.h2.length >= 2 && headings.h2.length <= 6) score += 10;
    
    // 检查 H3 标题
    if (headings.h3 && headings.h3.length >= 3) score += 5;
    
    return Math.min(100, score);
  };
  
  // 辅助函数：计算社交媒体评分
  const calculateSocialScore = (ogTitle: string | undefined, ogDesc: string | undefined, ogImg: string | undefined, 
                              xTitle: string | undefined, xDesc: string | undefined, xImg: string | undefined) => {
    let score = 0;
    
    // Open Graph 评分 (最高 50 分)
    if (ogTitle) score += 15;
    if (ogDesc) score += 15;
    if (ogImg) score += 20;
    
    // X (Twitter) 评分 (最高 50 分)
    if (xTitle) score += 15;
    if (xDesc) score += 15;
    if (xImg) score += 20;
    
    return score;
  };
  
  // 辅助函数：计算结构化数据评分
  const calculateStructuredDataScore = (schemaType: string | undefined, schemaData: any) => {
    let score = 0;
    
    if (schemaType) {
      score += 40; // 基础分数
      
      // 根据数据完整性加分
      if (schemaData) {
        const dataKeys = Object.keys(schemaData);
        if (dataKeys.length >= 3) score += 30;
        else if (dataKeys.length >= 1) score += 20;
        else score += 10;
      }
    }
    
    return Math.min(100, score);
  };
  
  // 辅助函数：计算高级设置评分
  const calculateAdvancedScore = (canonicalUrl: string | undefined, robots: any) => {
    let score = 60; // 基础分数
    
    if (canonicalUrl) score += 20;
    if (robots && robots.index !== undefined) score += 20;
    
    return Math.min(100, score);
  };

  const handleSave = (tab: string, data?: any) => {
    setIsSaving(true);
    
    // 模拟保存操作
    setTimeout(() => {
      setIsSaving(false);
      setLastSaved(new Date());
      
      // 根据不同的 tab 处理不同的保存逻辑
      switch (tab) {
        case "Basic": {
          const handleInvalid = () => {
            console.log("Basic SEO form validation failed");
          };
          
          // 触发基础 SEO 表单提交
          const basicForm = document.getElementById("basic-seo-form");
          if (basicForm) {
            const submitEvent = new Event("submit", { bubbles: true, cancelable: true });
            basicForm.dispatchEvent(submitEvent);
          }
          break;
        }
          
        case "Structure": {
          const handleInvalid = () => {
            console.log("Structure form validation failed");
          };
          
          // 触发结构表单提交
          const structureForm = document.getElementById("structure-form");
          if (structureForm) {
            const submitEvent = new Event("submit", { bubbles: true, cancelable: true });
            structureForm.dispatchEvent(submitEvent);
          }
          break;
        }
          
        case "Social Media": {
          const handleInvalid = () => {
            console.log("Social media form validation failed");
          };
          
          // 触发社交媒体表单提交
          const socialForm = document.getElementById("social-media-form");
          if (socialForm) {
            const submitEvent = new Event("submit", { bubbles: true, cancelable: true });
            socialForm.dispatchEvent(submitEvent);
          }
          break;
        }
          
        case "Structured Data": {
          const handleInvalid = () => {
            console.log("Structured data form validation failed");
          };
          
          // 触发结构化数据表单提交
          const structuredForm = document.getElementById("structured-data-form");
          if (structuredForm) {
            const submitEvent = new Event("submit", { bubbles: true, cancelable: true });
            structuredForm.dispatchEvent(submitEvent);
          }
          break;
        }
          
        case "Advanced SEO": {
          const handleInvalid = () => {
            console.log("Advanced SEO form validation failed");
          };
          
          // 触发高级 SEO 表单提交
          const advancedForm = document.getElementById("advanced-seo-form");
          if (advancedForm) {
            const submitEvent = new Event("submit", { bubbles: true, cancelable: true });
            advancedForm.dispatchEvent(submitEvent);
          }
          break;
        }
      }
    }, 1000);
  };

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    if (onTabChange) {
      onTabChange(value);
    }
  };

  const formatLastSaved = () => {
    if (!lastSaved) return "";
    
    return new Intl.DateTimeFormat("en-US", {
      hour: "numeric",
      minute: "numeric",
      second: "numeric",
      hour12: true,
    }).format(lastSaved);
  };

  return (
    <div className="container py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Page SEO Settings</h1>
          <p className="text-muted-foreground mt-1">
            Optimize this page for search engines and social media sharing
          </p>
        </div>
        <div className="flex items-center gap-2">
          {lastSaved && (
            <span className="text-sm text-muted-foreground">
              Last saved: {formatLastSaved()}
            </span>
          )}
        </div>
      </div>

      <Separator />

      <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-4">
        <div className="overflow-x-auto">
          <TabsList className="h-auto p-1 w-full grid grid-cols-6 gap-1">
            <TabsTrigger value="overview" className="flex items-center gap-1 py-2">
              <PieChart className="h-4 w-4" />
              <span>Overview</span>
            </TabsTrigger>
            <TabsTrigger value="basic" className="flex items-center gap-1 py-2">
              <FileText className="h-4 w-4" />
              <span>Basic</span>
            </TabsTrigger>
            <TabsTrigger value="structure" className="flex items-center gap-1 py-2">
              <Layers className="h-4 w-4" />
              <span>Content Structure</span>
            </TabsTrigger>
            <TabsTrigger value="social" className="flex items-center gap-1 py-2">
              <Share2 className="h-4 w-4" />
              <span>Social</span>
            </TabsTrigger>
            <TabsTrigger value="structured" className="flex items-center gap-1 py-2">
              <Code className="h-4 w-4" />
              <span>Structured Data</span>
            </TabsTrigger>
            <TabsTrigger value="advanced" className="flex items-center gap-1 py-2">
              <Settings className="h-4 w-4" />
              <span>Advanced</span>
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="overview">
          <PageSEOOverview 
            seoData={mockSEOData} 
            seoScores={seoScores}
            pageId={pageId}
            siteId={siteId}
            onTabChange={handleTabChange}
          />
        </TabsContent>
        
        <TabsContent value="basic">
          <Card className="shadow-sm">
            <CardHeader className="border-b">
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="text-xl">Basic SEO</CardTitle>
                  <CardDescription>
                    Configure essential SEO settings for this page
                  </CardDescription>
                </div>
                <div className="flex items-center justify-center h-8 w-8 rounded-full bg-primary/10 text-primary">
                  <FileText className="h-4 w-4" />
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-6">
              <BasicSEOForm 
                pageId={pageId} 
                onSaveSuccess={() => {
                  setLastSaved(new Date());
                  setIsSaving(false);
                }}
              />
            </CardContent>
            <CardFooter className="border-t bg-muted/30 py-4">
              <div className="flex justify-end w-full">
                <Button 
                  onClick={() => handleSave("Basic")}
                  disabled={isSaving}
                >
                  {isSaving ? "Saving..." : "Save Basic Settings"}
                </Button>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>
        
        <TabsContent value="structure">
          <Card className="shadow-sm">
            <CardHeader className="border-b">
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="text-xl">Content Structure & Hierarchy</CardTitle>
                  <CardDescription>
                    Optimize your page&apos;s HTML structure and heading hierarchy for better SEO and accessibility
                  </CardDescription>
                </div>
                <div className="flex items-center justify-center h-8 w-8 rounded-full bg-primary/10 text-primary">
                  <Layers className="h-4 w-4" />
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-6">
              <HeadingStructureForm 
                headings={{
                  h1: mockSEOData.contentAnalysis?.headings?.h1 || [], 
                  h2: mockSEOData.contentAnalysis?.headings?.h2 || [], 
                  h3: mockSEOData.contentAnalysis?.headings?.h3 || [], 
                  h4: mockSEOData.contentAnalysis?.headings?.h4 || [], 
                  h5: mockSEOData.contentAnalysis?.headings?.h5 || [], 
                  h6: mockSEOData.contentAnalysis?.headings?.h6 || [] 
                }}
                onUpdateHeadings={(headings: any) => {
                  console.log("Updated headings:", headings);
                }} 
              />
            </CardContent>
            <CardFooter className="border-t bg-muted/30 py-4">
              <div className="flex justify-end w-full">
                <Button 
                  onClick={() => handleSave("Structure")}
                  disabled={isSaving}
                >
                  {isSaving ? "Saving..." : "Save Content Structure"}
                </Button>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>
        
        <TabsContent value="social">
          <Card className="shadow-sm">
            <CardHeader className="border-b">
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="text-xl">Social Media</CardTitle>
                  <CardDescription>
                    Configure how your page appears when shared on social media platforms
                  </CardDescription>
                </div>
                <div className="flex items-center justify-center h-8 w-8 rounded-full bg-primary/10 text-primary">
                  <Share2 className="h-4 w-4" />
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-6">
              <SocialMediaSEOForm 
                pageId={pageId} 
                onSaveSuccess={() => {
                  setLastSaved(new Date());
                  setIsSaving(false);
                }}
              />
            </CardContent>
            <CardFooter className="border-t bg-muted/30 py-4">
              <div className="flex justify-end w-full">
                <Button 
                  onClick={() => handleSave("Social Media")}
                  disabled={isSaving}
                >
                  {isSaving ? "Saving..." : "Save Social Media Settings"}
                </Button>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>
        
        <TabsContent value="structured">
          <Card className="shadow-sm">
            <CardHeader className="border-b">
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="text-xl">Structured Data</CardTitle>
                  <CardDescription>
                    Add schema markup to enhance your search results with rich snippets
                  </CardDescription>
                </div>
                <div className="flex items-center justify-center h-8 w-8 rounded-full bg-primary/10 text-primary">
                  <Code className="h-4 w-4" />
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-6">
              <StructuredDataForm 
                pageId={pageId} 
                onSaveSuccess={() => {
                  setLastSaved(new Date());
                  setIsSaving(false);
                }}
              />
            </CardContent>
            <CardFooter className="border-t bg-muted/30 py-4">
              <div className="flex justify-end w-full">
                <Button 
                  onClick={() => handleSave("Structured Data")}
                  disabled={isSaving}
                >
                  {isSaving ? "Saving..." : "Save Structured Data"}
                </Button>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>
        
        <TabsContent value="advanced">
          <Card className="shadow-sm">
            <CardHeader className="border-b">
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="text-xl">Advanced SEO</CardTitle>
                  <CardDescription>
                    Configure technical SEO settings like canonical URLs, indexing, and more
                  </CardDescription>
                </div>
                <div className="flex items-center justify-center h-8 w-8 rounded-full bg-primary/10 text-primary">
                  <Settings className="h-4 w-4" />
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-6">
              <AdvancedSEOForm 
                pageId={pageId} 
                onSaveSuccess={() => {
                  setLastSaved(new Date());
                  setIsSaving(false);
                }} 
              />
            </CardContent>
            <CardFooter className="border-t bg-muted/30 py-4">
              <div className="flex justify-end w-full">
                <Button 
                  onClick={() => handleSave("Advanced SEO")}
                  disabled={isSaving}
                >
                  {isSaving ? "Saving..." : "Save Advanced Settings"}
                </Button>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
