import { PageSEOData } from "../types";

export const mockSEOData: PageSEOData = {
  // 基础元数据
  title: "Professional Web Development Services",
  useCustomTitle: true,
  description: "Expert web development services using the latest technologies. Our team delivers responsive, high-performance websites tailored to your business needs.",
  keywords: ["web development", "responsive design", "professional websites", "business solutions"],
  
  // 社交媒体设置
  ogTitle: "Professional Web Development | Litpage",
  ogDescription: "Transform your online presence with our expert web development services. Get a responsive, high-performance website tailored to your business.",
  ogImage: "https://example.com/images/web-development-og.jpg",
  xTitle: "Professional Web Development | Litpage",
  xDescription: "Transform your online presence with our expert web development services.",
  xImage: "https://example.com/images/web-development-twitter.jpg",
  xCardType: "SUMMARY_LARGE_IMAGE",
  
  // 结构化数据
  schemaType: "ORGANIZATION",
  schemaData: {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": "Professional Web Development",
    "description": "Expert web development services using the latest technologies.",
    "provider": {
      "@type": "Organization",
      "name": "Litpage",
      "url": "https://example.com"
    },
    "areaServed": "Worldwide",
    "serviceType": "Web Development"
  },
  
  // 技术设置
  canonicalUrl: "https://example.com/services/web-development",
  robots: {
    index: true,
    follow: true,
    noarchive: false,
    nosnippet: false,
    noimageindex: false,
    nocache: false,
    maxSnippet: -1,
    maxImagePreview: "large",
    maxVideoPreview: -1
  },
  priority: 0.8,
  changeFrequency: "MONTHLY",
  
  // 继承设置
  inheritFromSite: true,
  
  // hreflang 链接
  hreflangLinks: [
    { language: "en", url: "/services/web-development" },
    { language: "zh-CN", url: "/zh-CN/services/web-development" },
    { language: "es", url: "/es/services/web-development" }
  ],
  
  // 页面内容分析
  contentAnalysis: {
    headings: {
      h1: ["Professional Web Development Services"],
      h2: [
        "Our Web Development Process",
        "Technologies We Use",
        "Our Portfolio",
        "Client Testimonials"
      ],
      h3: [
        "Discovery Phase",
        "Design Phase",
        "Development Phase",
        "Testing Phase",
        "Launch Phase",
        "Frontend Technologies",
        "Backend Technologies",
        "Database Solutions"
      ]
    },
    images: [
      {
        url: "/images/web-development-hero.jpg",
        alt: "Professional web development team working on a project",
        width: 1200,
        height: 600
      },
      {
        url: "/images/development-process.jpg",
        alt: "Our web development process workflow diagram",
        width: 800,
        height: 400
      },
      {
        url: "/images/technologies.jpg",
        alt: "",
        width: 600,
        height: 400
      },
      {
        url: "/images/portfolio-preview.jpg",
        alt: "Preview of our web development portfolio",
        width: 900,
        height: 500
      }
    ],
    wordCount: 1250,
    readabilityScore: 68,
    seoScore: 82
  }
};
