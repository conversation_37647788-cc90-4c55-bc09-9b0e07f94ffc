"use client";

import { cn } from "@/lib/utils";

interface SERPPreviewProps {
  title: string;
  description: string;
  url: string;
}

export default function SERPPreview({ title, description, url }: SERPPreviewProps) {
  // Format URL for display
  const displayUrl = url.replace(/^https?:\/\//i, "");
  
  return (
    <div className="border rounded-md p-4 bg-background">
      <div className="space-y-1 font-sans">
        <div className="text-sm text-green-700 dark:text-green-400">{displayUrl}</div>
        <h3 className={cn(
          "text-xl text-blue-600 dark:text-blue-400 font-medium",
          title.length > 60 ? "text-red-600 dark:text-red-400" : ""
        )}>
          {title || "Page Title"}
        </h3>
        <p className={cn(
          "text-sm text-muted-foreground",
          description.length > 160 ? "text-red-600 dark:text-red-400" : ""
        )}>
          {description || "Page description will appear here. Make sure to write a compelling description that summarizes the page content."}
        </p>
      </div>
      
      <div className="mt-4 text-xs text-muted-foreground flex items-center">
        {title.length > 60 && (
          <div className="text-red-600 dark:text-red-400 mr-4">
            Title too long ({title.length}/60)
          </div>
        )}
        {description.length > 160 && (
          <div className="text-red-600 dark:text-red-400">
            Description too long ({description.length}/160)
          </div>
        )}
      </div>
    </div>
  );
}
