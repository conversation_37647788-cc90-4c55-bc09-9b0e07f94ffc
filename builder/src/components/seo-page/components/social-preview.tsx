"use client";

import { cn } from "@/lib/utils";
import { ImageIcon, Facebook } from "lucide-react";
import { SiX } from "react-icons/si";

interface SocialPreviewProps {
  platform: "facebook" | "x";
  title: string;
  description: string;
  image: string;
  url: string;
  cardType?: "summary" | "summary_large_image";
}

export function SocialPreview({ 
  platform, 
  title, 
  description, 
  image, 
  url,
  cardType = "summary_large_image"
}: SocialPreviewProps) {
  // Format URL for display
  const displayUrl = url.replace(/^https?:\/\//i, "");
  
  // Check if image is provided
  const hasImage = Boolean(image && image.trim() !== "");
  
  // Render image placeholder or actual image
  const renderImageArea = (className: string) => {
    if (hasImage) {
      return (
        <img 
          src={image} 
          alt="Social media preview" 
          className={className}
        />
      );
    } else {
      return (
        <div className={cn(className, "bg-muted/50 dark:bg-muted/30 flex flex-col items-center justify-center")}>
          <ImageIcon className="h-8 w-8 text-muted-foreground mb-2" />
          <p className="text-xs text-muted-foreground text-center px-4">
            {platform === "facebook" 
              ? "Add an image (1200×630px recommended)" 
              : "Add an image (1200×600px recommended)"}
          </p>
        </div>
      );
    }
  };
  
  if (platform === "facebook") {
    return (
      <div className="space-y-3">
        <div className="flex items-center">
          <Facebook className="h-4 w-4 mr-2 text-blue-600 dark:text-blue-400" />
          <span className="text-sm font-medium">Facebook Preview</span>
        </div>
        <div className="border rounded-md overflow-hidden max-w-md bg-background">
          <div className="border-b">
            {renderImageArea("w-full aspect-[1.91/1] object-cover")}
          </div>
          <div className="p-3 space-y-1">
            <div className="text-xs uppercase text-muted-foreground">{displayUrl || "example.com"}</div>
            <h3 className="text-base font-semibold line-clamp-2">{title || "Your title will appear here"}</h3>
            <p className="text-sm text-muted-foreground line-clamp-3">
              {description || "Your description will appear here. Make sure to write a compelling description that encourages people to click."}
            </p>
          </div>
        </div>
      </div>
    );
  }
  
  // X preview (formerly Twitter)
  if (cardType === "summary_large_image") {
    return (
      <div className="space-y-3">
        <div className="flex items-center">
          <SiX className="h-4 w-4 mr-2 text-foreground" />
          <span className="text-sm font-medium">X Preview</span>
        </div>
        <div className="border rounded-md overflow-hidden max-w-md bg-background">
          <div>
            {renderImageArea("w-full aspect-[2/1] object-cover")}
          </div>
          <div className="p-3 space-y-1">
            <h3 className="text-base font-semibold line-clamp-1">{title || "Your title will appear here"}</h3>
            <p className="text-sm text-muted-foreground line-clamp-2">
              {description || "Your description will appear here. Make sure to write a compelling description."}
            </p>
            <div className="text-xs text-muted-foreground mt-1">{displayUrl || "example.com"}</div>
          </div>
        </div>
      </div>
    );
  }
  
  // X summary card (formerly Twitter summary card)
  return (
    <div className="space-y-3">
      <div className="flex items-center">
        <SiX className="h-4 w-4 mr-2 text-foreground" />
        <span className="text-sm font-medium">X Preview (Summary Card)</span>
      </div>
      <div className="border rounded-md overflow-hidden max-w-md bg-background">
        <div className="flex">
          <div className="p-3 flex-1 space-y-1">
            <h3 className="text-base font-semibold line-clamp-1">{title || "Your title will appear here"}</h3>
            <p className="text-sm text-muted-foreground line-clamp-2">
              {description || "Your description will appear here. Make sure to write a compelling description."}
            </p>
            <div className="text-xs text-muted-foreground mt-1">{displayUrl || "example.com"}</div>
          </div>
          <div className="w-24 h-24 flex-shrink-0">
            {renderImageArea("w-full h-full object-cover")}
          </div>
        </div>
      </div>
    </div>
  );
}

export default SocialPreview;
