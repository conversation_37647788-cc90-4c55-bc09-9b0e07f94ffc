"use client";

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  CheckCircle2, 
  Circle, 
  ArrowRight, 
  FileText, 
  Layers, 
  Share2, 
  Settings, 
  Code,
  Target
} from "lucide-react";
import { PageSEOData } from "../types";

interface SEOOptimizationGuideProps {
  seoData: PageSEOData;
  completedTasks: number;
  totalTasks: number;
  nextTask: {
    task: string;
    description: string;
    tab: string;
  };
  pageId: string;
  siteId: string;
  onTabChange?: (tab: string) => void;
}

export function SEOOptimizationGuide({ 
  seoData, 
  completedTasks, 
  totalTasks, 
  nextTask, 
  pageId, 
  siteId, 
  onTabChange 
}: SEOOptimizationGuideProps) {
  const progressPercentage = (completedTasks / totalTasks) * 100;

  return (
    <Card className="border-primary/20 shadow-sm">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>SEO Optimization Guide</CardTitle>
            <CardDescription>Step-by-step guide to improve your page&apos;s SEO performance</CardDescription>
          </div>
          <div className="flex items-center justify-center h-8 w-8 rounded-full bg-primary/10 text-primary">
            <Target className="h-4 w-4" />
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Progress Overview */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Optimization Progress</span>
            <span>{completedTasks}/{totalTasks} tasks completed</span>
          </div>
          <Progress value={progressPercentage} className="h-2" />
        </div>

        {/* Optimization Steps */}
        <div className="space-y-3">
          {/* Basic SEO */}
          <div className="flex items-start gap-3 p-3 border rounded-md hover:bg-muted/50 transition-colors">
            <div className={`flex h-7 w-7 shrink-0 items-center justify-center rounded-full ${
              (seoData?.title?.length ?? 0) > 0 && (seoData?.description?.length ?? 0) > 0 ? "bg-green-500/10 text-green-600 dark:text-green-400" : "bg-primary/10 text-primary"
            }`}>
              {(seoData?.title?.length ?? 0) > 0 && (seoData?.description?.length ?? 0) > 0 ? (
                <CheckCircle2 className="h-4 w-4" />
              ) : (
                <FileText className="h-4 w-4" />
              )}
            </div>
            <div className="flex-1 space-y-1">
              <h3 className="font-medium">Basic SEO Settings</h3>
              <p className={`text-sm text-muted-foreground ${
                (seoData?.title?.length ?? 0) > 0 && (seoData?.description?.length ?? 0) > 0 ? "bg-green-50 dark:bg-green-950/20 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800" : ""
              }`}>
                Configure essential meta tags, title, and description for better search visibility.
              </p>
              <Button 
                size="sm" 
                variant="outline" 
                onClick={() => onTabChange?.("basic")}
                className="mt-1"
              >
                Configure Basic SEO
              </Button>
            </div>
          </div>

          {/* Content Structure */}
          <div className="flex items-start gap-3 p-3 border rounded-md hover:bg-muted/50 transition-colors">
            <div className={`flex h-7 w-7 shrink-0 items-center justify-center rounded-full ${
              (seoData?.contentAnalysis?.headings?.h1?.length ?? 0) > 0 ? "bg-green-500/10 text-green-600 dark:text-green-400" : "bg-primary/10 text-primary"
            }`}>
              {(seoData?.contentAnalysis?.headings?.h1?.length ?? 0) > 0 ? (
                <CheckCircle2 className="h-4 w-4" />
              ) : (
                <Layers className="h-4 w-4" />
              )}
            </div>
            <div className="flex-1 space-y-1">
              <h3 className="font-medium">Content Structure</h3>
              <p className={`text-sm text-muted-foreground ${
                (seoData?.contentAnalysis?.headings?.h1?.length ?? 0) > 0 ? "bg-green-50 dark:bg-green-950/20 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800" : ""
              }`}>
                Optimize heading hierarchy and content organization for better readability and SEO.
              </p>
              <Button 
                size="sm" 
                variant="outline" 
                onClick={() => onTabChange?.("structure")}
                className="mt-1"
              >
                Optimize Structure
              </Button>
            </div>
          </div>

          {/* Social Media */}
          <div className="flex items-start gap-3 p-3 border rounded-md hover:bg-muted/50 transition-colors">
            <div className={`flex h-7 w-7 shrink-0 items-center justify-center rounded-full ${
              (seoData?.ogTitle?.length ?? 0) > 0 || (seoData?.ogDescription?.length ?? 0) > 0 || (seoData?.ogImage?.length ?? 0) > 0 ? "bg-green-500/10 text-green-600 dark:text-green-400" : "bg-primary/10 text-primary"
            }`}>
              {(seoData?.ogTitle?.length ?? 0) > 0 || (seoData?.ogDescription?.length ?? 0) > 0 || (seoData?.ogImage?.length ?? 0) > 0 ? (
                <CheckCircle2 className="h-4 w-4" />
              ) : (
                <Share2 className="h-4 w-4" />
              )}
            </div>
            <div className="flex-1 space-y-1">
              <h3 className="font-medium">Social Media Optimization</h3>
              <p className={`text-sm text-muted-foreground ${
                (seoData?.ogTitle?.length ?? 0) > 0 || (seoData?.ogDescription?.length ?? 0) > 0 || (seoData?.ogImage?.length ?? 0) > 0 ? "bg-green-50 dark:bg-green-950/20 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800" : "bg-muted/50 text-muted-foreground"
              }`}>
                Configure Open Graph and Twitter Card settings for better social media sharing.
              </p>
              <Button 
                size="sm" 
                variant="outline" 
                onClick={() => onTabChange?.("social")}
                className="mt-1"
              >
                Setup Social Media
              </Button>
            </div>
          </div>

          {/* Advanced Settings */}
          <div className="flex items-start gap-3 p-3 border rounded-md hover:bg-muted/50 transition-colors">
            <div className={`flex h-7 w-7 shrink-0 items-center justify-center rounded-full ${
              (seoData?.canonicalUrl?.length ?? 0) > 0 || seoData?.robots?.index !== undefined ? "bg-green-500/10 text-green-600 dark:text-green-400" : "bg-primary/10 text-primary"
            }`}>
              {(seoData?.canonicalUrl?.length ?? 0) > 0 || seoData?.robots?.index !== undefined ? (
                <CheckCircle2 className="h-4 w-4" />
              ) : (
                <Settings className="h-4 w-4" />
              )}
            </div>
            <div className="flex-1 space-y-1">
              <h3 className="font-medium">Advanced SEO Settings</h3>
              <p className={`text-sm text-muted-foreground ${
                (seoData?.canonicalUrl?.length ?? 0) > 0 || seoData?.robots?.index !== undefined ? "bg-green-50 dark:bg-green-950/20 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800" : "bg-muted/50 text-muted-foreground"
              }`}>
                Configure canonical URLs, robots directives, and other technical SEO settings.
              </p>
              <Button 
                size="sm" 
                variant="outline" 
                onClick={() => onTabChange?.("advanced")}
                className="mt-1"
              >
                Configure Advanced
              </Button>
            </div>
          </div>

          {/* Structured Data */}
          <div className="flex items-start gap-3 p-3 border rounded-md hover:bg-muted/50 transition-colors">
            <div className={`flex h-7 w-7 shrink-0 items-center justify-center rounded-full ${
              (seoData?.schemaType?.length ?? 0) > 0 ? "bg-green-500/10 text-green-600 dark:text-green-400" : "bg-primary/10 text-primary"
            }`}>
              {(seoData?.schemaType?.length ?? 0) > 0 ? (
                <CheckCircle2 className="h-4 w-4" />
              ) : (
                <Code className="h-4 w-4" />
              )}
            </div>
            <div className="flex-1 space-y-1">
              <h3 className="font-medium">Structured Data</h3>
              <p className={`text-sm text-muted-foreground ${
                (seoData?.schemaType?.length ?? 0) > 0 ? "bg-green-50 dark:bg-green-950/20 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800" : "bg-muted/50 text-muted-foreground"
              }`}>
                Add schema markup to enhance search results with rich snippets and better visibility.
              </p>
              <Button 
                size="sm" 
                variant="outline" 
                onClick={() => onTabChange?.("structured")}
                className="mt-1"
              >
                Add Schema Markup
              </Button>
            </div>
          </div>
        </div>

        {/* Next Priority Task */}
        <div className="flex items-start gap-3 p-3 border border-primary/20 rounded-md bg-primary/5">
          <div className="flex h-7 w-7 shrink-0 items-center justify-center rounded-full bg-primary text-primary-foreground">
            <ArrowRight className="h-4 w-4" />
          </div>
          <div className="flex-1 space-y-1">
            <div className="flex items-center gap-2">
              <h3 className="font-medium text-primary">Next recommended step</h3>
              <Badge className="bg-primary/10 text-primary border-primary/20">Priority</Badge>
            </div>
            <p className="text-xs text-primary/80">
              {nextTask.description}
            </p>
            <Button 
              size="sm" 
              onClick={() => onTabChange?.(nextTask.tab)}
              className="mt-1 h-7 px-2 text-xs bg-primary hover:bg-primary/90"
            >
              {nextTask.task}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
