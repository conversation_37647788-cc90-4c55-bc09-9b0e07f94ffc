"use client";

import { <PERSON>, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  AlertCircle, 
  Check, 
  FileText, 
  Globe, 
  Layers, 
  Share2, 
  Settings, 
  AlertTriangle,
  Code,
  PieChart
} from "lucide-react";
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { PageSEOData } from "../types";

interface SEOPerformanceCardProps {
  seoData: PageSEOData;
  seoScores: {
    overall: number;
    categories: {
      content: number;
      technical: number;
      onPage: number;
      offPage: number;
      user: number;
    };
    sections: {
      basic: number;
      structure: number;
      social: number;
      structured: number;
      advanced: number;
    };
    issues: {
      critical: number;
      warnings: number;
      passed: number;
    };
  };
}

export function SEOPerformanceCard({ seoD<PERSON>, seoScores }: SEOPerformanceCardProps) {
  // Get status icon and color based on score
  const getScoreStatus = (score: number) => {
    if (score >= 80) return { 
      color: "text-green-600 dark:text-green-400", 
      bgColor: "bg-green-500/10 dark:bg-green-400/10", 
      icon: <Check className="h-4 w-4" /> 
    };
    if (score >= 60) return { 
      color: "text-amber-600 dark:text-amber-400", 
      bgColor: "bg-amber-500/10 dark:bg-amber-400/10", 
      icon: <AlertTriangle className="h-4 w-4" /> 
    };
    return { 
      color: "text-red-600 dark:text-red-400", 
      bgColor: "bg-red-500/10 dark:bg-red-400/10", 
      icon: <AlertCircle className="h-4 w-4" /> 
    };
  };

  // Get overall score color
  const getOverallScoreColor = () => {
    if (seoScores.overall >= 80) return 'text-green-600 dark:text-green-400';
    if (seoScores.overall >= 60) return 'text-amber-600 dark:text-amber-400';
    return 'text-red-600 dark:text-red-400';
  };

  return (
    <Card className="border-primary/20 shadow-sm">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>SEO Performance Analysis</CardTitle>
            <CardDescription>
              A comprehensive analysis of this page&apos;s SEO performance
            </CardDescription>
          </div>
          <div className="flex items-center justify-center h-8 w-8 rounded-full bg-primary/10 text-primary">
            <PieChart className="h-4 w-4" />
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-6">
        <div className="grid grid-cols-1 lg:grid-cols-7 gap-6">
          {/* Overall Score */}
          <div className="lg:col-span-2">
            <div className="flex flex-col items-center justify-center h-full p-6 border rounded-lg bg-muted/30">
              <div className="relative">
                <svg className="w-32 h-32">
                  <circle
                    className="text-muted-foreground/20"
                    strokeWidth="8"
                    stroke="currentColor"
                    fill="transparent"
                    r="56"
                    cx="64"
                    cy="64"
                  />
                  <circle
                    className={getOverallScoreColor()}
                    strokeWidth="8"
                    strokeDasharray={360}
                    strokeDashoffset={360 - (360 * seoScores.overall) / 100}
                    strokeLinecap="round"
                    stroke="currentColor"
                    fill="transparent"
                    r="56"
                    cx="64"
                    cy="64"
                  />
                </svg>
                <div className="absolute inset-0 flex flex-col items-center justify-center">
                  <span className="text-4xl font-bold">{seoScores.overall}</span>
                  <span className="text-sm text-muted-foreground">Overall Score</span>
                </div>
              </div>
              
              <div className="flex justify-between w-full mt-6 text-sm">
                <div className="flex items-center">
                  <span className="inline-block w-3 h-3 rounded-full bg-red-500 dark:bg-red-400 mr-2"></span>
                  <span>Critical: {seoScores.issues.critical}</span>
                </div>
                <div className="flex items-center">
                  <span className="inline-block w-3 h-3 rounded-full bg-amber-500 dark:bg-amber-400 mr-2"></span>
                  <span>Warnings: {seoScores.issues.warnings}</span>
                </div>
                <div className="flex items-center">
                  <span className="inline-block w-3 h-3 rounded-full bg-green-500 dark:bg-green-400 mr-2"></span>
                  <span>Passed: {seoScores.issues.passed}</span>
                </div>
              </div>
            </div>
          </div>
          
          {/* Category Scores */}
          <div className="lg:col-span-5">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex flex-col p-4 border rounded-lg bg-background shadow-sm">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center">
                          <FileText className="h-4 w-4 mr-2" />
                          <span className="font-medium">Content</span>
                        </div>
                        <Badge className={`${getScoreStatus(seoScores.categories.content).color} ${getScoreStatus(seoScores.categories.content).bgColor} border-0`}>
                          {seoScores.categories.content}
                        </Badge>
                      </div>
                      <Progress value={seoScores.categories.content} className="h-1.5" />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Content quality, readability, and keyword usage</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex flex-col p-4 border rounded-lg bg-background shadow-sm">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center">
                          <Layers className="h-4 w-4 mr-2" />
                          <span className="font-medium">Structure</span>
                        </div>
                        <Badge className={`${getScoreStatus(seoScores.categories.technical).color} ${getScoreStatus(seoScores.categories.technical).bgColor} border-0`}>
                          {seoScores.categories.technical}
                        </Badge>
                      </div>
                      <Progress value={seoScores.categories.technical} className="h-1.5" />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Heading structure, HTML semantics, and page organization</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex flex-col p-4 border rounded-lg bg-background shadow-sm">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center">
                          <Globe className="h-4 w-4 mr-2" />
                          <span className="font-medium">On-Page</span>
                        </div>
                        <Badge className={`${getScoreStatus(seoScores.categories.onPage).color} ${getScoreStatus(seoScores.categories.onPage).bgColor} border-0`}>
                          {seoScores.categories.onPage}
                        </Badge>
                      </div>
                      <Progress value={seoScores.categories.onPage} className="h-1.5" />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Meta tags, URLs, and on-page optimization factors</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex flex-col p-4 border rounded-lg bg-background shadow-sm">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center">
                          <Share2 className="h-4 w-4 mr-2" />
                          <span className="font-medium">Social</span>
                        </div>
                        <Badge className={`${getScoreStatus(seoScores.sections.social).color} ${getScoreStatus(seoScores.sections.social).bgColor} border-0`}>
                          {seoScores.sections.social}
                        </Badge>
                      </div>
                      <Progress value={seoScores.sections.social} className="h-1.5" />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Social media optimization and sharing settings</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex flex-col p-4 border rounded-lg bg-background shadow-sm">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center">
                          <Code className="h-4 w-4 mr-2" />
                          <span className="font-medium">Schema</span>
                        </div>
                        <Badge className={`${getScoreStatus(seoScores.sections.structured).color} ${getScoreStatus(seoScores.sections.structured).bgColor} border-0`}>
                          {seoScores.sections.structured}
                        </Badge>
                      </div>
                      <Progress value={seoScores.sections.structured} className="h-1.5" />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Structured data and schema markup implementation</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex flex-col p-4 border rounded-lg bg-background shadow-sm">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center">
                          <Settings className="h-4 w-4 mr-2" />
                          <span className="font-medium">Technical</span>
                        </div>
                        <Badge className={`${getScoreStatus(seoScores.sections.advanced).color} ${getScoreStatus(seoScores.sections.advanced).bgColor} border-0`}>
                          {seoScores.sections.advanced}
                        </Badge>
                      </div>
                      <Progress value={seoScores.sections.advanced} className="h-1.5" />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Technical SEO settings like canonicals and robots directives</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
        </div>
        
        {/* Critical Issues */}
        {seoScores.issues.critical > 0 && (
          <div className="mt-6">
            <h3 className="text-sm font-medium mb-3 text-red-600 dark:text-red-400 flex items-center">
              <AlertCircle className="h-4 w-4 mr-1" />
              Critical Issues ({seoScores.issues.critical})
            </h3>
            
            <div className="space-y-2">
              {seoData?.title?.length === 0 && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Missing page title. Add a descriptive title to improve SEO and click-through rates.
                  </AlertDescription>
                </Alert>
              )}
              
              {seoData?.description?.length === 0 && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Missing meta description. Add a compelling description to improve search result appearance.
                  </AlertDescription>
                </Alert>
              )}
              
              {!seoData?.contentAnalysis?.headings?.h1 && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Missing H1 heading. Every page should have exactly one H1 heading for proper structure.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
