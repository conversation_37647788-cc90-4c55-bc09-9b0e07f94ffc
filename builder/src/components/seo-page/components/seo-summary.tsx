"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { FileText } from "lucide-react";
import { PageSEOData } from "../types";

interface SEOSummaryProps {
  seoData: PageSEOData;
}

export function SEOSummary({ seoData }: SEOSummaryProps) {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-primary" />
            <CardTitle>Page SEO Summary</CardTitle>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-1">Page Title</h3>
              <p className="font-medium">{seoD<PERSON>?.title || "Not set"}</p>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-1">Meta Description</h3>
              <p>{seoData?.description || "Not set"}</p>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-1">Keywords</h3>
              <div className="flex flex-wrap gap-1">
                {seoData?.keywords && seoData?.keywords.length > 0 ? (
                  seoData?.keywords.map((keyword: string, index: number) => (
                    <Badge key={index} variant="outline" className="bg-muted/50">
                      {keyword}
                    </Badge>
                  ))
                ) : (
                  <span className="text-muted-foreground">No keywords set</span>
                )}
              </div>
            </div>
          </div>
          
          <div className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-1">Canonical URL</h3>
              <p className="font-mono text-sm">{seoData?.canonicalUrl || "Not set"}</p>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-1">Robots Directives</h3>
              <div className="flex gap-2">
                <Badge variant={seoData?.robots?.index ? "default" : "outline"} className={seoData?.robots?.index ? "bg-green-50 dark:bg-green-950/20 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800" : ""}>
                  {seoData?.robots?.index ? "Index" : "Noindex"}
                </Badge>
                <Badge variant={seoData?.robots?.follow ? "default" : "outline"} className={seoData?.robots?.follow ? "bg-green-50 dark:bg-green-950/20 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800" : ""}>
                  {seoData?.robots?.follow ? "Follow" : "Nofollow"}
                </Badge>
              </div>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-1">Structured Data Type</h3>
              <Badge variant="outline" className="bg-muted/50">
                {seoData?.schemaType || "None"}
              </Badge>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
