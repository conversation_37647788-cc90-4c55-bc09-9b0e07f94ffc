"use client";

import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { AlertCircle, Check, Info, Plus, Trash2 } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";

// 定义区块类型到标题级别的映射规则
const blockTypeToHeadingMap = {
  Hero: "h1",
  Features: "h2",
  Testimonials: "h2",
  Pricing: "h2",
  FAQ: "h2",
  Contact: "h2",
  Footer: "h3",
  // 其他区块类型的默认映射
};

// 定义区块描述到 HTML 标签的映射规则
const blockDescriptionToTagMap = {
  Hero: "p",
  Features: "p",
  Testimonials: "blockquote",
  Pricing: "p",
  FAQ: "p",
  Contact: "p",
  Footer: "small",
  // 其他区块类型的默认映射
};

// 模拟页面区块数据 - 在实际实现中，这应该从页面数据中获取
interface PageBlock {
  id: string;
  type: string;
  title: string;
  description?: string;
}

// 模拟页面区块数据
const mockPageBlocks: PageBlock[] = [
  {
    id: "block-1",
    type: "Hero",
    title: "Professional Web Development Services",
    description: "Expert web development services using the latest technologies."
  },
  {
    id: "block-2",
    type: "Features",
    title: "Our Web Development Process",
    description: "We follow a structured approach to deliver high-quality websites."
  },
  {
    id: "block-3",
    type: "Features",
    title: "Technologies We Use",
    description: "Modern frameworks and tools for optimal performance."
  },
  {
    id: "block-4",
    type: "Testimonials",
    title: "Client Testimonials",
    description: "See what our clients say about our exceptional service."
  },
  {
    id: "block-5",
    type: "FAQ",
    title: "Frequently Asked Questions",
    description: "Find answers to common questions about our services."
  }
];

interface HeadingStructureFormProps {
  headings: {
    h1: string[];
    h2: string[];
    h3: string[];
    h4?: string[];
    h5?: string[];
    h6?: string[];
  };
  onUpdateHeadings: (headings: any) => void;
}

interface StructuredHeading {
  id: string;
  type: string;
  level: string;
  text: string;
  description?: string;
  descriptionTag?: string;
}

export function HeadingStructureForm({ headings, onUpdateHeadings }: HeadingStructureFormProps) {
  // 将区块转换为结构化的标题数据
  const convertBlocksToStructuredHeadings = (blocks: PageBlock[]): StructuredHeading[] => {
    return blocks.map(block => {
      const headingLevel = blockTypeToHeadingMap[block.type as keyof typeof blockTypeToHeadingMap] || "h3";
      const descriptionTag = blockDescriptionToTagMap[block.type as keyof typeof blockDescriptionToTagMap] || "p";
      
      return {
        id: block.id,
        type: block.type,
        level: headingLevel,
        text: block.title,
        description: block.description || "",
        descriptionTag: descriptionTag
      };
    });
  };
  
  // 状态管理
  const [structuredHeadings, setStructuredHeadings] = useState<StructuredHeading[]>(
    convertBlocksToStructuredHeadings(mockPageBlocks)
  );
  
  // 更新标题文本
  const updateHeadingText = (id: string, text: string) => {
    const updated = structuredHeadings.map(heading => {
      if (heading.id === id) {
        return { ...heading, text };
      }
      return heading;
    });
    
    setStructuredHeadings(updated);
    updateHeadingsOutput(updated);
  };
  
  // 更新描述文本
  const updateDescriptionText = (id: string, description: string) => {
    const updated = structuredHeadings.map(heading => {
      if (heading.id === id) {
        return { ...heading, description };
      }
      return heading;
    });
    
    setStructuredHeadings(updated);
    // 描述更新不影响标题结构，所以不需要调用 updateHeadingsOutput
  };
  
  // 将结构化数据转换回扁平数组并更新父组件
  const updateHeadingsOutput = (headings: StructuredHeading[]) => {
    const output = {
      h1: [] as string[],
      h2: [] as string[],
      h3: [] as string[],
      h4: [] as string[],
      h5: [] as string[],
      h6: [] as string[],
    };
    
    headings.forEach(heading => {
      output[heading.level as keyof typeof output].push(heading.text);
    });
    
    onUpdateHeadings(output);
  };
  
  // 初始化时更新标题输出
  useEffect(() => {
    updateHeadingsOutput(structuredHeadings);
  }, []);
  
  // 检查 H1 是否唯一
  const h1Count = structuredHeadings.filter(h => h.level === "h1").length;
  const hasH1Issue = h1Count !== 1;
  
  // 获取标题级别的缩进样式
  const getIndentClass = (level: string) => {
    switch (level) {
      case "h1": return "pl-0";
      case "h2": return "pl-6";
      case "h3": return "pl-12";
      case "h4": return "pl-18";
      case "h5": return "pl-24";
      case "h6": return "pl-30";
      default: return "pl-0";
    }
  };
  
  // 获取标题级别的颜色样式
  const getLevelColorClass = (level: string) => {
    switch (level) {
      case "h1": return "text-blue-600 dark:text-blue-400 font-bold";
      case "h2": return "text-green-600 dark:text-green-400 font-semibold";
      case "h3": return "text-amber-600 dark:text-amber-400";
      case "h4": return "text-purple-600 dark:text-purple-400";
      case "h5": return "text-muted-foreground";
      case "h6": return "text-muted-foreground";
      default: return "";
    }
  };
  
  // 获取描述标签的样式
  const getDescriptionTagClass = (tag: string) => {
    switch (tag) {
      case "p": return "text-muted-foreground";
      case "blockquote": return "text-indigo-600 dark:text-indigo-400 italic";
      case "small": return "text-muted-foreground text-xs";
      default: return "text-muted-foreground";
    }
  };
  
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Page Structure</h3>
        <p className="text-sm text-muted-foreground">
          Edit your page heading structure for better SEO
        </p>
      </div>
      
      {hasH1Issue && (
        <div className="flex items-center gap-2 p-3 bg-amber-50 dark:bg-amber-950/20 text-amber-700 dark:text-amber-300 border border-amber-200 dark:border-amber-800 rounded-md text-sm mb-4">
          <AlertCircle className="h-4 w-4 flex-shrink-0" />
          <span>
            {h1Count === 0 ? "Your page needs exactly one H1 heading" : "Your page should have only one H1 heading"}
          </span>
        </div>
      )}
      
      <div className="space-y-5">
        {structuredHeadings.map((heading) => {
          // 根据标题级别动态调整宽度和样式
          const isH1 = heading.level === "h1";
          const cardClass = isH1 
            ? "border-blue-200 dark:border-blue-800 bg-blue-50/30 dark:bg-blue-950/20" 
            : heading.level === "h2" 
              ? "border-green-200 dark:border-green-800 bg-green-50/30 dark:bg-green-950/20" 
              : "";
          
          return (
            <div 
              key={heading.id} 
              className={`${getIndentClass(heading.level)}`}
            >
              <Card className={`border ${cardClass} overflow-hidden`}>
                <CardContent className="p-0">
                  {/* 标题栏部分 */}
                  <div className="flex items-center p-3 bg-muted/30 border-b gap-2">
                    <div className={`text-lg font-semibold ${getLevelColorClass(heading.level)} w-12 text-center`}>
                      {heading.level.toUpperCase()}
                    </div>
                    
                    <Badge variant="outline" className="mr-2">
                      {heading.type}
                    </Badge>
                    
                    <div className="flex-1 text-xs text-muted-foreground text-right">
                      Block ID: {heading.id}
                    </div>
                  </div>
                  
                  {/* 内容部分 */}
                  <div className="p-4">
                    <div className="mb-4">
                      <div className="flex items-center justify-between mb-1.5">
                        <div className="text-sm font-medium">Heading Text</div>
                        <Badge variant="secondary" className="text-xs">
                          {`<${heading.level}>`}
                        </Badge>
                      </div>
                      <Input
                        value={heading.text}
                        onChange={(e) => updateHeadingText(heading.id, e.target.value)}
                        className={`w-full ${isH1 ? "text-lg font-medium" : ""}`}
                      />
                    </div>
                    
                    <div>
                      <div className="flex items-center justify-between mb-1.5">
                        <div className="text-sm font-medium">Description</div>
                        {heading.descriptionTag && (
                          <Badge 
                            variant="secondary" 
                            className={`text-xs ${getDescriptionTagClass(heading.descriptionTag)}`}
                          >
                            {`<${heading.descriptionTag}>`}
                          </Badge>
                        )}
                      </div>
                      <Textarea
                        value={heading.description || ""}
                        onChange={(e) => updateDescriptionText(heading.id, e.target.value)}
                        className={`w-full resize-none ${
                          heading.descriptionTag === "blockquote" ? "italic" : ""
                        }`}
                        rows={isH1 ? 3 : 2}
                        placeholder={`Enter ${heading.type.toLowerCase()} description`}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          );
        })}
      </div>
      
      <div className="flex items-center gap-2 p-3 bg-muted/30 rounded-md text-sm text-muted-foreground">
        <Info className="h-4 w-4" />
        <span>Heading structure and descriptions are based on your page blocks. Edit the text to improve SEO.</span>
      </div>
    </div>
  );
}
