"use client";

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  FileText, 
  AlertCircle, 
  Check, 
  Info, 
  Eye,
  Hash,
  Image as ImageIcon
} from "lucide-react";
import { PageSEOData } from "../types";

interface PageContentAnalysisProps {
  seoData: PageSEOData;
}

export function PageContentAnalysis({ seoData }: PageContentAnalysisProps) {
  const contentAnalysis = seoData?.contentAnalysis;
  
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-2">
          <FileText className="h-5 w-5 text-primary" />
          <div>
            <CardTitle>Content Analysis</CardTitle>
            <CardDescription>
              Detailed analysis of your page content and structure
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Content Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 border rounded-lg">
            <div className="text-2xl font-bold">{contentAnalysis?.wordCount || 0}</div>
            <div className="text-sm text-muted-foreground">Words</div>
          </div>
          <div className="text-center p-4 border rounded-lg">
            <div className="text-2xl font-bold">{contentAnalysis?.readabilityScore || 0}</div>
            <div className="text-sm text-muted-foreground">Readability</div>
          </div>
                     <div className="text-center p-4 border rounded-lg">
             <div className="text-2xl font-bold">{(contentAnalysis as any)?.keywordDensity || 0}%</div>
             <div className="text-sm text-muted-foreground">Keyword Density</div>
           </div>
        </div>

        {/* Readability Analysis */}
        <div className="space-y-3">
          <h3 className="font-medium flex items-center gap-2">
            <Eye className="h-4 w-4" />
            Readability Analysis
          </h3>
          <div className="space-y-2">
                         <div className="flex justify-between text-sm">
               <span>Reading Level</span>
               <span>{(contentAnalysis as any)?.readingLevel || "Unknown"}</span>
             </div>
            <Progress value={contentAnalysis?.readabilityScore || 0} className="h-2" />
            <p className="text-sm text-muted-foreground">
              {contentAnalysis?.readabilityScore && contentAnalysis.readabilityScore >= 70 
                ? "Good readability score. Your content is easy to understand."
                : "Consider simplifying your content for better readability."
              }
            </p>
          </div>
        </div>

        {/* Heading Structure */}
        <div className="space-y-3">
          <h3 className="font-medium flex items-center gap-2">
            <Hash className="h-4 w-4" />
            Heading Structure
          </h3>
          <div className="space-y-2">
                         {['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].map((level) => {
               const count = (contentAnalysis?.headings as any)?.[level]?.length || 0;
              const isOptimal = getHeadingStatus(level, count);
              
              return (
                <div key={level} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {getHeadingIcon(level, count)}
                    <span className="text-sm font-mono uppercase">{level}</span>
                    <span className="text-sm text-muted-foreground">({count})</span>
                  </div>
                  <Badge variant={isOptimal.variant} className={isOptimal.className}>
                    {isOptimal.label}
                  </Badge>
                </div>
              );
            })}
          </div>
        </div>

        {/* Images Analysis */}
        {contentAnalysis?.images && contentAnalysis.images.length > 0 && (
          <div className="space-y-3">
            <h3 className="font-medium flex items-center gap-2">
              <ImageIcon className="h-4 w-4" />
              Images Analysis
            </h3>
            <div className="space-y-2">
              {contentAnalysis.images.slice(0, 5).map((image: any, i: number) => (
                <div key={`img-${i}`} className="text-sm border-b pb-2 last:border-b-0">
                  <div className="font-medium truncate">{image.src}</div>
                  <div className={`text-xs ${!image.alt ? "text-red-600 dark:text-red-400" : "text-muted-foreground"}`}>
                    {image.alt ? `Alt: ${image.alt}` : "Missing alt text"}
                  </div>
                </div>
              ))}
              {contentAnalysis.images.length > 5 && (
                <div className="text-xs text-muted-foreground">
                  +{contentAnalysis.images.length - 5} more images
                </div>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Helper function to get heading status
function getHeadingStatus(level: string, count: number) {
  if (level === 'h1') {
    if (count === 1) return { 
      variant: "outline" as const, 
      className: "bg-green-50 dark:bg-green-950/20 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800", 
      label: "Perfect" 
    };
    if (count === 0) return { 
      variant: "destructive" as const, 
      className: "", 
      label: "Missing" 
    };
    return { 
      variant: "outline" as const, 
      className: "bg-amber-50 dark:bg-amber-950/20 text-amber-700 dark:text-amber-300 border-amber-200 dark:border-amber-800", 
      label: "Too many" 
    };
  }
  
  if (count > 0) return { 
    variant: "outline" as const, 
    className: "bg-green-50 dark:bg-green-950/20 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800", 
    label: "Good" 
  };
  
  return { 
    variant: "outline" as const, 
    className: "bg-muted/50 text-muted-foreground border-muted", 
    label: "None" 
  };
}

// Helper function to get heading icon
function getHeadingIcon(level: string, count: number) {
  if (level === 'h1') {
    if (count === 1) return <Check className="h-4 w-4 text-green-600 dark:text-green-400" />;
    if (count === 0) return <AlertCircle className="h-4 w-4 text-red-600 dark:text-red-400" />;
    return <AlertCircle className="h-4 w-4 text-amber-600 dark:text-amber-400" />;
  }
  
  if (count > 0) return <Check className="h-4 w-4 text-green-600 dark:text-green-400" />;
  return <Info className="h-4 w-4 text-muted-foreground" />;
}
