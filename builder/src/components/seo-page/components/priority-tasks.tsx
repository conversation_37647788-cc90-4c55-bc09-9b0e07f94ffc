"use client";

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  ListChecks, 
  Check, 
  ArrowRight, 
  Share2, 
  Code 
} from "lucide-react";
import { PageSEOData } from "../types";

interface PriorityTasksProps {
  seoData: PageSEOData;
  completedTasks: number;
  totalTasks: number;
  nextTask: {
    task: string;
    description: string;
    tab: string;
  };
  onTabChange?: (tab: string) => void;
}

export function PriorityTasks({ 
  seoData, 
  completedTasks, 
  totalTasks, 
  nextTask, 
  onTabChange 
}: PriorityTasksProps) {
  const progressPercentage = (completedTasks / totalTasks) * 100;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-2">
          <ListChecks className="h-5 w-5 text-primary" />
          <div>
            <CardTitle>Priority Tasks</CardTitle>
            <CardDescription>Focus on these high-impact SEO improvements</CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Progress Summary */}
        {completedTasks === totalTasks ? (
          <Alert className="bg-green-50 dark:bg-green-950/20 border-green-200 dark:border-green-800">
            <Check className="h-4 w-4 text-green-600 dark:text-green-400" />
            <AlertDescription className="text-green-700 dark:text-green-300">
              Excellent! You&apos;ve completed all essential SEO tasks for this page.
            </AlertDescription>
          </Alert>
        ) : (
          <>
            <div className="flex items-center justify-between text-sm mb-2">
              <span>SEO Tasks Progress</span>
              <span className="font-medium">{completedTasks}/{totalTasks}</span>
            </div>
            <Progress value={progressPercentage} className="h-2" />

            {/* Next Priority Task */}
            <div className="p-3 border rounded-md bg-primary/5 border-primary/20">
              <div className="flex items-start gap-3">
                <ArrowRight className="h-5 w-5 text-primary mt-0.5 flex-shrink-0" />
                <div className="flex-1">
                  <h3 className="font-medium text-primary">{nextTask.task}</h3>
                  <p className="text-sm text-primary/80 mt-1">
                    {nextTask.description}
                  </p>
                  <Button 
                    size="sm" 
                    onClick={() => onTabChange?.(nextTask.tab)}
                    className="mt-2 bg-primary hover:bg-primary/90"
                  >
                    Start Now
                  </Button>
                </div>
              </div>
            </div>
          </>
        )}

        {/* Additional Recommendations */}
        <div className="space-y-3">
          <h4 className="font-medium text-sm">Additional Recommendations</h4>
          
          {/* Social Media Optimization */}
          {!(seoData?.ogTitle && seoData?.ogDescription && seoData?.ogImage) && (
            <div className="flex items-start gap-3 p-3 border rounded-md">
              <Share2 className="h-5 w-5 text-muted-foreground mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <h3 className="font-medium">Enhance Social Media Presence</h3>
                <p className="text-sm text-muted-foreground mt-1">
                  Optimize how your page appears when shared on social platforms.
                </p>
                <Button 
                  size="sm" 
                  variant="outline" 
                  onClick={() => onTabChange?.("social")}
                  className="mt-2"
                >
                  Setup Social Media
                </Button>
              </div>
            </div>
          )}

          {/* Structured Data */}
          {!seoData?.schemaType && (
            <div className="flex items-start gap-3 p-3 border rounded-md">
              <Code className="h-5 w-5 text-muted-foreground mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <h3 className="font-medium">Add Structured Data</h3>
                <p className="text-sm text-muted-foreground mt-1">
                  Enhance search results with rich snippets using schema markup.
                </p>
                <Button 
                  size="sm" 
                  variant="outline" 
                  onClick={() => onTabChange?.("structured")}
                  className="mt-2"
                >
                  Add Schema Markup
                </Button>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
