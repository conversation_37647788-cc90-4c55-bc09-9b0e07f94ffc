"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { PageSEOData } from "../types";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { SocialPreview } from "@/components/seo-page/components/social-preview";
import { useEffect, useState } from "react";
import { usePageSeo, useUpdateSocialMedia } from "@/lib/hooks/useSeo";
import { toast } from "@/components/ui/use-toast";
import { Skeleton } from "@/components/ui/skeleton";
import { AlertCircle, Info, ExternalLink, ChevronDown, ChevronUp, HelpCircle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Switch } from "@/components/ui/switch";

const socialMediaFormSchema = z.object({
  ogTitle: z.string().min(5, { message: "Title must be at least 5 characters" }).max(65, { message: "Title should not exceed 65 characters" }),
  ogDescription: z.string().min(10, { message: "Description must be at least 10 characters" }).max(155, { message: "Description should not exceed 155 characters" }),
  ogImage: z.string().url({ message: "Please enter a valid URL" }).or(z.string().length(0)),
  xTitle: z.string().min(5, { message: "Title must be at least 5 characters" }).max(70, { message: "Title should not exceed 70 characters" }),
  xDescription: z.string().min(10, { message: "Description must be at least 10 characters" }).max(200, { message: "Description should not exceed 200 characters" }),
  xImage: z.string().url({ message: "Please enter a valid URL" }).or(z.string().length(0)),
  xCard: z.enum(["SUMMARY", "SUMMARY_LARGE_IMAGE", "APP", "PLAYER"]),
  // Additional fields for APP card
  xAppName: z.string().max(50).optional(),
  xAppCountry: z.string().max(2).optional(),
  xAppIdIphone: z.string().optional(),
  xAppIdIpad: z.string().optional(),
  xAppIdGoogleplay: z.string().optional(),
  // Additional fields for PLAYER card
  xPlayerUrl: z.string().url().or(z.string().length(0)).optional(),
  xPlayerWidth: z.string().optional(),
  xPlayerHeight: z.string().optional(),
  xPlayerStream: z.boolean().optional(),
});

type SocialMediaFormValues = z.infer<typeof socialMediaFormSchema>;

interface SocialMediaSEOFormProps {
  pageId: string;
  onSaveSuccess?: () => void;
}

export default function SocialMediaSEOForm({ pageId, onSaveSuccess }: SocialMediaSEOFormProps) {
  const [isSaving, setIsSaving] = useState(false);
  const [canonicalUrl, setCanonicalUrl] = useState<string>("");
  const [tipsExpanded, setTipsExpanded] = useState(false);
  
  // Fetch page SEO data
  const { data: seoData, isLoading, error } = usePageSeo(pageId);
  
  // Update social media mutation
  const updateSocialMedia = useUpdateSocialMedia();

  const form = useForm<SocialMediaFormValues>({
    resolver: zodResolver(socialMediaFormSchema),
    defaultValues: {
      ogTitle: "",
      ogDescription: "",
      ogImage: "",
      xTitle: "",
      xDescription: "",
      xImage: "",
      xCard: "SUMMARY_LARGE_IMAGE",
      xAppName: "",
      xAppCountry: "",
      xAppIdIphone: "",
      xAppIdIpad: "",
      xAppIdGoogleplay: "",
      xPlayerUrl: "",
      xPlayerWidth: "1280",
      xPlayerHeight: "720",
      xPlayerStream: false,
    },
  });

  // Initialize form with data when it's loaded
  useEffect(() => {
    if (seoData) {
      setCanonicalUrl(seoData.canonicalUrl || "");
      
      // 获取可能存储在 JSON 字段中的额外数据
      const xCardData = seoData.xCardData || {};
      
      form.reset({
        ogTitle: seoData.ogTitle || seoData.title || "",
        ogDescription: seoData.ogDescription || seoData.description || "",
        ogImage: seoData.ogImage || "",
        xTitle: seoData.xTitle || seoData.title || "",
        xDescription: seoData.xDescription || seoData.description || "",
        xImage: seoData.xImage || "",
        xCard: (seoData.xCardType as "SUMMARY" | "SUMMARY_LARGE_IMAGE" | "APP" | "PLAYER") || "SUMMARY_LARGE_IMAGE",
        // 从 xCardData 中获取额外数据
        xAppName: xCardData.xAppName || "",
        xAppCountry: xCardData.xAppCountry || "",
        xAppIdIphone: xCardData.xAppIdIphone || "",
        xAppIdIpad: xCardData.xAppIdIpad || "",
        xAppIdGoogleplay: xCardData.xAppIdGoogleplay || "",
        xPlayerUrl: xCardData.xPlayerUrl || "",
        xPlayerWidth: xCardData.xPlayerWidth || "1280",
        xPlayerHeight: xCardData.xPlayerHeight || "720",
        xPlayerStream: xCardData.xPlayerStream || false,
      });
    }
  }, [seoData, form]);

  async function onSubmit(data: SocialMediaFormValues) {
    setIsSaving(true);
    try {
      // 准备基本数据
      const submitData: any = {
        ogTitle: data.ogTitle,
        ogDescription: data.ogDescription,
        ogImage: data.ogImage,
        xTitle: data.xTitle,
        xDescription: data.xDescription,
        xImage: data.xImage,
        xCardType: data.xCard,
      };

      // 根据卡片类型准备 xCardData
      if (data.xCard === "APP" || data.xCard === "PLAYER") {
        let xCardData: any = {};
        
        if (data.xCard === "APP") {
          xCardData = {
            xAppName: data.xAppName,
            xAppCountry: data.xAppCountry,
            xAppIdIphone: data.xAppIdIphone,
            xAppIdIpad: data.xAppIdIpad,
            xAppIdGoogleplay: data.xAppIdGoogleplay,
          };
        } else if (data.xCard === "PLAYER") {
          xCardData = {
            xPlayerUrl: data.xPlayerUrl,
            xPlayerWidth: data.xPlayerWidth,
            xPlayerHeight: data.xPlayerHeight,
            xPlayerStream: data.xPlayerStream,
          };
        }
        
        submitData.xCardData = xCardData;
      } else {
        // 对于基本卡片类型，清除 xCardData
        submitData.xCardData = null;
      }

      await updateSocialMedia.mutateAsync({
        pageId,
        data: submitData
      });
      
      toast({
        title: "Success",
        description: "Social media settings have been updated",
        variant: "default",
      });
      
      if (onSaveSuccess) {
        onSaveSuccess();
      }
    } catch (error) {
      console.error("Error updating social media settings:", error);
      toast({
        title: "Error",
        description: "Failed to update social media settings",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  }

  // Get form values for preview
  const ogTitle = form.watch("ogTitle");
  const ogDescription = form.watch("ogDescription");
  const ogImage = form.watch("ogImage");
  const xTitle = form.watch("xTitle");
  const xDescription = form.watch("xDescription");
  const xImage = form.watch("xImage");
  const xCard = form.watch("xCard");

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-full" />
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-20 w-full" />
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-10 w-full" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          Failed to load SEO data. Please try again later.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} id="social-media-form" className="space-y-6">
          {/* Facebook / Open Graph Card */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Facebook / Open Graph</CardTitle>
                  <CardDescription>Settings for Facebook and other platforms that support Open Graph</CardDescription>
                </div>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => window.open('https://developers.facebook.com/tools/debug/', '_blank')}
                        className="flex items-center"
                      >
                        <ExternalLink className="h-4 w-4 mr-2" />
                        Validator
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Test your Open Graph tags with Facebook Debugger</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-5 gap-6">
                {/* Form Fields - Left side */}
                <div className="lg:col-span-3 space-y-6">
                  <FormField
                    control={form.control}
                    name="ogTitle"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Open Graph Title</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter title for social sharing" {...field} />
                        </FormControl>
                        <div className="flex justify-between text-xs text-muted-foreground mt-1">
                          <span>Recommended: 40-60 characters</span>
                          <span className={field.value.length > 65 ? "text-red-600 dark:text-red-400 font-medium" : ""}>
                            {field.value.length}/65
                          </span>
                        </div>
                        <FormDescription>
                          The title that appears when shared on Facebook and other platforms
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="ogDescription"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Open Graph Description</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter description for social sharing"
                            className="resize-none"
                            {...field}
                          />
                        </FormControl>
                        <div className="flex justify-between text-xs text-muted-foreground mt-1">
                          <span>Recommended: 100-155 characters</span>
                          <span className={field.value.length > 155 ? "text-red-600 dark:text-red-400 font-medium" : ""}>
                            {field.value.length}/155
                          </span>
                        </div>
                        <FormDescription>
                          The description that appears when shared on Facebook and other platforms
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="ogImage"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Open Graph Image</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter image URL" {...field} />
                        </FormControl>
                        <FormDescription>
                          The image that appears when shared on Facebook and other platforms (optimal size: 1200×630 pixels, ratio 1.91:1)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                {/* Preview - Right side */}
                <div className="lg:col-span-2">
                  <Card>
                    <CardContent className="p-4">
                      <div className="space-y-2">
                        <h3 className="text-sm font-medium text-muted-foreground">Facebook / Open Graph Preview</h3>
                        <SocialPreview 
                          platform="facebook"
                          title={ogTitle}
                          description={ogDescription}
                          image={ogImage}
                          url={canonicalUrl || "https://example.com/page"}
                        />
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* X Card */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>X (formerly Twitter)</CardTitle>
                  <CardDescription>Settings for X platform</CardDescription>
                </div>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => window.open('https://cards-dev.twitter.com/validator', '_blank')}
                        className="flex items-center"
                      >
                        <ExternalLink className="h-4 w-4 mr-2" />
                        Card Validator
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Test your X cards with the Card Validator</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-5 gap-6">
                {/* X Form Fields - Left side */}
                <div className="lg:col-span-3 space-y-6">
                  <FormField
                    control={form.control}
                    name="xCard"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center">
                          <FormLabel>X Card Type</FormLabel>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 ml-2 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent className="w-80">
                                <p className="font-medium mb-1">X Card Types:</p>
                                <ul className="list-disc pl-4 text-xs space-y-1">
                                  <li><strong>Summary Card:</strong> Compact card with small image (120×120px)</li>
                                  <li><strong>Summary Card with Large Image:</strong> Larger image preview (1.91:1)</li>
                                  <li><strong>App Card:</strong> Special format for mobile apps</li>
                                  <li><strong>Player Card:</strong> Embeds video/audio players</li>
                                </ul>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                        <Select 
                          onValueChange={field.onChange} 
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a card type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="SUMMARY">Summary Card</SelectItem>
                            <SelectItem value="SUMMARY_LARGE_IMAGE">Summary Card with Large Image</SelectItem>
                            <SelectItem value="APP">App Card</SelectItem>
                            <SelectItem value="PLAYER">Player Card</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          {field.value === "SUMMARY" && "Summary Card: Compact format with small square image, ideal for text-focused content"}
                          {field.value === "SUMMARY_LARGE_IMAGE" && "Summary Card with Large Image: Features a large image, ideal for visual content"}
                          {field.value === "APP" && "App Card: Special format for mobile applications with app store links"}
                          {field.value === "PLAYER" && "Player Card: Embeds video or audio players directly in the X timeline"}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="xTitle"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>X Title</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter X title" {...field} />
                        </FormControl>
                        <div className="flex justify-between text-xs text-muted-foreground mt-1">
                          <span>Recommended: 40-60 characters</span>
                          <span className={field.value.length > 70 ? "text-red-600 dark:text-red-400 font-medium" : ""}>
                            {field.value.length}/70
                          </span>
                        </div>
                        <FormDescription>
                          The title that appears when shared on X
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="xDescription"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>X Description</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter X description"
                            className="resize-none"
                            {...field}
                          />
                        </FormControl>
                        <div className="flex justify-between text-xs text-muted-foreground mt-1">
                          <span>Recommended: 100-200 characters</span>
                          <span className={field.value.length > 200 ? "text-red-600 dark:text-red-400 font-medium" : ""}>
                            {field.value.length}/200
                          </span>
                        </div>
                        <FormDescription>
                          The description that appears when shared on X
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="xImage"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>X Image</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter image URL" {...field} />
                        </FormControl>
                        <FormDescription>
                          {form.watch("xCard") === "SUMMARY" && "Image size: 120×120 pixels (1:1 square ratio)"}
                          {form.watch("xCard") === "SUMMARY_LARGE_IMAGE" && "Image size: 1200×675 pixels (1.91:1 ratio)"}
                          {form.watch("xCard") === "APP" && "Image size: 800×800 pixels (1:1 square ratio)"}
                          {form.watch("xCard") === "PLAYER" && "Player card thumbnail (16:9 ratio recommended)"}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* APP Card specific fields */}
                  {form.watch("xCard") === "APP" && (
                    <div className="space-y-6 border-t pt-6">
                      <h4 className="font-medium">App Card Settings</h4>
                      
                      <FormField
                        control={form.control}
                        name="xAppName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>App Name</FormLabel>
                            <FormControl>
                              <Input placeholder="Enter app name" {...field} value={field.value || ""} />
                            </FormControl>
                            <FormDescription>
                              The name of your application (required for App Cards)
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="xAppCountry"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>App Country</FormLabel>
                            <FormControl>
                              <Input placeholder="US" {...field} value={field.value || ""} />
                            </FormControl>
                            <FormDescription>
                              Two-letter country code where your app is available (e.g., US, GB, JP)
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="xAppIdIphone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>iPhone App ID</FormLabel>
                            <FormControl>
                              <Input placeholder="123456789" {...field} value={field.value || ""} />
                            </FormControl>
                            <FormDescription>
                              Your app&apos;s numeric ID in the App Store
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="xAppIdIpad"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>iPad App ID</FormLabel>
                            <FormControl>
                              <Input placeholder="123456789" {...field} value={field.value || ""} />
                            </FormControl>
                            <FormDescription>
                              Your app&apos;s iPad-specific ID (if different from iPhone)
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="xAppIdGoogleplay"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Google Play App ID</FormLabel>
                            <FormControl>
                              <Input placeholder="com.example.app" {...field} value={field.value || ""} />
                            </FormControl>
                            <FormDescription>
                              Your app&apos;s package name in Google Play
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  )}

                  {/* PLAYER Card specific fields */}
                  {form.watch("xCard") === "PLAYER" && (
                    <div className="space-y-6 border-t pt-6">
                      <h4 className="font-medium">Player Card Settings</h4>
                      
                      <FormField
                        control={form.control}
                        name="xPlayerUrl"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Player URL</FormLabel>
                            <FormControl>
                              <Input placeholder="https://example.com/player" {...field} value={field.value || ""} />
                            </FormControl>
                            <FormDescription>
                              HTTPS URL to your player iframe (must be SSL)
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="grid grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="xPlayerWidth"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Player Width</FormLabel>
                              <FormControl>
                                <Input placeholder="1280" {...field} value={field.value || ""} />
                              </FormControl>
                              <FormDescription>
                                Width in pixels (144-4096)
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="xPlayerHeight"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Player Height</FormLabel>
                              <FormControl>
                                <Input placeholder="720" {...field} value={field.value || ""} />
                              </FormControl>
                              <FormDescription>
                                Height in pixels (144-4096)
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <FormField
                        control={form.control}
                        name="xPlayerStream"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                            <div className="space-y-0.5">
                              <FormLabel>Stream Content</FormLabel>
                              <FormDescription>
                                Enable for live streaming content
                              </FormDescription>
                            </div>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </div>
                  )}
                </div>
                
                {/* X Preview - Right side */}
                <div className="lg:col-span-2">
                  <Card>
                    <CardContent className="p-4">
                      <div className="space-y-2">
                        <h3 className="text-sm font-medium text-muted-foreground">X Card Preview</h3>
                        <SocialPreview 
                          platform="x"
                          title={xTitle}
                          description={xDescription}
                          image={xImage}
                          url={canonicalUrl || "https://example.com/page"}
                          cardType={xCard.toLowerCase() as "summary" | "summary_large_image"}
                        />
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Optimization Tips (Collapsible) */}
          <Collapsible
            open={tipsExpanded}
            onOpenChange={setTipsExpanded}
            className="border rounded-md"
          >
            <CollapsibleTrigger asChild>
              <Button variant="ghost" className="flex items-center w-full justify-between p-4">
                <div className="flex items-center">
                  <Info className="h-4 w-4 mr-2 text-primary" />
                  <span className="font-medium">Social Media Optimization Tips</span>
                </div>
                {tipsExpanded ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <div className="p-4 pt-0 bg-primary/5 text-primary rounded-b-md">
                <ul className="list-disc pl-4 space-y-1 text-sm">
                  <li>Use high-quality images (1200×630px for Facebook, 1200×675px for X)</li>
                  <li>Include your brand name in titles for better recognition</li>
                  <li>Keep descriptions concise and compelling to increase click-through rates</li>
                  <li>Test your social cards regularly using the validator tools</li>
                </ul>
              </div>
            </CollapsibleContent>
          </Collapsible>

        </form>
      </Form>
    </div>
  );
}
