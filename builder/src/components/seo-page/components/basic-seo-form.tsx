"use client";

import { useState, useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { PageSEOData } from "../types";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { X } from "lucide-react";
import SERPPreview from "./serp-preview";
import { Separator } from "@/components/ui/separator";
import { usePageSeo, useUpdateBasicSeo } from "@/lib/hooks/useSeo";
import { toast } from "@/components/ui/use-toast";
import { Skeleton } from "@/components/ui/skeleton";

const basicSeoFormSchema = z.object({
  title: z
    .string()
    .min(10, { message: "Title must be at least 10 characters" })
    .max(60, { message: "Title should not exceed 60 characters" }),
  useCustomTitle: z.boolean().default(true),
  description: z
    .string()
    .min(50, { message: "Description must be at least 50 characters" })
    .max(160, { message: "Description should not exceed 160 characters" }),
  keywords: z.array(z.string()).optional(),
});

type BasicSeoFormValues = z.infer<typeof basicSeoFormSchema>;

interface BasicSEOFormProps {
  pageId: string;
  onSaveSuccess?: () => void;
}

export default function BasicSEOForm({ pageId, onSaveSuccess }: BasicSEOFormProps) {
  const [keywords, setKeywords] = useState<string[]>([]);
  const [keywordInput, setKeywordInput] = useState("");
  const [title, setTitle] = useState<string>("");
  const [description, setDescription] = useState<string>("");
  const [isSaving, setIsSaving] = useState(false);

  // Fetch page SEO data
  const { data: seoData, isLoading, error } = usePageSeo(pageId);
  
  // Update basic SEO mutation
  const updateBasicSeo = useUpdateBasicSeo();

  // Initialize form with data when it's loaded
  useEffect(() => {
    if (seoData) {
      setKeywords(seoData.keywords || []);
      setTitle(seoData.title || "");
      setDescription(seoData.description || "");
      
      form.reset({
        title: seoData.title || "",
        useCustomTitle: seoData.useCustomTitle ?? true,
        description: seoData.description || "",
        keywords: seoData.keywords || [],
      });
    }
  }, [seoData]);

  const form = useForm<BasicSeoFormValues>({
    resolver: zodResolver(basicSeoFormSchema),
    defaultValues: {
      title: "",
      useCustomTitle: true,
      description: "",
      keywords: [],
    },
  });

  const addKeyword = () => {
    if (keywordInput.trim() && !keywords.includes(keywordInput.trim())) {
      const newKeywords = [...keywords, keywordInput.trim()];
      setKeywords(newKeywords);
      form.setValue("keywords", newKeywords);
      setKeywordInput("");
    }
  };

  const removeKeyword = (keywordToRemove: string) => {
    const newKeywords = keywords.filter(keyword => keyword !== keywordToRemove);
    setKeywords(newKeywords);
    form.setValue("keywords", newKeywords);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addKeyword();
    }
  };

  const onSubmit = async (values: BasicSeoFormValues) => {
    setIsSaving(true);
    try {
      await updateBasicSeo.mutateAsync({
        pageId,
        data: {
          title: values.title,
          useCustomTitle: values.useCustomTitle,
          description: values.description,
          keywords: keywords,
        }
      });
      
      toast({
        title: "Success",
        description: "Basic SEO settings have been updated",
        variant: "default",
      });
      
      if (onSaveSuccess) {
        onSaveSuccess();
      }
    } catch (error) {
      console.error("Error updating basic SEO:", error);
      toast({
        title: "Error",
        description: "Failed to update basic SEO settings",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Update preview when form values change
  useEffect(() => {
    const subscription = form.watch((value) => {
      if (value.title !== undefined) setTitle(value.title);
      if (value.description !== undefined) setDescription(value.description);
    });
    return () => subscription.unsubscribe();
  }, [form.watch]);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-24 w-full" />
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-16 w-full" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 border border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950/20 text-red-700 dark:text-red-300 rounded-md">
        <h3 className="font-medium">Error loading SEO data</h3>
        <p className="text-sm mt-1">Please try refreshing the page</p>
      </div>
    );
  }

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">Basic SEO Settings</h2>
      <SERPPreview title={title} description={description} url={seoData?.canonicalUrl || `https://example.com/page`} />
      
      <Separator className="my-6" />
      
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} id="basic-seo-form" className="space-y-6">
          <FormField
            control={form.control}
            name="title"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Page Title</FormLabel>
                <FormControl>
                  <Input 
                    placeholder="Enter page title" 
                    {...field} 
                    onChange={(e) => {
                      field.onChange(e);
                      setTitle(e.target.value);
                    }}
                  />
                </FormControl>
                <FormDescription>
                  The title that appears in search engine results ({field.value.length}/60 characters)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="useCustomTitle"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">Use Custom Title</FormLabel>
                  <FormDescription>
                    Override the website-level title template
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Meta Description</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Enter meta description"
                    className="resize-none"
                    {...field}
                    onChange={(e) => {
                      field.onChange(e);
                      setDescription(e.target.value);
                    }}
                  />
                </FormControl>
                <FormDescription>
                  A brief description of the page content ({field.value.length}/160 characters)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="space-y-2">
            <FormLabel>Keywords</FormLabel>
            <div className="flex gap-2">
              <Input
                value={keywordInput}
                onChange={(e) => setKeywordInput(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Add a keyword and press Enter"
              />
              <Button type="button" onClick={addKeyword}>
                Add
              </Button>
            </div>
            <FormDescription>
              Keywords related to your page content (press Enter to add)
            </FormDescription>

            <div className="flex flex-wrap gap-2 mt-3">
              {keywords.map((keyword) => (
                <Badge key={keyword} variant="secondary" className="px-3 py-1">
                  {keyword}
                  <X
                    className="ml-2 h-3 w-3 cursor-pointer"
                    onClick={() => removeKeyword(keyword)}
                  />
                </Badge>
              ))}
            </div>
          </div>

        </form>
      </Form>
    </div>
  );
}
