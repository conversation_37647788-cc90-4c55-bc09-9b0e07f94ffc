"use client";

import { PageSEOData } from "../types";
import { SEOPerformanceCard } from "./seo-performance-card";
import { SEOOptimizationGuide } from "./seo-optimization-guide";
import { PriorityTasks } from "./priority-tasks";
import { SEOChecklist } from "./seo-checklist";
import { SEOSummary } from "./seo-summary";

interface PageSEOOverviewProps {
  seoData: PageSEOData;
  seoScores: {
    overall: number;
    categories: {
      content: number;
      technical: number;
      onPage: number;
      offPage: number;
      user: number;
    };
    sections: {
      basic: number;
      structure: number;
      social: number;
      structured: number;
      advanced: number;
    };
    issues: {
      critical: number;
      warnings: number;
      passed: number;
    };
  };
  pageId: string;
  siteId: string;
  onTabChange?: (tab: string) => void;
}

export default function PageSEOOverview({ seoData, seoScores, pageId, siteId, onTabChange }: PageSEOOverviewProps) {
  // Calculate completed tasks
  const totalTasks = 8; // Total number of SEO optimization tasks
  const completedTasks = [
    (seoData?.title?.length ?? 0) > 0,
    (seoData?.description?.length ?? 0) > 0,
    (seoData?.keywords?.length ?? 0) > 0,
    (seoData?.contentAnalysis?.headings?.h1?.length ?? 0) > 0,
    (seoData?.ogTitle?.length ?? 0) > 0 || (seoData?.ogDescription?.length ?? 0) > 0 || (seoData?.ogImage?.length ?? 0) > 0,
    (seoData?.canonicalUrl?.length ?? 0) > 0,
    seoData?.robots?.index !== undefined,
    (seoData?.schemaType?.length ?? 0) > 0
  ].filter(Boolean).length;

  // Determine next priority task
  const getNextPriorityTask = () => {
    if (!seoData?.title || (seoData.title?.length ?? 0) === 0) {
      return {
        task: "Add a compelling page title",
        description: "A good title improves click-through rates and SEO rankings.",
        tab: "basic"
      };
    }
    if (!seoData?.description || (seoData.description?.length ?? 0) === 0) {
      return {
        task: "Add a meta description",
        description: "Meta descriptions appear in search results and affect click-through rates.",
        tab: "basic"
      };
    }
    if (!seoData?.contentAnalysis?.headings?.h1 || seoData?.contentAnalysis?.headings?.h1.length === 0) {
      return {
        task: "Optimize content structure",
        description: "Implement proper HTML hierarchy with H1 heading for better SEO and accessibility.",
        tab: "structure"
      };
    }
    if (!seoData?.ogTitle && !seoData?.ogImage) {
      return {
        task: "Set up social media sharing",
        description: "Optimize how your page appears when shared on social platforms.",
        tab: "social"
      };
    }
    if (!seoData?.canonicalUrl) {
      return {
        task: "Add a canonical URL",
        description: "Prevent duplicate content issues with a canonical URL.",
        tab: "advanced"
      };
    }
    return {
      task: "Review your SEO settings",
      description: "Your page has basic SEO elements. Review and optimize further.",
      tab: "basic"
    };
  };

  const nextTask = getNextPriorityTask();
  
  // 使用 window.location 直接修改 URL 查询参数
  const handleTabChange = (tab: string) => {
    if (onTabChange) {
      onTabChange(tab);
    } else {
      const url = new URL(window.location.href);
      url.searchParams.set('seo', tab);
      window.location.href = url.toString();
    }
  };

  return (
    <div className="space-y-6">
      {/* SEO Performance Analysis */}
      <SEOPerformanceCard seoData={seoData} seoScores={seoScores} />

      {/* SEO Optimization Guide */}
      <SEOOptimizationGuide 
        seoData={seoData}
        completedTasks={completedTasks}
        totalTasks={totalTasks}
        nextTask={nextTask}
        pageId={pageId}
        siteId={siteId}
        onTabChange={handleTabChange}
      />

      {/* Priority Tasks & SEO Checklist */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Priority Tasks */}
        <PriorityTasks 
          seoData={seoData}
          completedTasks={completedTasks}
          totalTasks={totalTasks}
          nextTask={nextTask}
          onTabChange={handleTabChange}
        />
        
        {/* SEO Checklist */}
        <SEOChecklist seoData={seoData} />
      </div>

      {/* Page SEO Summary */}
      <SEOSummary seoData={seoData} />
    </div>
  );
}
