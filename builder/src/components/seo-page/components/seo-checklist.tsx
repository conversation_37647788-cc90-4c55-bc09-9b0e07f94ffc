"use client";

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CheckCircle2, Check, Circle } from "lucide-react";
import { PageSEOData } from "../types";

interface SEOChecklistProps {
  seoData: PageSEOData;
}

export function SEOChecklist({ seoData }: SEOChecklistProps) {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-2">
          <CheckCircle2 className="h-5 w-5 text-primary" />
          <div>
            <CardTitle>SEO Checklist</CardTitle>
            <CardDescription>Track your SEO optimization progress</CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        {/* Page Title */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {(seoData?.title?.length ?? 0) > 0 ? (
              <Check className="h-4 w-4 text-green-600 dark:text-green-400" />
            ) : (
              <Circle className="h-4 w-4 text-muted-foreground/50" />
            )}
            <span className="text-sm">Page Title</span>
          </div>
          {(seoData?.title?.length ?? 0) > 0 ? (
            <Badge variant="outline" className="bg-green-50 dark:bg-green-950/20 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800">Complete</Badge>
          ) : (
            <Badge variant="outline" className="bg-amber-50 dark:bg-amber-950/20 text-amber-700 dark:text-amber-300 border-amber-200 dark:border-amber-800">Required</Badge>
          )}
        </div>

        {/* Meta Description */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {(seoData?.description?.length ?? 0) > 0 ? (
              <Check className="h-4 w-4 text-green-600 dark:text-green-400" />
            ) : (
              <Circle className="h-4 w-4 text-muted-foreground/50" />
            )}
            <span className="text-sm">Meta Description</span>
          </div>
          {(seoData?.description?.length ?? 0) > 0 ? (
            <Badge variant="outline" className="bg-green-50 dark:bg-green-950/20 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800">Complete</Badge>
          ) : (
            <Badge variant="outline" className="bg-amber-50 dark:bg-amber-950/20 text-amber-700 dark:text-amber-300 border-amber-200 dark:border-amber-800">Required</Badge>
          )}
        </div>

        {/* H1 Heading */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {(seoData?.contentAnalysis?.headings?.h1?.length ?? 0) > 0 ? (
              <Check className="h-4 w-4 text-green-600 dark:text-green-400" />
            ) : (
              <Circle className="h-4 w-4 text-muted-foreground/50" />
            )}
            <span className="text-sm">H1 Heading</span>
          </div>
          {(seoData?.contentAnalysis?.headings?.h1?.length ?? 0) > 0 ? (
            <Badge variant="outline" className="bg-green-50 dark:bg-green-950/20 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800">Complete</Badge>
          ) : (
            <Badge variant="outline" className="bg-amber-50 dark:bg-amber-950/20 text-amber-700 dark:text-amber-300 border-amber-200 dark:border-amber-800">Required</Badge>
          )}
        </div>

        {/* Keywords */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {(seoData?.keywords?.length ?? 0) > 0 ? (
              <Check className="h-4 w-4 text-green-600 dark:text-green-400" />
            ) : (
              <Circle className="h-4 w-4 text-muted-foreground/50" />
            )}
            <span className="text-sm">Keywords</span>
          </div>
          {(seoData?.keywords?.length ?? 0) > 0 ? (
            <Badge variant="outline" className="bg-green-50 dark:bg-green-950/20 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800">Complete</Badge>
          ) : (
            <Badge variant="outline" className="bg-muted/50 text-muted-foreground border-muted">Recommended</Badge>
          )}
        </div>

        {/* Open Graph */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {(seoData?.ogTitle?.length ?? 0) > 0 || (seoData?.ogImage?.length ?? 0) > 0 ? (
              <Check className="h-4 w-4 text-green-600 dark:text-green-400" />
            ) : (
              <Circle className="h-4 w-4 text-muted-foreground/50" />
            )}
            <span className="text-sm">Open Graph Tags</span>
          </div>
          {(seoData?.ogTitle?.length ?? 0) > 0 || (seoData?.ogImage?.length ?? 0) > 0 ? (
            <Badge variant="outline" className="bg-green-50 dark:bg-green-950/20 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800">Complete</Badge>
          ) : (
            <Badge variant="outline" className="bg-muted/50 text-muted-foreground border-muted">Recommended</Badge>
          )}
        </div>

        {/* Canonical URL */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {(seoData?.canonicalUrl?.length ?? 0) > 0 ? (
              <Check className="h-4 w-4 text-green-600 dark:text-green-400" />
            ) : (
              <Circle className="h-4 w-4 text-muted-foreground/50" />
            )}
            <span className="text-sm">Canonical URL</span>
          </div>
          {(seoData?.canonicalUrl?.length ?? 0) > 0 ? (
            <Badge variant="outline" className="bg-green-50 dark:bg-green-950/20 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800">Complete</Badge>
          ) : (
            <Badge variant="outline" className="bg-muted/50 text-muted-foreground border-muted">Recommended</Badge>
          )}
        </div>

        {/* Robots Meta */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {seoData?.robots?.index !== undefined ? (
              <Check className="h-4 w-4 text-green-600 dark:text-green-400" />
            ) : (
              <Circle className="h-4 w-4 text-muted-foreground/50" />
            )}
            <span className="text-sm">Robots Directives</span>
          </div>
          {seoData?.robots?.index !== undefined ? (
            <Badge variant="outline" className="bg-green-50 dark:bg-green-950/20 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800">Complete</Badge>
          ) : (
            <Badge variant="outline" className="bg-muted/50 text-muted-foreground border-muted">Recommended</Badge>
          )}
        </div>

        {/* Structured Data */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {(seoData?.schemaType?.length ?? 0) > 0 ? (
              <Check className="h-4 w-4 text-green-600 dark:text-green-400" />
            ) : (
              <Circle className="h-4 w-4 text-muted-foreground/50" />
            )}
            <span className="text-sm">Structured Data</span>
          </div>
          {(seoData?.schemaType?.length ?? 0) > 0 ? (
            <Badge variant="outline" className="bg-green-50 dark:bg-green-950/20 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800">Complete</Badge>
          ) : (
            <Badge variant="outline" className="bg-muted/50 text-muted-foreground border-muted">Recommended</Badge>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
