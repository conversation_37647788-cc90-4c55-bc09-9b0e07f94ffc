"use client";

import { useState, useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFieldArray } from "react-hook-form";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from "@/components/ui/card";
import { 
  Check, 
  Copy, 
  RefreshCw, 
  AlertCircle, 
  ExternalLink, 
  CheckCircle,
  Plus,
  Trash,
  Loader2 
} from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { 
  <PERSON><PERSON><PERSON>,
  <PERSON>lt<PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>rovider,
  TooltipTrigger 
} from "@/components/ui/tooltip";
import { useToast } from "@/components/ui/use-toast";
import { usePageSeo, useUpdatePageStructuredData } from "@/lib/hooks/useSeo";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { ChevronDown } from "lucide-react";

// 添加一个通用的表单类型
interface StructuredDataFormValues {
  schemaType: string;
  name: string;
  description: string;
  // Article specific
  headline?: string;
  author?: string;
  datePublished?: string;
  image?: string;
  // Product specific
  brand?: string;
  price?: string;
  priceCurrency?: string;
  // Service specific
  provider?: string;
  areaServed?: string;
  serviceType?: string;
  // Event specific
  startDate?: string;
  endDate?: string;
  location?: string;
  // Local Business specific
  address?: string;
  telephone?: string;
  priceRange?: string;
  // FAQ specific
  questions?: { question: string; answer: string }[];
}

// Dynamic schema based on schema type
const getSchemaValidator = (schemaType: string) => {
  const baseSchema = {
    schemaType: z.string(),
    name: z.string().min(1, "Name is required"),
    description: z.string().min(1, "Description is required"),
  };

  switch (schemaType) {
    case "ARTICLE":
      return z.object({
        ...baseSchema,
        headline: z.string().min(1, "Headline is required"),
        author: z.string().min(1, "Author is required"),
        datePublished: z.string().optional(),
        image: z.string().optional(),
      });
    case "PRODUCT":
      return z.object({
        ...baseSchema,
        brand: z.string().min(1, "Brand is required"),
        price: z.string().min(1, "Price is required"),
        priceCurrency: z.string().min(1, "Currency is required"),
      });
    case "SERVICE":
      return z.object({
        ...baseSchema,
        provider: z.string().optional(),
        areaServed: z.string().optional(),
        serviceType: z.string().optional(),
      });
    case "EVENT":
      return z.object({
        ...baseSchema,
        startDate: z.string().min(1, "Start date is required"),
        endDate: z.string().optional(),
        location: z.string().min(1, "Location is required"),
      });
    case "LOCALBUSINESS":
      return z.object({
        ...baseSchema,
        address: z.string().min(1, "Address is required"),
        telephone: z.string().min(1, "Telephone is required"),
        priceRange: z.string().optional(),
      });
    case "FAQ":
      return z.object({
        ...baseSchema,
        questions: z.array(z.object({
          question: z.string().min(1, "Question is required"),
          answer: z.string().min(1, "Answer is required"),
        })).optional().default([]),
      });
    case "HOWTO":
      return z.object(baseSchema);
    case "ORGANIZATION":
      return z.object(baseSchema);
    case "PERSON":
      return z.object(baseSchema);
    case "WEBSITE":
      return z.object(baseSchema);
    case "RECIPE":
      return z.object(baseSchema);
    case "COURSE":
      return z.object(baseSchema);
    case "REVIEW":
      return z.object(baseSchema);
    case "BREADCRUMB":
      return z.object(baseSchema);
    case "CUSTOM":
      return z.object(baseSchema);
    default:
      return z.object(baseSchema);
  }
};

interface StructuredDataFormProps {
  pageId: string;
  onSaveSuccess?: () => void;
}

export default function StructuredDataForm({ pageId, onSaveSuccess }: StructuredDataFormProps) {
  const { toast } = useToast();
  const { data: seoData, isLoading, error } = usePageSeo(pageId);
  const updateStructuredData = useUpdatePageStructuredData();
  
  const [schemaType, setSchemaType] = useState("ARTICLE");
  const [copied, setCopied] = useState(false);
  const [jsonPreview, setJsonPreview] = useState("");
  const [isSaving, setIsSaving] = useState(false);
  
  const schemaTypeOptions = [
    { value: "ARTICLE", label: "Article" },
    { value: "PRODUCT", label: "Product" },
    { value: "FAQ", label: "FAQ Page" },
    { value: "HOWTO", label: "How-To Guide" },
    { value: "EVENT", label: "Event" },
    { value: "ORGANIZATION", label: "Organization" },
    { value: "PERSON", label: "Person" },
    { value: "WEBSITE", label: "Web Page" },
    { value: "LOCALBUSINESS", label: "Local Business" },
    { value: "RECIPE", label: "Recipe" },
    { value: "COURSE", label: "Course" },
    { value: "REVIEW", label: "Review" },
    { value: "BREADCRUMB", label: "Breadcrumb" },
    { value: "CUSTOM", label: "Custom Schema" },
  ];

  const form = useForm<StructuredDataFormValues>({
    resolver: zodResolver(getSchemaValidator(schemaType)),
    defaultValues: {
      schemaType: "ARTICLE",
      name: "",
      description: "",
      headline: "",
      author: "",
      datePublished: new Date().toISOString().split("T")[0],
      image: "",
      brand: "",
      price: "",
      priceCurrency: "USD",
      provider: "",
      areaServed: "",
      serviceType: "",
      startDate: new Date().toISOString().split("T")[0],
      endDate: "",
      location: "",
      address: "",
      telephone: "",
      priceRange: "$",
      questions: [{ question: "", answer: "" }],
    },
  });

  // 将 useFieldArray 移到组件顶层，避免条件渲染中使用 Hooks
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "questions",
  });

  // 初始化表单数据
  useEffect(() => {
    if (seoData) {
      console.log('SEO Data loaded:', seoData);
      
      // 确保使用正确的 Schema Type
      const currentSchemaType = seoData.schemaType || "ARTICLE";
      console.log('Current Schema Type:', currentSchemaType);
      
      // 设置 Schema Type 状态（这会触发另一个 useEffect）
      setSchemaType(currentSchemaType);
      
      // 从 schemaData 中提取数据
      const schemaData = seoData.schemaData || {};
      console.log('Schema Data:', schemaData);
      
      // 处理 FAQ 类型的 questions 数据
      let questionsData = [{ question: "", answer: "" }];
      if (currentSchemaType === "FAQ" && schemaData.mainEntity && Array.isArray(schemaData.mainEntity)) {
        questionsData = schemaData.mainEntity.map((item: { name?: string; acceptedAnswer?: { text?: string } }) => ({
          question: item.name || "",
          answer: item.acceptedAnswer?.text || ""
        }));
        
        // 如果没有问题数据，添加一个空的问题
        if (questionsData.length === 0) {
          questionsData = [{ question: "", answer: "" }];
        }
        
        console.log('FAQ Questions Data:', questionsData);
      }
      
      // 重置表单值
      const formValues = {
        schemaType: currentSchemaType,
        name: schemaData.name || seoData.title || "",
        description: schemaData.description || seoData.description || "",
        headline: schemaData.headline || seoData.title || "",
        author: schemaData.author?.name || "",
        datePublished: schemaData.datePublished || new Date().toISOString().split("T")[0],
        image: schemaData.image || "",
        brand: schemaData.brand?.name || "",
        price: schemaData.offers?.price || "",
        priceCurrency: schemaData.offers?.priceCurrency || "USD",
        provider: schemaData.provider?.name || "",
        areaServed: schemaData.areaServed || "",
        serviceType: schemaData.serviceType || "",
        startDate: schemaData.startDate || new Date().toISOString().split("T")[0],
        endDate: schemaData.endDate || "",
        location: schemaData.location?.name || "",
        address: schemaData.address?.streetAddress || "",
        telephone: schemaData.telephone || "",
        priceRange: schemaData.priceRange || "$",
        questions: questionsData,
      };
      
      console.log('Form reset values:', formValues);
      
      // 延迟重置表单，确保 Schema Type 状态已更新
      setTimeout(() => {
        form.reset(formValues);
      }, 100);
    }
  }, [seoData, form]);
  
  // 当 Schema Type 变化时，仅记录日志
  useEffect(() => {
    console.log('Schema Type changed:', schemaType);
  }, [schemaType]);

  // 将枚举类型转换为 schema.org 类型名称
  const getSchemaOrgType = (schemaType: string): string => {
    const typeMap: Record<string, string> = {
      "ARTICLE": "Article",
      "PRODUCT": "Product",
      "FAQ": "FAQPage",
      "HOWTO": "HowTo",
      "EVENT": "Event",
      "ORGANIZATION": "Organization",
      "PERSON": "Person",
      "WEBSITE": "WebPage",
      "LOCALBUSINESS": "LocalBusiness",
      "RECIPE": "Recipe",
      "COURSE": "Course",
      "REVIEW": "Review",
      "BREADCRUMB": "BreadcrumbList",
      "CUSTOM": "Thing"
    };
    
    return typeMap[schemaType] || schemaType;
  };

  const generateJsonLd = () => {
    const values = form.getValues();
    let jsonLd: any = {
      "@context": "https://schema.org",
      "@type": getSchemaOrgType(values.schemaType),
      "name": values.name,
      "description": values.description,
    };

    switch (values.schemaType) {
      case "ARTICLE":
        jsonLd = {
          ...jsonLd,
          "headline": values.headline,
          "author": {
            "@type": "Person",
            "name": values.author
          },
          "datePublished": values.datePublished,
        };
        if (values.image) {
          jsonLd.image = values.image;
        }
        break;
      case "PRODUCT":
        jsonLd = {
          ...jsonLd,
          "brand": {
            "@type": "Brand",
            "name": values.brand
          },
          "offers": {
            "@type": "Offer",
            "price": values.price,
            "priceCurrency": values.priceCurrency
          }
        };
        break;
      case "SERVICE":
        jsonLd = {
          ...jsonLd,
        };
        if (values.provider) {
          jsonLd.provider = {
            "@type": "Organization",
            "name": values.provider
          };
        }
        if (values.areaServed) {
          jsonLd.areaServed = values.areaServed;
        }
        if (values.serviceType) {
          jsonLd.serviceType = values.serviceType;
        }
        break;
      case "EVENT":
        jsonLd = {
          ...jsonLd,
          "startDate": values.startDate,
          "location": {
            "@type": "Place",
            "name": values.location
          }
        };
        if (values.endDate) {
          jsonLd.endDate = values.endDate;
        }
        break;
      case "LOCALBUSINESS":
        jsonLd = {
          ...jsonLd,
          "address": {
            "@type": "PostalAddress",
            "streetAddress": values.address
          },
          "telephone": values.telephone
        };
        if (values.priceRange) {
          jsonLd.priceRange = values.priceRange;
        }
        break;
      case "FAQ":
        // 确保 questions 是数组，即使为空也能正确处理
        const questions = Array.isArray(values.questions) ? values.questions : [];
        // 过滤掉空的问题和答案
        const validQuestions = questions.filter(q => q.question.trim() && q.answer.trim());
        
        jsonLd = {
          ...jsonLd,
          "@type": "FAQPage",
          "mainEntity": validQuestions.map(q => ({
            "@type": "Question",
            "name": q.question,
            "acceptedAnswer": {
              "@type": "Answer",
              "text": q.answer
            }
          }))
        };
        break;
    }

    return JSON.stringify(jsonLd, null, 2);
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(generateJsonLd());
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  // 当表单值变化时更新 JSON 预览
  useEffect(() => {
    const subscription = form.watch(() => {
      setJsonPreview(generateJsonLd());
    });
    
    // 初始化 JSON 预览
    setJsonPreview(generateJsonLd());
    
    return () => subscription.unsubscribe();
  }, [form.watch, schemaType, form]);

  const onSubmit = async (data: StructuredDataFormValues) => {
    setIsSaving(true);
    
    try {
      // 生成 JSON-LD
      const jsonLd = JSON.parse(generateJsonLd());
      
      // 准备提交数据
      const submitData = {
        schemaType: data.schemaType as "ARTICLE" | "PRODUCT" | "EVENT" | "LOCALBUSINESS" | "ORGANIZATION" | "PERSON" | "FAQ" | "HOWTO" | "WEBSITE" | "RECIPE" | "COURSE" | "REVIEW" | "BREADCRUMB" | "CUSTOM",
        schemaData: jsonLd
      };
      
      // 调用 API
      await updateStructuredData.mutateAsync({
        pageId,
        data: submitData
      });
      
      // 显示成功消息
      toast({
        title: "Structured data saved",
        description: "Your schema markup has been successfully updated.",
      });
      
      // 调用成功回调
      if (onSaveSuccess) {
        onSaveSuccess();
      }
    } catch (error) {
      console.error("Error saving structured data:", error);
      toast({
        title: "Error saving structured data",
        description: "There was a problem updating your schema markup. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const renderSchemaFields = () => {
    switch (schemaType) {
      case "ARTICLE":
        return (
          <>
            <FormField
              control={form.control}
              name="headline"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Headline</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter headline" {...field} />
                  </FormControl>
                  <FormDescription>
                    The headline of the article
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="author"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Author</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter author name" {...field} />
                  </FormControl>
                  <FormDescription>
                    The author of the article
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="datePublished"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Publication Date</FormLabel>
                  <FormControl>
                    <Input type="date" {...field} />
                  </FormControl>
                  <FormDescription>
                    The date the article was published
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="image"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Image URL</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter image URL" {...field} />
                  </FormControl>
                  <FormDescription>
                    URL of an image associated with the article
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </>
        );
        
      case "PRODUCT":
        return (
          <>
            <FormField
              control={form.control}
              name="brand"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Brand</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter brand name" {...field} />
                  </FormControl>
                  <FormDescription>
                    The brand of the product
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="price"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Price</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter price" {...field} />
                  </FormControl>
                  <FormDescription>
                    The price of the product (numeric value only)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="priceCurrency"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Currency</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select currency" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="USD">USD ($)</SelectItem>
                      <SelectItem value="EUR">EUR (€)</SelectItem>
                      <SelectItem value="GBP">GBP (£)</SelectItem>
                      <SelectItem value="CNY">CNY (¥)</SelectItem>
                      <SelectItem value="JPY">JPY (¥)</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    The currency of the price
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </>
        );
        
      case "SERVICE":
        return (
          <>
            <FormField
              control={form.control}
              name="provider"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Provider</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter provider name" {...field} />
                  </FormControl>
                  <FormDescription>
                    The organization providing the service
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="areaServed"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Area Served</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter area served" {...field} />
                  </FormControl>
                  <FormDescription>
                    The geographic area where the service is provided
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="serviceType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Service Type</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter service type" {...field} />
                  </FormControl>
                  <FormDescription>
                    The type of service being offered
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </>
        );
        
      case "EVENT":
        return (
          <>
            <FormField
              control={form.control}
              name="startDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Start Date</FormLabel>
                  <FormControl>
                    <Input type="date" {...field} />
                  </FormControl>
                  <FormDescription>
                    The start date of the event
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="endDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>End Date</FormLabel>
                  <FormControl>
                    <Input type="date" {...field} />
                  </FormControl>
                  <FormDescription>
                    The end date of the event (optional)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="location"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Location</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter event location" {...field} />
                  </FormControl>
                  <FormDescription>
                    The location where the event will take place
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </>
        );
        
      case "LOCALBUSINESS":
        return (
          <>
            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Address</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter business address" {...field} />
                  </FormControl>
                  <FormDescription>
                    The street address of the business
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="telephone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Telephone</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter telephone number" {...field} />
                  </FormControl>
                  <FormDescription>
                    The telephone number of the business
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="priceRange"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Price Range</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select price range" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="$">$ (Inexpensive)</SelectItem>
                      <SelectItem value="$$">$$ (Moderate)</SelectItem>
                      <SelectItem value="$$$">$$$ (Expensive)</SelectItem>
                      <SelectItem value="$$$$">$$$$ (Very Expensive)</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    The price range of the business
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </>
        );
        
      case "FAQ":
        return (
          <div className="space-y-4">
            <div className="flex justify-between items-center mb-2">
              <h4 className="text-sm font-medium">FAQ Questions</h4>
              <Button 
                type="button" 
                variant="outline" 
                size="sm" 
                onClick={() => append({ question: "", answer: "" })}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Question
              </Button>
            </div>
            
            {fields.map((field, index) => (
              <Card key={field.id} className="p-3">
                <div className="flex justify-between items-center mb-2">
                  <h4 className="text-sm font-medium">Question {index + 1}</h4>
                  <Button 
                    type="button" 
                    variant="ghost" 
                    size="sm" 
                    onClick={() => remove(index)}
                  >
                    <Trash className="h-4 w-4" />
                  </Button>
                </div>
                
                <div className="space-y-3">
                  <FormField
                    control={form.control}
                    name={`questions.${index}.question`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Question</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter question" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name={`questions.${index}.answer`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Answer</FormLabel>
                        <FormControl>
                          <Textarea placeholder="Enter answer" {...field} rows={3} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </Card>
            ))}
            
            {fields.length === 0 && (
              <div className="text-center p-4 border border-dashed rounded-md">
                <p className="text-sm text-muted-foreground">No questions added yet.</p>
                <Button 
                  type="button" 
                  variant="outline" 
                  size="sm" 
                  className="mt-2"
                  onClick={() => append({ question: "", answer: "" })}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add First Question
                </Button>
              </div>
            )}
          </div>
        );
        
      default:
        return null;
    }
  };

  // 加载中状态
  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2 text-lg">Loading structured data...</span>
      </div>
    );
  }

  // 错误状态
  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error loading structured data</AlertTitle>
        <AlertDescription>
          There was a problem loading the structured data. Please refresh the page or try again later.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div>
      <div className="grid grid-cols-1 lg:grid-cols-5 gap-6">
        <div className="lg:col-span-3">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} id="structured-data-form" className="space-y-6">
              {/* Schema 类型选择卡片 */}
              <Card>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">Schema Type</CardTitle>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => window.open(`https://schema.org/${getSchemaOrgType(schemaType)}`, '_blank')}
                          >
                            <ExternalLink className="h-4 w-4 mr-2" />
                            Schema.org Docs
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>View official Schema.org documentation</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </CardHeader>
                <CardContent>
                  <FormField
                    control={form.control}
                    name="schemaType"
                    render={({ field }) => (
                      <FormItem>
                        <Select
                          onValueChange={(value) => {
                            field.onChange(value);
                            setSchemaType(value);
                          }}
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a schema type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {schemaTypeOptions.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          The type of structured data to use for this page
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              {/* 基本信息卡片 */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">Basic Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter name" {...field} />
                        </FormControl>
                        <FormDescription>
                          The name of the {schemaType.toLowerCase()}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter description"
                            className="resize-none"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          A description of the {schemaType.toLowerCase()}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
              
              {/* 特定类型字段卡片 */}
              {renderSchemaFields() && (
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg">{getSchemaOrgType(schemaType)} Specific Fields</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {renderSchemaFields()}
                  </CardContent>
                </Card>
              )}
            </form>
          </Form>
        </div>
        
        <div className="lg:col-span-2">
          <div className="sticky top-4 space-y-4">
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">JSON-LD Preview</CardTitle>
                  <div className="flex items-center gap-2">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button 
                            size="sm"
                            variant="outline"
                            onClick={() => window.open('https://validator.schema.org/', '_blank')}
                            className="h-8 px-2"
                          >
                            <CheckCircle className="h-3.5 w-3.5 mr-1" />
                            <span className="text-xs">Validate</span>
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Validate with Schema.org Validator</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={copyToClipboard}
                      className="h-8 px-2"
                    >
                      {copied ? (
                        <>
                          <Check className="h-3.5 w-3.5 mr-1" />
                          <span className="text-xs">Copied!</span>
                        </>
                      ) : (
                        <>
                          <Copy className="h-3.5 w-3.5 mr-1" />
                          <span className="text-xs">Copy</span>
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-0">
                <div className="bg-muted/30 rounded-md overflow-auto max-h-[500px]">
                  <pre className="text-xs p-4 whitespace-pre-wrap">
                    {jsonPreview}
                  </pre>
                </div>
              </CardContent>
              <CardFooter className="text-xs text-muted-foreground pt-3">
                <p>This JSON-LD code will be added to your page&apos;s HTML to help search engines understand your content.</p>
              </CardFooter>
            </Card>
            
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Testing Tools</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="w-full justify-start"
                  onClick={() => window.open('https://search.google.com/test/rich-results', '_blank')}
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Google Rich Results Test
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="w-full justify-start"
                  onClick={() => window.open('https://validator.schema.org/', '_blank')}
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Schema.org Validator
                </Button>
              </CardContent>
            </Card>
            
            <Collapsible className="w-full">
              <Card>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-sm">Tips & Best Practices</CardTitle>
                    <CollapsibleTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <ChevronDown className="h-4 w-4" />
                      </Button>
                    </CollapsibleTrigger>
                  </div>
                </CardHeader>
                <CollapsibleContent>
                  <CardContent className="text-xs space-y-2">
                    <p>• Use specific schema types that match your content</p>
                    <p>• Include all required properties for your schema type</p>
                    <p>• Test your structured data with Google&apos;s Rich Results Test</p>
                    <p>• Keep your structured data up to date with your content</p>
                  </CardContent>
                </CollapsibleContent>
              </Card>
            </Collapsible>
          </div>
        </div>
      </div>
    </div>
  );
}
