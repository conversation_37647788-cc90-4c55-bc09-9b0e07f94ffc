"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { Plus, X, Loader2, AlertCircle } from "lucide-react";
import { useState, useEffect } from "react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useToast } from "@/components/ui/use-toast";
import { usePageSeo, useUpdateAdvancedSeo } from "@/lib/hooks/useSeo";

const advancedSeoFormSchema = z.object({
  canonicalUrl: z.string().url({ message: "Please enter a valid URL" }).optional().nullable(),
  robots: z.object({
    index: z.boolean().default(true),
    follow: z.boolean().default(true),
    noarchive: z.boolean().default(false),
    nosnippet: z.boolean().default(false),
    noimageindex: z.boolean().default(false),
    nocache: z.boolean().default(false),
  }),
  priority: z.number().min(0).max(1).optional(),
  changeFrequency: z.enum(["ALWAYS", "HOURLY", "DAILY", "WEEKLY", "MONTHLY", "YEARLY", "NEVER"]).optional(),
});

type AdvancedSeoFormValues = z.infer<typeof advancedSeoFormSchema>;

interface AdvancedSEOFormProps {
  pageId: string;
  onSaveSuccess?: () => void;
}

export default function AdvancedSEOForm({ pageId, onSaveSuccess }: AdvancedSEOFormProps) {
  const { toast } = useToast();
  const { data: seoData, isLoading, error } = usePageSeo(pageId);
  const updateAdvancedSeo = useUpdateAdvancedSeo();
  
  const [hreflangLinks, setHreflangLinks] = useState<{language: string, url: string}[]>([]);
  const [newLanguage, setNewLanguage] = useState("");
  const [newUrl, setNewUrl] = useState("");
  const [isSaving, setIsSaving] = useState(false);

  const form = useForm<AdvancedSeoFormValues>({
    resolver: zodResolver(advancedSeoFormSchema),
    defaultValues: {
      canonicalUrl: "",
      robots: {
        index: true,
        follow: true,
        noarchive: false,
        nosnippet: false,
        noimageindex: false,
        nocache: false,
      },
      priority: 0.5,
      changeFrequency: "MONTHLY",
    },
  });

  // 初始化表单数据
  useEffect(() => {
    if (seoData) {
      form.reset({
        canonicalUrl: seoData.canonicalUrl || "",
        robots: {
          index: seoData.robots?.index ?? true,
          follow: seoData.robots?.follow ?? true,
          noarchive: seoData.robots?.noarchive ?? false,
          nosnippet: seoData.robots?.nosnippet ?? false,
          noimageindex: seoData.robots?.noimageindex ?? false,
          nocache: seoData.robots?.nocache ?? false,
        },
        priority: seoData.priority || 0.5,
        changeFrequency: seoData.changeFrequency || "MONTHLY",
      });
      
      // 初始化 hreflang 链接
      if (seoData.hreflangLinks && Array.isArray(seoData.hreflangLinks)) {
        setHreflangLinks(seoData.hreflangLinks);
      } else {
        setHreflangLinks([]);
      }
    }
  }, [seoData, form]);

  async function onSubmit(data: AdvancedSeoFormValues) {
    setIsSaving(true);
    
    try {
      // 准备提交数据
      const submitData = {
        canonicalUrl: data.canonicalUrl || undefined,
        robots: data.robots,
        hreflangLinks: hreflangLinks.length > 0 ? hreflangLinks : undefined,
        priority: data.priority,
        changeFrequency: data.changeFrequency,
      };
      
      // 调用 API
      await updateAdvancedSeo.mutateAsync({
        pageId,
        data: submitData
      });
      
      // 显示成功消息
      toast({
        title: "Advanced SEO settings saved",
        description: "Your advanced SEO settings have been successfully updated.",
      });
      
      // 调用成功回调
      if (onSaveSuccess) {
        onSaveSuccess();
      }
    } catch (error) {
      console.error("Error saving advanced SEO settings:", error);
      toast({
        title: "Error saving advanced SEO settings",
        description: "There was a problem updating your advanced SEO settings. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  }

  const addHreflangLink = () => {
    if (newLanguage && newUrl) {
      setHreflangLinks([...hreflangLinks, { language: newLanguage, url: newUrl }]);
      setNewLanguage("");
      setNewUrl("");
    }
  };

  const removeHreflangLink = (index: number) => {
    setHreflangLinks(hreflangLinks.filter((_, i) => i !== index));
  };

  // Generate robots.txt content based on form values
  const robotsContent = () => {
    const robots = form.watch("robots");
    let directives = [];
    
    if (!robots.index) directives.push("noindex");
    else directives.push("index");
    
    if (!robots.follow) directives.push("nofollow");
    else directives.push("follow");
    
    if (robots.noarchive) directives.push("noarchive");
    if (robots.nosnippet) directives.push("nosnippet");
    if (robots.noimageindex) directives.push("noimageindex");
    if (robots.nocache) directives.push("nocache");
    
    return directives.join(", ");
  };

  // 加载中状态
  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2 text-lg">Loading advanced SEO settings...</span>
      </div>
    );
  }

  // 错误状态
  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error loading advanced SEO settings</AlertTitle>
        <AlertDescription>
          There was a problem loading the advanced SEO settings. Please refresh the page or try again later.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-8">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} id="advanced-seo-form" className="space-y-6">
          <Accordion type="single" collapsible defaultValue="canonical" className="w-full">
            <AccordionItem value="canonical">
              <AccordionTrigger>Canonical URL</AccordionTrigger>
              <AccordionContent>
                          <div className="space-y-4">
            <div className="p-3 border rounded-lg bg-primary/5 text-primary border-primary/20">
              <h4 className="text-sm font-medium mb-1">What is a canonical URL?</h4>
                    <p className="text-sm">
                      A canonical URL tells search engines which version of a page is the primary one, helping prevent duplicate content issues and consolidating ranking signals.
                    </p>
                  </div>
                  
                  <FormField
                    control={form.control}
                    name="canonicalUrl"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center justify-between">
                          <FormLabel>Canonical URL</FormLabel>
                          <Button 
                            type="button" 
                            variant="outline" 
                            size="sm" 
                            className="h-7 text-xs"
                            onClick={() => field.onChange(window.location.origin + window.location.pathname)}
                          >
                            Use Current URL
                          </Button>
                        </div>
                        <FormControl>
                          <Input placeholder="https://example.com/page" {...field} value={field.value || ""} />
                        </FormControl>
                        <div className="flex flex-col gap-2 mt-2">
                          <div className="flex items-center gap-2">
                            <Button 
                              type="button" 
                              variant="ghost" 
                              size="sm" 
                              className="h-6 text-xs px-2 border"
                              onClick={() => {
                                // 使用当前页面 URL 作为自引用规范链接
                                const currentUrl = window.location.origin + window.location.pathname;
                                field.onChange(currentUrl);
                              }}
                            >
                              Self-reference (recommended)
                            </Button>
                            <Button 
                              type="button" 
                              variant="ghost" 
                              size="sm" 
                              className="h-6 text-xs px-2 border"
                              onClick={() => field.onChange("")}
                            >
                              Clear
                            </Button>
                          </div>
                          {field.value && (
                            <code className="text-xs p-2 bg-slate-100 rounded border overflow-x-auto">
                              &lt;link rel=&quot;canonical&quot; href=&quot;{field.value}&quot; /&gt;
                            </code>
                          )}
                        </div>
                        <FormDescription className="mt-2">
                          Common uses: Prevent duplicate content, consolidate similar pages, manage parameter-based URLs
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </AccordionContent>
            </AccordionItem>
            
            <AccordionItem value="robots">
              <AccordionTrigger>Robots Directives</AccordionTrigger>
              <AccordionContent>
                <div className="space-y-4">
                              <div className="p-3 border rounded-lg bg-primary/5 text-primary border-primary/20">
              <h4 className="text-sm font-medium mb-1">Meta Robots vs. robots.txt</h4>
                    <p className="text-sm">
                      Meta robots tags control how search engines handle this specific page, while robots.txt controls site-wide crawler access. Meta robots are more precise and take precedence for individual pages.
                    </p>
                  </div>
                  
                  {/* Primary Controls - Most Common Options */}
                  <div className="border rounded-lg p-4 bg-background">
                    <h3 className="text-sm font-medium mb-3">Essential Controls</h3>
                    <div className="space-y-3">
                      <FormField
                        control={form.control}
                        name="robots.index"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 bg-muted/30">
                            <div className="space-y-0.5">
                              <div className="flex items-center gap-1">
                                <FormLabel className="text-base font-medium">Index</FormLabel>
                                {!field.value && <Badge variant="destructive" className="ml-2 text-[10px] h-5">NOINDEX</Badge>}
                              </div>
                              <FormDescription>
                                {field.value 
                                  ? "Search engines will index this page (recommended)" 
                                  : "Search engines will NOT index this page (use with caution)"}
                              </FormDescription>
                            </div>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="robots.follow"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 bg-muted/30">
                            <div className="space-y-0.5">
                              <div className="flex items-center gap-1">
                                <FormLabel className="text-base font-medium">Follow</FormLabel>
                                {!field.value && <Badge variant="destructive" className="ml-2 text-[10px] h-5">NOFOLLOW</Badge>}
                              </div>
                              <FormDescription>
                                {field.value 
                                  ? "Search engines will follow links on this page (recommended)" 
                                  : "Search engines will NOT follow links on this page"}
                              </FormDescription>
                            </div>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                  
                  {/* Advanced Controls - Collapsible */}
                  <div className="border rounded-lg p-4 bg-background">
                    <Accordion type="single" collapsible className="w-full">
                      <AccordionItem value="advanced-robots">
                        <AccordionTrigger className="text-sm font-medium py-0">
                          Advanced Options
                        </AccordionTrigger>
                        <AccordionContent className="pt-3">
                          <div className="space-y-3">
                            <FormField
                              control={form.control}
                              name="robots.noarchive"
                              render={({ field }) => (
                                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                                  <div className="space-y-0.5">
                                    <FormLabel className="text-base">No Archive</FormLabel>
                                    <FormDescription>
                                      Prevent search engines from storing cached versions
                                    </FormDescription>
                                  </div>
                                  <FormControl>
                                    <Switch
                                      checked={field.value}
                                      onCheckedChange={field.onChange}
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />
                            
                            <FormField
                              control={form.control}
                              name="robots.nosnippet"
                              render={({ field }) => (
                                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                                  <div className="space-y-0.5">
                                    <FormLabel className="text-base">No Snippet</FormLabel>
                                    <FormDescription>
                                      Prevent search engines from showing snippets in results
                                    </FormDescription>
                                  </div>
                                  <FormControl>
                                    <Switch
                                      checked={field.value}
                                      onCheckedChange={field.onChange}
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />
                            
                            <FormField
                              control={form.control}
                              name="robots.noimageindex"
                              render={({ field }) => (
                                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                                  <div className="space-y-0.5">
                                    <FormLabel className="text-base">No Image Index</FormLabel>
                                    <FormDescription>
                                      Prevent search engines from indexing images on this page
                                    </FormDescription>
                                  </div>
                                  <FormControl>
                                    <Switch
                                      checked={field.value}
                                      onCheckedChange={field.onChange}
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />
                            
                            <FormField
                              control={form.control}
                              name="robots.nocache"
                              render={({ field }) => (
                                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                                  <div className="space-y-0.5">
                                    <FormLabel className="text-base">No Cache</FormLabel>
                                    <FormDescription>
                                      Instruct browsers not to cache this page
                                    </FormDescription>
                                  </div>
                                  <FormControl>
                                    <Switch
                                      checked={field.value}
                                      onCheckedChange={field.onChange}
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    </Accordion>
                  </div>
                  
                  {/* Common Use Cases */}
                  <div className="border rounded-lg p-4 bg-background">
                    <h3 className="text-sm font-medium mb-2">Common Use Cases</h3>
                    <div className="grid grid-cols-2 gap-2">
                      <Button 
                        type="button" 
                        variant="outline" 
                        size="sm" 
                        className="justify-start h-auto py-1 px-2"
                        onClick={() => {
                          form.setValue("robots.index", true);
                          form.setValue("robots.follow", true);
                          form.setValue("robots.noarchive", false);
                          form.setValue("robots.nosnippet", false);
                          form.setValue("robots.noimageindex", false);
                          form.setValue("robots.nocache", false);
                        }}
                      >
                        <div className="text-left text-xs">
                          <div className="font-medium">Normal Indexing</div>
                          <div className="text-muted-foreground text-[10px]">index, follow</div>
                        </div>
                      </Button>
                      
                      <Button 
                        type="button" 
                        variant="outline" 
                        size="sm" 
                        className="justify-start h-auto py-1 px-2"
                        onClick={() => {
                          form.setValue("robots.index", false);
                          form.setValue("robots.follow", true);
                          form.setValue("robots.noarchive", false);
                          form.setValue("robots.nosnippet", false);
                          form.setValue("robots.noimageindex", false);
                          form.setValue("robots.nocache", false);
                        }}
                      >
                        <div className="text-left text-xs">
                          <div className="font-medium">Prevent Indexing</div>
                          <div className="text-muted-foreground text-[10px]">noindex, follow</div>
                        </div>
                      </Button>
                      
                      <Button 
                        type="button" 
                        variant="outline" 
                        size="sm" 
                        className="justify-start h-auto py-1 px-2"
                        onClick={() => {
                          form.setValue("robots.index", true);
                          form.setValue("robots.follow", true);
                          form.setValue("robots.noarchive", true);
                          form.setValue("robots.nosnippet", false);
                          form.setValue("robots.noimageindex", false);
                          form.setValue("robots.nocache", false);
                        }}
                      >
                        <div className="text-left text-xs">
                          <div className="font-medium">No Cache</div>
                          <div className="text-muted-foreground text-[10px]">index, follow, noarchive</div>
                        </div>
                      </Button>
                      
                      <Button 
                        type="button" 
                        variant="outline" 
                        size="sm" 
                        className="justify-start h-auto py-1 px-2"
                        onClick={() => {
                          form.setValue("robots.index", false);
                          form.setValue("robots.follow", false);
                          form.setValue("robots.noarchive", false);
                          form.setValue("robots.nosnippet", false);
                          form.setValue("robots.noimageindex", false);
                          form.setValue("robots.nocache", false);
                        }}
                      >
                        <div className="text-left text-xs">
                          <div className="font-medium">Complete Privacy</div>
                          <div className="text-muted-foreground text-[10px]">noindex, nofollow</div>
                        </div>
                      </Button>
                    </div>
                  </div>
                  
                  {/* Preview */}
                  <div className="p-3 border rounded-lg bg-muted/30">
                    <h4 className="text-sm font-medium mb-2">Generated Meta Robots Tag</h4>
                    <code className="text-xs block overflow-x-auto whitespace-nowrap">
                      &lt;meta name=&quot;robots&quot; content=&quot;{robotsContent()}&quot; /&gt;
                    </code>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
            
            <AccordionItem value="sitemap">
              <AccordionTrigger>Page Importance</AccordionTrigger>
              <AccordionContent>
                <div className="space-y-4">
                  <div className="mb-4 p-3 border rounded-lg bg-primary/5 text-primary border-primary/20">
                    <p className="text-sm">
                      Sitemap exclusions are managed in site-level SEO settings. This ensures consistency and prevents conflicts.
                    </p>
                  </div>
                  
                  <FormField
                    control={form.control}
                    name="priority"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Page Importance</FormLabel>
                        <Select 
                          onValueChange={(value) => field.onChange(parseFloat(value))}
                          defaultValue={field.value?.toString()}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select importance level" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="1.0">Critical (1.0)</SelectItem>
                            <SelectItem value="0.8">High (0.8)</SelectItem>
                            <SelectItem value="0.5">Medium (0.5)</SelectItem>
                            <SelectItem value="0.3">Low (0.3)</SelectItem>
                            <SelectItem value="0.1">Very Low (0.1)</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Sets this page&apos;s relative importance in your sitemap
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="changeFrequency"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Update Frequency</FormLabel>
                        <Select 
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select update frequency" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="ALWAYS">Always</SelectItem>
                            <SelectItem value="HOURLY">Hourly</SelectItem>
                            <SelectItem value="DAILY">Daily</SelectItem>
                            <SelectItem value="WEEKLY">Weekly</SelectItem>
                            <SelectItem value="MONTHLY">Monthly</SelectItem>
                            <SelectItem value="YEARLY">Yearly</SelectItem>
                            <SelectItem value="NEVER">Never</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          How frequently this page is updated
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </AccordionContent>
            </AccordionItem>
            
            <AccordionItem value="hreflang">
              <AccordionTrigger>Multilingual Settings (hreflang)</AccordionTrigger>
              <AccordionContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Alternate Language Pages</h4>
                    <p className="text-sm text-muted-foreground">
                      Specify URLs for different language versions of this page
                    </p>
                  </div>
                  
                  <div className="space-y-4">
                    {hreflangLinks.map((link, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <Badge variant="outline" className="min-w-[60px] justify-center">
                          {link.language}
                        </Badge>
                        <span className="text-sm truncate flex-1">{link.url}</span>
                        <Button 
                          type="button" 
                          variant="ghost" 
                          size="icon" 
                          onClick={() => removeHreflangLink(index)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                  
                  <div className="flex items-end gap-2">
                    <div className="w-24">
                      <FormLabel className="text-xs">Language Code</FormLabel>
                      <Input 
                        placeholder="en" 
                        value={newLanguage} 
                        onChange={(e) => setNewLanguage(e.target.value)} 
                        className="h-9"
                      />
                    </div>
                    <div className="flex-1">
                      <FormLabel className="text-xs">URL</FormLabel>
                      <Input 
                        placeholder="https://example.com/page" 
                        value={newUrl} 
                        onChange={(e) => setNewUrl(e.target.value)} 
                        className="h-9"
                      />
                    </div>
                    <Button 
                      type="button" 
                      variant="outline" 
                      size="sm" 
                      onClick={addHreflangLink}
                      className="h-9"
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      Add
                    </Button>
                  </div>
                  
                  <div className="mt-4 p-3 border rounded-lg bg-muted/30">
                    <h4 className="text-sm font-medium mb-2">Generated hreflang Tags</h4>
                    <div className="space-y-1">
                      {hreflangLinks.map((link, index) => (
                        <code key={index} className="text-xs block">
                          &lt;link rel=&quot;alternate&quot; hreflang=&quot;{link.language}&quot; href=&quot;{link.url}&quot; /&gt;
                        </code>
                      ))}
                    </div>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </form>
      </Form>
    </div>
  );
}
