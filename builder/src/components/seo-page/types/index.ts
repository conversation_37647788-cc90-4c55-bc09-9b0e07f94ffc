// 匹配服务端 PageSEO 模型的前端类型定义
export interface PageSEOData {
  id?: string;
  pageId?: string;
  
  // 基本 SEO
  title?: string;
  useCustomTitle?: boolean;
  description?: string;
  keywords?: string[];
  
  // 社交媒体
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  xTitle?: string;
  xDescription?: string;
  xImage?: string;
  xCardType?: 'SUMMARY' | 'SUMMARY_LARGE_IMAGE' | 'APP' | 'PLAYER';
  
  // 结构化数据
  schemaType?: 'ARTICLE' | 'PRODUCT' | 'FAQ' | 'HOWTO' | 'EVENT' | 'ORGANIZATION' | 'PERSON' | 'WEBSITE' | 'LOCALBUSINESS' | 'RECIPE' | 'COURSE' | 'REVIEW' | 'BREADCRUMB' | 'CUSTOM';
  schemaData?: any;
  
  // 高级设置
  canonicalUrl?: string;
  robots?: {
    index?: boolean;
    follow?: boolean;
    noarchive?: boolean;
    nosnippet?: boolean;
    noimageindex?: boolean;
    nocache?: boolean;
    maxSnippet?: number;
    maxImagePreview?: string;
    maxVideoPreview?: number;
  };
  hreflangLinks?: Array<{
    language: string;
    url: string;
  }>;
  priority?: number;
  changeFrequency?: 'ALWAYS' | 'HOURLY' | 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'YEARLY' | 'NEVER';
  
  // 页面内容分析
  contentAnalysis?: {
    headings?: {
      h1?: string[];
      h2?: string[];
      h3?: string[];
      h4?: string[];
      h5?: string[];
      h6?: string[];
    };
    images?: Array<{
      url: string;
      alt: string;
      width?: number;
      height?: number;
    }>;
    wordCount?: number;
    readabilityScore?: number;
    seoScore?: number;
    issues?: {
      critical?: Array<{
        id: string;
        message: string;
        impact: string;
        recommendation: string;
      }>;
      warnings?: Array<{
        id: string;
        message: string;
        impact: string;
        recommendation: string;
      }>;
      passed?: Array<{
        id: string;
        message: string;
      }>;
    };
  };
  
  // 继承设置
  inheritFromSite?: boolean;
  websiteSeoId?: string;
  websiteSeo?: any;
  
  createdAt?: string;
  updatedAt?: string;
}
