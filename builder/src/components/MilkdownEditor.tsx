'use client';

// 直接导出组件
export { default } from './MilkdownEditor/MilkdownEditor';

// 导出类型和工具函数
export type {
  EditorMode,
  MilkdownEditorProps,
  CrepeEditorProps,
  MarkdownEditorProps,
  ModeToggleProps,
  EditorRef
} from './MilkdownEditor/types';

export {
  getDefaultMarkdown,
  getUserPreferredMode,
  saveUserPreferredMode,
  detectThemeMode,
  watchThemeChange,
  formatShortcut,
  debounce
} from './MilkdownEditor/utils';