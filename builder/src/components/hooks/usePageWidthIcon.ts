import { useMemo, useState, useEffect, useCallback } from 'react';
import { IconSizeConfig, ResponsiveIconConfig } from './useResponsiveIcon';

// 页面宽度模式定义
export type PageWidthMode = 'normal' | 'wide' | 'full';

// 页面宽度感知的图标配置
export interface PageWidthIconConfig {
  normal?: IconSizeConfig;
  wide?: IconSizeConfig;
  full?: IconSizeConfig;
}

// 检测当前页面宽度模式
export const usePageWidthMode = (): PageWidthMode => {
  const [pageWidthMode, setPageWidthMode] = useState<PageWidthMode>('normal');

  const detectPageWidthMode = useCallback(() => {
    const htmlElement = document.documentElement;
    if (htmlElement.classList.contains('page-width-full')) {
      setPageWidthMode('full');
    } else if (htmlElement.classList.contains('page-width-wide')) {
      setPageWidthMode('wide');
    } else {
      setPageWidthMode('normal');
    }
  }, []);

  useEffect(() => {
    // 初始检测
    detectPageWidthMode();

    // 监听 HTML 类名变化
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
          detectPageWidthMode();
        }
      });
    });

    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    });

    return () => observer.disconnect();
  }, [detectPageWidthMode]);

  return pageWidthMode;
};

// 页面宽度感知的图标 Hook
export const usePageWidthIcon = (config: PageWidthIconConfig): IconSizeConfig => {
  const pageWidthMode = usePageWidthMode();
  
  return useMemo(() => {
    // 默认配置
    const defaultConfig: IconSizeConfig = { 
      container: 48, 
      icon: 28, 
      stroke: 2 
    };
    
    // 按页面宽度模式选择配置
    return config[pageWidthMode] || 
           config.normal || 
           defaultConfig;
  }, [pageWidthMode, config]);
};

// 根据页面宽度的预设图标配置
export const PAGE_WIDTH_PRESETS = {
  // 导航图标 - 根据页面宽度适配
  navigation: {
    normal: { container: 44, icon: 26, stroke: 2 },
    wide: { container: 48, icon: 28, stroke: 2 },
    full: { container: 52, icon: 30, stroke: 2 }
  },
  
  // 功能卡片图标 - 随页面宽度放大
  feature: {
    normal: { container: 64, icon: 36, stroke: 2 },
    wide: { container: 72, icon: 40, stroke: 2 },
    full: { container: 80, icon: 44, stroke: 2.5 }
  },
  
  // 内联图标 - 跟随文字大小
  inline: {
    normal: { container: 20, icon: 16, stroke: 1.5 },
    wide: { container: 22, icon: 17, stroke: 1.5 },
    full: { container: 24, icon: 18, stroke: 1.5 }
  },
  
  // 列表图标 - 适度缩放
  list: {
    normal: { container: 40, icon: 24, stroke: 2 },
    wide: { container: 44, icon: 26, stroke: 2 },
    full: { container: 48, icon: 28, stroke: 2 }
  },
  
  // 英雄区域图标 - 大幅度缩放
  hero: {
    normal: { container: 96, icon: 52, stroke: 2.5 },
    wide: { container: 112, icon: 60, stroke: 2.5 },
    full: { container: 128, icon: 68, stroke: 3 }
  },
  
  // 按钮图标 - 跟随按钮尺寸系统
  button: {
    normal: { container: 18, icon: 16, stroke: 1.5 }, // 对应 --button-medium-icon-size: 1rem (16px)
    wide: { container: 20, icon: 18, stroke: 1.5 },   // 对应 --button-medium-icon-size: 1.125rem (18px)
    full: { container: 22, icon: 20, stroke: 1.5 }    // 对应 --button-medium-icon-size: 1.25rem (20px)
  }
} as const;

// 使用页面宽度预设的便捷 Hook
export const usePageWidthPresetIcon = (preset: keyof typeof PAGE_WIDTH_PRESETS): IconSizeConfig => {
  return usePageWidthIcon(PAGE_WIDTH_PRESETS[preset]);
};

// 根据页面宽度计算图标缩放比例
export const getPageWidthScale = (mode: PageWidthMode): number => {
  switch (mode) {
    case 'normal': return 1.0;
    case 'wide': return 1.125; // 12.5% 增大
    case 'full': return 1.25;  // 25% 增大
    default: return 1.0;
  }
};

// 获取当前页面的字体大小比例（用于内联图标）
export const useTextSizeScale = (): number => {
  const pageWidthMode = usePageWidthMode();
  
  return useMemo(() => {
    // 基于页面宽度模式的文字大小比例
    switch (pageWidthMode) {
      case 'normal': return 1.0;    // body-base-size: 1rem (16px)
      case 'wide': return 1.1875;   // body-base-size: 1.1875rem (19px)
      case 'full': return 1.25;     // body-base-size: 1.25rem (20px)
      default: return 1.0;
    }
  }, [pageWidthMode]);
};

// 智能内联图标尺寸（跟随文字大小）
export const useInlineIconSize = (baseSize = 16): IconSizeConfig => {
  const textScale = useTextSizeScale();
  
  return useMemo(() => {
    const scaledSize = Math.round(baseSize * textScale);
    return {
      container: scaledSize,
      icon: Math.round(scaledSize * 0.8), // 图标略小于容器
      stroke: 1.5
    };
  }, [baseSize, textScale]);
}; 