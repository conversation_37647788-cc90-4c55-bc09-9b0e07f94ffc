import { useMemo, useState, useEffect } from 'react';

// 响应式断点定义
export type Breakpoint = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';

// 图标尺寸配置接口
export interface IconSizeConfig {
  container: number;
  icon: number;
  stroke?: number;
}

// 响应式图标配置
export interface ResponsiveIconConfig {
  xs?: IconSizeConfig;
  sm?: IconSizeConfig;
  md?: IconSizeConfig;
  lg?: IconSizeConfig;
  xl?: IconSizeConfig;
  '2xl'?: IconSizeConfig;
}

// 获取当前断点
export const useBreakpoint = (): Breakpoint => {
  const [breakpoint, setBreakpoint] = useState<Breakpoint>('lg');

  useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth;
      if (width < 640) setBreakpoint('xs');
      else if (width < 768) setBreakpoint('sm');
      else if (width < 1024) setBreakpoint('md');
      else if (width < 1280) setBreakpoint('lg');
      else if (width < 1536) setBreakpoint('xl');
      else setBreakpoint('2xl');
    };

    updateBreakpoint();
    window.addEventListener('resize', updateBreakpoint);
    return () => window.removeEventListener('resize', updateBreakpoint);
  }, []);

  return breakpoint;
};

// 响应式图标 Hook
export const useResponsiveIcon = (config: ResponsiveIconConfig): IconSizeConfig => {
  const breakpoint = useBreakpoint();
  
  return useMemo(() => {
    // 默认配置
    const defaultConfig: IconSizeConfig = { 
      container: 48, 
      icon: 28, 
      stroke: 2 
    };
    
    // 按优先级查找匹配的配置
    return config[breakpoint] || 
           config.lg || 
           config.md || 
           config.sm || 
           config.xs || 
           defaultConfig;
  }, [breakpoint, config]);
};

// 预设的响应式配置
export const RESPONSIVE_PRESETS = {
  // 导航图标 - 保持触摸友好
  navigation: {
    xs: { container: 40, icon: 24, stroke: 1.5 },
    sm: { container: 44, icon: 26, stroke: 1.5 },
    md: { container: 48, icon: 28, stroke: 2 },
    lg: { container: 48, icon: 28, stroke: 2 }, // 桌面端不需要太大
    xl: { container: 48, icon: 28, stroke: 2 },
    '2xl': { container: 48, icon: 28, stroke: 2 }
  },
  
  // 功能卡片图标 - 随屏幕缩放
  feature: {
    xs: { container: 48, icon: 28, stroke: 1.5 },
    sm: { container: 56, icon: 32, stroke: 2 },
    md: { container: 64, icon: 36, stroke: 2 },
    lg: { container: 72, icon: 40, stroke: 2 },
    xl: { container: 80, icon: 44, stroke: 2.5 },
    '2xl': { container: 88, icon: 48, stroke: 2.5 }
  },
  
  // 内联图标 - 固定小尺寸
  inline: {
    xs: { container: 16, icon: 12, stroke: 1.5 },
    sm: { container: 18, icon: 14, stroke: 1.5 },
    md: { container: 20, icon: 16, stroke: 1.5 },
    lg: { container: 20, icon: 16, stroke: 1.5 },
    xl: { container: 22, icon: 18, stroke: 1.5 },
    '2xl': { container: 24, icon: 20, stroke: 1.5 }
  },
  
  // 列表图标 - 中等尺寸
  list: {
    xs: { container: 32, icon: 20, stroke: 1.5 },
    sm: { container: 36, icon: 22, stroke: 1.5 },
    md: { container: 40, icon: 24, stroke: 2 },
    lg: { container: 44, icon: 26, stroke: 2 },
    xl: { container: 48, icon: 28, stroke: 2 },
    '2xl': { container: 52, icon: 30, stroke: 2 }
  },
  
  // 大型展示图标 - 适合英雄区域
  hero: {
    xs: { container: 64, icon: 36, stroke: 2 },
    sm: { container: 80, icon: 44, stroke: 2 },
    md: { container: 96, icon: 52, stroke: 2.5 },
    lg: { container: 112, icon: 60, stroke: 2.5 },
    xl: { container: 128, icon: 68, stroke: 3 },
    '2xl': { container: 144, icon: 76, stroke: 3 }
  }
} as const;

// 计算最优图标尺寸的工具函数
export const calculateOptimalIconSize = (containerSize: number, ratio = 0.65): number => {
  return Math.round(containerSize * ratio);
};

// 获取最小触摸目标尺寸
export const getMinTouchTarget = (containerSize: number): number => {
  const MIN_TOUCH_SIZE = 44; // Apple HIG 推荐
  return Math.max(containerSize, MIN_TOUCH_SIZE);
};

// 网格对齐工具
export const alignToGrid = (size: number, gridUnit = 4): number => {
  return Math.round(size / gridUnit) * gridUnit;
};

// 暗色模式尺寸调整
export const adjustForDarkMode = (size: number, isDark: boolean, factor = 1.05): number => {
  return isDark ? Math.round(size * factor) : size;
};

// 使用预设配置的便捷 Hook
export const usePresetIcon = (preset: keyof typeof RESPONSIVE_PRESETS): IconSizeConfig => {
  return useResponsiveIcon(RESPONSIVE_PRESETS[preset]);
};

// 获取视觉权重对应的尺寸
export const getVisualWeight = (weight: 'primary' | 'secondary' | 'tertiary'): ResponsiveIconConfig => {
  switch (weight) {
    case 'primary':
      return RESPONSIVE_PRESETS.feature;
    case 'secondary':
      return RESPONSIVE_PRESETS.list;
    case 'tertiary':
      return RESPONSIVE_PRESETS.inline;
    default:
      return RESPONSIVE_PRESETS.list;
  }
}; 