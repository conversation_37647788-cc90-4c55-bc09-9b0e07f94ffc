'use client';

import React from 'react';
import { Eye, Code } from 'lucide-react';
import { ModeToggleProps } from './types';

const ModeToggle: React.FC<ModeToggleProps> = ({
  mode,
  onModeChange,
  disabled = false
}) => {
  return (
    <div className="inline-flex items-center bg-muted/50 rounded-md p-0.5">
      <button
        onClick={() => !disabled && onModeChange('wysiwyg')}
        disabled={disabled}
        className={`
          inline-flex items-center gap-1.5 px-2.5 py-1 rounded text-xs font-medium transition-all duration-200
          ${mode === 'wysiwyg' 
            ? 'bg-background text-foreground shadow-sm border border-border/50' 
            : 'text-muted-foreground hover:text-foreground hover:bg-background/50'
          }
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        `}
        title="Visual Editor"
      >
        <Eye className="w-3.5 h-3.5" />
        <span>Visual</span>
      </button>

      <button
        onClick={() => !disabled && onModeChange('markdown')}
        disabled={disabled}
        className={`
          inline-flex items-center gap-1.5 px-2.5 py-1 rounded text-xs font-medium transition-all duration-200
          ${mode === 'markdown' 
            ? 'bg-background text-foreground shadow-sm border border-border/50' 
            : 'text-muted-foreground hover:text-foreground hover:bg-background/50'
          }
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        `}
        title="Source Editor"
      >
        <Code className="w-3.5 h-3.5" />
        <span>Source</span>
      </button>
    </div>
  );
};

export default ModeToggle;