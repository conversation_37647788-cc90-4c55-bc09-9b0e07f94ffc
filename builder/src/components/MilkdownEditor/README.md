# MilkdownEditor Component

A powerful Markdown editor that supports WYSIWYG and source mode switching.

## Features

- ✅ **Dual Mode Support**: WYSIWYG visual editing + Markdown source editing
- ✅ **Real-time Sync**: Real-time content synchronization between two modes
- ✅ **Shortcut Support**: Ctrl+M to toggle mode, Ctrl+S to save
- ✅ **Auto Save**: Configurable debounced auto-save
- ✅ **Theme Adaptation**: Automatic light/dark theme adaptation
- ✅ **Error Boundary**: Comprehensive error handling mechanism
- ✅ **TypeScript**: Complete type support
- ✅ **Controlled/Uncontrolled**: Support for both usage modes

## Basic Usage

```tsx
import MilkdownEditor from '@/components/MilkdownEditor';

function MyComponent() {
  const [content, setContent] = useState('');

  return (
    <MilkdownEditor
      value={content}
      onChange={setContent}
      placeholder="Start typing content..."
    />
  );
}
```

## Complete Example

```tsx
import React, { useState, useRef } from 'react';
import MilkdownEditor, { type EditorRef, type EditorMode } from '@/components/MilkdownEditor';

function AdvancedEditor() {
  const [content, setContent] = useState('');
  const [mode, setMode] = useState<EditorMode>('wysiwyg');
  const editorRef = useRef<EditorRef>(null);

  const handleSave = (content: string) => {
    console.log('Save content:', content);
    // Implement save logic
  };

  const handleGetContent = () => {
    const currentContent = editorRef.current?.getContent();
    console.log('Current content:', currentContent);
  };

  return (
    <div>
      <button onClick={handleGetContent}>
        Get Content
      </button>
      
      <MilkdownEditor
        ref={editorRef}
        value={content}
        onChange={setContent}
        onSave={handleSave}
        onModeChange={setMode}
        placeholder="Start typing content..."
        showModeToggle={true}
        enableShortcuts={true}
        autoSave={true}
        autoSaveDelay={3000}
      />
    </div>
  );
}
```

## API Reference

### MilkdownEditorProps

| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `defaultValue` | `string` | - | Default value (uncontrolled mode) |
| `value` | `string` | - | Current value (controlled mode) |
| `onChange` | `(value: string) => void` | - | Content change callback |
| `onSave` | `(value: string) => void` | - | Save callback |
| `onModeChange` | `(mode: EditorMode) => void` | - | Mode change callback |
| `className` | `string` | `"w-full"` | CSS class name |
| `placeholder` | `string` | - | Placeholder text |
| `defaultMode` | `EditorMode` | `'wysiwyg'` | Default editor mode |
| `showModeToggle` | `boolean` | `true` | Whether to show mode toggle button |
| `enableShortcuts` | `boolean` | `true` | Whether to enable shortcuts |
| `autoSave` | `boolean` | `false` | Whether to enable auto save |
| `autoSaveDelay` | `number` | `2000` | Auto save delay (milliseconds) |

### EditorRef

Methods accessible through `ref`:

```tsx
interface EditorRef {
  getContent: () => string;
  setContent: (content: string) => void;
  getMode: () => EditorMode;
  setMode: (mode: EditorMode) => void;
  focus: () => void;
}
```

- `getContent(): string` - Get current content
- `setContent(content: string): void` - Set content
- `getMode(): EditorMode` - Get current mode
- `setMode(mode: EditorMode): void` - Set mode
- `focus(): void` - Focus editor

### EditorMode

```tsx
type EditorMode = 'wysiwyg' | 'markdown';
```

- `'wysiwyg'` - Visual editing mode
- `'markdown'` - Markdown source mode

## Shortcuts

| Shortcut | Function |
|----------|----------|
| `Ctrl+M` / `Cmd+M` | Toggle editing mode |
| `Ctrl+S` / `Cmd+S` | Save content |

## Theme Support

The editor automatically detects and adapts to system themes:

- Auto-detects `dark` class or system preferences
- Real-time theme change response
- Supports custom theme configuration

## Error Handling

The editor has comprehensive error boundaries:

- Automatically captures and handles runtime errors
- Provides friendly error message interface
- Shows detailed error information in development mode
- Supports retry and page refresh

## Performance Optimization

- Debounced input handling (300ms)
- Debounced auto-save (configurable)
- Dynamic imports to avoid SSR issues
- Memory leak prevention

## Notes

1. **SSR Compatibility**: Component already handles SSR issues, no additional configuration needed
2. **Dependency Management**: Avoid conflicts with other CodeMirror instances
3. **Content Sync**: Content automatically syncs when switching modes
4. **User Preferences**: Mode selection is automatically saved to localStorage

## Troubleshooting

### Common Issues

**Q: Editor fails to load?**
A: Check if all dependencies are correctly installed and ensure there are no version conflicts.

**Q: Shortcuts not working?**
A: Ensure `enableShortcuts` is set to `true`, check if other components are intercepting keyboard events.

**Q: Content not syncing?**
A: Check if `onChange` callback is correctly implemented, ensure `value` property is properly updated in controlled mode.

**Q: Theme incorrect?**
A: Ensure Tailwind CSS dark mode is correctly configured in your project.

### Debugging Tips

1. Open browser developer tools to check console errors
2. Check user preference settings in localStorage
3. Verify CSS class names are correctly applied
4. Use React DevTools to inspect component state

## Changelog

### v1.0.0
- Initial version release
- Support for WYSIWYG and Markdown dual modes
- Implemented real-time content synchronization
- Added shortcut support
- Integrated error boundary handling