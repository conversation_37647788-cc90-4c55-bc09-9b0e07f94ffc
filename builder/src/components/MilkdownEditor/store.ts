import { create } from 'zustand';
import { EditorMode } from './types';

interface EditorStore {
  // Content state
  content: string;
  mode: EditorMode;
  
  // Sync state
  isSyncing: boolean;
  lastUpdatedBy: 'wysiwyg' | 'markdown' | null;
  
  // Actions
  setContent: (content: string, updatedBy?: 'wysiwyg' | 'markdown') => void;
  setMode: (mode: EditorMode) => void;
  setSyncing: (syncing: boolean) => void;
  
  // Content sync method
  syncContent: (content: string, fromMode: 'wysiwyg' | 'markdown') => void;
}

// Store 工厂函数，为每个编辑器实例创建独立的 store
export const createEditorStore = () => create<EditorStore>((set, get) => ({
  // Initial state
  content: '',
  mode: 'wysiwyg',
  isSyncing: false,
  lastUpdatedBy: null,
  
  // Actions
  setContent: (content: string, updatedBy?: 'wysiwyg' | 'markdown') => {
    const state = get();
    
    // Prevent circular updates
    if (state.isSyncing || state.content === content) return;
    
    set({
      content,
      lastUpdatedBy: updatedBy || null
    });
  },
  
  setMode: (mode: EditorMode) => set({ mode }),
  
  setSyncing: (syncing: boolean) => set({ isSyncing: syncing }),
  
  syncContent: (content: string, fromMode: 'wysiwyg' | 'markdown') => {
    const state = get();
    

    
    // Prevent circular updates
    if (state.isSyncing) {

      return;
    }
    
    if (state.content === content) {

      return;
    }
    

    
    set({
      content,
      lastUpdatedBy: fromMode,
      isSyncing: true
    });
    
    // Reset sync state
    setTimeout(() => {
      set({ isSyncing: false });

    }, 10);
  }
}));

// 为了向后兼容，保留原有的全局 store（但建议使用 createEditorStore）
export const useEditorStore = createEditorStore();