'use client';

import React, { useRef, useImperativeHandle, useEffect } from 'react';
import MilkdownEditor from './index';
import { MilkdownEditorProps, EditorRef } from './types';

/**
 * MilkdownEditor 的包装组件，用于解决 Next.js dynamic 导入时的 forwardRef 问题
 * 这个组件不使用 forwardRef，而是通过内部 ref 管理来暴露方法
 */
interface MilkdownEditorWrapperProps extends MilkdownEditorProps {
  onRef?: (ref: EditorRef | null) => void;
}

const MilkdownEditorWrapper: React.FC<MilkdownEditorWrapperProps> = ({
  onRef,
  ...props
}) => {
  const editorRef = useRef<EditorRef>(null);

  useEffect(() => {
    if (onRef) {
      onRef(editorRef.current);
    }
  }, [onRef]);

  return <MilkdownEditor ref={editorRef} {...props} />;
};

MilkdownEditorWrapper.displayName = 'MilkdownEditorWrapper';

export default MilkdownEditorWrapper;
export type { EditorRef, MilkdownEditorProps, EditorMode } from './types';