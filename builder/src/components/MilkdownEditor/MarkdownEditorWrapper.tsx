'use client';

import React, { useRef, useImperativeHandle, useEffect } from 'react';
import MarkdownEditor from './MarkdownEditor';
import { MarkdownEditorProps, MarkdownEditorRef } from './types';

/**
 * MarkdownEditor 的包装组件，用于解决 Next.js dynamic 导入时的 forwardRef 问题
 * 这个组件不使用 forwardRef，而是通过内部 ref 管理来暴露方法
 */
interface MarkdownEditorWrapperProps extends MarkdownEditorProps {
  onRef?: (ref: MarkdownEditorRef | null) => void;
}

const MarkdownEditorWrapper: React.FC<MarkdownEditorWrapperProps> = ({
  onRef,
  ...props
}) => {
  const editorRef = useRef<MarkdownEditorRef>(null);

  useEffect(() => {
    if (onRef) {
      onRef(editorRef.current);
    }
  }, [onRef]);

  return <MarkdownEditor ref={editorRef} {...props} />;
};

MarkdownEditorWrapper.displayName = 'MarkdownEditorWrapper';

export default MarkdownEditorWrapper;
export type { MarkdownEditorRef, MarkdownEditorProps } from './types';