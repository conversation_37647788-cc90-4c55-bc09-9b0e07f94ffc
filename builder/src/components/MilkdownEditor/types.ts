export type EditorMode = 'wysiwyg' | 'markdown';

export interface MilkdownEditorProps {
  /** Default value (uncontrolled mode) */
  defaultValue?: string;
  /** Current value (controlled mode) */
  value?: string;
  /** Content change callback */
  onChange?: (value: string) => void;
  /** Save callback */
  onSave?: (value: string) => void;
  /** Editor mode change callback */
  onModeChange?: (mode: EditorMode) => void;
  /** CSS class name */
  className?: string;
  /** Placeholder text */
  placeholder?: string;
  /** Default editor mode */
  defaultMode?: EditorMode;
  /** Whether to show mode toggle button */
  showModeToggle?: boolean;
  /** Whether to enable shortcuts */
  enableShortcuts?: boolean;
  /** Whether to enable auto save */
  autoSave?: boolean;
  /** Auto save delay (milliseconds) */
  autoSaveDelay?: number;
  /** Editor height configuration */
  height?: number | string | 'auto';
  /** Minimum height */
  minHeight?: number | string;
  /** Maximum height */
  maxHeight?: number | string;
}

export interface CrepeEditorProps {
  defaultValue?: string;
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  className?: string;
  editorStore?: any; // 独立的 store 实例
  height?: number | string | 'auto';
  minHeight?: number | string;
  maxHeight?: number | string;
}

export interface CrepeEditorRef {
  focus: () => void;
  getEditor: () => any;
}

export interface MarkdownEditorProps {
  defaultValue?: string;
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  className?: string;
  theme?: 'light' | 'dark';
  editorStore?: any; // 独立的 store 实例
  height?: number | string | 'auto';
  minHeight?: number | string;
  maxHeight?: number | string;
}

export interface MarkdownEditorRef {
  focus: () => void;
  getEditor: () => any;
}

export interface ModeToggleProps {
  mode: EditorMode;
  onModeChange: (mode: EditorMode) => void;
  disabled?: boolean;
}

export interface EditorRef {
  getContent: () => string;
  setContent: (content: string) => void;
  getMode: () => EditorMode;
  setMode: (mode: EditorMode) => void;
  focus: () => void;
}