'use client';

import React, { useRef, useImperativeHandle, useEffect } from 'react';
import CrepeEditor from './CrepeEditor';
import { CrepeEditorProps, CrepeEditorRef } from './types';

/**
 * CrepeEditor 的包装组件，用于解决 Next.js dynamic 导入时的 forwardRef 问题
 * 这个组件不使用 forwardRef，而是通过内部 ref 管理来暴露方法
 */
interface CrepeEditorWrapperProps extends CrepeEditorProps {
  onRef?: (ref: CrepeEditorRef | null) => void;
}

const CrepeEditorWrapper: React.FC<CrepeEditorWrapperProps> = ({
  onRef,
  ...props
}) => {
  const editorRef = useRef<CrepeEditorRef>(null);

  useEffect(() => {
    if (onRef) {
      onRef(editorRef.current);
    }
  }, [onRef]);

  return <CrepeEditor ref={editorRef} {...props} />;
};

CrepeEditorWrapper.displayName = 'CrepeEditorWrapper';

export default CrepeEditorWrapper;
export type { CrepeEditorRef, CrepeEditorProps } from './types';