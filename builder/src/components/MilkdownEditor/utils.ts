import { EditorMode } from './types';

/**
 * Get default Markdown content
 */
export const getDefaultMarkdown = (): string => {
  return ``;
};

/**
 * Get user preferred editor mode
 */
export const getUserPreferredMode = (): EditorMode => {
  if (typeof window === 'undefined') return 'wysiwyg';
  
  try {
    const saved = localStorage.getItem('milkdown-editor-mode');
    return (saved === 'markdown' || saved === 'wysiwyg') ? saved : 'wysiwyg';
  } catch {
    return 'wysiwyg';
  }
};

/**
 * Save user preferred editor mode
 */
export const saveUserPreferredMode = (mode: EditorMode): void => {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.setItem('milkdown-editor-mode', mode);
  } catch {
    // Ignore localStorage errors
  }
};

/**
 * Debounce function
 */
export const debounce = <T extends (...args: any[]) => void>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

/**
 * Detect current theme mode
 */
export const detectThemeMode = (): 'light' | 'dark' => {
  if (typeof window === 'undefined') return 'light';
  
  // Check if HTML element has dark class
  if (document.documentElement.classList.contains('dark')) {
    return 'dark';
  }
  
  // Check system preference
  if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
    return 'dark';
  }
  
  return 'light';
};

/**
 * Watch theme changes
 */
export const watchThemeChange = (callback: (theme: 'light' | 'dark') => void): (() => void) => {
  if (typeof window === 'undefined') return () => {};
  
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
  
  const handleChange = () => {
    callback(detectThemeMode());
  };
  
  // Listen to system theme changes
  mediaQuery.addEventListener('change', handleChange);
  
  // Listen to DOM changes (for detecting manual theme switching)
  const observer = new MutationObserver(handleChange);
  observer.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['class']
  });
  
  return () => {
    mediaQuery.removeEventListener('change', handleChange);
    observer.disconnect();
  };
};

/**
 * Format shortcut display text
 */
export const formatShortcut = (shortcut: string): string => {
  const isMac = typeof navigator !== 'undefined' && navigator.platform.toUpperCase().indexOf('MAC') >= 0;
  return shortcut.replace('Ctrl', isMac ? '⌘' : 'Ctrl');
};