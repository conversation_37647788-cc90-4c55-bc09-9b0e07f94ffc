'use client';

import React, { useState, useCallback, useEffect, useImperativeHandle, forwardRef, useMemo, useRef } from 'react';
import { useDebouncedCallback } from 'use-debounce';
import { MilkdownEditorProps, EditorRef, EditorMode, CrepeEditorRef, MarkdownEditorRef } from './types';
import { getDefaultMarkdown, getUserPreferredMode, saveUserPreferredMode, detectThemeMode, watchThemeChange } from './utils';
import { createEditorStore } from './store';
import dynamic from 'next/dynamic';

// 动态导入编辑器包装组件以减少初始 bundle 大小
// 使用 wrapper 组件解决 forwardRef 与 dynamic 导入的兼容性问题
const CrepeEditorWrapper = dynamic(() => import('./CrepeEditorWrapper'), {
  loading: () => (
    <div className="flex items-center justify-center h-[900px] bg-background border-0">
      <div className="flex flex-col items-center space-y-3">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <p className="text-sm text-muted-foreground">Loading WYSIWYG editor...</p>
      </div>
    </div>
  ),
  ssr: false
});

const MarkdownEditorWrapper = dynamic(() => import('./MarkdownEditorWrapper'), {
  loading: () => (
    <div className="flex items-center justify-center h-[400px] bg-background border-0">
      <div className="flex flex-col items-center space-y-3">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <p className="text-sm text-muted-foreground">Loading Markdown editor...</p>
      </div>
    </div>
  ),
  ssr: false
});
import ModeToggle from './ModeToggle';
import EditorErrorBoundary from './ErrorBoundary';

/**
 * MilkdownEditor - 支持 WYSIWYG 和 Markdown 双模式的编辑器组件
 * 
 * 优化内容：
 * 1. 移除了直接的 DOM 操作 (document.getElementById)
 * 2. 使用 React ref 机制进行 DOM 引用
 * 3. 改进了快捷键处理逻辑
 * 4. 增强了错误处理和类型安全性
 * 5. 优化了子组件的 focus 方法调用
 */



const MilkdownEditor = forwardRef<EditorRef, MilkdownEditorProps>(({
  defaultValue,
  value,
  onChange,
  onSave,
  onModeChange,
  className = "w-full",
  placeholder,
  defaultMode = 'wysiwyg',
  showModeToggle = true,
  enableShortcuts = true,
  autoSave = false,
  autoSaveDelay = 2000,
  height = 900,
  minHeight,
  maxHeight
}, ref) => {
  // 为每个编辑器实例创建独立的 store
  const editorStore = useMemo(() => createEditorStore(), []);
  const useStore = editorStore;
  
  // 编辑器容器引用
  const editorContainerRef = useRef<HTMLDivElement>(null);
  // 子编辑器引用状态
  const [crepeEditorRef, setCrepeEditorRef] = useState<CrepeEditorRef | null>(null);
  const [markdownEditorRef, setMarkdownEditorRef] = useState<MarkdownEditorRef | null>(null);
  
  // 处理子编辑器 ref 回调
  const handleCrepeEditorRef = useCallback((ref: CrepeEditorRef | null) => {
    setCrepeEditorRef(ref);
  }, []);
  
  const handleMarkdownEditorRef = useCallback((ref: MarkdownEditorRef | null) => {
    setMarkdownEditorRef(ref);
  }, []);
  
  // 使用独立的 Zustand store
  const {
    content,
    mode,
    setMode,
    syncContent
  } = useStore();

  // 本地状态
  const [theme, setTheme] = useState<'light' | 'dark'>(() => detectThemeMode());

  // 初始化标志
  const [initialized, setInitialized] = useState(false);
  
  // 预加载状态
  const [preloadTriggered, setPreloadTriggered] = useState(false);

  // 初始化 store
  useEffect(() => {
    if (!initialized) {
      const initialMode = defaultMode === 'wysiwyg' ? getUserPreferredMode() : defaultMode;
      // 优先使用 value，如果 value 为 undefined 则使用 defaultValue，最后使用默认值
      const initialContent = value !== undefined ? value : (defaultValue ?? getDefaultMarkdown());
      
      setMode(initialMode);
      // 即使是空字符串也要同步，确保编辑器有正确的初始状态
      syncContent(initialContent, initialMode);
      setInitialized(true);
    }
  }, [defaultMode, value, defaultValue, setMode, syncContent, initialized, content]);

  // 防抖保存
  const debouncedSave = useDebouncedCallback((content: string) => {
    if (autoSave && onSave) {
      onSave(content);
    }
  }, autoSaveDelay);

  // 防抖 onChange
  const debouncedOnChange = useDebouncedCallback((content: string) => {
    if (onChange) {
      onChange(content);
    }
  }, 300);

  // 内容变化处理
  const handleContentChange = useCallback((newContent: string) => {
    // 防止无效内容
    if (typeof newContent !== 'string') {
      console.warn('Invalid content type received:', typeof newContent);
      return;
    }
    
    // 由于 store 已经处理了内容更新，这里只需要处理回调
    debouncedOnChange(newContent);
    debouncedSave(newContent);
  }, [debouncedOnChange, debouncedSave]);

  // 模式切换处理
  const handleModeChange = useCallback((newMode: EditorMode) => {
    // 切换模式
    setMode(newMode);
    
    // 保存用户偏好
    saveUserPreferredMode(newMode);
    
    // 触发回调
    if (onModeChange) {
      onModeChange(newMode);
    }
  }, [setMode, onModeChange]);

  // 快捷键处理
  useEffect(() => {
    if (!enableShortcuts) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      // 只有当前编辑器获得焦点时才响应快捷键
      const activeElement = document.activeElement;
      const editorContainer = editorContainerRef.current;
      
      // 检查焦点是否在当前编辑器内
      if (!editorContainer || !activeElement || !editorContainer.contains(activeElement)) {
        return;
      }

      // Ctrl+M 或 Cmd+M 切换模式
      if ((e.ctrlKey || e.metaKey) && e.key.toLowerCase() === 'm') {
        e.preventDefault();
        e.stopPropagation();
        handleModeChange(mode === 'wysiwyg' ? 'markdown' : 'wysiwyg');
      }
      
      // Ctrl+S 或 Cmd+S 保存
      if ((e.ctrlKey || e.metaKey) && e.key.toLowerCase() === 's') {
        e.preventDefault();
        e.stopPropagation();
        if (onSave) {
          onSave(content);
        }
      }
    };

    // 使用 capture 阶段监听，确保能够拦截事件
    document.addEventListener('keydown', handleKeyDown, true);
    return () => document.removeEventListener('keydown', handleKeyDown, true);
  }, [enableShortcuts, mode, content, onSave, handleModeChange]);

  // 主题监听
  useEffect(() => {
    const unwatch = watchThemeChange(setTheme);
    return unwatch;
  }, []);

  // 受控模式：当 value 改变时更新内容（确保初始化后才同步，避免循环更新）
  useEffect(() => {
    if (initialized && value !== undefined && value !== content) {
      syncContent(value, mode);
    }
  }, [value, content, mode, syncContent, initialized]);

  // 预加载策略：在组件初始化后延迟预加载另一个编辑器
  useEffect(() => {
    if (initialized && !preloadTriggered) {
      const timer = setTimeout(() => {
        // 预加载另一个编辑器包装组件
        if (mode === 'wysiwyg') {
          import('./MarkdownEditorWrapper');
        } else {
          import('./CrepeEditorWrapper');
        }
        setPreloadTriggered(true);
      }, 2000); // 2秒后开始预加载
      
      return () => clearTimeout(timer);
    }
  }, [initialized, preloadTriggered, mode]);

  // 预加载触发函数
  const triggerPreload = useCallback(() => {
    if (!preloadTriggered) {
      if (mode === 'wysiwyg') {
        import('./MarkdownEditorWrapper');
      } else {
        import('./CrepeEditorWrapper');
      }
      setPreloadTriggered(true);
    }
  }, [mode, preloadTriggered]);

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    getContent: () => {
      return content;
    },
    setContent: (newContent: string) => {
      syncContent(newContent, mode);
    },
    getMode: () => mode,
    setMode: (newMode: EditorMode) => {
      handleModeChange(newMode);
    },
    focus: () => {
      // 通过子组件的 ref 来聚焦编辑器
      if (mode === 'wysiwyg') {
        crepeEditorRef?.focus();
      } else {
        markdownEditorRef?.focus();
      }
    }
  }), [mode, content, syncContent, handleModeChange]);

  return (
    <EditorErrorBoundary
      onError={(error, errorInfo) => {
        console.error('MilkdownEditor Error:', error);
        console.error('Error Info:', errorInfo);
        
        // 可以在这里添加错误上报逻辑
        // 例如：sendErrorReport(error, errorInfo);
      }}
    >
      <div ref={editorContainerRef} className={`milkdown-editor ${className}`}>
        {/* 模式切换 */}
        {showModeToggle && (
          <div className="mb-3 flex justify-start">
            <div onMouseEnter={triggerPreload}>
              <ModeToggle
                mode={mode}
                onModeChange={handleModeChange}
              />
            </div>
          </div>
        )}

        {/* 编辑器容器 */}
        <div className="relative border border-border rounded-lg overflow-hidden bg-background shadow-sm">
          {mode === 'wysiwyg' ? (
            <CrepeEditorWrapper
              onRef={handleCrepeEditorRef}
              editorStore={editorStore}
              value={initialized ? content : (value !== undefined ? value : (defaultValue ?? getDefaultMarkdown()))}
              onChange={handleContentChange}
              placeholder={placeholder}
              className="w-full border-0 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 hover:scrollbar-thumb-gray-400 dark:scrollbar-thumb-gray-600 dark:scrollbar-track-gray-800"
              height={height}
              minHeight={minHeight}
              maxHeight={maxHeight}
            />
          ) : (
            <MarkdownEditorWrapper
              onRef={handleMarkdownEditorRef}
              editorStore={editorStore}
              value={initialized ? content : (value !== undefined ? value : (defaultValue ?? getDefaultMarkdown()))}
              onChange={handleContentChange}
              placeholder={placeholder}
              className="w-full border-0 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 hover:scrollbar-thumb-gray-400 dark:scrollbar-thumb-gray-600 dark:scrollbar-track-gray-800"
              theme={theme}
              height={height}
              minHeight={minHeight}
              maxHeight={maxHeight}
            />
          )}
        </div>
      </div>
    </EditorErrorBoundary>
  );
});

MilkdownEditor.displayName = 'MilkdownEditor';

export default MilkdownEditor;