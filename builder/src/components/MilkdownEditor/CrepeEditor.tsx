'use client';

import React, { FC, useLayoutEffect, useRef, MutableRefObject, forwardRef, useImperativeHandle } from 'react';
import { Crepe } from '@milkdown/crepe';
import { listener, listenerCtx } from '@milkdown/plugin-listener';
import { throttle } from 'throttle-debounce';
import '@milkdown/crepe/theme/common/style.css';
import '@milkdown/crepe/theme/frame.css';
import '@/styles/milkdown-dark-theme.css';
import { CrepeEditorProps, CrepeEditorRef } from './types';
import { getDefaultMarkdown } from './utils';
import { useEditorStore } from './store';

const CrepeEditor = forwardRef<CrepeEditorRef, CrepeEditorProps>(({ 
  defaultValue, 
  value, 
  onChange, 
  placeholder, 
  className = "crepe flex h-full flex-1 flex-col",
  editorStore,
  height = 900,
  minHeight,
  maxHeight
}, ref) => {
  const crepeRef = useRef<Crepe>(null);
  const divRef = useRef<HTMLDivElement>(null);
  const loading = useRef(false);
  const initialized = useRef(false);
  
  // Use Zustand store
  const defaultStore = useEditorStore;
  const useStore = editorStore || defaultStore;
  
  const {
    content,
    isSyncing,
    syncContent
  } = useStore();

  useLayoutEffect(() => {
    if (!divRef.current || loading.current || initialized.current) return;

    loading.current = true;
    initialized.current = true;
    
    const initialValue = value ?? defaultValue ?? content ?? getDefaultMarkdown();

    
    const crepe = new Crepe({
      root: divRef.current,
      defaultValue: initialValue,
    });

    // Configure listener
    crepe.editor
      .config((ctx) => {
        ctx.get(listenerCtx).markdownUpdated(
          throttle(200, (_: any, markdown: string) => {
            // Sync content to store
            syncContent(markdown, 'wysiwyg');
            
            // Call external onChange callback
            if (onChange) {
              onChange(markdown);
            }
          }),
        );
      })
      .use(listener);

    crepe.create().then(() => {
      (crepeRef as MutableRefObject<Crepe>).current = crepe;
      loading.current = false;
    });

    // Cleanup function
    return () => {
      if (loading.current) return;
      crepe.destroy();
      initialized.current = false;
    };
  }, []);

  // 处理外部 value 变化
  useLayoutEffect(() => {
    if (!initialized.current || !crepeRef.current) {

      return;
    }
    
    if (value !== undefined && value !== content && !isSyncing) {

      
      // 如果外部 value 与当前内容差异很大，重新创建编辑器
      if (Math.abs(value.length - content.length) > 100) {

        initialized.current = false;
        loading.current = false;
        // 触发重新创建
        setTimeout(() => {
          if (divRef.current && !loading.current) {
            initialized.current = false;
          }
        }, 0);
      } else {

        syncContent(value, 'external');
      }
    }
  }, [value, content, isSyncing, syncContent]);

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    focus: () => {
      if (crepeRef.current && divRef.current) {
        const editorElement = divRef.current.querySelector('.milkdown');
        if (editorElement) {
          (editorElement as HTMLElement).focus();
        }
      }
    },
    getEditor: () => crepeRef.current
  }), []);

  const containerStyle = {
    height: height ? (typeof height === 'number' ? `${height}px` : height) : undefined,
    minHeight: minHeight ? (typeof minHeight === 'number' ? `${minHeight}px` : minHeight) : undefined,
    maxHeight: maxHeight ? (typeof maxHeight === 'number' ? `${maxHeight}px` : maxHeight) : undefined,
  };

  return <div className={className} ref={divRef} style={containerStyle} />;
});

CrepeEditor.displayName = 'CrepeEditor';

export default CrepeEditor;