'use client';

import React, { FC, useCallback, useRef, useEffect, forwardRef, useImperativeHandle } from 'react';
import MDEditor from '@uiw/react-md-editor';
import '@uiw/react-md-editor/markdown-editor.css';
import '@uiw/react-markdown-preview/markdown.css';
import { MarkdownEditorProps, MarkdownEditorRef } from './types';
import { getDefaultMarkdown } from './utils';
import { useEditorStore } from './store';

const MarkdownEditor = forwardRef<MarkdownEditorRef, MarkdownEditorProps>((
  {
    defaultValue,
    value,
    onChange,
    placeholder,
    className = "overflow-visible",
    theme,
    editorStore,
    height = 900,
    minHeight,
    maxHeight
  },
  ref
) => {
  const editorRef = useRef<HTMLDivElement>(null);

  // 使用传入的独立 store 或默认全局 store
  const defaultStore = useEditorStore;
  const useStore = editorStore || defaultStore;
  
  const {
    content,
    isSyncing,
    syncContent
  } = useStore();

  // 同步外部 value 变化
  useEffect(() => {
    if (value !== undefined && value !== content && !isSyncing) {
      syncContent(value, 'markdown');
    }
  }, [value, content, isSyncing, syncContent]);

  const handleChange = useCallback((val?: string) => {
    const newContent = val || '';
    
    if (!isSyncing && newContent !== content) {
      syncContent(newContent, 'markdown');
      onChange?.(newContent);
    }
  }, [onChange, isSyncing, syncContent, content]);

  const currentContent = value !== undefined ? value : content;

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    focus: () => {
      if (editorRef.current) {
        const textarea = editorRef.current.querySelector('.w-md-editor-text-area, .w-md-editor textarea');
        if (textarea) {
          (textarea as HTMLElement).focus();
        }
      }
    },
    getEditor: () => editorRef.current
  }), []);

  const containerStyle = {
    minHeight: minHeight ? (typeof minHeight === 'number' ? `${minHeight}px` : minHeight) : undefined,
    maxHeight: maxHeight ? (typeof maxHeight === 'number' ? `${maxHeight}px` : maxHeight) : undefined,
  };

  // 处理 MDEditor 的 height 属性
  const editorHeight = height ? (typeof height === 'number' ? height : (height === 'auto' ? undefined : parseInt(height))) : 900;

  return (
    <div className={className} ref={editorRef} style={containerStyle}>
      <style jsx>{`
        :global(.w-md-editor) {
          border: none !important;
          box-shadow: none !important;
        }
        :global(.w-md-editor.w-md-editor-focus) {
          border: none !important;
          box-shadow: none !important;
        }
        :global(.w-md-editor .w-md-editor-text-input) {
          border: none !important;
          box-shadow: none !important;
        }
        :global(.w-md-editor .w-md-editor-text-input:focus) {
          border: none !important;
          box-shadow: none !important;
          outline: none !important;
        }
        :global(.w-md-editor .w-md-editor-text-area) {
          border: none !important;
          box-shadow: none !important;
        }
        :global(.w-md-editor .w-md-editor-text-area:focus) {
          border: none !important;
          box-shadow: none !important;
          outline: none !important;
        }
      `}</style>
      <MDEditor
        value={currentContent}
        onChange={handleChange}
        preview="edit"
        hideToolbar={false}
        visibleDragbar={false}
        textareaProps={{
          placeholder: placeholder || "Start typing Markdown...",
          style: {
            fontSize: 14,
            lineHeight: 1.5,
            fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
            border: 'none',
            outline: 'none',
            boxShadow: 'none',
          },
        }}
        height={editorHeight}
        data-color-mode={theme || 'light'}
        style={{
          backgroundColor: 'transparent',
        }}
      />
    </div>
  );
});

MarkdownEditor.displayName = 'MarkdownEditor';

export default MarkdownEditor;