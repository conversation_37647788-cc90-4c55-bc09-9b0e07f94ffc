import React, { useState, useCallback } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { useSectionContext } from '@litpage/sections';
import { PencilIcon, TrashIcon, PlusIcon } from '@heroicons/react/24/outline';
import LogoMaker from "@/components/logo-maker";

interface LogoData {
  url: string;
}

interface LogoSelectorProps {
  formData: LogoData;
  onChange: (logoData: LogoData | undefined) => void;
}

const LogoSelector: React.FC<LogoSelectorProps> = (props) => {
  const ctx = useSectionContext();
  const [isOpen, setIsOpen] = useState(false);
  const [logoData, setLogoData] = useState<LogoData>(props.formData || { url: '' });

  const handleOpen = () => setIsOpen(true);

  const updateFormData = useCallback((newLogoData: LogoData) => {
    if (newLogoData.url) {
      props.onChange(newLogoData);
    } else {
      props.onChange(undefined);
    }
    setLogoData(newLogoData);
  }, [props]);

  const handleChange = useCallback((key: string) => {
    const url = `/${key}`;
    const newLogoData = { url };
    updateFormData(newLogoData);
    setIsOpen(false);
  }, [updateFormData]);

  const handleClear = () => {
    const emptyLogoData = { url: '' };
    updateFormData(emptyLogoData);
  };

  return (
    <div className="w-40 h-40 border border-gray-200 rounded-lg shadow-sm overflow-hidden">
      <div className="relative group h-full">
        {!logoData.url ? (
          <Button
            onClick={handleOpen}
            type="button"
            className="w-full h-full flex flex-col items-center justify-center bg-gray-50 hover:bg-gray-100 transition-colors"
          >
            <PlusIcon className="h-6 w-6 text-gray-400 mb-1" />
            <span className="text-xs text-gray-500">Select Logo</span>
          </Button>
        ) : (
          <div className="w-full h-full flex flex-col items-center justify-center p-2">
            <img src={logoData.url} alt="Logo" className="max-w-full max-h-full object-contain" />
          </div>
        )}
        {logoData.url && (
          <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
            <Button onClick={handleOpen} type="button" className="mr-1" aria-label="Change logo" size="sm">
              <PencilIcon className="h-4 w-4" />
            </Button>
            <Button onClick={handleClear} type="button" variant="destructive" aria-label="Remove logo" size="sm">
              <TrashIcon className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="max-w-7xl">
          <LogoMaker domain={ctx.domain} onChange={handleChange} />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default LogoSelector;