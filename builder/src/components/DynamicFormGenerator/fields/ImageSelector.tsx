import React, { useState, useCallback } from 'react';
// import { FieldProps } from '@rjsf/utils';
import { Button } from "@/components/ui/button";
import ImageEditor, { ImageSelectData } from '@/components/image-editor';
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { useSectionContext } from '@litpage/sections';
import { Edit, Trash2 } from 'lucide-react';

interface ImageData {
  url: string;
  alt: string;
}

interface ImageSelectorProps {
  formData: ImageData;
  onChange: (imageData: ImageData | undefined) => void;
}
const ImageSelector: React.FC<ImageSelectorProps> = (props) => {
  const ctx = useSectionContext();
  const [isOpen, setIsOpen] = useState(false);
  const [imageData, setImageData] = useState<ImageData>(props.formData || { url: '', alt: '' });

  const handleOpen = () => setIsOpen(true);
  const handleClose = () => setIsOpen(false);

  const updateFormData = useCallback((newImageData: ImageData) => {
    if (newImageData.url) {
      props.onChange(newImageData);
    } else {
      props.onChange(undefined);
    }
    setImageData(newImageData);
  }, [props]);

  const handleChange = useCallback((result: ImageSelectData, editPos?: any) => {
    if (result.result?.filename) {
      const url = `/${result.result.filename}`;
      const newImageData = { ...imageData, url };
      updateFormData(newImageData);
      handleClose();
    }
  }, [imageData, updateFormData]);

  const handleAltChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newImageData = { ...imageData, alt: event.target.value };
    updateFormData(newImageData);
  };

  const handleClear = () => {
    const emptyImageData = { url: '', alt: '' };
    updateFormData(emptyImageData);
  };

  return (
    <div className="w-60 border border-gray-200 rounded-lg shadow-sm">
      {!imageData.url ? (
        <Button onClick={handleOpen} type="button" className="w-full h-[180px] border-2 border-dashed border-gray-300 rounded-t-lg hover:border-gray-400 transition-colors">
          Select Image
        </Button>
      ) : (
        <div className="relative group">
          <div className="h-[180px] overflow-hidden rounded-t-lg">
            <img src={imageData.url} alt={imageData.alt} className="object-cover w-full h-full" />
            <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
              <Button onClick={handleOpen} type="button" className="mr-2" aria-label="Change image">
                <Edit className="h-5 w-5" />
              </Button>
              <Button onClick={handleClear} type="button" variant="destructive" aria-label="Remove image">
                <Trash2 className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>
      )}
      <div className="p-3">
        <input
          type="text"
          value={imageData.alt}
          onChange={handleAltChange}
          placeholder="Enter alt text"
          className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="max-w-7xl p-12 max-h-[90vh]">
          <ImageEditor onChange={handleChange} />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ImageSelector;

// import React, { useState, useCallback } from 'react';
// import { FieldProps } from '@rjsf/utils';
// import { Button } from "@/components/ui/button";
// import ImageEditor from '@/components/image-editor';
// import { Dialog, DialogContent } from "@/components/ui/dialog";
// import { useSectionContext } from '@litpage/sections';
// import { PencilIcon, TrashIcon } from '@heroicons/react/24/outline';

// interface ImageData {
//   url: string;
//   alt: string;
// }

// const ImageSelector: React.FC<FieldProps> = (props) => {
//   const ctx = useSectionContext();
//   const [isOpen, setIsOpen] = useState(false);
//   const [imageData, setImageData] = useState<ImageData>(props.formData || { url: '', alt: '' });

//   const handleOpen = () => setIsOpen(true);
//   const handleClose = () => setIsOpen(false);

//   const updateFormData = useCallback((newImageData: ImageData) => {
//     console.log(22222, newImageData) //  && newImageData.alt
//     if (newImageData.url) {
//       props.onChange(newImageData);
//     } else {
//       props.onChange(undefined);
//     }
//     setImageData(newImageData);
//   }, [props]);

//   const handleChange = useCallback((image: { result: { filename: string } }) => {
//     const url = `https://${ctx.domain}.imgpipe.net/${image.result.filename}`;
//     const newImageData = { ...imageData, url };
//     updateFormData(newImageData);
//     handleClose();
//   }, [ctx.domain, imageData, updateFormData]);

//   const handleAltChange = (event: React.ChangeEvent<HTMLInputElement>) => {
//     const newImageData = { ...imageData, alt: event.target.value };
//     updateFormData(newImageData);
//   };

//   const handleClear = () => {
//     const emptyImageData = { url: '', alt: '' };
//     updateFormData(emptyImageData);
//   };

//   return (
//     <div className="w-full sm:w-80">
//       {!imageData.url ? (
//         <Button onClick={handleOpen} type="button" className="w-full py-12 border-2 border-dashed border-gray-300 rounded-lg hover:border-gray-400 transition-colors">
//           Select Image
//         </Button>
//       ) : (
//         <div className="relative group rounded-lg shadow-md overflow-hidden">
//           <div className="aspect-w-4 aspect-h-3">
//             <img src={imageData.url} alt={imageData.alt} className="object-cover w-full h-full rounded-t-lg" />
//             <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
//               <Button onClick={handleOpen} type="button" className="mr-2" aria-label="Change image">
//                 <PencilIcon className="h-5 w-5" />
//               </Button>
//               <Button onClick={handleClear} type="button" variant="destructive" aria-label="Remove image">
//                 <TrashIcon className="h-5 w-5" />
//               </Button>
//             </div>
//           </div>
//           <div className="p-4">
//             <input
//               type="text"
//               value={imageData.alt}
//               onChange={handleAltChange}
//               placeholder="Enter alt text"
//               className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
//             />
//           </div>
//         </div>
//       )}
//       <Dialog open={isOpen} onOpenChange={setIsOpen}>
//         <DialogContent className="max-w-7xl p-12 max-h-[90vh]">
//           <ImageEditor onChange={handleChange} />
//         </DialogContent>
//       </Dialog>
//     </div>
//   );
// };

// export default ImageSelector;

// import React, { useState, useCallback } from 'react';
// import { FieldProps } from '@rjsf/utils';
// import { Button } from "@/components/ui/button";
// import ImageEditor from '@/components/image-editor';
// import { Dialog, DialogContent } from "@/components/ui/dialog";
// import { useSectionContext } from '@litpage/sections';

// interface ImageData {
//   url: string;
//   alt: string;
// }

// const ImageSelector: React.FC<FieldProps> = (props) => {
//   const ctx = useSectionContext();
//   const [isOpen, setIsOpen] = useState(false);
//   const [imageData, setImageData] = useState<ImageData>(props.formData || { url: '', alt: '' });

//   const handleOpen = () => setIsOpen(true);
//   const handleClose = () => setIsOpen(false);

//   const updateFormData = useCallback((newImageData: ImageData) => {
//     // 只有当 URL 和 alt 都有值时才更新表单数据
//     if (newImageData.url && newImageData.alt) {
//       props.onChange(newImageData);
//     } else {
//       // 如果 URL 或 alt 为空，则不更新表单数据
//       props.onChange(undefined);
//     }
//     setImageData(newImageData);
//   }, [props]);

//   const handleChange = useCallback((image: { result: { filename: string } }) => {
//     const url = `https://${ctx.domain}.imgpipe.net/${image.result.filename}`;
//     const newImageData = { ...imageData, url };
//     updateFormData(newImageData);
//     handleClose();
//   }, [ctx.domain, imageData, updateFormData]);

//   const handleAltChange = (event: React.ChangeEvent<HTMLInputElement>) => {
//     const newImageData = { ...imageData, alt: event.target.value };
//     updateFormData(newImageData);
//   };

//   const handleClear = () => {
//     const emptyImageData = { url: '', alt: '' };
//     updateFormData(emptyImageData);
//   };

//   return (
//     <div>
//       <Button onClick={handleOpen} type="button">
//         {imageData.url ? 'Change Image' : 'Select Image'}
//       </Button>
//       {imageData.url && (
//         <div>
//           <img src={imageData.url} alt={imageData.alt} style={{ maxWidth: '200px' }} />
//           <input
//             type="text"
//             value={imageData.alt}
//             onChange={handleAltChange}
//             placeholder="Enter alt text"
//           />
//           <Button onClick={handleClear} type="button">Clear</Button>
//         </div>
//       )}
//       <Dialog open={isOpen} onOpenChange={setIsOpen}>
//         <DialogContent className="max-w-7xl p-12 max-h-[90vh]">
//           <ImageEditor onChange={handleChange} />
//         </DialogContent>
//       </Dialog>
//     </div>
//   );
// };

// export default ImageSelector;

// import React, { useState, useCallback } from 'react';
// import { FieldProps } from '@rjsf/utils';
// import { Button } from "@/components/ui/button";
// import ImageEditor from '@/components/image-editor';
// import { Dialog, DialogContent } from "@/components/ui/dialog";
// import { useSectionContext } from '@litpage/sections';

// interface ImageData {
//   url: string;
//   alt: string;
// }

// const ImageSelector: React.FC<FieldProps> = (props) => {
//   const ctx = useSectionContext();
//   const [isOpen, setIsOpen] = useState(false);
//   const [imageData, setImageData] = useState<ImageData>(props.formData || { url: '', alt: '' });

//   const handleOpen = () => setIsOpen(true);
//   const handleClose = () => setIsOpen(false);

//   const handleChange = useCallback((image: { result: { filename: string } }) => {
//     const url = `https://${ctx.domain}.imgpipe.net/${image.result.filename}`;
//     const newImageData = { ...imageData, url };
//     setImageData(newImageData);
//     props.onChange(newImageData);
//     handleClose();
//   }, [ctx.domain, imageData, props]);

//   const handleAltChange = (event: React.ChangeEvent<HTMLInputElement>) => {
//     const newImageData = { ...imageData, alt: event.target.value };
//     setImageData(newImageData);
//     props.onChange(newImageData);
//   };

//   const handleClear = () => {
//     const emptyImageData = { url: '', alt: '' };
//     setImageData(emptyImageData);
//     props.onChange(emptyImageData);
//   };

//   return (
//     <div>
//       <Button onClick={handleOpen}>
//         {imageData.url ? 'Change Image' : 'Select Image'}
//       </Button>
//       {imageData.url && (
//         <div>
//           <img src={imageData.url} alt={imageData.alt} style={{ maxWidth: '200px' }} />
//           <input
//             type="text"
//             value={imageData.alt}
//             onChange={handleAltChange}
//             placeholder="Enter alt text"
//           />
//           <Button onClick={handleClear}>Clear</Button>
//         </div>
//       )}
//       <Dialog open={isOpen} onOpenChange={setIsOpen}>
//         <DialogContent className="max-w-7xl p-12 max-h-[90vh]">
//           <ImageEditor onChange={handleChange} />
//         </DialogContent>
//       </Dialog>
//     </div>
//   );
// };

// export default ImageSelector;

// // src/components/DynamicFormGenerator/fields/ImageSelector.tsx

// import React, { useState, useCallback } from 'react';
// import { FieldProps } from '@rjsf/utils';
// import { Button } from '@mui/material';
// import ImageEditor from '@/components/image-editor';
// import {
//   Dialog,
//   DialogContent,
// } from "@/components/ui/dialog"
// import { useSectionContext } from '@litpage/sections';

// const ImageSelector: React.FC<FieldProps> = (props) => {
//   const ctx = useSectionContext();
//   const [isOpen, setIsOpen] = useState(false);
//   const [selectedImage, setSelectedImage] = useState(props.formData);

//   const handleOpen = () => setIsOpen(true);
//   const handleClose = () => setIsOpen(false);

//   const handleChange = useCallback((image: any) => {
//     const url = `https://${ctx.domain}.imgpipe.net/${image.result.filename}`;
//     setSelectedImage(url);
//     props.onChange(url);
//     handleClose();
//   }, [props]);

//   return (
//     <div>
//       <Button onClick={handleOpen}>
//         {selectedImage ? 'Change Image' : 'Select Image'}
//       </Button>
//       {selectedImage && <img src={selectedImage} alt="Selected" style={{ maxWidth: '200px' }} />}
//       <Dialog open={isOpen} onOpenChange={setIsOpen}>
//         <DialogContent className="max-w-7xl p-12 max-h-[90vh]">
//           <ImageEditor onChange={handleChange}></ImageEditor>
//         </DialogContent>
//       </Dialog>
//     </div>
//   );
// };

// export default ImageSelector;

// // src/components/DynamicFormGenerator/fields/ImageSelector.tsx

// import React, { useState } from 'react';
// import { FieldProps } from '@rjsf/utils';
// import { Button, Dialog, DialogTitle, DialogContent, DialogActions, Grid } from '@mui/material';

// const ImageSelector: React.FC<FieldProps> = (props) => {
//   const [open, setOpen] = useState(false);
//   const [selectedImage, setSelectedImage] = useState(props.formData);

//   const handleOpen = () => setOpen(true);
//   const handleClose = () => setOpen(false);

//   const handleSelectImage = (imageUrl: string) => {
//     setSelectedImage(imageUrl);
//     props.onChange(imageUrl);
//     handleClose();
//   };

//   // 这里可以添加一个模拟的图片库
//   const imageLibrary = [
//     'https://example.com/image1.jpg',
//     'https://example.com/image2.jpg',
//     'https://example.com/image3.jpg',
//   ];

//   return (
//     <div>
//       <Button onClick={handleOpen}>
//         {selectedImage ? 'Change Image' : 'Select Image'}
//       </Button>
//       {selectedImage && <img src={selectedImage} alt="Selected" style={{ maxWidth: '200px' }} />}
//       <Dialog open={open} onClose={handleClose}>
//         <DialogTitle>Select an Image</DialogTitle>
//         <DialogContent>
//           <Grid container spacing={2}>
//             {imageLibrary.map((image, index) => (
//               <Grid item key={index}>
//                 <img
//                   src={image}
//                   alt={`Option ${index + 1}`}
//                   style={{ width: '100px', cursor: 'pointer' }}
//                   onClick={() => handleSelectImage(image)}
//                 />
//               </Grid>
//             ))}
//           </Grid>
//         </DialogContent>
//         <DialogActions>
//           <Button onClick={handleClose}>Cancel</Button>
//         </DialogActions>
//       </Dialog>
//     </div>
//   );
// };

// export default ImageSelector;