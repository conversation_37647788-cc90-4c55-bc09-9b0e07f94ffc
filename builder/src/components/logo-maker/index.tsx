"use client";
// import { Metadata } from "next"
// import Image from "next/image"
import { CounterClockwiseClockIcon } from "@radix-ui/react-icons"
// import { SketchPicker } from 'react-color';
import LogoCanva from './components/logo-canva';
import { nanoid } from 'nanoid';

// import Icon from './components/icons/add-reaction';

import Icon from './components/icons';
import { IconList } from './components/icon-list';

import { Button } from "@/components/ui/button"
// import { Input } from "@/components/ui/input";
import { ColorPicker } from './components/color-picker';
// import {
//   HoverCard,
//   HoverCardContent,
//   HoverCardTrigger,
// } from "@/components/ui/hover-card"
// import { Label } from "@/components/ui/label"
// import { Separator } from "@/components/ui/separator"
// import {
//   Tabs,
//   TabsContent,
//   TabsList,
//   TabsTrigger,
// } from "@/components/ui/tabs"
// import { Textarea } from "@/components/ui/textarea"

// import { CodeViewer } from "./components/code-viewer"
// import { MaxLengthSelector } from "./components/maxlength-selector"
// import { ModelSelector } from "./components/model-selector"
// import { PresetActions } from "./components/preset-actions"
// import { PresetSave } from "./components/preset-save"
// import { PresetSelector } from "./components/preset-selector"
// import { PresetShare } from "./components/preset-share"
import { Selector } from "./components/selector"
// import { TopPSelector } from "./components/top-p-selector"
// import { models, types } from "./data/models"
import { useMemo, useRef, useState } from "react";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
// import { presets } from "./data/presets"

// export const metadata: Metadata = {
//   title: "Playground",
//   description: "The OpenAI Playground built using the components.",
// }

export default function LogoMaker({ domain, onChange }: any) {
  const svgRef: any = useRef();

  const [iconName, setIconName] = useState<string>('emoticon');

  const [iconSize, setIconSize] = useState<number>(256);
  const [round, setRound] = useState<number>(35);
  const [border, setBorder] = useState<number>(20);

  const [borderOpacity, setBorderOpacity] = useState<number>(0.07);

  const [iconColor, setIconColor] = useState<string>('#becdcf');
  const [borderColor, setBorderColor] = useState<string>('#393c65');
  const [bgColor, setBgColor] = useState<string>('#288895');

  const [glare, setGlare] = useState<boolean>(false);

  const iconPos = useMemo(() => {
    return (512 - iconSize) / 2
  }, [iconSize])

  const exportSVG = (svg: any) => {
    const serializer = new XMLSerializer();
    const svgString = serializer.serializeToString(svg);
    const blob = new Blob([svgString], { type: 'image/svg+xml' });
    return blob;
  };

  const uploadSVG = async () => {
    if (!svgRef.current) return;

    const id = nanoid(7);
    const blob: any = exportSVG(svgRef.current.getSVG());
    const formData = new FormData();
    formData.append('file', blob, `${domain}_logo_${id}.svg`);
    formData.append('key', `${domain}_logo_${id}.svg`);
    formData.append('metadata', JSON.stringify({ description: 'litpage test app', author: 'litpage' }));

    try {

      const pushService = process.env.NODE_ENV === 'development' 
        ? `http://localhost:7001/push/form` 
        : `https://${domain}.imgpipe.io/push/form`;

      const response = await fetch(pushService, {
        method: 'POST',
        body: formData,
        // mode: 'no-cors',
      });
      const data = await response.json();
      console.log('Upload success:', data);
      onChange(`/${domain}_logo_${id}.svg`)
    } catch (error) {
      console.error('Upload error:', error);
      onChange(`/${domain}_logo_${id}.svg`)
    }
  };

  return (
    <>
      {/* <div className="md:hidden">
        <Image
          src="/examples/playground-light.png"
          width={1280}
          height={916}
          alt="Playground"
          className="block dark:hidden"
        />
        <Image
          src="/examples/playground-dark.png"
          width={1280}
          height={916}
          alt="Playground"
          className="hidden dark:block"
        />
      </div> */}
      <div className="h-full flex-col md:flex">
        {/* <div className="container flex flex-col items-start justify-between space-y-2 py-4 sm:flex-row sm:items-center sm:space-y-0 md:h-16">
          <h2 className="text-lg font-semibold">Playground</h2>
          <div className="ml-auto flex w-full space-x-2 sm:justify-end">
            <PresetSelector presets={presets} />
            <PresetSave />
            <div className="hidden space-x-2 md:flex">
              <CodeViewer />
              <PresetShare />
            </div>
            <PresetActions />
          </div>
        </div> */}
        {/* <Separator /> */}
        <div className="flex-1">
          <div className="container h-full py-6">
            <div className="grid h-full items-stretch gap-6 md:grid-cols-[1fr_200px]">
              <div className="hidden flex-col space-y-4 sm:flex md:order-2">
                {/* <div className="grid gap-2">
                  
                  
                </div> */}
                
                <Selector label="Icon size" max={512} min={128} step={1} defaultValue={[256]} onChange={(v: number) => { setIconSize(v) }} />
                <Selector label="Round" max={256} min={10} step={1} defaultValue={[35]} onChange={(v: number) => { setRound(v) }} />
                <Selector label="Border" max={128} step={1} defaultValue={[35]} onChange={(v: number) => { setBorder(v) }} />
                <Selector label="Border Opacity" max={1} step={0.01} defaultValue={[borderOpacity]} onChange={(v: number) => { setBorderOpacity(v) }} />

                <ColorPicker id="color" label="Icon Color" initialColor={iconColor} onChange={(color: string) => { setIconColor(color) }} />
                <ColorPicker id="color" label="Border Color" initialColor={borderColor} onChange={(color: string) => { setBorderColor(color) }} />
                <ColorPicker id="bgcolor" label="Background Color" initialColor={bgColor} onChange={(color: string) => { setBgColor(color) }} />

                <div className="flex items-center space-x-2">
                  <Label htmlFor="glare-mode">Glare Mode</Label>
                  <Switch id="glare-mode" checked={glare} onCheckedChange={setGlare} />
                </div>

              </div>
              <div className="md:order-1">
                <div className="t-0 border-0 p-0">
                  <div className="flex flex-col space-y-4">
                    <div className="grid h-full grid-rows-2 gap-6 lg:grid-cols-2 lg:grid-rows-1">
                      <div className="h-full min-h-[300px] lg:min-h-[700px] xl:min-h-[700px] rounded-md border p-4 sm:p-6 lg:p-8">
                        <IconList onSelect={(key: string) => setIconName(key)}></IconList>
                      </div>
                      <div className="flex rounded-md border bg-dashed bg-size-1rem items-center justify-center">
                        <LogoCanva ref={svgRef} bgColor={bgColor} borderColor={borderColor} round={round} border={border} borderOpacity={borderOpacity} glare={glare}>
                          <Icon iconName={iconName} iconSize={iconSize} iconPos={iconPos} iconColor={iconColor}></Icon>
                        </LogoCanva>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button onClick={uploadSVG}>Submit</Button>
                      <Button variant="secondary">
                        <span className="sr-only">Show history</span>
                        <CounterClockwiseClockIcon className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
