'use client';

import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, X, CheckCircle, AlertCircle, Image as ImageIcon, Copy, ExternalLink, RotateCcw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useLocalImageUpload } from './useLocalImageUpload';
import { cn } from '@/lib/utils';

interface DropzoneUploadProps {
  onUploadSuccess?: (imageUrl: string) => void;
  onUploadError?: (error: string) => void;
  domain?: string;
  className?: string;
  disabled?: boolean;
}

interface PreviewImage {
  file: File;
  preview: string;
}

export const DropzoneUpload: React.FC<DropzoneUploadProps> = ({
  onUploadSuccess,
  onUploadError,
  domain,
  className,
  disabled = false
}) => {
  const [previewImage, setPreviewImage] = useState<PreviewImage | null>(null);
  const [uploadedImageUrl, setUploadedImageUrl] = useState<string | null>(null);
  
  const { uploadImage, isUploading, progress, error, clearError, convertImageIdToUrl } = useLocalImageUpload({
    domain,
    onSuccess: (result) => {
      console.log('Upload success callback:', result);
    },
    onError: (errorMsg) => {
      console.error('Upload error callback:', errorMsg);
      onUploadError?.(errorMsg);
    }
  });
  
  const onDrop = useCallback(async (acceptedFiles: File[], rejectedFiles: any[]) => {
    // Handle rejected files
    if (rejectedFiles.length > 0) {
      const rejection = rejectedFiles[0];
      const errorMessages = rejection.errors.map((e: any) => {
        switch (e.code) {
          case 'file-too-large':
            return 'File size exceeds 50MB limit';
          case 'file-invalid-type':
            return 'Unsupported file format';
          case 'too-many-files':
            return 'Only one file can be uploaded at a time';
          default:
            return e.message;
        }
      }).join(', ');
      
      onUploadError?.(errorMessages);
      return;
    }
    
    if (acceptedFiles.length === 0) return;
    
    const file = acceptedFiles[0];
    
    // Create preview
    const preview = URL.createObjectURL(file);
    setPreviewImage({ file, preview });
    setUploadedImageUrl(null);
    clearError();

    try {
      // Execute upload, returns image ID
      const imageId = await uploadImage(file);
      // Convert ID to relative path for storage
      const imagePath = imageId.startsWith('/') ? imageId : `/${imageId}`;
      const displayUrl = convertImageIdToUrl(imageId); // For preview display
      setUploadedImageUrl(displayUrl);
      // Pass relative path in callback
      onUploadSuccess?.(imagePath);
      
    } catch (err: any) {
      console.error('Upload failed:', err);
      // Error is already handled in hook
    }
  }, [uploadImage, onUploadSuccess, onUploadError, clearError]);
  
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.webp', '.avif', '.gif', '.tiff', '.svg']
    },
    maxSize: 50 * 1024 * 1024, // 50MB
    multiple: false,
    disabled: disabled || isUploading
  });
  
  const clearPreview = useCallback(() => {
    if (previewImage?.preview) {
      URL.revokeObjectURL(previewImage.preview);
    }
    setPreviewImage(null);
    setUploadedImageUrl(null);
    clearError();
  }, [previewImage, clearError]);

  const resetUpload = useCallback(() => {
    clearPreview();
  }, [clearPreview]);

  const copyToClipboard = useCallback(async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      // Success notification can be added
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  }, []);
  
  // Clean up preview URL
  React.useEffect(() => {
    return () => {
      if (previewImage?.preview) {
        URL.revokeObjectURL(previewImage.preview);
      }
    };
  }, [previewImage]);
  
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };
  
  return (
    <div className={cn('w-full', className)}>
      {/* Drag and drop area */}
      <div
        {...getRootProps()}
        className={cn(
          'relative border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors',
          'hover:border-primary/50 hover:bg-primary/5',
          isDragActive && 'border-primary bg-primary/10',
          isUploading && 'pointer-events-none opacity-50',
          disabled && 'pointer-events-none opacity-50 cursor-not-allowed',
          error && 'border-destructive/50 bg-destructive/5',
          uploadedImageUrl && 'border-green-500/50 bg-green-50'
        )}
      >
        <input {...getInputProps()} />
        
        {/* Upload status display */}
        {!previewImage && !isUploading && (
          <div className="space-y-4">
              <div className="mx-auto w-12 h-12 text-muted-foreground">
                <Upload className="w-full h-full" />
              </div>
              <div className="space-y-2">
                <p className="text-lg font-medium">
                  {isDragActive ? 'Release files to start uploading' : 'Drag and drop images here to upload'}
                </p>
                <p className="text-sm text-muted-foreground">
                  or click to select files
                </p>
                <p className="text-xs text-muted-foreground">
                  Supports JPG, PNG, GIF, WebP formats, maximum 50MB
                </p>
              </div>
            </div>
        )}
        
        {/* Uploading Status */}
        {isUploading && (
          <div className="space-y-4">
            <div className="mx-auto w-12 h-12 text-primary animate-spin">
              <Upload className="w-full h-full" />
            </div>
            <div className="space-y-2">
              <p className="text-lg font-medium">Uploading...</p>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-primary h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                />
              </div>
              <p className="text-sm text-muted-foreground">{progress}%</p>
            </div>
          </div>
        )}
        
        {/* Upload Success Status */}
        {uploadedImageUrl && !isUploading && (
          <div className="space-y-4">
            <div className="mx-auto w-12 h-12 text-green-600">
              <CheckCircle className="w-full h-full" />
            </div>
            <div className="space-y-2">
              <p className="text-lg font-medium text-green-600">Upload successful!</p>
              <p className="text-sm text-muted-foreground break-all">
                {uploadedImageUrl}
              </p>
              <div className="flex justify-center space-x-2 mt-3">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(uploadedImageUrl)}
                >
                  <Copy className="w-4 h-4 mr-2" />
                  Copy Link
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open(uploadedImageUrl, '_blank')}
                >
                  <ExternalLink className="w-4 h-4 mr-2" />
                  View Image
                </Button>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={resetUpload}
                className="mt-2"
              >
                <RotateCcw className="w-4 h-4 mr-2" />
                Retry
              </Button>
            </div>
          </div>
        )}
        
        {/* Error Status */}
        {error && !isUploading && (
          <div className="space-y-4">
            <div className="mx-auto w-12 h-12 text-destructive">
              <AlertCircle className="w-full h-full" />
            </div>
            <div className="space-y-2">
              <p className="text-lg font-medium text-destructive">Upload failed</p>
              <p className="text-sm text-destructive">{error}</p>
            </div>
          </div>
        )}
      </div>
      
      {/* Image preview */}
      {previewImage && (
        <div className="mt-4 p-4 border rounded-lg bg-card">
          <div className="flex items-start gap-4">
            {/* Preview image */}
            <div className="relative flex-shrink-0">
              <img
                src={previewImage.preview}
                alt="Preview"
                className="w-20 h-20 object-cover rounded border"
              />
              {uploadedImageUrl && (
                <div className="absolute -top-1 -right-1 w-6 h-6 bg-green-600 rounded-full flex items-center justify-center">
                  <CheckCircle className="w-4 h-4 text-white" />
                </div>
              )}
            </div>
            
            {/* File information */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between">
                <div className="space-y-1">
                  <p className="font-medium truncate">{previewImage.file.name}</p>
                  <p className="text-sm text-muted-foreground">
                    {formatFileSize(previewImage.file.size)} • {previewImage.file.type}
                  </p>
                  {uploadedImageUrl && (
                    <p className="text-sm text-green-600">✓ Upload completed</p>
                  )}
                </div>
                
                {/* Clear button */}
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    clearPreview();
                  }}
                  className="p-1 hover:bg-gray-100 rounded transition-colors"
                  disabled={isUploading}
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DropzoneUpload;