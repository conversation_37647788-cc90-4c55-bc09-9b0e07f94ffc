'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Copy, ExternalLink, RotateCcw } from 'lucide-react';
import { DropzoneUpload } from './DropzoneUpload';
import { cn } from '@/lib/utils';

interface ImageUploadWidgetProps {
  domain?: string;
  className?: string;
  onUploadSuccess?: (imageUrl: string) => void;
  onUploadError?: (error: string) => void;
}

interface UploadResult {
  imageUrl: string;
  timestamp: number;
  method: 'dropzone';
}

export const ImageUploadWidget: React.FC<ImageUploadWidgetProps> = ({
  domain,
  className,
  onUploadSuccess,
  onUploadError
}) => {
  const [uploadResults, setUploadResults] = useState<UploadResult[]>([]);
  
  const handleUploadSuccess = (imageUrl: string) => {
    const result: UploadResult = {
      imageUrl,
      timestamp: Date.now(),
      method: 'dropzone'
    };
    
    setUploadResults(prev => [result, ...prev.slice(0, 4)]); // Keep latest 5 results
    onUploadSuccess?.(imageUrl);
  };
  
  const handleUploadError = (error: string) => {
    onUploadError?.(error);
  };
  
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      // A toast notification can be added here
      console.log('Copied to clipboard:', text);
    } catch (err) {
      console.error('Copy failed:', err);
    }
  };
  
  const clearResults = () => {
    setUploadResults([]);
  };
  
  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };
  
  return (
    <div className={cn('w-full max-w-2xl mx-auto space-y-6', className)}>
      <Card>
        <CardHeader>
          <CardTitle>Image Upload</CardTitle>
          <CardDescription>
            Supports JPG, PNG, GIF, WebP formats, maximum 50MB
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DropzoneUpload
            domain={domain}
            onUploadSuccess={handleUploadSuccess}
            onUploadError={onUploadError}
          />
        </CardContent>
      </Card>
      
      {/* Upload History */}
      {uploadResults.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg">Upload History</CardTitle>
                <CardDescription>
                  Recent uploaded image records
                </CardDescription>
              </div>
              <Button
                onClick={clearResults}
                variant="outline"
                size="sm"
                className="text-xs"
              >
                <RotateCcw className="w-3 h-3 mr-1" />
                Clear
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {uploadResults.map((result, index) => (
                <div
                  key={`${result.timestamp}-${index}`}
                  className="flex items-center gap-3 p-3 border rounded-lg bg-muted/30"
                >
                  {/* Preview Image */}
                  <div className="flex-shrink-0">
                    <img
                      src={result.imageUrl}
                      alt="Uploaded image"
                      className="w-12 h-12 object-cover rounded border"
                      onError={(e) => {
                        // Hide image when loading fails
                        (e.target as HTMLImageElement).style.display = 'none';
                      }}
                    />
                  </div>
                  
                  {/* Image Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <Badge 
                        variant={result.method === 'dropzone' ? 'default' : 'secondary'}
                        className="text-xs"
                      >
                        Drag & Drop
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        {formatTime(result.timestamp)}
                      </span>
                    </div>
                    <p className="text-sm font-mono break-all text-muted-foreground">
                      {result.imageUrl}
                    </p>
                  </div>
                  
                  {/* Action Buttons */}
                  <div className="flex items-center gap-1">
                    <Button
                      onClick={() => copyToClipboard(result.imageUrl)}
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                      title="Copy link"
                    >
                      <Copy className="w-3 h-3" />
                    </Button>
                    <Button
                      onClick={() => window.open(result.imageUrl, '_blank')}
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                      title="Open in new window"
                    >
                      <ExternalLink className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
      
      {/* Usage Instructions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Usage Instructions</CardTitle>
          <CardDescription>
            Image upload service usage guide
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-medium mb-2">Supported Formats</h4>
            <div className="flex flex-wrap gap-2">
              {['JPEG', 'PNG', 'WebP', 'AVIF', 'GIF', 'TIFF', 'SVG'].map(format => (
                <Badge key={format} variant="secondary">
                  {format}
                </Badge>
              ))}
            </div>
          </div>
          
          <div>
            <h4 className="font-medium mb-2">Limitations</h4>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• Maximum file size: 50MB</li>
              <li>• Only one file can be uploaded at a time</li>
              <li>• Permanent links are automatically generated after upload</li>
              <li>• Supports cross-origin access</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ImageUploadWidget;