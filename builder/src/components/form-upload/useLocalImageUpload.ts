import { useState, useCallback } from 'react';

// Function to convert image ID to URL
export const convertImageIdToUrl = (imageId: string): string => {
  if (!imageId) return '';
  
  // Extract domain and filename from ID format: domain/filename
  const parts = imageId.split('/');
  if (parts.length !== 2) return imageId; // Return original if format is unexpected
  
  const [domain, filename] = parts;
  return `https://${domain}.imgpipe.net/${filename}?fm=webp`;
};

export interface LocalImageUploadOptions {
  domain?: string;
  onProgress?: (progress: number) => void;
  onSuccess?: (result: any) => void;
  onError?: (error: string) => void;
}

export interface LocalImageUploadReturn {
  uploadImage: (file: File) => Promise<string>;
  isUploading: boolean;
  progress: number;
  error: string | null;
  clearError: () => void;
  convertImageIdToUrl: (imageId: string) => string;
}

// Supported image formats
const ALLOWED_TYPES = [
  'image/jpeg',
  'image/jpg', 
  'image/png',
  'image/webp',
  'image/avif',
  'image/gif',
  'image/tiff',
  'image/svg+xml'
];

// Maximum file size 50MB
const MAX_FILE_SIZE = 50 * 1024 * 1024;

// File validation function
const validateFile = (file: File): void => {
  if (!ALLOWED_TYPES.includes(file.type)) {
    throw new Error(`Unsupported file format: ${file.type}. Supported formats: JPEG, PNG, WebP, AVIF, GIF, TIFF, SVG`);
  }
  
  if (file.size > MAX_FILE_SIZE) {
    const sizeMB = (file.size / 1024 / 1024).toFixed(2);
    throw new Error(`File size exceeds limit: ${sizeMB}MB, maximum supported is 50MB`);
  }
};

// Generate upload key
const generateUploadKey = (): string => {
  return `local-upload-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

// Get upload URL
const getUploadUrl = (domain?: string): string => {
  const isDevelopment = process.env.NODE_ENV === 'development';
  return isDevelopment 
    ? 'http://localhost:7001/push/form'
    : `https://${domain || 'default'}.imgpipe.io/push/form`;
};

// Use XMLHttpRequest to implement upload with progress
const uploadWithProgress = (
  formData: FormData, 
  url: string, 
  onProgress?: (progress: number) => void
): Promise<any> => {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();
    
    // Upload progress listener
    xhr.upload.addEventListener('progress', (event) => {
      if (event.lengthComputable && onProgress) {
        const progress = Math.round((event.loaded / event.total) * 100);
        onProgress(progress);
      }
    });
    
    // Request completion listener
    xhr.addEventListener('load', () => {
      if (xhr.status >= 200 && xhr.status < 300) {
        try {
          const result = JSON.parse(xhr.responseText);
          resolve(result);
        } catch (error) {
          reject(new Error('Server response format error'));
        }
      } else {
        reject(new Error(`Upload failed: ${xhr.status} ${xhr.statusText}`));
      }
    });
    
    // Request error listener
    xhr.addEventListener('error', () => {
      reject(new Error('Network error, upload failed'));
    });
    
    // Request timeout listener
    xhr.addEventListener('timeout', () => {
      reject(new Error('Upload timeout, please retry'));
    });
    
    // Set timeout (5 minutes)
    xhr.timeout = 5 * 60 * 1000;
    
    // Send request
    xhr.open('POST', url);
    xhr.send(formData);
  });
};

export const useLocalImageUpload = (options: LocalImageUploadOptions = {}): LocalImageUploadReturn => {
  const { domain, onProgress, onSuccess, onError } = options;
  
  const [isUploading, setIsUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  
  const clearError = useCallback(() => {
    setError(null);
  }, []);
  
  const uploadImage = useCallback(async (file: File): Promise<string> => {
    try {
      // Clear previous errors
      setError(null);
      setIsUploading(true);
      setProgress(0);
      
      // File validation
      validateFile(file);
      
      // Build FormData
      const formData = new FormData();
      const uploadKey = generateUploadKey();
      
      formData.append('file', file);
      formData.append('key', uploadKey);
      formData.append('metadata', JSON.stringify({
        originalName: file.name,
        size: file.size,
        type: file.type,
        uploadedAt: new Date().toISOString(),
        source: 'local-upload'
      }));
      
      // Get upload URL
      const uploadUrl = getUploadUrl(domain);
      
      console.log('Starting file upload:', {
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
        uploadUrl,
        uploadKey
      });
      
      // Execute upload
      const result = await uploadWithProgress(
        formData, 
        uploadUrl, 
        (progressValue) => {
          setProgress(progressValue);
          onProgress?.(progressValue);
        }
      );
      
      console.log('Upload successful:', result);
      
      // Get image ID (prioritize result.id, then result.result?.id)
      const imageId = result.id || result.result?.id;
      
      if (!imageId) {
        throw new Error('Server did not return a valid image ID');
      }
      
      // Call success callback
      onSuccess?.(result);
      
      return imageId;
      
    } catch (err: any) {
      const errorMessage = err.message || 'Upload failed, please try again';
      console.error('Upload error:', err);
      
      setError(errorMessage);
      onError?.(errorMessage);
      
      throw new Error(errorMessage);
      
    } finally {
      setIsUploading(false);
      setProgress(0);
    }
  }, [domain, onProgress, onSuccess, onError]);
  
  return {
    uploadImage,
    isUploading,
    progress,
    error,
    clearError,
    convertImageIdToUrl
  };
};

export default useLocalImageUpload;