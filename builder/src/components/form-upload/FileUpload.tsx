'use client';

import React, { useRef, useCallback, useState } from 'react';
import { Upload, X, CheckCircle, AlertCircle, FileImage } from 'lucide-react';
import { useLocalImageUpload } from './useLocalImageUpload';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';

interface FileUploadProps {
  onUploadSuccess?: (imageUrl: string) => void;
  onUploadError?: (error: string) => void;
  domain?: string;
  className?: string;
  disabled?: boolean;
  variant?: 'default' | 'outline' | 'secondary';
}

interface SelectedFile {
  file: File;
  preview: string;
}

export const FileUpload: React.FC<FileUploadProps> = ({
  onUploadSuccess,
  onUploadError,
  domain,
  className,
  disabled = false,
  variant = 'default'
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedFile, setSelectedFile] = useState<SelectedFile | null>(null);
  const [uploadedImageUrl, setUploadedImageUrl] = useState<string | null>(null);
  
  const { uploadImage, isUploading, progress, error, clearError, convertImageIdToUrl } = useLocalImageUpload({
    domain,
    onSuccess: (result) => {
      console.log('File upload successful:', result);
    },
    onError: (errorMsg) => {
      console.error('File upload failed:', errorMsg);
      onUploadError?.(errorMsg);
    }
  });
  
  const handleFileSelect = useCallback(() => {
    fileInputRef.current?.click();
  }, []);
  
  const handleFileChange = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;
    
    const file = files[0];
    
    // Validate file type
    const allowedTypes = [
      'image/jpeg', 'image/jpg', 'image/png', 'image/webp',
      'image/avif', 'image/gif', 'image/tiff', 'image/svg+xml'
    ];
    
    if (!allowedTypes.includes(file.type)) {
      onUploadError?.('Unsupported file format, please select an image file');
      return;
    }
    
    // Validate file size
    const maxSize = 50 * 1024 * 1024; // 50MB
    if (file.size > maxSize) {
      const sizeMB = (file.size / 1024 / 1024).toFixed(2);
      onUploadError?.(`File size exceeds limit: ${sizeMB}MB, maximum supported is 50MB`);
      return;
    }
    
    // Create preview
    const preview = URL.createObjectURL(file);
    setSelectedFile({ file, preview });
    setUploadedImageUrl(null);
    clearError();
    
    try {
      // Execute upload, returns image ID
      const imageId = await uploadImage(file);
      // Convert ID to relative path for storage
      const imagePath = imageId.startsWith('/') ? imageId : `/${imageId}`;
      const displayUrl = convertImageIdToUrl(imageId); // For preview display
      setUploadedImageUrl(displayUrl);
      // Pass relative path in callback
      onUploadSuccess?.(imagePath);
      
    } catch (err: any) {
      console.error('Upload failed:', err);
      // Error is already handled in hook
    }
    
    // Clear input value to allow reselecting the same file
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [uploadImage, onUploadSuccess, onUploadError, clearError]);
  
  const clearSelection = useCallback(() => {
    if (selectedFile?.preview) {
      URL.revokeObjectURL(selectedFile.preview);
    }
    setSelectedFile(null);
    setUploadedImageUrl(null);
    clearError();
    
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [selectedFile, clearError]);
  
  // Clean up preview URL
  React.useEffect(() => {
    return () => {
      if (selectedFile?.preview) {
        URL.revokeObjectURL(selectedFile.preview);
      }
    };
  }, [selectedFile]);
  
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };
  
  return (
    <div className={cn('w-full space-y-4', className)}>
      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*,.jpg,.jpeg,.png,.webp,.avif,.gif,.tiff,.svg"
        onChange={handleFileChange}
        className="hidden"
        disabled={disabled || isUploading}
      />
      
      {/* File selection button */}
      <div className="flex flex-col items-center gap-4">
        <Button
          onClick={handleFileSelect}
          disabled={disabled || isUploading}
          variant={variant}
          size="lg"
          className="w-full max-w-xs"
        >
          <Upload className="w-4 h-4 mr-2" />
          {isUploading ? 'Uploading...' : 'Select Image File'}
        </Button>
        
        <p className="text-sm text-muted-foreground text-center">
          Supports JPEG, PNG, WebP, AVIF, GIF, TIFF, SVG formats<br />
          Maximum file size: 50MB
        </p>
      </div>
      
      {/* Upload progress */}
      {isUploading && (
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span>Upload Progress</span>
            <span>{progress}%</span>
          </div>
          <Progress value={progress} className="w-full" />
        </div>
      )}
      
      {/* Error message */}
      {error && !isUploading && (
        <div className="flex items-center gap-2 p-3 bg-destructive/10 border border-destructive/20 rounded-md">
          <AlertCircle className="w-4 h-4 text-destructive flex-shrink-0" />
          <p className="text-sm text-destructive">{error}</p>
        </div>
      )}
      
      {/* Success message */}
      {uploadedImageUrl && !isUploading && (
        <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-md">
          <CheckCircle className="w-4 h-4 text-green-600 flex-shrink-0" />
          <div className="flex-1">
            <p className="text-sm text-green-600 font-medium">Upload successful!</p>
            <p className="text-xs text-green-600/80 break-all">{uploadedImageUrl}</p>
          </div>
        </div>
      )}
      
      {/* File preview */}
      {selectedFile && (
        <div className="p-4 border rounded-lg bg-card">
          <div className="flex items-start gap-4">
            {/* Preview image */}
            <div className="relative flex-shrink-0">
              <img
                src={selectedFile.preview}
                alt="Preview"
                className="w-16 h-16 object-cover rounded border"
              />
              {uploadedImageUrl && (
                <div className="absolute -top-1 -right-1 w-5 h-5 bg-green-600 rounded-full flex items-center justify-center">
                  <CheckCircle className="w-3 h-3 text-white" />
                </div>
              )}
            </div>
            
            {/* File information */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between">
                <div className="space-y-1">
                  <p className="font-medium text-sm truncate">{selectedFile.file.name}</p>
                  <p className="text-xs text-muted-foreground">
                    {formatFileSize(selectedFile.file.size)} • {selectedFile.file.type}
                  </p>
                  {uploadedImageUrl && (
                    <p className="text-xs text-green-600">✓ Upload completed</p>
                  )}
                </div>
                
                {/* Clear button */}
                <Button
                  onClick={clearSelection}
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0"
                  disabled={isUploading}
                >
                  <X className="w-3 h-3" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FileUpload;