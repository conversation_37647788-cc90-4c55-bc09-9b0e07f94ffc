import React from 'react';
import { Skeleton } from "@/components/ui/skeleton";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Loader2, ImageIcon } from "lucide-react";
import { ImageMetadata } from '@/hooks/useImageSearch';

interface ImageGridProps {
  images: ImageMetadata[];
  onSelect: (image: ImageMetadata) => Promise<void>;
  loading: boolean;
  uploadingId: string | null;
  disabled: boolean;
}

export const ImageGrid: React.FC<ImageGridProps> = ({ images, onSelect, loading, uploadingId, disabled }) => {
  if (loading && images?.length === 0) {
    return (
      <ScrollArea className="h-[600px]">
        <div className="columns-2 md:columns-3 lg:columns-4 gap-4 space-y-4">
          {Array.from({ length: 12 }).map((_, index) => (
            <div key={index} className="break-inside-avoid mb-4">
              <Skeleton className={`w-full rounded-lg ${
                index % 3 === 0 ? 'h-48' : index % 3 === 1 ? 'h-64' : 'h-56'
              }`} />
            </div>
          ))}
        </div>
      </ScrollArea>
    );
  }

  if (images?.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-muted-foreground">
        <ImageIcon className="w-16 h-16 mb-4 opacity-50" />
        <p className="text-center text-base font-medium mb-2">No images found</p>
        <p className="text-center text-sm">Try different search terms or adjust your filters</p>
      </div>
    );
  }

  return (
    <ScrollArea className="h-[600px]">
      <div className="columns-2 md:columns-3 lg:columns-4 gap-4 space-y-4">
      {images?.map((image) => {
        const isUploading = uploadingId === image.id;
        const isDisabled = disabled && !isUploading;
        
        return (
          <div 
            key={image.id} 
            className={`relative group break-inside-avoid mb-4 ${
              isDisabled ? 'cursor-not-allowed' : 'cursor-pointer'
            }`}
          >
            <div className="relative overflow-hidden rounded-lg bg-muted">
              <img
                src={image.previewUrl}
                alt="Search result"
                className={`w-full h-auto object-cover transition-all duration-300 ${
                  isUploading 
                    ? 'opacity-50 scale-105' 
                    : isDisabled 
                    ? 'opacity-50 grayscale' 
                    : 'group-hover:opacity-90 group-hover:scale-105'
                }`}
                onClick={() => !disabled && onSelect(image)}
                loading="lazy"
              />
              
              {/* Overlay */}
              <div className={`absolute inset-0 bg-black/20 transition-all duration-300 ${
                isUploading 
                  ? 'opacity-100' 
                  : isDisabled 
                  ? 'opacity-0' 
                  : 'opacity-0 group-hover:opacity-100'
              }`} />
              
              {/* Action Button/Loading */}
              <div className={`absolute inset-0 flex items-center justify-center transition-all duration-300 ${
                isUploading 
                  ? 'opacity-100' 
                  : isDisabled 
                  ? 'opacity-0' 
                  : 'opacity-0 group-hover:opacity-100'
              }`}>
                {isUploading ? (
                  <div className="bg-white/90 backdrop-blur-sm rounded-full p-3 shadow-lg">
                    <Loader2 className="h-6 w-6 animate-spin text-primary" />
                  </div>
                ) : !disabled && (
                  <button 
                    className="bg-primary hover:bg-primary/90 text-primary-foreground px-4 py-2 rounded-full text-sm font-medium shadow-lg backdrop-blur-sm transition-all duration-200 hover:scale-105"
                    onClick={(e) => {
                      e.stopPropagation();
                      onSelect(image);
                    }}
                  >
                    Choose Image
                  </button>
                )}
              </div>
              
              {/* Image dimensions badge */}
              {image.width && image.height && (
                <div className="absolute top-2 right-2 bg-black/60 text-white text-xs px-2 py-1 rounded-full backdrop-blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  {image.width} × {image.height}
                </div>
              )}
            </div>
          </div>
        );
      })}
      </div>
    </ScrollArea>
  );
};