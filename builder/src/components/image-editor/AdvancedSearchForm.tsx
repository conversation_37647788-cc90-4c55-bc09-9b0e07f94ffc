import React from 'react';
import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Search, RotateCcw, Settings2 } from 'lucide-react';
import { SearchParams } from '@/hooks/useImageSearch';

interface AdvancedSearchFormProps {
  onSearch: (params: SearchParams) => void;
}

const imageTypes = [
  { value: 'all', label: 'All Types' },
  { value: 'photo', label: 'Photos' },
  { value: 'illustration', label: 'Illustrations' },
  { value: 'vector', label: 'Vectors' }
];

const orientations = [
  { value: 'all', label: 'All Orientations' },
  { value: 'horizontal', label: 'Horizontal' },
  { value: 'vertical', label: 'Vertical' }
];

const categories = [
  { value: 'all', label: 'All Categories' },
  { value: 'backgrounds', label: 'Backgrounds' },
  { value: 'fashion', label: 'Fashion' },
  { value: 'nature', label: 'Nature' },
  { value: 'science', label: 'Science' },
  { value: 'education', label: 'Education' },
  { value: 'feelings', label: 'Feelings' },
  { value: 'health', label: 'Health' },
  { value: 'people', label: 'People' },
  { value: 'religion', label: 'Religion' },
  { value: 'places', label: 'Places' },
  { value: 'animals', label: 'Animals' },
  { value: 'industry', label: 'Industry' },
  { value: 'computer', label: 'Computer' },
  { value: 'food', label: 'Food' },
  { value: 'sports', label: 'Sports' },
  { value: 'transportation', label: 'Transportation' },
  { value: 'travel', label: 'Travel' },
  { value: 'buildings', label: 'Buildings' },
  { value: 'business', label: 'Business' },
  { value: 'music', label: 'Music' }
];

const colors = [
  { value: 'grayscale', label: 'Grayscale', color: '#808080' },
  { value: 'transparent', label: 'Transparent', color: 'transparent' },
  { value: 'red', label: 'Red', color: '#ff0000' },
  { value: 'orange', label: 'Orange', color: '#ffa500' },
  { value: 'yellow', label: 'Yellow', color: '#ffff00' },
  { value: 'green', label: 'Green', color: '#008000' },
  { value: 'turquoise', label: 'Turquoise', color: '#40e0d0' },
  { value: 'blue', label: 'Blue', color: '#0000ff' },
  { value: 'lilac', label: 'Purple', color: '#c8a2c8' },
  { value: 'pink', label: 'Pink', color: '#ffc0cb' },
  { value: 'white', label: 'White', color: '#ffffff' },
  { value: 'gray', label: 'Gray', color: '#808080' },
  { value: 'black', label: 'Black', color: '#000000' },
  { value: 'brown', label: 'Brown', color: '#a52a2a' }
];

const orderOptions = [
  { value: 'popular', label: 'Popular' },
  { value: 'latest', label: 'Latest' }
];

export default function AdvancedSearchForm({ onSearch }: AdvancedSearchFormProps) {
  const [selectedColors, setSelectedColors] = React.useState<string[]>([]);

  const { register, handleSubmit, reset, setValue, watch } = useForm<SearchParams>({
    defaultValues: {
      q: '',
      image_type: 'photo',
      orientation: 'all',
      category: 'all',
      min_width: 0,
      min_height: 0,
      colors: [],
      editors_choice: false,
      safesearch: true,
      order: 'popular',
      per_page: 20
    }
  });

  const watchedValues = watch();

  const onSubmit = (data: SearchParams) => {
    const searchParams = {
      ...data,
      colors: selectedColors.length > 0 ? selectedColors : undefined,
      min_width: data.min_width || undefined,
      min_height: data.min_height || undefined,
      category: data.category || undefined
    };
    onSearch(searchParams);
  };

  const handleReset = () => {
    reset();
    setSelectedColors([]);
  };

  const handleColorToggle = (colorValue: string) => {
    setSelectedColors(prev => 
      prev.includes(colorValue) 
        ? prev.filter(c => c !== colorValue)
        : [...prev, colorValue]
    );
  };

  return (
    <Card className="w-full">
      <CardContent className="pt-6">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* 第一行：主要搜索控件 */}
          <div className="flex gap-4 items-end">
            {/* 搜索关键词 - 占据更多空间 */}
            <div className="flex-1">
              <Input
                id="search-query"
                {...register('q', { required: true })}
                placeholder="Enter search keywords..."
                className="mt-1"
              />
            </div>

            {/* 图片类型 */}
            <div className="w-40">
              <Select 
                value={watchedValues.image_type} 
                onValueChange={(value) => setValue('image_type', value)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {imageTypes.map(type => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* 图片方向 */}
            <div className="w-32">
              <Select 
                value={watchedValues.orientation} 
                onValueChange={(value) => setValue('orientation', value)}
              >
                <SelectTrigger className="mt-1 h-9">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {orientations.map(orientation => (
                    <SelectItem key={orientation.value} value={orientation.value}>
                      {orientation.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* 搜索按钮 */}
            <Button 
              type="submit" 
              disabled={!watchedValues.q} 
              className="px-8"
            >
              <Search className="mr-2 h-4 w-4" />
              Search
            </Button>

            {/* 高级选项 */}
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="ghost" className="ml-2">
                  <Settings2 className="h-4 w-4" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="space-y-4 mt-4">
                {/* 尺寸过滤 */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Input
                      id="min-width"
                      type="number"
                      {...register('min_width', { valueAsNumber: true })}
                      placeholder="0"
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Input
                      id="min-height"
                      type="number"
                      {...register('min_height', { valueAsNumber: true })}
                      placeholder="0"
                      className="mt-1"
                    />
                  </div>
                </div>

                {/* 颜色过滤 */}
                <div>
                  <div className="grid grid-cols-7 gap-2 mt-2">
                    {colors.map(color => (
                      <div key={color.value} className="flex flex-col items-center">
                        <button
                          type="button"
                          onClick={() => handleColorToggle(color.value)}
                          className={`w-8 h-8 rounded-full border-2 transition-all ${
                            selectedColors.includes(color.value) 
                              ? 'border-blue-500 ring-2 ring-blue-200' 
                              : 'border-gray-300 hover:border-gray-400'
                          }`}
                          style={{ 
                            backgroundColor: color.color === 'transparent' ? '#ffffff' : color.color,
                            backgroundImage: color.color === 'transparent' 
                              ? 'linear-gradient(45deg, #ccc 25%, transparent 25%), linear-gradient(-45deg, #ccc 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #ccc 75%), linear-gradient(-45deg, transparent 75%, #ccc 75%)'
                              : undefined,
                            backgroundSize: color.color === 'transparent' ? '8px 8px' : undefined,
                            backgroundPosition: color.color === 'transparent' ? '0 0, 0 4px, 4px -4px, -4px 0px' : undefined
                          }}
                        />
                        <span className="text-xs mt-1 text-center">{color.label}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 排序和每页数量 */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Select 
                      value={watchedValues.order} 
                      onValueChange={(value) => setValue('order', value)}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {orderOptions.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Select 
                      value={watchedValues.per_page?.toString()} 
                      onValueChange={(value) => setValue('per_page', parseInt(value))}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="10">10</SelectItem>
                        <SelectItem value="20">20</SelectItem>
                        <SelectItem value="30">30</SelectItem>
                        <SelectItem value="50">50</SelectItem>
                        <SelectItem value="100">100</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* 分类 */}
                <div>
                  <Select 
                    value={watchedValues.category || 'all'} 
                    onValueChange={(value) => setValue('category', value)}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map(category => (
                        <SelectItem key={category.value} value={category.value}>
                          {category.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* 快速选项 */}
                <div className="flex items-center gap-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="editors-choice-quick"
                      checked={watchedValues.editors_choice}
                      onCheckedChange={(checked) => setValue('editors_choice', !!checked)}
                    />
                    <Label htmlFor="editors-choice-quick" className="text-sm">Editor&apos;s Choice</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="safesearch-quick"
                      checked={watchedValues.safesearch}
                      onCheckedChange={(checked) => setValue('safesearch', !!checked)}
                    />
                    <Label htmlFor="safesearch-quick" className="text-sm">Safe Search</Label>
                  </div>
                </div>

                {/* 重置按钮 */}
                <Button 
                  type="button" 
                  variant="outline" 
                  size="sm"
                  onClick={handleReset}
                  className="ml-auto"
                >
                  <RotateCcw className="h-4 w-4 mr-1" />
                  Reset
                </Button>
              </PopoverContent>
            </Popover>
          </div>


        </form>
      </CardContent>
    </Card>
  );
}
