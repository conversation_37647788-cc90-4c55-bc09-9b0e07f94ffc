import React, { useState } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import AdvancedSearchForm from './AdvancedSearchForm';
import { ImageGrid } from './ImageGrid';
import { useImageSearch, SearchParams, ImageMetadata } from '@/hooks/useImageSearch';

export interface ImageSelectData {
  result?: {
    id: string;
    url?: string;
    filename?: string;
  };
  error?: string;
}

interface ImageEditorProps {
  onChange: (result: ImageSelectData, editPos?: any) => void;
  domain?: string;
  editPos?: any;
}

export const ImageEditor: React.FC<ImageEditorProps> = ({ onChange, domain, editPos }) => {
  const [searchPerformed, setSearchPerformed] = useState(false);
  const [uploadingId, setUploadingId] = useState<string | null>(null);
  const [uploadError, setUploadError] = useState<string | null>(null);
  
  const {
    images,
    loading,
    error,
    total,
    totalHits,
    currentPage,
    searchImages,
    loadMore,
    selectAndUploadImage
  } = useImageSearch(
    domain || 'default',
    (result) => {
      console.log('upload success', result);
      onChange(result, editPos);
      setUploadingId(null);
      setUploadError(null);
    }
  );

  const handleSearch = async (params: SearchParams) => {
    setSearchPerformed(true);
    await searchImages(params);
  };

  const handleSelectAndUpload = async (image: ImageMetadata) => {
    if (!uploadingId) {
      setUploadingId(image.id);
      setUploadError(null);
      try {
        await selectAndUploadImage(image);
      } catch (error) {
        setUploadError("Failed to upload image. Please try again.");
        setUploadingId(null);
      }
    }
  };

  const isUploading = uploadingId !== null;

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <AdvancedSearchForm onSearch={handleSearch} />
        
        {uploadError && (
          <Alert variant="destructive">
            <AlertDescription>Upload failed: {uploadError}</AlertDescription>
          </Alert>
        )}
        
        {error && (
          <Alert variant="destructive">
            <AlertDescription>Search failed: {error}</AlertDescription>
          </Alert>
        )}
      </div>

      {searchPerformed && (
        <div className="space-y-4">
          {images.length > 0 && (
            <div className="text-sm text-gray-600">
              Showing {images.length} of {totalHits} images
            </div>
          )}
          
          <ImageGrid
            images={images}
            onSelect={handleSelectAndUpload}
            loading={loading}
            uploadingId={uploadingId}
            disabled={isUploading}
          />
          
          {images.length > 0 && images.length < totalHits && (
            <div className="flex justify-center">
              <Button 
                onClick={loadMore} 
                disabled={loading}
                variant="outline"
              >
                {loading ? 'Loading...' : `Load More (Page ${currentPage + 1})`}
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ImageEditor;