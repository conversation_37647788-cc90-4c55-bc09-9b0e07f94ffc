'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { PlusCircle, Globe, ExternalLink, Settings, Calendar } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { format } from 'date-fns';

import { WebsiteModel } from '@/modules/website/model';

interface WebsiteListViewProps {
  websites: WebsiteModel[];
}

const STARTER_PLAN_LIMIT = 3;

export function WebsiteListView({ websites }: WebsiteListViewProps) {
  const router = useRouter();
  const [isCreating, setIsCreating] = useState(false);
  
  const canCreateMore = websites.length < STARTER_PLAN_LIMIT;
  const remainingSlots = STARTER_PLAN_LIMIT - websites.length;

  const handleCreateWebsite = async () => {
    if (!canCreateMore) return;
    
    setIsCreating(true);
    try {
      router.push('/website/create');
    } catch (error) {
      console.error('Failed to navigate to website creation:', error);
    } finally {
      setIsCreating(false);
    }
  };

  const handleEditWebsite = (websiteId: string) => {
    router.push(`/site/${websiteId}`);
  };

  const handleViewWebsite = (domain: string | undefined) => {
    if (domain) {
      window.open(`https://${domain}.lit.page`, '_blank');
    }
  };

  const getStatusColor = (website: WebsiteModel) => {
    // Determine status based on domain and custom domain
    if (website.customDomain?.status === 'ACTIVE' || website.domain) {
      return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
    } else if (website.customDomain?.status === 'PENDING') {
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
    } else {
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  const getStatusText = (website: WebsiteModel) => {
    if (website.customDomain?.status === 'ACTIVE') {
      return 'Published';
    } else if (website.customDomain?.status === 'PENDING') {
      return 'Pending';
    } else if (website.domain) {
      return 'Published';
    } else {
      return 'Draft';
    }
  };

  return (
    <div className="max-w-7xl mx-auto">
      {/* Create Button - Only show when can create more */}
      {canCreateMore && (
        <div className="text-center mb-12">
          <Button 
            onClick={handleCreateWebsite}
            disabled={isCreating}
            size="lg"
            className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg hover:shadow-xl transition-all duration-200 px-8 py-3"
          >
            <PlusCircle className="w-5 h-5 mr-2" />
            {isCreating ? 'Creating...' : 'Create New Website'}
          </Button>
        </div>
      )}

      {/* Status Cards */}
      <div className="max-w-2xl mx-auto mb-10">
        {!canCreateMore && (
          <Card className="border-amber-700/50 bg-gradient-to-r from-amber-900/20 to-orange-900/20 shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-10 h-10 bg-amber-900/40 rounded-full flex items-center justify-center">
                  <svg className="w-5 h-5 text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <div>
                  <h3 className="font-semibold text-amber-100 mb-1">
                    Website Limit Reached
                  </h3>
                  <p className="text-amber-200 text-sm leading-relaxed">
                    You&apos;ve reached the limit of {STARTER_PLAN_LIMIT} websites for the starter plan. 
                    <span className="font-medium">Upgrade to Pro</span> to create up to 9 websites with AI-powered features.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {canCreateMore && remainingSlots > 0 && (
          <Card className="border-blue-700/50 bg-gradient-to-r from-blue-900/20 to-indigo-900/20 shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-10 h-10 bg-blue-900/40 rounded-full flex items-center justify-center">
                  <svg className="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <h3 className="font-semibold text-blue-100 mb-1">
                    Available Slots
                  </h3>
                  <p className="text-blue-200 text-sm leading-relaxed">
                    You can create {remainingSlots} more website{remainingSlots > 1 ? 's' : ''} in your starter plan.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Website Grid */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8">
        {websites.map((website) => (
          <Card key={website.id} className="group border-0 shadow-lg bg-slate-800/90 backdrop-blur-sm hover:shadow-2xl hover:scale-[1.02] transition-all duration-300 overflow-hidden">
            <div className="relative">
              {/* Thumbnail */}
              <div className="w-full h-48 bg-gradient-to-br from-gray-700 to-gray-800 flex items-center justify-center relative overflow-hidden">
                {website.logo ? (
                  <img 
                    src={website.logo} 
                    alt={website.name}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                ) : (
                  <div className="flex flex-col items-center justify-center text-gray-500">
                    <Globe className="w-12 h-12 mb-2" />
                    <span className="text-sm font-medium">No Preview</span>
                  </div>
                )}
                
                {/* Status Badge Overlay */}
                <div className="absolute top-3 right-3">
                  <Badge className={`${getStatusColor(website)} shadow-sm`}>
                    {getStatusText(website)}
                  </Badge>
                </div>
              </div>
            </div>
            
            <CardHeader className="pb-3">
              <div>
                <CardTitle className="text-xl font-bold text-white mb-2 line-clamp-1">
                  {website.name}
                </CardTitle>
                {website.domain && (
                  <div className="flex items-center gap-2 text-sm text-gray-300">
                    <Globe className="w-4 h-4 flex-shrink-0" />
                    <span className="truncate">{website.domain}</span>
                  </div>
                )}
              </div>
            </CardHeader>
            
            <CardContent className="pt-0">
              {/* Metadata */}
              <div className="flex items-center gap-2 text-xs text-gray-400 mb-6">
                <Calendar className="w-3 h-3" />
                <span>Created {website.createdAt ? format(new Date(website.createdAt), 'MMM d, yyyy') : 'Unknown'}</span>
              </div>
              
              {/* Actions */}
              <div className="flex gap-3">
                <Button 
                  onClick={() => handleEditWebsite(website.id ?? '')}
                  variant="outline"
                  size="sm"
                  className="flex-1 hover:bg-gray-700 transition-colors border-gray-600 text-gray-300 hover:text-white"
                >
                  <Settings className="w-4 h-4 mr-2" />
                  Edit
                </Button>
                {(website.domain || website.customDomain?.status === 'ACTIVE') && (
                  <Button 
                    onClick={() => handleViewWebsite(website.customDomain?.domain || website.domain)}
                    variant="outline"
                    size="sm"
                    className="flex-1 hover:bg-blue-900/20 hover:text-blue-400 hover:border-blue-600 transition-colors border-gray-600 text-gray-300"
                  >
                    <ExternalLink className="w-4 h-4 mr-2" />
                    View
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
        
        {/* Create New Website Card - Only show when under limit */}
        {websites.length < STARTER_PLAN_LIMIT && (
          <Card className="group border-2 border-dashed border-gray-600 bg-gradient-to-br from-slate-800/50 to-slate-700/50 hover:border-blue-500 hover:shadow-lg hover:scale-[1.02] transition-all duration-300 cursor-pointer" onClick={handleCreateWebsite}>
            <CardContent className="p-8 flex flex-col items-center justify-center text-center h-full min-h-[320px]">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-900/30 to-blue-800/30 rounded-2xl mb-6 group-hover:scale-110 transition-transform duration-300">
                <PlusCircle className="w-10 h-10 text-blue-400" />
              </div>
              <h3 className="text-xl font-bold text-white mb-3">
                Create New Website
              </h3>
              <p className="text-gray-300 text-sm leading-relaxed max-w-xs">
                Start building your next amazing website with our intuitive tools
              </p>
            </CardContent>
          </Card>
        )}
       </div>
     </div>
   );
}