'use client';

import { useState, useEffect } from 'react';
import { useFetchWebsites } from '@/lib/hooks/use-website';
import { StarterHeader } from './header/StarterHeader';
import { EmptyStateGuide } from './empty-state/EmptyStateGuide';
import { WebsiteListView } from './website-management/WebsiteListView';
import { UpgradePromotionBanner } from './upgrade-promotion/UpgradePromotionBanner';
import { LoadingSpinner } from './shared/LoadingSpinner';

export function StarterDashboardContent() {
  const { data: websites, isLoading, error } = useFetchWebsites();
  const [isEmpty, setIsEmpty] = useState(false);

  useEffect(() => {
    if (websites) {
      setIsEmpty(websites.length === 0);
    }
  }, [websites]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700">
        <StarterHeader />
        <main className="container mx-auto px-4 py-12">
          <LoadingSpinner />
        </main>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700">
        <StarterHeader />
        <main className="container mx-auto px-4 py-12">
          <div className="text-center">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-red-900/30 rounded-full mb-4">
              <svg className="w-8 h-8 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">
              Failed to load websites
            </h3>
            <p className="text-gray-400">
              Please try refreshing the page or contact support if the problem persists.
            </p>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700">
      <StarterHeader websiteCount={websites?.length || 0} planLimit={3} />
      
      <main className="container mx-auto px-4 py-8">
        {/* Main Content */}
        {isEmpty ? (
          <EmptyStateGuide />
        ) : (
          <>
            <WebsiteListView websites={websites || []} />
            
            {/* Upgrade Promotion Banner - Moved to bottom */}
            <div className="mt-12">
              <UpgradePromotionBanner />
            </div>
          </>
        )}
      </main>
    </div>
  );
}