'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Plus, Sparkles, Zap, Globe, Palette, ArrowRight } from 'lucide-react';
import { useRouter } from 'next/navigation';

export function EmptyStateGuide() {
  const router = useRouter();
  
  const handleCreateWebsite = () => {
    router.push('/website/create');
  };

  const features = [
    {
      icon: Palette,
      title: 'Beautiful Templates',
      description: 'Choose from professionally designed templates that look great on any device'
    },
    {
      icon: Zap,
      title: 'Quick Setup',
      description: 'Get your website online in minutes with our intuitive drag-and-drop builder'
    },
    {
      icon: Globe,
      title: 'Custom Domain',
      description: 'Connect your own domain name and establish your professional online presence'
    }
  ];

  return (
    <div className="max-w-6xl mx-auto px-4 py-16">
      {/* Hero Section */}
      <div className="text-center mb-16">
        <div className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full px-6 py-3 shadow-lg mb-8">
          <Sparkles className="w-4 h-4" />
          <span className="text-sm font-semibold">Get Started Today</span>
        </div>
        
        <h1 className="text-5xl lg:text-6xl font-bold text-gray-900 mb-8 leading-tight">
          Create Your First
          <br className="hidden sm:block" />
          <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Amazing Website
          </span>
        </h1>
        
        <p className="text-xl text-gray-600 mb-10 max-w-3xl mx-auto leading-relaxed">
          Welcome to LitPage! Transform your ideas into stunning websites with our powerful yet simple website builder. 
          No coding experience required – just your creativity.
        </p>
        
        <Button 
          onClick={handleCreateWebsite}
          size="lg"
          className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-bold px-10 py-4 shadow-xl hover:shadow-2xl transition-all duration-300 group text-lg"
        >
          <Plus className="w-6 h-6 mr-3" />
          Create Your First Website
          <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
        </Button>
      </div>

      {/* Features */}
      <div className="grid md:grid-cols-3 gap-8 mb-16">
        {features.map((feature, index) => {
          const Icon = feature.icon;
          return (
            <Card key={index} className="border-0 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 bg-gradient-to-br from-white to-gray-50">
              <CardContent className="p-8 text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-blue-500 via-purple-600 to-indigo-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                  <Icon className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">{feature.title}</h3>
                <p className="text-gray-600 leading-relaxed">{feature.description}</p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Additional Info */}
      <div className="text-center">
        <div className="inline-flex items-center gap-6 bg-white rounded-2xl shadow-lg px-8 py-6 border border-gray-100">
          <div className="flex items-center gap-2 text-green-600">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="font-medium">Free to start</span>
          </div>
          <div className="flex items-center gap-2 text-blue-600">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span className="font-medium">No credit card required</span>
          </div>
          <div className="flex items-center gap-2 text-purple-600">
            <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
            <span className="font-medium">Publish instantly</span>
          </div>
        </div>
      </div>
    </div>
  );
}