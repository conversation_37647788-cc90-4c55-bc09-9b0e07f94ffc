'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Sparkles, Zap, Target, Globe, ArrowRight } from 'lucide-react';

// 临时配置 - 控制升级功能的显示
const UPGRADE_CONFIG = {
  showUpgradeButton: false, // 设置为 true 显示升级按钮
  showUpgradeText: false,   // 设置为 true 显示升级相关文案
  comingSoonMode: true      // 设置为 true 显示 Coming Soon 模式
};

interface UpgradePromotionBannerProps {
  showUpgradeButton?: boolean;
  showUpgradeText?: boolean;
  comingSoonMode?: boolean;
}

export function UpgradePromotionBanner({
  showUpgradeButton = UPGRADE_CONFIG.showUpgradeButton,
  showUpgradeText = UPGRADE_CONFIG.showUpgradeText,
  comingSoonMode = UPGRADE_CONFIG.comingSoonMode
}: UpgradePromotionBannerProps = {}) {
  const handleUpgrade = () => {
    // TODO: Implement upgrade flow
    console.log('User wants to upgrade to Pro plan');
  };

  return (
    <Card className="border-0 bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-600 text-white shadow-2xl overflow-hidden relative">
      {/* Background decoration */}
      <div className="absolute inset-0">
        <div className="absolute top-0 right-0 w-96 h-96 bg-white/5 rounded-full -translate-y-48 translate-x-48"></div>
        <div className="absolute bottom-0 left-0 w-64 h-64 bg-white/5 rounded-full translate-y-32 -translate-x-32"></div>
        <div className="absolute top-1/2 left-1/2 w-32 h-32 bg-white/5 rounded-full -translate-x-16 -translate-y-16"></div>
      </div>
      
      <CardContent className="relative p-8 lg:p-12">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-10">
            <div className="inline-flex items-center gap-2 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 mb-6">
              <Sparkles className="w-4 h-4 text-yellow-300" />
              <span className="text-sm font-medium">
                {comingSoonMode ? 'Coming Soon' : (showUpgradeText ? 'Upgrade to Pro' : 'Pro Features')}
              </span>
            </div>
            
            <h2 className="text-4xl lg:text-5xl font-bold mb-6 leading-tight">
              {comingSoonMode ? 'Exciting Features' : 'Unlock AI-Powered'}
              <br className="hidden sm:block" />
              <span className="bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent">
                {comingSoonMode ? 'Coming Soon' : 'Website Creation'}
              </span>
            </h2>
            
            <p className="text-xl text-indigo-100 mb-8 leading-relaxed max-w-2xl mx-auto">
              {comingSoonMode 
                ? 'We\'re working on amazing new features to enhance your website building experience. Stay tuned for updates!'
                : 'Transform your web development experience with advanced AI capabilities, up to 9 websites, and professional features.'
              }
            </p>
          </div>
          
          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-10">
            <div className="text-center">
              <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Sparkles className="w-8 h-8 text-yellow-300" />
              </div>
              <h3 className="font-semibold mb-2">AI Website Generation</h3>
              <p className="text-sm text-indigo-200">Create stunning websites with AI assistance</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Zap className="w-8 h-8 text-blue-300" />
              </div>
              <h3 className="font-semibold mb-2">AI Content Writing</h3>
              <p className="text-sm text-indigo-200">Generate compelling content automatically</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Target className="w-8 h-8 text-green-300" />
              </div>
              <h3 className="font-semibold mb-2">AI SEO Optimization</h3>
              <p className="text-sm text-indigo-200">Boost your search rankings with AI</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Globe className="w-8 h-8 text-purple-300" />
              </div>
              <h3 className="font-semibold mb-2">Up to 9 Websites</h3>
                <p className="text-sm text-indigo-200">Create up to 9 professional websites</p>
            </div>
          </div>
          
          {/* CTA */}
          <div className="text-center">
            {showUpgradeButton && !comingSoonMode ? (
              <>
                <Button 
                  onClick={handleUpgrade}
                  size="lg"
                  className="bg-white text-indigo-600 hover:bg-gray-100 font-bold px-10 py-4 shadow-xl hover:shadow-2xl transition-all duration-300 group"
                >
                  Upgrade to Pro
                  <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
                </Button>
                <p className="text-sm text-indigo-200 mt-4">
                  Join thousands of creators building amazing websites
                </p>
              </>
            ) : (
              <p className="text-lg text-indigo-100 font-medium">
                {comingSoonMode 
                  ? 'Pro features coming soon! Stay tuned for exciting updates.'
                  : 'Advanced features available in Pro plan'
                }
              </p>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}