'use client';

import { Badge } from '@/components/ui/badge';
import { Sparkles } from 'lucide-react';

interface StarterHeaderProps {
  websiteCount?: number;
  planLimit?: number;
}

export function StarterHeader({ websiteCount = 0, planLimit = 2 }: StarterHeaderProps) {
  return (
    <div className="text-center py-12 bg-gradient-to-b from-gray-900 to-gray-800">
      <div className="max-w-2xl mx-auto px-4">
        <div className="inline-flex items-center gap-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-full px-6 py-3 shadow-lg mb-4">
          <Sparkles className="w-4 h-4" />
          <span className="font-semibold text-sm">LitPage Starter Plan</span>
        </div>
        
        <h1 className="text-3xl lg:text-4xl font-bold text-white mb-3">
          My Websites
        </h1>
        
        <p className="text-lg text-gray-300 leading-relaxed mb-2">
          You have {websiteCount} of {planLimit} websites in your starter plan
        </p>
        
        <p className="text-base text-gray-400">
          Create and manage your websites with our intuitive platform. Start building your online presence today.
        </p>
      </div>
    </div>
  );
}