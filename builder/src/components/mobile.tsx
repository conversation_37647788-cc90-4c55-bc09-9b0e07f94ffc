"use client";

import React, { useRef, useEffect } from 'react';
import Logger from '@/lib/logger';

const logger = new Logger('MobileIframe');

interface IframeComponentProps {
  config: any;
}

const IframeComponent: React.FC<IframeComponentProps> = ({ config }) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // 添加调试日志，查看接收到的配置
  console.log('[ MobileIframe ] Received config:', {
    domain: config.domain,
    language: config.language,
    hasHeaders: !!config.headers,
    hasFooters: !!config.footers,
    hasSchema: !!config.schema,
    // 多语言相关数据
    hasLanguageInfo: !!config.languageInfo,
    hasLanguageVersions: !!config.languageVersions,
    hasLanguageUrls: !!config.languageUrls,
    hasSeo: !!config.seo
  });

  // 如果有多语言数据，打印详细信息
  if (config.languageInfo) {
    console.log('[ MobileIframe ] Language info details:', config.languageInfo);
  }
  if (config.languageVersions) {
    console.log('[ MobileIframe ] Language versions count:', Object.keys(config.languageVersions).length);
  }
  if (config.languageUrls) {
    console.log('[ MobileIframe ] Language URLs count:', Object.keys(config.languageUrls).length);
  }

  useEffect(() => {
    const iframe = iframeRef.current;
    const targetOrigin = window.location.origin;

    const sendMessage = () => {
      if (iframe?.contentWindow) {
        logger.info('[ MobileIframe ] Sending data to mobile preview', {
          dataSize: JSON.stringify(config).length,
          hasHeaders: !!config.headers,
          hasFooters: !!config.footers,
          language: config.language,
          theme: config.theme,
          hasLanguageInfo: !!config.languageInfo,
          hasLanguageVersions: !!config.languageVersions,
          hasLanguageUrls: !!config.languageUrls,
          hasSeo: !!config.seo
        });
        
        // 记录头尾数据的关键部分
        if (config.headers && config.language && config.headers[config.language]) {
          const headerData = config.headers[config.language];
          logger.info('[ MobileIframe ] Header data details', {
            variant: headerData.variant,
            linksCount: headerData.links?.length || 0,
            actionsCount: headerData.actions?.length || 0,
            logo: headerData.logo
          });
        } else {
          logger.warn('[ MobileIframe ] Missing or invalid header data', {});
        }

        // 记录多语言数据详情
        if (config.languageInfo) {
          logger.info('[ MobileIframe ] Language info details', {
            currentLanguage: config.languageInfo.currentLanguage,
            defaultLanguage: config.languageInfo.defaultLanguage,
            supportedLanguages: config.languageInfo.supportedLanguages,
            translatedLanguages: config.languageInfo.translatedLanguages
          });
        }
        
        iframe.contentWindow.postMessage(config, targetOrigin);
        logger.info('[ MobileIframe ] Message posted to iframe', {});
      } else {
        logger.error('[ MobileIframe ] Cannot send message: contentWindow not available', {});
      }
    };

    const messageHandler = (event: MessageEvent) => {
      if (event.origin === window.location.origin) {
        if (event.data === 'onEventReady') {
          logger.info('[ MobileIframe ] Received ready event from mobile preview', {});
          sendMessage();
          window.removeEventListener('message', messageHandler);
          logger.info('[ MobileIframe ] Message handler removed after ready event', {});
        } else {
          logger.debug('[ MobileIframe ] Received other message from iframe', event.data);
        }
      } else {
        logger.warn('[ MobileIframe ] Received message from unknown origin', { origin: event.origin });
      }
    };

    if (iframe) {
      logger.info('[ MobileIframe ] Setting up message listener', {});
      window.addEventListener('message', messageHandler);
      if (iframe.contentWindow && iframe.src) {
        logger.info('[ MobileIframe ] Iframe ready, sending initial message', {});
        sendMessage();
      } else {
        logger.warn('[ MobileIframe ] Iframe not fully loaded yet', {});
      }
    } else {
      logger.error('[ MobileIframe ] Iframe reference not available', {});
    }

    return () => {
      logger.info('[ MobileIframe ] Cleaning up message listener', {});
      window.removeEventListener('message', messageHandler);
    };
  }, [config]);

  return (
    <div className='w-full h-full'>
      <iframe
        ref={iframeRef}
        src={`/mobile-preview`}
        className='w-full h-full border-0 rounded-md bg-transparent'
        // style={{ height: "calc(100vh - 328px)" }}
      />
    </div>
  );
}

export default IframeComponent;
