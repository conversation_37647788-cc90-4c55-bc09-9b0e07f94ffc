import React from 'react';
import { LucideProps } from 'lucide-react';
import * as LucideIcons from 'lucide-react';

export interface LucideIconProps {
  icon: React.ComponentType<LucideProps> | string;
  size?: number;
  strokeWidth?: number;
  color?: string;
  className?: string;
  x?: number;
  y?: number;
}

const LucideIcon: React.FC<LucideIconProps> = ({
  icon,
  size = 32,
  strokeWidth = 2,
  color = 'currentColor',
  className = '',
  x = 0,
  y = 0
}) => {
  // 处理字符串图标名称
  const IconComponent = typeof icon === 'string' 
    ? (LucideIcons as any)[icon] || LucideIcons.HelpCircle
    : icon;

  if (!IconComponent) {
    console.warn(`Icon "${icon}" not found in Lucide icons`);
    return (
      <g transform={`translate(${x}, ${y})`} className={className}>
        <LucideIcons.HelpCircle
          size={size}
          strokeWidth={strokeWidth}
          color={color}
        />
      </g>
    );
  }

  // 在 SVG 容器中渲染 Lucide 图标
  return (
    <g transform={`translate(${x}, ${y})`} className={className}>
      <IconComponent
        size={size}
        strokeWidth={strokeWidth}
        color={color}
      />
    </g>
  );
};

export default LucideIcon; 