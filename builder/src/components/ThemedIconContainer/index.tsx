import React, { forwardRef, useImperativeHandle, useRef } from 'react';

export interface ThemedIconContainerProps {
  size?: number;
  primaryColor?: string;
  className?: string;
  children?: React.ReactNode;
}

export interface ThemedIconContainerRef {
  getSVG: () => SVGSVGElement | null;
}

const ThemedIconContainer = forwardRef<ThemedIconContainerRef, ThemedIconContainerProps>(
  ({ size = 64, primaryColor = '#059669', className = '', children }, ref) => {
    const svgRef = useRef<SVGSVGElement>(null);
    
    useImperativeHandle(ref, () => ({
      getSVG: () => svgRef.current
    }));

    const gradientId = `gradient-${primaryColor.replace('#', '')}`;
    const strokeGradientId = `stroke-gradient-${primaryColor.replace('#', '')}`;

    return (
      <svg
        ref={svgRef}
        width={size}
        height={size}
        viewBox="0 0 64 64"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
        style={{ color: primaryColor }}
      >
        <defs>
          {/* 填充渐变 - 完全复制 a.svg 的效果 */}
          <radialGradient
            id={gradientId}
            cx="0"
            cy="0"
            r="1"
            gradientTransform="rotate(45) scale(90.5097)"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="currentColor" />
            <stop offset="1" stopColor="currentColor" stopOpacity="0.25" />
          </radialGradient>
          
          {/* 描边渐变 - 复制 a.svg 的描边效果 */}
          <radialGradient
            id={strokeGradientId}
            cx="0"
            cy="0"
            r="1"
            gradientTransform="rotate(45) scale(90.5097)"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="currentColor" />
            <stop offset="0.5" stopColor="currentColor" stopOpacity="0" />
            <stop offset="1" stopColor="currentColor" stopOpacity="0.25" />
          </radialGradient>
        </defs>
        
        {/* 背景矩形 - 完全复制 a.svg 的双矩形结构 */}
        <rect
          width="64"
          height="64"
          rx="16"
          fill={`url(#${gradientId})`}
          fillOpacity="0.4"
        />
        <rect
          width="63"
          height="63"
          x="0.5"
          y="0.5"
          rx="15.5"
          stroke={`url(#${strokeGradientId})`}
          strokeOpacity="0.5"
          fill="none"
        />
        
        {/* 图标内容区域 */}
        <g transform="translate(16, 16)">
          {children}
        </g>
      </svg>
    );
  }
);

ThemedIconContainer.displayName = 'ThemedIconContainer';

export default ThemedIconContainer; 