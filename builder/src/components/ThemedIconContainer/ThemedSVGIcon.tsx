import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import { LucideProps } from 'lucide-react';
import ThemedIconContainer, { ThemedIconContainerRef } from './index';
import LucideIcon from './LucideIcon';

export interface ThemedSVGIconProps {
  icon: React.ComponentType<LucideProps> | string;
  theme?: 'security' | 'access' | 'hosting';
  size?: number;
  iconSize?: number;
  strokeWidth?: number;
  primaryColor?: string;
  className?: string;
}

export interface ThemedSVGIconRef {
  getSVG: () => SVGSVGElement | null;
}

// 主题颜色映射
const THEME_COLORS = {
  security: '#059669',  // 绿色
  access: '#2563eb',    // 蓝色
  hosting: '#0891b2'    // 青色
};

const ThemedSVGIcon = forwardRef<ThemedSVGIconRef, ThemedSVGIconProps>(
  ({ 
    icon, 
    theme = 'security',
    size = 64, 
    iconSize = 32, 
    strokeWidth = 2, 
    primaryColor,
    className = '' 
  }, ref) => {
    const containerRef = useRef<ThemedIconContainerRef>(null);
    
    useImperativeHandle(ref, () => ({
      getSVG: () => containerRef.current?.getSVG() || null
    }));

    // 获取主题颜色
    const themeColor = primaryColor || THEME_COLORS[theme];

    // 计算图标在容器中的居中位置
    const iconX = (32 - iconSize) / 2;  // 32 是容器内容区域的宽度
    const iconY = (32 - iconSize) / 2;  // 32 是容器内容区域的高度

    return (
      <ThemedIconContainer
        ref={containerRef}
        size={size}
        primaryColor={themeColor}
        className={className}
      >
        <LucideIcon
          icon={icon}
          size={iconSize}
          strokeWidth={strokeWidth}
          color={themeColor}
          x={iconX}
          y={iconY}
        />
      </ThemedIconContainer>
    );
  }
);

ThemedSVGIcon.displayName = 'ThemedSVGIcon';

export default ThemedSVGIcon; 