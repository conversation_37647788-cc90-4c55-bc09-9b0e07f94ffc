'use client'
import { createContext, useContext, useState, ReactNode, useEffect } from 'react';

interface BlockEditorContextProps {
  children: ReactNode;
}

interface blockEditorState {
  curBlock: string;
  isEditorOpened: boolean;
  isThemeOpened: boolean;
  isPageThemeOpened: boolean;
  isCreateSectionOpened: boolean;
  scrollToBlockId: string | null;
  editorView: 'canvas' | 'seo';
}

const defaultState: blockEditorState = {
  curBlock: '',
  isEditorOpened: false,
  isThemeOpened: false,
  isPageThemeOpened: false,
  isCreateSectionOpened: false,
  scrollToBlockId: null,
  editorView: 'canvas',
};

interface ContextValue extends blockEditorState {
  setCurBlock: (cur: string) => void,
  setEditorOpened: (mode: boolean) => void,
  setThemeOpened: (mode: boolean) => void,
  setPageThemeOpened: (mode: boolean) => void,
  setCreateSectionOpened: (mode: boolean) => void,
  setScrollToBlockId: (id: string | null) => void,
  setEditorView: (view: 'canvas' | 'seo') => void,
}

const Context = createContext<ContextValue | undefined>(undefined);

export const useBlockEditor = () => {
  const context = useContext(Context);
  if (!context) {
    throw new Error('useBlockEditor must be used within a BlockEditorProvider');
  }
  return context;
};

export const BlockEditorProvider = ({ children }: BlockEditorContextProps) => {

  const [curBlock, setCurBlock] = useState<string>(defaultState.curBlock);
  const [isEditorOpened, setEditorOpened] = useState<boolean>(defaultState.isEditorOpened);
  const [isThemeOpened, setThemeOpened] = useState<boolean>(defaultState.isThemeOpened);
  const [isPageThemeOpened, setPageThemeOpened] = useState<boolean>(defaultState.isPageThemeOpened);
  const [isCreateSectionOpened, setCreateSectionOpened] = useState<boolean>(defaultState.isCreateSectionOpened);
  const [scrollToBlockId, setScrollToBlockId] = useState<string | null>(defaultState.scrollToBlockId);
  const [editorView, setEditorView] = useState<'canvas' | 'seo'>(defaultState.editorView);

  const ctx: ContextValue = {
    curBlock,
    isEditorOpened,
    isThemeOpened,
    isPageThemeOpened,
    isCreateSectionOpened,
    scrollToBlockId,
    editorView,
    setCurBlock,
    setEditorOpened,
    setThemeOpened,
    setPageThemeOpened,
    setCreateSectionOpened,
    setScrollToBlockId,
    setEditorView,
  };
 
  return <Context.Provider value={ctx}>{children}</Context.Provider>;
};
