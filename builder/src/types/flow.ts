import { Node, Edge } from '@xyflow/react';

export interface Website {
  id: string;
  name: string;
  description?: string;
  domain: string;
  createdAt: string;
  updatedAt: string;
  defaultLanguage?: string;
  supportedLanguages?: string[];
  actions?: string[];
  owner?: {
    id: string;
    name: string;
    email: string;
  };
}

export interface Page {
  id: string;
  nanoid: string;
  title: string;
  slug: string;
  description?: string;
  pageType: string;
  status: 'draft' | 'published' | 'archived';
  createdAt: string;
  updatedAt: string;
  websiteId: string;
  workspaceId: string;
  parentId: string | null;
}

export interface PageNode extends Node {
  data: {
    id: string;
    title: string;
    slug: string;
    pageType: string;
    status: string;
    previewUrl: string;
    updatedAt?: string;
    language?: string;
    websiteId: string;
    pageId?: string;
    isLanguageNode?: boolean;
  };
}

export interface PageEdge extends Edge {
  data?: {
    type: 'navigation' | 'reference' | 'language';
  };
}

export interface PageStructure {
  nodes: PageNode[];
  edges: PageEdge[];
}
