/* Grid Background Styles with Theme Support */

/* 基础网格变量 */
:root {
  --grid-color-light: 240 5.9% 90%;
  --grid-color-dark: 240 3.7% 15.9%;
  --grid-opacity-light: 0.6;
  --grid-opacity-dark: 0.4;
  --grid-size-small: 16px;
  --grid-size-medium: 20px;
  --grid-size-large: 24px;
  --grid-size-xlarge: 32px;
}

/* 亮色主题网格 */
.grid-background-light {
  background-image: 
    linear-gradient(to right, hsl(var(--grid-color-light) / var(--grid-opacity-light)) 1px, transparent 1px),
    linear-gradient(to bottom, hsl(var(--grid-color-light) / var(--grid-opacity-light)) 1px, transparent 1px);
}

/* 暗色主题网格 */
.dark .grid-background-light {
  background-image: 
    linear-gradient(to right, hsl(var(--grid-color-dark) / var(--grid-opacity-dark)) 1px, transparent 1px),
    linear-gradient(to bottom, hsl(var(--grid-color-dark) / var(--grid-opacity-dark)) 1px, transparent 1px);
}

/* 移动预览网格 */
.mobile-preview-grid {
  background: 
    linear-gradient(to right, hsl(var(--border) / 0.15) 1px, transparent 1px),
    linear-gradient(to bottom, hsl(var(--border) / 0.15) 1px, transparent 1px),
    linear-gradient(135deg, hsl(var(--muted) / 0.3) 0%, hsl(var(--muted) / 0.1) 100%);
  background-size: 
    var(--grid-size-medium) var(--grid-size-medium),
    var(--grid-size-medium) var(--grid-size-medium),
    100% 100%;
}

/* 设计器网格 */
.designer-grid {
  background-image: 
    linear-gradient(to right, hsl(var(--muted-foreground) / 0.08) 1px, transparent 1px),
    linear-gradient(to bottom, hsl(var(--muted-foreground) / 0.08) 1px, transparent 1px);
  background-size: var(--grid-size-large) var(--grid-size-large);
}

/* 画布网格 - 多层次网格 */
.canvas-grid {
  background-image: 
    /* 细网格 */
    linear-gradient(to right, hsl(var(--muted-foreground) / 0.06) 1px, transparent 1px),
    linear-gradient(to bottom, hsl(var(--muted-foreground) / 0.06) 1px, transparent 1px),
    /* 粗网格 */
    linear-gradient(to right, hsl(var(--muted-foreground) / 0.12) 1px, transparent 1px),
    linear-gradient(to bottom, hsl(var(--muted-foreground) / 0.12) 1px, transparent 1px);
  background-size: 
    var(--grid-size-xlarge) var(--grid-size-xlarge),
    var(--grid-size-xlarge) var(--grid-size-xlarge),
    calc(var(--grid-size-xlarge) * 4) calc(var(--grid-size-xlarge) * 4),
    calc(var(--grid-size-xlarge) * 4) calc(var(--grid-size-xlarge) * 4);
}

/* 点状网格 */
.dots-grid {
  background-image: radial-gradient(circle, hsl(var(--border) / 0.3) 1px, transparent 1px);
  background-size: var(--grid-size-medium) var(--grid-size-medium);
}

/* 对角线网格 */
.diagonal-grid {
  background-image: 
    linear-gradient(45deg, hsl(var(--border) / 0.15) 1px, transparent 1px),
    linear-gradient(-45deg, hsl(var(--border) / 0.15) 1px, transparent 1px);
  background-size: var(--grid-size-medium) var(--grid-size-medium);
}

/* 交叉网格 */
.cross-grid {
  background-image: 
    linear-gradient(to right, hsl(var(--border) / 0.15) 1px, transparent 1px),
    linear-gradient(to bottom, hsl(var(--border) / 0.15) 1px, transparent 1px),
    linear-gradient(45deg, hsl(var(--border) / 0.08) 1px, transparent 1px),
    linear-gradient(-45deg, hsl(var(--border) / 0.08) 1px, transparent 1px);
  background-size: 
    var(--grid-size-medium) var(--grid-size-medium),
    var(--grid-size-medium) var(--grid-size-medium),
    calc(var(--grid-size-medium) * 2) calc(var(--grid-size-medium) * 2),
    calc(var(--grid-size-medium) * 2) calc(var(--grid-size-medium) * 2);
}

/* 响应式网格大小 */
@media (max-width: 768px) {
  :root {
    --grid-size-small: 12px;
    --grid-size-medium: 16px;
    --grid-size-large: 20px;
    --grid-size-xlarge: 24px;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  :root {
    --grid-opacity-light: 0.8;
    --grid-opacity-dark: 0.6;
  }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  .grid-animated {
    animation: none;
  }
}

/* 网格动画效果 */
.grid-animated {
  animation: grid-pulse 3s ease-in-out infinite;
}

@keyframes grid-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* 网格悬停效果 */
.grid-hover:hover {
  background-size: 
    calc(var(--grid-size-medium) * 1.1) calc(var(--grid-size-medium) * 1.1);
  transition: background-size 0.3s ease;
}

/* 网格焦点效果 */
.grid-focus:focus-within {
  background-image: 
    linear-gradient(to right, hsl(var(--ring) / 0.2) 1px, transparent 1px),
    linear-gradient(to bottom, hsl(var(--ring) / 0.2) 1px, transparent 1px);
} 