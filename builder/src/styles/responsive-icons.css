/* ThemedIconV2 精炼 SSR 兼容方案 - 基于 SSRLab 设计理念 */

/* === 容器基础样式 === */
.themed-icon-v2 {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  flex-shrink: 0;
}

.themed-icon-v2-svg {
  width: 100%;
  height: 100%;
  flex-shrink: 0;
}

/* === 变体尺寸控制 - 直接通过类名设置，SSR 兼容 === */

/* Navigation 变体 */
.icon-variant-navigation {
  width: 44px;
  height: 44px;
}

.icon-variant-feature {
  width: 64px;
  height: 64px;
}

.icon-variant-inline {
  width: 20px;
  height: 20px;
}

.icon-variant-list {
  width: 40px;
  height: 40px;
}

.icon-variant-hero {
  width: 96px;
  height: 96px;
}

/* === 响应式断点调整 === */

/* 移动端 (≤639px) */
@media (max-width: 639px) {
  .icon-variant-navigation { width: 36px; height: 36px; }
  .icon-variant-feature { width: 48px; height: 48px; }
  .icon-variant-inline { width: 16px; height: 16px; }
  .icon-variant-list { width: 32px; height: 32px; }
  .icon-variant-hero { width: 72px; height: 72px; }
}

/* 平板端 (640px-1023px) */
@media (min-width: 640px) and (max-width: 1023px) {
  .icon-variant-navigation { width: 40px; height: 40px; }
  .icon-variant-feature { width: 56px; height: 56px; }
  .icon-variant-inline { width: 18px; height: 18px; }
  .icon-variant-list { width: 36px; height: 36px; }
  .icon-variant-hero { width: 80px; height: 80px; }
}

/* 超大屏 (≥1536px) */
@media (min-width: 1536px) {
  .icon-variant-navigation { width: 48px; height: 48px; }
  .icon-variant-feature { width: 72px; height: 72px; }
  .icon-variant-inline { width: 22px; height: 22px; }
  .icon-variant-list { width: 44px; height: 44px; }
  .icon-variant-hero { width: 112px; height: 112px; }
}

/* === 页面宽度感知调整 === */

/* 宽屏模式 - 直接缩放容器 */
html.page-width-wide .icon-variant-navigation { transform: scale(1.1); }
html.page-width-wide .icon-variant-feature { transform: scale(1.15); }
html.page-width-wide .icon-variant-inline { transform: scale(1.1); }
html.page-width-wide .icon-variant-list { transform: scale(1.1); }
html.page-width-wide .icon-variant-hero { transform: scale(1.2); }

/* 满屏模式 - 更大缩放 */
html.page-width-full .icon-variant-navigation { transform: scale(1.25); }
html.page-width-full .icon-variant-feature { transform: scale(1.3); }
html.page-width-full .icon-variant-inline { transform: scale(1.2); }
html.page-width-full .icon-variant-list { transform: scale(1.25); }
html.page-width-full .icon-variant-hero { transform: scale(1.4); }

/* === 渐变定义 === */
.themed-icon-v2 .icon-gradient-blue {
  fill: url(#iconGradientBlue);
}

.themed-icon-v2 .icon-gradient-green {
  fill: url(#iconGradientGreen);
}

.themed-icon-v2 .icon-gradient-cyan {
  fill: url(#iconGradientCyan);
} 