@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import grid background styles */
@import './grid-backgrounds.css';

/* Import ThemedIconV2 responsive styles */
@import './responsive-icons.css';

/* 系统色彩模式检测 */
@media (prefers-color-scheme: dark) {
  html.color-scheme-system {
    color-scheme: dark;
  }
  
  html.color-scheme-system:not(.dark) {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@media (prefers-color-scheme: light) {
  html.color-scheme-system {
    color-scheme: light;
  }
}

/* 宽屏模式 */
html.page-width-wide {
    --container-max-width: 96rem; /* 1536px */
    --container-content-width: 76rem; /* 1216px */
    --container-padding-x: 2rem; /* 32px */
    
    /* 宽屏模式下的间距 */
    --section-spacing-y: 5rem; /* 80px */
    --content-spacing-y: 2.5rem; /* 40px */
    --element-spacing-y: 1.25rem; /* 20px */
    --element-spacing-x: 1.25rem; /* 20px */

    /* 宽屏模式下的字体大小 - 优化后 */
    --heading-1-size: 3.75rem;   /* 60px - 增加 */
    --heading-2-size: 2.75rem;   /* 44px - 增加 */
    --heading-3-size: 2.25rem;   /* 36px - 增加 */
    --heading-4-size: 1.75rem;   /* 28px - 增加 */
    --body-large-size: 1.375rem; /* 22px - 增加 */
    --body-base-size: 1.1875rem; /* 19px - 增加 */
    --body-small-size: 1.0625rem;/* 17px - 增加 */
    
    /* 宽屏模式下的行高 */
    --heading-1-line-height: 1.15;
    --heading-2-line-height: 1.225;
    --heading-3-line-height: 1.275;
    --heading-4-line-height: 1.325;
    --body-large-line-height: 1.55;
    --body-base-line-height: 1.55;
    --body-small-line-height: 1.45;
    
    /* 宽屏模式下的按钮尺寸 */
    --button-large-height: 3.75rem;      /* 60px */
    --button-medium-height: 3rem;        /* 48px */
    --button-small-height: 2.5rem;       /* 40px */
    --button-large-padding-x: 2.25rem;   /* 36px */
    --button-medium-padding-x: 1.75rem;  /* 28px */
    --button-small-padding-x: 1.25rem;   /* 20px */
    --button-large-icon-size: 1.375rem;  /* 22px */
    --button-medium-icon-size: 1.125rem; /* 18px */
    --button-small-icon-size: 1rem;      /* 16px */
  }
  
  /* 满屏模式 */
  html.page-width-full {
    --container-max-width: 100%;
    --container-content-width: 85%;
    --container-padding-x: 3rem; /* 48px */
    
    /* 满屏模式下的间距 */
    --section-spacing-y: 6rem; /* 96px */
    --content-spacing-y: 3rem; /* 48px */
    --element-spacing-y: 1.5rem; /* 24px */
    --element-spacing-x: 1.5rem; /* 24px */

    /* 满屏模式下的字体大小 - 优化后 */
    --heading-1-size: 4.5rem;    /* 72px - 增加 */
    --heading-2-size: 3.25rem;   /* 52px - 增加 */
    --heading-3-size: 2.5rem;    /* 40px - 增加 */
    --heading-4-size: 2rem;      /* 32px - 增加 */
    --body-large-size: 1.5rem;   /* 24px - 增加 */
    --body-base-size: 1.25rem;   /* 20px - 增加 */
    --body-small-size: 1.125rem; /* 18px - 增加 */
    
    /* 满屏模式下的行高 */
    --heading-1-line-height: 1.2;
    --heading-2-line-height: 1.25;
    --heading-3-line-height: 1.3;
    --heading-4-line-height: 1.35;
    --body-large-line-height: 1.6;
    --body-base-line-height: 1.6;
    --body-small-line-height: 1.5;
    
    /* 满屏模式下的按钮尺寸 */
    --button-large-height: 4rem;         /* 64px */
    --button-medium-height: 3.25rem;     /* 52px */
    --button-small-height: 2.75rem;      /* 44px */
    --button-large-padding-x: 2.5rem;    /* 40px */
    --button-medium-padding-x: 2rem;     /* 32px */
    --button-small-padding-x: 1.5rem;    /* 24px */
    --button-large-icon-size: 1.5rem;    /* 24px */
    --button-medium-icon-size: 1.25rem;  /* 20px */
    --button-small-icon-size: 1.125rem;  /* 18px */
  }


@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 215.4 16.3% 35%;  /* 优化：从 222.2 84% 4.9% 调整为更柔和的颜色 */
    --card: 0 0% 100%;
    --card-foreground: 215.4 16.3% 35%;  /* 保持与foreground一致 */
    --popover: 0 0% 100%;
    --popover-foreground: 215.4 16.3% 35%;  /* 保持与foreground一致 */
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 217.2 32.6% 30%;  /* 优化：从 222.2 47.4% 11.2% 调整为更柔和的颜色 */
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 217.2 32.6% 30%;  /* 保持与secondary-foreground一致 */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.3rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* 页面宽度相关变量 - 默认为普通宽度 */
    --container-max-width: 80rem; /* 1280px, 对应 max-w-7xl */
    --container-content-width: 65rem; /* 1040px, 适合内容区域 */
    --container-padding-x: 1.5rem; /* 24px, 对应 px-6 */
    
    /* 间距相关变量 - 默认为普通间距 */
    --section-spacing-y: 4rem; /* 64px, 区块之间的垂直间距 */
    --content-spacing-y: 2rem; /* 32px, 内容元素之间的垂直间距 */
    --element-spacing-y: 1rem; /* 16px, 元素内部的垂直间距 */
    --element-spacing-x: 1rem; /* 16px, 元素内部的水平间距 */

    /* 字体大小相关变量 - 默认为普通字体大小 */
    --heading-1-size: 3rem;      /* 48px */
    --heading-2-size: 2.25rem;   /* 36px */
    --heading-3-size: 1.875rem;  /* 30px */
    --heading-4-size: 1.5rem;    /* 24px */
    --body-large-size: 1.125rem; /* 18px */
    --body-base-size: 1rem;      /* 16px */
    --body-small-size: 0.875rem; /* 14px */
    
    /* 行高相关变量 - 默认为普通行高 */
    --heading-1-line-height: 1.1;  /* 标题通常使用较紧凑的行高 */
    --heading-2-line-height: 1.2;
    --heading-3-line-height: 1.25;
    --heading-4-line-height: 1.3;
    --body-large-line-height: 1.5;
    --body-base-line-height: 1.5;  /* 正文使用较宽松的行高提高可读性 */
    --body-small-line-height: 1.4;
    
    /* 按钮基础变量 - 与全局设计系统协调统一 */
    --button-radius: var(--radius);
    --button-animation-duration: 200ms;
    
    /* 按钮尺寸变量 - 与元素间距系统对齐 */
    --button-small-height: var(--element-size-small, 2.25rem);    /* 36px */
    --button-medium-height: var(--element-size-medium, 2.75rem);   /* 44px */
    --button-large-height: var(--element-size-large, 3.5rem);      /* 56px */
    
    /* 按钮内边距变量 - 与元素间距系统对齐 */
    --button-small-padding-x: var(--element-spacing-x, 1rem);      /* 16px */
    --button-medium-padding-x: calc(var(--element-spacing-x, 1rem) * 1.5); /* 24px */
    --button-large-padding-x: calc(var(--element-spacing-x, 1rem) * 2);    /* 32px */
    
    /* 按钮内容密度比例 */
    --button-padding-to-height-ratio: 0.57;
    
    /* 按钮状态变量 - 与全局透明度系统对齐 */
    --button-hover-opacity: 0.9;
    --button-active-opacity: 0.8;
    --button-disabled-opacity: 0.6;
    --button-loading-opacity: 0.85;
    
    /* 按钮图标变量 - 与元素间距系统对齐 */
    --button-icon-spacing: var(--element-spacing-x, 0.5rem);       /* 图标与文本间距 */
    --button-icon-only-padding: calc(var(--element-spacing-x, 1rem) * 0.75); /* 仅图标按钮的内边距 */
    
    /* 按钮图标尺寸 - 与文本尺寸系统对齐 */
    --button-small-icon-size: var(--body-small-size, 0.875rem);  /* 14px */
    --button-medium-icon-size: var(--body-base-size, 1rem);      /* 16px */
    --button-large-icon-size: var(--body-large-size, 1.25rem);   /* 20px */
    
    /* 图标按钮正方形尺寸 - 与按钮高度保持一致 */
    --button-icon-only-small-size: var(--button-small-height, 2.25rem);  /* 36px */
    --button-icon-only-medium-size: var(--button-medium-height, 2.75rem); /* 44px */
    --button-icon-only-large-size: var(--button-large-height, 3.5rem);   /* 56px */
    
    /* 按钮加载状态变量 */
    --button-spinner-size: 1rem;
    --button-spinner-border-width: 2px;
    --button-spinner-animation-duration: 0.8s;
    --button-spinner-animation-duration-fast: 0.6s;
    --button-spinner-animation-duration-slow: 1.2s;
    
    /* 按钮渐变变量 */
    --button-gradient: linear-gradient(to right, hsl(var(--primary)), hsl(var(--accent)));
    --button-gradient-hover: linear-gradient(to right, hsl(var(--primary)), hsl(var(--secondary)));
    
    /* 按钮动画变量 - 基础 */
    --button-transition-property: all;
    --button-transition-duration-fast: 150ms;
    --button-transition-duration-normal: 200ms;
    --button-transition-duration-slow: 300ms;
    --button-transition-timing-default: cubic-bezier(0.4, 0, 0.2, 1);
    --button-transition-timing-entrance: cubic-bezier(0, 0, 0.2, 1);
    --button-transition-timing-exit: cubic-bezier(0.4, 0, 1, 1);
    --button-transition-timing-bounce: cubic-bezier(0.34, 1.56, 0.64, 1);
    
    /* 按钮悬停动画变量 */
    --button-hover-scale: 1.03;
    --button-hover-lift: -2px;
    --button-hover-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    --button-hover-glow-opacity: 0.3;
    --button-hover-glow-blur: 8px;
    --button-hover-glow-spread: 2px;
    
    /* 按钮点击动画变量 */
    --button-active-press-scale: 0.97;
    --button-active-sink: 1px;
    
    /* 按钮注意力动画变量 */
    --button-shine-opacity: 0.3;
    --button-border-pulse-width: 2px;
    --button-border-pulse-radius: 4px;
    --button-border-pulse-opacity: 0.7;
    
    /* 按钮可访问性变量 */
    --button-focus-ring-width: 2px;
    --button-focus-ring-offset: 2px;
    --button-transition-duration-normal: var(--button-animation-duration, 200ms);
    --button-transition-duration-slow: 300ms;
    --button-transition-timing-default: cubic-bezier(0.4, 0, 0.2, 1); /* 平滑过渡 */
    --button-transition-timing-entrance: cubic-bezier(0, 0, 0.2, 1);  /* 快入慢出 */
    --button-transition-timing-exit: cubic-bezier(0.4, 0, 1, 1);      /* 快出 */
    --button-transition-timing-bounce: cubic-bezier(0.34, 1.56, 0.64, 1); /* 弹性效果 */
    
    /* 按钮动画变量 - 组合 */
    --button-transition: var(--button-transition-property) var(--button-transition-duration-normal) var(--button-transition-timing-default);
    
    /* 按钮脉冲动画变量 */
    --button-pulse-scale-subtle: 1.03;
    --button-pulse-scale-medium: 1.05;
    --button-pulse-scale-strong: 1.08;
    --button-pulse-scale: var(--button-pulse-scale-medium);
    --button-pulse-duration-fast: 1.5s;
    --button-pulse-duration-normal: 2s;
    --button-pulse-duration-slow: 3s;
    
    /* 按钮动画系统 - 预设动画 */
    --button-animation-hover-scale: scale(var(--button-hover-scale));
    --button-animation-hover-lift: translateY(var(--button-hover-lift));
    --button-animation-hover-shadow: var(--button-hover-shadow);
    --button-animation-active-press: scale(var(--button-active-press-scale));
    --button-animation-active-sink: translateY(var(--button-active-sink));
    --button-pulse-duration: var(--button-pulse-duration-normal);
    
    /* 按钮可访问性变量 */
    --button-min-touch-target: 44px; /* 符合WCAG标准 */
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 0 0% 100%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    
    /* 暗模式下的按钮状态增强 */
    --button-hover-opacity: 0.8;
    --button-active-opacity: 0.7;
    --button-focus-ring-offset: 3px;
    
    /* 暗模式下的渐变调整 */
    --button-gradient: linear-gradient(to right, hsl(var(--primary)), hsl(var(--accent) / 0.8));
    --button-gradient-hover: linear-gradient(to right, hsl(var(--primary)), hsl(var(--ring)));
    
    /* 暗模式下的按钮动画调整 */
    --button-transition-duration-normal: 250ms;
    --button-pulse-scale: var(--button-pulse-scale-subtle);
    --button-pulse-duration: var(--button-pulse-duration-slow);
  }
  
  /* 科技主题 - 亮色模式 */
  .theme-tech {
    --primary: 230 80% 50%;
    --primary-foreground: 0 0% 100%;
    --secondary: 200 75% 55%;
    --secondary-foreground: 0 0% 100%;
    --accent: 210 75% 60%;
    --accent-foreground: 0 0% 100%;
    --ring: 230 80% 50%;
    
    /* 科技主题按钮设置 */
    --button-radius: 0.125rem;
    --button-animation-duration: 150ms;
    --button-gradient: linear-gradient(to right, hsl(var(--primary)), hsl(var(--secondary)));
    --button-transition-timing-default: cubic-bezier(0.25, 0.1, 0.25, 1);
    --button-pulse-scale: 1.04;
    --button-pulse-duration: 1.5s;
  }
  
  /* 科技主题 - 暗色模式 */
  .dark.theme-tech {
    --primary: 230 80% 60%;
    --primary-foreground: 0 0% 100%;
    --secondary: 200 75% 65%;
    --secondary-foreground: 0 0% 100%;
    --accent: 210 75% 70%;
    --accent-foreground: 0 0% 100%;
    --ring: 230 80% 60%;
    
    --button-gradient: linear-gradient(to right, hsl(var(--primary)), hsl(var(--secondary) / 0.9));
  }
  
  /* 创意主题 - 亮色模式 */
  .theme-creative {
    --primary: 320 80% 55%;
    --primary-foreground: 0 0% 100%;
    --secondary: 280 75% 60%;
    --secondary-foreground: 0 0% 100%;
    --accent: 300 75% 65%;
    --accent-foreground: 0 0% 100%;
    --ring: 320 80% 55%;
    
    /* 创意主题按钮设置 */
    --button-radius: 1.5rem;
    --button-gradient: linear-gradient(to right bottom, hsl(var(--primary)), hsl(var(--secondary)));
    --button-transition-duration-normal: 250ms;
    --button-transition-timing-bounce: cubic-bezier(0.34, 1.8, 0.64, 1);
    --button-pulse-scale: 1.06;
    --button-pulse-duration: 2.5s;
  }
  
  /* 创意主题 - 暗色模式 */
  .dark.theme-creative {
    --primary: 320 80% 65%;
    --primary-foreground: 0 0% 100%;
    --secondary: 280 75% 70%;
    --secondary-foreground: 0 0% 100%;
    --accent: 300 75% 75%;
    --accent-foreground: 0 0% 100%;
    --ring: 320 80% 65%;
    
    --button-gradient: linear-gradient(to right bottom, hsl(var(--primary)), hsl(var(--secondary) / 0.9));
  }
  
  /* 金融主题 - 亮色模式 */
  .theme-finance {
    --primary: 210 70% 35%;
    --primary-foreground: 0 0% 100%;
    --secondary: 180 50% 40%;
    --secondary-foreground: 0 0% 100%;
    --accent: 195 60% 45%;
    --accent-foreground: 0 0% 100%;
    --ring: 210 70% 35%;
    
    /* 金融主题按钮设置 */
    --button-radius: 0.25rem;
    --button-gradient: linear-gradient(to right, hsl(var(--primary)), hsl(var(--secondary)));
    --button-transition-duration-normal: 180ms;
    --button-pulse-scale: 1.03;
    --button-pulse-duration: 2s;
  }
  
  /* 金融主题 - 暗色模式 */
  .dark.theme-finance {
    --primary: 210 70% 45%;
    --primary-foreground: 0 0% 100%;
    --secondary: 180 50% 50%;
    --secondary-foreground: 0 0% 100%;
    --accent: 195 60% 55%;
    --accent-foreground: 0 0% 100%;
    --ring: 210 70% 45%;
    
    --button-gradient: linear-gradient(to right, hsl(var(--primary)), hsl(var(--secondary) / 0.9));
  }
  
  /* 教育主题 - 亮色模式 */
  .theme-education {
    --primary: 190 70% 45%;
    --primary-foreground: 0 0% 100%;
    --secondary: 150 60% 50%;
    --secondary-foreground: 0 0% 100%;
    --accent: 170 65% 55%;
    --accent-foreground: 0 0% 100%;
    --ring: 190 70% 45%;
    
    /* 教育主题按钮设置 */
    --button-radius: 0.75rem;
    --button-gradient: linear-gradient(to right, hsl(var(--primary)), hsl(var(--secondary)));
    --button-transition-duration-normal: 200ms;
    --button-pulse-scale: 1.05;
    --button-pulse-duration: 2.2s;
  }
  
  /* 教育主题 - 暗色模式 */
  .dark.theme-education {
    --primary: 190 70% 55%;
    --primary-foreground: 0 0% 100%;
    --secondary: 150 60% 60%;
    --secondary-foreground: 0 0% 100%;
    --accent: 170 65% 65%;
    --accent-foreground: 0 0% 100%;
    --ring: 190 70% 55%;
    
    --button-gradient: linear-gradient(to right, hsl(var(--primary)), hsl(var(--secondary) / 0.9));
  }
  
  /* 渐变按钮主题 - 蓝色 */
  .theme-gradient-blue {
    --button-gradient: linear-gradient(to right, #3b82f6, #2563eb, #1d4ed8);
    --button-gradient-hover: linear-gradient(to right, #60a5fa, #3b82f6, #2563eb);
    --button-focus-ring-color: 221 83% 53%;
    --button-focus-ring-color-dark: 221 83% 65%;
  }
  
  /* 渐变按钮主题 - 绿色 */
  .theme-gradient-green {
    --button-gradient: linear-gradient(to right, #4ade80, #22c55e, #16a34a);
    --button-gradient-hover: linear-gradient(to right, #86efac, #4ade80, #22c55e);
    --button-focus-ring-color: 142 71% 45%;
    --button-focus-ring-color-dark: 142 71% 55%;
  }
  
  /* 渐变按钮主题 - 青色 */
  .theme-gradient-cyan {
    --button-gradient: linear-gradient(to right, #22d3ee, #06b6d4, #0891b2);
    --button-gradient-hover: linear-gradient(to right, #67e8f9, #22d3ee, #06b6d4);
    --button-focus-ring-color: 186 100% 50%;
    --button-focus-ring-color-dark: 186 100% 60%;
  }
  
  /* 渐变按钮主题 - 青绿色 */
  .theme-gradient-teal {
    --button-gradient: linear-gradient(to right, #2dd4bf, #14b8a6, #0d9488);
    --button-gradient-hover: linear-gradient(to right, #5eead4, #2dd4bf, #14b8a6);
    --button-focus-ring-color: 166 76% 43%;
    --button-focus-ring-color-dark: 166 76% 53%;
  }
  
  /* 渐变按钮主题 - 青柿色 */
  .theme-gradient-lime {
    --button-gradient: linear-gradient(to right, #d9f99d, #a3e635, #84cc16);
    --button-gradient-hover: linear-gradient(to right, #ecfccb, #d9f99d, #a3e635);
    --button-focus-ring-color: 84 81% 44%;
    --button-focus-ring-color-dark: 84 81% 54%;
    --button-text-color: 0 0% 100%;
    --button-text-color-hover: 0 0% 100%;
  }
  
  /* 渐变按钮主题 - 红色 */
  .theme-gradient-red {
    --button-gradient: linear-gradient(to right, #f87171, #ef4444, #dc2626);
    --button-gradient-hover: linear-gradient(to right, #fca5a5, #f87171, #ef4444);
    --button-focus-ring-color: 0 84% 60%;
    --button-focus-ring-color-dark: 0 84% 70%;
  }
  
  /* 渐变按钮主题 - 粉色 */
  .theme-gradient-pink {
    --button-gradient: linear-gradient(to right, #f472b6, #ec4899, #db2777);
    --button-gradient-hover: linear-gradient(to right, #f9a8d4, #f472b6, #ec4899);
    --button-focus-ring-color: 332 79% 65%;
    --button-focus-ring-color-dark: 332 79% 75%;
  }
  
  /* 渐变按钮主题 - 紫色 */
  .theme-gradient-purple {
    --button-gradient: linear-gradient(to right, #a855f7, #9333ea, #7e22ce);
    --button-gradient-hover: linear-gradient(to right, #c084fc, #a855f7, #9333ea);
    --button-focus-ring-color: 270 76% 55%;
    --button-focus-ring-color-dark: 270 76% 65%;
  }
  
  /* 双色渐变按钮主题 */
  /* 紫蓝渐变 */
  .theme-gradient-purple-blue {
    --button-gradient: linear-gradient(to bottom right, #9333ea, #3b82f6);
    --button-gradient-hover: linear-gradient(to bottom left, #9333ea, #3b82f6);
    --button-focus-ring-color: 240 79% 54%;
    --button-focus-ring-color-dark: 240 79% 64%;
  }
  
  /* 青蓝渐变 */
  .theme-gradient-cyan-blue {
    --button-gradient: linear-gradient(to right, #06b6d4, #3b82f6);
    --button-gradient-hover: linear-gradient(to bottom left, #06b6d4, #3b82f6);
    --button-focus-ring-color: 199 89% 48%;
    --button-focus-ring-color-dark: 199 89% 58%;
  }
  
  /* 绿蓝渐变 */
  .theme-gradient-green-blue {
    --button-gradient: linear-gradient(to bottom right, #4ade80, #3b82f6);
    --button-gradient-hover: linear-gradient(to bottom left, #4ade80, #3b82f6);
    --button-focus-ring-color: 142 69% 58%;
    --button-focus-ring-color-dark: 142 69% 68%;
  }
  
  /* 紫粉渐变 */
  .theme-gradient-purple-pink {
    --button-gradient: linear-gradient(to right, #9333ea, #ec4899);
    --button-gradient-hover: linear-gradient(to left, #9333ea, #ec4899);
    --button-focus-ring-color: 328 86% 60%;
    --button-focus-ring-color-dark: 328 86% 70%;
  }
  
  /* 粉橙渐变 */
  .theme-gradient-pink-orange {
    --button-gradient: linear-gradient(to bottom right, #ec4899, #fb923c);
    --button-gradient-hover: linear-gradient(to bottom left, #ec4899, #fb923c);
    --button-focus-ring-color: 328 86% 60%;
    --button-focus-ring-color-dark: 328 86% 70%;
  }
  
  /* 青柿渐变 */
  .theme-gradient-teal-lime {
    --button-gradient: linear-gradient(to right, #5eead4, #a3e635);
    --button-gradient-hover: linear-gradient(to left, #2dd4bf, #84cc16);
    --button-focus-ring-color: 166 76% 43%;
    --button-focus-ring-color-dark: 166 76% 53%;
    --button-text-color: 0 0% 100%;
    --button-text-color-hover: 0 0% 100%;
  }
  
  /* 红黄渐变 */
  .theme-gradient-red-yellow {
    --button-gradient: linear-gradient(to right, #f87171, #facc15);
    --button-gradient-hover: linear-gradient(to bottom left, #ef4444, #eab308);
    --button-focus-ring-color: 0 84% 60%;
    --button-focus-ring-color-dark: 0 84% 70%;
    --button-text-color: 0 0% 100%;
    --button-text-color-hover: 0 0% 100%;
  }
  
  /* 渐变按钮基础类 */
  .gradient-btn {
    position: relative;
    z-index: 1;
    color: var(--button-text-color, white) !important;
  }
  
  /* 黑暗模式下的渐变按钮文字颜色 */
  .dark .gradient-btn,
  .dark [class*="bg-gradient"] {
    color: var(--button-text-color, white) !important;
  }
  
  /* 默认主题按钮在黑暗模式下的颜色调整 */
  .dark .bg-primary {
    color: var(--primary-foreground) !important;
  }
  
  .dark .bg-secondary {
    color: var(--secondary-foreground) !important;
  }
  
  .dark .bg-destructive {
    color: var(--destructive-foreground) !important;
  }
  
  .dark .text-primary {
    color: hsl(var(--primary)) !important;
  }
  
  .gradient-btn::before,
  .gradient-btn::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: inherit;
    z-index: -1;
    transition: opacity 0.3s ease;
  }
  
  .gradient-btn::before {
    background-image: var(--button-gradient, linear-gradient(to right, hsl(var(--primary)), hsl(var(--accent))));
    opacity: 1;
  }
  
  .gradient-btn::after {
    background-image: var(--button-gradient-hover, linear-gradient(to right, hsl(var(--primary)), hsl(var(--secondary))));
    opacity: 0;
  }
  
  .gradient-btn:hover::before {
    opacity: 0;
  }
  
  .gradient-btn:hover::after {
    opacity: 1;
  }
  
  .gradient-btn:focus {
    box-shadow: 0 0 0 2px hsl(var(--background)), 0 0 0 4px hsl(var(--button-focus-ring-color, var(--ring)));
  }
  
  .dark .gradient-btn:focus {
    box-shadow: 0 0 0 2px hsl(var(--background)), 0 0 0 4px hsl(var(--button-focus-ring-color-dark, var(--ring)));
  }

  /* 按钮动画类 */
  .btn-hover-scale {
    transition: transform var(--button-transition-duration-normal) var(--button-transition-timing-bounce);
  }
  
  .btn-hover-scale:hover:not(:disabled) {
    transform: scale(var(--button-hover-scale));
  }
  
  .btn-hover-lift {
    transition: transform var(--button-transition-duration-normal) var(--button-transition-timing-bounce),
                box-shadow var(--button-transition-duration-normal) var(--button-transition-timing-default);
  }
  
  .btn-hover-lift:hover:not(:disabled) {
    transform: translateY(var(--button-hover-lift));
    box-shadow: var(--button-hover-shadow);
  }
  
  .btn-hover-glow {
    transition: box-shadow var(--button-transition-duration-normal) var(--button-transition-timing-default);
  }
  
  .btn-hover-glow:hover:not(:disabled) {
    box-shadow: 0 0 var(--button-hover-glow-blur) var(--button-hover-glow-spread) hsla(var(--primary), var(--button-hover-glow-opacity));
  }
  
  /* 点击动画 - 按压效果 */
  .btn-active-press {
    position: relative;
    transition: transform var(--button-transition-duration-fast) var(--button-transition-timing-default);
    transform: translateZ(0); /* 启用硬件加速 */
    will-change: transform; /* 提示浏览器该属性将变化 */
  }
  
  .btn-active-press:active:not(:disabled),
  .btn-active-press:active:not([disabled]) {
    transform: scale(var(--button-active-press-scale));
  }
  
  /* 点击动画 - 下沉效果 */
  .btn-active-sink {
    position: relative;
    transition: transform var(--button-transition-duration-fast) var(--button-transition-timing-default);
    transform: translateZ(0); /* 启用硬件加速 */
    will-change: transform; /* 提示浏览器该属性将变化 */
  }
  
  .btn-active-sink:active:not(:disabled),
  .btn-active-sink:active:not([disabled]) {
    transform: translateY(var(--button-active-sink));
  }
  
  .btn-shine {
    position: relative;
    overflow: hidden;
  }
  
  .btn-shine::after {
    content: "";
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
      to right,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, var(--button-shine-opacity)) 50%,
      rgba(255, 255, 255, 0) 100%
    );
    transform: rotate(30deg);
    animation: shine var(--button-pulse-duration-slow) infinite;
  }
  
  .btn-border-pulse {
    position: relative;
  }
  
  .btn-border-pulse::after {
    content: "";
    position: absolute;
    inset: calc(-1 * var(--button-border-pulse-radius));
    border: var(--button-border-pulse-width) solid hsl(var(--primary));
    border-radius: calc(var(--button-radius) + var(--button-border-pulse-radius));
    opacity: 0;
    animation: border-pulse var(--button-pulse-duration-normal) infinite;
  }
  
  /* 动画关键帧 */
  @keyframes shine {
    0%, 20%, 100% {
      transform: translateX(-100%) rotate(30deg);
    }
    80% {
      transform: translateX(100%) rotate(30deg);
    }
  }
  
  @keyframes border-pulse {
    0%, 100% { opacity: 0; transform: scale(1); }
    50% { opacity: var(--button-border-pulse-opacity); transform: scale(1.03); }
  }
  
  @keyframes pulse-subtle {
    0%, 100% { 
      transform: scale(1);
      box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.4);
    }
    50% { 
      transform: scale(1.02);
      box-shadow: 0 0 0 10px rgba(255, 193, 7, 0);
    }
  }
  
  @keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
      transform: translate3d(0, 0, 0);
    }
    40%, 43% {
      transform: translate3d(0, -8px, 0);
    }
    70% {
      transform: translate3d(0, -4px, 0);
    }
    90% {
      transform: translate3d(0, -2px, 0);
    }
  }
  
  /* 可访问性优化 */
  @media (pointer: coarse) {
    .btn {
      min-height: var(--button-min-touch-target);
      min-width: var(--button-min-touch-target);
    }
    
    /* 确保小按钮在触摸设备上有足够大的点击区域 */
    .btn-small {
      min-height: var(--button-min-touch-target);
      min-width: var(--button-min-touch-target);
      padding: 0 var(--button-small-padding-x);
    }
    
    /* 仅图标按钮的触摸优化 */
    .btn-icon-only {
      min-height: var(--button-min-touch-target);
      min-width: var(--button-min-touch-target);
    }
  }
  
  /* 减少动画偏好支持 */
  @media (prefers-reduced-motion: reduce) {
    :root {
      --button-transition-duration-fast: 0ms;
      --button-transition-duration-normal: 0ms;
      --button-transition-duration-slow: 0ms;
      --button-pulse-duration-fast: 0s;
      --button-pulse-duration-normal: 0s;
      --button-pulse-duration-slow: 0s;
      --button-spinner-animation-duration: 1s; /* 保留必要的功能性动画，但降低频率 */
    }
    
    .btn-pulse-subtle,
    .btn-pulse-medium,
    .btn-pulse-strong,
    .btn-shine,
    .btn-border-pulse {
      animation: none !important;
    }
    
    .btn-hover-scale:hover,
    .btn-hover-lift:hover,
    .btn-active-press:active,
    .btn-active-sink:active {
      transform: none !important;
      box-shadow: none !important;
    }
  }
  
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* 
颜色优化说明 - 2024年优化
=========================

优化前后对比：

Secondary按钮：
- 背景：#f1f5f9 (210 40% 96.1%)
- 字体：#0f172a → #475569 (222.2 47.4% 11.2% → 217.2 32.6% 30%)
- 对比度：15:1 → 6.8:1 (仍符合WCAG AA标准)

Outline按钮：
- 背景：#ffffff (0 0% 100%)
- 字体：#020617 → #475569 (222.2 84% 4.9% → 215.4 16.3% 35%)
- 对比度：18:1 → 7.2:1 (符合WCAG AA标准)

优化效果：
✅ 保持优秀的可读性
✅ 减少视觉冲击，提升舒适度
✅ 更符合现代设计趋势
✅ 提升用户体验
✅ 保持设计系统一致性

影响范围：
- Secondary按钮字体颜色
- Outline按钮字体颜色
- 所有使用foreground的文本元素
- Card、Popover等组件的文本颜色
*/

/* 按钮动画类 */
.btn-hover-scale {
  transition: transform var(--button-transition-duration-normal) var(--button-transition-timing-bounce);
}

.btn-hover-scale:hover:not(:disabled) {
  transform: scale(var(--button-hover-scale));
}

.btn-hover-lift {
  transition: transform var(--button-transition-duration-normal) var(--button-transition-timing-bounce),
              box-shadow var(--button-transition-duration-normal) var(--button-transition-timing-default);
}

.btn-hover-lift:hover:not(:disabled) {
  transform: translateY(var(--button-hover-lift));
  box-shadow: var(--button-hover-shadow);
}

.btn-hover-glow {
  transition: box-shadow var(--button-transition-duration-normal) var(--button-transition-timing-default);
}

.btn-hover-glow:hover:not(:disabled) {
  box-shadow: 0 0 var(--button-hover-glow-blur) var(--button-hover-glow-spread) hsla(var(--primary), var(--button-hover-glow-opacity));
}

/* 脉冲动画类 */
.animate-pulse-subtle {
  animation: pulse-subtle 3s ease-in-out infinite;
}

.animate-bounce {
  animation: bounce 2s infinite;
}