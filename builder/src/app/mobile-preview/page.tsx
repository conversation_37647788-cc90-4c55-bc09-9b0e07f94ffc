"use client";
import { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';
import { BlockEditorProvider } from '@/context/BlockEditorContext';
import clsx from 'clsx';
import Logger from '@/lib/logger';

const logger = new Logger('MobilePreview');

// 懒加载组件
const PageRender = dynamic(() => import('@/components/pageRender'), { 
  ssr: false,
  loading: () => (
    <div className="fixed inset-0 flex items-center justify-center bg-background/50">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
    </div>
  )
});

const PageRenderProviders = dynamic(() => import('@/components/pageRender/Providers'), { 
  ssr: false,
  loading: () => (
    <div className="fixed inset-0 flex items-center justify-center bg-background/50">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
    </div>
  )
});

export default function Page() {
  const [schema, setSchema] = useState<any>(null);

  useEffect(() => {
    logger.info('[ MobilePreview ] Component mounted, setting up message listener', {});

    const handleMessage = (event: MessageEvent) => {
      if (event.origin === window.location.origin) {
        console.log('[ MobilePreview ]', event.data)
        logger.info('[ MobilePreview ] Received data from parent', {
          dataSize: JSON.stringify(event.data).length,
          hasHeaders: !!event.data?.headers,
          hasFooters: !!event.data?.footers,
          language: event.data?.language,
          theme: event.data?.theme,
          domain: event.data?.domain,
          // 记录多语言相关数据
          hasLanguageInfo: !!event.data?.languageInfo,
          hasLanguageVersions: !!event.data?.languageVersions,
          hasLanguageUrls: !!event.data?.languageUrls,
          hasSeo: !!event.data?.seo
        });

        // 记录头尾数据的关键部分
        if (event.data?.headers && event.data?.language) {
          const languageHeaders = event.data.headers[event.data.language];
          if (languageHeaders) {
            logger.info('[ MobilePreview ] Current language headers', {
              variant: languageHeaders.variant,
              id: languageHeaders.id,
              linksCount: languageHeaders.links?.length || 0,
              actionsCount: languageHeaders.actions?.length || 0
            });
            
            logger.info('[ MobilePreview ] Links in headers', {
              count: languageHeaders.links?.length || 0,
              firstLink: languageHeaders.links?.[0]?.text || 'none',
              hasIcons: languageHeaders.links?.some((link: any) => !!link.icon) || false
            });
          } else {
            logger.warn('[ MobilePreview ] No headers found for language', { language: event.data.language });
          }
        } else {
          logger.warn('[ MobilePreview ] Missing headers or language in received data', {});
        }

        // 记录多语言数据详情
        if (event.data?.languageInfo) {
          logger.info('[ MobilePreview ] Language info details', {
            currentLanguage: event.data.languageInfo.currentLanguage,
            defaultLanguage: event.data.languageInfo.defaultLanguage,
            supportedLanguagesCount: event.data.languageInfo.supportedLanguages?.length || 0,
            translatedLanguagesCount: event.data.languageInfo.translatedLanguages?.length || 0
          });
        }

        setSchema(event.data);
        logger.info('[ MobilePreview ] Schema state updated', {});
      } else {
        logger.warn('[ MobilePreview ] Received message from unknown origin', { origin: event.origin });
      }
    };

    window.addEventListener('message', handleMessage);
    logger.info('[ MobilePreview ] Sending ready event to parent', {});
    window.parent.postMessage('onEventReady', window.location.origin);

    return () => {
      logger.info('[ MobilePreview ] Cleaning up message listener', {});
      window.removeEventListener('message', handleMessage);
    };
  }, []);

  // 处理头尾数据，确保与 PageCanva 组件保持一致
  const processHeaderData = (headerData: any): Record<string, any> | undefined => {
    if (!headerData) {
      logger.warn('[ MobilePreview ] No header data to process', {});
      return undefined;
    }
    
    logger.info('[ MobilePreview ] Processing header data', {
      variant: headerData.variant,
      hasLinks: !!headerData.links && headerData.links.length > 0,
      hasActions: !!headerData.actions && headerData.actions.length > 0
    });
    
    const headerProps = {
      variant: headerData?.variant || 'default',
      id: headerData?.id || 'default-header',
      logo: headerData?.logo || 'LitPage',
      links: headerData?.links || [],
      actions: headerData?.actions || [],
      languages: [
        { code: 'en', name: 'English', flag: '' },
        { code: 'zh', name: '中文', flag: '' }
      ],
      currentLanguage: schema?.language?.toLowerCase() || 'en',
      theme: headerData?.theme || { enabled: false, defaultTheme: 'system' },
      // 添加多语言相关属性
      languageInfo: schema?.languageInfo,
      languageVersions: schema?.languageVersions,
      languageUrls: schema?.languageUrls,
      // 添加语言切换回调函数
      onLanguageChange: (langCode: string) => {
        logger.info('[ MobilePreview ] Language change requested:', { langCode });
        // 在预览模式中，我们只记录语言切换请求，不实际切换语言
        // 在实际的渲染端，这将是一个实际的链接跳转
      }
    };
    
    logger.info('[ MobilePreview ] Processed header props', {
      linksCount: headerProps.links.length,
      actionsCount: headerProps.actions.length,
      logo: headerProps.logo,
      hasLanguageInfo: !!headerProps.languageInfo,
      currentLanguage: headerProps.currentLanguage
    });
    return headerProps;
  };
  
  const processFooterData = (footerData: any): Record<string, any> | undefined => {
    if (!footerData) {
      logger.warn('[ MobilePreview ] No footer data to process', {});
      return undefined;
    }
    
    logger.info('[ MobilePreview ] Processing footer data', {
      variant: footerData.variant,
      hasLinks: !!footerData.links && footerData.links.length > 0
    });
    
    const footerProps = {
      variant: footerData?.variant || 'default',
      id: footerData?.id || 'default-footer',
      logo: footerData?.logo || { url: '', alt: 'Footer Logo' },
      links: footerData?.links || [],
      copyright: footerData?.copyright || `© ${new Date().getFullYear()} LitPage. All rights reserved.`,
      socialMedia: footerData?.socialMedia || []
    };
    
    logger.info('[ MobilePreview ] Processed footer props', {
      linksCount: footerProps.links.length,
      hasSocialMedia: footerProps.socialMedia.length > 0
    });
    return footerProps;
  };
  
  // 处理当前语言的头尾数据
  const currentHeaders = schema?.headers?.[schema?.language] 
    ? processHeaderData(schema.headers[schema.language]) 
    : undefined;
    
  const currentFooters = schema?.footers?.[schema?.language] 
    ? processFooterData(schema.footers[schema.language]) 
    : undefined;

  // 渲染前记录最终数据
  if (schema && currentHeaders && currentFooters) {
    logger.info('[ MobilePreview ] Rendering with processed data', {
      theme: schema.theme,
      headerVariant: currentHeaders.variant,
      footerVariant: currentFooters.variant,
      headerLinksCount: currentHeaders.links.length,
      footerLinksCount: currentFooters.links.length
    });
  }

  return <BlockEditorProvider>
    <div className={clsx(
      "overflow-hidden",
    )}
      data-theme={schema?.theme}
    >
      {schema ? (
        <PageRenderProviders editable={false} domain={schema?.domain}>
          
          <PageRender
            inCanva
            domain={schema?.domain}
            headers={currentHeaders}
            footers={currentFooters}
            schema={schema?.schema}
          ></PageRender>
        </PageRenderProviders>
      ) : (
        <div className="h-full w-full flex items-center justify-center">
          <p className="text-muted-foreground">等待数据加载...</p>
        </div>
      )}
    </div>
  </BlockEditorProvider>
}
