import { useEffect } from 'react'
import { useFormContext } from 'react-hook-form'
import { Card } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from '@/components/ui/form'
import { HelpCircle } from 'lucide-react'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { PAGE_TYPES } from '@/constants/page-types'
import { FormData } from './types'
import { cn } from '@/lib/utils'
import { LanguageSelect } from './language-select'
import Logger from '@/lib/logger'
import { WebsiteModel } from '@/modules/website/model'

const logger = new Logger('PageTypeStep')

interface PageTypeStepProps {
  website?: WebsiteModel | null;
}

export function PageTypeStep({ website }: PageTypeStepProps) {
  const form = useFormContext<FormData>()
  const defaultLanguage = website?.defaultLanguage || 'EN'

  // 初始化语言字段
  useEffect(() => {
    if (!form.getValues('language')) {
      form.setValue('language', defaultLanguage)
    }
  }, [form, defaultLanguage])

  // 当页面类型改变时，更新默认的 slug
  useEffect(() => {
    const pageType = form.watch('pageType')
    const selectedType = PAGE_TYPES.find((type) => type.id === pageType)
    if (selectedType) {
      logger.info('Page type changed:', {
        type: selectedType.id,
        title: selectedType.title,
        defaultSlug: selectedType.defaultSlug
      })
      form.setValue('slug', selectedType.defaultSlug)
    }
  }, [form.watch('pageType')])

  return (
    <div className="space-y-8">
      <div className="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4">
        {PAGE_TYPES.map((type) => {
          const Icon = type.icon
          return (
            <FormField
              key={type.id}
              control={form.control}
              name="pageType"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Card
                      className={cn(
                        'group relative cursor-pointer border-2 p-4 transition-colors hover:bg-accent/50',
                        field.value === type.id
                          ? 'border-primary bg-accent/25'
                          : 'border-muted bg-background'
                      )}
                      onClick={() => field.onChange(type.id)}
                    >
                      <div className="flex flex-col items-center gap-2 text-center">
                        <Icon className="h-8 w-8 text-primary" />
                        <span className="font-medium">{type.title}</span>
                      </div>
                      <TooltipProvider delayDuration={300}>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <button
                              type="button"
                              className="absolute right-2 top-2 opacity-0 transition-opacity group-hover:opacity-100"
                              onClick={(e) => e.stopPropagation()}
                            >
                              <HelpCircle className="h-4 w-4 text-muted-foreground" />
                            </button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="max-w-xs text-sm">{type.description}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </Card>
                  </FormControl>
                </FormItem>
              )}
            />
          )
        })}
      </div>

      <div className="grid gap-6">
        <FormField
          control={form.control}
          name="title"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Page Title</FormLabel>
              <FormControl>
                <Input 
                  placeholder="Enter page title" 
                  {...field}
                />
              </FormControl>
              <FormDescription>
                The title will be displayed at the top of your page
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <LanguageSelect defaultLanguage={defaultLanguage} />

        <FormField
          control={form.control}
          name="slug"
          render={({ field }) => (
            <FormItem>
              <FormLabel>URL Path</FormLabel>
              <FormControl>
                <Input 
                  placeholder="Enter URL path" 
                  {...field}
                  className="font-mono"
                />
              </FormControl>
              <FormDescription>
                This is the unique URL path for your page. It must be unique within each language.
              </FormDescription>
              <FormMessage className="text-destructive font-medium" />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description (Optional)</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Enter page description"
                  className="resize-none"
                  {...field}
                />
              </FormControl>
              <FormDescription>
                A brief description of what this page is about
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  )
}
