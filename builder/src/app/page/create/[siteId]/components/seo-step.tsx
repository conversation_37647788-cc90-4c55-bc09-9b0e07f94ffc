import { useFormContext } from 'react-hook-form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
// import { Separator } from '@/components/ui/separator'
import { Button } from '@/components/ui/button'
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from '@/components/ui/form'
// import { Badge } from '@/components/ui/badge'
import { Image as ImageIcon, Globe, Share2, ArrowRight, Settings2, FileText } from 'lucide-react'
import { PreviewCard } from '@/components/preview-card'
import { FormData } from './types'
import { useCallback } from 'react'
import { useParams } from 'next/navigation'
import { useFetchWebsite } from '@/lib/hooks/use-website'
import Logger from '@/lib/logger'

const logger = new Logger('SeoStep')

interface SeoStepProps {
  onSkip?: () => void;
}

export function SeoStep({ onSkip }: SeoStepProps) {
  const form = useFormContext<FormData>()
  const params = useParams<{ siteId: string }>()
  const { data: website } = useFetchWebsite(params?.siteId)

  // 根据网站配置获取正确的域名
  const getDomainUrl = () => {
    if (!website) {
      return 'https://example.com'
    }

    // 如果有自定义域名且状态为 ACTIVE，则使用自定义域名
    if (website.customDomain?.domain && website.customDomain.status === 'ACTIVE') {
      return `https://${website.customDomain.domain}`
    }
    
    // 否则使用二级域名 xxx.lit.page
    return `https://${website.domain || 'example'}.lit.page`
  }

  // 构建完整的 URL，确保不会出现双斜杠
  const getFullUrl = (slug: string) => {
    const baseUrl = getDomainUrl();
    // 移除 slug 开头的斜杠，避免双斜杠问题
    const cleanSlug = slug ? (slug.startsWith('/') ? slug.substring(1) : slug) : '';
    return `${baseUrl}/${cleanSlug}`;
  }

  const handleSkip = useCallback(() => {
    // 清空所有 SEO 相关字段
    form.setValue('metaKeywords', [])
    form.setValue('metaDescription', '')
    form.setValue('ogTitle', '')
    form.setValue('ogDescription', '')
    form.setValue('ogImage', '')
    // 跳转到下一步
    onSkip?.()
  }, [form, onSkip])

  const handleTabChange = useCallback((tab: string) => {
    // 使用 requestAnimationFrame 延迟到下一帧执行
    requestAnimationFrame(() => {
      logger.info('SEO step data:', {
        tab,
        pageType: form.getValues('pageType'),
        createMethod: form.getValues('createMethod'),
        basicInfo: {
          title: form.getValues('title'),
          description: form.getValues('description'),
          slug: form.getValues('slug')
        },
        seo: {
          metaDescription: form.getValues('metaDescription'),
          ogTitle: form.getValues('ogTitle'),
          ogDescription: form.getValues('ogDescription'),
          ogImage: form.getValues('ogImage')
        }
      })
    })
  }, [form])

  const pageTitle = form.watch('title') || 'Page Title'
  const pageDescription = form.watch('description') || 'Page Description'

  return (
    <div className="space-y-8">
      {/* 跳过提示 */}
      <div className="flex items-center justify-between p-4 rounded-lg bg-muted/50">
        <div className="flex items-center gap-2">
          <Settings2 className="h-5 w-5 text-muted-foreground" />
          <div>
            <h4 className="font-medium">SEO Settings (Optional)</h4>
            <p className="text-sm text-muted-foreground mt-1">
              You can skip this step and configure SEO settings later
            </p>
          </div>
        </div>
        <Button variant="ghost" className="gap-2" onClick={handleSkip}>
          Skip this step
          <ArrowRight className="w-4 h-4" />
        </Button>
      </div>

      <Tabs defaultValue="basic" className="space-y-6" onValueChange={handleTabChange}>
        <TabsList>
          <TabsTrigger value="basic" className="gap-2">
            <Globe className="h-4 w-4" />
            Basic SEO
          </TabsTrigger>
          <TabsTrigger value="social" className="gap-2">
            <Share2 className="h-4 w-4" />
            Social Sharing
          </TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-6">
          <div className="space-y-4">
            <FormField
              control={form.control}
              name="metaKeywords"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Meta Keywords</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter keywords separated by commas (e.g. web design, development)"
                      value={typeof field.value === 'string' ? field.value : field.value?.join(', ') || ''}
                      onChange={e => field.onChange(e.target.value)}
                      onBlur={e => {
                        const value = e.target.value
                        const keywords = value.split(',').map(k => k.trim()).filter(Boolean)
                        field.onChange(keywords)
                      }}
                    />
                  </FormControl>
                  <FormDescription>
                    Keywords to help search engines understand your page content (comma separated)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="metaDescription"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Meta Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter a brief description of your page for search engines"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Recommended length: 150-160 characters
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Search Result Preview */}
            <PreviewCard title="Search Result Preview" dotColor="bg-blue-500">
              <div className="space-y-1">
                <p className="text-[16px] text-blue-600 hover:underline cursor-pointer line-clamp-1 font-medium">
                  {form.watch('title') || 'Your Page Title'}
                </p>
                <p className="text-[14px] text-green-700">
                  {getFullUrl(form.watch('slug'))}
                </p>
                <p className="text-[14px] text-gray-600 line-clamp-2">
                  {form.watch('metaDescription') || 'Add a meta description to see how your page might appear in search results.'}
                </p>
              </div>
            </PreviewCard>
          </div>
        </TabsContent>

        <TabsContent value="social" className="space-y-6">
          <div className="grid grid-cols-5 gap-8">
            <div className="col-span-3 space-y-4">
              <FormField
                control={form.control}
                name="ogTitle"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Social Title</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter a title for social media sharing"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      The title that appears when sharing on social media
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="ogDescription"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Social Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter a description for social media sharing"
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      The description that appears when sharing on social media
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="ogImage"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Social Image URL</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter an image URL for social media sharing"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      The image that appears when sharing on social media
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="col-span-2 space-y-4">
              {/* Social Preview */}
              <PreviewCard title="Social Preview" dotColor="bg-orange-500" className="sticky top-4">
                <div>
                  {form.watch('ogImage') ? (
                    <div className="aspect-[1.91/1] w-full overflow-hidden rounded-md bg-muted">
                      {/* eslint-disable-next-line @next/next/no-img-element */}
                      <img
                        src={form.watch('ogImage')}
                        alt="Social preview"
                        className="h-full w-full object-cover"
                        onError={(e) => {
                          // 图片加载失败时显示占位图标
                          e.currentTarget.style.display = 'none'
                          e.currentTarget.nextElementSibling?.classList.remove('hidden')
                        }}
                      />
                      <div className="hidden absolute inset-0 flex items-center justify-center">
                        <ImageIcon className="h-8 w-8 text-muted-foreground" />
                      </div>
                    </div>
                  ) : (
                    <div className="aspect-[1.91/1] w-full rounded-md bg-muted flex items-center justify-center">
                      <ImageIcon className="h-8 w-8 text-muted-foreground" />
                    </div>
                  )}
                  <div className="mt-3">
                    <p className="text-sm text-muted-foreground">
                      {getFullUrl(form.watch('slug'))}
                    </p>
                    <h3 className="text-[16px] font-medium line-clamp-1 mt-1">
                      {form.watch('ogTitle') || pageTitle}
                    </h3>
                    <p className="mt-1 text-[14px] text-muted-foreground line-clamp-2">
                      {form.watch('ogDescription') || pageDescription}
                    </p>
                  </div>
                </div>
              </PreviewCard>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
