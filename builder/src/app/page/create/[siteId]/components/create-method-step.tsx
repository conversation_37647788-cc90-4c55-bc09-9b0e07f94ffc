import { useEffect } from 'react'
import { useFormContext } from 'react-hook-form'
import { Card } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from '@/components/ui/form'
import { HelpCircle } from 'lucide-react'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { CREATE_METHODS } from '@/constants/create-methods'
import { FormData } from './types'
import { cn } from '@/lib/utils'
import Logger from '@/lib/logger'

const logger = new Logger('CreateMethodStep')

export function CreateMethodStep() {
  const form = useFormContext<FormData>()
  const createMethod = form.watch('createMethod')
  const pageType = form.watch('pageType')

  useEffect(() => {
    // 默认选中第一个选项（空白页）
    if (!form.getValues('createMethod')) {
      logger.info('Setting default create method: blank')
      form.setValue('createMethod', CREATE_METHODS[0].id)
    }
  }, [])

  const handleMethodChange = (methodId: string) => {
    if (methodId === 'blank') {
      logger.info('Create method step data:', {
        method: methodId,
        pageType: form.getValues('pageType'),
        title: form.getValues('title'),
        description: form.getValues('description'),
        slug: form.getValues('slug')
      })
      logger.info('Selected create method:', {
        method: methodId,
        status: 'available'
      })
      form.setValue('createMethod', methodId)
    } else {
      logger.info('Selected create method:', {
        method: methodId,
        status: 'coming_soon'
      })
    }
  }

  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
        {CREATE_METHODS.map((method) => {
          const Icon = method.icon
          const isDisabled = method.id !== 'blank'

          return (
            <FormField
              key={method.id}
              control={form.control}
              name="createMethod"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Card
                      className={cn(
                        'group relative cursor-pointer border-2 p-4 transition-colors',
                        isDisabled
                          ? 'cursor-not-allowed opacity-60'
                          : 'hover:bg-accent/50',
                        field.value === method.id
                          ? 'border-primary bg-accent/25'
                          : 'border-muted bg-background'
                      )}
                      onClick={() => !isDisabled && handleMethodChange(method.id)}
                    >
                      <div className="flex h-[88px] flex-col items-center justify-center gap-2">
                        <Icon className="h-8 w-8 text-primary" />
                        <div className="flex flex-col items-center">
                          <span className="font-medium">{method.title}</span>
                          {isDisabled && (
                            <Badge variant="secondary" className="mt-1">
                              Coming Soon
                            </Badge>
                          )}
                        </div>
                      </div>
                      <TooltipProvider delayDuration={300}>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <button
                              type="button"
                              className="absolute right-2 top-2 opacity-0 transition-opacity group-hover:opacity-100"
                              onClick={(e) => e.stopPropagation()}
                            >
                              <HelpCircle className="h-4 w-4 text-muted-foreground" />
                            </button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="max-w-xs text-sm">{method.description}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </Card>
                  </FormControl>
                </FormItem>
              )}
            />
          )
        })}
      </div>

      {createMethod === 'template' && (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
          {/* {mockTemplates
            .filter((template) => template.pageType === pageType)
            .map((template) => (
              <FormField
                key={template.id}
                control={form.control}
                name="templateId"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Card
                        className={cn(
                          'group relative cursor-pointer border-2 transition-colors hover:bg-accent/50',
                          field.value === template.id
                            ? 'border-primary bg-accent/25'
                            : 'border-muted bg-background'
                        )}
                        onClick={() => field.onChange(template.id)}
                      >
                        <div className="relative">
                          <img
                            src={template.thumbnail}
                            alt={template.title}
                            className="aspect-video w-full object-cover"
                          />
                          <TooltipProvider delayDuration={300}>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <button
                                  type="button"
                                  className="absolute right-2 top-2 opacity-0 transition-opacity group-hover:opacity-100"
                                  onClick={(e) => e.stopPropagation()}
                                >
                                  <HelpCircle className="h-4 w-4 text-white drop-shadow-md" />
                                </button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p className="max-w-xs text-sm">{template.description}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                        <div className="p-4 text-center">
                          <h3 className="font-medium">{template.title}</h3>
                        </div>
                      </Card>
                    </FormControl>
                  </FormItem>
                )}
              />
            ))} */}
        </div>
      )}

      {createMethod === 'ai' && (
        <div className="space-y-6">
          <FormField
            control={form.control}
            name="aiSettings.prompt"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Describe your page</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe what you want your page to look like and what content it should have..."
                    className="min-h-[100px] resize-none"
                    value={field.value || ''}
                    onChange={field.onChange}
                    onBlur={field.onBlur}
                  />
                </FormControl>
                <FormDescription>
                  Be as specific as possible about the layout, content, and style you want.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="aiSettings.style"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Style</FormLabel>
                <FormControl>
                  <Select
                    value={field.value || ''}
                    onValueChange={field.onChange}
                    onOpenChange={() => field.onBlur()}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a style" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="modern">Modern</SelectItem>
                      <SelectItem value="minimal">Minimal</SelectItem>
                      <SelectItem value="classic">Classic</SelectItem>
                      <SelectItem value="bold">Bold</SelectItem>
                      <SelectItem value="playful">Playful</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormDescription>
                  Choose a style that matches your brand and target audience.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="aiSettings.reference"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Reference URL (Optional)</FormLabel>
                <FormControl>
                  <Input
                    type="url"
                    placeholder="https://example.com"
                    value={field.value || ''}
                    onChange={field.onChange}
                    onBlur={field.onBlur}
                  />
                </FormControl>
                <FormDescription>
                  Add a URL to a website that has a similar style to what you want.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      )}
    </div>
  )
}
