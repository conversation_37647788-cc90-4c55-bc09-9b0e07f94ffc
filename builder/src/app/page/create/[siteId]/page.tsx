'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Form } from '@/components/ui/form'
import { useToast } from '@/components/ui/use-toast'
import { Layout, Search, Wand, ChevronLeft, ChevronRight, FileText, Rocket, Loader2, PlusCircle } from 'lucide-react'
import { PageTypeStep } from './components/page-type-step'
import { SeoStep } from './components/seo-step'
import { CreateMethodStep } from './components/create-method-step'
import { ProgressIndicator } from './components/progress-indicator'
import { PAGE_TYPES } from '@/constants/page-types'
import type { Step, FormData } from './components/types'
import { formSchema } from './components/types'
import { useCreatePage, useValidatePageSlug } from '@/lib/hooks/use-page'
import { useFetchWebsite } from '@/lib/hooks/use-website'
import { useQueryClient } from '@tanstack/react-query'
import Logger from '@/lib/logger'

const logger = new Logger('CreatePage')

const steps: Step[] = [
  {
    title: 'Page Type',
    description: 'Choose a type for your page',
    icon: FileText,
    fields: ['title', 'description', 'slug', 'pageType', 'language'],
  },
  {
    title: 'SEO Settings',
    description: 'Configure SEO settings for your page',
    icon: Search,
    fields: ['metaKeywords', 'metaDescription', 'ogTitle', 'ogDescription', 'ogImage'],
    optional: true,
  },
  {
    title: 'Create Method',
    description: 'Choose how to create your page',
    icon: Rocket,
    fields: ['createMethod', 'templateId', 'aiSettings'],
  },
]

export default function CreatePage({ params }: { params: { siteId: string } }) {
  const router = useRouter()
  const { toast } = useToast()
  const [currentStep, setCurrentStep] = useState<number>(0)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const queryClient = useQueryClient()
  
  // 使用 React Query 获取网站信息
  const { data: currentWebsite, isLoading: isWebsiteLoading } = useFetchWebsite(params.siteId)
  const defaultLanguage = currentWebsite?.defaultLanguage || 'EN'

  const createPage = useCreatePage()
  const validateSlug = useValidatePageSlug()

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    mode: 'onTouched',
    defaultValues: {
      title: '',
      description: '',
      slug: '',
      pageType: 'landing',
      language: defaultLanguage, // 使用从 API 获取的默认语言
      createMethod: 'blank',
      metaKeywords: [],
      metaDescription: '',
      ogTitle: '',
      ogDescription: '',
      ogImage: '',
      templateId: undefined,
      aiSettings: {
        prompt: '',
        style: 'modern',
        reference: ''
      }
    },
  })

  // 当网站数据加载完成后，更新表单中的默认语言
  useEffect(() => {
    if (currentWebsite?.defaultLanguage && !form.getValues('language')) {
      form.setValue('language', currentWebsite.defaultLanguage)
    }
  }, [currentWebsite, form])

  const isLastStep = currentStep === steps.length - 1

  const next = async () => {
    try {
      // SEO 步骤是可选的，直接进入下一步
      if (currentStep === 1) {
        setCurrentStep(prev => prev + 1)
        return
      }

      // 验证当前步骤的字段
      const fields = stepsWithContent[currentStep].fields
      const isValid = await form.trigger(fields.filter(field => !field.includes('.')) as (keyof FormData)[])

      if (isValid) {
        if (!isLastStep) {
          setCurrentStep(prev => prev + 1)
        }
      } else {
        toast({
          title: 'Validation Error',
          description: 'Please fix the errors before proceeding',
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('Step validation error:', error)
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'An error occurred',
        variant: 'destructive',
      })
    }
  }

  const prev = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1)
    }
  }

  const stepContents = [
    <PageTypeStep key="page-type" website={currentWebsite} />,
    <SeoStep key="seo" onSkip={() => setCurrentStep(prev => prev + 1)} />,
    <CreateMethodStep key="create-method" />
  ]

  const stepsWithContent = steps.map((step, index) => ({
    ...step,
    content: stepContents[index]
  }))

  // 创建页面的处理函数
  const handleCreatePage = async (data: FormData) => {
    try {
      setIsSubmitting(true)
      const pageType = data.pageType
      const selectedType = PAGE_TYPES?.find(type => type.id === pageType)

      logger.info('Starting page creation', {
        timestamp: new Date().toISOString(),
        step: {
          current: currentStep,
          isLast: isLastStep,
        },
        formData: {
          // 基本信息
          basic: {
            title: data.title,
            description: data.description,
            slug: data.slug,
          },
          // 页面类型
          pageType: {
            type: pageType,
            selectedType: selectedType,
          },
          // 创建方法
          createMethod: {
            method: data.createMethod,
            templateId: data.templateId,
            aiSettings: data.aiSettings,
          },
          // SEO 设置
          seo: {
            metaDescription: data.metaDescription,
            metaKeywords: data.metaKeywords,
            ogTitle: data.ogTitle,
            ogDescription: data.ogDescription,
            ogImage: data.ogImage,
          },
          // 语言
          language: data.language,
          // 表单状态
          formState: {
            isDirty: form.formState.isDirty,
            isValid: form.formState.isValid,
            errors: form.formState.errors,
          }
        },
        siteId: params.siteId,
      })

      // 1. 验证 slug 可用性
      logger.info('Validating slug', {
        websiteId: params.siteId,
        slug: data.slug,
        language: data.language,
      })

      const slugValidation = await validateSlug.mutateAsync({
        websiteId: params.siteId,
        slug: data.slug,
        language: data.language
      })

      logger.info('Slug validation result:', {
        timestamp: new Date().toISOString(),
        available: slugValidation.available,
        slug: data.slug,
        language: data.language,
      })

      if (!slugValidation.available) {
        form.setError('slug', {
          type: 'manual',
          message: 'This URL is already taken. Please choose a different one.',
        })
        
        // 显示错误提示并停止表单提交
        toast({
          title: "URL Already Exists",
          description: "This URL is already taken. Please choose a different one.",
          variant: "destructive",
          duration: 5000,
        })
        
        // 回到第一步以便用户修改 slug
        setCurrentStep(0)
        
        setIsSubmitting(false)
        return
      }

      // 2. 创建页面
      logger.info('Calling create page API', {
        timestamp: new Date().toISOString(),
        requestData: {
          title: data.title,
          description: data.description,
          slug: data.slug,
          pageType: data.pageType,
          language: data.language,
          metaKeywords: data.metaKeywords?.join(', '),
          metaDescription: data.metaDescription,
          ogTitle: data.ogTitle,
          ogDescription: data.ogDescription,
          ogImage: data.ogImage,
          websiteId: params.siteId,
        },
      })

      const result = await createPage.mutateAsync({
        title: data.title,
        description: data.description,
        slug: data.slug,
        pageType: data.pageType,
        language: data.language,
        metaKeywords: data.metaKeywords?.join(', '),
        metaDescription: data.metaDescription,
        ogTitle: data.ogTitle,
        ogDescription: data.ogDescription,
        ogImage: data.ogImage,
        websiteId: params.siteId,
      })

      logger.info('Page created successfully', {
        timestamp: new Date().toISOString(),
        response: {
          pageId: result.id,
          nanoid: result.nanoid,
          title: result.title,
          slug: result.slug,
          pageType: result.pageType,
          websiteId: result.websiteId,
          workspaceId: result.workspaceId,
        },
        redirectUrl: `/site/${params.siteId}/page/${result.id}`,
      })

      toast({
        title: "🎉 Success!",
        description: "Your page has been created successfully",
      })

      // 短暂延迟跳转，让用户看到成功提示
      setTimeout(() => {
        router.push(`/site/${params.siteId}/page/${result.id}`)
      }, 300)
    } catch (error) {
      logger.error('Error occurred', {
        timestamp: new Date().toISOString(),
        error: {
          name: error instanceof Error ? error.name : 'Unknown',
          message: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : undefined,
        },
        context: {
          currentStep,
          formData: data,
          siteId: params.siteId,
        },
      })

      toast({
        title: "Failed to Create",
        description: error instanceof Error ? error.message : "An error occurred while creating your page",
        variant: "destructive",
        duration: 5000,
      })
    } finally {
      setIsSubmitting(false)
      logger.info('Operation completed', {
        timestamp: new Date().toISOString(),
        status: 'completed',
        isSubmitting: false,
      })
    }
  }

  const onSubmit = async (data: FormData) => {
    // 如果不是最后一步，不处理表单提交
    if (!isLastStep) {
      logger.info('Not last step, skipping submission', {
        currentStep,
        totalSteps: steps.length,
      })
      return
    }

    await handleCreatePage(data)
  }

  // 如果网站数据正在加载，显示加载状态
  if (isWebsiteLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p className="text-lg text-muted-foreground">Loading website information...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container max-w-2xl mx-auto py-20 px-4">
        {/* 页面标题 */}
        <div className="text-center mb-10">
          <h1 className="text-3xl font-bold tracking-tight mb-2 text-gray-900 dark:text-gray-50">
            Create Page
          </h1>
          <p className="text-muted-foreground">
            Create a new page for your website in three simple steps
          </p>
        </div>

        {/* 进度指示器 */}
        <div className="mb-8">
          <ProgressIndicator
            steps={stepsWithContent}
            currentStep={currentStep}
          />
        </div>

        <Card>
          <CardHeader className="flex flex-row items-center gap-4">
            {React.createElement(stepsWithContent[currentStep]?.icon || 'div', {
              className: "w-6 h-6 text-primary animate-pulse",
            })}
            <div>
              <CardTitle className="text-lg">{stepsWithContent[currentStep]?.title}</CardTitle>
              <CardDescription className="text-base mt-1">
                {stepsWithContent[currentStep]?.description}
              </CardDescription>
            </div>
          </CardHeader>

          <CardContent className="pt-8">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                {currentStep === 0 && (
                  <PageTypeStep website={currentWebsite} />
                )}
                {currentStep === 1 && (
                  <SeoStep onSkip={() => setCurrentStep(prev => prev + 1)} />
                )}
                {currentStep === 2 && (
                  <CreateMethodStep />
                )}

                <div className="flex justify-between pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={prev}
                    disabled={currentStep === 0}
                    className="gap-2"
                  >
                    <ChevronLeft className="h-4 w-4" />
                    Previous
                  </Button>

                  {isLastStep ? (
                    <Button
                      type="button"
                      onClick={async (e) => {
                        e.preventDefault()
                        const data = form.getValues()
                        await handleCreatePage(data)
                      }}
                      disabled={isSubmitting}
                      className="gap-2"
                    >
                      {isSubmitting ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Creating...
                        </>
                      ) : (
                        <>
                          <PlusCircle className="h-4 w-4" />
                          Create Page
                        </>
                      )}
                    </Button>
                  ) : (
                    <Button
                      type="button"
                      onClick={next}
                      className="gap-2"
                    >
                      Next
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>

        {/* 帮助文档入口 */}
        <div className="mt-8 text-center">
          <p className="text-sm text-muted-foreground">
            Need help? Check out our{' '}
            <a
              href="https://docs.litpage.com/pages/create"
              target="_blank"
              rel="noopener noreferrer"
              className="font-medium text-primary hover:text-primary/80 underline underline-offset-4"
            >
              documentation
            </a>{' '}
            for detailed instructions on creating pages
          </p>
        </div>
      </div>
    </div>
  )
}
