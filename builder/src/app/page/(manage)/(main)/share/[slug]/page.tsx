"use client"
import { CheckCircleIcon, QrCodeIcon } from '@heroicons/react/20/solid'
import { FacebookShareButton, EmailShareButton, LinkedinShareButton, TwitterShareButton, WhatsappShareButton } from "react-share";

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import CardSelect from "./cardSelect";
import { useCopyToClipboard } from 'usehooks-ts'
import { usePageStore } from '@/modules/page/store';

// import CodeMirror from '@uiw/react-codemirror';
// import { html } from '@codemirror/lang-html';

import { getEmbedCode } from './embedCode';

import {
  Card,
  CardContent,
} from "@/components/ui/card"


import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer"
import { useEffect, useState } from 'react';




const getCode = (id: string, type: string = '') => {
  return getEmbedCode(id, type);
}
export default function Page() {
  const [copyed, copy] = useCopyToClipboard()
  const formState = usePageStore();
  const [open, setOpen] = useState(false);
  const [type, setType] = useState<string>('');
  const [url, setUrl] = useState('');
  const [code, setCode] = useState('');

  
  // useEffect(() => {

  //   if (formState.currentForm?.nanoid) {
  //     setUrl(`${process.env.NEXT_PUBLIC_FORM_BASE}/page/${formState.currentForm?.nanoid}`);
  //   }
  // }, [formState])

  // useEffect(() => {
  //   if (formState.currentForm?.nanoid && type) {
  //     setCode(getCode(formState.currentForm?.nanoid, type));
  //   }
  // }, [type, formState])

  return <>

    <CardContent>

      <div className="rounded-md bg-green-50 p-4 mb-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <CheckCircleIcon className="h-5 w-5 text-green-400" aria-hidden="true" />
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium text-green-800">Your form is ready to share.</p>
          </div>

        </div>
      </div>

      <div className="max-w-xl">

        <div className="flex justify-between items-center mb-4 mt-10">
          <div className="text-gray-600 text-base font-semibold">Form link</div>

        </div>
        <div className="flex space-x-2">
          <Input value={url} readOnly />
          <Button variant="secondary" className="shrink-0 w-32" onClick={() => copy(url)}>
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" className="-ml-0.5 mr-2 h-4 w-4"><path d="M8 2a1 1 0 000 2h2a1 1 0 100-2H8z"></path><path d="M3 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v6h-4.586l1.293-1.293a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L10.414 13H15v3a2 2 0 01-2 2H5a2 2 0 01-2-2V5zM15 11h2a1 1 0 110 2h-2v-2z"></path></svg>
            {copyed ? 'copyed' : 'Copy Link'}
          </Button>
        </div>
        {/* <div className="flex space-x-2 mt-4">
          <div className="inline-block">
            <Button variant="outline" className='rounded-full' size="icon">
              <QrCodeIcon className="h-4 w-4" />
            </Button>
          </div>
          <div className="inline-block">
            <Button variant="outline" size="icon" className='rounded-full' asChild>
              <FacebookShareButton url={url} title={`${formState.currentForm?.title}`} resetButtonStyle={false}>
                <svg xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="xMidYMid meet" className='w-4 h-4 fill-gray-600' viewBox="0 0 320 512"><path d="M279.14 288l14.22-92.66h-88.91v-60.13c0-25.35 12.42-50.06 52.24-50.06h40.42V6.26S260.43 0 225.36 0c-73.22 0-121.08 44.38-121.08 124.72v70.62H22.89V288h81.39v224h100.17V288z"></path></svg>
              </FacebookShareButton>
            </Button>
          </div>


          <div className="inline-block">
            <Button variant="outline" className='rounded-full' size="icon" asChild>
              <LinkedinShareButton url={url} title={`${formState.currentForm?.title}`} resetButtonStyle={false}>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" className='w-4 h-4 fill-gray-600'><path d="M416 32H31.9C14.3 32 0 46.5 0 64.3v383.4C0 465.5 14.3 480 31.9 480H416c17.6 0 32-14.5 32-32.3V64.3c0-17.8-14.4-32.3-32-32.3zM135.4 416H69V202.2h66.5V416zm-33.2-243c-21.3 0-38.5-17.3-38.5-38.5S80.9 96 102.2 96c21.2 0 38.5 17.3 38.5 38.5 0 21.3-17.2 38.5-38.5 38.5zm282.1 243h-66.4V312c0-24.8-.5-56.7-34.5-56.7-34.6 0-39.9 27-39.9 54.9V416h-66.4V202.2h63.7v29.2h.9c8.9-16.8 30.6-34.5 62.9-34.5 67.2 0 79.7 44.3 79.7 101.9V416z"></path></svg>

              </LinkedinShareButton>
            </Button>
          </div>
          <div className="inline-block">
            <Button variant="outline" className='rounded-full' size="icon" asChild>
              <WhatsappShareButton url={url} title={`${formState.currentForm?.title}`} resetButtonStyle={false}>
                <svg xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="xMidYMid meet" viewBox="0 0 428 512" className='w-4 h-4 fill-gray-600'><path d="M380.9 97.1C339 55.1 283.2 32 223.9 32c-122.4 0-222 99.6-222 222 0 39.1 10.2 77.3 29.6 111L0 480l117.7-30.9c32.4 17.7 68.9 27 106.1 27h.1c122.3 0 224.1-99.6 224.1-222 0-59.3-25.2-115-67.1-157zm-157 341.6c-33.2 0-65.7-8.9-94-25.7l-6.7-4-69.8 18.3L72 359.2l-4.4-7c-18.5-29.4-28.2-63.3-28.2-98.2 0-101.7 82.8-184.5 184.6-184.5 49.3 0 95.6 19.2 130.4 54.1 34.8 34.9 56.2 81.2 56.1 130.5 0 101.8-84.9 184.6-186.6 184.6zm101.2-138.2c-5.5-2.8-32.8-16.2-37.9-18-5.1-1.9-8.8-2.8-12.5 2.8-3.7 5.6-14.3 18-17.6 21.8-3.2 3.7-6.5 4.2-12 1.4-32.6-16.3-54-29.1-75.5-66-5.7-9.8 5.7-9.1 16.3-30.3 1.8-3.7.9-6.9-.5-9.7-1.4-2.8-12.5-30.1-17.1-41.2-4.5-10.8-9.1-9.3-12.5-9.5-3.2-.2-6.9-.2-10.6-.2-3.7 0-9.7 1.4-14.8 6.9-5.1 5.6-19.4 19-19.4 46.3 0 27.3 19.9 53.7 22.6 57.4 2.8 3.7 39.1 59.7 94.8 83.8 35.2 15.2 49 16.5 66.6 13.9 10.7-1.6 32.8-13.4 37.4-26.4 4.6-13 4.6-24.1 3.2-26.4-1.3-2.5-5-3.9-10.5-6.6z"></path></svg>
              </WhatsappShareButton>
            </Button>
          </div>


          <div className="inline-block">
            <Button variant="outline" className='rounded-full' size="icon" asChild>
              <TwitterShareButton url={url} title={`${formState.currentForm?.title}`} resetButtonStyle={false}>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" className='w-4 h-4 fill-gray-600'><path d="M389.2 48h70.6L305.6 224.2 487 464H345L233.7 318.6 106.5 464H35.8L200.7 275.5 26.8 48H172.4L272.9 180.9 389.2 48zM364.4 421.8h39.1L151.1 88h-42L364.4 421.8z"></path></svg>

              </TwitterShareButton>
            </Button>
          </div>
          <div className="inline-block">
            <Button variant="outline" className='rounded-full' size="icon" asChild>
              <EmailShareButton url={url} title={`${formState.currentForm?.title}`} resetButtonStyle={false}>
                <svg xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="xMidYMid meet" viewBox="0 0 512 512" className='w-4 h-4 fill-gray-600'><path d="M502.3 190.8c3.9-3.1 9.7-.2 9.7 4.7V400c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V195.6c0-5 5.7-7.8 9.7-4.7 22.4 17.4 52.1 39.5 154.1 113.6 21.1 15.4 56.7 47.8 92.2 47.6 35.7.3 72-32.8 92.3-47.6 102-74.1 131.6-96.3 154-113.7zM256 320c23.2.4 56.6-29.2 73.4-41.4 132.7-96.3 142.8-104.7 173.4-128.7 5.8-4.5 9.2-11.5 9.2-18.9v-19c0-26.5-21.5-48-48-48H48C21.5 64 0 85.5 0 112v19c0 7.4 3.4 14.3 9.2 18.9 30.6 23.9 40.7 32.4 173.4 128.7 16.8 12.2 50.2 41.8 73.4 41.4z"></path></svg>

              </EmailShareButton>
            </Button>
          </div>




        </div> */}

      </div>

      <div className='mt-6'>
        <CardSelect onClick={(type: string) => { setType(type);setOpen(true) }}></CardSelect>
      </div>


    </CardContent>


    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerContent>
        <DrawerHeader className='mx-auto w-1/2 '>
          <DrawerTitle>Embed on your website</DrawerTitle>
          <DrawerDescription>Copy your code.</DrawerDescription>
        </DrawerHeader>
        <Card className='mx-auto w-1/2 h-96 py-4'>
          <CardContent>
          {/* <CodeMirror className='w-full mb-4 border border-dashed' height='200px' value={code} readOnly extensions={[html()]} /> */}

          <div className="w-full flex items-center justify-center">
          <Button variant="secondary" onClick={() => copy(code)}>
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" className="-ml-0.5 mr-2 h-4 w-4"><path d="M8 2a1 1 0 000 2h2a1 1 0 100-2H8z"></path><path d="M3 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v6h-4.586l1.293-1.293a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L10.414 13H15v3a2 2 0 01-2 2H5a2 2 0 01-2-2V5zM15 11h2a1 1 0 110 2h-2v-2z"></path></svg>
            {copyed ? 'copyed' : 'Copy Code'}
          </Button>
          </div>
          </CardContent>

          
        </Card>
        <DrawerFooter>
          <DrawerClose>
            <Button variant="outline">close</Button>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>


  </>
}