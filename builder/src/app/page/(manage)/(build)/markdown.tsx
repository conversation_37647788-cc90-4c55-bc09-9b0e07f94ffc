'use client'
// import { useCallback, useEffect } from 'react';
// import CodeMirror, { EditorView } from '@uiw/react-codemirror';
// import { markdown, markdownLanguage } from '@codemirror/lang-markdown';
// import { languages } from '@codemirror/language-data';
import { usePageBuilder } from '@/context/PageBuilderContext';
import MessageBox from '@/components/MessageBox';
import './markdown.css';
import { usePageStore } from '@/modules/page/store';
import { usePageEditorStore } from '@/hooks/usePageEditorStore';

const MarkdownEditor = ({ formId }: any) => {
  const { generatePage } = usePageBuilder();
  // const resetPageConfig = usePageStore((state) => state.resetPageConfig);
  // const setMarkdown = usePageStore((state) => state.setMarkdown);
  // const currentPage = usePageStore((state) => state.currentPage);
  // const md = usePageStore((state) => state.currentPage?.markdown);
  // const updatePage = usePageStore((state) => state.updatePage);
  // const fetchPage = usePageStore((state) => state.fetchPage);

  const formEditorState = usePageEditorStore();

  // const isMarkdownCompleted = usePageEditorStore((state) => state.isMarkdownCompleted);

  // const onChange = useCallback((val: string) => {
  //   setMarkdown(val);
  // }, [setMarkdown]);


  // useEffect(() => {
  //   formEditorState.setMarkdownLoading(isMarkdownLoading);
  // }, [isMarkdownLoading])


  // useEffect(() => {
  //   if (isMarkdownCompleted && md) {
  //     generatePage({ userPrompt: md }).then(() => {
  //       formEditorState.setCompleted(true);
  //     })
  //   }
  // }, [isMarkdownCompleted, md])

return (
  <>
    <MessageBox showHistory formId={formId} onClick={() => {
      // resetPageConfig();
      // setPageSchema({
      //   uiSchema: {},
      //   jsonSchema: {
      //     properties: {}
      //   },
      //   submit: '',
      // });
      generatePage({ userPrompt: '' }).then(() => {
        formEditorState.setCompleted(true);
      })
    }}
      onChangeVersion={(item) => {
        // updatePage(formId, { currentVersionId: item.id }).then(() => fetchPage(formId));
      }}
    >
      <div style={{ minHeight: 'calc(100vh - 185px)' }}>
        {/* <CodeMirror 
          value={currentPage?.markdown}
          height="calc(100vh - 185px)"
          // editable={!isMarkdownLoading}
          extensions={[
            markdown({ base: markdownLanguage, codeLanguages: languages }),
            EditorView.lineWrapping,
          ]} 
          onChange={onChange}
        /> */}
      </div>
    </MessageBox>
  </>
);
};

export default MarkdownEditor;
