import { Suspense } from 'react'
import { dehydrate } from '@tanstack/react-query'
import { getQueryClient } from '@/lib/getQueryClient'
import { Hydrate } from '@/components/hydrate'
import { ErrorBoundary } from '@/components/error-boundary'
import { SiteContent } from '@/app/site/components/site-content'
import { SiteOverviewHeader } from './components/site-overview-header'
import { SiteMapContainer } from './components/site-map-container'
import { GridViewContainer } from './components/grid-view-container'
import websiteService from '@/modules/website/service'
import { websiteKeys } from '@/lib/api/queryKeys'
import { SitePageContent } from './components/site-page-content'

/**
 * Site overview page component with server-side data prefetching
 */
export default async function Page({ params }: { params: { siteId: string } }) {
  const queryClient = getQueryClient()

  // Prefetch website list data on the server
  await queryClient.prefetchQuery({
    queryKey: websiteKeys.lists(),
    queryFn: websiteService.getWebsites,
    staleTime: 60 * 1000, // Data is considered fresh for 1 minute
  })

  // Dehydrate the query cache and pass it to the Hydrate component
  const dehydratedState = dehydrate(queryClient)

  return (
    <ErrorBoundary>
      <Hydrate state={dehydratedState}>
        <SitePageContent params={params} />
      </Hydrate>
    </ErrorBoundary>
  )
}
