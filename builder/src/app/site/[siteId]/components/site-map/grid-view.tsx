import React, { useCallback, useState } from 'react';
import { useEnhancedWebsitePages } from '@/lib/hooks/use-enhanced-website-pages';
import { PageCard } from './page-card';
import { GridViewControls } from './grid-view-controls';
import { Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Logger } from '@/lib/utils/logger';

// 创建日志记录器
const logger = new Logger('GridView');

interface GridViewProps {
  websiteId?: string;
  className?: string;
}

/**
 * Grid view component for displaying website pages in a grid layout
 */
export function GridView({ websiteId, className }: GridViewProps) {
  // 筛选状态
  const [filter, setFilter] = useState('');
  const [sortBy, setSortBy] = useState<'title' | 'updatedAt' | 'status'>('updatedAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // 获取增强的网站页面数据
  const {
    nodes: initialNodes,
    isLoading,
    availableLanguages,
    selectedLanguages,
    toggleLanguage,
    selectAllLanguages,
    clearLanguageSelection
  } = useEnhancedWebsitePages(websiteId, {
    layoutType: 'grid',
    initialSelectedLanguages: [],
  });

  // 处理筛选变化
  const handleFilterChange = useCallback((value: string) => {
    logger.info('Changing filter', { filter: value });
    setFilter(value);
  }, []);

  // 处理排序变化
  const handleSortChange = useCallback((value: 'title' | 'updatedAt' | 'status') => {
    logger.info('Changing sort field', { sortBy: value });
    setSortBy(value);
  }, []);

  // 处理排序顺序变化
  const handleSortOrderChange = useCallback(() => {
    logger.info('Toggling sort order', { current: sortOrder });
    setSortOrder(prev => prev === 'asc' ? 'desc' : 'asc');
  }, [sortOrder]);

  // 筛选和排序节点
  const filteredAndSortedNodes = React.useMemo(() => {
    if (!initialNodes) return [];

    // 过滤掉语言节点，只保留页面节点
    const pageNodes = initialNodes.filter(node => !node.data.isLanguageNode);

    // 应用文本筛选
    let filtered = pageNodes;
    if (filter) {
      const lowerFilter = filter.toLowerCase();
      filtered = pageNodes.filter(node =>
        node.data.title?.toLowerCase().includes(lowerFilter) ||
        node.data.slug?.toLowerCase().includes(lowerFilter)
      );
    }

    // 应用排序
    return [...filtered].sort((a, b) => {
      let valueA: string | number;
      let valueB: string | number;

      switch (sortBy) {
        case 'title':
          valueA = a.data.title || '';
          valueB = b.data.title || '';
          break;
        case 'status':
          valueA = a.data.status || '';
          valueB = b.data.status || '';
          break;
        case 'updatedAt':
        default:
          valueA = a.data.updatedAt ? new Date(a.data.updatedAt).getTime() : 0;
          valueB = b.data.updatedAt ? new Date(b.data.updatedAt).getTime() : 0;
          break;
      }

      // 根据排序顺序决定比较方式
      if (typeof valueA === 'string' && typeof valueB === 'string') {
        return sortOrder === 'asc'
          ? valueA.localeCompare(valueB)
          : valueB.localeCompare(valueA);
      } else {
        // 数字比较
        return sortOrder === 'asc'
          ? (valueA as number) - (valueB as number)
          : (valueB as number) - (valueA as number);
      }
    });
  }, [initialNodes, filter, sortBy, sortOrder]);

  // 按语言分组
  const nodesByLanguage = React.useMemo(() => {
    if (!filteredAndSortedNodes.length) return {};

    const result: Record<string, typeof filteredAndSortedNodes> = {};

    filteredAndSortedNodes.forEach(node => {
      const language = node.data.language || 'unknown';
      if (!result[language]) {
        result[language] = [];
      }
      result[language].push(node);
    });

    return result;
  }, [filteredAndSortedNodes]);

  return (
    <div className={cn("relative w-full h-full overflow-auto", className)}>
      {isLoading ? (
        <div className="absolute inset-0 flex items-center justify-center bg-background/80 z-10">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2 text-muted-foreground">Loading pages...</span>
        </div>
      ) : (
        <>
          <GridViewControls
            filter={filter}
            onFilterChange={handleFilterChange}
            sortBy={sortBy}
            onSortChange={handleSortChange}
            sortOrder={sortOrder}
            onSortOrderChange={handleSortOrderChange}
            availableLanguages={availableLanguages}
            selectedLanguages={selectedLanguages}
            onToggleLanguage={toggleLanguage}
            onSelectAllLanguages={selectAllLanguages}
            onClearLanguageSelection={clearLanguageSelection}
            totalPages={filteredAndSortedNodes.length}
          />

          <div className="p-6">
            {Object.entries(nodesByLanguage).map(([language, nodes]) => (
              <div key={language} className="mb-8">
                <h2 className="text-lg font-medium mb-4 flex items-center">
                  <span className="w-2 h-2 rounded-full bg-primary mr-2"></span>
                  {language === 'unknown' ? 'No Language' : language}
                  <span className="ml-2 text-sm text-muted-foreground">
                    ({nodes.length} {nodes.length === 1 ? 'page' : 'pages'})
                  </span>
                </h2>

                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-4 gap-3">
                  {nodes.map(node => (
                    <PageCard
                      key={node.id}
                      node={node}
                      websiteId={websiteId}
                    />
                  ))}
                </div>
              </div>
            ))}

            {filteredAndSortedNodes.length === 0 && (
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <div className="rounded-full bg-muted w-12 h-12 flex items-center justify-center mb-4">
                  <Loader2 className="h-6 w-6 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-medium">No pages found</h3>
                <p className="text-sm text-muted-foreground mt-1">
                  {filter ? 'Try adjusting your filters' : 'Create your first page to get started'}
                </p>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
}
