import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from '@xyflow/react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  FileText,
  Globe,
  // ExternalLink,
  MoreHorizontal,
  // Edit,
  Copy,
  Trash2,
  Eye,
  Edit2,
  // Image,
  Loader2
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  // DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
// import { Badge } from '@/components/ui/badge';
import { useRouter } from 'next/navigation';

// Define node data type
interface PageNodeData {
  id: string;
  title: string;
  slug?: string;
  pageType?: string;
  status?: string;
  language?: string;
  previewUrl?: string;
  isLanguageNode?: boolean;
  websiteId?: string;
  pageId?: string;
}

/**
 * Custom node component for page nodes in the site map
 */
export function PageNode({ data }: NodeProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [hasBeenVisible, setHasBeenVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const nodeData = data as unknown as PageNodeData;
  const router = useRouter();

  // Set node dimensions to fit mobile preview
  useEffect(() => {
    const nodeElement = document.getElementById(`node-${nodeData.id}`);
    if (nodeElement) {
      // Set fixed dimensions for mobile preview
      nodeElement.style.width = '320px';
      nodeElement.style.height = '640px';
    }
  }, [nodeData.id]);

  // Use Intersection Observer to only load iframe when visible
  useEffect(() => {
    const nodeElement = document.getElementById(`node-${nodeData.id}`);
    if (!nodeElement) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            setIsVisible(true);
            // 一旦节点变为可见，设置 hasBeenVisible 为 true，此后不再改变
            setHasBeenVisible(true);
          } else {
            // 只改变可见状态，不影响 hasBeenVisible
            setIsVisible(false);
          }
        });
      },
      { threshold: 0.1 }
    );

    observer.observe(nodeElement);
    return () => observer.disconnect();
  }, [nodeData.id]);

  // Handle iframe load event
  const handleIframeLoad = () => {
    setIsLoading(false);
  };

  // Page status badge color
  const getStatusColor = (status: string | undefined) => {
    switch (status) {
      case 'PUBLISHED':
        return 'bg-green-500';
      case 'MODIFIED':
        return 'bg-amber-500';
      case 'DRAFT':
        return 'bg-blue-500';
      default:
        return 'bg-slate-400';
    }
  };

  return (
    <>
      <div
        id={`node-${nodeData.id}`}
        className="relative border rounded-md shadow-sm bg-card overflow-hidden"
        style={{ width: '320px', height: '640px' }}
      >
        {/* Page header */}
        <div className="flex items-center justify-between border-b p-3 bg-muted/30">
          <div className="flex items-center space-x-2">
            <span className={cn("w-2 h-2 rounded-full", getStatusColor(nodeData.status))} />
            <span className="text-xs font-medium text-muted-foreground">
              {nodeData.status}
            </span>
          </div>

          <div className="flex items-center">
            {/* 编辑按钮 */}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6"
                    onClick={(e) => {
                      e.stopPropagation();
                      if (nodeData.websiteId && nodeData.id) {
                        router.push(`/site/${nodeData.websiteId}/page/${nodeData.id}`);
                      }
                    }}
                  >
                    <Edit2 className="h-3.5 w-3.5" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Edit Page</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            {/* Actions dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="h-6 w-6 ml-2">
                  <MoreHorizontal className="h-3.5 w-3.5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>
                  <Eye className="h-3.5 w-3.5 mr-2" />
                  <span>View</span>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Copy className="h-3.5 w-3.5 mr-2" />
                  <span>Duplicate</span>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem className="text-destructive">
                  <Trash2 className="h-3.5 w-3.5 mr-2" />
                  <span>Delete</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Preview content */}
        <div className="w-full h-[calc(100%-40px)]">
          {/* Show page title when iframe is not visible or loading */}
          {(!isVisible || isLoading) && (
            <div className="flex flex-col items-center justify-center h-full">
              {isLoading && hasBeenVisible ? (
                <>
                  <Loader2 className="h-8 w-8 animate-spin text-primary mb-2" />
                  <p className="text-sm text-muted-foreground">Loading preview...</p>
                </>
              ) : (
                <>
                  <h3 className="font-medium text-sm mb-1 text-center" title={nodeData.title}>
                    {nodeData.title}
                  </h3>
                  <p className="text-xs text-muted-foreground text-center" title={nodeData.slug}>
                    {nodeData.slug ? `/${nodeData.slug}` : '/home'}
                  </p>
                </>
              )}
            </div>
          )}

          {/* Only render iframe when node has been visible at least once */}
          {hasBeenVisible && (
            <iframe
              src={`/preview/${nodeData.id}`}
              className={cn("w-full h-full border-0",
                // 当节点不可见或正在加载时隐藏 iframe
                (!isVisible || isLoading) && "opacity-0")}
              title={`Preview of ${nodeData.title}`}
              onLoad={handleIframeLoad}
            />
          )}
        </div>
      </div>

      {/* Connection handles - 调整连线点位置以适应新的节点尺寸 */}
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3"
        style={{ left: '50%', top: '-2px' }}
      />
      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3"
        style={{ left: '50%', bottom: '-2px' }}
      />
    </>
  );
}

/**
 * Custom node component for language group nodes
 */
export function LanguageGroupNode({ data }: NodeProps) {
  const nodeData = data as unknown as PageNodeData;

  // Keep original dimensions for language nodes
  useEffect(() => {
    const nodeElement = document.getElementById(`language-node-${nodeData.id}`);
    if (nodeElement) {
      const { width, height } = nodeElement.getBoundingClientRect();
      nodeElement.style.width = `${Math.max(width, 180)}px`;
      nodeElement.style.height = `${Math.max(height, 80)}px`;
    }
  }, [nodeData.id]);

  return (
    <div
      id={`language-node-${nodeData.id}`}
      className={cn(
        "relative border border-dashed rounded-md p-2",
        "bg-indigo-50/50 border-indigo-200",
        "min-w-[180px] min-h-[80px] transition-all duration-200",
        "language-node-content"
      )}
    >
      <div className="flex items-center gap-1.5 mb-1">
        <Globe className="h-3.5 w-3.5 text-indigo-500" />
        <h3 className="font-medium text-sm text-indigo-700">
          {nodeData.title}
        </h3>
      </div>

      <p className="text-xs text-indigo-600/70">
        {nodeData.language}
      </p>

      {/* Connection handles - 调整连线点位置以适应新的节点尺寸 */}
      <Handle
        type="target"
        position={Position.Top}
        className="w-2.5 h-2.5 bg-indigo-500"
        style={{ left: '50%', top: '-2px' }}
      />
      <Handle
        type="source"
        position={Position.Bottom}
        className="w-2.5 h-2.5 bg-indigo-500"
        style={{ left: '50%', bottom: '-2px' }}
      />
    </div>
  );
}
