import React from 'react';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import {
  // MoreHorizontal,
  // ExternalLink,
  Edit2,
  // Copy,
  // Trash2,
  // Eye,
  Clock,
  FileText,
  // Globe
} from 'lucide-react';
// import {
//   DropdownMenu,
//   DropdownMenuContent,
//   DropdownMenuItem,
//   DropdownMenuLabel,
//   DropdownMenuSeparator,
//   DropdownMenuTrigger
// } from '@/components/ui/dropdown-menu';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { formatDistanceToNow, format } from 'date-fns';
import { Logger } from '@/lib/utils/logger';
import { useRouter } from 'next/navigation';

// 创建日志记录器
const logger = new Logger('PageCard');

interface PageCardProps {
  node: any;
  websiteId?: string;
}

/**
 * Card component for displaying a page in grid view
 */
export function PageCard({ node, websiteId }: PageCardProps) {
  // const [isHovered, setIsHovered] = useState(false);
  const router = useRouter();

  // Get page data
  const {
    title,
    slug,
    status,
    // language,
    updatedAt,
    // previewUrl,
    pageId
  } = node.data;

  // Format dates
  const updatedAtFormatted = updatedAt
    ? formatDistanceToNow(new Date(updatedAt), { addSuffix: true })
    : 'Unknown';

  const updatedAtFull = updatedAt
    ? format(new Date(updatedAt), 'PPP p')
    : 'Unknown date';

  // Get status color
  const getStatusColor = (status: string | undefined) => {
    switch (status) {
      case 'PUBLISHED':
        return 'bg-green-500';
      case 'MODIFIED':
        return 'bg-amber-500';
      case 'DRAFT':
        return 'bg-blue-500';
      default:
        return 'bg-slate-400';
    }
  };

  // Get status text
  const getStatusText = (status: string | undefined) => {
    switch (status) {
      case 'PUBLISHED':
        return 'Published';
      case 'MODIFIED':
        return 'Modified';
      case 'DRAFT':
        return 'Draft';
      default:
        return 'Unknown';
    }
  };

  // Handle edit click
  const handleEdit = () => {
    if (pageId && websiteId) {
      logger.info('Navigating to edit page', { pageId, websiteId });
      router.push(`/site/${websiteId}/page/${pageId}`);
    }
  };

  // Handle preview click
  // const handlePreview = () => {
  //   if (previewUrl) {
  //     logger.info('Opening preview', { previewUrl });
  //     // 预览链接通常是外部链接，仍然使用 window.open 打开新标签页
  //     window.open(previewUrl, '_blank');
  //   }
  // };

  return (
    <div
      className={cn(
        "p-4 rounded-lg cursor-pointer transition-all duration-200 mb-3 border",
        "hover:border-primary/30 hover:shadow-sm",
        "bg-card text-card-foreground",
        // 暗色模式下使用稍浅的背景色以增强层次感
        "dark:bg-slate-800/50"
      )}
      // onMouseEnter={() => setIsHovered(true)}
      // onMouseLeave={() => setIsHovered(false)}
    >
      {/* Header with title and actions */}
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2 overflow-hidden">
          <h3 className="font-medium text-base truncate" title={title}>{title || 'Untitled Page'}</h3>
        </div>

        <div className="flex items-center space-x-2">
          {/* Status badge */}
          <Badge
            variant="outline"
            className={cn("px-2 py-0.5 text-xs font-medium",
              status === 'PUBLISHED' ? "text-green-700 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-950/50 dark:border-green-800" :
              status === 'MODIFIED' ? "text-amber-700 bg-amber-50 border-amber-200 dark:text-amber-400 dark:bg-amber-950/50 dark:border-amber-800" :
              status === 'DRAFT' ? "text-blue-700 bg-blue-50 border-blue-200 dark:text-blue-400 dark:bg-blue-950/50 dark:border-blue-800" :
              "text-slate-700 bg-slate-50 border-slate-200 dark:text-slate-400 dark:bg-slate-800/50 dark:border-slate-700"
            )}
          >
            <span className={cn("w-1.5 h-1.5 rounded-full mr-1", getStatusColor(status))}></span>
            {getStatusText(status)}
          </Badge>

          {/* Actions dropdown */}
          {/* <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8 rounded-full opacity-70 hover:opacity-100">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>Page Actions</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handlePreview}>
                <Eye className="h-4 w-4 mr-2" />
                <span>View</span>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleEdit}>
                <Edit2 className="h-4 w-4 mr-2" />
                <span>Edit</span>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Copy className="h-4 w-4 mr-2" />
                <span>Duplicate</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-destructive">
                <Trash2 className="h-4 w-4 mr-2" />
                <span>Delete</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu> */}
        </div>
      </div>

      {/* Path/Slug */}
      <div className="flex items-center justify-between text-sm mb-3">
        <div className="flex items-center">
          <FileText className="h-4 w-4 mr-1.5 text-muted-foreground" />
          <span className="truncate text-muted-foreground">{slug || ''}</span>

          {/* {previewUrl && (
            <Button
              variant="ghost"
              size="icon"
              className={cn(
                "h-6 w-6 ml-1 rounded-full transition-opacity",
                isHovered ? "opacity-100" : "opacity-0"
              )}
              onClick={handlePreview}
            >
              <ExternalLink className="h-3 w-3" />
            </Button>
          )} */}
        </div>
      </div>

      {/* Footer with dates and language */}
      <div className="flex items-center justify-between text-xs text-muted-foreground mt-3 pt-2 border-t">
        <div className="flex flex-col gap-1">
          {updatedAt && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center">
                    <Clock className="h-3 w-3 mr-1" />
                    <span>Updated {updatedAtFormatted}</span>
                  </div>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>{updatedAtFull}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>
{/* 
        {language && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center gap-1 bg-primary/10 text-primary rounded-full px-2 py-0.5 dark:bg-primary/20 dark:text-primary-foreground">
                  <Globe className="h-3 w-3" />
                  <span className="font-medium">{language.toUpperCase()}</span>
                </div>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                <p>Language: {language}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )} */}

        {/* Quick action buttons */}
        <div className="ml-auto flex gap-1">
          <Button
            variant="ghost"
            size="sm"
            className={cn(
              "h-7 px-2 transition-opacity duration-200",
              // isHovered ? "opacity-100" : "opacity-0"
            )}
            onClick={handleEdit}
          >
            <Edit2 className="h-3.5 w-3.5 mr-1" />
            Edit
          </Button>
        </div>
      </div>
    </div>
  );
}
