import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  ReactFlow,
  Background,
  Controls,
  MiniMap,
  NodeTypes,
  EdgeTypes,
  Node,
  Edge,
  ConnectionLineType,
  MarkerType,
  useNodesState,
  useEdgesState,
  Panel,
  Viewport,
  OnMove,
  BackgroundVariant
} from '@xyflow/react';
import '@xyflow/react/dist/style.css'; // 导入 React Flow 样式
import { useEnhancedWebsitePages } from '@/lib/hooks/use-enhanced-website-pages';
import { PageNode as PageNodeComponent, LanguageGroupNode } from './page-node';
import { SiteMapControls } from './site-map-controls';
import { DagreLayoutOptions } from '@/lib/hooks/use-dagre-layout';
import { Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Logger } from '@/lib/utils/logger';
import { useRouter } from 'next/navigation';

// 自定义样式
import './site-map.css';

// 创建日志记录器
const logger = new Logger('SiteMapView');

// 注册自定义节点类型
const nodeTypes: NodeTypes = {
  pageNode: PageNodeComponent,
  languageGroup: LanguageGroupNode,
};

// 默认边样式
const defaultEdgeOptions = {
  type: 'smoothstep',  // 使用平滑阶梯式连线，视觉效果更好
  animated: false,     // 禁用动画
  style: {
    strokeWidth: 1.5,
    stroke: '#94a3b8',  // 使用柔和的颜色
    strokeDasharray: '5,5', // 使用虚线
  },
  markerEnd: {
    type: MarkerType.ArrowClosed,
    width: 10,         // 减小箭头大小
    height: 10,
    color: '#94a3b8',
  },
};

interface SiteMapViewProps {
  websiteId?: string;
  className?: string;
}

/**
 * Site map visualization component
 */
export function SiteMapView({ websiteId, className }: SiteMapViewProps) {
  const router = useRouter();
  // 默认布局选项
  const defaultLayoutOptions: DagreLayoutOptions = {
    direction: 'TB',
    nodeWidth: 320,       // 页面节点宽度，接近真实手机尺寸
    nodeHeight: 640,      // 页面节点高度
    rankSeparation: 200,  // 层级间距
    nodeSeparation: 20,   // 同级节点间距
    edgeSeparation: 40,   // 边间距
    marginX: 10,          // 水平边距
    marginY: 10,          // 垂直边距
    ranker: 'network-simplex', // 使用网络单纯形算法
  };

  // 获取增强的网站页面数据
  const {
    nodes: initialNodes,
    edges: initialEdges,
    isLoading,
    availableLanguages,
    selectedLanguages,
    toggleLanguage,
    selectAllLanguages,
    clearLanguageSelection,
    updateLayoutOptions,
    calculateLayout,
    currentLayoutOptions
  } = useEnhancedWebsitePages(websiteId, {
    dagreOptions: defaultLayoutOptions,
    initialSelectedLanguages: [],
  });

  // 使用 React Flow 的状态管理
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);

  // 保存视图状态
  const [viewportState, setViewportState] = useState({
    x: 50,
    y: 50,
    zoom: 1
  });

  // 当初始节点和边更新时，更新 React Flow 状态
  useEffect(() => {
    if (initialNodes && initialEdges) {
      logger.info('Updating nodes and edges', {
        nodeCount: initialNodes.length,
        edgeCount: initialEdges.length
      });

      // 确保节点有正确的类型
      const nodesWithTypes = initialNodes.map((node: any) => ({
        ...node,
        type: node.data.isLanguageNode ? 'languageGroup' : 'pageNode',
      }));

      setNodes(nodesWithTypes as any);
      setEdges(initialEdges as any);
    }
  }, [initialNodes, initialEdges, setNodes, setEdges]);

  // 处理视图变化
  const onViewportChange = useCallback<OnMove>((_, viewport) => {
    if (viewport) {
      setViewportState(viewport);
    }
  }, []);

  // 更新布局选项
  const handleUpdateLayoutOptions = useCallback((options: Partial<DagreLayoutOptions>) => {
    logger.info('Updating layout options', { options });

    // 调用 hook 的更新方法
    updateLayoutOptions(options);

    // 手动触发重新计算布局
    setTimeout(() => {
      calculateLayout();
    }, 100);
  }, [updateLayoutOptions, calculateLayout]);

  // 处理节点点击
  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    // 在这里处理节点点击事件，例如导航到页面编辑界面
    logger.info('Node clicked', { nodeId: node.id, nodeData: node.data });

    // 如果是页面节点，可以导航到页面编辑界面
    if (!node.data.isLanguageNode && node.data.pageId && node.data.websiteId) {
      router.push(`/site/${node.data.websiteId}/page/${node.data.pageId}`);
    }
  }, [router]);

  // 处理节点双击
  const onNodeDoubleClick = useCallback((event: React.MouseEvent, node: Node) => {
    logger.info('Node double-clicked', { nodeId: node.id, nodeData: node.data });

    // 如果是页面节点，可以导航到页面预览
    if (!node.data.isLanguageNode && node.data.previewUrl) {
      // 确保 previewUrl 是有效的字符串
      const previewUrl = node.data.previewUrl as string;
      if (previewUrl && typeof previewUrl === 'string') {
        // 预览链接通常是外部链接，仍然使用 window.open 打开新标签页
        window.open(previewUrl, '_blank');
      }
    }
  }, []);

  return (
    <div className={cn("relative w-full h-full", className)}>
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-background/80 z-10">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2 text-muted-foreground">Loading site map...</span>
        </div>
      )}

      <ReactFlow
        nodes={nodes as any}
        edges={edges as any}
        onNodesChange={onNodesChange as any}
        onEdgesChange={onEdgesChange as any}
        onNodeClick={onNodeClick}
        onNodeDoubleClick={onNodeDoubleClick}
        nodeTypes={nodeTypes}
        defaultEdgeOptions={defaultEdgeOptions}
        connectionLineType={ConnectionLineType.SmoothStep}
        fitView={nodes.length > 0 && edges.length > 0 ? false : true}
        fitViewOptions={{
          padding: 1.0,  // 增加填充，确保所有节点可见
          includeHiddenNodes: true,
          duration: 800,
          maxZoom: 0.8   // 降低最大缩放比例，确保整体视图
        }}
        attributionPosition="bottom-right"
        minZoom={0.05}
        maxZoom={1.5}
        proOptions={{ hideAttribution: true }}
        // Use saved viewport state
        defaultViewport={viewportState}
        onMove={onViewportChange}
        nodesDraggable={false}  // 禁止节点拖动，保持树形结构
        nodesConnectable={false} // 禁止节点连接
        elementsSelectable={true} // 允许选择元素
      >
        {/* Background */}
        <Background
          color="#e2e8f0"
          gap={20}
          size={1.5}
          variant={BackgroundVariant.Dots}
        />

        {/* Control panel */}
        <SiteMapControls
          availableLanguages={availableLanguages}
          selectedLanguages={selectedLanguages}
          onToggleLanguage={toggleLanguage}
          onSelectAllLanguages={selectAllLanguages}
          onClearLanguageSelection={clearLanguageSelection}
          onUpdateLayoutOptions={handleUpdateLayoutOptions}
          onRecalculateLayout={calculateLayout}
          currentLayoutOptions={currentLayoutOptions || defaultLayoutOptions}
        />

        {/* Mini map */}
        <MiniMap
          nodeStrokeWidth={3}
          zoomable
          pannable
          maskColor="rgba(240, 240, 240, 0.5)"
          position="bottom-right"
          style={{
            backgroundColor: 'rgba(255, 255, 255, 0.8)',
            border: '1px solid #e2e8f0',
            borderRadius: '8px',
          }}
          nodeColor={(node) => {
            if (node.data?.isLanguageNode) return '#6366F1';

            switch (node.data?.status) {
              case 'PUBLISHED':
                return '#10B981';
              case 'MODIFIED':
                return '#F59E0B';
              case 'DRAFT':
                return '#3B82F6';
              default:
                return '#94A3B8';
            }
          }}
        />

        {/* Add controls */}
        <Controls position="bottom-left" />
      </ReactFlow>
    </div>
  );
}
