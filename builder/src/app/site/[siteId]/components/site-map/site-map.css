/* 自定义 ReactFlow 节点样式 */

/* 移除语言节点的外框 */
.react-flow__node-languageGroup {
  background-color: transparent !important;
  border: none !important;
  padding: 0 !important;
  border-radius: 0 !important;
  box-shadow: none !important;
}

/* 确保节点内容正确显示 */
.react-flow__node {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 确保连接点可见 */
.react-flow__handle {
  z-index: 10;
}

/* 改进选中状态样式 */
.react-flow__node.selected {
  box-shadow: none !important;
}

/* 语言节点选中状态 */
.react-flow__node-languageGroup.selected .language-node-content {
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.5);
}
