import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  ArrowDownAZ, 
  ArrowUpAZ, 
  Calendar, 
  Filter, 
  Search, 
  SortAsc,
  Tag
} from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { languageNames } from '@/lib/hooks/use-enhanced-website-pages';
import { Logger } from '@/lib/utils/logger';
import { cn } from '@/lib/utils';

// 创建日志记录器
const logger = new Logger('GridViewControls');

interface GridViewControlsProps {
  filter: string;
  onFilterChange: (value: string) => void;
  sortBy: 'title' | 'updatedAt' | 'status';
  onSortChange: (value: 'title' | 'updatedAt' | 'status') => void;
  sortOrder: 'asc' | 'desc';
  onSortOrderChange: () => void;
  availableLanguages: string[];
  selectedLanguages: string[];
  onToggleLanguage: (language: string) => void;
  onSelectAllLanguages: () => void;
  onClearLanguageSelection: () => void;
  totalPages: number;
}

/**
 * Controls component for grid view
 */
export function GridViewControls({
  filter,
  onFilterChange,
  sortBy,
  onSortChange,
  sortOrder,
  onSortOrderChange,
  availableLanguages,
  selectedLanguages,
  onToggleLanguage,
  onSelectAllLanguages,
  onClearLanguageSelection,
  totalPages
}: GridViewControlsProps) {
  // Get sort icon based on current sort field
  const getSortIcon = () => {
    switch (sortBy) {
      case 'title':
        return <ArrowDownAZ className="h-4 w-4 flex-shrink-0" />;
      case 'status':
        return <Tag className="h-4 w-4 flex-shrink-0" />;
      case 'updatedAt':
      default:
        return <Calendar className="h-4 w-4 flex-shrink-0" />;
    }
  };
  
  return (
    <div className="sticky top-0 z-10 bg-background border-b p-4">
      <div className="flex flex-wrap items-center gap-3">
        {/* Search input */}
        <div className="relative w-full md:w-auto md:flex-1 md:max-w-md">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 flex-shrink-0 text-muted-foreground" />
          <Input
            type="text"
            placeholder="Search pages..."
            className="pl-9"
            value={filter}
            onChange={(e) => onFilterChange(e.target.value)}
          />
        </div>
        
        <div className="flex items-center gap-2 flex-wrap">
          {/* Sort controls */}
          <div className="flex items-center gap-2">
            <Select
              value={sortBy}
              onValueChange={(value) => onSortChange(value as 'title' | 'updatedAt' | 'status')}
            >
              <SelectTrigger className="w-[130px] h-9">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="title">
                  <div className="flex items-center">
                    <ArrowDownAZ className="h-4 w-4 mr-2 flex-shrink-0" />
                    <span>Title</span>
                  </div>
                </SelectItem>
                <SelectItem value="updatedAt">
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-2 flex-shrink-0" />
                    <span>Updated At</span>
                  </div>
                </SelectItem>
                <SelectItem value="status">
                  <div className="flex items-center">
                    <Tag className="h-4 w-4 mr-2 flex-shrink-0" />
                    <span>Status</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
            
            <Button
              variant="outline"
              size="icon"
              className="h-9 w-9"
              onClick={onSortOrderChange}
              title={sortOrder === 'asc' ? 'Sort descending' : 'Sort ascending'}
            >
              <SortAsc className={cn("h-4 w-4 flex-shrink-0", sortOrder === 'desc' && "rotate-180")} />
            </Button>
          </div>
          
          {/* Language filter */}
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" size="sm" className="h-9 gap-1">
                <Filter className="h-4 w-4 flex-shrink-0" />
                <span className="hidden sm:inline">Languages</span>
                <span className="bg-primary text-primary-foreground rounded-full px-1.5 py-0.5 text-xs">
                  {selectedLanguages.length}
                </span>
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-56">
              <div className="space-y-4">
                <h4 className="font-medium">Filter Languages</h4>
                
                <div className="space-y-2">
                  {availableLanguages.map(lang => (
                    <div key={lang} className="flex items-center space-x-2">
                      <Checkbox 
                        id={`grid-lang-${lang}`} 
                        checked={selectedLanguages.includes(lang)}
                        onCheckedChange={() => {
                          logger.info('Toggling language filter', { language: lang });
                          onToggleLanguage(lang);
                        }}
                      />
                      <Label htmlFor={`grid-lang-${lang}`} className="cursor-pointer">
                        {languageNames[lang] || lang}
                      </Label>
                    </div>
                  ))}
                </div>
                
                <div className="flex items-center justify-between">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => {
                      logger.info('Selecting all languages');
                      onSelectAllLanguages();
                    }}
                  >
                    Select All
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => {
                      logger.info('Clearing language selection');
                      onClearLanguageSelection();
                    }}
                  >
                    Clear
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>
          
          {/* Page count */}
          <div className="ml-auto text-sm text-muted-foreground">
            {totalPages} {totalPages === 1 ? 'page' : 'pages'}
          </div>
        </div>
      </div>
    </div>
  );
}
