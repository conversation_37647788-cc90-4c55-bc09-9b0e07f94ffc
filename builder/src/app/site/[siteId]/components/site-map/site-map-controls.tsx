import React from 'react';
import { Panel, useReactFlow, ControlButton } from '@xyflow/react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Slider } from '@/components/ui/slider';
import {
  ZoomIn,
  ZoomOut,
  Maximize,
  LayoutGrid,
  ArrowDown,
  ArrowUp,
  ArrowLeft,
  ArrowRight,
  RefreshCw,
  Filter
} from 'lucide-react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { DagreLayoutOptions } from '@/lib/hooks/use-dagre-layout';
import { languageNames } from '@/lib/hooks/use-enhanced-website-pages';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { logger } from '@/lib/utils/logger';

// 配置日志命名空间
logger.configure({ namespace: 'SiteMapControls' });

interface SiteMapControlsProps {
  availableLanguages: string[];
  selectedLanguages: string[];
  onToggleLanguage: (language: string) => void;
  onSelectAllLanguages: () => void;
  onClearLanguageSelection: () => void;
  onUpdateLayoutOptions: (options: Partial<DagreLayoutOptions>) => void;
  onRecalculateLayout: () => void;
  currentLayoutOptions: DagreLayoutOptions;
}

/**
 * Control panel for site map visualization
 */
export function SiteMapControls({
  availableLanguages,
  selectedLanguages,
  onToggleLanguage,
  onSelectAllLanguages,
  onClearLanguageSelection,
  onUpdateLayoutOptions,
  onRecalculateLayout,
  currentLayoutOptions
}: SiteMapControlsProps) {
  const { zoomIn, zoomOut, fitView } = useReactFlow();

  // Handle zoom in
  const handleZoomIn = () => {
    zoomIn({ duration: 300 });
  };

  // Handle zoom out
  const handleZoomOut = () => {
    zoomOut({ duration: 300 });
  };

  // Handle fit view
  const handleFitView = () => {
    fitView({ duration: 500, padding: 0.2 });
  };

  // Handle ranker algorithm change
  const handleRankerChange = (value: string) => {
    logger.info('Changing ranker algorithm', { ranker: value });
    onUpdateLayoutOptions({ ranker: value as DagreLayoutOptions['ranker'] });
  };

  // Handle direction change
  const handleDirectionChange = (value: string) => {
    logger.info('Changing layout direction', { direction: value });
    onUpdateLayoutOptions({ direction: value as DagreLayoutOptions['direction'] });
  };

  // Handle node separation change
  const handleNodeSeparationChange = (value: number[]) => {
    logger.info('Changing node separation', { nodeSeparation: value[0] });
    onUpdateLayoutOptions({ nodeSeparation: value[0] });
  };

  // Handle rank separation change
  const handleRankSeparationChange = (value: number[]) => {
    logger.info('Changing rank separation', { rankSeparation: value[0] });
    onUpdateLayoutOptions({ rankSeparation: value[0] });
  };

  // Handle edge separation change
  const handleEdgeSeparationChange = (value: number[]) => {
    logger.info('Changing edge separation', { edgeSeparation: value[0] });
    onUpdateLayoutOptions({ edgeSeparation: value[0] });
  };

  // Handle margin change
  const handleMarginChange = (value: number[]) => {
    logger.info('Changing margins', { margin: value[0] });
    onUpdateLayoutOptions({ marginX: value[0], marginY: value[0] });
  };

  // Get direction icon
  const getDirectionIcon = () => {
    switch (currentLayoutOptions.direction) {
      case 'TB':
        return <ArrowDown className="h-4 w-4" />;
      case 'BT':
        return <ArrowUp className="h-4 w-4" />;
      case 'LR':
        return <ArrowRight className="h-4 w-4" />;
      case 'RL':
        return <ArrowLeft className="h-4 w-4" />;
      default:
        return <ArrowDown className="h-4 w-4" />;
    }
  };

  return (
    <>
      {/* Zoom controls */}
      <Panel position="top-right" className="flex flex-col gap-2">
        <div className="bg-background border rounded-md shadow-sm flex flex-col p-1">
          <Button variant="ghost" size="icon" onClick={handleZoomIn} title="Zoom in">
            <ZoomIn className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" onClick={handleZoomOut} title="Zoom out">
            <ZoomOut className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" onClick={handleFitView} title="Fit view">
            <Maximize className="h-4 w-4" />
          </Button>
        </div>
      </Panel>

      {/* Layout controls */}
      <Panel position="top-center" className="flex items-center gap-2 mt-2">
        <div className="bg-background border rounded-md shadow-sm p-2 flex items-center gap-2">
          {/* Layout algorithm */}
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" size="sm" className="h-8 gap-1">
                <LayoutGrid className="h-4 w-4" />
                <span className="hidden sm:inline">Layout</span>
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-72">
              <div className="space-y-4">
                <h4 className="font-medium">Layout Options</h4>
                <div className="space-y-2">
                  <Label>Ranker Algorithm</Label>
                  <Select
                    value={currentLayoutOptions.ranker}
                    onValueChange={handleRankerChange}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select ranker algorithm" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="network-simplex">Network Simplex</SelectItem>
                      <SelectItem value="tight-tree">Tight Tree</SelectItem>
                      <SelectItem value="longest-path">Longest Path</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Direction</Label>
                  <Select
                    value={currentLayoutOptions.direction}
                    onValueChange={handleDirectionChange}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select direction" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="TB">Top to Bottom</SelectItem>
                      <SelectItem value="BT">Bottom to Top</SelectItem>
                      <SelectItem value="LR">Left to Right</SelectItem>
                      <SelectItem value="RL">Right to Left</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <Label>Margin</Label>
                    <span className="text-xs text-muted-foreground">
                      {currentLayoutOptions.marginX}px
                    </span>
                  </div>
                  <Slider
                    value={[currentLayoutOptions.marginX || 40]}
                    min={10}
                    max={100}
                    step={5}
                    onValueChange={handleMarginChange}
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <Label>Node Separation</Label>
                    <span className="text-xs text-muted-foreground">
                      {currentLayoutOptions.nodeSeparation}px
                    </span>
                  </div>
                  <Slider
                    value={[currentLayoutOptions.nodeSeparation || 80]}
                    min={20}
                    max={200}
                    step={10}
                    onValueChange={handleNodeSeparationChange}
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <Label>Rank Separation</Label>
                    <span className="text-xs text-muted-foreground">
                      {currentLayoutOptions.rankSeparation}px
                    </span>
                  </div>
                  <Slider
                    value={[currentLayoutOptions.rankSeparation || 200]}
                    min={50}
                    max={300}
                    step={10}
                    onValueChange={handleRankSeparationChange}
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <Label>Edge Separation</Label>
                    <span className="text-xs text-muted-foreground">
                      {currentLayoutOptions.edgeSeparation}px
                    </span>
                  </div>
                  <Slider
                    value={[currentLayoutOptions.edgeSeparation || 40]}
                    min={10}
                    max={100}
                    step={5}
                    onValueChange={handleEdgeSeparationChange}
                  />
                </div>

                <Button
                  size="sm"
                  className="w-full"
                  onClick={onRecalculateLayout}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Apply Layout
                </Button>
              </div>
            </PopoverContent>
          </Popover>

          {/* Direction indicator */}
          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8"
            onClick={() => {
              // Cycle through directions: TB -> LR -> BT -> RL -> TB
              const directions: DagreLayoutOptions['direction'][] = ['TB', 'LR', 'BT', 'RL'];
              const currentIndex = directions.indexOf(currentLayoutOptions.direction || 'TB');
              const nextIndex = (currentIndex + 1) % directions.length;
              onUpdateLayoutOptions({ direction: directions[nextIndex] });
            }}
          >
            {getDirectionIcon()}
          </Button>

          <Separator orientation="vertical" className="h-8" />

          {/* Language filter */}
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" size="sm" className="h-8 gap-1">
                <Filter className="h-4 w-4" />
                <span className="hidden sm:inline">Languages</span>
                <span className="bg-primary text-primary-foreground rounded-full px-1.5 py-0.5 text-xs">
                  {selectedLanguages.length}
                </span>
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-56">
              <div className="space-y-4">
                <h4 className="font-medium">Filter Languages</h4>

                <div className="space-y-2">
                  {availableLanguages.map(lang => (
                    <div key={lang} className="flex items-center space-x-2">
                      <Checkbox
                        id={`lang-${lang}`}
                        checked={selectedLanguages.includes(lang)}
                        onCheckedChange={() => {
                          logger.info('Toggling language filter', { language: lang });
                          onToggleLanguage(lang);
                        }}
                      />
                      <Label htmlFor={`lang-${lang}`} className="cursor-pointer">
                        {languageNames[lang] || lang}
                      </Label>
                    </div>
                  ))}
                </div>

                <div className="flex items-center justify-between">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      logger.info('Selecting all languages');
                      onSelectAllLanguages();
                    }}
                  >
                    Select All
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      logger.info('Clearing language selection');
                      onClearLanguageSelection();
                    }}
                  >
                    Clear
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </Panel>
    </>
  );
}
