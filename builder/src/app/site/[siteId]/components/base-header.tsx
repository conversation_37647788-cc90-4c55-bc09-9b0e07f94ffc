'use client'

import { ReactNode } from 'react'
import { SiteHeader } from '@/app/site/components/site-header'

interface BaseHeaderProps {
  /** Current page title */
  currentPage: string
  /** Action buttons to display on the right */
  actions?: ReactNode
  /** Optional extra content to display after the domain */
  extra?: ReactNode
}

/**
 * Base header component that uses the unified SiteHeader
 * This is a wrapper for backward compatibility
 */
export function BaseHeader({ 
  currentPage,
  actions,
  extra 
}: BaseHeaderProps) {
  return (
    <SiteHeader
      currentPage={currentPage}
      actions={actions}
      extra={extra}
      showDashboard={true}
    />
  )
}
