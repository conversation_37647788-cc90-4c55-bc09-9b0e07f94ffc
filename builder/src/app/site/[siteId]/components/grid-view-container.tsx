'use client';

import { useParams } from 'next/navigation';
import dynamic from 'next/dynamic';
import { Loader2 } from 'lucide-react';

// 动态导入 GridView 组件以减少初始加载大小
const GridView = dynamic(
  () => import('./site-map/grid-view').then(mod => ({ default: mod.GridView })),
  {
    loading: () => (
      <div className="h-full w-full flex items-center justify-center">
        <div className="flex flex-col items-center gap-2">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-sm text-muted-foreground">Loading grid view...</p>
        </div>
      </div>
    ),
    ssr: false
  }
);

/**
 * 网格视图容器组件
 * 从路由参数中获取 siteId 并传递给 GridView 组件
 */
export function GridViewContainer() {
  // 在 Next.js App Router 中，useParams 返回的对象不会为 null
  // 但类型定义可能不够精确，所以我们需要做类型断言
  const params = useParams();
  const siteId = params?.siteId as string;
  
  if (!siteId) {
    return (
      <div className="h-full w-full flex items-center justify-center">
        <p className="text-muted-foreground">Site ID not found</p>
      </div>
    );
  }
  
  return (
    <div className="h-[calc(100vh-170px)] w-full overflow-hidden">
      <GridView websiteId={siteId} className="h-full w-full" />
    </div>
  );
}
