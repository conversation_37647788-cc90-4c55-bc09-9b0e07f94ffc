'use client';

import { useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
// import { Button } from '@/components/ui/button';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Grid, Network } from 'lucide-react';
import { Logger } from '@/lib/utils/logger';

// 创建日志记录器
const logger = new Logger('ViewSwitcher');

/**
 * 视图切换组件
 * 允许用户在站点地图和网格视图之间切换
 */
export function ViewSwitcher() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const view = searchParams?.get('view') || 'grid';

  // 处理视图切换
  const handleViewChange = useCallback((value: string) => {
    logger.info('Switching view', { from: view, to: value });
    
    // 获取当前 URL 并创建新的 URL 对象
    const url = new URL(window.location.href);
    
    // 设置新的视图参数
    if (value === 'grid') {
      // 如果是默认视图，移除参数
      url.searchParams.delete('view');
    } else {
      url.searchParams.set('view', value);
    }
    
    // 使用 router.push 进行客户端导航
    router.push(url.pathname + url.search);
  }, [router, view]);

  return (
    <Tabs value={view} onValueChange={handleViewChange} className="w-auto">
      <TabsList className="grid grid-cols-2 h-9">
        <TabsTrigger value="sitemap" className="flex items-center gap-1.5 px-3">
          <Network className="h-4 w-4" />
          <span className="hidden sm:inline">Site Map</span>
        </TabsTrigger>
        <TabsTrigger value="grid" className="flex items-center gap-1.5 px-3">
          <Grid className="h-4 w-4" />
          <span className="hidden sm:inline">Grid</span>
        </TabsTrigger>
      </TabsList>
    </Tabs>
  );
}
