'use client'

import { useSearchParams } from 'next/navigation'
import { SiteContent } from '@/app/site/components/site-content'
import { SiteOverviewHeader } from './site-overview-header'
import { SiteMapContainer } from './site-map-container'
import { GridViewContainer } from './grid-view-container'

/**
 * Site page content component that handles client-side interactions
 */
export function SitePageContent({ params }: { params: { siteId: string } }) {
  const searchParams = useSearchParams();
  const view = searchParams?.get('view') || 'grid';

  return (
    <div className="flex flex-col h-full w-full">
      <SiteOverviewHeader />
      <SiteContent>
        {view === 'grid' ? (
          <GridViewContainer />
        ) : (
          <SiteMapContainer />
        )}
      </SiteContent>
    </div>
  )
}
