"use client";

import { useParams, useRouter } from "next/navigation";
import SiteSEO from "@/components/seo-site";

export default function SiteSEOPage() {
  const params = useParams();
  const router = useRouter();
  
  // 确保 params 不为 null
  const siteId = params?.siteId as string || "";
  const type = params?.type as string || "overview";

  // 处理标签切换
  const handleTabChange = (tab: string) => {
    router.push(`/site/${siteId}/seo/${tab}`);
  };

  return (
    <div>
      <SiteSEO websiteId={siteId} defaultTab={type} onTabChange={handleTabChange} />
    </div>
  );
}
