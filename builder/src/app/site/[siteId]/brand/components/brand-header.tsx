'use client'

import { useParams, usePathname } from 'next/navigation'
import { BaseHeader } from '../../components/base-header'
import { useQuery } from '@tanstack/react-query'
import websiteService from '@/modules/website/service'
import { brandKeys } from '@/lib/api/queryKeys'

const brandPageTitles: Record<string, string> = {
  'target-audience': 'Target Audience',
  'brand-values': 'Brand Values',
  'competitive-advantages': 'Competitive Advantages',
  'target-market-segments': 'Target Market Segments',
  'user-stories': 'User Stories',
  'keywords': 'Keywords'
}

export function BrandHeader() {
  const { siteId } = useParams<{ siteId: string }>() || { siteId: '' }
  const pathname = usePathname()
  
  // 使用 React Query 获取网站数据，利用预取的数据
  const { data: website, isLoading } = useQuery({
    queryKey: brandKeys.website(siteId),
    queryFn: () => websiteService.getWebsite(siteId),
    enabled: !!siteId,
  })

  // 获取当前品牌页面
  const currentBrandPage = pathname?.split('/').pop() || ''
  const pageTitle = brandPageTitles[currentBrandPage] || 'Brand'

  return (
    <BaseHeader currentPage={pageTitle} />
  )
}
