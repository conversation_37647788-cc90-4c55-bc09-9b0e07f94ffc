'use client'

import { ReactNode } from 'react'
import Logger from '@/lib/logger'
import { PageHeader } from './components/page-header'
import { SiteContent } from '@/app/site/components/site-content'
import { PageSkeleton } from '@/components/page-skeleton'
import { Suspense } from 'react'
import { ErrorBoundary } from '@/components/error-boundary'
import { BlockEditorProvider } from '@/context/BlockEditorContext'

const logger = new Logger('PageEditorLayout')

interface LayoutProps {
  children: ReactNode
}

/**
 * Page Editor Layout
 * Provides the layout structure for the page editor with error handling and loading states
 */
export default function Layout({ children }: LayoutProps) {
  logger.info('Site Page Editor Layout mounted')

  return (
    <ErrorBoundary>
      <BlockEditorProvider>
        <div className="flex flex-col h-full">
          <PageHeader />
          <Suspense fallback={<PageSkeleton />}>
            <SiteContent card={false} padding={false}>
              {children}
            </SiteContent>
          </Suspense>
        </div>
      </BlockEditorProvider>
    </ErrorBoundary>
  )
}
