'use client'

import React, { useEffect } from 'react'
import { use<PERSON>ara<PERSON>, useSearchParams, useRouter } from 'next/navigation'
import dynamic from 'next/dynamic'
import Logger from '@/lib/logger'
import { PageBuilderProvider } from '@/context/PageBuilderContext'
import usePageVersionStore from '@/modules/pageVersion/store'
// import { clone } from 'ramda'
// import { useMutation, useQuery } from '@tanstack/react-query'
import { cn } from '@/lib/utils'
import { usePreviewStore } from '@/lib/store/preview-store'
import { useBlockEditor } from '@/context/BlockEditorContext'
// import { pageKeys, websiteKeys } from '@/lib/api/queryKeys'
import { useFetchPage, useFetchPages } from '@/lib/hooks/use-page-query'
import { useFetchWebsite } from '@/lib/hooks/use-website'
import { PageHistory } from './components/page-history'
import ResetUIState from './components/ResetUIState'
import { MobilePreviewGrid } from '@/components/ui/grid-background'

// 懒加载组件
const PageCanva = dynamic(() => import('@/components/pageCanva'), { 
  ssr: false,
  loading: () => (
    <div className="h-full w-full flex items-center justify-center">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
    </div>
  )
});
const Mobile = dynamic(() => import('@/components/mobile'), { ssr: false });
const BlockList = dynamic(() => 
  import('@/components/pageRender/BlockList').then(mod => ({ default: mod.BlockList })),
  { ssr: false }
);

// 懒加载 SEO 页面组件
const SeoPage = dynamic(() => 
  import('@/components/seo-page').then(mod => ({ default: mod.default })),
  { 
    ssr: false,
    loading: () => (
      <div className="h-full w-full flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>
    )
  }
);

// 懒加载侧边栏组件 - 处理命名导出
const SidebarTabs = dynamic(() => 
  import('./components/sidebar-tabs').then(mod => ({ default: mod.SidebarTabs })),
  { ssr: false }
);

const BlockEditorPopup = dynamic(() => 
  import('@/components/pageRender/BlockEditorPopup').then(mod => ({ default: mod.BlockEditorPopup })),
  { ssr: false }
);

const CreateSectionPopup = dynamic(() => 
  import('@/components/pageRender/CreateSectionPopup').then(mod => ({ default: mod.CreateSectionPopup })),
  { ssr: false }
);

// 懒加载 SectionProvider
const SectionProvider = dynamic(() => 
  import('@litpage/sections').then(mod => ({ default: mod.SectionProvider })),
  { 
    ssr: false,
    loading: () => (
      <div className="h-full w-full flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>
    )
  }
);

const logger = new Logger('PageEditorPage');

/**
 * Page Editor Container
 * Implements the original page editor layout with data loading
 */
export default function Page() {
  const { pageId, siteId } = useParams<{ pageId: string; siteId: string }>() || { pageId: '', siteId: '' }
  const searchParams = useSearchParams()
  const seoTab = searchParams?.get('seo') || 'overview'
  const router = useRouter()
  // const createPageVersion = usePageVersionStore((state) => state.createPageVersion)
  const showPreview = usePreviewStore((state) => state.showPreview)
  const activeTab = usePreviewStore((state) => state.activeTab)
  const setActiveTab = usePreviewStore((state) => state.setActiveTab)
  const { editorView } = useBlockEditor()

  // 使用 React Query 获取页面数据
  const { data: currentPage, isLoading: isPageLoading } = useFetchPage(pageId || '')

  // 使用 React Query 获取网站数据
  const { data: website } = useFetchWebsite(currentPage?.websiteId)
  const domain = website?.domain

  // 使用 React Query 获取页面列表
  // const { data: pages = [] } = useFetchPages(currentPage?.websiteId)

  // 处理版本创建
  // const mutation = useMutation({
  //   mutationFn: createPageVersion,
  //   onSuccess: () => {
  //     logger.info('Page version created successfully')
  //   },
  // })

  // useEffect(() => {
  //   const sub = $page.subscribe(({ loading, page }: any) => {
  //     if (!loading && pages.length > 0 && page.sections.length > 0 && currentPage.pageId === pageId) {
  //       const configuration = clone(currentPage.configuration)
  //       mutation.mutate({
  //         pageId: pageId,
  //         configuration,
  //       })
  //     }
  //   })
  //   return () => sub.unsubscribe()
  // }, [currentPage, pages, pageId, mutation, $page])

  // 预加载关键组件
  useEffect(() => {
    // 立即预加载主编辑区组件
    const preloadComponents = async () => {
      try {
        // 使用 Promise.all 并行加载关键组件
        await Promise.all([
          import('@/components/pageCanva'),
          import('@litpage/sections')
        ]);
      } catch (error) {
        console.error('Failed to preload components:', error);
      }
    };
    
    preloadComponents();
    
    // 在浏览器空闲时预加载其他组件
    if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
      (window as any).requestIdleCallback(() => {
        import('@/components/mobile');
        import('@/components/pageRender/BlockList');
      });
    }
  }, []);
  
  // 加载中状态
  if (isPageLoading) {
    return (
      <div className="h-full w-full flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>
    );
  }
  
  return (
    <div className="h-full w-full overflow-hidden">
      <PageBuilderProvider>
        <ResetUIState pageId={pageId} />
        <SectionProvider domain={domain ?? ''} initialIsEditMode={true}>
          <div className="flex h-full w-full relative">
            {/* PC Preview - Larger area */}
            <div className={cn(
              "absolute inset-0 transition-all duration-300 ease-in-out",
              showPreview ? "right-[360px]" : "right-0"
            )}>
              <div className="h-full w-full p-4">
                <div className="h-full w-full bg-card rounded-lg overflow-auto">
                  {editorView === 'canvas' ? (
                    <PageCanva />
                  ) : (
                    <SeoPage pageId={pageId} siteId={siteId} defaultTab={seoTab} onTabChange={(tab) => {
                      // 使用 Next.js Router API 更新查询参数
                      const params = new URLSearchParams();
                      params.set('seo', tab);
                      router.push(`/site/${siteId}/page/${pageId}?${params.toString()}`);
                    }} />
                  )}
                </div>
              </div>
            </div>

            {/* Sidebar - Mobile Preview or Block List */}
            <div className={cn(
              "absolute right-0 w-[360px] h-full border-l bg-muted transition-transform duration-300 ease-in-out flex flex-col",
              showPreview ? "translate-x-0" : "translate-x-full"
            )}>
              {/* 根据 activeTab 条件渲染不同的内容 */}
              {activeTab === 'editor' && (
                <BlockEditorPopup editable domain={domain} />
              )}
              {activeTab === 'creator' && (
                <CreateSectionPopup editable domain={domain ?? ''} />
              )}
              
              {/* Tabs for switching between preview and edit */}
              <SidebarTabs 
                activeTab={activeTab} 
                onTabChange={setActiveTab} 
              />
              
              {/* Content area based on active tab */}
              <div className="flex-1">
                {activeTab === 'preview' && (
                  <MobilePreviewGrid variant="dots" gridSize={16} opacity={0.12}>
                    <div className="p-4 w-full max-w-sm mx-auto">
                      <div className="h-[600px] w-full overflow-auto bg-white/90 dark:bg-gray-800/90 shadow-lg rounded-lg border border-border">
                        <Mobile config={{
                          domain: currentPage?.domain,
                          theme: currentPage?.theme,
                          language: currentPage?.language,
                          headers: {
                            [currentPage?.language || 'en']: {
                              variant: currentPage?.header?.variant || 'default',
                              id: currentPage?.header?.id || 'default-header',
                              logo: currentPage?.header?.configuration?.logo || 'LitPage',
                              links: currentPage?.header?.configuration?.links || [],
                              actions: currentPage?.header?.configuration?.actions || [],
                              languages: [
                                { code: 'en', name: 'English', flag: '' },
                                { code: 'zh', name: '中文', flag: '' }
                              ],
                              currentLanguage: currentPage?.language?.toLowerCase() || 'en',
                              theme: currentPage?.header?.configuration?.theme || { enabled: false, defaultTheme: 'system' }
                            }
                          },
                          footers: {
                            [currentPage?.language || 'en']: {
                              variant: currentPage?.footer?.variant || 'default',
                              id: currentPage?.footer?.id || 'default-footer',
                              logo: currentPage?.footer?.configuration?.logo || { url: '', alt: 'Footer Logo' },
                              links: currentPage?.footer?.configuration?.links || [],
                              copyright: currentPage?.footer?.configuration?.copyright || `© ${new Date().getFullYear()} LitPage. All rights reserved.`,
                              socialMedia: currentPage?.footer?.configuration?.socialMedia || []
                            }
                          },
                          schema: currentPage?.configuration,
                          languageInfo: currentPage?.languageInfo,
                          languageVersions: currentPage?.languageVersions,
                          languageUrls: currentPage?.languageUrls,
                          seo: currentPage?.seo
                        }} />
                      </div>
                    </div>
                  </MobilePreviewGrid>
                )}
                {activeTab === 'edit' && (
                  <BlockList domain={domain} />
                )}
                {activeTab === 'history' && (
                  <div className="h-full overflow-auto">
                    <PageHistory pageId={pageId} />
                  </div>
                )}
              </div>
            </div>
          </div>
        </SectionProvider>
      </PageBuilderProvider>
    </div>
  )
}
