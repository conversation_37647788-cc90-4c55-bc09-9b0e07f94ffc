'use client'

import React, { useEffect } from 'react';
import { useBlockEditor } from '@/context/BlockEditorContext';
import { usePreviewStore } from '@/lib/store/preview-store';

interface ResetUIStateProps {
  pageId: string;
}

const ResetUIState: React.FC<ResetUIStateProps> = ({ pageId }) => {
  const setShowPreview = usePreviewStore((state) => state.setShowPreview);
  const setActiveTab = usePreviewStore((state) => state.setActiveTab);
  const {
    setCurBlock,
    setEditorOpened,
    setThemeOpened,
    setPageThemeOpened,
    setCreateSectionOpened,
    setScrollToBlockId,
  } = useBlockEditor();

  useEffect(() => {
    setShowPreview(true);
    setActiveTab('preview');
    setCurBlock('');
    setEditorOpened(false);
    setThemeOpened(false);
    setPageThemeOpened(false);
    setCreateSectionOpened(false);
    setScrollToBlockId(null);
  }, [pageId]);

  return null;
};

export default ResetUIState;
