'use client'
import Link from 'next/link';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { toast } from '@/components/ui/use-toast'
import { 
  HistoryIcon, 
  SaveIcon, 
  GlobeIcon, 
  EyeIcon,
  MoreHorizontalIcon,
  ArrowUpIcon,
  PanelRightCloseIcon, 
  PanelRightOpenIcon,
  UndoIcon,
  ArchiveIcon,
  TrashIcon,
  RotateCcwIcon,
  CheckIcon,
  Loader2,
  SearchCheckIcon,
  ArrowLeftIcon,
  PencilIcon
} from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'
import { usePreviewStore } from '@/lib/store/preview-store'
import { Separator } from '@/components/ui/separator'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { BaseHeader } from '../../../components/base-header'
import { PageStatus } from '@/types/page'
import { cn } from '@/lib/utils'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { useFetchPage, usePublishPage, useArchivePage, useRestorePage, useDiscardChanges, useDeletePage } from '@/lib/hooks/use-page-query'
import { useFetchWebsite } from '@/lib/hooks/use-website'
import { useMemo, useState, useEffect } from 'react'
import { PublishDialog } from './publish-dialog'
import { PageModel } from "@/modules/page/model"
import pageService from '@/modules/page/service'
import { useBlockEditor } from '@/context/BlockEditorContext'

export function PageHeader() {
  const { siteId, pageId } = useParams<{ siteId: string; pageId: string }>() || { siteId: '', pageId: '' }
  const router = useRouter()
  const showPreview = usePreviewStore((state) => state.showPreview)
  const togglePreview = usePreviewStore((state) => state.togglePreview)
  const setActiveTab = usePreviewStore((state) => state.setActiveTab)
  const [showDiscardDialog, setShowDiscardDialog] = useState(false)
  const [showArchiveDialog, setShowArchiveDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [showPublishDialog, setShowPublishDialog] = useState(false)
  const [confirmSlug, setConfirmSlug] = useState('')
  const [affectedPages, setAffectedPages] = useState<PageModel[]>([])
  const [isLoadingAffectedPages, setIsLoadingAffectedPages] = useState(false)
  const [isSharedComponentModified, setIsSharedComponentModified] = useState(false)
  const [modifiedComponentType, setModifiedComponentType] = useState('')
  
  // Using React Query to fetch page data
  const { data: currentPage, isLoading: isPageLoading } = useFetchPage(pageId)
  
  // Using React Query to fetch website data
  const { data: currentWebsite } = useFetchWebsite(siteId)
  
  // Get editor view state from BlockEditorContext
  const { editorView, setEditorView } = useBlockEditor()
  
  // Using React Query mutation hooks
  const publishPageMutation = usePublishPage()
  const archivePageMutation = useArchivePage()
  const restorePageMutation = useRestorePage()
  const discardChangesMutation = useDiscardChanges()
  const deletePageMutation = useDeletePage()

  // State evaluation with useMemo for better reactivity
  const pageState = useMemo(() => {
    if (!currentPage) return null
    
    // Debug log
    console.log('Current page state:', {
      status: currentPage.status,
      publishedAt: currentPage.publishedAt,
      header: currentPage.header?.status,
      footer: currentPage.footer?.status,
      draftVersionId: currentPage.draftVersionId,
      publishedVersionId: currentPage.publishedVersionId
    })

    // Check header and footer component status
    const headerModified = currentPage.header?.status === 'MODIFIED';
    const footerModified = currentPage.footer?.status === 'MODIFIED';
    
    // Check if page content itself is modified
    // Page is considered modified if:
    // 1. It has a draft version ID, OR
    // 2. Its status is MODIFIED (which means content has changed)
    const contentModified = !!currentPage.draftVersionId || currentPage.status === 'MODIFIED';
    
    // If page is published but header or footer is modified, overall status should be MODIFIED
    let effectiveStatus = currentPage.status;
    if (currentPage.status === PageStatus.PUBLISHED && (headerModified || footerModified || contentModified)) {
      effectiveStatus = PageStatus.MODIFIED;
    }

    return {
      status: effectiveStatus,
      hasPublishedVersion: !!currentPage.publishedVersionId,
      hasDraftVersion: !!currentPage.draftVersionId,
      isInitialized: currentPage.status === PageStatus.INITIALIZED,
      publishedAt: currentPage.publishedAt,
      headerStatus: currentPage.header?.status,
      footerStatus: currentPage.footer?.status,
      headerModified,
      footerModified,
      contentModified
    }
  }, [currentPage])

  // Helper function to get current page state (uses memoized value)
  const getPageState = () => pageState

  // Get status text and icon
  const getStatusInfo = () => {
    const state = getPageState()
    if (!state) return { text: '', color: '', icon: null, tooltip: '' }

    // Debug log
    console.log('Status info state:', state)

    const getTimeTooltip = (prefix: string) => {
      if (!state.publishedAt) return ''
      try {
        return `${prefix} ${formatDistanceToNow(new Date(state.publishedAt), { 
          addSuffix: true,
          locale: zhCN,
          includeSeconds: true
        })}`
      } catch (error) {
        console.error('Error formatting date:', error, state.publishedAt)
        return ''
      }
    }

    // Add extra information based on component status
    const getComponentStatusText = () => {
      const parts = [];
      
      // Check if page content itself is modified
      if (state.contentModified) {
        parts.push('Content');
      }
      
      if (state.headerModified) {
        parts.push('Header');
      }
      
      if (state.footerModified) {
        parts.push('Footer');
      }
      
      if (parts.length === 0) return '';
      return ` (${parts.join(', ')} modified)`;
    }

    switch (state.status) {
      case PageStatus.DRAFT:
        return { 
          text: 'Draft', 
          color: 'bg-yellow-100 text-yellow-800',
          icon: <SaveIcon className="h-3 w-3" />,
          tooltip: ''
        }
      case PageStatus.PUBLISHED:
        const publishedTooltip = getTimeTooltip('Published')
        console.log('Published tooltip:', publishedTooltip) // Debug log
        return { 
          text: 'Published', 
          color: 'bg-green-100 text-green-800',
          icon: <CheckIcon className="h-3 w-3" />,
          tooltip: publishedTooltip
        }
      case PageStatus.MODIFIED:
        const componentStatus = getComponentStatusText();
        return { 
          text: `Unpublished Changes${componentStatus}`, 
          color: 'bg-blue-100 text-blue-800',
          icon: <ArrowUpIcon className="h-3 w-3" />,
          tooltip: getTimeTooltip('Last published')
        }
      case PageStatus.ARCHIVED:
        return { 
          text: 'Archived', 
          color: 'bg-gray-100 text-gray-800',
          icon: <ArchiveIcon className="h-3 w-3" />,
          tooltip: ''
        }
      default:
        return { text: '', color: '', icon: null, tooltip: '' }
    }
  }

  // Handle various operations
  const handlePublishClick = async () => {
    try {
      // Check if header or footer is modified
      if (currentPage) {
        setIsLoadingAffectedPages(true);
        let componentId = null;
        let componentType = null;
        
        // Check if header is modified
        if (currentPage.header && currentPage.header.status === 'MODIFIED') {
          componentId = currentPage.header.id;
          componentType = 'HEADER';
        } 
        // Check if footer is modified
        else if (currentPage.footer && currentPage.footer.status === 'MODIFIED') {
          componentId = currentPage.footer.id;
          componentType = 'FOOTER';
        }
        
        // If a shared component is modified, get affected pages
        if (componentId && componentType) {
          try {
            const result = await pageService.getAffectedPages(componentType, componentId);
            console.log('Affected pages result:', result);
            
            // API returns { success: true, data: [...pages] }
            if (result && Array.isArray(result)) {
              setAffectedPages(result);
              setIsSharedComponentModified(true);
              setModifiedComponentType(componentType);
            } else {
              // Fallback if result structure is different
              setAffectedPages([]);
              setIsSharedComponentModified(true);
              setModifiedComponentType(componentType);
              console.error('Unexpected API response format:', result);
            }
          } catch (error) {
            console.error('Error fetching affected pages:', error);
            // Continue with publish dialog even if we can't get affected pages
            setAffectedPages([]);
            setIsSharedComponentModified(true);
            setModifiedComponentType(componentType);
            toast({ 
              title: "Warning", 
              description: "Could not fetch all affected pages, but you can still proceed with publishing", 
              variant: "default" 
            });
          }
        } else {
          setIsSharedComponentModified(false);
          setAffectedPages([]);
        }
        
        setIsLoadingAffectedPages(false);
        setShowPublishDialog(true);
      }
    } catch (error) {
      setIsLoadingAffectedPages(false);
      console.error('Error checking component status:', error);
      toast({ 
        title: "Error", 
        description: "Failed to check component status", 
        variant: "destructive" 
      });
    }
  };

  const handlePublish = async (publishNote?: string) => {
    try {
      await publishPageMutation.mutateAsync({ pageId, publishNote });
      toast({ title: "Published", description: "Page published successfully" });
      setShowPublishDialog(false);
    } catch (error) {
      console.error('Error in handlePublish:', error);
      toast({ 
        title: "Error", 
        description: "Failed to publish page", 
        variant: "destructive" 
      });
    }
  };

  const handleDelete = async () => {
    try {
      if (!currentPage) return;
      
      // 检查是否为根页面，不允许删除根页面
      if (currentPage.slug === '/') {
        toast({ 
          title: "Error", 
          description: "Cannot delete the root page", 
          variant: "destructive" 
        });
        setShowDeleteDialog(false);
        return;
      }
      
      // 验证确认 slug 是否匹配
      if (confirmSlug !== currentPage.slug) {
        toast({ 
          title: "Error", 
          description: "The slug you entered does not match the page slug", 
          variant: "destructive" 
        });
        return;
      }
      
      await deletePageMutation.mutateAsync({ pageId, confirmSlug });
      toast({ title: "Deleted", description: "Page has been permanently deleted" });
      setShowDeleteDialog(false);
      
      // 删除成功后跳转到页面列表
      window.location.href = `/site/${siteId}/page`;
    } catch (error) {
      console.error('Error in handleDelete:', error);
      toast({ 
        title: "Error", 
        description: "Failed to delete page", 
        variant: "destructive" 
      });
    }
  };

  const handleArchive = async () => {
    try {
      // 检查是否为根页面，不允许归档根页面
      if (currentPage?.slug === '/') {
        toast({ 
          title: "Error", 
          description: "Cannot archive the root page", 
          variant: "destructive" 
        });
        setShowArchiveDialog(false);
        return;
      }
      
      await archivePageMutation.mutateAsync(pageId)
      toast({ title: "Archived", description: "Page has been archived" })
    } catch (error) {
      console.error('Error in handleArchive:', error)
      toast({ title: "Error", description: "Failed to archive page", variant: "destructive" })
    }
  }

  const handleRestore = async () => {
    try {
      await restorePageMutation.mutateAsync(pageId)
      toast({ title: "Restored", description: "Page has been restored" })
    } catch (error) {
      console.error('Error in handleRestore:', error)
      toast({ title: "Error", description: "Failed to restore page", variant: "destructive" })
    }
  }

  const handleDiscard = async () => {
    try {
      await discardChangesMutation.mutateAsync(pageId)
      toast({ title: "Discarded", description: "All changes have been discarded" })
      setShowDiscardDialog(false)
    } catch (error) {
      console.error('Error in handleDiscard:', error)
      toast({ title: "Error", description: "Failed to discard changes", variant: "destructive" })
    }
  }

  // Render action buttons
  const renderActionButtons = () => {
    const state = getPageState()
    if (!state) return null

    const buttons = []

    // Draft state
    if (state.status === PageStatus.DRAFT) {
      buttons.push(
        <Button 
          key="publish" 
          onClick={handlePublishClick}
          disabled={publishPageMutation.isPending || isLoadingAffectedPages}
        >
          <GlobeIcon className="h-4 w-4 mr-2" />
          {publishPageMutation.isPending || isLoadingAffectedPages ? 'Publishing...' : 'Publish'}
        </Button>,
        <Button
          key="seo-assessment"
          variant="outline"
          onClick={() => setEditorView(editorView === 'canvas' ? 'seo' : 'canvas')}
        >
          {editorView === 'canvas' ? (
            <>
              <SearchCheckIcon className="h-4 w-4 mr-2" />
              SEO Assessment
            </>
          ) : (
            <>
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Editor
            </>
          )}
        </Button>
      )
    }

    // Published without changes
    if (state.status === PageStatus.PUBLISHED) {
      buttons.push(
        <Button key="preview" variant="ghost" size="sm" asChild>
          <Link 
            href={(() => {
              // Get page language and website default language
              const pageLanguage = currentPage?.language || 'EN';
              const defaultLanguage = currentWebsite?.defaultLanguage || 'EN';
              
              // Build base URL
              const baseUrl = `https://${currentWebsite?.domain}.lit.page`;
              
              // Build slug part
              let slugPart = currentPage?.slug || '';
              if (!slugPart.startsWith('/')) {
                slugPart = `/${slugPart}`;
              }
              
              // Non-default languages need a language prefix
              if (pageLanguage !== defaultLanguage) {
                slugPart = `/${pageLanguage.toLowerCase()}${slugPart}`;
              }
              
              return `${baseUrl}${slugPart}`;
            })()} 
            target="_blank"
          >
            <EyeIcon className="h-4 w-4 mr-2" />
            View
          </Link>
        </Button>,
        <Button
          key="seo-assessment"
          variant="outline"
          onClick={() => setEditorView(editorView === 'canvas' ? 'seo' : 'canvas')}
        >
          {editorView === 'canvas' ? (
            <>
              <SearchCheckIcon className="h-4 w-4 mr-2" />
              SEO Assessment
            </>
          ) : (
            <>
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Editor
            </>
          )}
        </Button>,
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm">
              <MoreHorizontalIcon className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => setActiveTab('history')}>
              <HistoryIcon className="h-4 w-4 mr-2" />
              History
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem 
              onClick={() => setShowArchiveDialog(true)} 
              className="text-destructive"
              disabled={archivePageMutation.isPending}
            >
              <ArchiveIcon className="h-4 w-4 mr-2" />
              {archivePageMutation.isPending ? 'Archiving...' : 'Archive'}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    }

    // Published with changes
    if (state.status === PageStatus.MODIFIED) {
      buttons.push(
        <Button 
          key="update" 
          onClick={handlePublishClick}
          disabled={publishPageMutation.isPending || isLoadingAffectedPages}
        >
          <ArrowUpIcon className="h-4 w-4 mr-2" />
          {publishPageMutation.isPending || isLoadingAffectedPages ? 'Updating...' : 'Update'}
        </Button>,
        <Button
          key="seo-assessment"
          variant="outline"
          onClick={() => setEditorView(editorView === 'canvas' ? 'seo' : 'canvas')}
        >
          {editorView === 'canvas' ? (
            <>
              <SearchCheckIcon className="h-4 w-4 mr-2" />
              SEO Assessment
            </>
          ) : (
            <>
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Editor
            </>
          )}
        </Button>,
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm">
              <MoreHorizontalIcon className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem 
              onClick={() => setShowDiscardDialog(true)}
              disabled={discardChangesMutation.isPending}
            >
              <UndoIcon className="h-4 w-4 mr-2" />
              {discardChangesMutation.isPending ? 'Discarding...' : 'Discard Changes'}
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => setActiveTab('history')}>
              <HistoryIcon className="h-4 w-4 mr-2" />
              History
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem 
              onClick={() => setShowArchiveDialog(true)} 
              className="text-destructive"
              disabled={archivePageMutation.isPending}
            >
              <ArchiveIcon className="h-4 w-4 mr-2" />
              {archivePageMutation.isPending ? 'Archiving...' : 'Archive'}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    }

    // Archived state
    if (state.status === PageStatus.ARCHIVED) {
      buttons.push(
        <Button 
          key="restore" 
          onClick={handleRestore}
          disabled={restorePageMutation.isPending}
        >
          <RotateCcwIcon className="h-4 w-4 mr-2" />
          {restorePageMutation.isPending ? 'Restoring...' : 'Restore'}
        </Button>,
        <Button
          key="seo-assessment"
          variant="outline"
          onClick={() => setEditorView(editorView === 'canvas' ? 'seo' : 'canvas')}
        >
          {editorView === 'canvas' ? (
            <>
              <SearchCheckIcon className="h-4 w-4 mr-2" />
              SEO Assessment
            </>
          ) : (
            <>
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Editor
            </>
          )}
        </Button>,
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm">
              <MoreHorizontalIcon className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => setActiveTab('history')}>
              <HistoryIcon className="h-4 w-4 mr-2" />
              History
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem 
              className="text-destructive"
              onClick={() => setShowDeleteDialog(true)}
              disabled={deletePageMutation.isPending}
            >
              <TrashIcon className="h-4 w-4 mr-2" />
              {deletePageMutation.isPending ? 'Deleting...' : 'Delete Permanently'}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    }

    // Add preview toggle button
    buttons.push(
      <Separator key="separator" orientation="vertical" className="h-4" />,
      <Button
        key="preview-toggle"
        variant="ghost"
        size="icon"
        className="ml-0"
        onClick={togglePreview}
      >
        {showPreview ? (
          <PanelRightCloseIcon className="h-4 w-4" />
        ) : (
          <PanelRightOpenIcon className="h-4 w-4" />
        )}
      </Button>
    )

    return buttons
  }

  // Discard changes confirmation dialog
  const renderDiscardDialog = () => {
    return (
      <AlertDialog open={showDiscardDialog} onOpenChange={setShowDiscardDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Discard Changes</AlertDialogTitle>
            <AlertDialogDescription>
              This will discard all unpublished changes and revert to the last published version.
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDiscard}
              disabled={discardChangesMutation.isPending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {discardChangesMutation.isPending ? 'Discarding...' : 'Discard Changes'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    )
  }

  // Archive page confirmation dialog
  const renderArchiveDialog = () => {
    // 检查是否为根页面
    const isRootPage = currentPage?.slug === '/';
    
    return (
      <AlertDialog open={showArchiveDialog} onOpenChange={setShowArchiveDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Archive Page</AlertDialogTitle>
            <AlertDialogDescription>
              {isRootPage ? (
                <p className="text-destructive font-bold">Root page (/) cannot be archived as it serves as the website homepage.</p>
              ) : (
                <>
                  <p>Archiving this page will:</p>
                  <ul className="list-disc pl-5 mt-2 space-y-1">
                    <li>Remove it from public view</li>
                    <li>Make it inaccessible to visitors</li>
                    <li>Preserve all content and versions</li>
                    <li>Allow you to restore it later if needed</li>
                  </ul>
                  <p className="mt-2">The page will remain in your dashboard but will be marked as archived.</p>
                </>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            {!isRootPage && (
              <AlertDialogAction 
                onClick={handleArchive}
                disabled={archivePageMutation.isPending}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                {archivePageMutation.isPending ? 'Archiving...' : 'Archive Page'}
              </AlertDialogAction>
            )}
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    )
  }

  // Delete page confirmation dialog
  const renderDeleteDialog = () => {
    // 检查是否为根页面
    const isRootPage = currentPage?.slug === '/';
    
    return (
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Page Permanently</AlertDialogTitle>
            <AlertDialogDescription>
              {isRootPage ? (
                <p className="text-destructive font-bold">Root page (/) cannot be deleted as it serves as the website homepage.</p>
              ) : (
                <>
                  <p className="mb-2">This action <span className="font-bold">cannot be undone</span>. This will permanently delete the page and all its versions.</p>
                  <p className="mb-4">To confirm, please enter the page slug: <span className="font-bold">{currentPage?.slug}</span></p>
                  <input
                    type="text"
                    value={confirmSlug}
                    onChange={(e) => setConfirmSlug(e.target.value)}
                    placeholder="Enter page slug to confirm"
                    className="w-full p-2 border border-gray-300 rounded-md"
                  />
                </>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setConfirmSlug('')}>Cancel</AlertDialogCancel>
            {!isRootPage && (
              <AlertDialogAction 
                onClick={handleDelete}
                disabled={deletePageMutation.isPending || confirmSlug !== currentPage?.slug}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                {deletePageMutation.isPending ? 'Deleting...' : 'Delete Permanently'}
              </AlertDialogAction>
            )}
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    )
  }

  // Publish Dialog
  const renderPublishDialog = () => {
    return (
      <PublishDialog
        isOpen={showPublishDialog}
        onClose={() => setShowPublishDialog(false)}
        onConfirm={handlePublish}
        isPublishing={publishPageMutation.isPending}
        affectedPages={affectedPages}
        isSharedComponent={isSharedComponentModified}
        componentType={modifiedComponentType}
      />
    )
  }

  const { text, color, icon, tooltip } = getStatusInfo()

  return (
    <>
      <BaseHeader
        currentPage={currentPage?.title || 'Page'}
        extra={
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            {(() => {
              // Get page language and website default language
              const pageLanguage = currentPage?.language || 'EN';
              const defaultLanguage = currentWebsite?.defaultLanguage || 'EN';
              
              // Build base URL
              const baseUrl = `https://${currentWebsite?.domain}.lit.page`;
              
              // Build slug part
              let slugPart = currentPage?.slug || '';
              if (!slugPart.startsWith('/')) {
                slugPart = `/${slugPart}`;
              }
              
              // Non-default languages need a language prefix
              if (pageLanguage !== defaultLanguage) {
                slugPart = `/${pageLanguage.toLowerCase()}${slugPart}`;
              }
              
              return `${baseUrl}${slugPart}`;
            })()}
          </div>
        }
        actions={
          <div className="flex items-center gap-2">
            {isPageLoading ? (
              <div className="animate-pulse h-6 w-20 bg-muted rounded"></div>
            ) : (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <span className={cn(
                      "px-2 py-1 rounded text-xs flex items-center gap-1.5 cursor-default",
                      color
                    )}>
                      {icon}
                      {text}
                    </span>
                  </TooltipTrigger>
                  {tooltip && <TooltipContent>{tooltip}</TooltipContent>}
                </Tooltip>
              </TooltipProvider>
            )}
            <Separator orientation="vertical" className="h-4" />
            {renderActionButtons()}
          </div>
        }
      />
      {renderDiscardDialog()}
      {renderArchiveDialog()}
      {renderDeleteDialog()}
      {renderPublishDialog()}
    </>
  )
}
