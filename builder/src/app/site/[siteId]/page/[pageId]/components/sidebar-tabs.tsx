import React from 'react';
import { cn } from '@/lib/utils';
import { Smartphone, List, History } from 'lucide-react';

interface SidebarTabsProps {
  activeTab: 'preview' | 'edit' | 'editor' | 'creator' | 'history';
  onTabChange: (tab: 'preview' | 'edit' | 'editor' | 'creator' | 'history') => void;
}

export const SidebarTabs: React.FC<SidebarTabsProps> = ({ 
  activeTab, 
  onTabChange 
}) => {
  // Only switch between main tabs (preview, edit, and history)
  // editor and creator tabs return to edit tab via back button
  const isMainTab = activeTab === 'preview' || activeTab === 'edit' || activeTab === 'history';
  
  if (!isMainTab) return null; // If it's an editor or creator tab, don't show the tab bar
  
  return (
    <div className="flex border-b border-border">
      <button
        className={cn(
          "flex-1 py-3 px-4 text-sm font-medium text-center flex items-center justify-center transition-colors",
          activeTab === 'preview' 
            ? "border-b-2 border-primary text-primary" 
            : "text-muted-foreground hover:text-foreground"
        )}
        onClick={() => onTabChange('preview')}
      >
        <Smartphone size={18} className="mr-2" />
        Mobile Preview
      </button>
      <button
        className={cn(
          "flex-1 py-3 px-4 text-sm font-medium text-center flex items-center justify-center transition-colors",
          activeTab === 'edit' 
            ? "border-b-2 border-primary text-primary" 
            : "text-muted-foreground hover:text-foreground"
        )}
        onClick={() => onTabChange('edit')}
      >
        <List size={18} className="mr-2" />
        Block List
      </button>
      <button
        className={cn(
          "flex-1 py-3 px-4 text-sm font-medium text-center flex items-center justify-center transition-colors",
          activeTab === 'history' 
            ? "border-b-2 border-primary text-primary" 
            : "text-muted-foreground hover:text-foreground"
        )}
        onClick={() => onTabChange('history')}
      >
        <History size={18} className="mr-2" />
        History
      </button>
    </div>
  );
};
