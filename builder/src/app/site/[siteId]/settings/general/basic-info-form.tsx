"use client"

import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { useParams } from "next/navigation"
import { useEffect } from "react"
import { toast } from "@/components/ui/use-toast"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { langs } from "@/lib/langs"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { useFetchWebsite, useUpdateWebsite } from "@/lib/hooks/use-website"
import { useQueryClient } from "@tanstack/react-query";
import { websiteKeys } from "@/lib/api/queryKeys";
import Logger from "@/lib/logger"

const logger = new Logger('BasicInfoForm')

// 表单值类型定义
interface BasicInfoFormValues {
  name: string;
  description: string;
  defaultLanguage: string;
  logo: string;
  tags: string;
  domain: string; // 新增 domain 字段
}

// 表单验证规则
const basicInfoSchema = z.object({
  name: z.string().min(1, { message: "Website name is required" }),
  description: z.string().optional(),
  defaultLanguage: z.string().min(1, { message: "Default language is required" }),
  logo: z.string().optional(),
  tags: z.string().optional(),
  domain: z.string().min(1, { message: "Domain is required" }), // 新增 domain 字段验证
});

export function BasicInfoForm() {
  const params = useParams();
  const siteId = params?.siteId as string;

  // 使用项目现有的 React Query 钩子获取网站信息
  const { data: website, isLoading } = useFetchWebsite(siteId);
  
  // 使用项目现有的 React Query 钩子更新网站信息
  const updateWebsite = useUpdateWebsite();
  const queryClient = useQueryClient();

  const form = useForm<BasicInfoFormValues>({
    resolver: zodResolver(basicInfoSchema),
    defaultValues: {
      name: "",
      description: "",
      defaultLanguage: "EN",
      logo: "",
      tags: "",
      domain: "", // 新增 domain 字段默认值
    }
  });

  // 当网站数据加载完成后，更新表单值
  useEffect(() => {
    if (website) {
      logger.debug('Setting form values from website data', website);
      form.setValue('name', website.name || "");
      form.setValue('description', website.description || "");
      form.setValue('defaultLanguage', website.defaultLanguage || "EN");
      form.setValue('logo', website.logo || "");
      form.setValue('tags', website.tags?.join(', ') || "");
      form.setValue('domain', website.domain || ""); // 新增 domain 字段赋值
    }
  }, [website, form]);

  const onSubmit = async (data: BasicInfoFormValues) => {
    logger.debug('Submitting form data', data);
    
    // 处理标签
    const tags = data.tags 
      ? data.tags.split(',').map(tag => tag.trim()).filter(Boolean)
      : undefined;

    // 准备更新数据 - 不包含 domain 字段
    const updateData = {
      name: data.name,
      description: data.description,
      defaultLanguage: data.defaultLanguage,
      logo: data.logo || undefined,
      tags,
      configuration: {
        ...(website?.configuration || {}),
        theme: website?.configuration?.theme || 'default'
      }
    };
    
    logger.debug('Sending update data to server', updateData);

    // 使用 useUpdateWebsite 钩子更新网站信息
    try {
      await updateWebsite.mutateAsync({
        websiteId: siteId,
        data: updateData
      });
      logger.debug('Website update successful');
      
      // 显示成功提示
      toast({
        title: "Success",
        description: "Website information updated successfully",
      });
    } catch (error) {
      logger.error('Website update failed', error);
      // 显示错误提示
      toast({
        title: "Error",
        description: "Failed to update website information",
        variant: "destructive",
      });
    }
  };

  if (isLoading) {
    return <div className="flex items-center justify-center p-4">Loading...</div>;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Website Information</CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="domain"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Domain</FormLabel>
                  <FormControl>
                    <div className="flex items-center">
                      <Input 
                        placeholder="yoursite" 
                        {...field} 
                        disabled 
                        className="bg-muted" 
                      />
                      <span className="ml-2 text-sm text-muted-foreground">.lit.page</span>
                    </div>
                  </FormControl>
                  <FormDescription>
                    Your website URL is {field.value || "yoursite"}.lit.page (read-only)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Website Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter website name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Enter website description" 
                      className="min-h-[100px]"
                      {...field} 
                    />
                  </FormControl>
                  <FormDescription>
                    Brief description of your website (shown in search results)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="defaultLanguage"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Default Language</FormLabel>
                  <Select
                    value={field.value}
                    onValueChange={field.onChange}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a language" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {Object.entries(langs).map(([code, lang]) => (
                        <SelectItem key={code} value={code}>
                          {lang.name} ({lang.label})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Primary language for your website
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="logo"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Logo URL</FormLabel>
                  <FormControl>
                    <Input placeholder="https://example.com/logo.png" {...field} />
                  </FormControl>
                  <FormDescription>
                    URL to your website logo image
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="tags"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tags</FormLabel>
                  <FormControl>
                    <Input placeholder="blog, portfolio, business" {...field} />
                  </FormControl>
                  <FormDescription>
                    Comma-separated tags to categorize your website
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button 
              type="submit" 
              disabled={updateWebsite.isPending}
            >
              {updateWebsite.isPending ? "Saving..." : "Save Changes"}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
