"use client"

import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { usePara<PERSON>, useRouter } from "next/navigation"
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useEffect, useState } from "react"
import { toast } from "@/components/ui/use-toast"
import { Button } from "@/components/ui/button"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, CheckCircle2, Copy, ExternalLink } from "lucide-react"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import websiteService from "@/modules/website/service"
import { websiteKeys } from '@/lib/api/queryKeys'

type VerifyDomainResponse = {
  success: boolean;
  message: string;
  data: {
    domain: string;
    status: 'PENDING' | 'ACTIVE' | 'FAILED';
    verificationCode?: string;
  };
  error?: string;
};

type CustomDomain = {
  id: string;
  domain: string;
  status: 'PENDING' | 'ACTIVE' | 'FAILED';
  websiteId: string;
  createdAt: string;
  updatedAt: string;
  verificationCode?: string;
};

const domainFormSchema = z.object({
  domain: z
    .string()
    .min(1, "Please enter your domain name")
    .regex(/^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$/, {
      message: "Please enter a valid domain (e.g., example.com or www.example.com)",
    })
    .refine(domain => !domain.includes('lit.page'), {
      message: "Please use your own domain, not a lit.page subdomain"
    }),
})

type DomainFormValues = z.infer<typeof domainFormSchema>

export function GeneralForm() {
  const params = useParams();
  const siteId = params?.siteId as string;
  const queryClient = useQueryClient();
  const router = useRouter();

  // 获取自定义域名信息
  const { data: customDomain, isLoading } = useQuery({
    queryKey: websiteKeys.customDomain(siteId),
    queryFn: () => websiteService.getCustomDomain(siteId),
    enabled: Boolean(siteId),
    staleTime: 5 * 60 * 1000, // 5分钟内数据保持新鲜
    refetchOnWindowFocus: false, // 避免频繁重新获取
  });

  // 设置自定义域名
  const { mutate: setDomain, isPending: isSettingDomain } = useMutation({
    mutationFn: (domain: string) => websiteService.setCustomDomain(siteId, domain),
    onSuccess: (response) => {
      // 直接更新缓存而不是使其失效，这样可以立即显示新的域名
      queryClient.setQueryData(websiteKeys.customDomain(siteId), response);
      // 同时使其失效以确保后续请求获取最新数据
      queryClient.invalidateQueries({ queryKey: websiteKeys.customDomain(siteId) });
      toast({
        title: "Custom domain configured",
        description: "DNS configuration required. Follow the setup instructions below to complete domain setup.",
      });
    },
    onError: (error: any) => {
      let errorMessage = "Unable to configure domain. Please check your domain and try again.";
      
      if (error?.response?.status === 409) {
        errorMessage = "This domain is already connected to another website. Each domain can only be used once.";
      } else if (error?.response?.status === 400) {
        errorMessage = "Invalid domain format. Please use a valid domain like &apos;example.com&apos; or &apos;www.example.com&apos;.";
      } else if (error?.response?.status === 403) {
        errorMessage = "Domain verification required. Please ensure you own this domain.";
      } else if (error?.response?.status === 429) {
        errorMessage = "Too many requests. Please wait a moment before trying again.";
      } else if (error?.message) {
        errorMessage = error.message;
      }
      
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  });

  // 验证域名DNS设置
  const { mutate: verifyDomain, isPending: isVerifying } = useMutation<VerifyDomainResponse>({
    mutationFn: async () => {
      return await websiteService.verifyCustomDomain(siteId);
    },
    onSuccess: (response: any) => {
      queryClient.invalidateQueries({ queryKey: websiteKeys.customDomain(siteId) });
      
      if (response.status === 'ACTIVE') {
        toast({
          title: "Domain verification successful",
          description: "Your custom domain is now active and ready to use!",
        });
      } else {
        toast({
          title: "Domain verification pending",
          description: response.message || "DNS records not detected yet. Changes can take up to 48 hours to propagate globally.",
          variant: "destructive",
        });
      }
    },
    onError: (error) => {
      toast({
        title: "Verification failed",
        description: "Unable to verify domain. Please check your DNS settings and try again.",
        variant: "destructive",
      });
    }
  });

  // 删除自定义域名
  const { mutate: deleteDomain, isPending: isDeleting } = useMutation({
    mutationFn: () => websiteService.deleteCustomDomain(siteId),
    onSuccess: () => {
      // 直接设置缓存为 null 而不是使其失效，立即反映删除状态
      queryClient.setQueryData(websiteKeys.customDomain(siteId), null);
      form.reset({ domain: ""});
      toast({
        title: "Custom domain removed",
        description: "Your website is now accessible only via the default .lit.page subdomain.",
      });
    },
    onError: (error) => {
      toast({
        title: "Removal failed",
        description: "Unable to remove custom domain. Please contact support if this issue persists.",
        variant: "destructive",
      });
    }
  });

  const form = useForm<DomainFormValues>({
    resolver: zodResolver(domainFormSchema),
    defaultValues: {
      domain: "",
    }
  });

  // 当域名数据加载完成后，更新表单值
  useEffect(() => {
    if (customDomain?.domain) {
      form.setValue('domain', customDomain.domain);
    } else if (customDomain === null) {
      // 明确没有自定义域名时，清空表单
      form.setValue('domain', '');
    }
  }, [customDomain, form]);

  const onSubmit = async (data: DomainFormValues) => {
    await setDomain(data.domain);
  };

  if (isLoading) {
    return <div className="flex items-center justify-center p-4">Loading...</div>;
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <FormField
          control={form.control}
          name="domain"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Custom Domain</FormLabel>
              <FormControl>
                <div className="flex space-x-2">
                  <Input
                    placeholder="example.com"
                    {...field}
                    disabled={isSettingDomain || isVerifying || isDeleting}
                  />
                  <Button 
                    type="submit" 
                    disabled={isSettingDomain || isVerifying || isDeleting}
                  >
                    {customDomain ? "Update" : "Connect Domain"}
                  </Button>
                </div>
              </FormControl>
              <FormDescription>
                Enter your domain name (e.g., example.com or www.example.com). No protocol needed.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {customDomain && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium">Domain Status:</span>
                <Badge variant={customDomain.status === 'ACTIVE' ? 'default' : customDomain.status === 'FAILED' ? 'destructive' : 'secondary'}>
                  {customDomain.status === 'PENDING' ? 'Configuration Required' : 
                   customDomain.status === 'ACTIVE' ? 'Active & Live' : 
                   'Verification Failed'}
                </Badge>
              </div>
              {customDomain.status === 'ACTIVE' && (
                <div className="text-sm text-muted-foreground">
                  🌐 <a 
                    href={`https://${customDomain.domain}`} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 underline"
                  >
                    Visit {customDomain.domain}
                  </a>
                </div>
              )}
            </div>

            {customDomain.status === 'ACTIVE' && (
              <Alert className="border-green-200 bg-green-50 dark:bg-green-950/30">
                <CheckCircle2 className="h-4 w-4 text-green-600" />
                <AlertDescription>
                  <div className="space-y-2">
                    <p className="font-medium text-green-900 dark:text-green-100">
                      🎉 Your custom domain is live!
                    </p>
                    <p className="text-sm text-green-800 dark:text-green-200">
                      Your website is now accessible at <strong>{customDomain.domain}</strong> with automatic HTTPS encryption.
                      The domain is fully configured and ready for visitors.
                    </p>
                    <div className="pt-2 border-t border-green-200 dark:border-green-800">
                      <p className="text-xs text-green-700 dark:text-green-300">
                        💡 <strong>Pro tip:</strong> You can update your domain anytime. Any changes will require DNS reconfiguration.
                      </p>
                    </div>
                  </div>
                </AlertDescription>
              </Alert>
            )}

            {customDomain.status === 'FAILED' && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  <div className="space-y-2">
                    <p className="font-medium">
                      ❌ Domain verification failed
                    </p>
                    <p className="text-sm">
                      We couldn&apos;t verify your domain configuration. Please check your DNS settings and try again.
                    </p>
                    <div className="pt-2 border-t border-destructive/20">
                      <p className="text-xs">
                        Common issues: Incorrect DNS records, propagation delays, or domain ownership problems.
                        Contact support if this persists after 48 hours.
                      </p>
                    </div>
                  </div>
                </AlertDescription>
              </Alert>
            )}

            {customDomain.status === 'PENDING' && (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription className="space-y-5">
                  <div>
                    <h4 className="font-semibold mb-3">DNS Configuration Required</h4>
                    <p className="text-sm text-muted-foreground mb-4">
                      Configure your DNS settings to connect your domain. Add both records below to your DNS provider.
                    </p>
                  </div>
                  
                  <div className="space-y-4">
                    <div>
                      <p className="font-medium mb-2 text-sm">1. Domain Verification (TXT Record)</p>
                      <p className="text-xs text-muted-foreground mb-2">
                        This proves you own the domain. Required for security.
                      </p>
                      <div className="bg-muted p-3 rounded-md border">
                        <div className="grid grid-cols-1 gap-2 text-sm font-mono">
                          <div><span className="text-muted-foreground">Type:</span> TXT</div>
                          <div><span className="text-muted-foreground">Name:</span> @ <span className="text-xs text-muted-foreground">(or root domain)</span></div>
                          <div><span className="text-muted-foreground">Value:</span> {customDomain.verificationCode}</div>
                        </div>
                      </div>
                    </div>

                    <div>
                      <p className="font-medium mb-2 text-sm">2. Domain Pointing (CNAME Record)</p>
                      <p className="text-xs text-muted-foreground mb-2">
                        This directs traffic from your domain to our servers.
                      </p>
                      <div className="bg-muted p-3 rounded-md border">
                        <div className="grid grid-cols-1 gap-2 text-sm font-mono">
                          <div><span className="text-muted-foreground">Type:</span> CNAME</div>
                          <div><span className="text-muted-foreground">Name:</span> {customDomain.domain.startsWith('www.') ? 'www' : '@'}</div>
                          <div><span className="text-muted-foreground">Target:</span> cdn.lit.page</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-blue-50 dark:bg-blue-950/30 p-4 rounded-md border border-blue-200 dark:border-blue-800">
                    <p className="font-medium text-sm mb-2 text-blue-900 dark:text-blue-100">
                      🌐 Using Cloudflare DNS?
                    </p>
                    <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                      <li>• Enable <strong>Proxy status</strong> (orange cloud) for better performance</li>
                      <li>• Set SSL/TLS mode to <strong>&quot;Full&quot;</strong> or <strong>&quot;Full (strict)&quot;</strong></li>
                      <li>• Enable <strong>&quot;Always Use HTTPS&quot;</strong> for security</li>
                    </ul>
                  </div>

                  <div className="bg-amber-50 dark:bg-amber-950/30 p-4 rounded-md border border-amber-200 dark:border-amber-800">
                    <div className="flex items-start space-x-2">
                      <span className="text-amber-600 dark:text-amber-400 text-lg">⏱️</span>
                      <div className="text-sm">
                        <p className="font-medium text-amber-900 dark:text-amber-100 mb-1">
                          Propagation Timeline
                        </p>
                        <p className="text-amber-800 dark:text-amber-200">
                          DNS changes typically take <strong>5-30 minutes</strong>, but can take up to <strong>48 hours</strong> to propagate globally.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="pt-2">
                    <p className="text-xs text-muted-foreground">
                      💡 <strong>Tip:</strong> Click &quot;Verify DNS&quot; below to check if your configuration is detected.
                    </p>
                  </div>
                </AlertDescription>
              </Alert>
            )}

            <div className="flex space-x-2">
              <Button
                type="button"
                variant="secondary"
                onClick={() => verifyDomain()}
                disabled={isSettingDomain || isVerifying || isDeleting}
                className="flex items-center space-x-2"
              >
                {isVerifying ? (
                  <>
                    <span className="animate-spin">🔄</span>
                    <span>Checking DNS...</span>
                  </>
                ) : (
                  <>
                    <span>🔍</span>
                    <span>Verify DNS Configuration</span>
                  </>
                )}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => deleteDomain()}
                disabled={isSettingDomain || isVerifying || isDeleting}
                className="text-destructive hover:text-destructive"
              >
                {isDeleting ? "Removing..." : "Remove Domain"}
              </Button>
            </div>
          </div>
        )}
      </form>
    </Form>
  );
}
