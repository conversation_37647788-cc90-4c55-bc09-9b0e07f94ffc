"use client"

import { Separator } from "@/components/ui/separator"
import { GeneralForm } from "./general-form"
import { BasicInfoForm } from "./basic-info-form"
import { DeleteWebsiteZone } from "./delete-website-zone"
import { useParams } from "next/navigation"

export default function SettingsGeneralPage() {
  const params = useParams();
  const siteId = params?.siteId as string;

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">General Settings</h3>
        <p className="text-sm text-muted-foreground">
          Manage your website general settings and custom domain
        </p>
      </div>
      <Separator />
      
      {/* 基本信息表单 */}
      <div className="space-y-4">
        <h4 className="text-md font-medium">Website Information</h4>
        <BasicInfoForm />
      </div>
      
      {/* 自定义域名设置 */}
      <div className="space-y-4 mt-8">
        <h4 className="text-md font-medium">Custom Domain</h4>
        <GeneralForm />
      </div>
      
      {/* 危险区域 */}
      <div className="mt-12">
        <Separator className="my-6" />
        <h2 className="text-xl font-semibold text-destructive mb-4">Danger Zone</h2>
        <DeleteWebsiteZone websiteId={siteId} />
      </div>
    </div>
  )
}
