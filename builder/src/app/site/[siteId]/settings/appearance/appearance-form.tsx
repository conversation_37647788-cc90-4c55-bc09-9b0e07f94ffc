"use client"

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { useParams } from "next/navigation"
import { z } from "zod"
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from "@/components/ui/use-toast"
import { Button } from "@/components/ui/button"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from "@/components/ui/form"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import websiteService from "@/modules/website/service"
import { brandKeys } from '@/lib/api/queryKeys'
import { useEffect, useState } from "react"
import { 
  Monitor, 
  Smartphone, 
  Maximize, 
  Sun, 
  Moon, 
  Laptop, 
  Palette, 
  Briefcase, 
  Brush, 
  GraduationCap,
  Sparkles
} from "lucide-react"

// Form validation schema
const visualFormSchema = z.object({
  pageWidth: z.enum(['normal', 'wide', 'full'], {
    required_error: "Please select a page width setting",
  }),
  colorMode: z.enum(['light', 'dark', 'system'], {
    required_error: "Please select a color mode",
  }),
  theme: z.enum([
    'default', 'tech', 'creative', 'finance', 'education',
    'gradient-purple-blue', 'gradient-cyan-blue', 'gradient-green-blue',
    'gradient-purple-pink', 'gradient-pink-orange', 'gradient-teal-lime', 'gradient-red-yellow'
  ], {
    required_error: "Please select a website theme",
  }),
})

type VisualFormValues = z.infer<typeof visualFormSchema>

// Page width options
const pageWidthOptions = [
  {
    value: 'normal',
    label: 'Normal Width',
    description: 'Suitable for most content, max width 1280px',
    icon: <Smartphone className="w-5 h-5" />
  },
  {
    value: 'wide',
    label: 'Wide Mode',
    description: 'Great for displaying more content, max width 1536px',
    icon: <Monitor className="w-5 h-5" />
  },
  {
    value: 'full',
    label: 'Full Width',
    description: 'Utilize full screen space, width 100%',
    icon: <Maximize className="w-5 h-5" />
  }
]

// Color mode options
const colorModeOptions = [
  {
    value: 'light',
    label: 'Light Mode',
    description: 'Classic light theme',
    icon: <Sun className="w-5 h-5" />
  },
  {
    value: 'dark',
    label: 'Dark Mode',
    description: 'Eye-friendly dark theme',
    icon: <Moon className="w-5 h-5" />
  },
  {
    value: 'system',
    label: 'System',
    description: 'Follow system settings automatically',
    icon: <Laptop className="w-5 h-5" />
  }
]

// Website theme options
const themeOptions = [
  {
    value: 'default',
    label: 'Default Theme',
    description: 'Clean and classic design style',
    icon: <Palette className="w-5 h-5" />,
    className: '',
    category: 'basic'
  },
  {
    value: 'tech',
    label: 'Tech Theme',
    description: 'Modern technology-focused design',
    icon: <Laptop className="w-5 h-5" />,
    className: 'theme-tech',
    category: 'basic'
  },
  {
    value: 'creative',
    label: 'Creative Theme',
    description: 'Creative and artistic design style',
    icon: <Brush className="w-5 h-5" />,
    className: 'theme-creative',
    category: 'basic'
  },
  {
    value: 'finance',
    label: 'Finance Theme',
    description: 'Professional and stable business style',
    icon: <Briefcase className="w-5 h-5" />,
    className: 'theme-finance',
    category: 'basic'
  },
  {
    value: 'education',
    label: 'Education Theme',
    description: 'Fresh and friendly educational style',
    icon: <GraduationCap className="w-5 h-5" />,
    className: 'theme-education',
    category: 'basic'
  },
  {
    value: 'gradient-purple-blue',
    label: 'Purple Blue Gradient',
    description: 'Elegant purple to blue gradient',
    icon: <Sparkles className="w-5 h-5" />,
    className: 'theme-gradient-purple-blue',
    category: 'gradient'
  },
  {
    value: 'gradient-cyan-blue',
    label: 'Cyan Blue Gradient',
    description: 'Fresh cyan to blue gradient',
    icon: <Sparkles className="w-5 h-5" />,
    className: 'theme-gradient-cyan-blue',
    category: 'gradient'
  },
  {
    value: 'gradient-green-blue',
    label: 'Green Blue Gradient',
    description: 'Natural green to blue gradient',
    icon: <Sparkles className="w-5 h-5" />,
    className: 'theme-gradient-green-blue',
    category: 'gradient'
  },
  {
    value: 'gradient-purple-pink',
    label: 'Purple Pink Gradient',
    description: 'Romantic purple to pink gradient',
    icon: <Sparkles className="w-5 h-5" />,
    className: 'theme-gradient-purple-pink',
    category: 'gradient'
  },
  {
    value: 'gradient-pink-orange',
    label: 'Pink Orange Gradient',
    description: 'Warm pink to orange gradient',
    icon: <Sparkles className="w-5 h-5" />,
    className: 'theme-gradient-pink-orange',
    category: 'gradient'
  },
  {
    value: 'gradient-teal-lime',
    label: 'Teal Lime Gradient',
    description: 'Vibrant teal to lime gradient',
    icon: <Sparkles className="w-5 h-5" />,
    className: 'theme-gradient-teal-lime',
    category: 'gradient'
  },
  {
    value: 'gradient-red-yellow',
    label: 'Red Yellow Gradient',
    description: 'Energetic red to yellow gradient',
    icon: <Sparkles className="w-5 h-5" />,
    className: 'theme-gradient-red-yellow',
    category: 'gradient'
  }
]

export function AppearanceForm() {
  const params = useParams();
  const siteId = params?.siteId as string;
  const queryClient = useQueryClient();
  const [mounted, setMounted] = useState(false);

  // Get website data
  const { data: website, isLoading } = useQuery({
    queryKey: brandKeys.website(siteId),
    queryFn: () => websiteService.getWebsite(siteId),
    enabled: Boolean(siteId),
    staleTime: 0,
  });

  // Update visual settings
  const { mutate: updateVisualSettings, isPending: isUpdating } = useMutation({
    mutationFn: (data: VisualFormValues) => {
      const currentConfig = website?.configuration || {};
      return websiteService.updateWebsiteConfig(siteId, {
        configuration: {
          ...currentConfig,
          pageWidth: data.pageWidth,
          colorMode: data.colorMode,
          theme: data.theme
        }
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: brandKeys.website(siteId) });
      toast({
        title: "Visual settings updated",
        description: "Your website visual settings have been updated successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Update failed",
        description: "Failed to update visual settings. Please try again.",
        variant: "destructive",
      });
    }
  });

  const form = useForm<VisualFormValues>({
    resolver: zodResolver(visualFormSchema),
    defaultValues: {
      pageWidth: 'normal',
      colorMode: 'light',
      theme: 'default'
    }
  });

  // Client-side mount flag
  useEffect(() => {
    setMounted(true);
  }, []);

  // Update form values when website data is loaded
  useEffect(() => {
    if (website?.configuration) {
      const config = website.configuration;
      form.setValue('pageWidth', config.pageWidth || 'normal');
      form.setValue('colorMode', config.colorMode || 'light');
      form.setValue('theme', config.theme || 'default');
    }
  }, [website?.configuration, form]);

  // Apply visual settings to the page
  const applyVisualSettings = (data: VisualFormValues) => {
    if (!mounted) return;

    // Apply page width settings
    document.documentElement.classList.toggle('page-width-wide', data.pageWidth === 'wide');
    document.documentElement.classList.toggle('page-width-full', data.pageWidth === 'full');

    // Apply color mode
    if (data.colorMode === 'system') {
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      document.documentElement.classList.toggle('dark', prefersDark);
    } else {
      document.documentElement.classList.toggle('dark', data.colorMode === 'dark');
    }

    // Apply theme settings
    const allThemeClasses = themeOptions
      .filter(option => option.className)
      .map(option => option.className);
    
    document.documentElement.classList.remove(...allThemeClasses);
    
    const selectedTheme = themeOptions.find(option => option.value === data.theme);
    if (selectedTheme?.className) {
      document.documentElement.classList.add(selectedTheme.className);
    }
  };

  // Watch form changes for real-time preview
  useEffect(() => {
    const subscription = form.watch((data) => {
      if (data.pageWidth && data.colorMode && data.theme) {
        applyVisualSettings(data as VisualFormValues);
      }
    });
    return () => subscription.unsubscribe();
  }, [form, mounted]);

  const onSubmit = async (data: VisualFormValues) => {
    try {
      await updateVisualSettings(data);
    } catch (error) {
      console.error('Failed to update visual settings:', error);
    }
  };

  if (isLoading) {
    return <div className="flex items-center justify-center p-4">Loading...</div>;
  }

  // Group basic and gradient themes
  const basicThemes = themeOptions.filter(theme => theme.category === 'basic');
  const gradientThemes = themeOptions.filter(theme => theme.category === 'gradient');

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        {/* Page Width Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Monitor className="w-5 h-5" />
              Page Width Settings
            </CardTitle>
            <CardDescription>
              Choose the maximum width for page content, affecting overall layout and reading experience
            </CardDescription>
          </CardHeader>
          <CardContent>
            <FormField
              control={form.control}
              name="pageWidth"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="grid grid-cols-1 md:grid-cols-3 gap-4"
                    >
                      {pageWidthOptions.map((option) => (
                        <div key={option.value} className="flex items-center space-x-2">
                          <RadioGroupItem value={option.value} id={`pageWidth-${option.value}`} />
                          <Label 
                            htmlFor={`pageWidth-${option.value}`}
                            className="flex-1 cursor-pointer"
                          >
                            <div className="flex items-start gap-3 p-3 rounded-lg border hover:bg-accent/50 transition-colors">
                              <div className="mt-0.5">
                                {option.icon}
                              </div>
                              <div>
                                <div className="font-medium">{option.label}</div>
                                <div className="text-sm text-muted-foreground">{option.description}</div>
                              </div>
                            </div>
                          </Label>
                        </div>
                      ))}
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Color Mode Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Sun className="w-5 h-5" />
              Color Mode
            </CardTitle>
            <CardDescription>
              Choose the color scheme for your website, affecting the overall visual style
            </CardDescription>
          </CardHeader>
          <CardContent>
            <FormField
              control={form.control}
              name="colorMode"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="grid grid-cols-1 md:grid-cols-3 gap-4"
                    >
                      {colorModeOptions.map((option) => (
                        <div key={option.value} className="flex items-center space-x-2">
                          <RadioGroupItem value={option.value} id={`colorMode-${option.value}`} />
                          <Label 
                            htmlFor={`colorMode-${option.value}`}
                            className="flex-1 cursor-pointer"
                          >
                            <div className="flex items-start gap-3 p-3 rounded-lg border hover:bg-accent/50 transition-colors">
                              <div className="mt-0.5">
                                {option.icon}
                              </div>
                              <div>
                                <div className="font-medium">{option.label}</div>
                                <div className="text-sm text-muted-foreground">{option.description}</div>
                              </div>
                            </div>
                          </Label>
                        </div>
                      ))}
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Website Theme Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Palette className="w-5 h-5" />
              Website Theme
            </CardTitle>
            <CardDescription>
              Choose the overall design theme for your website, including button styles and color schemes
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <FormField
              control={form.control}
              name="theme"
              render={({ field }) => (
                <FormItem className="space-y-4">
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="space-y-6"
                    >
                      {/* Basic Themes */}
                      <div>
                        <h4 className="text-sm font-medium mb-3">Basic Themes</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                          {basicThemes.map((theme) => (
                            <div key={theme.value} className="flex items-center space-x-2">
                              <RadioGroupItem value={theme.value} id={`theme-${theme.value}`} />
                              <Label 
                                htmlFor={`theme-${theme.value}`}
                                className="flex-1 cursor-pointer"
                              >
                                <div className="flex items-start gap-3 p-3 rounded-lg border hover:bg-accent/50 transition-colors">
                                  <div className="mt-0.5">
                                    {theme.icon}
                                  </div>
                                  <div>
                                    <div className="font-medium">{theme.label}</div>
                                    <div className="text-sm text-muted-foreground">{theme.description}</div>
                                  </div>
                                </div>
                              </Label>
                            </div>
                          ))}
                        </div>
                      </div>

                      <Separator />

                      {/* Gradient Themes */}
                      <div>
                        <h4 className="text-sm font-medium mb-3">Gradient Themes</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                          {gradientThemes.map((theme) => (
                            <div key={theme.value} className="flex items-center space-x-2">
                              <RadioGroupItem value={theme.value} id={`theme-${theme.value}`} />
                              <Label 
                                htmlFor={`theme-${theme.value}`}
                                className="flex-1 cursor-pointer"
                              >
                                <div className="flex items-start gap-3 p-3 rounded-lg border hover:bg-accent/50 transition-colors">
                                  <div className="mt-0.5">
                                    {theme.icon}
                                  </div>
                                  <div>
                                    <div className="font-medium">{theme.label}</div>
                                    <div className="text-sm text-muted-foreground">{theme.description}</div>
                                  </div>
                                </div>
                              </Label>
                            </div>
                          ))}
                        </div>
                      </div>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Submit Button */}
        <div className="flex justify-end">
          <Button type="submit" disabled={isUpdating}>
            {isUpdating ? "Saving..." : "Save Settings"}
          </Button>
        </div>
      </form>
    </Form>
  )
}
