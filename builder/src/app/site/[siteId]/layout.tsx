import { getQueryClient } from "@/lib/getQueryClient"
import { Hydrate } from "@/components/hydrate"
import { websiteKeys } from "@/lib/api/queryKeys"
import websiteService from "@/modules/website/service"
import { dehydrate } from "@tanstack/react-query"
import { AppSidebar } from "../components/app-sidebar"
import { SidebarInset } from "@/components/ui/sidebar"
import { cn } from "@/lib/utils"

/**
 * Site layout component with server-side data prefetching
 */
export default async function Layout({
  children,
  params,
}: {
  children: React.ReactNode
  params: { siteId: string }
}) {
  const { siteId } = params
  const queryClient = getQueryClient()
  
  // Check if current path is page editor
  const isPageEditor = params.siteId && params.siteId.includes('page')

  // Prefetch website list data on the server
  await queryClient.prefetchQuery({
    queryKey: websiteKeys.lists(),
    queryFn: websiteService.getWebsites,
    staleTime: 60 * 1000, // Data is considered fresh for 1 minute
  })

  // Prefetch specific website data on the server
  await queryClient.prefetchQuery({
    queryKey: websiteKeys.detail(siteId),
    queryFn: () => websiteService.getWebsite(siteId),
    staleTime: 60 * 1000,
  })

  // Dehydrate the query cache and pass it to the Hydrate component
  const dehydratedState = dehydrate(queryClient)

  return (
    <Hydrate state={dehydratedState}>
      <div className="grid h-screen w-full">
        <div className="flex h-full w-full">
          <AppSidebar />
          <SidebarInset className={cn(
            "flex-1 w-0 min-w-0 overflow-hidden bg-background",
          )}>
            <div className={cn(
              "h-full w-full",
              !isPageEditor && "bg-card rounded-lg shadow-sm"
            )}>
              {children}
            </div>
          </SidebarInset>
        </div>
      </div>
    </Hydrate>
  )
}
