"use client"

import * as React from "react"
import {
  BookOpen,
  Bot,
  Command,
  Frame,
  LifeBuoy,
  Map,
  PieChart,
  Send,
  Settings,
  LayoutDashboard,
  AudioWaveform,
  GalleryVerticalEnd,
  Languages,
} from "lucide-react"

import { NavMain } from "./nav-main"
// import { NavProjects } from "./nav-projects"
import { NavSecondary } from "./nav-secondary"
import { NavBrand } from "./nav-brand"
import { TeamSwitcher } from "./team-switcher"
import { UserNav } from "@/components/user/UserNav"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  // SidebarMenu,
  // SidebarMenuButton,
  // SidebarMenuItem,
} from "@/components/ui/sidebar"
import { useParams } from "next/navigation"
import { useQuery } from "@tanstack/react-query"
import { useUserProfile } from "@/lib/hooks/use-user-profile"
import { sidebarKeys } from '@/lib/api/queryKeys'

// 不包含用户数据的侧边栏数据
async function fetchSidebarData(siteId: string) {
  return {
    teams: [
      {
        name: "Acme Inc",
        logo: GalleryVerticalEnd,
        plan: "Enterprise",
      },
      {
        name: "Acme Corp.",
        logo: AudioWaveform,
        plan: "Startup",
      },
      {
        name: "Evil Corp.",
        logo: Command,
        plan: "Free",
      },
    ],
    navMain: [
      {
        title: "Pages",
        url: "#",
        icon: LayoutDashboard,
        isActive: true,
        items: [
          {
            title: "Published",
            url: "#",
          },
          {
            title: "Unpublished",
            url: "#",
          },
          {
            title: "Settings",
            url: "#",
          },
        ],
      },
      {
        title: "Settings",
        url: "#",
        icon: Settings,
        isActive: true,
        items: [
          {
            title: "General",
            url: `/site/${siteId}/settings/general`,
          },
          {
            title: "Appearance",
            url: `/site/${siteId}/settings/appearance`,
          },
          {
            title: "SEO",
            url: `/site/${siteId}/seo/overview`,
          },
          // {
          //   title: "Account",
          //   url: `/site/${siteId}/settings/account`,
          // },
          // {
          //   title: "Display",
          //   url: `/site/${siteId}/settings/display`,
          // },
        ],
      },
    ],
    navSecondary: [
      {
        title: "Help",
        url: "#",
        icon: LifeBuoy,
      },
      {
        title: "Documentation",
        url: "#",
        icon: BookOpen,
      },
      {
        title: "Chat",
        url: "#",
        icon: Bot,
      },
      {
        title: "Feedback",
        url: "#",
        icon: Send,
      },
      {
        title: "Analytics",
        url: "#",
        icon: PieChart,
      },
      {
        title: "Roadmap",
        url: "#",
        icon: Map,
      },
    ],
  };
}



export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {

  const { siteId } = useParams<{ siteId: string }>() || { siteId: '' }
  const { user } = useUserProfile(); // 使用新的 useUserProfile hook

  const { data, isLoading, error } = useQuery({
    queryKey: sidebarKeys.data(siteId),
    queryFn: () => fetchSidebarData(siteId),
    enabled: !!siteId,
    staleTime: 5 * 60 * 1000,
  });

  // 准备传递给 NavUser 的用户数据，使用 hook 返回的数据或默认值
  const userData = user || {
    id: '',
    name: "Guest User",
    email: "<EMAIL>",
    avatar: "",
    isSubscribed: false,
    subscriptionLevel: 'BASIC',
    subscriptionStatus: 'INACTIVE'
  };

  return (
    <Sidebar variant="inset" {...props}>
      <SidebarHeader>
        <TeamSwitcher />
      </SidebarHeader>
      <SidebarContent>
        {
          isLoading || !data ? <div>Loading sidebar...</div> : <>
            <NavMain items={data.navMain} />
            {/* <NavBrand /> */}
            <NavSecondary items={data.navSecondary} className="mt-auto" />
          </>
        }

      </SidebarContent>
      <SidebarFooter>
        <UserNav user={userData} />
      </SidebarFooter>
    </Sidebar>
  )
}
