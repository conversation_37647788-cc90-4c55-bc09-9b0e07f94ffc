"use client"

import { ChevronRight, type LucideIcon } from "lucide-react"
import { NavPages } from "./nav-pages"
import { usePathname } from "next/navigation"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar"
// import { cn } from "@/lib/utils"
import Link from "next/link"

export function NavMain({
  items,
}: {
  items: {
    title: string
    url: string
    icon?: LucideIcon
    isActive?: boolean
    items?: {
      title: string
      url: string
    }[]
  }[]
}) {
  const pathname = usePathname();

  return (
    <SidebarGroup>
      <SidebarGroupLabel>Website</SidebarGroupLabel>
      <SidebarMenu>
        {/* Pages Navigation */}
        <NavPages />

        {/* Other Navigation Items */}
        {items.map((item) => {
          // 检查当前路径是否匹配设置页面
          const isSettingsActive = item.title === "Settings" && pathname?.includes("/settings/");

          return item.title !== "Pages" && (
            <Collapsible
              key={item.title}
              asChild
              defaultOpen={item.isActive || isSettingsActive}
              className="group/collapsible"
            >
              <SidebarMenuItem>
                <CollapsibleTrigger asChild>
                  <SidebarMenuButton tooltip={item.title}>
                    <ChevronRight className="transition-transform group-data-[state=open]/collapsible:rotate-90" />
                    {item.icon && <item.icon />}
                    <span>{item.title}</span>
                  </SidebarMenuButton>
                </CollapsibleTrigger>
                {item.items && (
                  <CollapsibleContent asChild>
                    <SidebarMenuSub>
                      {item.items.map((subItem) => {
                        // 精确匹配当前路径，避免多个设置页面同时高亮
                        const isActive = pathname === subItem.url;

                        return (
                          <SidebarMenuSubItem key={subItem.title}>
                            <SidebarMenuSubButton
                              asChild
                              isActive={isActive}
                            >
                              <Link href={subItem.url}>
                                <span>{subItem.title}</span>
                              </Link>
                            </SidebarMenuSubButton>
                          </SidebarMenuSubItem>
                        );
                      })}
                    </SidebarMenuSub>
                  </CollapsibleContent>
                )}
              </SidebarMenuItem>
            </Collapsible>
          );
        })}
      </SidebarMenu>
    </SidebarGroup>
  )
}
