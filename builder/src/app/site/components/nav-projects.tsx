"use client"

// import Link from 'next/link'
// import { useParams } from 'next/navigation'
// import { useWebsiteStore } from '@/modules/website/store'
// import { cn } from '@/lib/utils'
// import {
//   SidebarNav,
//   SidebarNavHeader,
//   SidebarNavHeaderTitle,
//   SidebarNavList,
//   // SidebarNavLink,
// } from '@/components/ui/sidebar'

export function NavProjects() {
  // const { siteId } = useParams<{ siteId: string }>() || { siteId: '' }
  // const websites = useWebsiteStore((state) => state.websites)

  return (
    <></>
    // <SidebarNav>
    //   <SidebarNavHeader className="flex items-center justify-between">
    //     <SidebarNavHeaderTitle>Projects</SidebarNavHeaderTitle>
    //   </SidebarNavHeader>
    //   <SidebarNavList>
    //     {websites?.map((site) => (
    //       <Link
    //         key={site.id}
    //         href={`/site/${site.id}`}
    //         className={cn(
    //           'flex w-full items-center rounded-md border border-transparent px-2 py-1',
    //           site.id === siteId
    //             ? 'bg-accent text-accent-foreground'
    //             : 'hover:bg-muted/50'
    //         )}
    //       >
    //         <div className="flex w-full flex-col">
    //           <span className="text-xs font-medium leading-none">
    //             {site.name}
    //           </span>
    //           <span className="text-xs text-muted-foreground">
    //             {site.domain}.lit.page
    //           </span>
    //         </div>
    //       </Link>
    //     ))}
    //   </SidebarNavList>
    // </SidebarNav>
  )
}
