"use client"

import { ChevronRight, FileText, Newspaper, CircleHelp, UserRound, BadgeDollarSign, Sparkles, Home, FileCheck, Plus, Languages, LayoutDashboard } from "lucide-react"
import { useWebsitePages } from '@/hooks/useWebsitePages';
import { useWebsite } from '@/hooks/useWebsite';
import { useParams, usePathname } from 'next/navigation';
import { cn } from "@/lib/utils"
import React from 'react'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import {
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarMenuSub,
  SidebarMenuSubItem,
  SidebarMenuSubButton,
} from "@/components/ui/sidebar"
import Link from "next/link"
import { LucideIcon } from "lucide-react"
import { useMemo } from 'react';

// 页面类型图标映射
const pageTypeIcons: Record<string, LucideIcon> = {
  LANDING: Home,
  BLOG: FileText,
  FAQ: CircleHelp,
  ABOUT: UserRound,
  PRICING: BadgeDollarSign,
  FEATURE: Sparkles,
  TERMS: FileCheck,
};

// 语言名称映射
const languageNames: Record<string, string> = {
  'EN': 'English',
  'CN': '简体中文',
  'ZH': '繁體中文',
  'ES': 'Español',
  'HI': 'हिन्दी',
  'FR': 'Français',
  'DE': 'Deutsch',
  'RU': 'Русский',
  'PT': 'Português',
  'AR': 'العربية',
  'JP': '日本語',
  'KR': '한국어',
  'IT': 'Italiano',
  'TR': 'Türkçe',
  'PL': 'Polski',
  'NL': 'Nederlands',
  'ID': 'Bahasa Indonesia',
  'TH': 'ไทย',
  'VI': 'Tiếng Việt',
  'SV': 'Svenska',
};

export const NavPages = React.memo(() => {
  // 获取路由参数，包括当前站点ID和页面ID
  const { siteId, pageId } = useParams<{ siteId: string, pageId: string }>() || { siteId: '', pageId: '' };

  // 获取当前路径
  const pathname = usePathname();

  // 使用 useWebsite hook 获取当前网站数据
  const { data: currentWebsite, isLoading: isLoadingWebsite } = useWebsite(siteId);

  // 使用 useWebsitePages 获取页面数据
  const { pages, isLoading: isLoadingPages } = useWebsitePages(siteId);

  // 合并加载状态
  const isLoading = isLoadingWebsite || isLoadingPages;

  // 检查当前是否在设置页面
  const isSettingsPage = pathname?.includes('/settings/');

  // 使用 useMemo 缓存按语言分组的页面
  const pagesByLanguage = useMemo(() => {
    if (!pages) return {};

    // 按语言分组页面
    const groupedPages: Record<string, typeof pages> = {};

    pages.forEach(page => {
      const language = page.language || 'EN'; // 默认为英语
      if (!groupedPages[language]) {
        groupedPages[language] = [];
      }
      groupedPages[language].push(page);
    });

    // 对每个语言组内的页面按 slug 长度排序
    Object.keys(groupedPages).forEach(language => {
      groupedPages[language].sort((a, b) => {
        const slugA = a.slug || '';
        const slugB = b.slug || '';
        // 首先按 slug 长度排序（短的在前）
        if (slugA.length !== slugB.length) {
          return slugA.length - slugB.length;
        }
        // 如果长度相同，则按字母顺序排序
        return slugA.localeCompare(slugB);
      });
    });

    return groupedPages;
  }, [pages]);

  // 渲染单个页面项
  const renderPageItem = (page: any, websiteId: string) => {
    const PageTypeIcon = pageTypeIcons[page.pageType ?? ''] || FileText;

    // 获取页面语言和网站默认语言
    const pageLanguage = page.language || 'EN';
    const defaultLanguage = currentWebsite?.defaultLanguage || 'EN';

    // 构建显示的 slug
    let displaySlug = page.slug || 'untitled';

    // 非默认语言需要添加语言前缀
    if (pageLanguage !== defaultLanguage) {
      // 确保 slug 以 / 开头
      const slugWithLeadingSlash = displaySlug.startsWith('/') ? displaySlug : `/${displaySlug}`;
      // 添加语言前缀，例如 /cn/about
      displaySlug = `/${pageLanguage.toLowerCase()}${slugWithLeadingSlash}`;
    }

    // 处理超长 slug 的显示
    let formattedSlug = displaySlug;
    if (displaySlug.length > 25) {
      // 如果 slug 包含多个路径段，保留第一段和最后一段
      const segments = displaySlug.split('/').filter(Boolean);
      if (segments.length > 2) {
        formattedSlug = `/${segments[0]}/../${segments[segments.length - 1]}`;
      } else {
        // 如果只有一个或两个段，截断中间部分
        const start = displaySlug.substring(0, 10);
        const end = displaySlug.substring(displaySlug.length - 10);
        formattedSlug = `${start}...${end}`;
      }
    }

    // 检查是否为当前页面
    const isCurrentPage = page.id === pageId;

    // 构建页面链接URL
    const pageUrl = `/site/${websiteId}/page/${page.id}`;

    return (
      <SidebarMenuSubItem key={page.id}>
        {isCurrentPage ? (
          // 当前页面 - 使用按钮而不是链接，防止重复点击
          <SidebarMenuSubButton
            isActive={true}
            className={cn(
              "group/item relative",
              page.status === 'INITIALIZED' && "text-muted-foreground/70"
            )}
            onClick={(e) => {
              // 阻止当前页面的导航，防止重复点击
              e.preventDefault();
            }}
          >
            <div className="flex items-center w-full">
              <PageTypeIcon className="mr-2 h-4 w-4" />
              <span className="truncate" title={displaySlug}>{formattedSlug}</span>
            </div>
            {page.status === 'INITIALIZED' && (
              <div className="absolute inset-y-0 right-0 hidden items-center pr-2 group-hover/item:flex">
                <Plus className="h-3 w-3 text-muted-foreground" />
              </div>
            )}
          </SidebarMenuSubButton>
        ) : (
          // 非当前页面 - 使用链接
          <Link href={pageUrl} passHref legacyBehavior>
            <SidebarMenuSubButton
              className={cn(
                "group/item relative",
                "transition-colors duration-150",
                page.status === 'INITIALIZED' && "text-muted-foreground/70 hover:text-muted-foreground"
              )}
            >
              <div className="flex items-center w-full">
                <PageTypeIcon className="mr-2 h-4 w-4" />
                <span className="truncate" title={displaySlug}>{formattedSlug}</span>
              </div>
              {page.status === 'INITIALIZED' && (
                <div className="absolute inset-y-0 right-0 hidden items-center pr-2 group-hover/item:flex">
                  <Plus className="h-3 w-3 text-muted-foreground" />
                </div>
              )}
            </SidebarMenuSubButton>
          </Link>
        )}
      </SidebarMenuSubItem>
    );
  };

  if (!siteId || isLoading) {
    return null;
  }

  // 获取语言列表并排序（英语优先，其他按字母顺序）
  const languages = Object.keys(pagesByLanguage).sort((a, b) => {
    if (a === 'EN') return -1;
    if (b === 'EN') return 1;
    return a.localeCompare(b);
  });

  // 检查是否只有默认语言
  const defaultLanguage = currentWebsite?.defaultLanguage || 'EN';
  const hasOnlyDefaultLanguage = languages.length === 1 && languages[0] === defaultLanguage;

  // 判断是否应该默认展开 Pages 菜单
  // 当在设置页面、有当前页面、页面数量少时自动展开
  const shouldOpenPagesMenu = isSettingsPage || !!pageId || pages?.length === 0 || pages?.length === 1;

  return (
    <Collapsible asChild defaultOpen={shouldOpenPagesMenu} className="group/collapsible">
      <SidebarMenuItem>
        <CollapsibleTrigger asChild>
          <SidebarMenuButton tooltip="Pages">
            <ChevronRight className="transition-transform group-data-[state=open]/collapsible:rotate-90" />
            <LayoutDashboard />
            <span>Pages</span>
          </SidebarMenuButton>
        </CollapsibleTrigger>
        <CollapsibleContent asChild>
          <SidebarMenuSub>
            {/* 按语言分组的页面列表 */}
            {languages.length > 0 ? (
              hasOnlyDefaultLanguage ? (
                // 只有默认语言时，直接显示页面列表，不显示语言分组
                pagesByLanguage[defaultLanguage].map(page => renderPageItem(page, siteId))
              ) : (
                // 多语言时，显示语言分组
                languages.map(language => {
                  // 检查当前语言组是否包含当前页面
                  const hasCurrentPage = !!pageId && pagesByLanguage[language].some(page => page.id === pageId);
                  // 如果是默认语言或包含当前页面，则默认展开
                  const shouldOpen = language === defaultLanguage || hasCurrentPage;

                  return (
                    <Collapsible key={language} asChild defaultOpen={shouldOpen} className="group/language-collapsible">
                      <SidebarMenuItem>
                        <CollapsibleTrigger asChild>
                          <SidebarMenuSubButton className="flex items-center w-full">
                            <ChevronRight className="h-4 w-4 transition-transform group-data-[state=open]/language-collapsible:rotate-90" />
                            <Languages className="ml-1 mr-2 h-4 w-4" />
                            <span>{languageNames[language] || language}</span>
                          </SidebarMenuSubButton>
                        </CollapsibleTrigger>
                        <CollapsibleContent>
                          <SidebarMenuSub>
                            {pagesByLanguage[language].map(page => renderPageItem(page, siteId))}
                          </SidebarMenuSub>
                        </CollapsibleContent>
                      </SidebarMenuItem>
                    </Collapsible>
                  );
                })
              )
            ) : (
              <div className="px-2 py-1.5 text-sm text-muted-foreground">No pages found</div>
            )}

            {/* 新建页面按钮 */}
            <SidebarMenuSubItem>
              <Link href={`/page/create/${siteId || ''}`} passHref legacyBehavior>
                <SidebarMenuSubButton className="group/item relative">
                  <div className="flex items-center w-full">
                    <Plus className="mr-2 h-4 w-4" />
                    <span className="truncate">New Page</span>
                  </div>
                </SidebarMenuSubButton>
              </Link>
            </SidebarMenuSubItem>
          </SidebarMenuSub>
        </CollapsibleContent>
      </SidebarMenuItem>
    </Collapsible>
  );
});

NavPages.displayName = 'NavPages';
