'use client'

import { ReactNode } from 'react'
import { useParams } from 'next/navigation'
import { SidebarTrigger } from '@/components/ui/sidebar'
import { Separator } from '@/components/ui/separator'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { useFetchWebsite } from '@/lib/hooks/use-website'
import { HomeIcon, LayoutIcon, FileIcon } from 'lucide-react'
import Logger from '@/lib/logger'

const logger = new Logger('SiteHeader')

interface SiteHeaderProps {
  /** Current page title */
  currentPage?: string
  /** Action buttons to display on the right */
  actions?: ReactNode
  /** Optional extra content to display after the domain */
  extra?: ReactNode
  /** Whether to show dashboard link (default: true) */
  showDashboard?: boolean
}

/**
 * Unified header component for site pages with breadcrumb navigation
 */
export function SiteHeader({ 
  currentPage,
  actions,
  extra,
  showDashboard = true
}: SiteHeaderProps) {
  const { siteId } = useParams<{ siteId?: string }>() || { siteId: '' }
  const { data: website } = useFetchWebsite(siteId)

  logger.debug('Rendering SiteHeader', { siteId, websiteName: website?.name })

  return (
    <header className="flex h-14 shrink-0 items-center justify-between gap-2 border-b bg-background px-4 w-full">
      <div className="flex items-center gap-2">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 h-4" />
        <Breadcrumb>
          <BreadcrumbList>
            {showDashboard ? (
              <BreadcrumbItem>
                <BreadcrumbLink href="/dashboard" className="flex items-center gap-1">
                  <HomeIcon className="h-3.5 w-3.5" />
                  <span>Dashboard</span>
                </BreadcrumbLink>
              </BreadcrumbItem>
            ) : (
              <BreadcrumbItem>
                <BreadcrumbLink href="/site" className="flex items-center gap-1">
                  <HomeIcon className="h-3.5 w-3.5" />
                  <span>Sites</span>
                </BreadcrumbLink>
              </BreadcrumbItem>
            )}
            {siteId && (
              <>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbLink href={`/site/${siteId}`} className="flex items-center gap-1">
                    <LayoutIcon className="h-3.5 w-3.5" />
                    <span>{website?.name || 'Site'}</span>
                  </BreadcrumbLink>
                </BreadcrumbItem>
              </>
            )}
            {currentPage && (
              <>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage className="flex items-center gap-1">
                    <FileIcon className="h-3.5 w-3.5" />
                    <span>{currentPage}</span>
                  </BreadcrumbPage>
                </BreadcrumbItem>
              </>
            )}
          </BreadcrumbList>
        </Breadcrumb>

        {extra && (
          <>
            <Separator orientation="vertical" className="mx-2 h-4" />
            {extra}
          </>
        )}
      </div>

      {actions && (
        <div className="flex items-center gap-2">
          {actions}
        </div>
      )}
    </header>
  )
}
