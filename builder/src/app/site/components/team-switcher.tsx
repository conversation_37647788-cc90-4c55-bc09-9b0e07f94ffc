"use client"

import * as React from "react"
import { useRouter, use<PERSON>ara<PERSON> } from "next/navigation"
import { useQueryClient, useMutation } from '@tanstack/react-query'
import { cn } from "@/lib/utils"
import { websiteKeys, pageKeys } from '@/lib/api/queryKeys'
import { useWebsite, useWebsites } from "@/hooks/useWebsite"
import { WebsiteModel } from '@/modules/website/model'
import Logger from '@/lib/logger'
import { Skeleton } from "@/components/ui/skeleton"
import { useCallback } from 'react'
import { Check, ChevronsUpDown, Plus, GalleryVerticalEnd, Loader2 } from "lucide-react"
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { 
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar"

const logger = new Logger('TeamSwitcher')

function LoadingItem() {
  return (
    <div className="flex items-center gap-2 px-2 py-1.5">
      <Skeleton className="h-6 w-6 rounded-md" />
      <div className="flex flex-col gap-1">
        <Skeleton className="h-3 w-20" />
        <Skeleton className="h-2 w-24" />
      </div>
    </div>
  )
}

export function TeamSwitcher() {
  const { isMobile } = useSidebar()
  const router = useRouter()
  const queryClient = useQueryClient()
  const params = useParams<{ siteId?: string }>()
  const siteId = params?.siteId || ''
  const [isNavigating, setIsNavigating] = React.useState(false)

  // Fetch all websites using useWebsites hook
  const { 
    data: websites = [], 
    error: websitesError, 
    isLoading: isLoadingWebsites 
  } = useWebsites();
  
  // Fetch current website using useWebsite hook
  const { 
    data: currentWebsite,
    error: currentWebsiteError,
    isLoading: isLoadingCurrentWebsite
  } = useWebsite(siteId);

  // Website switching mutation
  const switchWebsiteMutation = useMutation({
    mutationFn: async (site: WebsiteModel) => {
      logger.info('Switching to website', { from: currentWebsite?.id, to: site.id });
      return site;
    },
    onSuccess: async (site) => {
      // Update cache directly instead of invalidating queries
      queryClient.setQueryData(websiteKeys.detail(site.id || ''), site);
      
      // Only invalidate page list query if needed
      const existingPages = queryClient.getQueryData(pageKeys.list(site.id || ''));
      if (!existingPages) {
        queryClient.invalidateQueries({ queryKey: pageKeys.list(site.id || '') });
      }
    },
    onError: (error) => {
      logger.error('Error switching website', error);
    }
  });

  // Optimized website switching handler
  const handleSwitchWebsite = useCallback(async (site: WebsiteModel) => {
    // Skip if already navigating or selecting the current website
    if (isNavigating || site.id === currentWebsite?.id) {
      return;
    }
    
    try {
      setIsNavigating(true);
      
      // First navigate to the new site
      await router.push(`/site/${site.id}`);
      
      // Then trigger the mutation to update cache
      await switchWebsiteMutation.mutateAsync(site);
    } catch (error) {
      logger.error('Error switching website', error);
    } finally {
      setIsNavigating(false);
    }
  }, [currentWebsite?.id, router, switchWebsiteMutation, isNavigating]);

  // Log important state changes
  React.useEffect(() => {
    logger.info('Current state', { 
      siteId,
      currentWebsiteId: currentWebsite?.id,
      websitesCount: websites.length,
      isLoadingWebsites,
      isLoadingCurrentWebsite
    })
  }, [siteId, currentWebsite, websites, isLoadingWebsites, isLoadingCurrentWebsite])

  const isLoading = isLoadingWebsites || isLoadingCurrentWebsite;
  const error = websitesError || currentWebsiteError;

  return (
    <React.Fragment>
      <SidebarMenu>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton tooltip="Switch Site">
              <div className="flex items-center gap-2">
                {isLoading ? (
                  <Skeleton className="h-6 w-6 rounded-md" />
                ) : currentWebsite ? (
                  <>
                    <div className="flex aspect-square size-6 items-center justify-center rounded-md border">
                      {currentWebsite.name?.charAt(0) || <GalleryVerticalEnd className="h-4 w-4" />}
                    </div>
                    <div className="flex flex-col">
                      <span className="text-xs font-medium leading-none">
                        {currentWebsite.name}
                        {isNavigating && <Loader2 className="h-3 w-3 ml-1 inline animate-spin" />}
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {currentWebsite.domain}.lit.page
                      </span>
                    </div>
                  </>
                ) : (
                  <div className="flex flex-col">
                    <span className="text-xs font-medium leading-none">
                      Select a site
                      {isNavigating && <Loader2 className="h-3 w-3 ml-1 inline animate-spin" />}
                    </span>
                  </div>
                )}
              </div>
              <ChevronsUpDown className="ml-auto h-4 w-4" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-56"
            side={isMobile ? "bottom" : "right"}
            align={isMobile ? "end" : "start"}
          >
            <DropdownMenuLabel>My sites</DropdownMenuLabel>
            {isLoading ? (
              <>
                <LoadingItem />
                <LoadingItem />
                <LoadingItem />
              </>
            ) : error ? (
              <div className="text-destructive text-xs px-2 py-1">
                Failed to load sites
              </div>
            ) : websites.length === 0 ? (
              <DropdownMenuItem disabled className="text-muted-foreground">
                No sites found
              </DropdownMenuItem>
            ) : (
              websites.map((site) => (
                <DropdownMenuItem
                  key={site.id}
                  onClick={() => handleSwitchWebsite(site)}
                  disabled={isNavigating || site.id === currentWebsite?.id}
                  className={cn(
                    site.id === currentWebsite?.id && "bg-accent text-accent-foreground",
                    "justify-between"
                  )}
                >
                  <div className="flex items-center gap-2">
                    <div className="flex aspect-square size-6 items-center justify-center rounded-md border">
                      {site.name?.charAt(0) || <GalleryVerticalEnd className="h-4 w-4" />}
                    </div>
                    <div className="flex flex-col">
                      <span className="text-xs font-medium leading-none">
                        {site.name}
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {site.domain}.lit.page
                      </span>
                    </div>
                  </div>
                  {/* Show check mark for current website */}
                  {site.id === currentWebsite?.id && (
                    <Check className="h-4 w-4 ml-2" />
                  )}
                  {/* Show loading indicator when navigating */}
                  {isNavigating && site.id !== currentWebsite?.id && (
                    <Loader2 className="h-4 w-4 ml-2 animate-spin" />
                  )}
                </DropdownMenuItem>
              ))
            )}
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => router.push('/website/create')}>
              <Plus className="mr-2 h-4 w-4" />
              Create Site
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenu>
    </React.Fragment>
  )
}
