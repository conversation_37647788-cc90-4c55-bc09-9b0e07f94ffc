"use client"

import { useRouter } from "next/navigation"
import React from 'react'
import {
  ChevronsUpDown,
  LogOut,
  Sparkles,
  Zap,
} from "lucide-react"

import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar"
// import { toast } from "@/components/ui/use-toast"
import { useQueryClient } from "@tanstack/react-query"
import { useCallback, useMemo } from 'react'
import { UserProfile, useUserProfile } from "@/lib/hooks/use-user-profile"

export const NavUser = React.memo(({
  user,
}: {
  user: UserProfile
}) => {
  const { isMobile } = useSidebar()
  const router = useRouter()
  const queryClient = useQueryClient()
  const { logout } = useUserProfile() // 使用 hook 提供的登出功能

  // 提取用户名的首字母作为头像的 fallback
  const userInitials = user.name ? user.name.substring(0, 2).toUpperCase() : 'GU';

  const handleLogout = useCallback(async () => {
    try {
      await logout()
      
      // 清除所有查询缓存
      queryClient.clear()
      
      // 使用 replace 而不是 push，防止返回到需要认证的页面
      router.replace("/signin")
    } catch (error) {
      console.error("Logout failed:", error)
    }
  }, [logout, queryClient, router])

  const userMenu = useMemo(() => (
    <DropdownMenuContent
      className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
      side={isMobile ? "bottom" : "right"}
      align="end"
      sideOffset={4}
    >
      <DropdownMenuLabel className="p-0 font-normal">
        <div className="flex items-center gap-2 rounded-t-lg bg-accent/50 p-4">
          <Avatar className="h-8 w-8 rounded-lg">
            <AvatarImage src={user.avatar} alt={user.name} />
            <AvatarFallback className="rounded-lg">{userInitials}</AvatarFallback>
          </Avatar>
          <div className="grid flex-1 gap-0.5">
            <span className="text-sm font-medium">{user.name}</span>
            <span className="text-xs text-muted-foreground">{user.email}</span>
          </div>
        </div>
      </DropdownMenuLabel>
      <DropdownMenuGroup className="p-2">
        <DropdownMenuItem>
          <Sparkles className="mr-2 h-4 w-4" />
          <span>What&apos;s New</span>
        </DropdownMenuItem>
        <DropdownMenuItem className="opacity-70 cursor-not-allowed">
          <Zap className="mr-2 h-4 w-4" />
          <span>Upgrade to Pro</span>
        </DropdownMenuItem>
      </DropdownMenuGroup>
      <DropdownMenuSeparator />
      <DropdownMenuGroup className="p-2">
        <DropdownMenuItem onClick={handleLogout}>
          <LogOut className="mr-2 h-4 w-4" />
          <span>Logout</span>
        </DropdownMenuItem>
      </DropdownMenuGroup>
    </DropdownMenuContent>
  ), [user, isMobile, handleLogout, userInitials])

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <Avatar className="h-8 w-8 rounded-lg">
                <AvatarImage src={user.avatar} alt={user.name} />
                <AvatarFallback className="rounded-lg">{userInitials}</AvatarFallback>
              </Avatar>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-semibold">{user.name}</span>
                <span className="truncate text-xs">{user.email}</span>
              </div>
              <ChevronsUpDown className="ml-auto size-4" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          {userMenu}
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  )
});

NavUser.displayName = 'NavUser'
