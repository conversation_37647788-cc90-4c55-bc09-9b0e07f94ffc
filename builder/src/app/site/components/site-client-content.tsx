'use client'

import Link from 'next/link'
import { Card, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { useQuery } from '@tanstack/react-query'
import { OverviewHeader } from '@/app/site/components/overview-header'
import { SiteContent } from '@/app/site/components/site-content'
import websiteService from '@/modules/website/service'
// import workspaceService from '@/modules/workspace/service'
import { websiteKeys, workspaceKeys } from '@/lib/api/queryKeys'

export function SiteClientContent() {
  // 使用与服务器端相同的查询键
  const { data: websites = [] } = useQuery({
    queryKey: websiteKeys.all(),
    queryFn: websiteService.getWebsites,
    // 由于数据已经在服务器端预取，这个查询将立即从缓存中返回数据
  })

  // 使用与服务器端相同的查询键
  // const { data: workspaces = [] } = useQuery({
  //   queryKey: workspaceKeys.all(),
  //   queryFn: workspaceService.getWorkspaces,
  //   // 由于数据已经在服务器端预取，这个查询将立即从缓存中返回数据
  // })

  const firstSiteId = websites?.[0]?.id

  return (
    <div className="flex flex-col h-full w-full">
      <OverviewHeader />
      <SiteContent>
        <div className="space-y-4">
          <div className="grid auto-rows-min gap-4 md:grid-cols-3">
            <Link href={firstSiteId ? `/site/${firstSiteId}/brand/target-audience` : '#'}>
              <Card className="h-full hover:bg-accent/50 transition-colors">
                <CardHeader>
                  <CardTitle>Brand Strategy</CardTitle>
                  <CardDescription>Define your brand identity and target audience</CardDescription>
                </CardHeader>
              </Card>
            </Link>
            <Card className="h-full">
              <CardHeader>
                <CardTitle>Content Management</CardTitle>
                <CardDescription>Manage and organize your website content</CardDescription>
              </CardHeader>
            </Card>
            <Card className="h-full">
              <CardHeader>
                <CardTitle>Analytics</CardTitle>
                <CardDescription>Track and analyze your website performance</CardDescription>
              </CardHeader>
            </Card>
          </div>
          <div className="rounded-xl bg-muted/50 p-4">
            <h2 className="text-lg font-semibold mb-2">Recent Activity</h2>
            <p className="text-sm text-muted-foreground">No recent activity</p>
          </div>
        </div>
      </SiteContent>
    </div>
  )
}
