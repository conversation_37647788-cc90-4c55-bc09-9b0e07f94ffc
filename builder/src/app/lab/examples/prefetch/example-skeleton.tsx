import { Skeleton } from "@/components/ui/skeleton"

export function ExampleSkeleton() {
  return (
    <div className="space-y-6">
      <div className="bg-green-100 dark:bg-green-900 p-4 rounded-md mb-4">
        <Skeleton className="h-4 w-full max-w-md" />
      </div>

      <Skeleton className="h-6 w-48 mb-4" />
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {[1, 2, 3].map((i) => (
          <div key={i} className="border rounded-lg p-4 space-y-2">
            <Skeleton className="h-5 w-32" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
          </div>
        ))}
      </div>
    </div>
  )
}
