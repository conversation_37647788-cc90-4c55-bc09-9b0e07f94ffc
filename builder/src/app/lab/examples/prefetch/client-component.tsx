'use client'

import { useQuery } from '@tanstack/react-query'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import websiteService from '@/modules/website/service'
import { websiteKeys } from '@/lib/api/queryKeys'

export function ClientComponent() {
  // 使用与服务器端相同的查询键
  const { data: websites = [], isLoading } = useQuery({
    queryKey: websiteKeys.all(),
    queryFn: websiteService.getWebsites,
    // 由于数据已经在服务器端预取，这个查询将立即从缓存中返回数据
    // 除非数据已经过期（根据 staleTime 设置）
  })

  if (isLoading) {
    return <div>Loading websites...</div>
  }

  return (
    <div className="space-y-4">
      <div className="bg-green-100 dark:bg-green-900 p-4 rounded-md mb-4">
        <p className="text-green-800 dark:text-green-100">
          This data was prefetched on the server and hydrated on the client.
          Notice there was no loading state!
        </p>
      </div>

      <h2 className="text-xl font-semibold">Your Websites</h2>
      
      {websites.length === 0 ? (
        <p>No websites found.</p>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {websites.map((website) => (
            <Card key={website.id}>
              <CardHeader>
                <CardTitle>{website.name}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Domain: {website.domain || 'Not set'}
                </p>
                <p className="text-sm text-muted-foreground">
                  Created: {website.createdAt ? new Date(website.createdAt).toLocaleDateString() : 'Unknown'}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
