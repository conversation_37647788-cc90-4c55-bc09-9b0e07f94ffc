import { Suspense } from 'react'
import { dehydrate } from '@tanstack/react-query'
import { Hydrate } from '@/components/hydrate'
import { getQueryClient } from '@/lib/getQueryClient'
import { ClientComponent } from './client-component'
import { ExampleSkeleton } from './example-skeleton'
import { ErrorBoundary } from '@/components/error-boundary'
import websiteService from '@/modules/website/service'
import { websiteKeys } from '@/lib/api/queryKeys'

// This is a Server Component
export default async function PrefetchExample() {
  const queryClient = getQueryClient()
  
  // Prefetch data on the server
  await queryClient.prefetchQuery({
    queryKey: websiteKeys.all(),
    queryFn: websiteService.getWebsites,
    staleTime: 60 * 1000,
  })
  
  // Dehydrate the query cache and pass it to the Hydrate component
  const dehydratedState = dehydrate(queryClient)
  
  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-6">React Query Prefetch Example</h1>
      
      {/* Hydrate the client with the prefetched data */}
      <ErrorBoundary>
        <Hydrate state={dehydratedState}>
          <Suspense fallback={<ExampleSkeleton />}>
            <ClientComponent />
          </Suspense>
        </Hydrate>
      </ErrorBoundary>
    </div>
  )
}
