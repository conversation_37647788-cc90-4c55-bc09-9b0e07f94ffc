'use client';

import React, { useState, useRef } from 'react';
import dynamic from 'next/dynamic';
import { type EditorRef, type EditorMode } from '@/components/MilkdownEditor';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Save, Download, Upload, Eye, Code, Keyboard, RefreshCw } from 'lucide-react';

// 动态导入 MilkdownEditor 包装组件，避免 SSR 问题和 forwardRef 冲突
// 优化后的动态加载配置，包含更好的加载状态和错误处理
const MilkdownEditor = dynamic(
  () => import('@/components/MilkdownEditor/MilkdownEditorWrapper'),
  {
    ssr: false,
    loading: () => (
      <div className="border rounded-md min-h-[400px] flex items-center justify-center bg-background">
        <div className="text-center space-y-3">
          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary mx-auto"></div>
          <div className="space-y-1">
            <p className="text-sm font-medium">Loading Milkdown Editor</p>
            <p className="text-xs text-muted-foreground">Initializing rich text editor...</p>
          </div>
          <div className="flex items-center justify-center space-x-1">
            <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
            <div className="w-2 h-2 bg-primary rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></div>
            <div className="w-2 h-2 bg-primary rounded-full animate-pulse" style={{animationDelay: '0.4s'}}></div>
          </div>
        </div>
      </div>
    )
  }
);

export default function MilkdownEditorDemo() {
  const [content, setContent] = useState('');
  const [mode, setMode] = useState<EditorMode>('wysiwyg');
  const [savedContent, setSavedContent] = useState('');
  const [saveCount, setSaveCount] = useState(0);
  const [editorRef, setEditorRef] = useState<EditorRef | null>(null);

  const handleContentChange = (newContent: string) => {
    setContent(newContent);
  };

  const handleModeChange = (newMode: EditorMode) => {
    setMode(newMode);
  };

  const handleSave = (content: string) => {
    setSavedContent(content);
    setSaveCount(prev => prev + 1);
    console.log('Saved content:', content);
  };

  const handleGetContent = () => {
    const currentContent = editorRef?.getContent();
    if (currentContent) {
      alert(`Current content length: ${currentContent.length} characters`);
    }
  };

  const handleSetContent = () => {
    const newContent = `# New Content ${Date.now()}

This is new content set via the setContent method.

## Features

- ✅ WYSIWYG and Markdown mode switching
- ✅ Real-time content synchronization
- ✅ Keyboard shortcuts (Ctrl+M to toggle mode)
- ✅ Auto-save functionality
- ✅ Automatic theme adaptation

## Code Example

\`\`\`javascript
const editor = useRef();
editor.current?.setContent('New content');
\`\`\`

**Current time:** ${new Date().toLocaleString()}
`;
    editorRef?.setContent(newContent);
  };

  const handleFocus = () => {
    editorRef?.focus();
  };

  const handleToggleMode = () => {
    const newMode = mode === 'wysiwyg' ? 'markdown' : 'wysiwyg';
    editorRef?.setMode(newMode);
  };

  const handleDownload = () => {
    const currentContent = editorRef?.getContent() || content;
    const blob = new Blob([currentContent], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `milkdown-content-${Date.now()}.md`;
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Milkdown Editor Demo</h1>
        <p className="text-muted-foreground">
          Rich text editor with WYSIWYG and Markdown mode switching
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 编辑器区域 */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Editor</CardTitle>
                  <CardDescription>
                    Current mode: <Badge variant="outline">{mode === 'wysiwyg' ? 'Visual' : 'Source'}</Badge>
                  </CardDescription>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleToggleMode}
                  >
                    {mode === 'wysiwyg' ? <Code className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                    Toggle Mode
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleDownload}
                  >
                    <Download className="w-4 h-4" />
                    Download
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <MilkdownEditor
                onRef={setEditorRef}
                onChange={handleContentChange}
                onSave={handleSave}
                onModeChange={handleModeChange}
                placeholder="Start typing content..."
                showModeToggle={true}
                enableShortcuts={true}
                autoSave={true}
                autoSaveDelay={3000}
                className="w-full"
              />
            </CardContent>
          </Card>
        </div>

        {/* 控制面板 */}
        <div className="space-y-6">
          {/* 操作按钮 */}
          <Card>
            <CardHeader>
              <CardTitle>Actions</CardTitle>
              <CardDescription>Editor control functions</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button
                onClick={handleGetContent}
                className="w-full"
                variant="outline"
              >
                <Eye className="w-4 h-4 mr-2" />
                Get Content
              </Button>
              <Button
                onClick={handleSetContent}
                className="w-full"
                variant="outline"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Set New Content
              </Button>
              <Button
                onClick={handleFocus}
                className="w-full"
                variant="outline"
              >
                <Keyboard className="w-4 h-4 mr-2" />
                Focus Editor
              </Button>
            </CardContent>
          </Card>

          {/* 快捷键说明 */}
          <Card>
            <CardHeader>
              <CardTitle>Shortcuts</CardTitle>
              <CardDescription>Keyboard shortcuts</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm">Toggle Mode</span>
                <Badge variant="secondary">Ctrl+M</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Save</span>
                <Badge variant="secondary">Ctrl+S</Badge>
              </div>
            </CardContent>
          </Card>

          {/* 状态信息 */}
          <Card>
            <CardHeader>
              <CardTitle>Status</CardTitle>
              <CardDescription>Editor status information</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm">Content Length</span>
                <Badge variant="outline">{content.length}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Save Count</span>
                <Badge variant="outline">{saveCount}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Current Mode</span>
                <Badge variant={mode === 'wysiwyg' ? 'default' : 'secondary'}>
                  {mode === 'wysiwyg' ? 'Visual' : 'Source'}
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* 内容预览 */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Content Preview</CardTitle>
          <CardDescription>Real-time preview of current editor content</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="current" className="w-full">
            <TabsList>
              <TabsTrigger value="current">Current Content</TabsTrigger>
              <TabsTrigger value="saved">Saved Content</TabsTrigger>
            </TabsList>
            <TabsContent value="current" className="mt-4">
              <pre className="bg-muted p-4 rounded-lg overflow-auto max-h-60 text-sm">
                {content || '(No content)'}
              </pre>
            </TabsContent>
            <TabsContent value="saved" className="mt-4">
              <pre className="bg-muted p-4 rounded-lg overflow-auto max-h-60 text-sm">
                {savedContent || '(Not saved)'}
              </pre>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}