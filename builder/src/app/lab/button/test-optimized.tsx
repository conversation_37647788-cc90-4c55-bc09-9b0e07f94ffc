'use client';

import React from 'react';
import { ButtonV2 as <PERSON><PERSON> } from '@litpage/sections';
import { Download, Heart, Star, ArrowRight } from 'lucide-react';

export default function TestOptimizedButton() {
  return (
    <div className="p-8 space-y-8 bg-background">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8 text-center">
          优化后的 Button 组件测试
        </h1>
        
        {/* 基础变体测试 */}
        <section className="space-y-4">
          <h2 className="text-xl font-semibold">基础变体</h2>
          <div className="flex flex-wrap gap-4">
            <Button variant="primary">Primary</Button>
            <Button variant="secondary">Secondary</Button>
            <Button variant="outline">Outline</Button>
            <Button variant="ghost">Ghost</Button>
            <Button variant="destructive">Destructive</Button>
            <Button variant="link">Link</Button>
          </div>
        </section>

        {/* 渐变按钮测试 */}
        <section className="space-y-4">
          <h2 className="text-xl font-semibold">渐变按钮</h2>
          <div className="flex flex-wrap gap-4">
            <Button variant="gradient">Gradient</Button>
            <Button variant="gradient-blue">Blue</Button>
            <Button variant="gradient-green">Green</Button>
            <Button variant="gradient-cyan">Cyan</Button>
            <Button variant="gradient-purple">Purple</Button>
          </div>
        </section>

        {/* 尺寸测试 */}
        <section className="space-y-4">
          <h2 className="text-xl font-semibold">尺寸变体</h2>
          <div className="flex flex-wrap items-center gap-4">
            <Button size="small">Small</Button>
            <Button size="medium">Medium</Button>
            <Button size="large">Large</Button>
          </div>
        </section>

        {/* 图标按钮测试 */}
        <section className="space-y-4">
          <h2 className="text-xl font-semibold">图标按钮</h2>
          <div className="flex flex-wrap gap-4">
            <Button leftIcon={<Heart />}>With Left Icon</Button>
            <Button rightIcon={<ArrowRight />}>With Right Icon</Button>
            <Button leftIcon={<Download />} size="small">Small with Icon</Button>
            <Button leftIcon={<Star />} size="large">Large with Icon</Button>
          </div>
        </section>

        {/* 仅图标按钮测试 */}
        <section className="space-y-4">
          <h2 className="text-xl font-semibold">仅图标按钮</h2>
          <div className="flex flex-wrap gap-4">
            <Button size="icon">
              <Heart />
            </Button>
            <Button size="icon" variant="outline">
              <Star />
            </Button>
            <Button leftIcon={<Download />} iconOnly size="small" />
            <Button leftIcon={<ArrowRight />} iconOnly size="medium" />
            <Button leftIcon={<Heart />} iconOnly size="large" />
          </div>
        </section>

        {/* 状态测试 */}
        <section className="space-y-4">
          <h2 className="text-xl font-semibold">状态测试</h2>
          <div className="flex flex-wrap gap-4">
            <Button loading>Loading</Button>
            <Button loading loadingText="Processing...">Custom Loading</Button>
            <Button disabled>Disabled</Button>
            <Button pulse>Pulse Animation</Button>
          </div>
        </section>

        {/* 动画效果测试 */}
        <section className="space-y-4">
          <h2 className="text-xl font-semibold">动画效果</h2>
          <div className="flex flex-wrap gap-4">
            <Button 
              animation={{ hover: 'scale' }}
              variant="primary"
            >
              Hover Scale
            </Button>
            <Button 
              animation={{ hover: 'lift' }}
              variant="secondary"
            >
              Hover Lift
            </Button>
            <Button 
              animation={{ hover: 'glow' }}
              variant="outline"
            >
              Hover Glow
            </Button>
            <Button 
              animation={{ active: 'press' }}
              variant="ghost"
            >
              Active Press
            </Button>
            <Button 
              animation={{ attention: 'shine' }}
              variant="gradient-blue"
            >
              Shine Effect
            </Button>
          </div>
        </section>

        {/* 链接按钮测试 */}
        <section className="space-y-4">
          <h2 className="text-xl font-semibold">链接按钮</h2>
          <div className="flex flex-wrap gap-4">
            <Button href="#" variant="primary">Internal Link</Button>
            <Button href="https://example.com" external variant="outline">
              External Link
            </Button>
            <Button href="/download.pdf" download variant="secondary">
              Download File
            </Button>
          </div>
        </section>

        {/* 组合测试 */}
        <section className="space-y-4">
          <h2 className="text-xl font-semibold">复杂组合</h2>
          <div className="flex flex-wrap gap-4">
            <Button 
              variant="gradient-purple"
              size="large"
              leftIcon={<Star />}
              animation={{ hover: 'lift', active: 'press' }}
            >
              Premium Feature
            </Button>
            <Button 
              variant="outline"
              size="medium"
              rightIcon={<ArrowRight />}
              animation={{ hover: 'scale' }}
              href="#next-section"
            >
              Continue Reading
            </Button>
            <Button 
              variant="destructive"
              size="small"
              leftIcon={<Download />}
              loading
              loadingText="Deleting..."
            >
              Delete
            </Button>
          </div>
        </section>

        {/* 响应式测试 */}
        <section className="space-y-4">
          <h2 className="text-xl font-semibold">响应式测试</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            <Button variant="primary" className="w-full">
              Full Width on Mobile
            </Button>
            <Button variant="secondary" className="w-full">
              Responsive Button
            </Button>
            <Button variant="outline" className="w-full">
              Grid Layout Test
            </Button>
          </div>
        </section>
      </div>
    </div>
  );
} 