import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';
import { Slot } from '@radix-ui/react-slot';
import { Loader2 } from 'lucide-react';

const buttonVariants = cva(
  // 基础样式
  "inline-flex items-center justify-center whitespace-nowrap rounded-md font-medium transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-var(--button-disabled-opacity, 0.65) disabled:shadow-none disabled:cursor-not-allowed disabled:hover:bg-inherit disabled:hover:shadow-none disabled:hover:translate-y-0",
  {
    variants: {
      variant: {
        primary: "bg-primary !text-primary-foreground hover:bg-primary/90 hover:shadow-md active:opacity-var(--button-active-opacity, 0.8) focus:ring-4 focus:ring-primary/30 focus:outline-none transition-all",
        secondary: "bg-secondary !text-secondary-foreground hover:bg-secondary/90 hover:shadow-md active:opacity-var(--button-active-opacity, 0.8) focus:ring-4 focus:ring-secondary/30 focus:outline-none transition-all",
        outline: "border border-input bg-background !text-foreground hover:bg-accent hover:!text-accent-foreground hover:shadow-md focus:ring-4 focus:ring-accent/30 focus:outline-none transition-all",
        ghost: "!text-foreground hover:bg-accent hover:!text-accent-foreground focus:ring-4 focus:ring-accent/20 focus:outline-none transition-all",
        destructive: "bg-destructive !text-destructive-foreground hover:bg-destructive/90 hover:shadow-md active:opacity-var(--button-active-opacity, 0.8) focus:ring-4 focus:ring-destructive/30 focus:outline-none transition-all",
        link: "!text-primary underline-offset-4 hover:underline focus:ring-2 focus:ring-primary/20 focus:outline-none transition-all",
        gradient: "!text-white gradient-btn hover:shadow-lg hover:translate-y-[-1px] focus:ring-4 focus:ring-primary/30 focus:outline-none transition-all",
        'gradient-blue': "!text-white bg-gradient-to-r from-blue-500 via-blue-600 to-blue-700 hover:bg-gradient-to-br hover:shadow-lg hover:translate-y-[-1px] focus:ring-4 focus:ring-blue-300 dark:focus:ring-blue-800 transition-all",
        'gradient-green': "!text-white bg-gradient-to-r from-green-400 via-green-500 to-green-600 hover:bg-gradient-to-br hover:shadow-lg hover:translate-y-[-1px] focus:ring-4 focus:ring-green-300 dark:focus:ring-green-800 transition-all",
        'gradient-cyan': "!text-white bg-gradient-to-r from-cyan-400 via-cyan-500 to-cyan-600 hover:bg-gradient-to-br hover:shadow-lg hover:translate-y-[-1px] focus:ring-4 focus:ring-cyan-300 dark:focus:ring-cyan-800 transition-all",
        'gradient-teal': "!text-white bg-gradient-to-r from-teal-400 via-teal-500 to-teal-600 hover:bg-gradient-to-br hover:shadow-lg hover:translate-y-[-1px] focus:ring-4 focus:ring-teal-300 dark:focus:ring-teal-800 transition-all",
        'gradient-lime': "!text-white bg-gradient-to-r from-lime-200 via-lime-400 to-lime-500 hover:bg-gradient-to-br hover:shadow-lg hover:translate-y-[-1px] focus:ring-4 focus:ring-lime-300 dark:focus:ring-lime-800 transition-all",
        'gradient-red': "!text-white bg-gradient-to-r from-red-400 via-red-500 to-red-600 hover:bg-gradient-to-br hover:shadow-lg hover:translate-y-[-1px] focus:ring-4 focus:ring-red-300 dark:focus:ring-red-800 transition-all",
        'gradient-pink': "!text-white bg-gradient-to-r from-pink-400 via-pink-500 to-pink-600 hover:bg-gradient-to-br hover:shadow-lg hover:translate-y-[-1px] focus:ring-4 focus:ring-pink-300 dark:focus:ring-pink-800 transition-all",
        'gradient-purple': "!text-white bg-gradient-to-r from-purple-500 via-purple-600 to-purple-700 hover:bg-gradient-to-br hover:shadow-lg hover:translate-y-[-1px] focus:ring-4 focus:ring-purple-300 dark:focus:ring-purple-800 transition-all",
      },
      size: {
        small: "h-btn-small text-body-small px-btn-small-x",
        medium: "h-btn-medium text-body-base px-btn-medium-x",
        large: "h-btn-large text-body-large px-btn-large-x",
        icon: "size-btn-icon-only-medium aspect-square p-0 flex items-center justify-center", // Shadcn 风格的图标按钮尺寸
      },
      iconPosition: {
        left: "flex-row",
        right: "flex-row-reverse",
        none: "",
      },
      iconOnly: {
        true: "p-btn-icon-only",
        false: "",
      },
      pulse: {
        true: "animate-btn-pulse",
        false: "",
      },
    },
    compoundVariants: [
      // 图标按钮尺寸复合变体 - 与元素间距系统对齐
      {
        iconOnly: true,
        size: "small",
        className: "w-btn-icon-only-small size-btn-icon-only-small",
      },
      {
        iconOnly: true,
        size: "medium",
        className: "w-btn-icon-only-medium size-btn-icon-only-medium",
      },
      {
        iconOnly: true,
        size: "large",
        className: "w-btn-icon-only-large size-btn-icon-only-large",
      },
      // 当子元素中有图标时的间距和大小 - 与文本尺寸系统对齐
      {
        iconOnly: false,
        size: "small",
        className: "[&>svg]:size-btn-icon-small [&>svg]:inline-flex [&>svg+*]:ml-element-x [&>*+svg]:ml-element-x",
      },
      {
        iconOnly: false,
        size: "medium",
        className: "[&>svg]:size-btn-icon-medium [&>svg]:inline-flex [&>svg+*]:ml-element-x [&>*+svg]:ml-element-x",
      },
      {
        iconOnly: false,
        size: "large",
        className: "[&>svg]:size-btn-icon-large [&>svg]:inline-flex [&>svg+*]:ml-element-x [&>*+svg]:ml-element-x",
      },
      // 当使用 size="icon" 时的图标大小 - 与文本尺寸系统对齐
      {
        size: "icon",
        className: "[&>svg]:size-btn-icon-medium [&>svg]:inline-flex",
      }
    ],
    defaultVariants: {
      variant: "primary",
      size: "medium",
      iconPosition: "left",
      iconOnly: false,
      pulse: false,
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  loading?: boolean;
  loadingText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  href?: string;
  external?: boolean;
  download?: boolean | string;
  // 动画类型
  animation?: {
    hover?: 'scale' | 'lift' | 'glow' | 'none';
    active?: 'press' | 'sink' | 'none';
    attention?: 'shine' | 'border-pulse' | 'pulse' | 'none';
  };
  // 兼容 Shadcn 风格的仅图标按钮
  // 当 size="icon" 时，自动应用仅图标样式
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant,
      size,
      asChild = false,
      loading = false,
      loadingText,
      leftIcon,
      rightIcon,
      iconPosition,
      iconOnly,
      pulse = false,
      href,
      external = false,
      download,
      animation,
      children,
      ...props
    },
    ref
  ) => {
    // 处理 Shadcn 风格的图标按钮
    const isShadcnIconButton = size === 'icon';
    if (isShadcnIconButton) {
      iconOnly = true;
    }
    
    // 检测子元素中的图标
    const childrenArray = React.Children.toArray(children);
    const hasChildIcon = childrenArray.some(
      child => React.isValidElement(child) && typeof child.type !== 'string'
    );
    
    // 直接使用子元素模式
    const useDirectChildren = hasChildIcon && !leftIcon && !rightIcon && !isShadcnIconButton;
    
    // 设置图标位置默认值
    iconPosition = iconPosition || (leftIcon ? "left" : rightIcon ? "right" : "none");
    // 设置是否仅显示图标（如果未明确指定）
    iconOnly = iconOnly ?? (!!(leftIcon || rightIcon) && !children);
    // 如果同时提供了左右图标，优先使用与icon位置匹配的图标
    const icon = iconPosition === "left" ? leftIcon : rightIcon;
    // 设置图标大小类名
    let iconSize = "size-btn-icon-medium w-auto h-auto";
    if (size === "small") {
      iconSize = "size-btn-icon-small w-auto h-auto";
    } else if (size === "large") {
      iconSize = "size-btn-icon-large w-auto h-auto";
    }
    
    // 处理渐变按钮样式
    const getGradientStyle = () => {
      if (!variant || !variant.includes('gradient')) return {};
      
      // 在客户端才执行
      if (typeof window === 'undefined') {
        return {
          background: 'linear-gradient(to right, hsl(var(--primary)), hsl(var(--accent)))',
          color: 'white',
        };
      }
      
      // 获取当前渲染的按钮元素的计算样式
      // 这样可以获取到父元素上应用的主题样式
      // 为渐变按钮添加特殊类名，便于暗模式下样式覆盖
      const isGradientVariant = variant?.includes('gradient');
      const gradientClass = isGradientVariant ? 'gradient-btn' : '';
      
      return {
        backgroundImage: 'var(--button-gradient)',
        color: 'var(--button-text-color, white)',
        '&:hover': {
          backgroundImage: 'var(--button-gradient-hover)',
          color: 'var(--button-text-color-hover, white)',
        },
      };
    };
    
    // 渲染图标元素 - 传统方式
    const renderIcon = () => {
      // 如果是 Shadcn 风格的图标按钮，不在这里渲染图标，而是直接使用子元素
      if (isShadcnIconButton) {
        return null;
      }
      
      if (loading) {
        return <Loader2 className={`animate-btn-spinner ${iconSize} text-current`} />;
      }
      
      // 如果是仅图标按钮，使用 leftIcon 或 rightIcon
      if (iconOnly) {
        const iconToUse = leftIcon || rightIcon;
        if (iconToUse) {
          // 使用与按钮尺寸匹配的图标尺寸变量
          let iconSizeClass = 'size-btn-icon-medium';
          if (size === 'small') iconSizeClass = 'size-btn-icon-small';
          else if (size === 'large') iconSizeClass = 'size-btn-icon-large';
          
          // 获取CSS变量对应的实际尺寸值
          let iconSizeValue = '1rem';
          if (size === 'small') iconSizeValue = 'var(--button-small-icon-size, 0.875rem)';
          else if (size === 'large') iconSizeValue = 'var(--button-large-icon-size, 1.25rem)';
          else iconSizeValue = 'var(--button-medium-icon-size, 1rem)';
          
          return React.isValidElement(iconToUse)
            ? React.cloneElement(iconToUse as React.ReactElement, {
                className: cn(iconSizeClass, 'inline-flex', (iconToUse as React.ReactElement).props.className),
                style: { 
                  width: iconSizeValue, 
                  height: iconSizeValue, 
                  minWidth: iconSizeValue, 
                  minHeight: iconSizeValue,
                  aspectRatio: '1/1',
                  display: 'inline-flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                },
                width: "1em",
                height: "1em"
              })
            : iconToUse;
        }
      }
      
      // 常规按钮模式
      if (icon) {
        // 使用与按钮尺寸匹配的图标尺寸变量
        let iconSizeClass = 'size-btn-icon-medium';
        if (size === 'small') iconSizeClass = 'size-btn-icon-small';
        else if (size === 'large') iconSizeClass = 'size-btn-icon-large';
        
        // 获取CSS变量对应的实际尺寸值
        let iconSizeValue = '1rem';
        if (size === 'small') iconSizeValue = 'var(--button-small-icon-size, 0.875rem)';
        else if (size === 'large') iconSizeValue = 'var(--button-large-icon-size, 1.25rem)';
        else iconSizeValue = 'var(--button-medium-icon-size, 1rem)';
        
        return React.isValidElement(icon)
          ? React.cloneElement(icon as React.ReactElement, {
              className: cn(iconSizeClass, 'inline-flex', (icon as React.ReactElement).props.className),
              style: { 
                width: iconSizeValue, 
                height: iconSizeValue, 
                minWidth: iconSizeValue, 
                minHeight: iconSizeValue,
                aspectRatio: '1/1',
                display: 'inline-flex',
                alignItems: 'center',
                justifyContent: 'center'
              },
              width: "1em",
              height: "1em"
            })
          : icon;
      }
      return null;
    };

    // 处理动画类名
    const getAnimationClasses = () => {
      // 如果没有提供动画配置，返回空字符串
      if (!animation) return '';
      
      const classes = [];
      
      // 悬停动画 - 与全局动画系统对齐
      if (animation.hover === 'scale') classes.push('btn-hover-scale');
      else if (animation.hover === 'lift') classes.push('btn-hover-lift');
      else if (animation.hover === 'glow') classes.push('btn-hover-glow');
      
      // 点击动画 - 与全局动画系统对齐
      if (animation.active === 'press') classes.push('btn-active-press');
      else if (animation.active === 'sink') classes.push('btn-active-sink');
      
      // 注意力动画 - 与全局动画系统对齐
      if (animation.attention === 'shine') classes.push('btn-shine');
      else if (animation.attention === 'border-pulse') classes.push('btn-border-pulse');
      else if (animation.attention === 'pulse') classes.push('animate-btn-pulse');
      
      return classes.join(' ');
    };
    
    // 确保按钮有基础过渡效果
    const getBaseClasses = () => {
      const baseClasses = [];
      
      // 如果有动画配置，添加基础过渡类
      if (animation) {
        baseClasses.push('transition-all duration-var(--button-animation-duration, 200ms)');
      }
      
      return baseClasses.join(' ');
    };
    
    // 处理加载文本
    const renderChildren = () => {
      if (loading && loadingText) {
        return loadingText;
      }
      return children;
    };
    
    // 获取加载状态类名
    const getLoadingClasses = () => {
      if (!loading) return '';
      return '!opacity-[0.85] cursor-wait !shadow-none hover:!shadow-none hover:!translate-y-0 hover:!opacity-[0.85]';
    };
    
    // 获取加载或禁用状态的样式
    const getStateStyle = () => {
      if (loading) {
        return {
          opacity: 'var(--button-loading-opacity, 0.85)',
          cursor: 'wait',
          boxShadow: 'none',
          transform: 'none'
        } as React.CSSProperties;
      }
      
      if (props.disabled) {
        return {
          opacity: 'var(--button-disabled-opacity, 0.6)',
          cursor: 'not-allowed',
          boxShadow: 'none',
          // 不能使用 pointerEvents 因为类型错误
          // 我们已经在 CSS 中设置了 disabled:pointer-events-none
        } as React.CSSProperties;
      }
      
      return {} as React.CSSProperties;
    };

    // 处理链接按钮
    if (href) {
      const linkProps = {
        href,
        ...(external ? { target: "_blank", rel: "noopener noreferrer" } : {}),
        ...(download ? { download: download === true ? undefined : download } : {}),
        className: cn(
          buttonVariants({
            variant,
            size,
            iconPosition,
            iconOnly,
            pulse,
            className,
          }),
          getAnimationClasses(),
          getLoadingClasses(),
          variant?.includes('gradient') ? 'gradient-btn' : ''
        ),
        style: {
          ...(variant?.includes('gradient') ? getGradientStyle() : {}),
          ...getStateStyle()
        },
      };

      return (
        <a {...linkProps} className={cn(linkProps.className, getBaseClasses())}>
          {isShadcnIconButton ? (
            children // Shadcn 风格的图标按钮直接渲染子元素
          ) : useDirectChildren ? (
            // 直接渲染子元素，但添加空白处理
            <div className="inline-flex items-center gap-btn-icon">{children}</div>
          ) : iconOnly ? (
            // 仅图标按钮单独处理
            renderIcon()
          ) : (
            <>
              {renderIcon()}
              <span className={icon ? `mx-btn-icon` : ""}>{renderChildren()}</span>
            </>
          )}
        </a>
      );
    }

    const Comp = asChild ? Slot : "button";
    
    return (
      <Comp
        className={cn(
          buttonVariants({
            variant,
            size,
            iconPosition,
            iconOnly,
            pulse,
            className,
          }),
          getAnimationClasses(),
          getBaseClasses(),
          getLoadingClasses(),
          variant?.includes('gradient') ? 'gradient-btn' : ''
        )}
        ref={ref}
        disabled={loading || props.disabled}
        style={{
          ...(variant?.includes('gradient') ? getGradientStyle() : {}),
          ...getStateStyle()
        }}
        {...props}
      >
        {isShadcnIconButton ? (
          children // Shadcn 风格的图标按钮直接渲染子元素
        ) : useDirectChildren ? (
          // 直接渲染子元素，但添加空白处理
          <div className="inline-flex items-center gap-btn-icon">{children}</div>
        ) : iconOnly ? (
          // 仅图标按钮单独处理
          renderIcon()
        ) : (
          <>
            {renderIcon()}
            <span className={icon ? `mx-btn-icon` : ""}>{renderChildren()}</span>
          </>
        )}
      </Comp>
    );
  }
);

Button.displayName = "Button";

export { Button, buttonVariants };
