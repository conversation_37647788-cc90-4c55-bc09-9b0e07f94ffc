'use client';

import React, { useState, useEffect } from 'react';
import { ButtonV2 as Button, ButtonGroupV2 as ButtonGroup } from '@litpage/sections';
// import { Button as OriginalButton } from './Button';
import TestOptimizedButton from './test-optimized';
import { 
  Palette, 
  Sun, 
  Moon, 
  Laptop, 
  Check, 
  Plus, 
  Mail, 
  Download, 
  ExternalLink, 
  ChevronRight, 
  Trash, 
  Briefcase,
  Sparkles,
  ArrowRight,
  ArrowLeft,
  ShoppingCart,
  CreditCard,
  Search,
  Brush,
  GraduationCap,
  Code,
  Zap
} from 'lucide-react';

// 应用主题类型
type AppTheme = 'default' | 'tech' | 'creative' | 'finance' | 'education' | 
  'gradient-purple-blue' | 'gradient-cyan-blue' | 'gradient-green-blue' | 
  'gradient-purple-pink' | 'gradient-pink-orange' | 'gradient-teal-lime' | 'gradient-red-yellow';

// 应用主题信息映射
interface ThemeInfo {
  name: string;
  icon: React.ReactNode;
  className: string;
}

const appThemes: Record<AppTheme, ThemeInfo> = {
  default: {
    name: '默认',
    icon: <Palette size={18} />,
    className: ''
  },
  tech: {
    name: '科技',
    icon: <Laptop size={18} />,
    className: 'theme-tech'
  },
  creative: {
    name: '创意',
    icon: <Brush size={18} />,
    className: 'theme-creative'
  },
  finance: {
    name: '金融',
    icon: <Briefcase size={18} />,
    className: 'theme-finance'
  },
  education: {
    name: '教育',
    icon: <GraduationCap size={18} />,
    className: 'theme-education'
  },
  // 双色渐变主题
  'gradient-purple-blue': {
    name: '紫蓝渐变',
    icon: <Brush size={18} />,
    className: 'theme-gradient-purple-blue'
  },
  'gradient-cyan-blue': {
    name: '青蓝渐变',
    icon: <Brush size={18} />,
    className: 'theme-gradient-cyan-blue'
  },
  'gradient-green-blue': {
    name: '绿蓝渐变',
    icon: <Brush size={18} />,
    className: 'theme-gradient-green-blue'
  },
  'gradient-purple-pink': {
    name: '紫粉渐变',
    icon: <Brush size={18} />,
    className: 'theme-gradient-purple-pink'
  },
  'gradient-pink-orange': {
    name: '粉橙渐变',
    icon: <Brush size={18} />,
    className: 'theme-gradient-pink-orange'
  },
  'gradient-teal-lime': {
    name: '青柿渐变',
    icon: <Brush size={18} />,
    className: 'theme-gradient-teal-lime'
  },
  'gradient-red-yellow': {
    name: '红黄渐变',
    icon: <Brush size={18} />,
    className: 'theme-gradient-red-yellow'
  },
};

export default function ButtonTestPage() {
  const [colorMode, setColorMode] = useState<'light' | 'dark'>('light');
  const [appTheme, setAppTheme] = useState<AppTheme>('default');
  const [showOptimizedVersion, setShowOptimizedVersion] = useState<boolean>(false);

  // 初始化页面主题
  useEffect(() => {
    // 检查系统偏好
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    if (prefersDark) {
      setColorMode('dark');
      document.documentElement.classList.add('dark');
    }
  }, []);

  // 切换明暗模式
  const toggleColorMode = () => {
    const newColorMode = colorMode === 'light' ? 'dark' : 'light';
    setColorMode(newColorMode);
    
    if (newColorMode === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  };

  // 我们已经将渐变主题整合到应用主题中
  
  // 切换应用主题
  const changeAppTheme = (newTheme: AppTheme) => {
    // 先移除所有主题类
    document.documentElement.classList.remove(
      'theme-tech',
      'theme-creative',
      'theme-finance',
      'theme-education',
      'theme-gradient-blue',
      'theme-gradient-green',
      'theme-gradient-cyan',
      'theme-gradient-teal',
      'theme-gradient-lime',
      'theme-gradient-red',
      'theme-gradient-pink',
      'theme-gradient-purple',
      'theme-gradient-purple-blue',
      'theme-gradient-cyan-blue',
      'theme-gradient-green-blue',
      'theme-gradient-purple-pink',
      'theme-gradient-pink-orange',
      'theme-gradient-teal-lime',
      'theme-gradient-red-yellow'
    );
    
    // 添加新主题类
    if (newTheme !== 'default') {
      document.documentElement.classList.add(appThemes[newTheme].className);
    }
    
    setAppTheme(newTheme);
  };

  return (
    <div className="min-h-screen bg-background text-foreground p-8">
      <div className="max-w-6xl mx-auto">
        <header className="mb-12">
          <h1 className="text-heading-1 font-bold mb-4">按钮组件测试页面</h1>
          <p className="text-body-large mb-6">
            此页面展示了按钮组件的所有变体、尺寸和状态，并支持切换主题。现在包含优化版本对比！
          </p>
          
          {/* 版本切换器 */}
          <div className="mb-6 p-4 bg-card rounded-lg border-2 border-primary/20">
            <div className="flex flex-wrap gap-4 items-center">
              <span className="font-medium flex items-center gap-2">
                <Code size={18} /> 版本对比:
              </span>
              <ButtonGroup>
                <Button 
                  variant={!showOptimizedVersion ? 'primary' : 'outline'} 
                  size="small"
                  onClick={() => setShowOptimizedVersion(false)}
                  leftIcon={<Zap />}
                >
                  原始版本
                </Button>
                <Button 
                  variant={showOptimizedVersion ? 'primary' : 'outline'} 
                  size="small"
                  onClick={() => setShowOptimizedVersion(true)}
                  leftIcon={<Code />}
                >
                  优化版本 (@apply)
                </Button>
              </ButtonGroup>
              <div className="text-sm text-muted-foreground">
                {showOptimizedVersion ? (
                  <span className="flex items-center gap-1">
                    <Code size={14} />
                    使用 @apply 指令优化的版本 - 代码更简洁，维护性更好
                  </span>
                ) : (
                  <span className="flex items-center gap-1">
                    <Zap size={14} />
                    原始版本 - 使用长 className 列表
                  </span>
                )}
              </div>
            </div>
          </div>
          
          <div className="flex flex-wrap gap-4 items-center">
            {/* 明暗模式切换按钮 */}
            <Button 
              variant="outline" 
              onClick={toggleColorMode}
              leftIcon={colorMode === 'light' ? <Moon /> : <Sun />}
            >
              {colorMode === 'light' ? '切换到暗色模式' : '切换到亮色模式'}
            </Button>
            
            {/* 应用主题选择器 */}
            <div className="flex items-center gap-2">
              <span className="font-medium flex items-center gap-2">
                <Palette size={18} /> 应用主题:
              </span>
              <ButtonGroup>
                {Object.entries(appThemes).map(([key, theme]) => (
                  <Button 
                    key={key}
                    variant={appTheme === key ? 'primary' : 'outline'} 
                    size="small"
                    onClick={() => changeAppTheme(key as AppTheme)}
                    leftIcon={theme.icon}
                  >
                    {theme.name}
                  </Button>
                ))}
              </ButtonGroup>
            </div>
            

          </div>
        </header>

        {/* 条件渲染：根据选择的版本显示不同内容 */}
        {showOptimizedVersion ? (
          <TestOptimizedButton />
        ) : (
          <>
        {/* 按钮变体展示 */}
        <section className="mb-16">
          <h2 className="text-heading-3 font-semibold mb-6">按钮变体</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="p-6 bg-card rounded-lg shadow-sm">
              <h3 className="text-heading-4 mb-4">Primary</h3>
              <div className="flex flex-wrap gap-4">
                <Button variant="primary">主要按钮</Button>
                <Button variant="primary" disabled>禁用状态</Button>
                <Button variant="primary" loading>加载状态</Button>
              </div>
            </div>
            
            <div className="p-6 bg-card rounded-lg shadow-sm">
              <h3 className="text-heading-4 mb-4">Secondary</h3>
              <div className="flex flex-wrap gap-4">
                <Button variant="secondary">次要按钮</Button>
                <Button variant="secondary" disabled>禁用状态</Button>
                <Button variant="secondary" loading>加载状态</Button>
              </div>
            </div>
            
            <div className="p-6 bg-card rounded-lg shadow-sm">
              <h3 className="text-heading-4 mb-4">Outline</h3>
              <div className="flex flex-wrap gap-4">
                <Button variant="outline">轮廓按钮</Button>
                <Button variant="outline" disabled>禁用状态</Button>
                <Button variant="outline" loading>加载状态</Button>
              </div>
            </div>
            
            <div className="p-6 bg-card rounded-lg shadow-sm">
              <h3 className="text-heading-4 mb-4">Ghost</h3>
              <div className="flex flex-wrap gap-4">
                <Button variant="ghost">幽灵按钮</Button>
                <Button variant="ghost" disabled>禁用状态</Button>
                <Button variant="ghost" loading>加载状态</Button>
              </div>
            </div>
            
            <div className="p-6 bg-card rounded-lg shadow-sm">
              <h3 className="text-heading-4 mb-4">Destructive</h3>
              <div className="flex flex-wrap gap-4">
                <Button variant="destructive">危险按钮</Button>
                <Button variant="destructive" disabled>禁用状态</Button>
                <Button variant="destructive" loading>加载状态</Button>
              </div>
            </div>
            
            <div className="p-6 bg-card rounded-lg shadow-sm">
              <h3 className="text-heading-4 mb-4">Link</h3>
              <div className="flex flex-wrap gap-4">
                <Button variant="link">链接按钮</Button>
                <Button variant="link" disabled>禁用状态</Button>
                <Button variant="link" loading>加载状态</Button>
              </div>
            </div>
            
            <div className="p-6 bg-card rounded-lg shadow-sm">
              <h3 className="text-heading-4 mb-4">Gradient</h3>
              <div className="flex flex-wrap gap-4">
                <Button variant="gradient">渐变按钮</Button>
                <Button variant="gradient" disabled>禁用状态</Button>
                <Button variant="gradient" loading>加载状态</Button>
              </div>
            </div>
          </div>
        </section>
        
        {/* 渐变按钮展示 */}
        <section className="mb-16">
          <h2 className="text-heading-3 font-semibold mb-6">渐变按钮集合</h2>
          <div className="p-6 bg-card rounded-lg shadow-sm">
            <h3 className="text-heading-4 mb-4">预设渐变按钮</h3>
            <div className="flex flex-wrap gap-4">
              <Button variant="gradient-blue">蓝色渐变</Button>
              <Button variant="gradient-green">绿色渐变</Button>
              <Button variant="gradient-cyan">青色渐变</Button>
              <Button variant="gradient-teal">青绿渐变</Button>
              <Button variant="gradient-lime">青柿渐变</Button>
              <Button variant="gradient-red">红色渐变</Button>
              <Button variant="gradient-pink">粉色渐变</Button>
              <Button variant="gradient-purple">紫色渐变</Button>
              
              <div className="w-full border-t my-4"></div>
              <h3 className="text-lg font-medium mb-2">带图标的渐变按钮</h3>
              <div className="flex flex-wrap gap-4">
                <Button variant="gradient-blue" leftIcon={<Download />}>下载文件</Button>
                <Button variant="gradient-green" rightIcon={<ChevronRight />}>下一步</Button>
                <Button variant="gradient-purple" leftIcon={<Mail />}>发送邮件</Button>
                <Button variant="gradient-red" leftIcon={<Trash />}>删除</Button>
                <Button variant="gradient-cyan" rightIcon={<ExternalLink />}>外部链接</Button>
                <Button variant="gradient-pink" leftIcon={<Plus />}>新增</Button>
                <Button variant="gradient-teal" leftIcon={<Check />}>确认</Button>
              </div>
              
              <div className="w-full border-t my-4"></div>
              <h3 className="text-lg font-medium mb-2">仅图标渐变按钮</h3>
              <div className="flex flex-wrap gap-4">
                <Button variant="gradient-blue" iconOnly leftIcon={<Download />} aria-label="下载" className="rounded-full" />
                <Button variant="gradient-green" iconOnly leftIcon={<Check />} aria-label="确认" className="rounded-full" />
                <Button variant="gradient-purple" iconOnly leftIcon={<Mail />} aria-label="邮件" className="rounded-full" />
                <Button variant="gradient-red" iconOnly leftIcon={<Trash />} aria-label="删除" className="rounded-full" />
                <Button variant="gradient-cyan" iconOnly leftIcon={<ExternalLink />} aria-label="链接" className="rounded-full" />
                <Button variant="gradient-pink" iconOnly leftIcon={<Plus />} aria-label="新增" className="rounded-full" />
              </div>
              
              <div className="w-full border-t my-4"></div>
              <h3 className="text-lg font-medium mb-2">不同尺寸的图标按钮</h3>
              <div className="flex flex-wrap gap-4 items-center theme-gradient-purple-blue">
                <Button variant="gradient" size="small" leftIcon={<Download />}>小型</Button>
                <Button variant="gradient" size="medium" leftIcon={<Download />}>中型</Button>
                <Button variant="gradient" size="large" leftIcon={<Download />}>大型</Button>
                <Button variant="gradient" size="small" iconOnly leftIcon={<Download />} aria-label="下载" className="rounded-full" />
                <Button variant="gradient" size="medium" iconOnly leftIcon={<Download />} aria-label="下载" className="rounded-full" />
                <Button variant="gradient" size="large" iconOnly leftIcon={<Download />} aria-label="下载" className="rounded-full" />
              </div>
              
              <div className="w-full border-t my-4"></div>
              <h3 className="text-lg font-medium mb-2">双色渐变按钮主题</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mt-4">
                <div className="p-4 bg-card rounded-lg shadow-sm theme-gradient-purple-blue">
                  <h4 className="text-base font-medium mb-2">紫蓝渐变主题</h4>
                  <Button variant="gradient">紫蓝渐变</Button>
                </div>
                
                <div className="p-4 bg-card rounded-lg shadow-sm theme-gradient-cyan-blue">
                  <h4 className="text-base font-medium mb-2">青蓝渐变主题</h4>
                  <Button variant="gradient">青蓝渐变</Button>
                </div>
                
                <div className="p-4 bg-card rounded-lg shadow-sm theme-gradient-green-blue">
                  <h4 className="text-base font-medium mb-2">绿蓝渐变主题</h4>
                  <Button variant="gradient">绿蓝渐变</Button>
                </div>
                
                <div className="p-4 bg-card rounded-lg shadow-sm theme-gradient-purple-pink">
                  <h4 className="text-base font-medium mb-2">紫粉渐变主题</h4>
                  <Button variant="gradient">紫粉渐变</Button>
                </div>
                
                <div className="p-4 bg-card rounded-lg shadow-sm theme-gradient-pink-orange">
                  <h4 className="text-base font-medium mb-2">粉橙渐变主题</h4>
                  <Button variant="gradient">粉橙渐变</Button>
                </div>
                
                <div className="p-4 bg-card rounded-lg shadow-sm theme-gradient-teal-lime">
                  <h4 className="text-base font-medium mb-2">青柿渐变主题</h4>
                  <Button variant="gradient">青柿渐变</Button>
                </div>
                
                <div className="p-4 bg-card rounded-lg shadow-sm theme-gradient-red-yellow">
                  <h4 className="text-base font-medium mb-2">红黄渐变主题</h4>
                  <Button variant="gradient">红黄渐变</Button>
                </div>
              </div>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
            <div className="p-6 bg-card rounded-lg shadow-sm theme-gradient-blue">
              <h3 className="text-heading-4 mb-4">蓝色主题渐变按钮</h3>
              <div className="flex flex-wrap gap-4">
                <Button variant="gradient">主题渐变</Button>
                <Button variant="gradient" leftIcon={<Download />}>下载</Button>
                <Button variant="gradient" rightIcon={<ChevronRight />}>下一步</Button>
              </div>
            </div>
            
            <div className="p-6 bg-card rounded-lg shadow-sm theme-gradient-green">
              <h3 className="text-heading-4 mb-4">绿色主题渐变按钮</h3>
              <div className="flex flex-wrap gap-4">
                <Button variant="gradient">主题渐变</Button>
                <Button variant="gradient" leftIcon={<Check />}>确认</Button>
                <Button variant="gradient" rightIcon={<ExternalLink />}>访问</Button>
              </div>
            </div>
            
            <div className="p-6 bg-card rounded-lg shadow-sm theme-gradient-purple-blue">
              <h3 className="text-heading-4 mb-4">紫蓝渐变主题</h3>
              <div className="flex flex-wrap gap-4">
                <Button variant="gradient">主题渐变</Button>
                <Button variant="gradient" leftIcon={<Mail />}>发送邮件</Button>
                <Button variant="gradient" rightIcon={<ChevronRight />}>下一步</Button>
              </div>
            </div>
            
            <div className="p-6 bg-card rounded-lg shadow-sm theme-gradient-red-yellow">
              <h3 className="text-heading-4 mb-4">红黄渐变主题</h3>
              <div className="flex flex-wrap gap-4">
                <Button variant="gradient">主题渐变</Button>
                <Button variant="gradient" leftIcon={<Trash />}>删除</Button>
                <Button variant="gradient" iconOnly leftIcon={<Plus />} aria-label="新增" />
              </div>
            </div>
            
            <div className="p-6 bg-card rounded-lg shadow-sm theme-gradient-teal-lime">
              <h3 className="text-heading-4 mb-4">青柿渐变主题</h3>
              <div className="flex flex-wrap gap-4">
                <Button variant="gradient">主题渐变</Button>
                <Button variant="gradient" leftIcon={<Plus />}>新增</Button>
                <Button variant="gradient" iconOnly leftIcon={<Check />} aria-label="确认" />
              </div>
            </div>
            
            <div className="p-6 bg-card rounded-lg shadow-sm theme-gradient-pink-orange">
              <h3 className="text-heading-4 mb-4">粉橙渐变主题</h3>
              <div className="flex flex-wrap gap-4">
                <Button variant="gradient">主题渐变</Button>
                <Button variant="gradient" leftIcon={<Download />}>下载</Button>
                <Button variant="gradient" iconOnly leftIcon={<Mail />} aria-label="邮件" />
              </div>
            </div>
            
            <div className="p-6 bg-card rounded-lg shadow-sm theme-gradient-cyan-blue">
              <h3 className="text-heading-4 mb-4">青蓝渐变主题</h3>
              <div className="flex flex-wrap gap-4">
                <Button variant="gradient">主题渐变</Button>
                <Button variant="gradient" disabled leftIcon={<Download />}>禁用状态</Button>
                <Button variant="gradient" loading>加载状态</Button>
              </div>
            </div>
          </div>
        </section>
        
        {/* 落地页最佳实践按钮示例 */}
        <section className="mb-16">
          <h2 className="text-heading-3 font-semibold mb-6">落地页最佳实践按钮示例</h2>
          
          {/* Hero 区块按钮示例 */}
          <div className="p-6 bg-card rounded-lg shadow-sm mb-8">
            <h3 className="text-heading-4 mb-6">Hero 区块按钮设计</h3>
            
            <div className="max-w-4xl mx-auto bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-950/30 dark:to-blue-950/30 p-10 rounded-xl">
              <div className="text-center mb-8">
                <h2 className="text-4xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-purple-600 to-blue-600 dark:from-purple-400 dark:to-blue-400">打造下一代数字体验</h2>
                <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">使用我们的平台创建您的完美落地页，提升转化率并吸引更多客户</p>
                
                <div className="flex flex-wrap justify-center gap-4 theme-gradient-purple-blue">
                  <Button variant="gradient" size="large" leftIcon={<Briefcase />}>免费开始使用</Button>
                  <Button variant="outline" size="large" rightIcon={<ExternalLink />}>查看演示</Button>
                </div>
              </div>
            </div>
            
            <div className="mt-6 p-4 bg-muted/30 rounded-lg">
              <h4 className="text-base font-medium mb-2">最佳实践说明：</h4>
              <ul className="list-disc list-inside space-y-2 text-sm">
                <li>主要 CTA 按钮使用高对比度的渐变色，增强视觉吸引力</li>
                <li>次要按钮使用较低调的轮廓样式，不与主要按钮竞争注意力</li>
                <li>使用大尺寸按钮增强可点击区域，提高转化率</li>
                <li>添加图标增强按钮可识别性和吸引力</li>
                <li>保持文案简洁明确，使用行动导向的语言</li>
              </ul>
            </div>
          </div>
          
          {/* 中间 CTA 区块按钮示例 */}
          <div className="p-6 bg-card rounded-lg shadow-sm mb-8">
            <h3 className="text-heading-4 mb-6">中间 CTA 区块按钮设计</h3>
            
            <div className="max-w-4xl mx-auto bg-gradient-to-r from-teal-50 to-lime-50 dark:from-teal-950/30 dark:to-lime-950/30 p-10 rounded-xl">
              <div className="flex flex-col md:flex-row items-center justify-between gap-8">
                <div className="md:w-2/3">
                  <h3 className="text-2xl font-bold mb-2">提升您的业务增长能力</h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">加入我们的专业计划，获取更多高级功能和专属支持</p>
                </div>
                <div className="md:w-1/3 theme-gradient-teal-lime">
                  <Button variant="gradient" size="large" rightIcon={<ChevronRight />}>升级到专业版</Button>
                </div>
              </div>
            </div>
            
            <div className="mt-6 p-4 bg-muted/30 rounded-lg">
              <h4 className="text-base font-medium mb-2">最佳实践说明：</h4>
              <ul className="list-disc list-inside space-y-2 text-sm">
                <li>使用与内容相关的渐变色调，增强视觉一致性</li>
                <li>按钮位置靠近内容右侧，符合用户视线流动</li>
                <li>使用右侧箭头图标暗示前进操作，增强行动引导</li>
                <li>保持按钮文案简洁直接，明确表达价值主张</li>
              </ul>
            </div>
          </div>
          
          {/* 底部 CTA 区块按钮示例 */}
          <div className="p-6 bg-card rounded-lg shadow-sm mb-8">
            <h3 className="text-heading-4 mb-6">底部 CTA 区块按钮设计</h3>
            
            <div className="max-w-4xl mx-auto bg-gradient-to-r from-pink-50 to-orange-50 dark:from-pink-950/30 dark:to-orange-950/30 p-10 rounded-xl text-center">
              <h3 className="text-3xl font-bold mb-4">准备好开始您的旅程了吗？</h3>
              <p className="text-lg text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">加入成千上万的满意客户，体验我们的服务并提升您的业务</p>
              
              <div className="flex flex-col sm:flex-row justify-center gap-4 max-w-md mx-auto theme-gradient-pink-orange">
                <Button variant="gradient" size="large" className="w-full" leftIcon={<GraduationCap />}>立即注册</Button>
                <Button variant="outline" size="large" className="w-full">联系销售</Button>
              </div>
              
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-4">无需信用卡 · 免费试用 14 天 · 随时取消</p>
            </div>
            
            <div className="mt-6 p-4 bg-muted/30 rounded-lg">
              <h4 className="text-base font-medium mb-2">最佳实践说明：</h4>
              <ul className="list-disc list-inside space-y-2 text-sm">
                <li>使用强烈的渐变色调吸引最终行动</li>
                <li>提供两个按钮选项，满足不同准备程度的用户需求</li>
                <li>添加安心文案，降低用户的决策风险</li>
                <li>使用全宽按钮增大可点击区域，提高移动端转化率</li>
                <li>主按钮使用图标增强识别度，次要按钮保持简洁</li>
              </ul>
            </div>
          </div>
          
          {/* 科技风格 Hero 区块示例 */}
          <div className="p-6 bg-card rounded-lg shadow-sm mb-8">
            <h3 className="text-heading-4 mb-6">科技风格 Hero 区块按钮设计</h3>
            
            <div className="max-w-4xl mx-auto bg-gradient-to-r from-slate-900 to-slate-800 p-10 rounded-xl overflow-hidden relative">
              <div className="absolute inset-0 bg-grid-white/[0.05] bg-[size:20px_20px]" />
              <div className="absolute h-40 w-full top-0 left-0 bg-gradient-to-r from-cyan-500/20 to-blue-500/20 blur-3xl" />
              
              <div className="relative z-10 text-center mb-8">
                <h2 className="text-4xl font-bold mb-4 text-white">人工智能驱动的未来</h2>
                <p className="text-lg text-slate-300 mb-8 max-w-2xl mx-auto">探索我们的 AI 平台，释放您业务的无限潜力</p>
                
                <div className="flex flex-wrap justify-center gap-4 theme-gradient-cyan-blue">
                  <Button variant="gradient" size="large" leftIcon={<Briefcase />}>开始免费试用</Button>
                  <Button variant="ghost" size="large" className="text-white hover:bg-white/10" rightIcon={<ExternalLink />}>观看演示</Button>
                </div>
              </div>
            </div>
            
            <div className="mt-6 p-4 bg-muted/30 rounded-lg">
              <h4 className="text-base font-medium mb-2">设计说明：</h4>
              <ul className="list-disc list-inside space-y-2 text-sm">
                <li>深色背景配合青蓝渐变按钮，创造高科技感</li>
                <li>次要按钮使用半透明效果，增强未来感</li>
                <li>使用网格和模糊光效增强科技感</li>
                <li>主按钮的青蓝渐变色与背景光效呈现美学一致性</li>
              </ul>
            </div>
          </div>
          
          {/* 创意设计风格 Hero 区块示例 */}
          <div className="p-6 bg-card rounded-lg shadow-sm mb-8">
            <h3 className="text-heading-4 mb-6">创意设计风格 Hero 区块按钮设计</h3>
            
            <div className="max-w-4xl mx-auto bg-white dark:bg-slate-900 p-10 rounded-xl overflow-hidden relative">
              <div className="absolute inset-0 bg-[url('/noise.svg')] opacity-20" />
              <div className="absolute -top-24 -left-24 w-96 h-96 bg-purple-600 rounded-full mix-blend-multiply filter blur-3xl opacity-30 dark:opacity-20" />
              <div className="absolute -bottom-24 -right-24 w-96 h-96 bg-pink-600 rounded-full mix-blend-multiply filter blur-3xl opacity-30 dark:opacity-20" />
              
              <div className="relative z-10 text-center mb-8">
                <h2 className="text-4xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-purple-600 to-pink-600 dark:from-purple-400 dark:to-pink-400">释放您的创意潜力</h2>
                <p className="text-lg text-gray-700 dark:text-gray-300 mb-8 max-w-2xl mx-auto">使用我们的设计工具，将您的想法转化为现实</p>
                
                <div className="flex flex-wrap justify-center gap-4 theme-gradient-purple-pink">
                  <Button variant="gradient" size="large" leftIcon={<Brush />}>开始创作</Button>
                  <Button variant="outline" size="large" className="border-purple-300 dark:border-purple-700">查看模板</Button>
                </div>
              </div>
            </div>
            
            <div className="mt-6 p-4 bg-muted/30 rounded-lg">
              <h4 className="text-base font-medium mb-2">设计说明：</h4>
              <ul className="list-disc list-inside space-y-2 text-sm">
                <li>使用紫粉渐变色调强调创意与设计属性</li>
                <li>模糊圆形光效增强视觉吸引力</li>
                <li>渐变文字与按钮颜色协调，强化品牌一致性</li>
                <li>次要按钮使用染色边框，与主要按钮形成色彩关联</li>
              </ul>
            </div>
          </div>
          
          {/* SaaS 中间 CTA 区块示例 */}
          <div className="p-6 bg-card rounded-lg shadow-sm mb-8">
            <h3 className="text-heading-4 mb-6">SaaS 中间 CTA 区块按钮设计</h3>
            
            <div className="max-w-4xl mx-auto bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 p-10 rounded-xl">
              <div className="flex flex-col md:flex-row items-center justify-between gap-8">
                <div className="md:w-2/3">
                  <h3 className="text-2xl font-bold mb-2">提升团队协作效率</h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">我们的企业级方案可以帮助您的团队提高生产力并简化工作流程</p>
                </div>
                <div className="md:w-1/3 theme-gradient-green-blue">
                  <Button variant="gradient" size="large" className="w-full" leftIcon={<Briefcase />}>安排演示</Button>
                </div>
              </div>
            </div>
            
            <div className="mt-6 p-4 bg-muted/30 rounded-lg">
              <h4 className="text-base font-medium mb-2">设计说明：</h4>
              <ul className="list-disc list-inside space-y-2 text-sm">
                <li>使用绿蓝渐变色调表达增长与效率</li>
                <li>全宽按钮设计增强视觉重量感</li>
                <li>公文包图标暗示业务属性，增强专业感</li>
                <li>渐变背景与按钮颜色协调，创造和谐的视觉体验</li>
              </ul>
            </div>
          </div>
          
          {/* 电商风格底部 CTA 区块示例 */}
          <div className="p-6 bg-card rounded-lg shadow-sm">
            <h3 className="text-heading-4 mb-6">电商风格底部 CTA 区块按钮设计</h3>
            
            <div className="max-w-4xl mx-auto bg-gradient-to-r from-red-50 to-yellow-50 dark:from-red-950/30 dark:to-yellow-950/30 p-10 rounded-xl text-center">
              <div className="inline-block px-4 py-1 bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 rounded-full text-sm font-medium mb-4">限时优惠</div>
              <h3 className="text-3xl font-bold mb-4">年中促销活动现已开始</h3>
              <p className="text-lg text-gray-600 dark:text-gray-300 mb-4 max-w-2xl mx-auto">全场商品低至 5 折，还有更多惊喜等待您的发现</p>
              <p className="text-lg font-bold text-red-600 dark:text-red-400 mb-8">仅剩 3 天！</p>
              
              <div className="flex flex-col sm:flex-row justify-center gap-4 max-w-md mx-auto theme-gradient-red-yellow">
                <Button variant="gradient" size="large" className="w-full" leftIcon={<Briefcase />}>立即购买</Button>
                <Button variant="outline" size="large" className="w-full border-red-300 dark:border-red-700 text-red-600 dark:text-red-400">查看全部促销</Button>
              </div>
            </div>
            
            <div className="mt-6 p-4 bg-muted/30 rounded-lg">
              <h4 className="text-base font-medium mb-2">设计说明：</h4>
              <ul className="list-disc list-inside space-y-2 text-sm">
                <li>红黄渐变色调表达紧迫感和促销氛围</li>
                <li>使用限时标签和倒计时增强紧迫感</li>
                <li>主按钮使用强烈的渐变色吸引点击</li>
                <li>次要按钮使用与主题相关的边框和文字颜色</li>
                <li>文案强调紧迫感和限时性，驱动快速决策</li>
              </ul>
            </div>
          </div>
        </section>

        {/* 按钮尺寸展示 */}
        <section className="mb-16">
          <h2 className="text-heading-3 font-semibold mb-6">按钮尺寸</h2>
          <div className="p-6 bg-card rounded-lg shadow-sm">
            <div className="flex flex-wrap items-center gap-4">
              <Button size="small">小按钮</Button>
              <Button size="medium">中按钮</Button>
              <Button size="large">大按钮</Button>
            </div>
          </div>
        </section>

        {/* 按钮图标展示 */}
        <section className="mb-16">
          <h2 className="text-heading-3 font-semibold mb-6">按钮图标</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="p-6 bg-card rounded-lg shadow-sm">
              <h3 className="text-heading-4 mb-4">左侧图标</h3>
              <div className="flex flex-wrap gap-4">
                <Button leftIcon={<Mail />} size="small">发送邮件</Button>
                <Button leftIcon={<Mail />}>发送邮件</Button>
                <Button leftIcon={<Mail />} size="large">发送邮件</Button>
              </div>
            </div>
            
            <div className="p-6 bg-card rounded-lg shadow-sm">
              <h3 className="text-heading-4 mb-4">右侧图标</h3>
              <div className="flex flex-wrap gap-4">
                <Button rightIcon={<ChevronRight />} size="small">下一步</Button>
                <Button rightIcon={<ChevronRight />}>下一步</Button>
                <Button rightIcon={<ChevronRight />} size="large">下一步</Button>
              </div>
            </div>
            
            <div className="p-6 bg-card rounded-lg shadow-sm">
              <h3 className="text-heading-4 mb-4">仅图标按钮</h3>
              <div className="flex flex-wrap gap-4">
                <Button leftIcon={<Plus />} iconOnly size="small" aria-label="添加" />
                <Button leftIcon={<Plus />} iconOnly aria-label="添加" />
                <Button leftIcon={<Plus />} iconOnly size="large" aria-label="添加" />
              </div>
            </div>
            
            <div className="p-6 bg-card rounded-lg shadow-sm">
              <h3 className="text-heading-4 mb-4">不同变体的图标按钮</h3>
              <div className="flex flex-wrap gap-4">
                <Button leftIcon={<Plus />} variant="primary" iconOnly aria-label="添加" />
                <Button leftIcon={<Check />} variant="secondary" iconOnly aria-label="确认" />
                <Button leftIcon={<Trash />} variant="destructive" iconOnly aria-label="删除" />
                <Button leftIcon={<Download />} variant="outline" iconOnly aria-label="下载" />
              </div>
            </div>
          </div>
        </section>

        {/* 按钮链接展示 */}
        <section className="mb-16">
          <h2 className="text-heading-3 font-semibold mb-6">按钮链接</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="p-6 bg-card rounded-lg shadow-sm">
              <h3 className="text-heading-4 mb-4">内部链接</h3>
              <Button href="/" leftIcon={<ChevronRight />}>
                返回首页
              </Button>
            </div>
            
            <div className="p-6 bg-card rounded-lg shadow-sm">
              <h3 className="text-heading-4 mb-4">外部链接</h3>
              <Button 
                href="https://github.com" 
                external 
                rightIcon={<ExternalLink />}
              >
                访问 GitHub
              </Button>
            </div>
            
            <div className="p-6 bg-card rounded-lg shadow-sm">
              <h3 className="text-heading-4 mb-4">下载链接</h3>
              <Button 
                href="/example.pdf" 
                download="example.pdf" 
                rightIcon={<Download />}
              >
                下载文件
              </Button>
            </div>
          </div>
        </section>

        {/* 社交媒体按钮展示 */}
        <section className="mb-16">
          <h2 className="text-heading-3 font-semibold mb-6">社交媒体按钮</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="p-6 bg-card rounded-lg shadow-sm">
              <h3 className="text-heading-4 mb-4">基本社交媒体按钮</h3>
              <div className="flex flex-wrap gap-4">
                <Button 
                  variant="outline" 
                  className="bg-white hover:bg-blue-50 text-blue-600 border-blue-200 hover:border-blue-300 dark:bg-slate-800 dark:hover:bg-slate-700 dark:text-blue-400 dark:border-blue-800"
                  leftIcon={<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/></svg>}
                >
                  Facebook
                </Button>
                <Button 
                  variant="outline" 
                  className="bg-white hover:bg-blue-50 text-blue-400 border-blue-200 hover:border-blue-300 dark:bg-slate-800 dark:hover:bg-slate-700 dark:text-blue-400 dark:border-blue-800"
                  leftIcon={<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/></svg>}
                >
                  Twitter
                </Button>
                <Button 
                  variant="outline" 
                  className="bg-white hover:bg-red-50 text-red-500 border-red-200 hover:border-red-300 dark:bg-slate-800 dark:hover:bg-slate-700 dark:text-red-400 dark:border-red-800"
                  leftIcon={<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M12 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0zm4.83 8.86c.17 1.87-.56 3.46-1.53 4.67C14.33 14.73 13.22 15.73 12 16c-1.22-.27-2.33-1.27-3.3-2.47-1-1.22-1.7-2.8-1.53-4.67.1-1.04.5-1.87 1.2-2.47.7-.6 1.62-.93 2.63-.92 1 0 1.94.34 2.63.92.7.6 1.1 1.43 1.2 2.47zm-9.5 6.97c.57-.8 1.23-1.4 1.87-1.87.64-.47 1.47-.8 2.3-.94.37-.07.67-.22.8-.47.13-.25.17-.6-.03-1.07-.2-.47-.5-.87-.97-1.2-.47-.33-1.07-.53-1.7-.53s-1.23.2-1.7.53c-.47.33-.77.73-.97 1.2-.2.47-.17.82-.03 1.07.13.25.43.4.8.47.83.14 1.66.47 2.3.94.64.47 1.3 1.07 1.87 1.87.57.8 1.1 1.8 1.33 3.07C8.33 18.93 8.1 18 8 17.13c-.1-.87-.37-1.67-.67-2.37-.3-.7-.67-1.37-1.1-1.93-.43-.57-.9-1.07-1.4-1.47-.5-.4-1-.73-1.47-1.1-.47-.37-.9-.77-1.23-1.17-.33-.4-.57-.8-.7-1.3-.13-.5-.13-1 .07-1.5.2-.5.57-.93 1.07-1.27.5-.33 1.13-.57 1.87-.57.73 0 1.37.23 1.87.57.5.33.87.77 1.07 1.27.2.5.2 1 .07 1.5-.13.5-.37.9-.7 1.3-.33.4-.77.8-1.23 1.17-.47.37-.97.7-1.47 1.1-.5.4-.97.9-1.4 1.47-.43.57-.8 1.23-1.1 1.93-.3.7-.57 1.5-.67 2.37-.1.87-.33 1.8-.2 2.7.23-1.27.77-2.27 1.33-3.07z"/></svg>}
                >
                  Pinterest
                </Button>
              </div>
            </div>
            
            <div className="p-6 bg-card rounded-lg shadow-sm">
              <h3 className="text-heading-4 mb-4">品牌色社交媒体按钮</h3>
              <div className="flex flex-wrap gap-4">
                <Button 
                  className="bg-[#1877F2] hover:bg-[#166FE5] text-white border-none"
                  leftIcon={<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/></svg>}
                >
                  Facebook
                </Button>
                <Button 
                  className="bg-[#1DA1F2] hover:bg-[#1A94DA] text-white border-none"
                  leftIcon={<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/></svg>}
                >
                  Twitter
                </Button>
                <Button 
                  className="bg-[#E60023] hover:bg-[#D50020] text-white border-none"
                  leftIcon={<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M12 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0zm4.83 8.86c.17 1.87-.56 3.46-1.53 4.67C14.33 14.73 13.22 15.73 12 16c-1.22-.27-2.33-1.27-3.3-2.47-1-1.22-1.7-2.8-1.53-4.67.1-1.04.5-1.87 1.2-2.47.7-.6 1.62-.93 2.63-.92 1 0 1.94.34 2.63.92.7.6 1.1 1.43 1.2 2.47zm-9.5 6.97c.57-.8 1.23-1.4 1.87-1.87.64-.47 1.47-.8 2.3-.94.37-.07.67-.22.8-.47.13-.25.17-.6-.03-1.07-.2-.47-.5-.87-.97-1.2-.47-.33-1.07-.53-1.7-.53s-1.23.2-1.7.53c-.47.33-.77.73-.97 1.2-.2.47-.17.82-.03 1.07.13.25.43.4.8.47.83.14 1.66.47 2.3.94.64.47 1.3 1.07 1.87 1.87.57.8 1.1 1.8 1.33 3.07C8.33 18.93 8.1 18 8 17.13c-.1-.87-.37-1.67-.67-2.37-.3-.7-.67-1.37-1.1-1.93-.43-.57-.9-1.07-1.4-1.47-.5-.4-1-.73-1.47-1.1-.47-.37-.9-.77-1.23-1.17-.33-.4-.57-.8-.7-1.3-.13-.5-.13-1 .07-1.5.2-.5.57-.93 1.07-1.27.5-.33 1.13-.57 1.87-.57.73 0 1.37.23 1.87.57.5.33.87.77 1.07 1.27.2.5.2 1 .07 1.5-.13.5-.37.9-.7 1.3-.33.4-.77.8-1.23 1.17-.47.37-.97.7-1.47 1.1-.5.4-.97.9-1.4 1.47-.43.57-.8 1.23-1.1 1.93-.3.7-.57 1.5-.67 2.37-.1.87-.33 1.8-.2 2.7.23-1.27.77-2.27 1.33-3.07z"/></svg>}
                >
                  Pinterest
                </Button>
              </div>
            </div>
            
            <div className="p-6 bg-card rounded-lg shadow-sm">
              <h3 className="text-heading-4 mb-4">圆形社交媒体按钮</h3>
              <div className="flex flex-wrap gap-4">
                <Button 
                  variant="outline" 
                  className="rounded-full bg-white hover:bg-blue-50 text-blue-600 border-blue-200 hover:border-blue-300 dark:bg-slate-800 dark:hover:bg-slate-700 dark:text-blue-400 dark:border-blue-800 p-2"
                  iconOnly
                  leftIcon={<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/></svg>}
                  aria-label="Facebook"
                />
                <Button 
                  variant="outline" 
                  className="rounded-full bg-white hover:bg-blue-50 text-blue-400 border-blue-200 hover:border-blue-300 dark:bg-slate-800 dark:hover:bg-slate-700 dark:text-blue-400 dark:border-blue-800 p-2"
                  iconOnly
                  leftIcon={<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/></svg>}
                  aria-label="Twitter"
                />
                <Button 
                  variant="outline" 
                  className="rounded-full bg-white hover:bg-red-50 text-red-500 border-red-200 hover:border-red-300 dark:bg-slate-800 dark:hover:bg-slate-700 dark:text-red-400 dark:border-red-800 p-2"
                  iconOnly
                  leftIcon={<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M12 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0zm4.83 8.86c.17 1.87-.56 3.46-1.53 4.67C14.33 14.73 13.22 15.73 12 16c-1.22-.27-2.33-1.27-3.3-2.47-1-1.22-1.7-2.8-1.53-4.67.1-1.04.5-1.87 1.2-2.47.7-.6 1.62-.93 2.63-.92 1 0 1.94.34 2.63.92.7.6 1.1 1.43 1.2 2.47zm-9.5 6.97c.57-.8 1.23-1.4 1.87-1.87.64-.47 1.47-.8 2.3-.94.37-.07.67-.22.8-.47.13-.25.17-.6-.03-1.07-.2-.47-.5-.87-.97-1.2-.47-.33-1.07-.53-1.7-.53s-1.23.2-1.7.53c-.47.33-.77.73-.97 1.2-.2.47-.17.82-.03 1.07.13.25.43.4.8.47.83.14 1.66.47 2.3.94.64.47 1.3 1.07 1.87 1.87.57.8 1.1 1.8 1.33 3.07C8.33 18.93 8.1 18 8 17.13c-.1-.87-.37-1.67-.67-2.37-.3-.7-.67-1.37-1.1-1.93-.43-.57-.9-1.07-1.4-1.47-.5-.4-1-.73-1.47-1.1-.47-.37-.9-.77-1.23-1.17-.33-.4-.57-.8-.7-1.3-.13-.5-.13-1 .07-1.5.2-.5.57-.93 1.07-1.27.5-.33 1.13-.57 1.87-.57.73 0 1.37.23 1.87.57.5.33.87.77 1.07 1.27.2.5.2 1 .07 1.5-.13.5-.37.9-.7 1.3-.33.4-.77.8-1.23 1.17-.47.37-.97.7-1.47 1.1-.5.4-.97.9-1.4 1.47-.43.57-.8 1.23-1.1 1.93-.3.7-.57 1.5-.67 2.37-.1.87-.33 1.8-.2 2.7.23-1.27.77-2.27 1.33-3.07z"/></svg>}
                  aria-label="Pinterest"
                />
              </div>
            </div>
            
            <div className="p-6 bg-card rounded-lg shadow-sm">
              <h3 className="text-heading-4 mb-4">带分享数据的社交按钮</h3>
              <div className="flex flex-wrap gap-4">
                <Button 
                  className="bg-[#1877F2] hover:bg-[#166FE5] text-white border-none"
                  leftIcon={<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/></svg>}
                >
                  分享 <span className="ml-1 bg-white/20 px-1.5 py-0.5 rounded text-xs">2.5k</span>
                </Button>
                <Button 
                  className="bg-[#1DA1F2] hover:bg-[#1A94DA] text-white border-none"
                  leftIcon={<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/></svg>}
                >
                  转发 <span className="ml-1 bg-white/20 px-1.5 py-0.5 rounded text-xs">1.2k</span>
                </Button>
              </div>
            </div>
          </div>
          
          <div className="mt-6 p-4 bg-muted/30 rounded-lg">
            <h4 className="text-base font-medium mb-2">设计说明：</h4>
            <ul className="list-disc list-inside space-y-2 text-sm">
              <li>社交媒体按钮使用各平台的品牌色和图标，提高识别度</li>
              <li>提供了轮廓样式和方形样式，适应不同设计需求</li>
              <li>带分享数据的按钮增强社交证明，鼓励用户参与</li>
              <li>所有按钮都支持暗模式，确保在不同主题下的可读性</li>
            </ul>
          </div>
        </section>
        
        {/* 支付按钮展示 */}
        <section className="mb-16">
          <h2 className="text-heading-3 font-semibold mb-6">支付按钮</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="p-6 bg-card rounded-lg shadow-sm">
              <h3 className="text-heading-4 mb-4">基本支付按钮</h3>
              <div className="flex flex-wrap gap-4">
                <Button 
                  className="bg-[#00A1E9] hover:bg-[#0096D6] text-white border-none"
                  leftIcon={<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M10.97 4.97a.75.75 0 0 1 1.07 1.05l-3.99 4.99a.75.75 0 0 1-1.08.02L4.324 8.384a.75.75 0 1 1 1.06-1.06l2.094 2.093 3.473-4.425a.267.267 0 0 1 .02-.022z"/><path d="M5.5 2A3.5 3.5 0 0 0 2 5.5v13A3.5 3.5 0 0 0 5.5 22h13a3.5 3.5 0 0 0 3.5-3.5V13a.5.5 0 0 0-1 0v5.5a2.5 2.5 0 0 1-2.5 2.5h-13a2.5 2.5 0 0 1-2.5-2.5v-13A2.5 2.5 0 0 1 5.5 3H11a.5.5 0 0 0 0-1H5.5z"/></svg>}
                >
                  立即支付
                </Button>
                <Button 
                  variant="outline" 
                  className="border-[#00A1E9] text-[#00A1E9] hover:bg-blue-50 dark:hover:bg-slate-800"
                  leftIcon={<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="8" cy="21" r="1"></circle><circle cx="19" cy="21" r="1"></circle><path d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12"></path></svg>}
                >
                  加入购物车
                </Button>
              </div>
            </div>
            
            <div className="p-6 bg-card rounded-lg shadow-sm">
              <h3 className="text-heading-4 mb-4">支付平台按钮</h3>
              <div className="flex flex-wrap gap-4">
                <Button 
                  className="bg-[#4285F4] hover:bg-[#3B78E7] text-white border-none"
                  leftIcon={<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="#4285F4"/><path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="#34A853"/><path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="#FBBC05"/><path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="#EA4335"/><path d="M1 1h22v22H1z" fill="none"/></svg>}
                >
                  Google Pay
                </Button>
                <Button 
                  className="bg-black hover:bg-gray-900 text-white border-none"
                  leftIcon={<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M17.72 11.91c-.28-3.06 2.55-4.5 2.67-4.58-1.46-2.12-3.7-2.42-4.5-2.44-1.9-.2-3.73 1.13-4.7 1.13-.98 0-2.49-1.1-4.1-1.08-2.09.04-4.03 1.23-5.1 3.1-2.19 3.8-.56 9.4 1.55 12.46 1.05 1.5 2.28 3.17 3.9 3.12 1.57-.07 2.16-1 4.06-1 1.9 0 2.44 1 4.08.96 1.7-.03 2.76-1.52 3.78-3.03 1.2-1.73 1.7-3.43 1.7-3.52-.03-.01-3.26-1.24-3.3-4.96l.04-.1zM14.68 3.77c.85-1.04 1.42-2.48 1.27-3.94-1.22.05-2.7.82-3.57 1.86-.77.9-1.46 2.37-1.28 3.75 1.37.1 2.76-.68 3.58-1.67z"/></svg>}
                >
                  Apple Pay
                </Button>
                <Button 
                  className="bg-[#0070BA] hover:bg-[#005EA6] text-white border-none"
                  leftIcon={<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M20.067 8.478c.492.88.556 2.014.3 3.327-.74 3.806-3.276 5.12-6.514 5.12h-.5a.805.805 0 0 0-.794.68l-.04.22-.63 4.023-.024.13a.804.804 0 0 1-.794.68h-2.52a.483.483 0 0 1-.477-.558l.79-5.02c.008-.08.033-.156.074-.22a.483.483 0 0 1 .403-.239h1.203c4.035 0 6.775-1.436 7.64-5.763.32-1.61.152-2.95-.72-3.9a3.132 3.132 0 0 0-.903-.765c.266.254.48.54.636.848zm-3.13-3.039c-.218-.062-.443-.113-.676-.153a12.173 12.173 0 0 0-1.92-.133h-5.318c-.118 0-.227.037-.317.103a.523.523 0 0 0-.203.263l-1.314 8.323-.4.253a.483.483 0 0 0 .477.558h3.05c.23 0 .447-.15.486-.38l.02-.107.383-2.44.026-.13c.038-.23.256-.38.485-.38h.305c2.578 0 4.604-.83 5.2-3.227.25-1.01.12-1.85-.386-2.434a2.11 2.11 0 0 0-.598-.516z"/><path d="M16.16 5.48c-.9.9-2.53 1.37-4.583 1.37h-1.195c-.115 0-.23.08-.252.193l-.017.053-.398 2.523-.026.166c.02-.112.115-.193.23-.193h1.43c2.053 0 3.684-.47 4.583-1.37.9-.9 1.37-2.53 1.37-4.583 0-2.053-.47-3.684-1.37-4.583-.9-.9-2.53-1.37-4.583-1.37h-5.48c-.115 0-.23.08-.252.193l-1.43 9.063c-.023.112.07.193.184.193h3.48c.115 0 .23-.08.252-.193l.017-.053.398-2.523.026-.166c.02-.112.115-.193.23-.193h1.43c2.053 0 3.684-.47 4.583-1.37.9-.9 1.37-2.53 1.37-4.583 0-2.053-.47-3.684-1.37-4.583-.9-.9-2.53-1.37-4.583-1.37h-5.48c-.115 0-.23.08-.252.193l-1.43 9.063c-.023.112.07.193.184.193h3.48c.115 0 .23-.08.252-.193l.017-.053.398-2.523.026-.166c.02-.112.115-.193.23-.193h1.43c2.053 0 3.684-.47 4.583-1.37z"/></svg>}
                >
                  PayPal
                </Button>
              </div>
            </div>
            
            <div className="p-6 bg-card rounded-lg shadow-sm">
              <h3 className="text-heading-4 mb-4">微信支付和支付宝按钮</h3>
              <div className="flex flex-wrap gap-4">
                <Button 
                  className="bg-[#07C160] hover:bg-[#06AD56] text-white border-none"
                  leftIcon={<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M9.5 4.25C5.5 4.25 2 7.13 2 10.38c0 1.86 1.28 3.56 3.26 4.48.19.1.31.29.33.51l.7.86c.03.17.22.32.38.23l1.32-.55c.23-.1.49-.05.7.12.57.45 1.29.71 2.07.71.18 0 .34-.3.51-.05-1.24-5.86 6.43-8.36 5.65-12.4-1.26-.25-2.56-.39-3.88-.39-1.36 0-2.64.15-3.84.4zm-1.54 3.88c.78 0 1.42.64 1.42 1.43s-.64 1.42-1.42 1.42c-.79 0-1.42-.64-1.42-1.42 0-.79.64-1.43 1.42-1.43zm5.4 0c.78 0 1.42.64 1.42 1.43s-.64 1.42-1.42 1.42c-.79 0-1.43-.64-1.43-1.42 0-.79.64-1.43 1.43-1.43z"/><path d="M18.65 20.29l.8-.31c.16-.06.34.05.37.22l.07.76c.01.16.13.3.29.32 1.73.34 2.82-1.25 2.82-1.25 1.73-.81 2-2.29 2-2.29.87-3.09-2.82-5.61-5.89-5.61-3.3 0-5.96 2.25-5.96 5.04 0 2.79 2.67 5.04 5.96 5.04.18 0 .35-.1.52-.03zm-3.17-4.95c-.2-.2-.2-.53 0-.73.2-.2.53-.2.73 0 .2.2.2.53 0 .73-.2.2-.53.2-.73 0zm2.68 0c-.2-.2-.2-.53 0-.73.2-.2.53-.2.73 0 .2.2.2.53 0 .73-.2.2-.53.2-.73 0z"/></svg>}
                >
                  微信支付
                </Button>
                <Button 
                  className="bg-[#1677FF] hover:bg-[#0E68E3] text-white border-none"
                  leftIcon={<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M21.422 15.358c-4.597.474-8.7-3.857-8.7-8.478 0-1.748.6-3.357 1.607-4.66A12.97 12.97 0 0 0 12 2C6.477 2 2 6.477 2 12s4.477 10 10 10c4.1 0 7.625-2.468 9.168-6a8.118 8.118 0 0 1-1.746-.642h2zm-9.19-12.06c-3.627 1.045-6.394 4.426-6.394 8.402 0 3.715 2.213 6.936 5.558 8.4a9.847 9.847 0 0 1-1.667-5.5c0-4.55 3.125-8.37 7.399-9.525-.985-.69-2.082-1.177-3.275-1.423a10.954 10.954 0 0 0-1.622-.355z"/></svg>}
                >
                  支付宝
                </Button>
              </div>
            </div>
            
            <div className="p-6 bg-card rounded-lg shadow-sm">
              <h3 className="text-heading-4 mb-4">订阅和购买按钮</h3>
              <div className="flex flex-wrap gap-4">
                <Button 
                  className="bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white border-none"
                  leftIcon={<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M2.5 19.5A2 2 0 0 1 4.5 17H20"></path><path d="M2.5 14.5A2 2 0 0 1 4.5 12H20"></path><path d="M2.5 9.5A2 2 0 0 1 4.5 7H20"></path><path d="M3.5 4.5h16"></path></svg>}
                >
                  订阅年度计划 <span className="ml-1 bg-white/20 px-1.5 py-0.5 rounded text-xs">省 20%</span>
                </Button>
                <Button 
                  className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white border-none"
                  leftIcon={<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M3 3h18v18H3z"></path><path d="m16 10-4 4-4-4"></path></svg>}
                >
                  一键购买
                </Button>
              </div>
            </div>
          </div>
          
          <div className="mt-6 p-4 bg-muted/30 rounded-lg">
            <h4 className="text-base font-medium mb-2">设计说明：</h4>
            <ul className="list-disc list-inside space-y-2 text-sm">
              <li>支付按钮使用各支付平台的品牌色和图标，提高用户信任感</li>
              <li>对于国际支付平台，保留英文名称以保证识别度</li>
              <li>对于订阅按钮，添加优惠信息标签增强转化率</li>
              <li>使用渐变色背景增强视觉吸引力，突出重要操作</li>
            </ul>
          </div>
        </section>
        
        {/* 按钮组展示 */}
        <section className="mb-16">
          <h2 className="text-heading-3 font-semibold mb-6">按钮组</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="p-6 bg-card rounded-lg shadow-sm">
              <h3 className="text-heading-4 mb-4">水平按钮组</h3>
              <ButtonGroup>
                <Button>上一步</Button>
                <Button variant="primary">确认</Button>
                <Button>下一步</Button>
              </ButtonGroup>
            </div>
            
            <div className="p-6 bg-card rounded-lg shadow-sm">
              <h3 className="text-heading-4 mb-4">紧凑型按钮组</h3>
              <ButtonGroup compact>
                <Button variant="outline">上一步</Button>
                <Button variant="primary">确认</Button>
                <Button variant="outline">下一步</Button>
              </ButtonGroup>
            </div>
            
            <div className="p-6 bg-card rounded-lg shadow-sm">
              <h3 className="text-heading-4 mb-4">垂直按钮组</h3>
              <ButtonGroup direction="vertical">
                <Button>选项 1</Button>
                <Button variant="primary">选项 2</Button>
                <Button>选项 3</Button>
              </ButtonGroup>
            </div>
            
            <div className="p-6 bg-card rounded-lg shadow-sm">
              <h3 className="text-heading-4 mb-4">全宽度按钮组</h3>
              <ButtonGroup fullWidth>
                <Button>取消</Button>
                <Button variant="primary">确认</Button>
              </ButtonGroup>
            </div>
          </div>
        </section>

        {/* 特殊状态展示 */}
        <section className="mb-16">
          <h2 className="text-heading-3 font-semibold mb-6">特殊状态</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="p-6 bg-card rounded-lg shadow-sm">
              <h3 className="text-heading-4 mb-4">脉冲效果</h3>
              <Button pulse variant="primary">
                点击注册
              </Button>
            </div>
            
            <div className="p-6 bg-card rounded-lg shadow-sm">
              <h3 className="text-heading-4 mb-4">加载状态</h3>
              <div className="flex flex-wrap gap-4">
                <Button loading>加载中...</Button>
                <Button loading leftIcon={<Mail />}>发送中...</Button>
              </div>
            </div>
          </div>
        </section>
        
        {/* 按钮图标使用方式示例 */}
        <section className="mb-16">
          <h2 className="text-heading-3 font-semibold mb-6">按钮图标使用方式</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 传统方式 */}
            <div className="p-6 bg-card rounded-lg shadow-sm">
              <h3 className="text-heading-4 mb-4">传统方式（属性方式）</h3>
              <div className="flex flex-wrap gap-4">
                <Button leftIcon={<Mail />}>左侧图标</Button>
                <Button rightIcon={<ArrowRight />}>右侧图标</Button>
                <Button iconOnly leftIcon={<Plus />} aria-label="添加" />
              </div>
              <p className="mt-2 text-sm text-muted-foreground">使用 leftIcon 和 rightIcon 属性添加图标</p>
            </div>
            
            {/* Shadcn 风格 */}
            <div className="p-6 bg-card rounded-lg shadow-sm">
              <h3 className="text-heading-4 mb-4">Shadcn 风格（子元素方式）</h3>
              <div className="flex flex-wrap gap-4">
                <Button>
                  <Mail /> 左侧图标
                </Button>
                <Button>
                  右侧图标 <ArrowRight />
                </Button>
                <Button variant="outline" size="icon">
                  <Plus />
                </Button>
              </div>
              <p className="mt-2 text-sm text-muted-foreground">直接将图标作为子元素放入按钮中</p>
            </div>
            
            {/* 仅图标按钮对比 */}
            <div className="p-6 bg-card rounded-lg shadow-sm col-span-1 md:col-span-2">
              <h3 className="text-heading-4 mb-4">仅图标按钮对比</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <div>
                  <h4 className="text-base font-medium mb-2">传统方式</h4>
                  <div className="flex flex-wrap gap-4">
                    <Button variant="primary" iconOnly leftIcon={<Search />} aria-label="搜索" />
                    <Button variant="secondary" iconOnly leftIcon={<Download />} aria-label="下载" />
                    <Button variant="outline" iconOnly leftIcon={<Check />} aria-label="确认" />
                    <Button variant="destructive" iconOnly leftIcon={<Trash />} aria-label="删除" />
                  </div>
                  <p className="mt-2 text-sm text-muted-foreground">iconOnly + leftIcon 属性</p>
                </div>
                
                <div>
                  <h4 className="text-base font-medium mb-2">Shadcn 风格</h4>
                  <div className="flex flex-wrap gap-4">
                    <Button variant="primary" size="icon"><Search /></Button>
                    <Button variant="secondary" size="icon"><Download /></Button>
                    <Button variant="outline" size="icon"><Check /></Button>
                    <Button variant="destructive" size="icon"><Trash /></Button>
                  </div>
                  <p className="mt-2 text-sm text-muted-foreground">size=&quot;icon&quot; + 子元素图标</p>
                </div>
              </div>
            </div>
            
            {/* 混合使用 */}
            <div className="p-6 bg-card rounded-lg shadow-sm col-span-1 md:col-span-2">
              <h3 className="text-heading-4 mb-4">高级组合示例</h3>
              <div className="flex flex-wrap gap-4">
                <Button variant="primary" leftIcon={<Mail />} animation={{ hover: 'scale', active: 'press' }}>
                  发送邮件
                </Button>
                
                <Button variant="outline">
                  <Download /> 下载文件
                </Button>
                
                <Button variant="gradient" size="large" animation={{ attention: 'shine' }}>
                  <ShoppingCart /> 立即购买 <ArrowRight />
                </Button>
                
                <Button variant="secondary" size="icon" animation={{ hover: 'lift' }}>
                  <CreditCard />
                </Button>
              </div>
              <p className="mt-2 text-sm text-muted-foreground">可以同时使用两种图标模式，并结合动画效果</p>
            </div>
          </div>
        </section>
        
        {/* 按钮动画系统示例 */}
        <section className="mb-16">
          <h2 className="text-heading-3 font-semibold mb-6">按钮动画系统</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 悬停动画 */}
            <div className="p-6 bg-card rounded-lg shadow-sm">
              <h3 className="text-heading-4 mb-4">悬停动画</h3>
              <div className="flex flex-wrap gap-4">
                <Button 
                  variant="primary" 
                  animation={{ hover: 'scale' }}
                >
                  缩放悬停
                </Button>
                
                <Button 
                  variant="secondary" 
                  animation={{ hover: 'lift' }}
                >
                  上浮悬停
                </Button>
                
                <Button 
                  variant="outline" 
                  animation={{ hover: 'glow' }}
                >
                  发光悬停
                </Button>
              </div>
            </div>
            
            {/* 点击动画 */}
            <div className="p-6 bg-card rounded-lg shadow-sm">
              <h3 className="text-heading-4 mb-4">点击动画</h3>
              <div className="flex flex-wrap gap-4">
                <Button 
                  variant="primary" 
                  animation={{ active: 'press' }}
                >
                  按压效果
                </Button>
                
                <Button 
                  variant="secondary" 
                  animation={{ active: 'sink' }}
                >
                  下沉效果
                </Button>
              </div>
            </div>
            
            {/* 注意力动画 */}
            <div className="p-6 bg-card rounded-lg shadow-sm">
              <h3 className="text-heading-4 mb-4">注意力动画</h3>
              <div className="flex flex-wrap gap-4">
                <Button 
                  variant="primary" 
                  animation={{ attention: 'shine' }}
                >
                  闪光效果
                </Button>
                
                <Button 
                  variant="secondary" 
                  animation={{ attention: 'border-pulse' }}
                >
                  边框脉动
                </Button>
                
                <Button 
                  variant="outline" 
                  animation={{ attention: 'pulse' }}
                >
                  按钮脉动
                </Button>
              </div>
            </div>
            
            {/* 组合动画 */}
            <div className="p-6 bg-card rounded-lg shadow-sm">
              <h3 className="text-heading-4 mb-4">组合动画</h3>
              <div className="flex flex-wrap gap-4">
                <Button 
                  variant="primary" 
                  animation={{ 
                    hover: 'scale', 
                    active: 'press',
                    attention: 'shine'
                  }}
                >
                  完整动画组合
                </Button>
                
                <Button 
                  variant="gradient" 
                  animation={{ 
                    hover: 'lift', 
                    active: 'sink' 
                  }}
                >
                  渐变+悬浮组合
                </Button>
              </div>
            </div>
          </div>
        </section>
        
        {/* 可访问性优化示例 */}
        <section className="mb-16">
          <h2 className="text-heading-3 font-semibold mb-6">按钮可访问性优化</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 仅图标按钮的无障碍标签 */}
            <div className="p-6 bg-card rounded-lg shadow-sm">
              <h3 className="text-heading-4 mb-4">仅图标按钮的无障碍标签</h3>
              <div className="flex flex-wrap gap-4">
                <Button 
                  variant="primary" 
                  iconOnly 
                  leftIcon={<Plus />} 
                  aria-label="添加项目"
                />
                
                <Button 
                  variant="outline" 
                  iconOnly 
                  leftIcon={<Check />} 
                  aria-label="确认"
                />
                
                <Button 
                  variant="secondary" 
                  iconOnly 
                  leftIcon={<Search />} 
                  aria-label="搜索"
                />
              </div>
              <p className="mt-2 text-sm text-muted-foreground">所有仅图标按钮都应提供 aria-label 属性，确保屏幕阅读器可访问性</p>
            </div>
            
            {/* 加载状态文本 */}
            <div className="p-6 bg-card rounded-lg shadow-sm">
              <h3 className="text-heading-4 mb-4">加载状态文本</h3>
              <div className="flex flex-wrap gap-4">
                <Button 
                  variant="primary" 
                  loading 
                  loadingText="提交中..."
                >
                  提交
                </Button>
                
                <Button 
                  variant="secondary" 
                  loading 
                  loadingText="加载中..."
                  leftIcon={<Download />}
                >
                  下载
                </Button>
              </div>
              <p className="mt-2 text-sm text-muted-foreground">加载状态下显示替代文本，提供更好的用户反馈</p>
            </div>
            
            {/* 触摸目标大小 */}
            <div className="col-span-1 md:col-span-2 p-6 bg-card rounded-lg shadow-sm">
              <h3 className="text-heading-4 mb-4">触摸目标大小优化</h3>
              <p className="mb-4 text-sm text-muted-foreground">在触摸设备上，所有按钮都会自动应用最小 44px 的触摸目标尺寸，符合 WCAG 标准</p>
              <div className="flex flex-wrap gap-4 items-center">
                <Button size="small" variant="primary">小按钮</Button>
                <Button size="small" variant="outline" iconOnly leftIcon={<Plus />} aria-label="添加" />
                <div className="text-sm text-muted-foreground">小按钮在触摸设备上会自动扩展点击区域</div>
              </div>
            </div>
          </div>
        </section>
          </>
        )}
      </div>
    </div>
  );
}
