/**
 * 文档编辑器测试页面
 * 
 * 用于测试 Documentation 内容模型的表单功能
 */

'use client';

import React, { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import { BlockEditorShadcn } from '@/components/pageRender/BlockEditor/BlockEditorShadcn';
import documentationConfig from '@/content-schema/Documentation';
import { Calendar, Clock, Tag, ExternalLink, BookOpen, Code, Users, History, Plus, Zap, Wrench, AlertTriangle, Trash2 } from 'lucide-react';

// Document type configuration
const docTypeConfig = {
  guide: { 
    label: '📖 Guide', 
    description: 'Step-by-step instructions',
    color: 'bg-blue-100 text-blue-800'
  },
  api: { 
    label: '🔌 API', 
    description: 'Technical API reference',
    color: 'bg-purple-100 text-purple-800'
  },
  tutorial: { 
    label: '🎓 Tutorial', 
    description: 'Learning-focused content',
    color: 'bg-green-100 text-green-800'
  },
  reference: { 
    label: '📚 Reference', 
    description: 'Quick lookup information',
    color: 'bg-orange-100 text-orange-800'
  },
  faq: { 
    label: '❓ FAQ', 
    description: 'Frequently asked questions',
    color: 'bg-yellow-100 text-yellow-800'
  }
} as const;

// Difficulty configuration
const difficultyConfig = {
  beginner: { label: '🟢 Beginner', description: 'No prior experience needed', color: 'bg-green-100 text-green-800' },
  intermediate: { label: '🟡 Intermediate', description: 'Some experience required', color: 'bg-yellow-100 text-yellow-800' },
  advanced: { label: '🔴 Advanced', description: 'Expert level knowledge', color: 'bg-red-100 text-red-800' }
} as const;

// Change type configuration
const changeTypeConfig = {
  added: { 
    label: '✨ Added', 
    color: 'text-green-700 bg-green-50 border-green-200',
    icon: Plus
  },
  improved: { 
    label: '⚡ Improved', 
    color: 'text-blue-700 bg-blue-50 border-blue-200',
    icon: Zap
  },
  fixed: { 
    label: '🔧 Fixed', 
    color: 'text-purple-700 bg-purple-50 border-purple-200',
    icon: Wrench
  },
  deprecated: { 
    label: '⚠️ Deprecated', 
    color: 'text-orange-700 bg-orange-50 border-orange-200',
    icon: AlertTriangle
  },
  removed: { 
    label: '🗑️ Removed', 
    color: 'text-red-700 bg-red-50 border-red-200',
    icon: Trash2
  }
} as const;

// Types for better type safety
interface CodeExample {
  title: string;
  description?: string;
  content: string;
}

interface ChangelogEntry {
  version: string;
  date: string;
  type: keyof typeof changeTypeConfig;
  description: string;
  details?: string;
}

interface SEOData {
  metaTitle: string;
  metaDescription: string;
  keywords: string[];
}

interface DocumentationData {
  title: string;
  slug: string;
  category: string;
  docType: keyof typeof docTypeConfig;
  difficulty: keyof typeof difficultyConfig;
  summary: string;
  content: string;
  estimatedTime: number;
  prerequisites: string[];
  codeExamples: CodeExample[];
  relatedDocs: string[];
  lastUpdated: string;
  version: string;
  changelog: ChangelogEntry[];
  seo: SEOData;
}

// 默认文档数据
const defaultDocumentationData: DocumentationData = {
  title: "Getting Started with Our API",
  slug: "getting-started-api", 
  category: "API Documentation",
  docType: "guide",
  difficulty: "beginner",
  summary: "Learn how to integrate with our API in just a few steps. This guide covers authentication, making your first request, and handling responses.",
  content: `# Getting Started with Our API

## Introduction

Welcome to our API documentation. This guide will help you get up and running with our API in minutes.

## Authentication

All API requests require authentication using an API key. You can obtain your API key from your dashboard.

### Setting up Authentication

1. Login to your dashboard
2. Navigate to API settings
3. Generate a new API key

## Making Your First Request

Here's how to make your first API call to retrieve user information:

1. Set up your headers with authentication
2. Make a GET request to the users endpoint
3. Handle the response data

### Example Request

The following example shows a basic API request:

\`\`\`bash
curl -X GET "https://api.example.com/users" \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json"
\`\`\`

## Error Handling

Our API returns standard HTTP status codes. Here are the most common ones:

- **200**: Success
- **400**: Bad Request
- **401**: Unauthorized
- **404**: Not Found
- **500**: Internal Server Error

## Next Steps

Once you've made your first successful request, explore our other endpoints in the API reference.`,

  estimatedTime: 10,
  prerequisites: ["Basic knowledge of REST APIs", "API testing tool (Postman, curl)"],
  
  codeExamples: [
    {
      title: "JavaScript Fetch Example", 
      description: "Basic API request using modern fetch API",
      content: `Here's how to make a request using JavaScript:

\`\`\`javascript
const response = await fetch('https://api.example.com/users', {
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  }
});

const data = await response.json();
console.log(data);
\`\`\``
    },
    {
      title: "Python Requests Example",
      description: "API request using Python requests library", 
      content: `Using Python to call the API:

\`\`\`python
import requests

headers = {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
}

response = requests.get('https://api.example.com/users', headers=headers)
data = response.json()
print(data)
\`\`\``
    }
  ],
  
  relatedDocs: ["authentication-guide", "api-reference"],
  lastUpdated: "2024-01-15",
  version: "1.0",
  
  changelog: [
    {
      version: "1.0",
      date: "2024-01-15",
      type: "added",
      description: "Initial documentation created",
      details: "Created comprehensive API documentation with examples and best practices"
    },
    {
      version: "1.1",
      date: "2024-01-20",
      type: "improved",
      description: "Enhanced code examples with better error handling",
      details: "Added try-catch blocks and response validation to all API examples"
    },
    {
      version: "1.2",
      date: "2024-01-25",
      type: "fixed",
      description: "Corrected authentication endpoint URL",
      details: "Fixed incorrect API endpoint reference in authentication section"
    },
    {
      version: "1.3",
      date: "2024-01-30",
      type: "added",
      description: "Added Python SDK examples",
      details: "Included examples using our official Python SDK alongside raw HTTP requests"
    }
  ],
  
  seo: {
    metaTitle: "API Getting Started Guide - Complete Integration Tutorial",
    metaDescription: "Learn how to integrate with our API quickly. Step-by-step guide with code examples in JavaScript and Python.",
    keywords: ["API", "getting started", "integration", "tutorial"]
  }
};

// 生成目录的函数
const generateTableOfContents = (content: string) => {
  const headings = content.match(/^#{1,6}\s+.+$/gm) || [];
  return headings.map((heading, index) => {
    const level = heading.match(/^#+/)?.[0].length || 1;
    const text = heading.replace(/^#+\s+/, '');
    const id = text.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');
    
    return {
      id,
      text,
      level,
      index
    };
  });
};

const DocumentationEditorTestPage = () => {
  const [formData, setFormData] = useState<DocumentationData>(defaultDocumentationData);

  const handleSave = (data: DocumentationData) => {
    console.log('Saved documentation data:', data);
    setFormData(data);
  };

  // 生成目录
  const tableOfContents = generateTableOfContents(formData.content);

  // 按类型分组 changelog
  const groupedChangelog = formData.changelog.reduce((groups: Record<string, ChangelogEntry[]>, change) => {
    const type = change.type;
    if (!groups[type]) {
      groups[type] = [];
    }
    groups[type].push(change);
    return groups;
  }, {});

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="w-full">
        <h1 className="text-3xl font-bold text-foreground mb-6">
          Documentation Editor Test - Documentation Configuration
        </h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 h-[calc(100vh-120px)]">
          {/* Form Editor */}
          <div className="bg-card rounded-lg shadow-lg p-4 border flex flex-col h-full">
            <h2 className="text-xl font-semibold text-card-foreground mb-4">
              Documentation Editor
            </h2>
            <div className="flex-1 overflow-auto">
              <BlockEditorShadcn
                config={documentationConfig}
                initialData={formData}
                onSave={handleSave}
              />
            </div>
          </div>
          
          {/* Preview */}
          <div className="bg-card rounded-lg shadow-lg p-4 border flex flex-col h-full">
            <h2 className="text-xl font-semibold text-card-foreground mb-4">
              Documentation Preview
            </h2>
            <div className="flex-1 overflow-auto space-y-4">
              {/* Document Info Preview */}
              <div className="bg-muted/30 p-4 rounded-lg border">
                {/* Document Header */}
                <div className="mb-4">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="text-2xl font-bold text-foreground">
                      {formData.title}
                    </h3>
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${docTypeConfig[formData.docType]?.color}`}>
                      {docTypeConfig[formData.docType]?.label}
                    </span>
                  </div>
                  
                  <div className="flex items-center gap-3 mb-3">
                    <span className={`px-2 py-1 rounded text-xs font-medium ${difficultyConfig[formData.difficulty]?.color}`}>
                      {difficultyConfig[formData.difficulty]?.label}
                    </span>
                    <span className="text-sm text-muted-foreground">/{formData.slug}</span>
                  </div>
                  
                  <div className="flex items-center gap-4 text-sm text-muted-foreground mb-4">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      Updated: {formData.lastUpdated}
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      {formData.estimatedTime} min read
                    </div>
                    <div className="flex items-center gap-1">
                      <Tag className="h-4 w-4" />
                      v{formData.version}
                    </div>
                  </div>
                  
                  <div className="bg-primary/5 p-4 rounded-lg mb-4">
                    <p className="text-sm text-muted-foreground font-medium">
                      📂 {formData.category}
                    </p>
                    <p className="text-muted-foreground leading-relaxed mt-2">
                      {formData.summary}
                    </p>
                  </div>

                  {/* Prerequisites */}
                  {formData.prerequisites && formData.prerequisites.length > 0 && (
                    <div className="mb-4">
                      <h4 className="font-medium mb-2 text-foreground flex items-center gap-2">
                        <Users className="h-4 w-4" />
                        Prerequisites
                      </h4>
                      <ul className="text-sm text-muted-foreground space-y-1">
                        {formData.prerequisites.map((prereq: string, index: number) => (
                          <li key={index} className="flex items-start gap-2">
                            <span className="text-primary mt-1">•</span>
                            <span>{prereq}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {/* Related Docs */}
                  {formData.relatedDocs && formData.relatedDocs.length > 0 && (
                    <div className="mb-4">
                      <h4 className="font-medium mb-2 text-foreground flex items-center gap-2">
                        <ExternalLink className="h-4 w-4" />
                        Related Documents
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {formData.relatedDocs.map((doc: string, index: number) => (
                          <span key={index} className="bg-primary/10 text-primary px-2 py-1 rounded text-xs font-medium">
                            {doc}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Table of Contents */}
                {tableOfContents.length > 0 && (
                  <div className="mb-4">
                    <h4 className="font-semibold text-foreground mb-2 flex items-center gap-2">
                      <BookOpen className="h-4 w-4" />
                      Table of Contents (Auto-generated)
                    </h4>
                    <div className="bg-muted/50 p-4 rounded-lg">
                      <div className="space-y-2">
                        {tableOfContents.map((item, index) => (
                          <div
                            key={index}
                            className="flex items-center text-sm hover:text-primary cursor-pointer transition-colors"
                            style={{ paddingLeft: `${(item.level - 1) * 16}px` }}
                          >
                            <span className="text-muted-foreground mr-2">
                              {item.level === 1 ? '•' : item.level === 2 ? '◦' : '▪'}
                            </span>
                            <span className="truncate text-muted-foreground hover:text-foreground">
                              {item.text}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* Code Examples */}
                {formData.codeExamples && formData.codeExamples.length > 0 && (
                  <div className="mb-4">
                    <h4 className="font-semibold text-foreground mb-2 flex items-center gap-2">
                      <Code className="h-4 w-4" />
                      Code Examples
                    </h4>
                    <div className="space-y-4">
                      {formData.codeExamples.map((example: CodeExample, index: number) => (
                        <div key={index} className="bg-muted/50 p-4 rounded-lg border">
                          <h5 className="font-medium text-foreground mb-1">{example.title}</h5>
                          {example.description && (
                            <p className="text-sm text-muted-foreground mb-3">{example.description}</p>
                          )}
                          <div className="prose prose-sm max-w-none bg-muted p-3 rounded">
                            <ReactMarkdown
                              components={{
                                code: ({ className, children, ...props }) => {
                                  const match = /language-(\w+)/.exec(className || '');
                                  return match ? (
                                    <code className={`${className} font-mono text-sm`} {...props}>
                                      {children}
                                    </code>
                                  ) : (
                                    <code className="font-mono text-sm bg-muted px-1 py-0.5 rounded" {...props}>
                                      {children}
                                    </code>
                                  );
                                }
                              }}
                            >
                              {example.content}
                            </ReactMarkdown>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Changelog */}
                {formData.changelog && formData.changelog.length > 0 && (
                  <div className="mb-4">
                    <h4 className="font-semibold text-foreground mb-2 flex items-center gap-2">
                      <History className="h-4 w-4" />
                      Change History
                    </h4>
                    <div className="space-y-4">
                      {Object.entries(groupedChangelog).map(([type, changes]) => (
                        <div key={type} className="space-y-2">
                          <h5 className="text-sm font-medium text-foreground flex items-center gap-2">
                            {changeTypeConfig[type as keyof typeof changeTypeConfig]?.label}
                          </h5>
                          <div className="space-y-2">
                            {changes.map((change, index) => (
                              <div 
                                key={index}
                                className={`p-3 rounded-lg border text-sm ${changeTypeConfig[change.type]?.color}`}
                              >
                                <div className="flex items-center justify-between mb-1">
                                  <span className="font-medium">{change.description}</span>
                                  <div className="flex items-center gap-2 text-xs opacity-75">
                                    <span>v{change.version}</span>
                                    <span>•</span>
                                    <span>{change.date}</span>
                                  </div>
                                </div>
                                {change.details && (
                                  <div className="text-xs opacity-90 mt-1">
                                    {change.details}
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Content Preview */}
                <div className="mb-4">
                  <h4 className="font-semibold text-foreground mb-2">Content Preview</h4>
                  <div className="prose prose-sm max-w-none bg-muted/30 p-4 rounded-lg">
                    <ReactMarkdown
                      components={{
                        h1: ({ children }) => <h1 className="text-2xl font-bold mb-4 text-foreground">{children}</h1>,
                        h2: ({ children }) => <h2 className="text-xl font-semibold mb-3 text-foreground">{children}</h2>,
                        h3: ({ children }) => <h3 className="text-lg font-medium mb-2 text-foreground">{children}</h3>,
                        p: ({ children }) => <p className="mb-4 text-muted-foreground leading-relaxed">{children}</p>,
                        ul: ({ children }) => <ul className="mb-4 text-muted-foreground space-y-1">{children}</ul>,
                        ol: ({ children }) => <ol className="mb-4 text-muted-foreground space-y-1">{children}</ol>,
                        li: ({ children }) => <li className="ml-4">{children}</li>,
                        strong: ({ children }) => <strong className="font-semibold text-foreground">{children}</strong>,
                        code: ({ className, children, ...props }) => {
                          const match = /language-(\w+)/.exec(className || '');
                          return match ? (
                            <pre className="bg-muted p-3 rounded text-sm overflow-x-auto mb-4 font-mono">
                              <code className={className} {...props}>
                                {children}
                              </code>
                            </pre>
                          ) : (
                            <code className="bg-muted px-1 py-0.5 rounded text-sm font-mono" {...props}>
                              {children}
                            </code>
                          );
                        }
                      }}
                    >
                      {formData.content}
                    </ReactMarkdown>
                  </div>
                </div>

                {/* SEO Info */}
                {(formData.seo.metaTitle || formData.seo.metaDescription) && (
                  <div className="mt-4 pt-4 border-t">
                    <h4 className="text-sm font-semibold text-muted-foreground mb-2">SEO Preview</h4>
                    <div className="bg-blue-50 dark:bg-blue-950/30 p-4 rounded-lg border">
                      <h5 className="text-blue-800 dark:text-blue-200 font-medium mb-1 truncate">
                        {formData.seo.metaTitle}
                      </h5>
                      <p className="text-green-700 dark:text-green-400 text-sm mb-2">
                        https://example.com/docs/{formData.slug}
                      </p>
                      <p className="text-gray-700 dark:text-gray-300 text-sm">
                        {formData.seo.metaDescription}
                      </p>
                      {formData.seo.keywords && formData.seo.keywords.length > 0 && (
                        <div className="mt-3">
                          <div className="flex flex-wrap gap-1">
                            {formData.seo.keywords.map((keyword: string, index: number) => (
                              <span key={index} className="bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-2 py-1 rounded text-xs">
                                {keyword}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
              
              {/* Raw JSON */}
              <div>
                <h3 className="font-semibold mb-2 text-card-foreground">Raw JSON Data</h3>
                <pre className="bg-muted p-4 rounded-md overflow-auto text-sm text-muted-foreground font-mono max-h-96">
                  {JSON.stringify(formData, null, 2)}
                </pre>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentationEditorTestPage; 