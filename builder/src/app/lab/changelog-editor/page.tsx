/**
 * Changelog 编辑器测试页面
 * 
 * 用于测试 Changelog 内容模型的表单功能
 */

'use client';

import React, { useState } from 'react';
import { BlockEditorShadcn } from '@/components/pageRender/BlockEditor/BlockEditorShadcn';
import changelogConfig from '@/content-schema/Changelog';
import { Calendar, Tag, Zap, Plus, Wrench, Shield } from 'lucide-react';

// Change type configuration
const changeTypeConfig = {
  added: { 
    label: '🎉 Added', 
    color: 'text-green-700 bg-green-50 border-green-200',
    icon: Plus
  },
  improved: { 
    label: '⚡ Improved', 
    color: 'text-blue-700 bg-blue-50 border-blue-200',
    icon: Zap
  },
  fixed: { 
    label: '🔧 Fixed', 
    color: 'text-purple-700 bg-purple-50 border-purple-200',
    icon: Wrench
  },
  security: { 
    label: '🔒 Security', 
    color: 'text-orange-700 bg-orange-50 border-orange-200',
    icon: Shield
  }
} as const;

// Release type configuration
const releaseTypeConfig = {
  major: { label: '🚀 Major', description: 'Breaking changes', color: 'bg-red-100 text-red-800' },
  minor: { label: '✨ Minor', description: 'New features', color: 'bg-blue-100 text-blue-800' },
  patch: { label: '🔧 Patch', description: 'Bug fixes', color: 'bg-green-100 text-green-800' },
  hotfix: { label: '🚨 Hotfix', description: 'Critical fixes', color: 'bg-orange-100 text-orange-800' }
} as const;

// Default changelog data
const defaultChangelogData = {
  version: "2.1.0",
  releaseDate: "2024-01-15",
  releaseType: "minor",
  title: "Enhanced Dashboard & Performance Improvements",
  summary: "This release introduces a redesigned user dashboard, significant performance improvements, and fixes for several user-reported issues. Thank you for all the valuable feedback!",
  changes: [
    {
      type: "added",
      description: "New user dashboard",
      details: "Redesigned dashboard interface with improved data visualization and user experience"
    },
    {
      type: "improved",
      description: "40% faster page loading",
      details: "Optimized database queries and caching strategies for better performance"
    },
    {
      type: "fixed",
      description: "Fixed intermittent email notification failures",
      details: ""
    },
    {
      type: "security",
      description: "Enhanced account security verification",
      details: "Updated encryption algorithms for stronger login protection"
    }
  ],
  seo: {
    metaTitle: "Version 2.1.0 Release Notes - New Dashboard Launch",
    metaDescription: "Discover the latest features: new dashboard, 40% performance boost, security enhancements and more in this update"
  }
};

const ChangelogEditorTestPage = () => {
  const [formData, setFormData] = useState(defaultChangelogData);

  const handleSave = (data: any) => {
    console.log('Saved changelog data:', data);
    setFormData(data);
  };

  // Group changes by type
  const groupedChanges = formData.changes.reduce((groups: any, change: any) => {
    const type = change.type;
    if (!groups[type]) {
      groups[type] = [];
    }
    groups[type].push(change);
    return groups;
  }, {});

  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-3xl font-bold text-foreground mb-8">
          Changelog Editor Test - Changelog Configuration
        </h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Form Editor */}
          <div className="bg-card rounded-lg shadow-lg p-6 border">
            <h2 className="text-xl font-semibold text-card-foreground mb-4">
              Changelog Editor
            </h2>
            <BlockEditorShadcn
              config={changelogConfig}
              initialData={formData}
              onSave={handleSave}
            />
          </div>
          
          {/* Preview */}
          <div className="bg-card rounded-lg shadow-lg p-6 border">
            <h2 className="text-xl font-semibold text-card-foreground mb-4">
              Changelog Preview
            </h2>
            <div className="space-y-6">
              {/* Formatted Preview */}
              <div className="bg-muted/30 p-6 rounded-lg border">
                {/* Version Header */}
                <div className="mb-6">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="text-2xl font-bold text-foreground">
                      Version {formData.version}
                    </h3>
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${releaseTypeConfig[formData.releaseType as keyof typeof releaseTypeConfig]?.color}`}>
                      {releaseTypeConfig[formData.releaseType as keyof typeof releaseTypeConfig]?.label}
                    </span>
                  </div>
                  
                  <h4 className="text-xl text-muted-foreground mb-3">
                    {formData.title}
                  </h4>
                  
                  <div className="flex items-center gap-4 text-sm text-muted-foreground mb-4">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      {formData.releaseDate}
                    </div>
                    <div className="flex items-center gap-1">
                      <Tag className="h-4 w-4" />
                      {releaseTypeConfig[formData.releaseType as keyof typeof releaseTypeConfig]?.description}
                    </div>
                  </div>
                  
                  {formData.summary && (
                    <p className="text-muted-foreground leading-relaxed">
                      {formData.summary}
                    </p>
                  )}
                </div>

                {/* Changes by Type */}
                <div className="space-y-6">
                  {Object.entries(groupedChanges).map(([type, changes]: [string, any]) => (
                    <div key={type} className="space-y-3">
                      <h4 className="text-lg font-semibold text-foreground flex items-center gap-2">
                        {changeTypeConfig[type as keyof typeof changeTypeConfig]?.label}
                      </h4>
                      <div className="space-y-2">
                        {changes.map((change: any, index: number) => (
                          <div 
                            key={index}
                            className={`p-4 rounded-lg border ${changeTypeConfig[type as keyof typeof changeTypeConfig]?.color}`}
                          >
                            <div className="font-medium mb-1">
                              {change.description}
                            </div>
                            {change.details && (
                              <div className="text-sm opacity-90">
                                {change.details}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>

                {/* SEO Info */}
                {(formData.seo.metaTitle || formData.seo.metaDescription) && (
                  <div className="mt-6 pt-6 border-t">
                    <h4 className="text-sm font-semibold text-muted-foreground mb-2">SEO Information</h4>
                    <div className="space-y-1 text-sm">
                      <div><span className="font-medium">Title:</span> {formData.seo.metaTitle}</div>
                      <div><span className="font-medium">Description:</span> {formData.seo.metaDescription}</div>
                    </div>
                  </div>
                )}
              </div>
              
              {/* Raw JSON */}
              <div>
                <h3 className="font-semibold mb-2">Raw JSON Data</h3>
                <pre className="bg-muted p-4 rounded-md overflow-auto text-sm text-muted-foreground font-mono max-h-96">
                  {JSON.stringify(formData, null, 2)}
                </pre>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChangelogEditorTestPage; 