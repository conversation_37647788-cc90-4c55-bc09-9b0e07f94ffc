/**
 * 数组操作测试页面
 * 
 * 用于测试 BlockEditor 中的数组操作功能，使用 HeroText 的真实数据
 */

'use client';

import React, { useState } from 'react';
import { BlockEditorShadcn } from '@/components/pageRender/BlockEditor/BlockEditorShadcn';
import heroImageConfig from '@/content-schema/HeroImage';
import { defaultProps } from '@litpage/sections';

const TestArrayPage = () => {
  const [formData, setFormData] = useState(defaultProps.HeroImage);

  const handleSave = (data: any) => {
    console.log('Saved data:', data);
    setFormData(data);
  };

  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-3xl font-bold text-foreground mb-8">JSON Form Test - HeroImage Configuration</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Form Editor */}
          <div className="bg-card rounded-lg shadow-lg p-6 border">
            <h2 className="text-xl font-semibold text-card-foreground mb-4">Form Editor</h2>
            <BlockEditorShadcn
              config={heroImageConfig}
              initialData={formData}
              onSave={handleSave}
            />
          </div>
          
          {/* JSON Output */}
          <div className="bg-card rounded-lg shadow-lg p-6 border">
            <h2 className="text-xl font-semibold text-card-foreground mb-4">Current Data</h2>
            <pre className="bg-muted p-4 rounded-md overflow-auto text-sm text-muted-foreground font-mono">
              {JSON.stringify(formData, null, 2)}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestArrayPage;
