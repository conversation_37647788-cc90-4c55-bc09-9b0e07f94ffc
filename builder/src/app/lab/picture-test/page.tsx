'use client';

import React from 'react';
import { PictureExamples, StandalonePictureExample } from '@litpage/sections/src/components/Picture/examples';
import { PictureProvider, Format } from '@litpage/sections/src/components/Picture';

// 基础Provider配置
const globalConfig = {
  baseURL: 'http://imgpipe-test.imgpipe.net',
  defaultFormats: ['avif', 'webp', 'jpeg'] as Format[],
  defaultQuality: 70,
  // 对于imgpipe服务，不需要tenant前缀，所以这里不设置tenant
  // tenant: 'demo', 
  sign: (url: string) => `${url}&token=global-demo-token`
};

/**
 * Picture组件测试页面
 * 展示Picture组件的所有功能和用法示例
 */
export default function PictureTestPage() {
  return (
    <PictureProvider config={globalConfig}>
      <div className="min-h-screen bg-gray-50">
      {/* 页面头部 */}
      <header className="bg-white border-b border-gray-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="md:flex md:items-center md:justify-between">
            <div className="flex-1 min-w-0">
              <h1 className="text-3xl font-bold leading-7 text-gray-900 sm:text-4xl sm:truncate">
                Picture 组件测试
              </h1>
              <p className="mt-2 text-sm text-gray-600">
                演示智能图片组件的完整功能：响应式、格式优化、媒体查询、艺术指导等
              </p>
            </div>
            <div className="mt-4 flex md:mt-0 md:ml-4">
              <a
                href="/dashboard"
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                返回首页
              </a>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容区域 */}
      <main className="max-w-7xl mx-auto py-8">
        {/* 功能特性说明 */}
        <div className="px-4 sm:px-6 lg:px-8 mb-12">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h2 className="text-lg font-semibold text-blue-900 mb-3">
              🚀 Picture 组件核心特性
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
              <div className="flex items-start space-x-2">
                <span className="text-blue-600 text-lg">📱</span>
                <div>
                  <strong>响应式图片</strong>
                  <p className="text-blue-700">自动生成多尺寸srcset</p>
                </div>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-blue-600 text-lg">🎨</span>
                <div>
                  <strong>艺术指导</strong>
                  <p className="text-blue-700">不同断点使用不同图片</p>
                </div>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-blue-600 text-lg">⚡</span>
                <div>
                  <strong>格式优化</strong>
                  <p className="text-blue-700">AVIF → WebP → JPEG</p>
                </div>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-blue-600 text-lg">🛡️</span>
                <div>
                  <strong>SSR兼容</strong>
                  <p className="text-blue-700">无hydration问题</p>
                </div>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-blue-600 text-lg">🔧</span>
                <div>
                  <strong>可扩展</strong>
                  <p className="text-blue-700">自定义Loader机制</p>
                </div>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-blue-600 text-lg">🎯</span>
                <div>
                  <strong>性能优先</strong>
                  <p className="text-blue-700">预加载、懒加载、CLS防护</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Picture Provider示例 */}
        <section className="px-4 sm:px-6 lg:px-8 mb-12">
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">
                📦 完整示例集合 (with PictureProvider)
              </h2>
              <p className="mt-1 text-sm text-gray-600">
                包含流体宽度、固定尺寸、艺术指导、媒体查询、主题兼容等完整功能演示
              </p>
            </div>
            <div className="p-6">
              <PictureExamples />
            </div>
          </div>
        </section>

        {/* 独立示例 */}
        <section className="px-4 sm:px-6 lg:px-8 mb-12">
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">
                🔧 独立使用示例 (without Provider)
              </h2>
              <p className="mt-1 text-sm text-gray-600">
                展示在没有PictureProvider的情况下如何使用组件
              </p>
            </div>
            <div className="p-6">
              <StandalonePictureExample />
            </div>
          </div>
        </section>

        {/* 技术说明 */}
        <section className="px-4 sm:px-6 lg:px-8 mb-12">
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">
                🔍 实现细节
              </h2>
            </div>
            <div className="p-6">
              <div className="prose max-w-none">
                <h3>核心架构</h3>
                <ul>
                  <li><strong>Provider模式</strong>：通过PictureProvider管理全局配置</li>
                  <li><strong>Loader机制</strong>：可插拔的URL生成器，支持任意图片服务</li>
                  <li><strong>SSR安全</strong>：固定HTML结构，CSS控制视觉，无hydration问题</li>
                  <li><strong>类型安全</strong>：完整的TypeScript类型定义</li>
                </ul>

                <h3>性能优化</h3>
                <ul>
                  <li><strong>格式协商</strong>：自动生成AVIF、WebP、JPEG多格式源</li>
                  <li><strong>响应式加载</strong>：基于视口和DPR的智能尺寸选择</li>
                  <li><strong>预加载支持</strong>：priority图片自动注入preload link</li>
                  <li><strong>懒加载</strong>：非关键图片默认lazy loading</li>
                  <li><strong>CLS防护</strong>：aspect-ratio确保布局稳定</li>
                </ul>

                <h3>媒体查询能力</h3>
                <ul>
                  <li><strong>艺术指导</strong>：不同断点显示不同构图的图片</li>
                  <li><strong>参数优化</strong>：不同屏幕使用不同质量和尺寸设置</li>
                  <li><strong>格式控制</strong>：基于设备能力的格式选择</li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        {/* 开发工具 */}
        <section className="px-4 sm:px-6 lg:px-8 mb-12">
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">
                🛠️ 开发工具
              </h2>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">浏览器开发者工具</h3>
                  <p className="text-sm text-gray-600 mb-3">
                    使用浏览器开发者工具查看生成的HTML结构：
                  </p>
                  <ol className="text-sm text-gray-700 space-y-1">
                    <li>1. 打开开发者工具 (F12)</li>
                    <li>2. 选择Elements/元素标签页</li>
                    <li>3. 查看&lt;picture&gt;元素的完整结构</li>
                    <li>4. 观察media查询和srcset的生成</li>
                  </ol>
                </div>
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">网络监控</h3>
                  <p className="text-sm text-gray-600 mb-3">
                    在Network标签页观察图片加载：
                  </p>
                  <ol className="text-sm text-gray-700 space-y-1">
                    <li>1. 切换到Network/网络标签页</li>
                    <li>2. 过滤Img类型</li>
                    <li>3. 调整窗口大小测试响应式</li>
                    <li>4. 观察不同格式的加载情况</li>
                  </ol>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* 页脚 */}
      <footer className="bg-white border-t border-gray-200 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-sm text-gray-500">
            <p>Picture 智能图片组件 v2.0 - 现代Web图片处理的最佳实践</p>
            <p className="mt-1">支持艺术指导、媒体查询、格式优化、SSR兼容等企业级特性</p>
          </div>
        </div>
      </footer>
    </div>
    </PictureProvider>
  );
} 