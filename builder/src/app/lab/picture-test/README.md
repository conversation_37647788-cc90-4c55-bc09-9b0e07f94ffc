# Picture 组件测试页面

## 📍 访问地址

- **本地开发**: `http://localhost:3000/picture-test`
- **从主页访问**: `http://localhost:3000` → 点击"Picture 组件测试"按钮

## 🚀 功能展示

### 1. 完整示例集合 (with PictureProvider)
- ✅ **流体宽度横幅**: 响应式宽度适配，支持srcset
- ✅ **固定尺寸头像**: DPR(设备像素比)优化
- ✅ **响应式产品网格**: 多图片网格布局
- ✅ **🎨 艺术指导**: 不同断点显示不同构图的图片
- ✅ **📱 响应式优化**: 不同屏幕的质量和尺寸优化
- ✅ **主题兼容性**: Tech/Creative主题适配
- ✅ **自定义Loader**: Cloudinary风格URL生成
- ✅ **错误处理**: 图片加载失败的fallback

### 2. 独立使用示例 (without Provider)
- ✅ **无Provider模式**: 直接配置loader的使用方式

## 🔍 测试指南

### 响应式测试
1. 打开浏览器开发者工具 (F12)
2. 切换到设备模拟模式
3. 尝试不同的屏幕尺寸：
   - 📱 Mobile: 320px - 767px
   - 📟 Tablet: 768px - 1023px  
   - 💻 Desktop: 1024px+

### 媒体查询验证
1. 在Elements标签页查看`<picture>`元素
2. 观察`<source>`标签的`media`属性
3. 确认不同断点的`srcset`内容

### 网络性能监控
1. 切换到Network标签页
2. 过滤显示"Img"类型
3. 调整窗口大小，观察：
   - 不同尺寸图片的加载
   - 格式协商 (AVIF→WebP→JPEG)
   - 加载时机 (lazy loading)

## 🎨 艺术指导演示

**示例**: 产品展示图片
- **移动端** (≤767px): 显示产品特写，竖向构图
- **平板端** (768px-1023px): 显示产品+背景，方形构图  
- **桌面端** (≥1024px): 显示完整场景，横向构图

## 📱 响应式优化演示

**示例**: 性能优化配置
- **移动端**: 320px-480px, 质量60%, 节省流量
- **平板端**: 600px-900px, 质量70%, 平衡体验
- **桌面端**: 800px-1600px, 质量85%, 高清体验

## 🛠️ 技术验证点

### HTML结构验证
```html
<picture>
  <!-- 桌面端 - 不同图片 -->
  <source media="(min-width: 1024px)" type="image/avif" srcset="..." />
  <source media="(min-width: 1024px)" type="image/webp" srcset="..." />
  
  <!-- 平板端 - 不同图片 -->  
  <source media="(min-width: 768px)" type="image/avif" srcset="..." />
  <source media="(min-width: 768px)" type="image/webp" srcset="..." />
  
  <!-- 默认移动端 -->
  <source type="image/avif" srcset="..." />
  <source type="image/webp" srcset="..." />
  
  <img src="fallback.jpg" alt="..." />
</picture>
```

### CSS类名验证
- ✅ `litpage-picture`: 主容器类
- ✅ `litpage-picture__img`: 图片元素类
- ✅ `litpage-picture__placeholder`: 占位图类
- ✅ `picture-fluid` / `picture-fixed`: 模式类
- ✅ `picture-priority`: 优先级类

### 性能指标验证
- ✅ **CLS (Cumulative Layout Shift)**: 应该接近0
- ✅ **LCP (Largest Contentful Paint)**: 优先级图片快速加载
- ✅ **图片格式**: 现代浏览器优先加载AVIF/WebP

## 🐛 已知问题

1. **图片源限制**: 当前使用测试图片，可能加载较慢
2. **CORS问题**: 某些图片服务可能有跨域限制
3. **格式支持**: 老版本浏览器可能不支持AVIF格式

## 📝 反馈收集

测试过程中如发现问题，请记录：
- 浏览器版本和设备信息
- 具体的错误现象
- 复现步骤
- 控制台错误信息

## 🔗 相关文档

- [Picture组件完整文档](../../../../../sections/src/components/Picture/README.md)
- [组件源码](../../../../../sections/src/components/Picture/)
- [类型定义](../../../../../sections/src/components/Picture/types.ts) 