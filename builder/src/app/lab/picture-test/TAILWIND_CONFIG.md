# Builder Tailwind 配置更新

## 📝 更新内容

为了支持Picture组件测试页面，我们对 `builder/tailwind.config.ts` 进行了以下更新：

### ✅ 1. Safelist 类名添加

在 `safelist` 数组中添加了Picture组件相关的CSS类名，确保这些类名不会被Tailwind的purge机制移除：

```typescript
// Picture组件CSS类名
'litpage-picture',                    // 主容器类
'litpage-picture__img',              // 图片元素类
'litpage-picture__placeholder',      // 占位图类
'picture-fluid',                     // 流体宽度模式
'picture-fixed',                     // 固定尺寸模式
'picture-priority',                  // 优先级图片
'picture-blur-placeholder',          // 模糊占位图
'picture-loading-state',             // 加载状态
'picture-fit-cover',                 // fit: cover
'picture-fit-contain',               // fit: contain
'picture-fit-fill',                  // fit: fill
'picture-fit-scale-down',            // fit: scale-down

// Picture组件状态类
'litpage-picture[data-loaded="true"]',   // 已加载状态
'litpage-picture[data-error="true"]',    // 错误状态
'litpage-picture[data-loading="true"]',  // 加载中状态

// 主题兼容类
'theme-tech',                        // Tech主题
'theme-creative',                    // Creative主题
'theme-finance',                     // Finance主题
'theme-education'                    // Education主题
```

### ✅ 2. 动画关键帧

添加了Picture组件专用的动画关键帧：

```typescript
keyframes: {
  // ... 现有动画
  
  // Picture组件动画
  shimmer: {
    '0%': { transform: 'translateX(-100%)' },
    '100%': { transform: 'translateX(100%)' },
  },
  'picture-fade-in': {
    '0%': { opacity: '0' },
    '100%': { opacity: '1' },
  },
}
```

### ✅ 3. 动画配置

添加了Picture组件的动画配置：

```typescript
animation: {
  // ... 现有动画
  
  // Picture组件动画
  shimmer: "shimmer var(--picture-shimmer-duration, 2s) infinite",
  "picture-fade-in": "picture-fade-in var(--picture-transition-duration, 300ms) ease",
}
```

### ✅ 4. 过渡时间配置

添加了Picture组件的过渡时间配置：

```typescript
// Picture组件过渡时间
transitionDuration: {
  'picture-normal': 'var(--picture-transition-duration, 300ms)',
  'picture-fast': 'var(--picture-transition-duration-fast, 150ms)',
},
transitionTimingFunction: {
  'picture-ease': 'var(--picture-transition-timing, ease)',
},
```

## 🔄 现有配置确认

### ✅ Content 路径

确认现有的 `content` 配置已经正确包含了sections目录：

```typescript
content: [
  './pages/**/*.{ts,tsx}',
  './components/**/*.{ts,tsx}',
  './app/**/*.{ts,tsx}',
  './src/**/*.{ts,tsx}',
  '../sections/**/*.{js,ts,jsx,tsx}',  // ✅ 已包含sections目录
],
```

这确保了Tailwind能够扫描并提取Picture组件中使用的所有CSS类名。

## 🎯 配置效果

通过这些配置更新：

1. **✅ CSS类名保护**: Picture组件的所有CSS类名都被safelist保护，不会被purge
2. **✅ 动画支持**: shimmer加载动画和fade-in过渡动画可正常使用
3. **✅ 过渡效果**: 平滑的图片加载过渡效果
4. **✅ 主题兼容**: 支持不同主题的样式切换
5. **✅ 状态管理**: 支持加载、错误、完成等状态的CSS类

## 🚀 使用验证

在Picture测试页面 (`/picture-test`) 中，你可以验证：

1. **开发者工具检查**: 所有Picture相关的CSS类名都正确应用
2. **动画效果**: shimmer加载动画和图片淡入效果
3. **响应式行为**: 不同断点下的样式变化
4. **主题切换**: 不同主题下的视觉效果

## 📚 相关文档

- [Picture组件CSS文档](../../../../../sections/src/components/Picture/picture.css)
- [Picture组件完整文档](../../../../../sections/src/components/Picture/README.md)
- [Tailwind CSS官方文档](https://tailwindcss.com/docs) 