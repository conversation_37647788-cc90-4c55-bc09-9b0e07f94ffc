'use client';

import React, { useState, useRef } from 'react';
import { ThemedIcon } from '@litpage/sections';
import ThemedSVGIcon from '../../../../components/ThemedIconContainer/ThemedSVGIcon';
import { 
  useResponsiveIcon, 
  usePresetIcon, 
  RESPONSIVE_PRESETS,
  useBreakpoint 
} from '../../../../components/hooks/useResponsiveIcon';
import {
  usePageWidthMode,
  usePageWidthIcon,
  usePageWidthPresetIcon,
  PAGE_WIDTH_PRESETS,
  useInlineIconSize,
  useTextSizeScale
} from '../../../../components/hooks/usePageWidthIcon';
import { 
  Smartphone, 
  Sliders, 
  BookOpen, 
  MessageSquare,
  Palette,
  Code,
  ShieldCheck,
  Globe,
  Heart,
  Star,
  Settings,
  Users,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Download,
  Monitor,
  Tablet,
  Menu,
  Navigation,
  Layout,
  Type,
  Layers
} from 'lucide-react';

interface CurrentVersionDemoProps {
  isEditMode: boolean;
  onIconEdit: (iconIndex: string) => void;
  selectedPageWidth: 'normal' | 'wide' | 'full';
  onPageWidthChange: (width: 'normal' | 'wide' | 'full') => void;
}

const CurrentVersionDemo: React.FC<CurrentVersionDemoProps> = ({
  isEditMode,
  onIconEdit,
  selectedPageWidth,
  onPageWidthChange
}) => {
  const [currentViewport, setCurrentViewport] = useState<'mobile' | 'tablet' | 'desktop'>('desktop');
  const svgRefs = useRef<{ [key: string]: { getSVG: () => SVGSVGElement | null } }>({});
  
  // 使用响应式 Hook
  const currentBreakpoint = useBreakpoint();
  const navigationSizes = usePresetIcon('navigation');
  const featureSizes = usePresetIcon('feature');
  const inlineSizes = usePresetIcon('inline');
  const listSizes = usePresetIcon('list');
  const heroSizes = usePresetIcon('hero');
  
  // 使用页面宽度感知的 Hook
  const currentPageWidth = usePageWidthMode();
  const pageNavSizes = usePageWidthPresetIcon('navigation');
  const pageFeatureSizes = usePageWidthPresetIcon('feature');
  const pageInlineSizes = usePageWidthPresetIcon('inline');
  const textScale = useTextSizeScale();
  const inlineIconSizes = useInlineIconSize(16);

  // 响应式尺寸配置
  const responsiveSizes = {
    mobile: {
      container: 32,
      icon: 20,
      stroke: 1.5,
      label: '移动端 (≤640px)'
    },
    tablet: {
      container: 48,
      icon: 28,
      stroke: 2,
      label: '平板 (641-1024px)'
    },
    desktop: {
      container: 64,
      icon: 36,
      stroke: 2,
      label: '桌面 (≥1024px)'
    }
  };

  // 测试图标数据
  const testIcons = [
    { name: 'Smartphone', icon: Smartphone },
    { name: 'Sliders', icon: Sliders },
    { name: 'BookOpen', icon: BookOpen },
    { name: 'MessageSquare', icon: MessageSquare },
    { name: 'Palette', icon: Palette },
    { name: 'Code', icon: Code },
    { name: 'ShieldCheck', icon: ShieldCheck },
    { name: 'Globe', icon: Globe },
    { name: 'Heart', icon: Heart },
    { name: 'Star', icon: Star },
    { name: 'Settings', icon: Settings },
    { name: 'Users', icon: Users },
    { name: 'Mail', icon: Mail },
    { name: 'Phone', icon: Phone },
    { name: 'MapPin', icon: MapPin },
    { name: 'Calendar', icon: Calendar }
  ];

  // 可用主题
  const themes = [
    { name: 'borderGradient', label: 'Border Gradient' },
    { name: 'solidBlue', label: 'Solid Blue' },
    { name: 'squareShape', label: 'Square Shape' },
    { name: 'centerAligned', label: 'Center Aligned' },
    { name: 'simpleLeft', label: 'Simple Left' },
    { name: 'iconGray', label: 'Icon Gray' },
    { name: 'stackedCards', label: 'Stacked Cards' },
    { name: 'stacks', label: 'Stacks' },
    { name: 'twoCols', label: 'Two Cols' }
  ];

  // 基于 icon-maker SVG 样式的自定义主题
  const customGradientThemes = [
    {
      name: 'security',
      label: 'Security Style',
      config: {
        size: "size-6",
        color: "text-green-600 dark:text-green-400",
        background: "bg-gradient-to-br from-green-500/30 to-green-600/5",
        containerClass: "relative flex justify-center items-center size-16 rounded-2xl border border-green-500/30 shadow-lg shadow-green-500/20",
        strokeWidth: 2
      }
    },
    {
      name: 'access',
      label: 'Access Control Style',
      config: {
        size: "size-6",
        color: "text-blue-600 dark:text-blue-400",
        background: "bg-gradient-to-br from-blue-500/30 to-blue-600/5",
        containerClass: "relative flex justify-center items-center size-16 rounded-2xl border border-blue-500/30 shadow-lg shadow-blue-500/20",
        strokeWidth: 2
      }
    },
    {
      name: 'hosting',
      label: 'Self-Hosted Style',
      config: {
        size: "size-6",
        color: "text-cyan-600 dark:text-cyan-400",
        background: "bg-gradient-to-br from-cyan-500/30 to-cyan-600/5",
        containerClass: "relative flex justify-center items-center size-16 rounded-2xl border border-cyan-500/30 shadow-lg shadow-cyan-500/20",
        strokeWidth: 2
      }
    }
  ];

  // SVG 主题配置
  const svgThemes = [
    { name: 'security', label: 'Security SVG', primaryColor: '#059669' },
    { name: 'access', label: 'Access Control SVG', primaryColor: '#2563eb' },
    { name: 'hosting', label: 'Self-Hosted SVG', primaryColor: '#0891b2' }
  ];

  // 导出 SVG 功能
  const exportSVG = (themeKey: string, iconIndex: number) => {
    const key = `${themeKey}-${iconIndex}`;
    const svgElement = svgRefs.current[key]?.getSVG();
    
    if (svgElement) {
      const serializer = new XMLSerializer();
      const svgString = serializer.serializeToString(svgElement);
      const blob = new Blob([svgString], { type: 'image/svg+xml' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `themed-icon-${themeKey}-${iconIndex}.svg`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    }
  };

  return (
    <div className="space-y-8">
      {/* 页面宽度感知图标演示 */}
      <div className="space-y-6">
        <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200">
          📏 页面宽度感知图标演示 (page-width-* 类适配)
        </h3>
        
        {/* 页面宽度控制器 */}
        <div className="flex justify-center">
          <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
            {[
              { key: 'normal', label: '普通宽度', desc: '默认 (80rem)' },
              { key: 'wide', label: '宽屏模式', desc: 'page-width-wide (96rem)' },
              { key: 'full', label: '满屏模式', desc: 'page-width-full (100%)' }
            ].map(({ key, label, desc }) => (
              <button
                key={key}
                onClick={() => onPageWidthChange(key as any)}
                className={`flex flex-col items-center px-3 py-2 rounded-md transition-colors text-xs ${
                  selectedPageWidth === key
                    ? 'bg-white dark:bg-gray-600 text-blue-600 dark:text-blue-400 shadow-sm'
                    : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                <span className="font-medium">{label}</span>
                <span className="opacity-75">{desc}</span>
              </button>
            ))}
          </div>
        </div>

        {/* 当前状态显示 */}
        <div className="text-center">
          <div className="inline-flex items-center gap-2 px-3 py-2 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <Layout size={16} className="text-green-600 dark:text-green-400" />
            <span className="text-green-900 dark:text-green-100 text-sm font-medium">
              当前页面宽度模式: <code className="bg-green-100 dark:bg-green-800 px-1 py-0.5 rounded text-xs">{currentPageWidth}</code>
            </span>
            <span className="text-green-700 dark:text-green-300 text-xs">
              | 文字缩放: {(textScale * 100).toFixed(1)}%
            </span>
          </div>
        </div>

        {/* 页面宽度感知的预设配置演示 */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 导航图标 */}
          <div className="space-y-3">
            <div className="flex items-center gap-2 text-gray-800 dark:text-white">
              <Navigation size={16} />
              <h4 className="font-semibold text-sm">导航图标 (page-aware)</h4>
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              容器: {pageNavSizes.container}px | 图标: {pageNavSizes.icon}px | 描边: {pageNavSizes.stroke}
            </div>
            <div className="flex flex-wrap gap-2">
              {[Menu, Settings, Users, Mail].map((Icon, index) => (
                <div key={index} className="flex flex-col items-center gap-1">
                  <ThemedSVGIcon
                    icon={Icon}
                    theme="access"
                    size={pageNavSizes.container}
                    iconSize={pageNavSizes.icon}
                    strokeWidth={pageNavSizes.stroke}
                  />
                  <span className="text-xs text-gray-400">页面适配</span>
                </div>
              ))}
            </div>
          </div>

          {/* 功能卡片图标 */}
          <div className="space-y-3">
            <div className="flex items-center gap-2 text-gray-800 dark:text-white">
              <Layers size={16} />
              <h4 className="font-semibold text-sm">功能图标 (page-aware)</h4>
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              容器: {pageFeatureSizes.container}px | 图标: {pageFeatureSizes.icon}px | 描边: {pageFeatureSizes.stroke}
            </div>
            <div className="flex flex-wrap gap-2">
              {[Palette, Code, Globe, Star].map((Icon, index) => (
                <div key={index} className="flex flex-col items-center gap-1">
                  <ThemedSVGIcon
                    icon={Icon}
                    theme="security"
                    size={pageFeatureSizes.container}
                    iconSize={pageFeatureSizes.icon}
                    strokeWidth={pageFeatureSizes.stroke}
                  />
                  <span className="text-xs text-gray-400">随页面缩放</span>
                </div>
              ))}
            </div>
          </div>

          {/* 智能内联图标 */}
          <div className="space-y-3">
            <div className="flex items-center gap-2 text-gray-800 dark:text-white">
              <Type size={16} />
              <h4 className="font-semibold text-sm">智能内联图标</h4>
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              容器: {inlineIconSizes.container}px | 图标: {inlineIconSizes.icon}px | 文字缩放: {(textScale * 100).toFixed(1)}%
            </div>
            <div className="space-y-2">
              <p className="text-gray-700 dark:text-gray-300 text-sm" style={{ fontSize: `${textScale * 0.875}rem` }}>
                这是一段跟随页面宽度缩放的文本 
                <ThemedSVGIcon
                  icon={Heart}
                  theme="hosting"
                  size={inlineIconSizes.container}
                  iconSize={inlineIconSizes.icon}
                  strokeWidth={inlineIconSizes.stroke}
                  className="inline-block align-text-bottom mx-1"
                />
                图标自动适配文字大小
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* useResponsiveIcon Hook 演示 */}
      <div className="space-y-6">
        <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200">
          🎣 useResponsiveIcon Hook 演示 (屏幕断点适配)
        </h3>
        
        {/* 当前断点信息 */}
        <div className="text-center">
          <div className="inline-flex items-center gap-2 px-3 py-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <Monitor size={16} className="text-blue-600 dark:text-blue-400" />
            <span className="text-blue-900 dark:text-blue-100 text-sm font-medium">
              当前断点: <code className="bg-blue-100 dark:bg-blue-800 px-1 py-0.5 rounded text-xs">{currentBreakpoint}</code>
            </span>
          </div>
        </div>

        {/* 预设配置演示 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {/* 导航图标预设 */}
          <div className="space-y-3">
            <h4 className="font-semibold text-gray-800 dark:text-white text-sm">
              导航图标预设
            </h4>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              容器: {navigationSizes.container}px | 图标: {navigationSizes.icon}px | 描边: {navigationSizes.stroke}
            </div>
            <div className="flex flex-wrap gap-2">
              {[Menu, Settings, Users].map((Icon, index) => (
                <div key={index} className="flex flex-col items-center gap-1">
                  <ThemedSVGIcon
                    icon={Icon}
                    theme="access"
                    size={navigationSizes.container}
                    iconSize={navigationSizes.icon}
                    strokeWidth={navigationSizes.stroke}
                  />
                  <span className="text-xs text-gray-400">导航</span>
                </div>
              ))}
            </div>
          </div>

          {/* 功能图标预设 */}
          <div className="space-y-3">
            <h4 className="font-semibold text-gray-800 dark:text-white text-sm">
              功能图标预设
            </h4>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              容器: {featureSizes.container}px | 图标: {featureSizes.icon}px | 描边: {featureSizes.stroke}
            </div>
            <div className="flex flex-wrap gap-2">
              {[Palette, Code, Globe].map((Icon, index) => (
                <div key={index} className="flex flex-col items-center gap-1">
                  <ThemedSVGIcon
                    icon={Icon}
                    theme="security"
                    size={featureSizes.container}
                    iconSize={featureSizes.icon}
                    strokeWidth={featureSizes.stroke}
                  />
                  <span className="text-xs text-gray-400">功能</span>
                </div>
              ))}
            </div>
          </div>

          {/* 英雄图标预设 */}
          <div className="space-y-3">
            <h4 className="font-semibold text-gray-800 dark:text-white text-sm">
              英雄图标预设
            </h4>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              容器: {heroSizes.container}px | 图标: {heroSizes.icon}px | 描边: {heroSizes.stroke}
            </div>
            <div className="flex flex-wrap gap-2">
              {[Star, ShieldCheck].map((Icon, index) => (
                <div key={index} className="flex flex-col items-center gap-1">
                  <ThemedSVGIcon
                    icon={Icon}
                    theme="hosting"
                    size={heroSizes.container}
                    iconSize={heroSizes.icon}
                    strokeWidth={heroSizes.stroke}
                  />
                  <span className="text-xs text-gray-400">英雄</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* 响应式图标尺寸演示 */}
      <div className="space-y-6">
        <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200">
          📱 响应式图标尺寸演示
        </h3>
        
        {/* 视口切换器 */}
        <div className="flex justify-center">
          <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
            {Object.entries(responsiveSizes).map(([key, config]) => (
              <button
                key={key}
                onClick={() => setCurrentViewport(key as any)}
                className={`flex items-center gap-2 px-3 py-2 rounded-md transition-colors text-xs ${
                  currentViewport === key
                    ? 'bg-white dark:bg-gray-600 text-blue-600 dark:text-blue-400 shadow-sm'
                    : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                {key === 'mobile' && <Smartphone size={14} />}
                {key === 'tablet' && <Tablet size={14} />}
                {key === 'desktop' && <Monitor size={14} />}
                <span className="font-medium">{config.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* 当前视口下的图标展示 */}
        <div className="space-y-4">
          <div className="text-center">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
              当前视口: {responsiveSizes[currentViewport].label}
            </h4>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              容器: {responsiveSizes[currentViewport].container}px × {responsiveSizes[currentViewport].container}px | 
              图标: {responsiveSizes[currentViewport].icon}px | 
              描边: {responsiveSizes[currentViewport].stroke}px
            </div>
          </div>

          {/* SVG 容器实现 */}
          <div className="space-y-3">
            <h4 className="font-semibold text-center text-gray-800 dark:text-white text-sm">
              SVG 容器实现
            </h4>
            <div className="grid grid-cols-4 gap-3 justify-items-center">
              {testIcons.slice(0, 8).map((iconItem, index) => (
                <div key={index} className="flex flex-col items-center space-y-1">
                  <ThemedSVGIcon
                    icon={iconItem.icon}
                    theme="security"
                    size={responsiveSizes[currentViewport].container}
                    iconSize={responsiveSizes[currentViewport].icon}
                    strokeWidth={responsiveSizes[currentViewport].stroke}
                  />
                  <span className="text-xs text-gray-500 dark:text-gray-400 text-center">
                    {iconItem.name}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* 真实响应式演示 */}
          <div className="p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
            <h4 className="font-semibold text-gray-800 dark:text-white mb-3 text-center text-sm">
              🎯 真实响应式效果 (调整浏览器宽度查看)
            </h4>
            <div className="grid grid-cols-2 sm:grid-cols-4 lg:grid-cols-6 gap-3 justify-items-center">
              {testIcons.slice(0, 6).map((iconItem, index) => (
                <div key={index} className="flex flex-col items-center space-y-1">
                  <ThemedSVGIcon
                    icon={iconItem.icon}
                    theme="access"
                    size={24}
                    iconSize={16}
                    strokeWidth={1.5}
                    className="sm:w-8 sm:h-8 md:w-10 md:h-10 lg:w-12 lg:h-12"
                  />
                  <span className="text-xs text-gray-500 dark:text-gray-400 text-center">
                    {iconItem.name}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* 原生 SVG 容器展示 */}
      <div className="space-y-6">
        <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200">
          🎯 原生 SVG 容器组件 (基于 a.svg 拆解)
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {svgThemes.map((theme, themeIndex) => (
            <div key={theme.name} className="space-y-3">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 text-center">
                {theme.label}
              </h4>
              <div className="grid grid-cols-4 gap-3">
                {testIcons.slice(0, 8).map((iconItem, index) => {
                  const key = `svg-${theme.name}-${index}`;
                  return (
                    <div key={index} className="flex flex-col items-center space-y-1">
                      <div className="flex justify-center relative group">
                        <ThemedSVGIcon
                          ref={(el: any) => {
                            if (el) svgRefs.current[key] = el;
                          }}
                          icon={iconItem.icon}
                          theme={theme.name as any}
                          primaryColor={theme.primaryColor}
                          size={48}
                          iconSize={24}
                          strokeWidth={1.5}
                        />
                        {/* 导出按钮 */}
                        <button
                          onClick={() => exportSVG(`svg-${theme.name}`, index)}
                          className="absolute -top-1 -right-1 bg-blue-600 hover:bg-blue-700 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                          title="导出 SVG"
                        >
                          <Download size={10} />
                        </button>
                      </div>
                      <span className="text-xs text-gray-600 dark:text-gray-400 text-center">
                        {iconItem.name}
                      </span>
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Icon-Maker 风格的主题展示 */}
      <div className="space-y-6">
        <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200">
          🎨 Icon-Maker 风格主题 (基于 a.svg 和 c.svg)
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {customGradientThemes.map((theme, themeIndex) => (
            <div key={theme.name} className="space-y-3">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 text-center">
                {theme.label}
              </h4>
              <div className="grid grid-cols-4 gap-3">
                {testIcons.slice(0, 8).map((iconItem, index) => (
                  <div key={index} className="flex flex-col items-center space-y-1">
                    <div className="flex justify-center">
                      <ThemedIcon 
                        icon={iconItem.icon} 
                        theme={theme.config}
                        isEditMode={isEditMode}
                        at={`${theme.name}-${index}`}
                        onClick={() => onIconEdit(`${theme.name}-${index}`)}
                      />
                    </div>
                    <span className="text-xs text-gray-600 dark:text-gray-400 text-center">
                      {iconItem.name}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 预设主题展示 - 简化版本 */}
      <div className="space-y-6">
        <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200">
          🎭 预设主题展示 (前4个主题)
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {themes.slice(0, 4).map((theme) => (
            <div key={theme.name} className="space-y-3">
              <h4 className="font-medium text-gray-700 dark:text-gray-300 text-center text-sm">
                {theme.label} ({theme.name})
              </h4>
              <div className="grid grid-cols-4 gap-3">
                {testIcons.slice(0, 8).map((iconItem, index) => (
                  <div key={index} className="flex flex-col items-center space-y-1">
                    <div className="flex justify-center">
                      <ThemedIcon 
                        icon={iconItem.icon} 
                        theme={theme.name as any}
                        isEditMode={isEditMode}
                        at={`${theme.name}-${index}`}
                        onClick={() => onIconEdit(`${theme.name}-${index}`)}
                      />
                    </div>
                    <span className="text-xs text-gray-600 dark:text-gray-400 text-center">
                      {iconItem.name}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 字符串图标测试 */}
      <div className="space-y-6">
        <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200">
          🔤 字符串图标测试 (使用 Lucide 图标名称)
        </h3>
        
        <div className="grid grid-cols-4 gap-3">
          {['Smartphone', 'Sliders', 'BookOpen', 'MessageSquare', 'Palette', 'Code', 'ShieldCheck', 'Globe'].map((iconName, index) => (
            <div key={index} className="flex flex-col items-center space-y-1">
              <div className="flex justify-center">
                <ThemedIcon 
                  icon={iconName} 
                  theme="borderGradient"
                  isEditMode={isEditMode}
                  at={`string-${index}`}
                  onClick={() => onIconEdit(`string-${index}`)}
                />
              </div>
              <span className="text-xs text-gray-600 dark:text-gray-400 text-center">
                &quot;{iconName}&quot;
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* 自定义样式测试 */}
      <div className="space-y-6">
        <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200">
          🎨 自定义样式测试
        </h3>
        
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
          <div className="flex flex-col items-center space-y-2">
            <ThemedIcon 
              icon={Heart} 
              theme="solidBlue"
              className="text-red-500 hover:text-red-600"
              isEditMode={isEditMode}
              at="custom-1"
            />
            <span className="text-xs text-gray-600 dark:text-gray-400">
              自定义颜色 (红色)
            </span>
          </div>
          
          <div className="flex flex-col items-center space-y-2">
            <ThemedIcon 
              icon={Star} 
              theme={{
                size: "size-8",
                color: "text-yellow-500",
                background: "bg-yellow-100",
                containerClass: "flex justify-center items-center size-16 rounded-full border-2 border-yellow-300",
                strokeWidth: 3
              }}
              isEditMode={isEditMode}
              at="custom-2"
            />
            <span className="text-xs text-gray-600 dark:text-gray-400">
              自定义主题对象
            </span>
          </div>
          
          <div className="flex flex-col items-center space-y-2">
            <ThemedIcon 
              icon={Settings} 
              theme="centerAligned"
              className="animate-spin"
              isEditMode={isEditMode}
              at="custom-3"
            />
            <span className="text-xs text-gray-600 dark:text-gray-400">
              旋转动画
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CurrentVersionDemo; 