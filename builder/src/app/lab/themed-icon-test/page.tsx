'use client';

import React, { useState } from 'react';
import { SectionProvider } from '@litpage/sections';
import CurrentVersionDemo from './components/CurrentVersionDemo';
import { ThemedIconV2Demo } from '@litpage/sections/src/components/ThemedIconV2';
// import { Settings } from 'lucide-react';

const ThemedIconTestPage: React.FC = () => {
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedPageWidth, setSelectedPageWidth] = useState<'normal' | 'wide' | 'full'>('normal');

  const handleIconEdit = (iconIndex: string) => {
    console.log('编辑图标:', iconIndex);
    alert(`编辑图标: ${iconIndex}`);
  };

  // 页面宽度控制函数
  const handlePageWidthChange = (width: 'normal' | 'wide' | 'full') => {
    setSelectedPageWidth(width);
    
    // 移除所有页面宽度类
    document.documentElement.classList.remove('page-width-wide', 'page-width-full');
    
    // 添加对应的类
    if (width === 'wide') {
      document.documentElement.classList.add('page-width-wide');
    } else if (width === 'full') {
      document.documentElement.classList.add('page-width-full');
    }
  };

  return (
    <SectionProvider
      initialIsEditMode={false}
      domain="localhost:4000"
      onIconEditClick={handleIconEdit}
    >
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="w-full px-4 py-8">
          {/* 页面标题 */}
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              ThemedIcon 组件对比测试
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-6">
              对比当前版本与新版本的图标系统实现差异
            </p>
            
            {/* 编辑模式开关 */}
            <div className="flex items-center justify-center gap-3 mb-8">
              <label className="text-gray-700 dark:text-gray-300">编辑模式:</label>
              <button
                onClick={() => setIsEditMode(!isEditMode)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  isEditMode ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-700'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    isEditMode ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {isEditMode ? '开启' : '关闭'}
              </span>
            </div>
          </div>

          {/* 两列布局 - 占满页面宽度 */}
          <div className="grid grid-cols-2 gap-6 h-[calc(100vh-240px)]">
            {/* 左侧：当前版本 */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 overflow-y-auto">
              <div className="flex items-center gap-3 mb-6 border-b border-gray-200 dark:border-gray-700 pb-4">
                <div className="flex items-center justify-center w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                  <span className="text-blue-600 dark:text-blue-400 font-bold text-sm">V1</span>
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
                    当前版本 (对照组)
                  </h2>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    基于 @litpage/sections ThemedIcon + ThemedIconContainer
                  </p>
                </div>
              </div>

              <CurrentVersionDemo
                isEditMode={isEditMode}
                onIconEdit={handleIconEdit}
                selectedPageWidth={selectedPageWidth}
                onPageWidthChange={handlePageWidthChange}
              />
            </div>

            {/* 右侧：新版本 */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 overflow-y-auto">
              <div className="flex items-center gap-3 mb-6 border-b border-gray-200 dark:border-gray-700 pb-4">
                <div className="flex items-center justify-center w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-lg">
                  <span className="text-green-600 dark:text-green-400 font-bold text-sm">V2</span>
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
                    新版本 (ThemedIconV2)
                  </h2>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    SSR兼容 + CSS变量驱动的响应式系统
                    </p>
                </div>
              </div>

              <ThemedIconV2Demo
                isEditMode={isEditMode}
                onIconEdit={handleIconEdit}
                selectedPageWidth={selectedPageWidth}
              />
            </div>
          </div>

          {/* 对比说明 */}
          <div className="mt-8 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-yellow-900 dark:text-yellow-100 mb-4">
              📊 版本对比说明
                    </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-yellow-800 dark:text-yellow-200">
              <div>
                <h4 className="font-medium mb-2">当前版本 (V1) 特点:</h4>
                <ul className="space-y-1 text-sm">
                  <li>• 基于JavaScript Hook实现响应式</li>
                  <li>• 使用useEffect监听窗口变化</li>
                  <li>• 两套独立的图标系统</li>
                  <li>• SSR可能存在hydration问题</li>
                  <li>• 功能完整，已验证可用</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2">新版本 (V2) 目标:</h4>
                <ul className="space-y-1 text-sm">
                  <li>• 纯CSS驱动的响应式系统</li>
                  <li>• 完全SSR兼容，无hydration问题</li>
                  <li>• 统一的图标API接口</li>
                  <li>• 支持区块层配置化</li>
                  <li>• 更好的性能和可维护性</li>
                </ul>
               </div>
            </div>
          </div>
        </div>
      </div>
    </SectionProvider>
  );
};

export default ThemedIconTestPage; 