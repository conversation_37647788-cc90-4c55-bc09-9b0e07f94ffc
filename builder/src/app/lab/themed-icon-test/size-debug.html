<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SVG容器尺寸测试</title>
    <style>
        :root {
            --icon-hero-container: 112;
            --icon-hero-icon: 56;
        }
        
        .test-container {
            width: calc(var(--icon-hero-container) * 1px);
            height: calc(var(--icon-hero-container) * 1px);
            border: 2px solid red;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px;
            position: relative;
        }
        
        .test-container::before {
            content: 'Container: ' attr(data-size) 'px';
            position: absolute;
            top: -25px;
            left: 0;
            font-size: 12px;
            color: red;
        }
        
        .test-svg {
            width: 100%;
            height: 100%;
            border: 1px solid blue;
        }
        
        .info {
            margin: 20px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="info">
        <h2>SVG容器尺寸测试</h2>
        <p>红色边框: 外层容器 (应该是112px)</p>
        <p>蓝色边框: SVG元素 (应该填满容器，也是112px)</p>
    </div>
    
    <div class="test-container" data-size="112">
        <svg class="test-svg" viewBox="0 0 112 112" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect width="112" height="112" rx="28" fill="lightblue" fill-opacity="0.4"></rect>
            <rect width="111" height="111" x="0.5" y="0.5" rx="27.5" stroke="blue" stroke-opacity="0.5" fill="none"></rect>
            <g transform="translate(28, 28)">
                <circle cx="28" cy="28" r="28" fill="green" fill-opacity="0.3"/>
                <text x="28" y="32" text-anchor="middle" font-size="10" fill="black">56px图标</text>
            </g>
        </svg>
    </div>
    
    <script>
        // 实时显示计算值
        function updateSizes() {
            const container = document.querySelector('.test-container');
            const svg = document.querySelector('.test-svg');
            
            const containerRect = container.getBoundingClientRect();
            const svgRect = svg.getBoundingClientRect();
            
            console.log('Container actual size:', containerRect.width, 'x', containerRect.height);
            console.log('SVG actual size:', svgRect.width, 'x', svgRect.height);
            
            const style = getComputedStyle(document.documentElement);
            const heroContainer = style.getPropertyValue('--icon-hero-container').trim();
            const heroIcon = style.getPropertyValue('--icon-hero-icon').trim();
            
            console.log('CSS Variables:', {
                'hero-container': heroContainer,
                'hero-icon': heroIcon
            });
            
            // 更新显示
            document.querySelector('.info').innerHTML += `
                <div style="background: #f0f0f0; padding: 10px; margin-top: 10px;">
                    <strong>实际测量:</strong><br>
                    容器尺寸: ${containerRect.width}px × ${containerRect.height}px<br>
                    SVG尺寸: ${svgRect.width}px × ${svgRect.height}px<br>
                    CSS变量: --icon-hero-container = ${heroContainer}, --icon-hero-icon = ${heroIcon}
                </div>
            `;
        }
        
        // 页面加载后测量
        window.addEventListener('load', updateSizes);
    </script>
</body>
</html> 