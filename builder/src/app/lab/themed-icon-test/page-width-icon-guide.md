# 页面宽度感知图标适配最佳实践

## 概述

在 LitPage 项目中，页面宽度通过 HTML 类名 `page-width-wide` 和 `page-width-full` 来控制。这种设计模式需要图标系统能够感知页面宽度的变化并相应调整尺寸，确保在不同页面宽度模式下保持最佳的视觉效果。

## 🎯 设计理念

### 1. 页面宽度模式
- **普通宽度 (normal)**: 默认模式，容器最大宽度 80rem (1280px)
- **宽屏模式 (wide)**: `page-width-wide` 类，容器最大宽度 96rem (1536px)
- **满屏模式 (full)**: `page-width-full` 类，容器最大宽度 100%

### 2. 适配原则
- **视觉一致性**: 图标在不同页面宽度下保持相对比例
- **内容协调**: 图标尺寸与文字、间距等元素协调缩放
- **性能优先**: 使用高效的监听机制，避免不必要的重渲染

## 📐 尺寸规范体系

### 页面宽度感知的图标尺寸

```typescript
// 页面宽度模式定义
export type PageWidthMode = 'normal' | 'wide' | 'full';

// 各模式下的缩放比例
const PAGE_WIDTH_SCALES = {
  normal: 1.0,    // 基准尺寸
  wide: 1.125,    // 12.5% 增大
  full: 1.25      // 25% 增大
};
```

### 预设配置表

| 场景 | 普通宽度 | 宽屏模式 | 满屏模式 | 说明 |
|------|----------|----------|----------|------|
| **导航图标** | 44×26px | 48×28px | 52×30px | 触摸友好，适度缩放 |
| **功能图标** | 64×36px | 72×40px | 80×44px | 随页面宽度显著缩放 |
| **内联图标** | 20×16px | 22×17px | 24×18px | 跟随文字大小 |
| **列表图标** | 40×24px | 44×26px | 48×28px | 中等尺寸，平衡缩放 |
| **英雄图标** | 96×52px | 112×60px | 128×68px | 大型展示，大幅缩放 |
| **按钮图标** | 18×16px | 20×18px | 22×20px | 跟随按钮尺寸系统 |

## 💻 实现方案

### 1. 页面宽度检测 Hook

```typescript
export const usePageWidthMode = (): PageWidthMode => {
  const [pageWidthMode, setPageWidthMode] = useState<PageWidthMode>('normal');

  const detectPageWidthMode = useCallback(() => {
    const htmlElement = document.documentElement;
    if (htmlElement.classList.contains('page-width-full')) {
      setPageWidthMode('full');
    } else if (htmlElement.classList.contains('page-width-wide')) {
      setPageWidthMode('wide');
    } else {
      setPageWidthMode('normal');
    }
  }, []);

  useEffect(() => {
    // 初始检测
    detectPageWidthMode();

    // 监听 HTML 类名变化
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
          detectPageWidthMode();
        }
      });
    });

    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    });

    return () => observer.disconnect();
  }, [detectPageWidthMode]);

  return pageWidthMode;
};
```

### 2. 页面宽度感知图标 Hook

```typescript
export const usePageWidthIcon = (config: PageWidthIconConfig): IconSizeConfig => {
  const pageWidthMode = usePageWidthMode();
  
  return useMemo(() => {
    const defaultConfig: IconSizeConfig = { 
      container: 48, 
      icon: 28, 
      stroke: 2 
    };
    
    // 按页面宽度模式选择配置
    return config[pageWidthMode] || 
           config.normal || 
           defaultConfig;
  }, [pageWidthMode, config]);
};
```

### 3. 预设配置使用

```typescript
// 使用预设配置
const navigationSizes = usePageWidthPresetIcon('navigation');

// 自定义配置
const customSizes = usePageWidthIcon({
  normal: { container: 44, icon: 26, stroke: 2 },
  wide: { container: 48, icon: 28, stroke: 2 },
  full: { container: 52, icon: 30, stroke: 2 }
});
```

## 🎨 应用场景

### 1. 导航栏图标

```tsx
const NavigationIcon = ({ icon, label }) => {
  const iconSizes = usePageWidthPresetIcon('navigation');
  
  return (
    <button className="flex items-center gap-2 p-2">
      <ThemedSVGIcon
        icon={icon}
        theme="access"
        size={iconSizes.container}
        iconSize={iconSizes.icon}
        strokeWidth={iconSizes.stroke}
      />
      <span>{label}</span>
    </button>
  );
};
```

### 2. 功能卡片图标

```tsx
const FeatureCard = ({ feature }) => {
  const iconSizes = usePageWidthPresetIcon('feature');
  
  return (
    <div className="p-6 bg-white rounded-lg shadow">
      <ThemedSVGIcon
        icon={feature.icon}
        theme="security"
        size={iconSizes.container}
        iconSize={iconSizes.icon}
        strokeWidth={iconSizes.stroke}
        className="mb-4"
      />
      <h3 className="text-lg font-semibold">{feature.title}</h3>
      <p className="text-gray-600">{feature.description}</p>
    </div>
  );
};
```

### 3. 智能内联图标

```tsx
const SmartInlineIcon = ({ icon, children }) => {
  const iconSizes = useInlineIconSize(16);
  const textScale = useTextSizeScale();
  
  return (
    <p style={{ fontSize: `${textScale}rem` }}>
      {children}
      <ThemedSVGIcon
        icon={icon}
        theme="hosting"
        size={iconSizes.container}
        iconSize={iconSizes.icon}
        strokeWidth={iconSizes.stroke}
        className="inline-block align-text-bottom mx-1"
      />
    </p>
  );
};
```

## 🔧 高级特性

### 1. 文字缩放感知

```typescript
export const useTextSizeScale = (): number => {
  const pageWidthMode = usePageWidthMode();
  
  return useMemo(() => {
    switch (pageWidthMode) {
      case 'normal': return 1.0;    // body-base-size: 1rem (16px)
      case 'wide': return 1.1875;   // body-base-size: 1.1875rem (19px)
      case 'full': return 1.25;     // body-base-size: 1.25rem (20px)
      default: return 1.0;
    }
  }, [pageWidthMode]);
};
```

### 2. CSS 变量集成

```typescript
export const useCSSVariableIcon = (baseSizes: IconSizeConfig): IconSizeConfig => {
  const pageWidthMode = usePageWidthMode();
  
  return useMemo(() => {
    const scale = getPageWidthScale(pageWidthMode);
    
    return {
      container: Math.round(baseSizes.container * scale),
      icon: Math.round(baseSizes.icon * scale),
      stroke: baseSizes.stroke
    };
  }, [baseSizes, pageWidthMode]);
};
```

### 3. 混合适配策略

```typescript
export const useHybridIcon = (
  pageWidthConfig: PageWidthIconConfig,
  fallbackBreakpointConfig?: ResponsiveIconConfig
): IconSizeConfig => {
  const pageWidthMode = usePageWidthMode();
  
  return useMemo(() => {
    // 优先使用页面宽度配置
    const pageWidthResult = pageWidthConfig[pageWidthMode];
    if (pageWidthResult) {
      return pageWidthResult;
    }
    
    // 回退到断点配置
    // ... 断点检测逻辑
    
    return { container: 48, icon: 28, stroke: 2 };
  }, [pageWidthMode, pageWidthConfig, fallbackBreakpointConfig]);
};
```

## 📊 性能优化

### 1. MutationObserver 优化

```typescript
// 使用防抖优化频繁变化
const debouncedDetect = useMemo(
  () => debounce(detectPageWidthMode, 100),
  [detectPageWidthMode]
);

// 只监听 class 属性变化
observer.observe(document.documentElement, {
  attributes: true,
  attributeFilter: ['class'],
  subtree: false,
  childList: false
});
```

### 2. 缓存策略

```typescript
// 使用 useMemo 缓存计算结果
const iconSizes = useMemo(() => {
  return calculateIconSizes(pageWidthMode, config);
}, [pageWidthMode, config]);
```

### 3. 批量更新

```typescript
// 批量更新多个图标
const batchUpdateIcons = useCallback((newMode: PageWidthMode) => {
  // 使用 React 的批量更新机制
  startTransition(() => {
    setPageWidthMode(newMode);
  });
}, []);
```

## 🎯 最佳实践

### 1. 组件设计

```tsx
// ✅ 推荐: 封装页面宽度感知的图标组件
const PageAwareIcon = ({ 
  icon, 
  preset = 'feature', 
  theme = 'security',
  ...props 
}) => {
  const iconSizes = usePageWidthPresetIcon(preset);
  
  return (
    <ThemedSVGIcon
      icon={icon}
      theme={theme}
      {...iconSizes}
      {...props}
    />
  );
};

// ❌ 避免: 在每个使用处重复逻辑
const BadExample = () => {
  const pageMode = usePageWidthMode();
  const sizes = pageMode === 'full' ? { container: 80, icon: 44 } 
                : pageMode === 'wide' ? { container: 72, icon: 40 }
                : { container: 64, icon: 36 };
  
  return <ThemedSVGIcon {...sizes} />;
};
```

### 2. 类型安全

```typescript
// 严格的类型定义
interface PageWidthIconProps {
  preset: keyof typeof PAGE_WIDTH_PRESETS;
  icon: LucideIconType;
  theme?: 'security' | 'access' | 'hosting';
  className?: string;
}

// 类型保护
const isValidPreset = (preset: string): preset is keyof typeof PAGE_WIDTH_PRESETS => {
  return preset in PAGE_WIDTH_PRESETS;
};
```

### 3. 错误处理

```typescript
export const usePageWidthIcon = (config: PageWidthIconConfig): IconSizeConfig => {
  const pageWidthMode = usePageWidthMode();
  
  return useMemo(() => {
    try {
      const result = config[pageWidthMode] || config.normal;
      if (!result) {
        console.warn(`No configuration found for page width mode: ${pageWidthMode}`);
        return DEFAULT_ICON_CONFIG;
      }
      return result;
    } catch (error) {
      console.error('Error in usePageWidthIcon:', error);
      return DEFAULT_ICON_CONFIG;
    }
  }, [pageWidthMode, config]);
};
```

## 🧪 测试策略

### 1. 单元测试

```typescript
describe('usePageWidthMode', () => {
  it('should detect normal mode by default', () => {
    const { result } = renderHook(() => usePageWidthMode());
    expect(result.current).toBe('normal');
  });

  it('should detect wide mode when class is added', () => {
    document.documentElement.classList.add('page-width-wide');
    const { result } = renderHook(() => usePageWidthMode());
    expect(result.current).toBe('wide');
  });
});
```

### 2. 集成测试

```typescript
describe('PageAwareIcon', () => {
  it('should resize when page width changes', () => {
    const { container } = render(<PageAwareIcon icon={Star} preset="feature" />);
    
    // 默认尺寸
    expect(container.querySelector('svg')).toHaveAttribute('width', '64');
    
    // 切换到宽屏模式
    act(() => {
      document.documentElement.classList.add('page-width-wide');
    });
    
    expect(container.querySelector('svg')).toHaveAttribute('width', '72');
  });
});
```

## 📝 总结

页面宽度感知的图标系统通过以下方式实现最佳用户体验：

1. **智能检测**: 使用 MutationObserver 监听 HTML 类名变化
2. **预设配置**: 提供 6 种常用场景的预设配置
3. **灵活适配**: 支持自定义配置和混合适配策略
4. **性能优化**: 使用缓存、防抖等技术优化性能
5. **类型安全**: 完整的 TypeScript 支持
6. **易于维护**: 清晰的架构和错误处理

这套系统确保图标在 LitPage 的三种页面宽度模式下都能提供最佳的视觉效果和用户体验。 