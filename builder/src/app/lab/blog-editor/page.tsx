/**
 * 博客编辑器测试页面
 * 
 * 用于测试 BlogDetail 内容模型的表单功能
 */

'use client';

import React, { useState } from 'react';
import { BlockEditorShadcn } from '@/components/pageRender/BlockEditor/BlockEditorShadcn';
import blogDetailConfig from '@/content-schema/BlogDetail';

// 默认博客数据
const defaultBlogData = {
  title: "How to Build Amazing Web Applications",
  excerpt: "A comprehensive guide to building modern web applications with the latest technologies and best practices.",
  content: "# Introduction\n\nThis is the main content of your blog post. You can use Markdown formatting to structure your content.\n\n## Key Points\n\n- Point one\n- Point two\n- Point three\n\n## Conclusion\n\nWrap up your article with a compelling conclusion.",
  featuredImage: {
    url: "/images/blog/featured-image.jpg",
    alt: "Featured image for the blog post"
  },
  author: {
    name: "<PERSON>",
    avatar: "/images/avatars/author.jpg",
    bio: "Software engineer and technical writer passionate about web development."
  },
  category: "Technology",
  tags: ["web development", "javascript", "react"],
  readingTime: 5,
  seo: {
    metaTitle: "How to Build Amazing Web Applications | Your Blog",
    metaDescription: "Learn how to build modern web applications with this comprehensive guide covering the latest technologies and best practices."
  }
};

const BlogEditorTestPage = () => {
  const [formData, setFormData] = useState(defaultBlogData);

  const handleSave = (data: any) => {
    console.log('Saved blog data:', data);
    setFormData(data);
  };

  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-3xl font-bold text-foreground mb-8">Blog Editor Test - BlogDetail Configuration</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Form Editor */}
          <div className="bg-card rounded-lg shadow-lg p-6 border">
            <h2 className="text-xl font-semibold text-card-foreground mb-4">Blog Editor</h2>
            <BlockEditorShadcn
              config={blogDetailConfig}
              initialData={formData}
              onSave={handleSave}
            />
          </div>
          
          {/* JSON Output */}
          <div className="bg-card rounded-lg shadow-lg p-6 border">
            <h2 className="text-xl font-semibold text-card-foreground mb-4">Blog Data Preview</h2>
            <div className="space-y-4">
              {/* Quick Preview */}
              <div className="bg-muted p-4 rounded-md">
                <h3 className="font-semibold text-lg mb-2">{formData.title}</h3>
                <p className="text-sm text-muted-foreground mb-2">{formData.excerpt}</p>
                <div className="flex flex-wrap gap-1 mb-2">
                  {formData.tags.map((tag: string, index: number) => (
                    <span key={index} className="bg-primary/10 text-primary px-2 py-1 rounded-md text-xs">
                      {tag}
                    </span>
                  ))}
                </div>
                <p className="text-xs text-muted-foreground">
                  By {formData.author.name} • {formData.readingTime} min read • {formData.category}
                </p>
              </div>
              
              {/* Raw JSON */}
              <div>
                <h3 className="font-semibold mb-2">Raw JSON Data</h3>
                <pre className="bg-muted p-4 rounded-md overflow-auto text-sm text-muted-foreground font-mono max-h-96">
                  {JSON.stringify(formData, null, 2)}
                </pre>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BlogEditorTestPage;