'use client';

import React, { useState } from 'react';
import { ImageUploadWidget } from '@/components/form-upload/ImageUploadWidget';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, XCircle, Settings, Upload } from 'lucide-react';

interface UploadEvent {
  id: string;
  type: 'success' | 'error';
  message: string;
  timestamp: number;
  imageUrl?: string;
}

export default function FormUploadPage() {
  const [domain, setDomain] = useState('');
  const [events, setEvents] = useState<UploadEvent[]>([]);
  const [isConfigOpen, setIsConfigOpen] = useState(false);
  
  const addEvent = (type: 'success' | 'error', message: string, imageUrl?: string) => {
    const event: UploadEvent = {
      id: Math.random().toString(36).substr(2, 9),
      type,
      message,
      timestamp: Date.now(),
      imageUrl
    };
    
    setEvents(prev => [event, ...prev.slice(0, 9)]); // Keep latest 10 events
  };
  
  const handleUploadSuccess = (imageUrl: string) => {
    addEvent('success', `Image upload successful: ${imageUrl}`, imageUrl);
  };
  
  const handleUploadError = (error: string) => {
    addEvent('error', `Upload failed: ${error}`);
  };
  
  const clearEvents = () => {
    setEvents([]);
  };
  
  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };
  
  return (
    <div className="min-h-screen bg-background">
      {/* Page Header */}
      <div className="border-b bg-muted/30">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
                <Upload className="w-8 h-8" />
                Local Image Upload Test
              </h1>
              <p className="text-muted-foreground mt-2">
                Test the functionality and performance of local image upload component
              </p>
            </div>
            <Button
              onClick={() => setIsConfigOpen(!isConfigOpen)}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Settings className="w-4 h-4" />
              Settings
            </Button>
          </div>
        </div>
      </div>
      
      <div className="container mx-auto px-4 py-8">
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Content Area */}
          <div className="lg:col-span-2 space-y-6">
            {/* Configuration Panel */}
            {isConfigOpen && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Configuration Options</CardTitle>
                  <CardDescription>
                    Customize upload service configuration parameters
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="domain">Custom Domain (Optional)</Label>
                    <Input
                      id="domain"
                      placeholder="e.g: my-domain"
                      value={domain}
                      onChange={(e) => setDomain(e.target.value)}
                      className="font-mono"
                    />
                    <p className="text-xs text-muted-foreground">
                      Leave empty to use default domain. Production will use: {domain || 'default'}.imgpipe.io
                    </p>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs">
                      Development
                    </Badge>
                    <span className="text-sm font-mono">localhost:7001</span>
                  </div>
                </CardContent>
              </Card>
            )}
            
            {/* Upload Component */}
            <ImageUploadWidget
              domain={domain || undefined}
              onUploadSuccess={handleUploadSuccess}
              onUploadError={handleUploadError}
            />
          </div>
          
          {/* Sidebar - Event Log */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg">Event Log</CardTitle>
                    <CardDescription>
                      Real-time display of upload events
                    </CardDescription>
                  </div>
                  {events.length > 0 && (
                    <Button
                      onClick={clearEvents}
                      variant="ghost"
                      size="sm"
                      className="text-xs"
                    >
                      Clear
                    </Button>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                {events.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <Upload className="w-8 h-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">No upload events yet</p>
                  </div>
                ) : (
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {events.map((event, index) => (
                      <div key={event.id}>
                        <div className="flex items-start gap-3">
                          <div className="flex-shrink-0 mt-0.5">
                            {event.type === 'success' ? (
                              <CheckCircle className="w-4 h-4 text-green-500" />
                            ) : (
                              <XCircle className="w-4 h-4 text-red-500" />
                            )}
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                              <Badge 
                                variant={event.type === 'success' ? 'default' : 'destructive'}
                                className="text-xs"
                              >
                                {event.type === 'success' ? 'Success' : 'Failed'}
                              </Badge>
                              <span className="text-xs text-muted-foreground">
                                {formatTime(event.timestamp)}
                              </span>
                            </div>
                            <p className="text-sm break-all">
                              {event.message}
                            </p>
                            {event.imageUrl && (
                              <div className="mt-2">
                                <img
                                  src={event.imageUrl}
                                  alt="Uploaded image"
                                  className="w-full max-w-32 h-20 object-cover rounded border"
                                  onError={(e) => {
                                    (e.target as HTMLImageElement).style.display = 'none';
                                  }}
                                />
                              </div>
                            )}
                          </div>
                        </div>
                        {index < events.length - 1 && (
                          <Separator className="mt-3" />
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
            
            {/* System Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">System Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="text-sm space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Environment:</span>
                    <Badge variant="outline">
                      {process.env.NODE_ENV === 'development' ? 'Development' : 'Production'}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Upload Service:</span>
                    <span className="font-mono text-xs">
                      {process.env.NODE_ENV === 'development' 
                        ? 'localhost:7001' 
                        : `${domain || 'default'}.imgpipe.io`
                      }
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Max File Size:</span>
                    <span>50MB</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Supported Formats:</span>
                    <span>7 types</span>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            {/* Usage Tips */}
            <Alert>
              <Upload className="h-4 w-4" />
              <AlertDescription className="text-sm">
                <strong>Test Tips:</strong> Try dragging images to the upload area. Supported formats include JPEG, PNG, WebP, AVIF, GIF, TIFF, SVG.
              </AlertDescription>
            </Alert>
          </div>
        </div>
      </div>
    </div>
  );
}