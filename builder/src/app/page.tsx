import Link from 'next/link';

export default function Page() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-8">
          Builder 测试页面
        </h1>
        
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link 
              href="/themed-icon-test"
              className="inline-block bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
            >
              ThemedIcon 组件测试
            </Link>
            
            <Link 
              href="/picture-test"
              className="inline-block bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
            >
              Picture 组件测试
            </Link>
          </div>
          
          <div className="text-gray-600 dark:text-gray-400 mt-6 space-y-2">
            <p><strong>ThemedIcon 测试</strong>：响应式图标组件，支持多主题和多尺寸</p>
            <p><strong>Picture 测试</strong>：智能图片组件，支持响应式、艺术指导、媒体查询等</p>
          </div>
        </div>
      </div>
    </div>
  );
}