'use client';

import { useState } from 'react';
import { motion } from 'motion/react';
import { 
  LayoutTemplate, 
  Wrench, 
  FileText, 
  Bookmark, 
  Star 
} from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * Category section component
 * Displays horizontally arranged category tags
 */
export function CategorySection() {
  const [activeCategory, setActiveCategory] = useState('my-websites');

  const categories = [
    {
      id: 'my-websites',
      name: 'My Websites',
      icon: <Bookmark size={16} />
    },
    {
      id: 'templates',
      name: 'Templates',
      icon: <LayoutTemplate size={16} />
    },
    {
      id: 'tools',
      name: 'Tools',
      icon: <Wrench size={16} />
    },
    {
      id: 'resources',
      name: 'Resources',
      icon: <FileText size={16} />
    },
    {
      id: 'bookmarks',
      name: 'Bookmarks',
      icon: <Bookmark size={16} />
    },
    {
      id: 'favorites',
      name: 'Favorites',
      icon: <Star size={16} />
    }
  ];

  return (
    <motion.div 
      className="w-full mb-10"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5, delay: 0.7 }}
    >
      <div className="flex flex-wrap justify-center gap-3">
        {categories.map((category, index) => (
          <motion.button
            key={category.id}
            className={cn(
              "flex items-center gap-2 px-4 py-2 rounded-full transition-all",
              "text-sm font-medium",
              activeCategory === category.id 
                ? "bg-blue-100 text-blue-700 dark:bg-blue-900/40 dark:text-blue-300" 
                : "bg-white text-slate-600 hover:bg-slate-100 dark:bg-slate-800 dark:text-slate-300 dark:hover:bg-slate-700"
            )}
            onClick={() => setActiveCategory(category.id)}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3, delay: 0.8 + index * 0.05 }}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <span className={cn(
              activeCategory === category.id 
                ? "text-blue-600 dark:text-blue-400" 
                : "text-slate-500 dark:text-slate-400"
            )}>
              {category.icon}
            </span>
            {category.name}
          </motion.button>
        ))}
      </div>
    </motion.div>
  );
}
