'use client';

import { motion } from 'motion/react';
import { cn } from '@/lib/utils';

/**
 * Logo section component
 * Displays the lit.page logo and brand name
 */
export function LogoSection() {
  return (
    <motion.div 
      className="flex flex-col items-center mb-8"
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="relative w-16 h-16 mb-3">
        <div className={cn(
          "absolute inset-0 rounded-full bg-gradient-to-r from-blue-500 to-purple-600",
          "flex items-center justify-center text-white font-bold text-2xl"
        )}>
          <span className="relative z-10">lit</span>
          <div className="absolute top-0 left-0 w-full h-full rounded-full bg-white/20 blur-sm"></div>
        </div>
      </div>
      <motion.h2 
        className="text-xl font-medium text-slate-800 dark:text-slate-200"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.2, duration: 0.5 }}
      >
        lit.page
      </motion.h2>
    </motion.div>
  );
}
