'use client';

import { Skeleton } from "@/components/ui/skeleton";

/**
 * Skeleton loading component for dashboard
 * Shows placeholder elements while content is loading
 */
export function DashboardSkeleton() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-50 to-white dark:from-slate-950 dark:to-slate-900 flex flex-col items-center px-4 py-8 md:py-12">
      <div className="w-full max-w-5xl mx-auto flex flex-col items-center">
        {/* Logo skeleton */}
        <Skeleton className="h-16 w-16 rounded-full mb-4" />
        <Skeleton className="h-6 w-32 mb-8" />
        
        {/* Title skeleton */}
        <Skeleton className="h-10 w-64 mb-2" />
        <Skeleton className="h-6 w-80 mb-10" />
        
        {/* AI input skeleton */}
        <Skeleton className="h-14 w-full max-w-2xl rounded-full mb-6" />
        
        {/* Prompt guide skeleton */}
        <div className="flex flex-col items-center mb-10 w-full max-w-xl">
          <Skeleton className="h-5 w-40 mb-4" />
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 w-full">
            <Skeleton className="h-12 w-full rounded-md" />
            <Skeleton className="h-12 w-full rounded-md" />
            <Skeleton className="h-12 w-full rounded-md" />
          </div>
        </div>
        
        {/* Category skeleton */}
        <div className="flex flex-wrap justify-center gap-3 mb-10">
          {Array(5).fill(0).map((_, i) => (
            <Skeleton key={i} className="h-10 w-24 rounded-full" />
          ))}
        </div>
        
        {/* Website list skeleton */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 w-full">
          {Array(10).fill(0).map((_, i) => (
            <div key={i} className="flex flex-col">
              <Skeleton className="h-32 w-full rounded-t-lg" />
              <Skeleton className="h-6 w-3/4 mt-3 mb-1" />
              <Skeleton className="h-4 w-full mb-3" />
              <div className="flex gap-2">
                <Skeleton className="h-8 w-8 rounded-full" />
                <Skeleton className="h-8 w-8 rounded-full" />
                <Skeleton className="h-8 w-8 rounded-full" />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
