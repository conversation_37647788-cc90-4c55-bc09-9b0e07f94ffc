'use client';

import { motion } from 'motion/react';
import { ArrowUp } from 'lucide-react';
import { Button } from '@/components/ui/button';

/**
 * Prompt guide section component
 * Provides example prompts to help users understand how to use the AI feature
 */
export function PromptGuideSection() {
  const promptExamples = [
    {
      text: "Photo Portfolio"
    },
    {
      text: "SaaS Landing"
    },
    {
      text: "Minimal Blog"
    },
    {
      text: "Online Shop"
    }
  ];

  const handleExampleClick = (example: string) => {
    // Here you would typically set the input value
    console.log(`Selected example: ${example}`);
  };

  return (
    <motion.div 
      className="w-full max-w-xl mb-6 -mt-2"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5, delay: 0.5 }}
    >
      <div className="flex flex-wrap justify-center gap-2 w-full">
        {promptExamples.map((example, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.6 + index * 0.1 }}
          >
            <Button
              variant="outline"
              className="h-auto py-1.5 px-2 justify-start text-left text-xs border-slate-200 dark:border-slate-700 hover:bg-slate-100 dark:hover:bg-slate-800 transition-all"
              onClick={() => handleExampleClick(example.text)}
            >
              <span className="truncate">{example.text}</span>
              <span className="ml-1.5 text-blue-500 dark:text-blue-400">
                <ArrowUp size={12} />
              </span>
            </Button>
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
}
