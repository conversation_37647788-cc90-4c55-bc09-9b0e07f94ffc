'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'motion/react';
import { 
  Eye, 
  Edit, 
  Share2, 
  MoreHorizon<PERSON>,
  Clock
} from 'lucide-react';
import { 
  <PERSON>, 
  CardContent, 
  CardFooter, 
  CardHeader 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from '@/lib/utils';

interface Website {
  id: string;
  name: string;
  description?: string;
  thumbnail?: string;
  domain: string;
  createdAt: string;
}

interface WebsiteCardProps {
  website: Website;
  index: number;
}

/**
 * Website card component
 * Displays a single website project card
 */
export function WebsiteCard({ website, index }: WebsiteCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const router = useRouter();

  // Format date to relative time (e.g., "2 days ago")
  const formatRelativeTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} min ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    return `${Math.floor(diffInSeconds / 86400)} days ago`;
  };

  // Generate a placeholder gradient background if no thumbnail
  const placeholderStyle = {
    background: `linear-gradient(135deg, hsl(${(index * 40) % 360}, 70%, 60%), hsl(${((index * 40) + 60) % 360}, 70%, 50%))`
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, delay: 0.9 + index * 0.05 }}
      whileHover={{ y: -5 }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Card className="overflow-hidden border border-slate-200 dark:border-slate-700 h-full flex flex-col">
        <div className="relative h-32 overflow-hidden">
          <div 
            className="w-full h-full"
            style={placeholderStyle}
          >
            {/* If you have actual thumbnails, uncomment this */}
            {/* {website.thumbnail && (
              <Image 
                src={website.thumbnail} 
                alt={website.name} 
                fill 
                className="object-cover"
              />
            )} */}
          </div>
          <div className={cn(
            "absolute inset-0 bg-gradient-to-t from-black/60 to-transparent transition-opacity",
            isHovered ? "opacity-100" : "opacity-0"
          )}>
            <div className="absolute bottom-2 right-2 flex space-x-1">
              <a
                href={`https://${website.domain}.lit.page`}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex h-8 w-8 items-center justify-center rounded-full bg-white/20 text-white hover:bg-white/30"
              >
                <Eye size={14} />
              </a>
              <Button 
                size="icon" 
                variant="ghost" 
                className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                onClick={() => router.push(`/site/${website.id}`)}
              >
                <Edit size={14} />
              </Button>
              <Button 
                size="icon" 
                variant="ghost" 
                className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
              >
                <Share2 size={14} />
              </Button>
            </div>
          </div>
        </div>
        
        <CardContent className="flex-grow p-4">
          <h3 className="font-medium text-slate-900 dark:text-white mb-1 truncate">{website.name}</h3>
          <p className="text-sm text-slate-500 dark:text-slate-400 line-clamp-2">
            {website.description ?? ''}
          </p>
        </CardContent>
        
        <CardFooter className="p-4 pt-0 flex items-center justify-between">
          <div className="flex items-center text-xs text-slate-500 dark:text-slate-400">
            <Clock size={12} className="mr-1" />
            <span>{formatRelativeTime(website.createdAt)}</span>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                size="icon" 
                variant="ghost" 
                className="h-8 w-8 rounded-full hover:bg-slate-100 dark:hover:bg-slate-800"
              >
                <MoreHorizontal size={14} />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>View</DropdownMenuItem>
              <DropdownMenuItem>Edit</DropdownMenuItem>
              <DropdownMenuItem>Duplicate</DropdownMenuItem>
              <DropdownMenuItem className="text-red-600 dark:text-red-400">Delete</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </CardFooter>
      </Card>
    </motion.div>
  );
}
