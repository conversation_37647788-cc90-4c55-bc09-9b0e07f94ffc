'use client';

import { LogoSection } from './logo-section';
import { TitleSection } from './title-section';
import { AIInputSection } from './ai-input-section';
import { PromptGuideSection } from './prompt-guide-section';
import { CategorySection } from './category-section';
import { WebsiteListSection } from './website-list-section';
import { motion } from 'motion/react';

/**
 * Main dashboard content component
 * Organizes all dashboard sections in a centered layout
 */
export function DashboardContent() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-50 to-white dark:from-slate-950 dark:to-slate-900 flex flex-col items-center px-4 py-8 md:py-12">
      <div className="w-full max-w-5xl mx-auto flex flex-col items-center">
        <LogoSection />
        <TitleSection />
        <AIInputSection />
        <PromptGuideSection />
        <motion.div 
          className="w-full border-t mx-auto my-8 max-w-[35rem]" 
          style={{
            borderImageSlice: 1,
            borderImageSource: 'radial-gradient(circle at 50% 0%, rgba(59, 130, 246, 0.5) 0%, rgba(59, 130, 246, 0) 100%)'
          }}
          initial={{ opacity: 0, width: "0%" }}
          animate={{ opacity: 1, width: "100%" }}
          transition={{ duration: 0.8, delay: 0.7 }}
        />
        <CategorySection />
        <WebsiteListSection />
      </div>
    </div>
  );
}
