'use client';

import { useWebsiteList } from '@/lib/hooks/use-website-list';
import { motion } from 'motion/react';
import { 
  Eye, 
  Edit, 
  Share2, 
  MoreHorizontal 
} from 'lucide-react';
import { 
  <PERSON>, 
  Card<PERSON>ontent, 
  <PERSON>Footer, 
  CardHeader 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { WebsiteCard } from './website-card';

/**
 * Website list section component
 * Displays a grid of website projects
 */
export function WebsiteListSection() {
  // Fetch real website list
  const { data: websites = [], isLoading, isError } = useWebsiteList();

  if (isLoading) {
    return <div className="text-center py-8">Loading...</div>;
  }

  if (isError) {
    return <div className="text-center py-8 text-red-500">Error: Failed to fetch websites.</div>;
  }

  return (
    <motion.div 
      className="w-full"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5, delay: 0.9 }}
    >
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
        {websites.map((website, index) => (
          <WebsiteCard 
            key={website.id} 
            website={website} 
            index={index} 
          />
        ))}
      </div>
    </motion.div>
  );
}
