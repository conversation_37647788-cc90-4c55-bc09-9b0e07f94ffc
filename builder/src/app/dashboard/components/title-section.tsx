'use client';

import { motion } from 'motion/react';

/**
 * Title section component
 * Displays the main title and subtitle
 */
export function TitleSection() {
  return (
    <motion.div 
      className="text-center mb-10"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.3 }}
    >
      <h1 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-3">
        Create Beautiful Websites with AI
      </h1>
      <p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl">
        Build and launch stunning websites in minutes, no coding required
      </p>
    </motion.div>
  );
}
