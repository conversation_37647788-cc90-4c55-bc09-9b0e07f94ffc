'use client';

import { useState, useEffect, useRef } from 'react';
import { motion } from 'motion/react';
import { Send, ArrowUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import Typewriter from 'typewriter-effect';

/**
 * AI Input section component
 * Provides a large input field for users to enter their requirements
 */
export function AIInputSection() {
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim()) return;
    
    // Simulate loading state
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      setInput('');
      // Here you would typically handle the actual submission
    }, 1500);
  };

  // Focus textarea when user clicks on the typewriter text
  const handleTypewriterClick = () => {
    if (textareaRef.current) {
      textareaRef.current.focus();
    }
  };

  return (
    <motion.div 
      className="w-full max-w-2xl mb-8"
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5, delay: 0.4 }}
    >
      <form onSubmit={handleSubmit} className="relative">
        <Textarea
          ref={textareaRef}
          placeholder=""
          className="w-full min-h-[120px] p-4 text-base rounded-3xl border-slate-300 dark:border-slate-700 shadow-sm focus:ring-2 focus:ring-blue-500 dark:bg-slate-800 dark:text-white resize-none"
          value={input}
          onChange={(e) => setInput(e.target.value)}
          disabled={isLoading}
        />
        
        {/* Typewriter effect for placeholder */}
        {!input && (
          <div 
            className="absolute top-4 left-4 text-slate-400 dark:text-slate-500 pointer-events-none"
            onClick={handleTypewriterClick}
          >
            <div className="typewriter-container">
              <Typewriter
                options={{
                  strings: [
                    'Create a portfolio website for a photographer...',
                    'Build a landing page for my SaaS product...',
                    'Design a blog with a minimalist style...',
                    'Make an e-commerce store for handmade products...'
                  ],
                  autoStart: true,
                  loop: true,
                  delay: 50,
                  deleteSpeed: 30,
                }}
              />
            </div>
          </div>
        )}
        
        <Button 
          type="submit" 
          size="icon" 
          className="absolute right-3 bottom-3 h-10 w-10 rounded-full bg-blue-600 hover:bg-blue-700 text-white"
          disabled={isLoading || !input.trim()}
        >
          {isLoading ? (
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            >
              <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full" />
            </motion.div>
          ) : (
            <Send size={18} />
          )}
        </Button>
      </form>
    </motion.div>
  );
}
