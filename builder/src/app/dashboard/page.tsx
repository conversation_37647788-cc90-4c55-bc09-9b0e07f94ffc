import { Suspense } from 'react'
import { DashboardContent } from './components/dashboard-content'
import { StarterDashboardContent } from '@/components/starter/StarterDashboardContent'
import { DashboardSkeleton } from './components/dashboard-skeleton'
import { ErrorBoundary } from '@/components/error-boundary'

/**
 * Modern Dashboard Page
 * Temporarily using StarterDashboardContent for the simplified starter plan experience
 */
export default function Page() {
  // TODO: Add user plan detection logic here
  // const { userPlan } = useUserPlan();
  // const isStarterPlan = userPlan === 'starter';
  const isStarterPlan = true; // Temporarily set to true for testing

  return (
    <ErrorBoundary>
      <Suspense fallback={<DashboardSkeleton />}>
        {isStarterPlan ? (
          <StarterDashboardContent />
        ) : (
          <DashboardContent />
        )}
      </Suspense>
    </ErrorBoundary>
  )
}
