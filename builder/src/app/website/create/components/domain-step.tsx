import React from 'react'
import { UseFormReturn } from 'react-hook-form'
import { FormData } from './types'
import { useValidateDomain } from '@/lib/hooks/use-website'
import { Input } from '@/components/ui/input'
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { cn } from '@/lib/utils'
import { AlertTriangle } from 'lucide-react'

interface DomainStepProps {
  form: UseFormReturn<FormData>
}

export function DomainStep({ form }: DomainStepProps) {
  const validateDomain = useValidateDomain()
  const domain = form.watch('domain')

  return (
    <div className="space-y-8">
      <FormField
        control={form.control}
        name="domain"
        rules={{
          required: 'Domain is required',
          validate: {
            format: (value) => {
              if (!value) return true
              const regex = /^[a-z0-9][a-z0-9-]{1,61}[a-z0-9]$/
              return regex.test(value) || 'Invalid domain format'
            }
          }
        }}
        render={({ field }) => (
          <FormItem>
            <FormLabel className="text-base font-semibold">Domain Name</FormLabel>
            <div className="flex">
              <FormControl>
                <Input
                  placeholder="Enter domain"
                  className={cn(
                    "text-base h-12 rounded-r-none bg-background",
                    validateDomain.isPending && "pr-8"
                  )}
                  {...field}
                  onChange={async (e) => {
                    const value = e.target.value.toLowerCase();
                    field.onChange(value);
                    
                    // 输入时进行格式验证
                    const regex = /^[a-z0-9][a-z0-9-]{1,61}[a-z0-9]$/;
                    if (!regex.test(value)) {
                      form.setError('domain', {
                        message: 'Invalid domain format'
                      });
                      return;
                    }

                    // 当输入长度大于等于3时，进行域名可用性验证
                    if (value && value.length >= 3) {
                      try {
                        await validateDomain.mutateAsync(value);
                        form.clearErrors('domain');
                      } catch (error) {
                        form.setError('domain', {
                          message: error instanceof Error ? error.message : 'Invalid domain'
                        });
                      }
                    }
                  }}
                  onBlur={async (e) => {
                    field.onBlur();
                    const value = e.target.value;
                    
                    // 失去焦点时再次验证
                    if (value) {
                      const regex = /^[a-z0-9][a-z0-9-]{1,61}[a-z0-9]$/;
                      if (!regex.test(value)) {
                        form.setError('domain', {
                          message: 'Invalid domain format'
                        });
                        return;
                      }

                      if (value.length >= 3) {
                        try {
                          await validateDomain.mutateAsync(value);
                          form.clearErrors('domain');
                        } catch (error) {
                          form.setError('domain', {
                            message: error instanceof Error ? error.message : 'Invalid domain'
                          });
                        }
                      }
                    }
                  }}
                  autoFocus
                />
              </FormControl>
              <div className="flex items-center px-4 border border-l-0 rounded-r-md bg-muted text-muted-foreground">
                .lit.page
              </div>
            </div>
            <div className="mt-1.5 space-y-1 text-sm">
              {validateDomain.isPending && (
                <p className="text-muted-foreground">
                  Checking availability...
                </p>
              )}
              {validateDomain.isSuccess && !form.formState.errors.domain && domain && (
                <p className="text-green-600 dark:text-green-500">
                  ✓ Domain is available
                </p>
              )}
              <p className="text-amber-600 dark:text-amber-500 font-medium border border-amber-200 dark:border-amber-800 bg-amber-50 dark:bg-amber-950/30 p-2 rounded flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-amber-700 dark:text-amber-400 flex-shrink-0" />
                <span><span className="font-bold">WARNING:</span> Domain cannot be changed after creation.</span>
              </p>
              <p className="text-muted-foreground">
                Only lowercase letters, numbers, and hyphens (-) are allowed
              </p>
            </div>
            <FormMessage />
          </FormItem>
        )}
      />

      <div className="rounded-lg border p-4 bg-background">
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <div className="w-3 h-3 rounded-full bg-green-500" />
          <span>Domain Preview</span>
        </div>
        <div className="mt-3 text-lg font-medium">
          https://{domain || 'your-domain'}.lit.page
        </div>
      </div>
    </div>
  )
}
