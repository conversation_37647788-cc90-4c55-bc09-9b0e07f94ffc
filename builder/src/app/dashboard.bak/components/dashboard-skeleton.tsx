import { Skeleton } from "@/components/ui/skeleton";

/**
 * 仪表板加载骨架屏
 */
export function DashboardSkeleton() {
  return (
    <div className="flex h-full w-full">
      {/* 左侧网站列表骨架屏 */}
      <div className="w-72 flex-shrink-0 border-r bg-background">
        <div className="p-4 border-b">
          <Skeleton className="h-8 w-3/4 mb-4" />
          <Skeleton className="h-10 w-full" />
        </div>
        <div className="p-2">
          {Array(5).fill(0).map((_, index) => (
            <div key={index} className="p-3 mb-2">
              <Skeleton className="h-5 w-3/4 mb-2" />
              <Skeleton className="h-4 w-1/2 mb-2" />
              <Skeleton className="h-3 w-1/3" />
            </div>
          ))}
        </div>
      </div>

      {/* 右侧内容区域骨架屏 */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <div className="flex items-center justify-between p-4 border-b">
          <Skeleton className="h-6 w-40" />
          <div className="flex space-x-2">
            <Skeleton className="h-8 w-24" />
            <Skeleton className="h-8 w-24" />
          </div>
        </div>
        <div className="flex-1 p-8">
          <div className="grid grid-cols-3 gap-4 mb-8">
            {Array(6).fill(0).map((_, index) => (
              <div key={index} className="border rounded-lg p-4">
                <Skeleton className="h-4 w-3/4 mb-3" />
                <Skeleton className="h-3 w-1/2 mb-2" />
                <Skeleton className="h-3 w-full mb-2" />
                <Skeleton className="h-3 w-2/3" />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
