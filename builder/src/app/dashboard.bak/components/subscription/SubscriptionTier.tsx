"use client"

import { UserProfile } from "@/lib/hooks/use-user-profile"

interface SubscriptionTierProps {
  user: UserProfile | null;
  websiteCount?: number;
}

/**
 * 订阅等级提示组件
 *
 * 显示用户当前的订阅计划信息和使用限制
 */
export function SubscriptionTier({ user, websiteCount = 0 }: SubscriptionTierProps) {
  if (!user) return null;

  return (
    <div className="rounded-md overflow-hidden border border-border shadow-sm">
      {user.subscriptionLevel === 'PRO' ? (
        <div className="bg-green-50 dark:bg-green-950/20 px-4 py-3 text-sm">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center flex-shrink-0">
              <span className="inline-block w-3 h-3 rounded-full bg-green-500"></span>
            </div>
            <div>
              <p className="font-medium text-green-700 dark:text-green-400">Pro Plan</p>
              <p className="text-xs text-green-600/70 dark:text-green-500/70 mt-0.5">Unlimited websites and pages</p>
            </div>
          </div>
        </div>
      ) : user.subscriptionLevel === 'BUSINESS' ? (
        <div className="bg-purple-50 dark:bg-purple-950/20 px-4 py-3 text-sm">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center flex-shrink-0">
              <span className="inline-block w-3 h-3 rounded-full bg-purple-500"></span>
            </div>
            <div>
              <p className="font-medium text-purple-700 dark:text-purple-400">Business Plan</p>
              <p className="text-xs text-purple-600/70 dark:text-purple-500/70 mt-0.5">Unlimited websites with advanced features</p>
            </div>
          </div>
        </div>
      ) : (
        <>
          <div className="px-4 py-3 bg-muted/50">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center flex-shrink-0">
                <span className="inline-block w-3 h-3 rounded-full bg-blue-500"></span>
              </div>
              <div>
                <p className="font-medium text-sm">Free Plan</p>
                <div className="text-xs text-muted-foreground mt-0.5 space-y-0.5">
                  <p><span className={websiteCount >= 1 ? "text-amber-600 font-medium" : ""}>{websiteCount}/1</span> website</p>
                  <p>Limited to 3 pages per website</p>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-r from-blue-50 via-blue-100/50 to-blue-50 dark:from-blue-950/20 dark:via-blue-900/20 dark:to-blue-950/20 px-4 py-3 border-t border-border">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium text-sm text-foreground">Upgrade to Pro</p>
                <p className="text-xs text-muted-foreground mt-1">Get unlimited websites and pages</p>
              </div>
              <a
                href="/pricing"
                className="text-xs bg-primary hover:bg-primary/90 text-primary-foreground px-3 py-1.5 rounded-md transition-colors font-medium shadow-sm"
              >
                Upgrade
              </a>
            </div>
          </div>
        </>
      )}
    </div>
  )
}
