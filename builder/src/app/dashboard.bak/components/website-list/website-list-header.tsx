import { Search, X } from "lucide-react";
import { Input } from "@/components/ui/input";
import { useState, useRef } from "react";
import { cn } from "@/lib/utils";

interface WebsiteListHeaderProps {
  onSearch: (query: string) => void;
}

/**
 * Header component for the website list with search functionality
 */
export function WebsiteListHeader({
  onSearch
}: WebsiteListHeaderProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    onSearch(value);
  };

  // Clear search input
  const handleClearSearch = () => {
    setSearchQuery("");
    onSearch("");
    searchInputRef.current?.focus();
  };

  return (
    <div className="p-4 border-b sticky top-0 bg-background z-10">
      {/* Header with title */}
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold">Websites</h2>
      </div>

      {/* Search input with clear button */}
      <div className={cn(
        "relative transition-all duration-200",
        isSearchFocused ? "ring-2 ring-primary/20 rounded-md" : ""
      )}>
        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
          <Search className="h-4 w-4 text-muted-foreground" />
        </div>

        <Input
          ref={searchInputRef}
          type="text"
          placeholder="Search websites..."
          value={searchQuery}
          onChange={handleSearchChange}
          onFocus={() => setIsSearchFocused(true)}
          onBlur={() => setIsSearchFocused(false)}
          className="pl-10 pr-10 w-full"
        />

        {searchQuery && (
          <button
            onClick={handleClearSearch}
            className="absolute inset-y-0 right-0 flex items-center pr-3 text-muted-foreground hover:text-foreground"
            aria-label="Clear search"
          >
            <X className="h-4 w-4" />
          </button>
        )}
      </div>
    </div>
  );
}
