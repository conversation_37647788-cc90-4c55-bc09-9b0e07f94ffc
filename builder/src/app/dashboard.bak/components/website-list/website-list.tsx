import { useState, useEffect } from "react";
import { useWebsiteList } from "@/lib/hooks/use-website-list";
import { WebsiteListHeader } from "./website-list-header";
import { WebsiteListItem } from "./website-list-item";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { useRouter, useSearchParams } from "next/navigation";
import { Website } from "../../types";
import { Logger } from "@/lib/utils/logger";
import { Button } from "@/components/ui/button";
import { PlusIcon } from "lucide-react";
import { useUserProfile } from "@/lib/hooks/use-user-profile";

const logger = new Logger('WebsiteList');

interface WebsiteListProps {
  className?: string;
  onWebsiteSelect?: (websiteId: string) => void;
  onWebsiteVisit?: (domain: string) => void;
}

/**
 * Component to display the list of websites with search and selection functionality
 */
export function WebsiteList({
  className,
  onWebsiteSelect,
  onWebsiteVisit
}: WebsiteListProps) {
  const { data: websites, isLoading, error } = useWebsiteList();
  const [filteredWebsites, setFilteredWebsites] = useState<Website[]>([]);
  const [selectedWebsiteId, setSelectedWebsiteId] = useState<string | null>(null);
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useUserProfile();

  // Initialize selected website from URL query parameter
  useEffect(() => {
    const websiteId = searchParams?.get('websiteId');
    if (websiteId) {
      setSelectedWebsiteId(websiteId);
    }
  }, [searchParams]);

  // 添加默认的 actions 字段
  const addDefaultActions = (websites: Website[]): Website[] => {
    return websites.map(website => {
      if (!website.actions || !Array.isArray(website.actions) || website.actions.length === 0) {
        // 根据网站的属性添加默认的 actions
        // 所有网站都应该有编辑、访问和预览操作
        const defaultActions = ['edit', 'visit', 'preview', 'publish'];

        // 如果网站有自定义域名，添加设置操作
        if (website.domain) {
          defaultActions.push('settings');
        }

        // 如果网站有多种语言，添加复制操作
        if (website.supportedLanguages && website.supportedLanguages.length > 1) {
          defaultActions.push('duplicate');
        }

        // 所有网站都应该可以删除
        defaultActions.push('delete');

        console.log(`Adding default actions for website ${website.id}:`, defaultActions);

        return {
          ...website,
          actions: defaultActions
        };
      }
      return website;
    });
  };

  // Update filtered websites when data changes or search is performed
  useEffect(() => {
    if (websites) {
      // 添加日志，查看从 API 获取的原始网站数据
      console.log('Raw websites data from API:', websites);
      if (websites.length > 0) {
        console.log('First website sample:', websites[0]);
      }

      // 检查网站数据中是否包含 actions 字段
      const hasActions = websites.some(website => Array.isArray(website.actions) && website.actions.length > 0);
      console.log('Websites have actions field:', hasActions);

      // 检查网站数据中是否包含 status 字段
      const hasStatus = websites.some(website => (website as any).status !== undefined);
      console.log('Websites have status field:', hasStatus);

      // 添加默认的 actions 字段
      const websitesWithActions = addDefaultActions(websites);
      console.log('Websites with default actions:', websitesWithActions);

      setFilteredWebsites(websitesWithActions);
    }
  }, [websites]);

  // Handle website selection
  const handleSelectWebsite = (websiteId: string) => {
    setSelectedWebsiteId(websiteId);

    // Update URL with selected website ID
    const params = new URLSearchParams(searchParams?.toString() || '');
    params.set('websiteId', websiteId);
    router.push(`/dashboard?${params.toString()}`);

    // Notify parent component (layout) about the selection
    if (onWebsiteSelect) {
      onWebsiteSelect(websiteId);
    }

    logger.info('Website selected', { websiteId });
  };

  // Handle search
  const handleSearch = (query: string) => {
    if (!websites) return;

    if (!query.trim()) {
      setFilteredWebsites(websites);
      return;
    }

    const filtered = websites.filter(website =>
      website.name.toLowerCase().includes(query.toLowerCase()) ||
      website.domain.toLowerCase().includes(query.toLowerCase()) ||
      (website.description?.toLowerCase().includes(query.toLowerCase()) ?? false)
    );

    setFilteredWebsites(filtered);
    logger.debug('Search websites', { query, results: filtered.length });
  };

  // Handle create website
  const handleCreateWebsite = () => {
    router.push('/website/create');
    logger.info('Navigate to create website');
  };

  // 处理网站操作
  const handleWebsiteAction = (action: string, websiteId: string, domain?: string) => {
    console.log(`Handling website action: ${action}`, { websiteId, domain });

    switch (action) {
      case 'edit':
      case 'settings':
        // 直接跳转到设置页面
        router.push(`/site/${websiteId}/settings/general`);
        logger.info('Navigate to website settings', { websiteId });
        break;

      case 'visit':
      case 'preview':
        if (domain) {
          // 处理域名格式
          let url = domain;
          if (!url.startsWith('http')) {
            // 如果域名不包含协议，添加 https://
            if (!url.includes('.')) {
              // 如果域名不包含点，这可能是二级域名
              url = `https://${url}.lit.page`;
            } else {
              url = `https://${url}`;
            }
          }

          // 如果是预览操作，添加预览参数
          if (action === 'preview') {
            // 添加预览参数
            const previewUrl = new URL(url);
            previewUrl.searchParams.set('preview', 'true');
            url = previewUrl.toString();
          }

          if (onWebsiteVisit) {
            onWebsiteVisit(url);
          } else {
            // 外部网站链接仍然使用 window.open 打开新标签页
            window.open(url, '_blank');
          }
          logger.info(`${action === 'preview' ? 'Preview' : 'Visit'} website`, { domain: url });
        } else {
          console.warn(`Cannot ${action} website: domain is missing`, { websiteId });
        }
        break;

      default:
        console.warn(`Unknown website action: ${action}`, { websiteId });
    }
  };

  // 只保留访问网站的处理函数

  // 处理网站访问
  const handleVisitWebsite = (domain: string) => {
    console.log('Visit website with domain:', domain);

    // 处理域名格式
    let url = domain;
    if (!url.startsWith('http')) {
      // 如果域名不包含协议，添加 https://
      url = `https://${url}`;
    }

    // 找到对应的网站 ID
    // 注意：域名可能已经被处理过，所以需要检查原始域名和处理后的 URL
    const website = websites?.find(site => {
      const siteDomain = site.domain;
      return siteDomain === domain ||
             `https://${siteDomain}` === domain ||
             `https://${siteDomain}.lit.page` === domain;
    });

    if (website) {
      console.log('Found website for domain:', domain, website);
      handleWebsiteAction('visit', website.id, url);
    } else {
      console.warn('Cannot find website with domain:', domain);
      if (onWebsiteVisit) {
        onWebsiteVisit(url);
      } else {
        // 外部网站链接仍然使用 window.open 打开新标签页
        window.open(url, '_blank');
      }
    }
  };

  // Render loading skeletons
  const renderSkeletons = () => {
    return Array(5).fill(0).map((_, index) => (
      <div key={index} className="p-3 mb-2">
        <Skeleton className="h-5 w-3/4 mb-2" />
        <Skeleton className="h-4 w-1/2 mb-2" />
        <Skeleton className="h-3 w-1/3" />
      </div>
    ));
  };

  // Render error state
  const renderError = () => {
    return (
      <div className="p-4 text-center text-muted-foreground">
        <p>Failed to load websites</p>
        <p className="text-sm mt-1">{error instanceof Error ? error.message : 'Unknown error'}</p>
      </div>
    );
  };

  // Render empty state
  const renderEmpty = () => {
    return (
      <div className="p-4 text-center text-muted-foreground">
        <p>No websites found</p>
        <p className="text-sm mt-1">Create your first website to get started</p>
      </div>
    );
  };

  return (
    <div className={cn("flex flex-col h-full bg-background border-r", className)}>
      <WebsiteListHeader
        onSearch={handleSearch}
      />

      <ScrollArea className="flex-1">
        <div className="p-2">
          {isLoading ? (
            renderSkeletons()
          ) : error ? (
            renderError()
          ) : filteredWebsites.length === 0 ? (
            renderEmpty()
          ) : (
            filteredWebsites.map(website => (
              <WebsiteListItem
                key={website.id}
                website={website}
                isSelected={website.id === selectedWebsiteId}
                onSelect={() => handleSelectWebsite(website.id)}
                onVisit={onWebsiteVisit && website.domain ? () => handleVisitWebsite(website.domain) : undefined}
              />
            ))
          )}
        </div>
      </ScrollArea>

      {/* Create website button at the bottom */}
      <div className="p-4">
        <Button
          className="w-full"
          onClick={handleCreateWebsite}
          variant="outline"
          disabled={user?.subscriptionLevel === 'FREE' && filteredWebsites.length >= 1}
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Create New Website
        </Button>

        {/* Subscription tier information moved to SubscriptionTier component */}
      </div>
    </div>
  );
}
