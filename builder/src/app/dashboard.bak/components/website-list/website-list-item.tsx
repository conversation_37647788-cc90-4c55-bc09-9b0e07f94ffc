import { cn } from "@/lib/utils";
import { Website } from "../../types";
import { formatDistanceToNow, format } from "date-fns";
import { GlobeIcon, Globe, MoreHorizontal, ExternalLink, Calendar, Clock, Settings } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useState } from "react";
import { useRouter } from "next/navigation";
// 语言名称映射
const languageNames: Record<string, string> = {
  'EN': 'English',
  'CN': '简体中文',
  'ZH': '繁體中文',
  'ES': 'Español',
  'HI': 'हिन्दी',
  'FR': 'Français',
  'DE': 'Deutsch',
  'RU': 'Русский',
  'PT': 'Português',
  'AR': 'العربية',
  'JP': '日本語',
  'KR': '한국어',
  'IT': 'Italiano',
  'TR': 'Türkçe',
  'PL': 'Polski',
  'NL': 'Nederlands',
  'ID': 'Bahasa Indonesia',
  'TH': 'ไทย',
  'VI': 'Tiếng Việt',
  'SV': 'Svenska',
};

interface WebsiteListItemProps {
  website: Website;
  isSelected: boolean;
  onSelect: () => void;
  onVisit?: (domain: string) => void;
}

/**
 * Enhanced component to display a single website in the list with improved UI and interactions
 */
export function WebsiteListItem({
  website,
  isSelected,
  onSelect,
  onVisit
}: WebsiteListItemProps) {
  const [isHovered, setIsHovered] = useState(false);
  const router = useRouter();

  // Format dates
  const createdAtFormatted = website.createdAt
    ? formatDistanceToNow(new Date(website.createdAt), { addSuffix: true })
    : 'Unknown date';

  const updatedAtFormatted = website.updatedAt
    ? formatDistanceToNow(new Date(website.updatedAt), { addSuffix: true })
    : 'Unknown';

  const createdAtFull = website.createdAt
    ? format(new Date(website.createdAt), 'PPP p')
    : 'Unknown date';

  const updatedAtFull = website.updatedAt
    ? format(new Date(website.updatedAt), 'PPP p')
    : 'Unknown date';

  // 移除状态相关代码

  // Get language display name
  const getLanguageDisplayName = (languageCode?: string) => {
    if (!languageCode) return 'None';
    return languageNames[languageCode] || languageCode;
  };

  // Handle visit action

  const handleVisit = (e: React.MouseEvent) => {
    e.stopPropagation();
    // 添加日志，查看 website 的完整数据结构
    console.log('Website data:', JSON.stringify(website, null, 2));
    console.log('Website domain:', website.domain);
    console.log('Website actions:', website.actions);

    // 构建完整的域名 URL
    let url = website.domain;
    if (url && !url.startsWith('http')) {
      // 如果域名不包含协议，添加 https://
      if (!url.includes('.')) {
        // 如果域名不包含点，这可能是二级域名
        url = `https://${url}.lit.page`;
      } else {
        url = `https://${url}`;
      }
    }

    if (onVisit && website.domain) {
      console.log('Calling onVisit with domain:', url);
      onVisit(url);
    } else {
      console.warn('Cannot visit website: onVisit callback or domain is missing', {
        hasOnVisit: !!onVisit,
        domain: website.domain,
        url: url
      });
    }
  };

  return (
    <div
      className={cn(
        "p-4 rounded-lg cursor-pointer transition-all duration-200 mb-3 border",
        "hover:border-primary/30 hover:shadow-sm",
        isSelected
          ? "bg-accent text-accent-foreground border-primary/40 shadow-sm"
          : "bg-card text-card-foreground"
      )}
      onClick={onSelect}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Header with name and actions */}
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2 overflow-hidden">
          <h3 className="font-medium text-base truncate">{website.name}</h3>
        </div>

        <div className="flex items-center space-x-2">

          {/* Actions dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8 rounded-full opacity-70 hover:opacity-100">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>Website Actions</DropdownMenuLabel>
              <DropdownMenuSeparator />

              {/* 只显示设置选项 */}
              <DropdownMenuItem onClick={() => {
                if (website.id) {
                  // 使用 Next.js router 跳转到设置页面
                  router.push(`/site/${website.id}/settings/general`);
                }
              }}>
                <Settings className="h-4 w-4 mr-2" />
                <span>Settings</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Domain and Actions */}
      <div className="flex items-center justify-between text-sm mb-3">
        <div className="flex items-center">
          <GlobeIcon className="h-4 w-4 mr-1.5 text-muted-foreground" />
          <span className="truncate">{website.domain}</span>

          {onVisit && (
            <Button
              variant="ghost"
              size="icon"
              className={cn(
                "h-6 w-6 ml-1 rounded-full transition-opacity",
                isHovered ? "opacity-100" : "opacity-0"
              )}
              onClick={handleVisit}
            >
              <ExternalLink className="h-3 w-3" />
            </Button>
          )}
        </div>
      </div>

      {/* Footer with dates and language */}
      <div className="flex items-center justify-between text-xs text-muted-foreground mt-3 pt-2 border-t">
        <div className="flex flex-col gap-1">
          {website.updatedAt && website.updatedAt !== website.createdAt && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center">
                    <Clock className="h-3 w-3 mr-1" />
                    <span>Updated {updatedAtFormatted}</span>
                  </div>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>{updatedAtFull}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>

        {website.defaultLanguage && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center gap-1 bg-primary/10 text-primary rounded-full px-2 py-0.5">
                  <Globe className="h-3 w-3" />
                  <span className="font-medium">{website.defaultLanguage.toUpperCase()}</span>
                </div>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                <p>Default language: {getLanguageDisplayName(website.defaultLanguage)}</p>
                {website.supportedLanguages && website.supportedLanguages.length > 1 && (
                  <p className="text-xs text-muted-foreground mt-1">
                    Supports {website.supportedLanguages.length} languages
                  </p>
                )}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>
    </div>
  );
}
