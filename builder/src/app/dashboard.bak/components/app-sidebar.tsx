"use client"

import * as React from "react"
import { WebsiteList } from "./website-list/website-list"
import { SubscriptionTier } from "./subscription/SubscriptionTier"
import { useUserProfile } from "@/lib/hooks/use-user-profile"
import { useWebsiteList } from "@/lib/hooks/use-website-list"
import { UserNav } from "@/components/user/UserNav"
import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarFooter,
  SidebarRail,
} from "@/components/ui/sidebar"

interface AppSidebarProps extends React.ComponentProps<typeof Sidebar> {
  onWebsiteSelect?: (websiteId: string) => void;
  onWebsiteVisit?: (domain: string) => void;
}

export function AppSidebar({ onWebsiteSelect, onWebsiteVisit, ...props }: AppSidebarProps) {
  const { user } = useUserProfile();
  const { data: websites } = useWebsiteList();
  const websiteCount = websites?.length || 0;

  return (
    <Sidebar {...props}>
      <SidebarHeader className="h-16 border-b border-sidebar-border flex items-center justify-between px-4">
        <h2 className="text-xl font-semibold">Dashboard</h2>
      </SidebarHeader>
      <SidebarContent className="flex-1 p-0 flex flex-col overflow-hidden">
        <WebsiteList
          className="flex-1"
          onWebsiteSelect={onWebsiteSelect}
          onWebsiteVisit={onWebsiteVisit}
        />
      </SidebarContent>
      <SidebarFooter className="border-t border-sidebar-border">
        <div className="p-4">
          <SubscriptionTier user={user} websiteCount={websiteCount} />
        </div>
        {user && <UserNav user={user} />}
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
