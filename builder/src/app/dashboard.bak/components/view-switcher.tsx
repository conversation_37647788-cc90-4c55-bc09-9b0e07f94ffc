"use client"

import { MapPin, Grid } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"

export type ViewType = "sitemap" | "grid"

interface ViewSwitcherProps {
  activeView: ViewType
  onViewChange: (view: ViewType) => void
  className?: string
}

/**
 * ViewSwitcher component for switching between different view modes
 * Follows single responsibility principle by handling only view switching logic
 */
export function ViewSwitcher({
  activeView,
  onViewChange,
  className
}: ViewSwitcherProps) {
  // Handle view change with type safety
  const handleViewChange = (value: string) => {
    if (value === "sitemap" || value === "grid") {
      onViewChange(value)
    }
  }

  return (
    <div className={cn("flex space-x-2", className)}>
      <Button
        variant={activeView === "sitemap" ? "secondary" : "ghost"}
        size="sm"
        onClick={() => handleViewChange("sitemap")}
        className={cn(
          "transition-all",
          activeView === "sitemap"
            ? "font-medium bg-secondary text-secondary-foreground"
            : "text-muted-foreground hover:text-foreground hover:bg-secondary/20"
        )}
      >
        <MapPin className={cn(
          "h-4 w-4 mr-2",
          activeView === "sitemap" ? "text-secondary-foreground" : "text-muted-foreground"
        )} />
        <span>Site Map</span>
      </Button>

      <Button
        variant={activeView === "grid" ? "secondary" : "ghost"}
        size="sm"
        onClick={() => handleViewChange("grid")}
        className={cn(
          "transition-all",
          activeView === "grid"
            ? "font-medium bg-secondary text-secondary-foreground"
            : "text-muted-foreground hover:text-foreground hover:bg-secondary/20"
        )}
      >
        <Grid className={cn(
          "h-4 w-4 mr-2",
          activeView === "grid" ? "text-secondary-foreground" : "text-muted-foreground"
        )} />
        <span>Grid View</span>
      </Button>
    </div>
  )
}
