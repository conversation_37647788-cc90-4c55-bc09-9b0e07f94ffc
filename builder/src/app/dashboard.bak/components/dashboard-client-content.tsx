'use client';

import { useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useWebsiteList } from "@/lib/hooks/use-website-list";
import { Logger } from "@/lib/utils/logger";
import { Loader2 } from "lucide-react";

const logger = new Logger('DashboardClientContent');

/**
 * Dashboard client content component
 * Handles redirection to the new site route
 */
export function DashboardClientContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { data: websites, isLoading } = useWebsiteList();

  // Handle redirection to new route
  useEffect(() => {
    if (isLoading || !websites) return;

    logger.info('Dashboard websites data loaded', { count: websites.length });

    // Get websiteId from URL or use first website
    const websiteId = searchParams?.get('websiteId') || (websites.length > 0 ? websites[0].id : null);
    
    if (websiteId) {
      logger.info('Redirecting to new site route', { websiteId });
      
      // Redirect to new route
      const view = searchParams?.get('view') || 'sitemap';
      const newUrl = `/site/${websiteId}${view !== 'sitemap' ? `?view=${view}` : ''}`;
      router.replace(newUrl);
    }
  }, [searchParams, websites, isLoading, router]);

  return (
    <div className="h-full w-full flex items-center justify-center">
      <div className="flex flex-col items-center gap-2">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="text-sm text-muted-foreground">Redirecting to new dashboard...</p>
      </div>
    </div>
  );
}
