import { Button } from '@/components/ui/button';
import { ZoomIn, ZoomOut, Maximize, Grid3X3, GitBranchPlus } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';

interface CanvasControlsProps {
  onZoomIn: () => void;
  onZoomOut: () => void;
  onFitView: () => void;
  layoutType?: 'tree' | 'grid';
  onLayoutChange?: (type: 'tree' | 'grid') => void;
}

/**
 * Controls for the canvas (zoom in, zoom out, fit view)
 */
export function CanvasControls({ 
  onZoomIn, 
  onZoomOut, 
  onFitView,
  layoutType = 'tree',
  onLayoutChange
}: CanvasControlsProps) {
  return (
    <div className="flex flex-col gap-2 bg-background/80 backdrop-blur-sm p-2 rounded-md border border-border shadow-sm">
      <TooltipProvider>
        <div className="flex flex-col gap-2">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="outline" size="icon" onClick={onZoomIn}>
                <ZoomIn className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Zoom In</p>
            </TooltipContent>
          </Tooltip>
          
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="outline" size="icon" onClick={onZoomOut}>
                <ZoomOut className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Zoom Out</p>
            </TooltipContent>
          </Tooltip>
          
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="outline" size="icon" onClick={onFitView}>
                <Maximize className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Fit View</p>
            </TooltipContent>
          </Tooltip>
        </div>
        
        {onLayoutChange && (
          <div className="mt-2 pt-2 border-t border-border">
            <ToggleGroup type="single" value={layoutType} onValueChange={(value: string) => value && onLayoutChange(value as 'tree' | 'grid')}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <ToggleGroupItem value="tree" aria-label="Tree Layout">
                    <GitBranchPlus className="h-4 w-4" />
                  </ToggleGroupItem>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Tree Layout</p>
                </TooltipContent>
              </Tooltip>
              
              <Tooltip>
                <TooltipTrigger asChild>
                  <ToggleGroupItem value="grid" aria-label="Grid Layout">
                    <Grid3X3 className="h-4 w-4" />
                  </ToggleGroupItem>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Grid Layout</p>
                </TooltipContent>
              </Tooltip>
            </ToggleGroup>
          </div>
        )}
      </TooltipProvider>
    </div>
  );
}
