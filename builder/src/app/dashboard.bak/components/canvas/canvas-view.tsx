import { useEffect, useMemo, useState, useCallback } from 'react';
import { 
  ReactFlow, 
  Background, 
  Controls, 
  ReactFlowProvider,
  useNodesState,
  useEdgesState,
  useReactFlow,
  Panel,
  EdgeProps,
  getBezierPath,
  Node,
  Edge
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';

import { useWebsitePages } from '@/lib/hooks/use-website-pages';
import { useCanvas } from '@/lib/hooks/use-canvas';
import { CanvasControls } from './canvas-controls';
import { CanvasEmpty } from './canvas-empty';
import PageNode from './page-node';
import { logger } from '@/utils/logger';
import { Spinner } from '@/components/ui/spinner';

// 配置日志命名空间
logger.configure({ namespace: 'CanvasView' });

// 自定义边组件
const CustomEdge = ({ id, source, target, sourceX, sourceY, targetX, targetY, sourcePosition, targetPosition, data }: EdgeProps) => {
  const [edgePath] = getBezierPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
  });

  // 根据边的类型设置不同的样式
  const isLanguageEdge = data?.type === 'language';
  const strokeColor = isLanguageEdge ? 'var(--primary)' : '#888';
  const strokeWidth = isLanguageEdge ? 2 : 1;
  const strokeDasharray = isLanguageEdge ? '' : '5,5';

  return (
    <path
      id={id}
      className="react-flow__edge-path"
      d={edgePath}
      stroke={strokeColor}
      strokeWidth={strokeWidth}
      strokeDasharray={strokeDasharray}
    />
  );
};

// 节点类型映射
const nodeTypes = {
  pageNode: PageNode,
};

// 边类型映射
const edgeTypes = {
  smoothstep: CustomEdge,
};

// Inner component to use React Flow hooks
function CanvasViewInner({ websiteId, websiteName }: CanvasViewProps) {
  const [layoutType, setLayoutType] = useState<'tree' | 'grid'>('tree');
  const { data: pageStructure, isLoading, error, refetch } = useWebsitePages(websiteId, layoutType);
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const { zoomIn, zoomOut, fitView, onViewportChange } = useCanvas();
  const reactFlowInstance = useReactFlow();
  
  // 处理自动适应视图
  const handleAutoFit = useCallback(() => {
    // 根据节点数量和布局类型调整缩放级别
    const nodeCount = pageStructure?.nodes?.length || 0;
    const isTreeLayout = layoutType === 'tree';
    
    // 计算合适的缩放参数 - 提高最小缩放级别
    const padding = isTreeLayout ? 0.15 : 0.1;
    
    // 调整最小缩放级别，确保节点不会太小
    // 即使页面较多，也保持较大的最小缩放级别
    const minZoom = 0.7;
    
    // 最大缩放级别也相应提高
    const maxZoom = nodeCount > 20 ? 1.0 : 1.5;
    
    // 应用缩放
    fitView({
      padding,
      includeHiddenNodes: false,
      minZoom,
      maxZoom
    });
    
    logger.info('Auto-fitted canvas view', {
      nodeCount,
      layoutType,
      padding,
      minZoom,
      maxZoom
    });
  }, [pageStructure, layoutType, fitView]);
  
  // 处理布局切换
  const handleLayoutChange = (type: 'tree' | 'grid') => {
    setLayoutType(type);
    // 重新获取数据，触发重新布局
    refetch();
  };
  
  // 处理适应视图
  const handleFitView = () => {
    fitView({
      padding: 0.2,
      includeHiddenNodes: false
    });
  };
  
  // Update nodes and edges when page structure changes
  useEffect(() => {
    if (pageStructure && !isLoading) {
      setNodes(pageStructure.nodes as any);
      setEdges(pageStructure.edges as any);
      
      // 延迟执行自动适应视图，确保节点已经渲染
      setTimeout(() => {
        handleAutoFit();
      }, 100);
    }
  }, [pageStructure, isLoading, setNodes, setEdges, handleAutoFit]);
  
  // Display loading state
  if (isLoading) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center">
          <Spinner size="lg" className="mb-4" />
          <p className="text-muted-foreground">Loading pages...</p>
        </div>
      </div>
    );
  }
  
  // Display error state
  if (error) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center max-w-md">
          <h3 className="text-lg font-medium mb-2 text-destructive">Error: Failed to load pages</h3>
          <p className="text-muted-foreground mb-4">
            There was a problem loading the page structure for this website.
          </p>
          <p className="text-sm text-muted-foreground">
            {error instanceof Error ? error.message : 'Unknown error'}
          </p>
        </div>
      </div>
    );
  }
  
  // Display empty state when no pages are available
  if (!pageStructure || pageStructure.nodes.length === 0) {
    return <CanvasEmpty websiteId={websiteId} websiteName={websiteName} />;
  }
  
  return (
    <div className="w-full h-full">
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        fitView
        minZoom={0.2}
        maxZoom={2}
        onMove={onViewportChange}
      >
        <Background />
        <Controls />
        <Panel position="top-right">
          <CanvasControls 
            onZoomIn={zoomIn} 
            onZoomOut={zoomOut} 
            onFitView={handleFitView} 
            layoutType={layoutType}
            onLayoutChange={handleLayoutChange}
          />
        </Panel>
        
        {/* Legend panel - 移动到右下角 */}
        <Panel position="bottom-right" className="bg-white dark:bg-gray-900 p-2 rounded-md shadow-md border border-border">
          <div className="text-xs">
            <h4 className="font-medium mb-1">Legend</h4>
            <div className="flex items-center mb-1">
              <div className="w-3 h-3 rounded-full bg-primary mr-2"></div>
              <span>Language</span>
            </div>
            <div className="flex items-center mb-1">
              <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
              <span>Published</span>
            </div>
            <div className="flex items-center mb-1">
              <div className="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
              <span>Draft</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 rounded-full bg-gray-500 mr-2"></div>
              <span>Archived</span>
            </div>
          </div>
        </Panel>
      </ReactFlow>
    </div>
  );
}

interface CanvasViewProps {
  websiteId?: string;
  websiteName?: string;
}

/**
 * Canvas view component to display website pages as a graph
 */
export function CanvasView({ websiteId, websiteName }: CanvasViewProps) {
  // Memoize the inner component to prevent unnecessary re-renders
  const memoizedInner = useMemo(() => {
    return (
      <ReactFlowProvider>
        <CanvasViewInner websiteId={websiteId} websiteName={websiteName} />
      </ReactFlowProvider>
    );
  }, [websiteId, websiteName]);
  
  return memoizedInner;
}
