import { Button } from "@/components/ui/button";
import { FileIcon, PlusIcon } from "lucide-react";
import { useRouter } from "next/navigation";

interface CanvasEmptyProps {
  websiteId?: string;
  websiteName?: string;
}

/**
 * Empty state component for the canvas when no pages are available
 */
export function CanvasEmpty({ websiteId, websiteName }: CanvasEmptyProps) {
  const router = useRouter();
  
  const handleCreatePage = () => {
    if (websiteId) {
      router.push(`/site/${websiteId}/page/create`);
    }
  };
  
  return (
    <div className="h-full flex flex-col items-center justify-center p-8 text-center">
      <div className="w-16 h-16 rounded-full bg-muted flex items-center justify-center mb-4">
        <FileIcon className="h-8 w-8 text-muted-foreground" />
      </div>
      
      <h3 className="text-xl font-medium mb-2">No Pages Found</h3>
      
      {websiteId ? (
        <>
          <p className="text-muted-foreground mb-6 max-w-md">
            {websiteName ? `"${websiteName}"` : "This website"} doesn&apos;t have any pages yet. 
            Create your first page to get started.
          </p>
          
          <Button onClick={handleCreatePage}>
            <PlusIcon className="h-4 w-4 mr-2" />
            Create Page
          </Button>
        </>
      ) : (
        <p className="text-muted-foreground mb-6 max-w-md">
          Select a website from the list to view its pages.
        </p>
      )}
    </div>
  );
}
