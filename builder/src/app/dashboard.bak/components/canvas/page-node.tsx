import { memo } from 'react';
import { <PERSON>le, Position, NodeProps } from '@xyflow/react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { FileIcon, Globe, ExternalLink, Edit } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { PageNode as PageNodeType } from '../../types';

/**
 * Custom node component to display a page with mobile preview
 */
const PageNode = memo(({ data }: NodeProps) => {
  // 使用类型断言，确保数据符合预期结构
  const nodeData = data as PageNodeType['data'];
  const { id, title, slug, pageType, status, previewUrl, updatedAt, language, websiteId, pageId } = nodeData;
  
  // Format the update date
  const updatedAtFormatted = updatedAt 
    ? formatDistanceToNow(new Date(updatedAt), { addSuffix: true })
    : 'Unknown';
  
  // Map status to badge variant
  const getBadgeVariant = (status: string): "default" | "secondary" | "outline" | "destructive" => {
    switch (status) {
      case 'published': return "default"; // 使用 default 代替 success
      case 'draft': return "secondary";
      case 'archived': return "outline";
      default: return "secondary";
    }
  };
  
  // 处理预览按钮点击
  const handlePreviewClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    
    if (!previewUrl) return;
    
    // 检查 URL 是否是完整的 URL（以 http 或 https 开头）
    if (previewUrl.startsWith('http')) {
      // 如果是完整 URL，直接在新标签页中打开
      window.open(previewUrl, '_blank');
    } else {
      // 如果是相对 URL，在新标签页中打开
      window.open(previewUrl, '_blank');
    }
  };
  
  // 处理编辑按钮点击
  const handleEditClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent node selection
    window.open(`/site/page/${id}`, '_blank');
  };
  
  // 检查是否为语言节点
  const isLanguageNode = pageType === 'language';
  
  // 根据节点类型选择不同的样式和内容
  return (
    <>
      {/* Connection handles */}
      {isLanguageNode ? (
        <Handle
          type="source"
          position={Position.Bottom}
          className="w-3 h-3 bg-primary"
        />
      ) : (
        <>
          <Handle
            type="target"
            position={Position.Top}
            className="w-3 h-3 bg-primary"
          />
          <Handle
            type="source"
            position={Position.Bottom}
            className="w-3 h-3 bg-primary"
          />
        </>
      )}
      
      <Card className={cn("w-96 shadow-md", {
        "border-2 border-primary bg-primary/5": isLanguageNode
      })}>
        <CardHeader className={cn("p-4 pb-0", {
          "pb-2 flex flex-row items-center justify-center": isLanguageNode
        })}>
          {isLanguageNode ? (
            <>
              <Globe className="mr-2 h-5 w-5 text-primary" />
              <h3 className="font-medium text-lg text-primary">{title}</h3>
            </>
          ) : (
            <>
              <div className="flex items-center justify-between">
                <h3 className="font-medium text-base truncate" title={title}>
                  {title}
                </h3>
                <Badge variant={getBadgeVariant(status)} className="text-xs">
                  {status}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <p className="text-xs text-muted-foreground truncate" title={slug}>
                  /{slug}
                </p>
                {language && (
                  <span className="text-xs bg-gray-100 dark:bg-gray-800 px-1.5 py-0.5 rounded text-muted-foreground">
                    {language}
                  </span>
                )}
              </div>
            </>
          )}
        </CardHeader>
        
        {!isLanguageNode && (
          <CardContent className="p-4">
            <div 
              className={cn(
                "w-full aspect-[9/19] bg-muted rounded-xl overflow-hidden",
                "flex items-center justify-center relative border-4 border-gray-300 shadow-inner"
              )}
            >
              {/* Phone frame top notch */}
              <div className="absolute top-0 w-1/3 h-5 bg-gray-300 rounded-b-xl z-10"></div>
              
              <iframe
                src={previewUrl}
                className="w-full h-full border-0"
                onMouseDown={(e) => e.stopPropagation()}
                onTouchStart={(e) => e.stopPropagation()}
                title={`Preview of ${title}`}
                loading="lazy"
                sandbox="allow-scripts"
              />
              
              {/* Fallback while iframe loads */}
              <div className="absolute inset-0 flex items-center justify-center bg-muted pointer-events-none">
                <FileIcon className="h-8 w-8 text-muted-foreground opacity-50" />
              </div>
            </div>
          </CardContent>
        )}
        
        {!isLanguageNode && (
          <CardFooter className="p-4 pt-0 flex justify-between">
            <div className="text-xs text-muted-foreground">
              Updated {updatedAtFormatted}
            </div>
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                size="icon" 
                className="h-7 w-7"
                asChild
              >
                <a 
                  href={`/site/${websiteId}/page/${pageId}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  onMouseDown={(e) => e.stopPropagation()}
                  onTouchStart={(e) => e.stopPropagation()}
                >
                  <Edit className="h-3.5 w-3.5" />
                </a>
              </Button>
              <Button 
                variant="outline" 
                size="icon" 
                className="h-7 w-7"
                asChild
              >
                <a 
                  href={previewUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  onMouseDown={(e) => e.stopPropagation()}
                  onTouchStart={(e) => e.stopPropagation()}
                >
                  <ExternalLink className="h-3.5 w-3.5" />
                </a>
              </Button>
            </div>
          </CardFooter>
        )}
      </Card>
    </>
  );
});

PageNode.displayName = 'PageNode';

export default PageNode;
