import { Suspense } from 'react'
import { dehydrate } from '@tanstack/react-query'
import { getQueryClient } from '@/lib/getQueryClient'
import { Hydrate } from '@/components/hydrate'
import { DashboardClientContent } from './components/dashboard-client-content'
import { DashboardSkeleton } from './components/dashboard-skeleton'
import { ErrorBoundary } from '@/components/error-boundary'
import websiteService from '@/modules/website/service'
import { websiteKeys } from '@/lib/api/queryKeys'

/**
 * Dashboard page component with server-side data prefetching
 */
export default async function DashboardPage() {
  const queryClient = getQueryClient()

  // 在服务器端预取网站列表数据
  await queryClient.prefetchQuery({
    queryKey: websiteKeys.lists(),
    queryFn: websiteService.getWebsites,
    staleTime: 60 * 1000, // 1分钟内数据被认为是新鲜的
  })

  // 脱水查询缓存并将其传递给 Hydrate 组件
  const dehydratedState = dehydrate(queryClient)

  return (
    <ErrorBoundary>
      <Hydrate state={dehydratedState}>
        <Suspense fallback={<DashboardSkeleton />}>
          <DashboardClientContent />
        </Suspense>
      </Hydrate>
    </ErrorBoundary>
  )
}
