"use client"

import { AppSidebar } from "./components/app-sidebar"
import {
  <PERSON><PERSON><PERSON>rumb,
  BreadcrumbItem,
  BreadcrumbList,
  BreadcrumbPage,
} from "@/components/ui/breadcrumb"
import { Separator } from "@/components/ui/separator"
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar"
import { useState, useEffect } from "react"
import { useWebsiteList } from "@/lib/hooks/use-website-list"
import { useSearchParams, usePathname, useRouter } from "next/navigation"
import { ViewSwitcher, ViewType } from "./components/view-switcher"

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const router = useRouter();
  const pathname = usePathname();
  const [selectedWebsiteName, setSelectedWebsiteName] = useState<string | undefined>();
  const [activeView, setActiveView] = useState<ViewType>("sitemap");
  const { data: websites } = useWebsiteList();
  const searchParams = useSearchParams();

  // 从 URL 参数获取当前视图类型
  useEffect(() => {
    const view = searchParams?.get('view') as ViewType;
    if (view === "sitemap" || view === "grid") {
      setActiveView(view);
    }
  }, [searchParams]);

  // 处理视图切换
  const handleViewChange = (view: ViewType) => {
    setActiveView(view);

    // 更新 URL 参数
    const params = new URLSearchParams(searchParams?.toString() || '');
    params.set('view', view);

    // 保留当前的 websiteId 参数
    const websiteId = params.get('websiteId');
    if (!websiteId && websites && websites.length > 0) {
      params.set('websiteId', websites[0].id);
    }

    router.push(`${pathname}?${params.toString()}`);
  };

  // 从 URL 参数或网站列表中获取选中的网站名称
  useEffect(() => {
    const websiteId = searchParams?.get('websiteId');
    if (websiteId && websites) {
      const website = websites.find((site) => site.id === websiteId);
      if (website) {
        setSelectedWebsiteName(website.name);
      }
    } else if (websites && websites.length > 0) {
      // 如果没有选中的网站，默认选择第一个
      setSelectedWebsiteName(websites[0].name);
    }
  }, [searchParams, websites]);

  const handleWebsiteSelect = (websiteId: string) => {
    // 当选择网站时更新网站名称
    if (websites) {
      const website = websites.find((site) => site.id === websiteId);
      if (website) {
        setSelectedWebsiteName(website.name);

        // 更新 URL 参数
        const params = new URLSearchParams(searchParams?.toString() || '');
        params.set('websiteId', websiteId);

        // 保留当前的 view 参数
        if (!params.has('view') && activeView) {
          params.set('view', activeView);
        }

        router.push(`${pathname}?${params.toString()}`);
      }
    }
  };

  // 处理网站访问
  const handleWebsiteVisit = (domain: string) => {
    // 外部网站链接使用 window.open 打开新标签页
    window.open(domain, '_blank');
  };

  return (
    <SidebarProvider defaultOpen={true}>
      <AppSidebar
        onWebsiteSelect={handleWebsiteSelect}
        onWebsiteVisit={handleWebsiteVisit}
      />
      <SidebarInset>
        <header className="sticky top-0 flex h-16 shrink-0 items-center gap-2 border-b bg-background px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbPage>Dashboard</BreadcrumbPage>
              </BreadcrumbItem>
              {selectedWebsiteName && (
                <BreadcrumbItem>
                  <BreadcrumbPage>{selectedWebsiteName}</BreadcrumbPage>
                </BreadcrumbItem>
              )}
            </BreadcrumbList>
          </Breadcrumb>

          <div className="ml-auto flex items-center">
            <ViewSwitcher
              activeView={activeView}
              onViewChange={handleViewChange}
            />
          </div>
        </header>
        <div className="flex flex-1 flex-col">
          {children}
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}
