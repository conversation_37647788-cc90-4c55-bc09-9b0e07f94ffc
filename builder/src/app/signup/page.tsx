"use client"

import Link from "next/link"
import Image from "next/image"
import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { GoogleButton } from "@/components/auth/google-button"
import { <PERSON>, Card<PERSON>ontent, <PERSON>Footer, CardHeader } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { But<PERSON> } from "@/components/ui/button"
import { useAuth } from "@/lib/hooks/use-auth"
import { AlertCircle } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

export default function Page() {
  const router = useRouter()
  const { user: currentUser, isAuthenticated } = useAuth()
  const [showAlert, setShowAlert] = useState(false)

  useEffect(() => {
    // If user is already logged in, show alert
    if (isAuthenticated && currentUser) {
      setShowAlert(true)
    }
  }, [isAuthenticated, currentUser])

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-b from-slate-100 to-slate-200 dark:from-slate-900 dark:to-slate-800">
      <div className="relative mx-auto px-6 w-full max-w-[480px] flex flex-col items-center">
        {/* Brand logo */}
        <div className="mb-16 flex items-center gap-3">
          <Image
            src="/favicon.svg"
            alt="LitPage"
            width={36}
            height={36}
            className="dark:brightness-200"
            priority
          />
          <span className="text-2xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 dark:from-slate-200 dark:to-slate-400 bg-clip-text text-transparent">
            LitPage
          </span>
        </div>

        {/* Main card */}
        <Card className="w-full backdrop-blur-sm bg-white/80 dark:bg-slate-900/80 shadow-2xl rounded-2xl border-0">
          <CardHeader className="space-y-3 pt-8 pb-4">
            <h2 className="text-2xl text-center font-semibold tracking-tight">
              Create your account
            </h2>
            <p className="text-sm text-center text-muted-foreground">
              Join LitPage to get started
            </p>
          </CardHeader>

          <CardContent className="grid gap-6 p-8">
            {showAlert ? (
              <>
                <Alert variant="destructive" className="mb-4">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Already signed in</AlertTitle>
                  <AlertDescription>
                    You are already signed in as {currentUser?.name || "User"}.
                  </AlertDescription>
                </Alert>
                <div className="flex flex-col gap-3">
                  <Button
                    onClick={() => router.push("/dashboard")}
                    className="w-full"
                  >
                    Go to Dashboard
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => router.push("/signin")}
                    className="w-full"
                  >
                    Switch Account
                  </Button>
                </div>
              </>
            ) : (
              <GoogleButton mode="signup" isAuthenticated={isAuthenticated} />
            )}
          </CardContent>

          <CardFooter className="flex flex-col items-center gap-6 px-8 pb-8">
            <div className="text-sm text-muted-foreground text-center">
              By signing up, you agree to our{" "}
              <Link href="/terms" className="underline underline-offset-4 hover:text-primary transition-colors">
                Terms of Service
              </Link>{" "}
              and{" "}
              <Link href="/privacy" className="underline underline-offset-4 hover:text-primary transition-colors">
                Privacy Policy
              </Link>
            </div>

            <div className="w-full">
              <Separator className="my-4" />
              <div className="text-sm text-center">
                Already have an account?{" "}
                <Link
                  href="/signin"
                  className="font-medium text-primary hover:underline transition-colors"
                >
                  Sign in
                </Link>
              </div>
            </div>
          </CardFooter>
        </Card>

        {/* Help link */}
        <div className="mt-8">
          <Link
            href="mailto:<EMAIL>"
            className="text-sm text-muted-foreground hover:text-primary transition-colors"
          >
            Need help? Contact Support
          </Link>
        </div>
      </div>
    </div>
  )
}
