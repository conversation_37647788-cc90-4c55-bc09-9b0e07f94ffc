import type { Metadata } from 'next'
import clsx from 'clsx';
import localFont from 'next/font/local'
import '../styles/globals.css'
import { Toaster } from "@/components/ui/toaster"
import Providers from '@/components/providers'

// 使用本地字体文件
const inter = localFont({
  src: '../../public/fonts/Inter-Variable.woff2',
  display: 'swap',
  variable: '--font-inter',
})

export const metadata: Metadata = {
  title: 'lit.page',
  description: 'lit.page',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {

  return (
    <html lang="en" className="dark h-full">
      <head>
        <link rel="icon" href="/favicon.svg" sizes="16x16" type="image/png" />
        <link rel="icon" href="/favicon.svg" sizes="32x32" type="image/png" />
      </head>
      <body className={clsx('h-full', inter.className)}>
        <Providers>
          {children}
        </Providers>
        <Toaster />
      </body>
    </html>
  )
}
