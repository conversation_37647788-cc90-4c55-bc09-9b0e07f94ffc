// /modules/website/service.ts

import { WebsiteModel } from './model';
import { $get, $post, $put, $delete } from '@/lib';
// import { nanoid } from 'nanoid';
import Logger from '@/lib/logger';
import { NavConfig } from './types';
// VerifyDomainResponse
import { CustomDomain, CustomDomainCreateDto } from './types';

const logger = new Logger('WebsiteService');

export const createWebsite = async (website: WebsiteModel): Promise<WebsiteModel> => {
  try {
    logger.info('Creating website', website);
    const res = await $post<WebsiteModel, WebsiteModel>('/api/v1/websites', website);
    
    if (!res.success || !res.data) {
      logger.error('Failed to create website', { response: res });
      throw new Error('Failed to create website');
    }
    
    logger.info('Website created successfully', res.data);
    return res.data;
  } catch (error) {
    logger.error('Failed to create website', error as Error);
    throw error;
  }
};

export const updateWebsiteConfig = async (websiteId: string, data: Partial<WebsiteModel>): Promise<void> => {
  try {
    logger.info('Updating website config', { websiteId, data });
    const res = await $put<Partial<WebsiteModel>, void>(`/api/v1/websites/${websiteId}/config`, data);
    
    if (!res.success) {
      logger.error('Failed to update website config', { response: res });
      throw new Error('Failed to update website config');
    }
    
    logger.info('Website config updated successfully');
  } catch (error) {
    logger.error('Failed to update website config', error as Error);
    throw error;
  }
};

export const deleteWebsite = async (websiteId: string): Promise<void> => {
  try {
    logger.info('Deleting website', { websiteId });
    const res = await $delete<void>(`/api/v1/websites/${websiteId}`);
    
    if (!res.success) {
      logger.error('Failed to delete website', { response: res });
      throw new Error('Failed to delete website');
    }
    
    logger.info('Website deleted successfully');
  } catch (error) {
    logger.error('Failed to delete website', error as Error);
    throw error;
  }
};

export const setWebsiteConfig = async (websiteId: string, config: any): Promise<void> => {
  try {
    logger.info('Saving website config', { websiteId, config });
    const res = await $put<any, void>(`/api/v1/websites/${websiteId}/set-config`, config);
    
    if (!res.success) {
      logger.error('Failed to save website config', { response: res });
      throw new Error('Failed to save website config');
    }
    
    logger.info('Website config saved successfully');
  } catch (error) {
    logger.error('Failed to save website config', error as Error);
    throw error;
  }
};

export const getWebsites = async (): Promise<WebsiteModel[]> => {
  try {
    logger.info('Fetching websites...');
    const startTime = Date.now();
    
    const res: any = await $get<{ success: boolean; data: WebsiteModel[] }>('/api/v1/websites');
    const endTime = Date.now();
    
    logger.info('Raw API response received', { 
      responseTime: endTime - startTime,
      isError: res.isError,
      success: res.success,
      hasData: !!res.data
    });
    
    if (res.isError) {
      logger.error('Failed to get websites: API error response', res);
      throw new Error(res.message || 'Failed to get websites');
    }
    
    if (!res.success) {
      logger.error('Failed to get websites: unsuccessful response', res);
      throw new Error('Failed to get websites');
    }
    
    if (!res.data) {
      logger.error('Failed to get websites: no data in response', res);
      throw new Error('No websites data received');
    }

    // Handle nested response structure
    let websites: WebsiteModel[] = [];
    
    if (res.data.success && Array.isArray(res.data.data)) {
      websites = res.data.data;
    } else if (Array.isArray(res.data)) {
      websites = res.data;
    }

    logger.info('Processed websites data', { 
      websitesCount: websites.length,
      websiteIds: websites.map(w => w.id)
    });
    
    return websites;
  } catch (error) {
    logger.error('Failed to get websites', error);
    throw error;
  }
};

export const getWebsite = async (websiteId: string): Promise<WebsiteModel> => {
  try {
    logger.info('Fetching website', { websiteId });
    const res = await $get<WebsiteModel>(`/api/v1/websites/${websiteId}`);
    
    if (!res.success || !res.data) {
      logger.error('Failed to get website', { websiteId, response: res });
      throw new Error('Failed to get website');
    }
    
    logger.info('Website fetched successfully', res.data);
    return res.data;
  } catch (error) {
    logger.error('Failed to get website', { websiteId, error });
    throw error;
  }
};

export const updateWebsiteNav = async ({ websiteId, config }: { websiteId: string; config: Omit<NavConfig, 'data'> & Record<string, any> }): Promise<void> => {
  const { type, lang, logo, variant, ...restData } = config;
  
  // 构造符合后端 DTO 的数据结构
  const requestData: any = {
    type,
    lang,
    logo,
    data: {
      variant,
      ...restData.data
    }
  };
  
  try {
    logger.info('Updating website nav', { websiteId, config: requestData });
    const res = await $put<NavConfig, void>(`/api/v1/websites/${websiteId}/nav`, requestData);
    
    if (!res.success) {
      logger.error('Failed to update website nav', { response: res });
      throw new Error('Failed to update website nav');
    }
    
    logger.info('Website nav updated successfully');
  } catch (error) {
    logger.error('Failed to update website nav', error as Error);
    throw error;
  }
};

export const getCustomDomain = async (websiteId: string): Promise<CustomDomain | null> => {
  try {
    logger.info('Getting custom domain', { websiteId });
    const res = await $get<CustomDomain | null>(`/api/v1/websites/${websiteId}/custom-domain`);
    
    if (!res.success) {
      logger.error('Failed to get custom domain', { response: res });
      return null;
    }
    
    // 当后端返回 data: null 时，表示没有自定义域名
    if (res.data === null) {
      logger.info('No custom domain found for website', { websiteId });
      return null;
    }
    
    logger.info('Custom domain retrieved successfully', res.data);
    return res.data;
  } catch (error) {
    logger.error('Failed to get custom domain', error as Error);
    return null;
  }
};

export const setCustomDomain = async (websiteId: string, domain: string): Promise<CustomDomain> => {
  try {
    logger.info('Setting custom domain', { websiteId, domain });
    const res = await $post<CustomDomainCreateDto, CustomDomain>(
      `/api/v1/websites/${websiteId}/custom-domain`,
      { domain }
    );
    
    if (!res.success || !res.data) {
      logger.error('Failed to set custom domain', { response: res });
      throw new Error('Failed to set custom domain');
    }
    
    logger.info('Custom domain set successfully', res.data);
    return res.data;
  } catch (error) {
    logger.error('Failed to set custom domain', error as Error);
    throw error;
  }
};

export const verifyCustomDomain = async (websiteId: string): Promise<any> => {
  try {
    logger.info('Verifying custom domain', { websiteId });
    const res = await $post<void, any>(`/api/v1/websites/${websiteId}/custom-domain/verify`);
    
    if (!res.success || !res.data) {
      logger.error('Failed to verify custom domain', { response: res });
      throw new Error('Failed to verify custom domain');
    }
    
    logger.info('Custom domain verified successfully', res.data);
    return res.data;
  } catch (error) {
    logger.error('Failed to verify custom domain', error as Error);
    throw error;
  }
};

export const deleteCustomDomain = async (websiteId: string): Promise<void> => {
  try {
    logger.info('Deleting custom domain', { websiteId });
    const res = await $delete<void>(`/api/v1/websites/${websiteId}/custom-domain`);
    
    if (!res.success) {
      logger.error('Failed to delete custom domain', { response: res });
      throw new Error('Failed to delete custom domain');
    }
    
    logger.info('Custom domain deleted successfully');
  } catch (error) {
    logger.error('Failed to delete custom domain', error as Error);
    throw error;
  }
};

const websiteService = {
  createWebsite,
  getWebsite,
  getWebsites,
  updateWebsiteConfig,
  deleteWebsite,
  setWebsiteConfig,
  updateWebsiteNav,
  getCustomDomain,
  setCustomDomain,
  verifyCustomDomain,
  deleteCustomDomain,
};

export default websiteService;
