// /modules/website/model.ts

export interface WebsiteModel {
  id?: string;
  name: string;
  domain: string;
  logo?: string;
  defaultLanguage?: string;
  description?: string;
  tags?: string[];
  theme?: string;
  createdAt?: string;

  workspaceId?: string;
  configuration?: any;
  headers?: any;
  footers?: any;
  
  // 自定义域名
  customDomain?: {
    id: string;
    domain: string;
    status: 'PENDING' | 'ACTIVE' | 'FAILED';
    websiteId: string;
    createdAt: string;
    updatedAt: string;
  };
}
