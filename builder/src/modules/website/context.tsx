'use client'

import { createContext, useContext, ReactNode } from 'react'
import { WebsiteModel } from './model'

// Define the context type
interface WebsiteContextType {
  currentWebsite: WebsiteModel | null
}

// Create the context with a default value
export const WebsiteContext = createContext<WebsiteContextType>({
  currentWebsite: null
})

// Context provider component
export function WebsiteProvider({
  children,
  siteId,
  initialWebsite = null
}: {
  children: ReactNode
  siteId: string
  initialWebsite?: WebsiteModel | null
}) {
  return (
    <WebsiteContext.Provider value={{ currentWebsite: initialWebsite }}>
      {children}
    </WebsiteContext.Provider>
  )
}

// Custom hook to use the context
export function useWebsiteContext() {
  return useContext(WebsiteContext)
}
