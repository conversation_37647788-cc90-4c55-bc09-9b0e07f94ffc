// /modules/page/model.ts
import { Page } from '@/components/promptPopup';

export interface PageModel {
  id?: string;
  theme?: string;
  domain?: string;
  pageId?: string;
  nanoid?: string;
  slug?: string;
  title: string;
  description?: string;
  tags?: string[];
  isTemplate?: boolean;
  userId?: string;
  websiteId: string;
  workspaceId: string;
  language: string;
  publishedVersionId?: string;
  draftVersionId?: string;
  // currentVersionId?: string;
  // isPublished?: boolean;
  status: string;
  publishedAt?: Date;
  createdAt?: Date;
  updatedAt?: Date;
  configuration?: any;
  metaKeywords?: string | string[];  // 可以是字符串或字符串数组
  pageType?: string;
  header?: {
    id: string;
    language: string;
    variant: string;
    status: string;
    configuration: any;
  };
  footer?: {
    id: string;
    language: string;
    variant: string;
    status: string;
    configuration: any;
  };
  currentHeader?: {
    id: string;
    language: string;
    status: string;
    draftVersionId?: string;
  };
  currentFooter?: {
    id: string;
    language: string;
    status: string;
    draftVersionId?: string;
  };
  currentHeaderId?: string;
  currentFooterId?: string;
  // 新增多语言相关属性
  languageInfo?: {
    currentLanguage: string;
    defaultLanguage: string;
    supportedLanguages: string[];
    translatedLanguages: string[];
    languageNames: Record<string, string>;
  };
  languageVersions?: Record<string, {
    id: string;
    nanoid: string;
    status: string;
  }>;
  languageUrls?: Record<string, string>;
  seo?: {
    canonicalUrl: string;
    alternateUrls: Array<{
      language: string;
      url: string;
    }>;
  };
}

export function toPage(model: PageModel): Page {
  let metaKeywords: string[] = [];
  
  if (typeof model.metaKeywords === 'string') {
    metaKeywords = model.metaKeywords.split(',').map(k => k.trim());
  } else if (Array.isArray(model.metaKeywords)) {
    metaKeywords = model.metaKeywords;
  }

  return {
    id: model.id || model.pageId || '',
    name: model.title,
    slug: model.nanoid || '',
    pageType: 'landing',  // 默认为 landing 类型
    metaKeywords
  };
}
