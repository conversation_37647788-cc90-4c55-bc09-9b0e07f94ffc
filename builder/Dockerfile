# 基础阶段
FROM node:20-alpine AS base

# 使用一个 RUN 命令完成所有系统级配置
RUN apk add --no-cache libc6-compat && \
    corepack enable && \
    corepack prepare pnpm@9.15.4 --activate && \
    rm -rf /var/cache/apk/*

WORKDIR /app

# 环境变量集中设置
ENV PNPM_HOME=/root/.local/share/pnpm \
    PATH="/root/.local/share/pnpm:${PATH}" \
    NEXT_TELEMETRY_DISABLED=1

# 构建阶段
FROM base AS builder
WORKDIR /app

# 依赖安装优化
COPY pnpm-workspace.yaml package.json pnpm-lock.yaml ./
COPY sections/package.json ./sections/
COPY builder/package.json ./builder/

# 使用高级缓存策略
RUN --mount=type=cache,target=/root/.local/share/pnpm/store \
    --mount=type=cache,target=/app/.next/cache \
    pnpm install --frozen-lockfile || \
    (echo "Lockfile outdated, updating dependencies..." && pnpm install)

# 源代码复制
COPY sections/src ./sections/src
COPY builder ./builder

# 构建优化 - 直接在构建前更新 caniuse-lite
RUN cd builder && \
    pnpm up caniuse-lite && \
    echo "BROWSERSLIST_IGNORE_OLD_DATA=1" > .env.local && \
    NEXT_TELEMETRY_DISABLED=1 pnpm build

# 生产阶段
FROM node:20-alpine AS runner
WORKDIR /app

# 安全设置
RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 nextjs

# 文件复制和权限设置
COPY --from=builder --chown=nextjs:nodejs /app/builder/.next/standalone/. ./
COPY --from=builder --chown=nextjs:nodejs /app/builder/.next/static ./builder/.next/static
COPY --from=builder --chown=nextjs:nodejs /app/builder/public ./builder/public

# 清理和权限设置合并
RUN chown -R nextjs:nodejs /app && \
    rm -rf /tmp/* /var/cache/apk/* && \
    # 验证关键文件存在
    test -f builder/server.js

# 运行时配置
USER nextjs
ENV PORT=4000 \
    NODE_ENV=production \
    NEXT_TELEMETRY_DISABLED=1

EXPOSE 4000

# 健康检查使用 Node.js 内置功能
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:4000/', res => process.exit(res.statusCode === 200 ? 0 : 1))"

CMD ["node", "builder/server.js"]