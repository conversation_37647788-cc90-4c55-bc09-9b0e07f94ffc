const Icons = require('lucide-react');

const isValidReactComponent = (component) => {
  return component && (
    typeof component === 'function' || 
    (typeof component === 'object' && component.$$typeof) ||
    (typeof component === 'object' && component.render)
  );
};

const testIcons = ['Globe', 'ShieldCheck', 'SlidersHorizontal', 'BookOpen'];
testIcons.forEach(iconName => {
  const icon = Icons[iconName];
  console.log(`${iconName}: exists=${!!icon}, valid=${isValidReactComponent(icon)}, type=${typeof icon}`);
});

console.log('\nTesting Globe specifically:');
console.log('Globe:', Icons.Globe);
console.log('Globe keys:', Object.keys(Icons.Globe || {}));
console.log('Globe $$typeof:', Icons.Globe?.$$typeof); 