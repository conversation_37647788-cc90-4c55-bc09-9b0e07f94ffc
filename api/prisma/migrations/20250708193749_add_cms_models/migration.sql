/*
  Warnings:

  - You are about to drop the column `schemaMetadata` on the `PageSEO` table. All the data in the column will be lost.

*/
-- <PERSON><PERSON><PERSON>num
CREATE TYPE "ContentStatus" AS ENUM ('DRAFT', 'PUBLISHED', 'ARCHIVED');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "ContentType" AS ENUM ('BLOG', 'DOC', 'CHANGELOG', 'FAQ', 'PRODUCT', 'ANNOUNCEMENT', 'TUTORIAL', 'CASE_STUDY', 'WHITEPAPER', 'CUSTOM');

-- CreateEnum
CREATE TYPE "CategoryType" AS ENUM ('GENERAL', 'BLOG', 'DOC', 'FAQ', 'CHANGELOG', 'PRODUCT', 'TUTORIAL', 'ANNOUNCEMENT', 'CASE_STUDY', 'WHITEPAPER');

-- CreateEnum
CREATE TYPE "ContentAuditAction" AS ENUM ('CREATED', 'DRAFT_SAVED', 'PUBLISHED', 'MODIFIED', 'ARCHIVED', 'RESTORED', 'DELETED', 'REVISION_CREATED', 'REVISION_PUBLISHED', 'METADATA_UPDATED', 'CATEGORY_CHANGED', 'TAGS_UPDATED', 'AUTHOR_UPDATED', 'SEO_UPDATED');

-- AlterTable
ALTER TABLE "PageSEO" DROP COLUMN "schemaMetadata";

-- AlterTable
ALTER TABLE "Quota" ADD COLUMN     "contentCreationLimit" INTEGER NOT NULL DEFAULT 100,
ADD COLUMN     "contentRevisionsLimit" INTEGER NOT NULL DEFAULT 1000,
ADD COLUMN     "contentsCreated" INTEGER NOT NULL DEFAULT 0;

-- CreateTable
CREATE TABLE "ContentNode" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "workspaceId" TEXT NOT NULL,
    "websiteId" TEXT NOT NULL,
    "type" "ContentType" NOT NULL,
    "slug" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "excerpt" TEXT,
    "language" "Language" NOT NULL DEFAULT 'EN',
    "authorInfo" JSONB NOT NULL,
    "status" "ContentStatus" NOT NULL DEFAULT 'DRAFT',
    "publishedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "currentRevisionId" TEXT,
    "categoryId" TEXT,

    CONSTRAINT "ContentNode_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ContentRevision" (
    "id" TEXT NOT NULL,
    "nodeId" TEXT NOT NULL,
    "createdById" TEXT NOT NULL,
    "version" INTEGER NOT NULL,
    "data" JSONB NOT NULL,
    "changelog" TEXT,
    "isPublished" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "publishedAt" TIMESTAMP(3),

    CONSTRAINT "ContentRevision_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Category" (
    "id" TEXT NOT NULL,
    "workspaceId" TEXT NOT NULL,
    "websiteId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "slug" TEXT NOT NULL,
    "description" TEXT,
    "parentId" TEXT,
    "order" INTEGER NOT NULL DEFAULT 0,
    "type" "CategoryType" NOT NULL DEFAULT 'GENERAL',
    "language" "Language" NOT NULL DEFAULT 'EN',
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Category_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Tag" (
    "id" TEXT NOT NULL,
    "workspaceId" TEXT NOT NULL,
    "websiteId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "color" TEXT,
    "description" TEXT,
    "language" "Language" NOT NULL DEFAULT 'EN',
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Tag_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ContentNodeTag" (
    "contentId" TEXT NOT NULL,
    "tagId" TEXT NOT NULL,
    "weight" INTEGER NOT NULL DEFAULT 1,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ContentNodeTag_pkey" PRIMARY KEY ("contentId","tagId")
);

-- CreateTable
CREATE TABLE "ContentAuditLog" (
    "id" TEXT NOT NULL,
    "contentId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "action" "ContentAuditAction" NOT NULL,
    "revisionId" TEXT,
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ContentAuditLog_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "ContentNode_currentRevisionId_key" ON "ContentNode"("currentRevisionId");

-- CreateIndex
CREATE INDEX "ContentNode_userId_idx" ON "ContentNode"("userId");

-- CreateIndex
CREATE INDEX "ContentNode_workspaceId_idx" ON "ContentNode"("workspaceId");

-- CreateIndex
CREATE INDEX "ContentNode_websiteId_idx" ON "ContentNode"("websiteId");

-- CreateIndex
CREATE INDEX "ContentNode_websiteId_type_status_idx" ON "ContentNode"("websiteId", "type", "status");

-- CreateIndex
CREATE INDEX "ContentNode_websiteId_status_publishedAt_idx" ON "ContentNode"("websiteId", "status", "publishedAt");

-- CreateIndex
CREATE INDEX "ContentNode_authorInfo_idx" ON "ContentNode"("authorInfo");

-- CreateIndex
CREATE INDEX "ContentNode_categoryId_idx" ON "ContentNode"("categoryId");

-- CreateIndex
CREATE UNIQUE INDEX "ContentNode_websiteId_slug_language_key" ON "ContentNode"("websiteId", "slug", "language");

-- CreateIndex
CREATE INDEX "ContentRevision_nodeId_isPublished_idx" ON "ContentRevision"("nodeId", "isPublished");

-- CreateIndex
CREATE INDEX "ContentRevision_createdAt_idx" ON "ContentRevision"("createdAt");

-- CreateIndex
CREATE INDEX "ContentRevision_createdById_idx" ON "ContentRevision"("createdById");

-- CreateIndex
CREATE UNIQUE INDEX "ContentRevision_nodeId_version_key" ON "ContentRevision"("nodeId", "version");

-- CreateIndex
CREATE INDEX "Category_workspaceId_idx" ON "Category"("workspaceId");

-- CreateIndex
CREATE INDEX "Category_websiteId_idx" ON "Category"("websiteId");

-- CreateIndex
CREATE INDEX "Category_parentId_idx" ON "Category"("parentId");

-- CreateIndex
CREATE INDEX "Category_type_language_idx" ON "Category"("type", "language");

-- CreateIndex
CREATE INDEX "Category_order_idx" ON "Category"("order");

-- CreateIndex
CREATE UNIQUE INDEX "Category_workspaceId_slug_language_key" ON "Category"("workspaceId", "slug", "language");

-- CreateIndex
CREATE INDEX "Tag_workspaceId_idx" ON "Tag"("workspaceId");

-- CreateIndex
CREATE INDEX "Tag_websiteId_idx" ON "Tag"("websiteId");

-- CreateIndex
CREATE INDEX "Tag_language_idx" ON "Tag"("language");

-- CreateIndex
CREATE UNIQUE INDEX "Tag_workspaceId_name_language_key" ON "Tag"("workspaceId", "name", "language");

-- CreateIndex
CREATE INDEX "ContentNodeTag_tagId_idx" ON "ContentNodeTag"("tagId");

-- CreateIndex
CREATE INDEX "ContentAuditLog_contentId_idx" ON "ContentAuditLog"("contentId");

-- CreateIndex
CREATE INDEX "ContentAuditLog_userId_idx" ON "ContentAuditLog"("userId");

-- CreateIndex
CREATE INDEX "ContentAuditLog_action_idx" ON "ContentAuditLog"("action");

-- CreateIndex
CREATE INDEX "ContentAuditLog_createdAt_idx" ON "ContentAuditLog"("createdAt");

-- CreateIndex
CREATE INDEX "ContentAuditLog_contentId_createdAt_idx" ON "ContentAuditLog"("contentId", "createdAt");

-- AddForeignKey
ALTER TABLE "ContentNode" ADD CONSTRAINT "ContentNode_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ContentNode" ADD CONSTRAINT "ContentNode_workspaceId_fkey" FOREIGN KEY ("workspaceId") REFERENCES "Workspace"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ContentNode" ADD CONSTRAINT "ContentNode_websiteId_fkey" FOREIGN KEY ("websiteId") REFERENCES "Website"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ContentNode" ADD CONSTRAINT "ContentNode_currentRevisionId_fkey" FOREIGN KEY ("currentRevisionId") REFERENCES "ContentRevision"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ContentNode" ADD CONSTRAINT "ContentNode_categoryId_fkey" FOREIGN KEY ("categoryId") REFERENCES "Category"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ContentRevision" ADD CONSTRAINT "ContentRevision_nodeId_fkey" FOREIGN KEY ("nodeId") REFERENCES "ContentNode"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ContentRevision" ADD CONSTRAINT "ContentRevision_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Category" ADD CONSTRAINT "Category_workspaceId_fkey" FOREIGN KEY ("workspaceId") REFERENCES "Workspace"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Category" ADD CONSTRAINT "Category_websiteId_fkey" FOREIGN KEY ("websiteId") REFERENCES "Website"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Category" ADD CONSTRAINT "Category_parentId_fkey" FOREIGN KEY ("parentId") REFERENCES "Category"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Tag" ADD CONSTRAINT "Tag_workspaceId_fkey" FOREIGN KEY ("workspaceId") REFERENCES "Workspace"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Tag" ADD CONSTRAINT "Tag_websiteId_fkey" FOREIGN KEY ("websiteId") REFERENCES "Website"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ContentNodeTag" ADD CONSTRAINT "ContentNodeTag_contentId_fkey" FOREIGN KEY ("contentId") REFERENCES "ContentNode"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ContentNodeTag" ADD CONSTRAINT "ContentNodeTag_tagId_fkey" FOREIGN KEY ("tagId") REFERENCES "Tag"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ContentAuditLog" ADD CONSTRAINT "ContentAuditLog_contentId_fkey" FOREIGN KEY ("contentId") REFERENCES "ContentNode"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ContentAuditLog" ADD CONSTRAINT "ContentAuditLog_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
