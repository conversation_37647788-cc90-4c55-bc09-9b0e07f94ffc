-- Create<PERSON><PERSON>
CREATE TYPE "XCardType" AS ENUM ('SUMMARY', 'SUMMARY_LARGE_IMAGE', 'APP', 'PLAYER');

-- CreateEnum
CREATE TYPE "ChangeFrequency" AS ENUM ('ALWAYS', 'HOURLY', 'DAILY', 'WEEKLY', 'MONTHLY', 'YEARLY', 'NEVER');

-- CreateEnum
CREATE TYPE "SchemaType" AS ENUM ('ARTICLE', 'PRODUCT', 'FAQ', 'HOWTO', 'EVENT', 'ORGANIZATION', 'PERSON', 'WEBSITE', 'LOCALBUSINESS', 'RECIPE', 'COURSE', 'REVIEW', 'BREADCRUMB', 'CUSTOM');

-- CreateTable
CREATE TABLE "WebsiteSEO" (
    "id" TEXT NOT NULL,
    "websiteId" TEXT NOT NULL,
    "siteName" TEXT,
    "siteDescription" TEXT,
    "titleTemplate" TEXT,
    "defaultDescription" TEXT,
    "globalKeywords" TEXT[],
    "robotsTxt" TEXT,
    "sitemapSettings" JSONB,
    "organizationSchema" JSONB,
    "websiteSchema" JSONB,
    "defaultOgImage" TEXT,
    "ogTitleTemplate" TEXT,
    "ogDescriptionTemplate" TEXT,
    "defaultXCard" "XCardType" DEFAULT 'SUMMARY_LARGE_IMAGE',
    "xUsername" TEXT,
    "hreflangSettings" JSONB,
    "forceHttps" BOOLEAN NOT NULL DEFAULT true,
    "httpToHttpsRedirect" BOOLEAN NOT NULL DEFAULT true,
    "cacheControl" TEXT,
    "resourceCompression" BOOLEAN DEFAULT false,
    "browserCaching" BOOLEAN DEFAULT false,
    "lazyLoadingImages" BOOLEAN DEFAULT false,
    "analyticsSettings" JSONB,
    "brandSettings" JSONB,
    "seoScores" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "WebsiteSEO_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PageSEO" (
    "id" TEXT NOT NULL,
    "pageId" TEXT NOT NULL,
    "title" TEXT,
    "useCustomTitle" BOOLEAN NOT NULL DEFAULT true,
    "description" TEXT,
    "keywords" TEXT[],
    "ogTitle" TEXT,
    "ogDescription" TEXT,
    "ogImage" TEXT,
    "xTitle" TEXT,
    "xDescription" TEXT,
    "xImage" TEXT,
    "xCardType" "XCardType" DEFAULT 'SUMMARY_LARGE_IMAGE',
    "schemaType" "SchemaType",
    "schemaData" JSONB,
    "canonicalUrl" TEXT,
    "robots" JSONB,
    "hreflangLinks" JSONB,
    "priority" DOUBLE PRECISION DEFAULT 0.5,
    "changeFrequency" "ChangeFrequency" DEFAULT 'MONTHLY',
    "contentAnalysis" JSONB,
    "inheritFromSite" BOOLEAN NOT NULL DEFAULT true,
    "websiteSeoId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PageSEO_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "WebsiteSEO_websiteId_key" ON "WebsiteSEO"("websiteId");

-- CreateIndex
CREATE INDEX "WebsiteSEO_websiteId_idx" ON "WebsiteSEO"("websiteId");

-- CreateIndex
CREATE INDEX "WebsiteSEO_createdAt_idx" ON "WebsiteSEO"("createdAt");

-- CreateIndex
CREATE INDEX "WebsiteSEO_updatedAt_idx" ON "WebsiteSEO"("updatedAt");

-- CreateIndex
CREATE UNIQUE INDEX "PageSEO_pageId_key" ON "PageSEO"("pageId");

-- CreateIndex
CREATE INDEX "PageSEO_pageId_idx" ON "PageSEO"("pageId");

-- CreateIndex
CREATE INDEX "PageSEO_websiteSeoId_idx" ON "PageSEO"("websiteSeoId");

-- CreateIndex
CREATE INDEX "PageSEO_createdAt_idx" ON "PageSEO"("createdAt");

-- CreateIndex
CREATE INDEX "PageSEO_schemaType_idx" ON "PageSEO"("schemaType");

-- CreateIndex
CREATE INDEX "PageSEO_priority_idx" ON "PageSEO"("priority");

-- CreateIndex
CREATE INDEX "PageSEO_changeFrequency_idx" ON "PageSEO"("changeFrequency");

-- AddForeignKey
ALTER TABLE "WebsiteSEO" ADD CONSTRAINT "WebsiteSEO_websiteId_fkey" FOREIGN KEY ("websiteId") REFERENCES "Website"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PageSEO" ADD CONSTRAINT "PageSEO_pageId_fkey" FOREIGN KEY ("pageId") REFERENCES "Page"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PageSEO" ADD CONSTRAINT "PageSEO_websiteSeoId_fkey" FOREIGN KEY ("websiteSeoId") REFERENCES "WebsiteSEO"("id") ON DELETE SET NULL ON UPDATE CASCADE;
