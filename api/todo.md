- [ ] 数据库迁移
  - [ ] 移除 Page.currentHeaderId / currentFooterId 字段
  - [ ] 在 Website 表新增 draftHeaderId / publishedHeaderId / draftFooterId / publishedFooterId 字段
  - [ ] 调整 PageVersion 中 headerVersionId / footerVersionId 保留或删除策略
  - [ ] 创建迁移脚本将现有 Page 记录中的 Header/Footer 引用迁移到 Website

- [ ] 后端服务调整
  - [ ] 更新 PageService：仅依赖 PageVersion 持有布局信息或通过 Website 获取
  - [ ] 新增 WebsiteService 接口：管理 Header/Footer 的草稿、发布与回滚
  - [ ] 调整权限校验：Header/Footer 更新仅需 Website 级权限

- [ ] 渲染层更新
  - [ ] 页面渲染流程：先加载 Website 已发布 Header/Footer，再渲染 PageVersion
  - [ ] 支持特殊页面通过 overrideHeaderId / overrideFooterId 覆盖

- [ ] 缓存与 SEO
  - [ ] 更新 CDN 缓存失效逻辑：当 Header/Footer 发布或回滚时批量刷新关联页面缓存
  - [ ] 校验 sitemap 生成逻辑是否引用正确的 Header/Footer 版本

- [ ] 测试计划
  - [ ] 单元测试：Website Header/Footer 发布、回滚、覆盖页面流程
  - [ ] 集成测试：多语言网站 Header/Footer 与页面渲染一致性
  - [ ] 回滚测试：旧字段软删除后系统可正常回滚

- [ ] 部署与灰度
  - [ ] Feature flag 控制新布局逻辑
  - [ ] 灰度迁移观察 1 周，监控错误率与渲染延迟

- [ ] 文档更新
  - [ ] Prisma schema 变更说明
  - [ ] API 文档：Website Header/Footer 管理接口
  - [ ] 渲染层流程图 & 开发者指南 