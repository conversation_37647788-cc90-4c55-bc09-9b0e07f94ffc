# Pixabay API 配置指南

## 环境变量配置

为了使图片搜索功能正常工作，需要配置 Pixabay API 密钥。

### 1. 获取 Pixabay API 密钥

1. 访问 [Pixabay](https://pixabay.com/)
2. 注册账户并登录
3. 访问 [API 文档页面](https://pixabay.com/api/docs/)
4. 获取你的 API 密钥

### 2. 配置环境变量

在你的 `.env` 文件中添加以下配置：

```env
# Pixabay API Configuration
PIXABAY_API_KEY=your_pixabay_api_key_here
```

### 3. API 限制

- **免费账户**: 每小时 5,000 次请求
- **付费账户**: 每小时 20,000 次请求
- **图片热链**: 不允许永久热链，需要下载到自己的服务器

### 4. 使用说明

API 支持以下搜索参数：

- `q`: 搜索关键词
- `lang`: 语言代码 (默认: en)
- `image_type`: 图片类型 (all, photo, illustration, vector)
- `orientation`: 方向 (all, horizontal, vertical)
- `category`: 分类 (backgrounds, fashion, nature, etc.)
- `min_width`, `min_height`: 最小尺寸
- `colors`: 颜色过滤
- `editors_choice`: 编辑精选
- `safesearch`: 安全搜索
- `order`: 排序方式 (popular, latest)
- `page`: 页码
- `per_page`: 每页数量 (3-200)

### 5. 缓存机制

- 搜索结果缓存 24 小时
- 最多缓存 1000 个查询结果
- 相同参数的查询会直接返回缓存结果

### 6. 错误处理

- API 密钥未配置会返回错误
- 网络错误会有重试机制
- 所有错误都会记录日志

## 测试 API

可以使用以下 curl 命令测试 API：

```bash
curl -X GET "http://localhost:3000/api/v1/images/search?q=nature&image_type=photo&per_page=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```
