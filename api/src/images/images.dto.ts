import { IsString, IsOptional, IsIn, IsBoolean, <PERSON><PERSON>nt, <PERSON>, <PERSON>, IsArray } from 'class-validator';
import { Transform, Type } from 'class-transformer';

export class PixabaySearchDto {
  // @IsString()
  // key: string;

  @IsString()
  @IsOptional()
  q?: string;

  @IsString()
  @IsOptional()
  @IsIn(['cs', 'da', 'de', 'en', 'es', 'fr', 'id', 'it', 'hu', 'nl', 'no', 'pl', 'pt', 'ro', 'sk', 'fi', 'sv', 'tr', 'vi', 'th', 'bg', 'ru', 'el', 'ja', 'ko', 'zh'])
  lang?: string = 'en';

  @IsString()
  @IsOptional()
  id?: string;

  @IsString()
  @IsOptional()
  @IsIn(['all', 'photo', 'illustration', 'vector'])
  image_type?: string = 'all';

  @IsString()
  @IsOptional()
  @IsIn(['all', 'horizontal', 'vertical'])
  orientation?: string = 'all';

  @IsString()
  @IsOptional()
  @IsIn(['backgrounds', 'fashion', 'nature', 'science', 'education', 'feelings', 'health', 'people', 'religion', 'places', 'animals', 'industry', 'computer', 'food', 'sports', 'transportation', 'travel', 'buildings', 'business', 'music'])
  category?: string;

  @IsInt()
  @IsOptional()
  @Min(0)
  @Type(() => Number)
  @Transform(({ value }) => parseInt(value, 10))
  min_width?: number = 0;

  @IsInt()
  @IsOptional()
  @Min(0)
  @Type(() => Number)
  @Transform(({ value }) => parseInt(value, 10))
  min_height?: number = 0;

  @IsOptional()
  @IsString({ each: true })
  @IsIn(['grayscale', 'transparent', 'red', 'orange', 'yellow', 'green', 'turquoise', 'blue', 'lilac', 'pink', 'white', 'gray', 'black', 'brown'], { each: true })
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.split(',').map(v => v.trim());
    }
    return value;
  })
  colors?: string[];

  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value === 'true';
    }
    return value;
  })
  editors_choice?: boolean = false;

  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value === 'true';
    }
    return value;
  })
  safesearch?: boolean = false;

  @IsString()
  @IsOptional()
  @IsIn(['popular', 'latest'])
  order?: string = 'popular';

  @IsInt()
  @IsOptional()
  @Min(1)
  @Max(200)
  @Type(() => Number)
  @Transform(({ value }) => parseInt(value, 10))
  per_page?: number = 20;

  @IsInt()
  @IsOptional()
  @Min(1)
  @Type(() => Number)
  @Transform(({ value }) => parseInt(value, 10))
  page?: number = 1;

  @IsString()
  @IsOptional()
  callback?: string;

  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value === 'true';
    }
    return value;
  })
  pretty?: boolean = false;
}
