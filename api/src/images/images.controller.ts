import { Controller, Get, Query, UseGuards, HttpException, HttpStatus, ValidationPipe, UsePipes } from '@nestjs/common';
import { Observable, catchError } from 'rxjs';
import { AuthGuard } from '@nestjs/passport';
import { ImagesService } from './images.service';
import { PixabaySearchDto } from './images.dto';

@Controller('api/v1/images')
export class ImagesController {
  constructor(private readonly imagesService: ImagesService) {}

  @Get('search')
  @UseGuards(AuthGuard('jwt'))
  @UsePipes(new ValidationPipe({ 
    transform: true, 
    whitelist: true, 
    forbidNonWhitelisted: true 
  }))
  searchImages(@Query() params: PixabaySearchDto): Observable<any> {
    return this.imagesService.searchImages(params).pipe(
      catchError((error) => {
        throw new HttpException(
          {
            success: false,
            message: error.message || 'Failed to search images',
            status: error.status || HttpStatus.INTERNAL_SERVER_ERROR
          },
          error.status || HttpStatus.INTERNAL_SERVER_ERROR
        );
      })
    );
  }
}
