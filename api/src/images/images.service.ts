import { Inject, Injectable, Logger } from '@nestjs/common';
import { AxiosResponse } from 'axios';
import { HttpService } from '@nestjs/axios';
import { Observable, from, of, throwError } from 'rxjs';
import { map, switchMap, catchError } from 'rxjs/operators';
import { PixabaySearchDto } from './images.dto';
import { Cache } from 'cache-manager';
import { CACHE_MANAGER } from '@nestjs/cache-manager'

@Injectable()
export class ImagesService {
  private readonly logger = new Logger(ImagesService.name);
  private readonly API_URL = 'https://pixabay.com/api/';
  private readonly API_KEY = process.env.PIXABAY_API_KEY;

  constructor(
    private readonly httpService: HttpService,
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
  ) {
    if (!this.API_KEY) {
      this.logger.error('PIXABAY_API_KEY is not configured');
    }
  }

  searchImages(params: PixabaySearchDto): Observable<any> {
    if (!this.API_KEY) {
      return throwError(() => new Error('Pixabay API key is not configured'));
    }

    const cacheKey = this.generateCacheKey(params);
    this.logger.debug(`Searching images with params: ${JSON.stringify(params)}`);

    return from(this.cacheManager.get(cacheKey)).pipe(
      switchMap((cachedResult) => {
        if (cachedResult) {
          this.logger.debug('Cache hit for search query');
          return of({
            success: true,
            data: cachedResult,
            cached: true
          });
        }

        const queryParams = {
          key: this.API_KEY,
          ...params,
        };

        return this.httpService.get(this.API_URL, { params: queryParams }).pipe(
          map((response: AxiosResponse) => {
            const data = response.data;
            this.logger.debug(`Pixabay API response: ${data.hits?.length || 0} images found`);
            
            // Cache for 24 hours (86400 seconds)
            this.cacheManager.set(cacheKey, data, 86400);
            
            return {
              success: true,
              data: data,
              cached: false
            };
          }),
          catchError((error) => {
            this.logger.error(`Pixabay API error: ${error.message}`, error.stack);
            return throwError(() => ({
              success: false,
              message: error.response?.data?.error || 'Failed to fetch images from Pixabay',
              status: error.response?.status || 500
            }));
          })
        );
      }),
      catchError((error) => {
        this.logger.error(`Service error: ${error.message}`, error.stack);
        return throwError(() => ({
          success: false,
          message: 'Internal server error',
          status: 500
        }));
      })
    );
  }

  private generateCacheKey(params: PixabaySearchDto): string {
    // Generate a unique cache key based on the parameters
    const sortedParams = Object.keys(params)
      .sort()
      .reduce((result, key) => {
        result[key] = params[key];
        return result;
      }, {});
    
    return `pixabay_search_${Buffer.from(JSON.stringify(sortedParams)).toString('base64')}`;
  }
}
