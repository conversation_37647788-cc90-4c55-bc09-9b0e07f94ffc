import { Injectable, Logger, BadRequestException, NotFoundException, ConflictException, ForbiddenException, HttpException, HttpStatus, InternalServerErrorException } from '@nestjs/common';
import {
    PageCreateDto,
    PageUpdateDto,
    PageVersionUpdateDto,
    PageSettingsUpdateDto,
    ABTestCreateDto,
    // PageDataSubmitDto,
    PageVersionCreateDto,
    PageStatusUpdateDto,
    // PageNotificationSettingsUpdateDto,
    // PageAccessControlSettingsUpdateDto
    PageComponentsUpdateDto
} from './dto';
import { PrismaService } from '../prisma/prisma.service';
import { customAlphabet } from 'nanoid';
import { 
    Language, 
    PageType, 
    PageStatus, 
    PageAuditAction, 
    ComponentStatus, 
    VersionStatus,
    Prisma 
} from '@prisma/client';
import { ErrorCodes } from '../exceptions';
import { CanonicalUrlService } from '../common/services/canonical-url.service';

const nanoid = customAlphabet('1234567890abcdef', 10)

const languageMap: { [key: string]: Language } = {
    'EN': Language.EN,
    'CN': Language.CN,
    'ZH': Language.ZH,
    'ES': Language.ES,
    'HI': Language.HI,
    'FR': Language.FR,
    'DE': Language.DE,
    'RU': Language.RU,
    'PT': Language.PT,
    'AR': Language.AR,
    'JP': Language.JP,
    'KR': Language.KR,
    'IT': Language.IT,
    'TR': Language.TR,
    'PL': Language.PL,
    'NL': Language.NL,
    'ID': Language.ID,
    'TH': Language.TH,
    'VI': Language.VI,
    'SV': Language.SV,
};

@Injectable()
export class PageService {
    private readonly logger = new Logger(PageService.name);

    constructor(
        private readonly prisma: PrismaService,
        private readonly canonicalUrlService: CanonicalUrlService,
    ) { }

    async createPage(userId: string, data: PageCreateDto) {
        try {
            this.logger.log('[CreatePage] Starting page creation process', {
                userId,
                websiteId: data.websiteId,
                title: data.title,
                slug: data.slug,
            });

            // 1. 验证并获取工作区
            this.logger.log('[CreatePage] Step 1: Validating workspace');
            let workspaceId = data.workspaceId;
            
            if (!workspaceId) {
                this.logger.debug('No workspace ID provided, finding workspace by website');
                const website = await this.prisma.website.findFirst({
                    where: {
                        id: data.websiteId,
                        userId,
                    },
                    include: {
                        workspace: true,
                    }
                });
                
                if (!website || !website.workspace) {
                    this.logger.error(`No workspace found for website: ${data.websiteId}`);
                    throw new BadRequestException('No workspace found for this website');
                }
                workspaceId = website.workspaceId;
                this.logger.debug(`Using website's workspace: ${workspaceId}`);
            } else {
                this.logger.debug(`Validating provided workspace: ${workspaceId}`);
                const workspace = await this.prisma.workspace.findFirst({
                    where: { 
                        id: workspaceId,
                        userId,
                    }
                });
                
                if (!workspace) {
                    this.logger.error(`Invalid workspace ID: ${workspaceId} for user: ${userId}`);
                    throw new BadRequestException('Invalid workspace');
                }
                this.logger.debug('Workspace validation successful');
            }

            // 2. 验证网站权限
            this.logger.log('[CreatePage] Step 2: Validating website access');
            const website = await this.prisma.website.findFirst({
                where: {
                    id: data.websiteId,
                    userId,
                },
                select: {
                    id: true,
                    defaultLanguage: true,
                    userId: true
                }
            });

            if (!website) {
                this.logger.error(`Website not found or no access: ${data.websiteId}`);
                throw new NotFoundException('Website not found or no access');
            }

            // 3. 验证 slug 唯一性
            this.logger.log('[CreatePage] Step 3: Validating slug uniqueness');
            
            // 确定要使用的语言
            const language = data.language ? 
                Language[data.language.toUpperCase() as keyof typeof Language] : 
                website.defaultLanguage;
                
            this.logger.debug(`Using language for page creation: ${language}`);
            
            const existingPage = await this.prisma.page.findFirst({
                where: {
                    websiteId: data.websiteId,
                    slug: data.slug,
                    language: language
                }
            });

            if (existingPage) {
                this.logger.error(`Slug already exists: ${data.slug} with language: ${language}`);
                throw new ConflictException('This page URL is already taken');
            }

            // 新增: 确保头尾组件的语言版本存在
            if (language !== website.defaultLanguage) {
                this.logger.log(`[CreatePage] Ensuring ${language} language versions for header and footer components`);
                await this.ensureHeaderFooterLanguageVersions(
                    data.websiteId,
                    language,
                    website.defaultLanguage
                );
            }

            // 4. 获取网站的默认语言头尾组件
            this.logger.log('[CreatePage] Step 4: Finding default header and footer', {
                websiteId: data.websiteId,
                language
            });
            const defaultHeader = await this.prisma.websiteHeader.findFirst({
                where: {
                    websiteId: data.websiteId,
                    language: language,
                    isDeleted: false
                }
            });
            
            if (defaultHeader) {
                this.logger.debug(`[CreatePage] Found default header: ${defaultHeader.id}, variant: ${defaultHeader.variant} for language: ${language}`);
            } else {
                this.logger.warn(`[CreatePage] No default header found for language: ${language}, will create page without header`);
            }
            
            const defaultFooter = await this.prisma.websiteFooter.findFirst({
                where: {
                    websiteId: data.websiteId,
                    language: language,
                    isDeleted: false
                }
            });
            
            if (defaultFooter) {
                this.logger.debug(`[CreatePage] Found default footer: ${defaultFooter.id}, variant: ${defaultFooter.variant} for language: ${language}`);
            } else {
                this.logger.warn(`[CreatePage] No default footer found for language: ${language}, will create page without footer`);
            }

            // 5. 创建页面
            this.logger.log('[CreatePage] Step 5: Creating page record', {
                title: data.title,
                slug: data.slug,
                headerId: defaultHeader?.id || null,
                footerId: defaultFooter?.id || null
            });
            const page = await this.prisma.$transaction(async (tx: Prisma.TransactionClient) => {
                // 5.1 创建页面
                const newPage = await tx.page.create({
                    data: {
                        userId,
                        workspaceId,
                        websiteId: data.websiteId,
                        name: data.title,
                        title: data.title,
                        description: data.description,
                        slug: data.slug,
                        pageType: PageType[data.pageType.toUpperCase() as keyof typeof PageType],
                        language: language, // 使用之前确定的语言值
                        nanoid: nanoid(),
                        // 移除 SEO 相关字段
                        // 关联默认头尾组件
                        currentHeaderId: defaultHeader?.id || null,
                        currentFooterId: defaultFooter?.id || null
                    }
                });

                // 5.2 创建 PageSEO 记录
                this.logger.log('[CreatePage] Creating PageSEO record', {
                    pageId: newPage.id,
                    metaKeywords: data.metaKeywords,
                    metaDescription: data.metaDescription,
                    ogTitle: data.ogTitle,
                    ogDescription: data.ogDescription,
                    ogImage: data.ogImage
                });
                
                await tx.pageSEO.create({
                    data: {
                        pageId: newPage.id,
                        title: data.title, // 默认使用页面标题
                        description: data.metaDescription,
                        keywords: data.metaKeywords ? data.metaKeywords.split(',').map(k => k.trim()) : [],
                        ogTitle: data.ogTitle || data.title,
                        ogDescription: data.ogDescription || data.metaDescription,
                        ogImage: data.ogImage,
                        // 默认继承网站 SEO 设置
                        inheritFromSite: true
                    }
                });

                // 5.3 创建初始版本
                const initialVersion = await tx.pageVersion.create({
                    data: {
                        pageId: newPage.id,
                        versionNumber: 1,
                        theme: 'default',
                        configuration: {
                            sections: [],
                            customize: {
                                bgColor: 'bg-white',
                                textColor: 'text-gray-900'
                            }
                        }
                    }
                });

                // 5.4 更新页面的当前版本
                const updatedPage = await tx.page.update({
                    where: { id: newPage.id },
                    data: { 
                        draftVersionId: initialVersion.id,
                    },
                    include: {
                        versions: {
                            where: { id: initialVersion.id }
                        },
                        currentHeader: true,
                        currentFooter: true
                    }
                });

                return updatedPage;
            });

            this.logger.log('[CreatePage] Page created successfully', {
                pageId: page.id,
                nanoid: page.nanoid,
                headerId: page.currentHeaderId,
                footerId: page.currentFooterId
            });

            return {
                success: true,
                data: page,
                message: 'Page created successfully',
            };
        } catch (error) {
            this.logger.error('[CreatePage] Error creating page', {
                error: error.message,
                userId,
                websiteId: data.websiteId,
            });
            throw error;
        }
    }

    async getAllPages(userId: string, websiteId) {
        this.logger.log(`[GetAllPages] Retrieving all pages for website: ${websiteId}`);
        
        const pages = await this.prisma.page.findMany({
            where: {
                userId,
                websiteId,
                status: {
                    not: 'DELETED'
                }
            },
            include: {
                // 包含 SEO 信息
                seo: true
            }
        });
        
        this.logger.debug(`[GetAllPages] Found ${pages.length} pages`);
        
        // 处理返回数据，整合 SEO 信息
        const pagesWithSeo = pages.map(page => ({
            ...page,
            // 使用 SEO 信息覆盖页面基本信息
            title: page.seo?.title || page.title,
            description: page.seo?.description || page.description,
            metaKeywords: page.seo?.keywords || [],
            metaDescription: page.seo?.description,
            ogTitle: page.seo?.ogTitle,
            ogDescription: page.seo?.ogDescription,
            ogImage: page.seo?.ogImage,
        }));
        
        return { success: true, data: pagesWithSeo };
    }

    async getPage(pageId: string) {
        this.logger.log(`[GetPage] Retrieving page for editor with ID: ${pageId}`);
        
        const page = await this.prisma.page.findFirst({
            where: { id: pageId },
            include: {
                website: true,
                currentHeader: {
                    include: {
                        draftVersion: true,
                        publishedVersion: true
                    }
                },
                currentFooter: {
                    include: {
                        draftVersion: true,
                        publishedVersion: true
                    }
                },
                seo: true // 包含 SEO 信息
            }
        });

        if (!page) {
            this.logger.warn(`[GetPage] Page not found with ID: ${pageId}`);
            throw new NotFoundException('Page not found');
        }

        this.logger.debug(`[GetPage] Found page: ${page.id}, title: ${page.title}, status: ${page.status}`);

        // 获取当前版本（草稿或发布版本）
        // 优先使用草稿版本，如果不存在则使用发布版本
        const versionId = page.draftVersionId || page.publishedVersionId;
        
        // 添加日志记录版本ID
        this.logger.debug(`[GetPage] Using version ID: ${versionId}, draftVersionId: ${page.draftVersionId}, publishedVersionId: ${page.publishedVersionId}`);
        
        if (!versionId) {
            this.logger.error(`[GetPage] No version ID available for page: ${page.id}`);
            throw new NotFoundException('Page has no available version');
        }
        
        const currentPage = await this.prisma.pageVersion.findFirst({
            where: { id: versionId }
        });

        if (!currentPage) {
            this.logger.error(`[GetPage] Page version not found for page: ${page.id}, draftVersionId: ${page.draftVersionId}`);
            throw new NotFoundException('Page version not found');
        }

        this.logger.debug(`[GetPage] Found page version: ${currentPage.id}, version: ${currentPage.versionNumber}`);

        // 处理头尾数据 - 编辑器模式下优先使用草稿版本
        let headerData = null;
        if (page.currentHeader) {
            const headerVersion = page.currentHeader.draftVersion || page.currentHeader.publishedVersion;
            
            if (headerVersion) {
                headerData = {
                    id: page.currentHeader.id,
                    language: page.currentHeader.language,
                    variant: page.currentHeader.variant,
                    status: page.currentHeader.status,
                    configuration: headerVersion.configuration
                };
                this.logger.debug(`[GetPage] Including header: ${page.currentHeader.id}, variant: ${page.currentHeader.variant}, version: ${headerVersion.id}`);
            } else {
                this.logger.warn(`[GetPage] Header ${page.currentHeader.id} has no available version`);
            }
        } else if (page.currentHeaderId) {
            this.logger.warn(`[GetPage] Header with ID ${page.currentHeaderId} not found or not included in query`);
        }

        let footerData = null;
        if (page.currentFooter) {
            const footerVersion = page.currentFooter.draftVersion || page.currentFooter.publishedVersion;
            
            if (footerVersion) {
                footerData = {
                    id: page.currentFooter.id,
                    language: page.currentFooter.language,
                    variant: page.currentFooter.variant,
                    status: page.currentFooter.status,
                    configuration: footerVersion.configuration
                };
                this.logger.debug(`[GetPage] Including footer: ${page.currentFooter.id}, variant: ${page.currentFooter.variant}, version: ${footerVersion.id}`);
            } else {
                this.logger.warn(`[GetPage] Footer ${page.currentFooter.id} has no available version`);
            }
        } else if (page.currentFooterId) {
            this.logger.warn(`[GetPage] Footer with ID ${page.currentFooterId} not found or not included in query`);
        }

        // 获取多语言相关信息
        this.logger.debug(`[GetPage] Retrieving language versions for page: ${page.id}, slug: ${page.slug}`);
        
        // 1. 获取所有已翻译的语言版本
        const translatedVersions = await this.prisma.page.findMany({
            where: {
                websiteId: page.websiteId,
                slug: page.slug,
                status: { in: [PageStatus.PUBLISHED, PageStatus.MODIFIED, PageStatus.DRAFT] }
            },
            select: { 
                id: true,
                language: true,
                nanoid: true,
                status: true
            }
        });

        this.logger.debug(`[GetPage] Found ${translatedVersions.length} translated versions for slug: ${page.slug}`, {
            versions: translatedVersions.map(v => ({ id: v.id, language: v.language, status: v.status }))
        });

        // 2. 获取网站支持的所有语言
        const websiteLanguages = await this.prisma.page.findMany({
            where: {
                websiteId: page.websiteId,
                status: { in: [PageStatus.PUBLISHED, PageStatus.MODIFIED, PageStatus.DRAFT] }
            },
            select: { language: true },
            distinct: ['language']
        });

        const supportedLanguages = websiteLanguages.map(p => p.language);
        this.logger.debug(`[GetPage] Website supported languages: ${supportedLanguages.join(', ')}`);
        
        // 创建已翻译语言的映射
        const translatedLanguagesMap = translatedVersions.reduce((acc, p) => {
            acc[p.language] = {
                id: p.id,
                nanoid: p.nanoid,
                status: p.status
            };
            return acc;
        }, {} as Record<string, { id: string, nanoid: string, status: string }>);

        this.logger.debug(`[GetPage] Created language versions map with ${Object.keys(translatedLanguagesMap).length} entries`);

        // 4. 构建语言 URL 映射
        const languageUrlMap: Record<string, string> = {};
        
        // 为每种支持的语言构建 URL
        supportedLanguages.forEach(lang => {
            if (translatedLanguagesMap[lang]) {
                // 该语言有对应的页面版本，使用相同的 slug
                if (lang === page.website?.defaultLanguage) {
                    // 默认语言不带语言前缀
                    languageUrlMap[lang] = `${page.slug}`;
                } else {
                    // 非默认语言带语言前缀
                    languageUrlMap[lang] = `/${lang.toLowerCase()}${page.slug}`;
                }
            } else {
                // 该语言没有对应的页面版本，使用首页
                if (lang === page.website?.defaultLanguage) {
                    // 默认语言首页不带语言前缀
                    languageUrlMap[lang] = `/`;
                } else {
                    // 非默认语言首页带语言前缀
                    languageUrlMap[lang] = `/${lang.toLowerCase()}/`;
                }
            }
        });

        this.logger.debug(`[GetPage] Created language URL map`, { languageUrlMap });

        // 5. 获取语言名称映射
        const languageNames = this.getLanguageNames();

        this.logger.log(`[GetPage] Successfully retrieved page data for editor: ${page.id}, title: ${page.title}`);

        // 构建 SEO 相关信息
        const seo = {
            canonicalUrl: page.language === page.website?.defaultLanguage ? 
                `${page.slug}` : 
                `/${page.language.toLowerCase()}${page.slug}`,
            alternateUrls: Object.entries(languageUrlMap).map(([lang, url]) => ({
                language: lang.toLowerCase(),
                url
            })),
            // 包含 PageSEO 信息
            title: page.seo?.title || page.title,
            description: page.seo?.description || page.description,
            keywords: page.seo?.keywords || [],
            ogTitle: page.seo?.ogTitle || page.title,
            ogDescription: page.seo?.ogDescription || page.description,
            ogImage: page.seo?.ogImage,
        };

        this.logger.debug(`[GetPage] Generated SEO information`, { 
            canonicalUrl: seo.canonicalUrl,
            alternateUrls: seo.alternateUrls,
            title: seo.title,
            ogTitle: seo.ogTitle,
        });

        return {
            success: true,
            data: {
                id: page.id,
                nanoid: page.nanoid,
                name: page.name,
                title: page.title,
                domain: page.website?.domain,
                description: page.description,
                slug: page.slug,
                status: page.status,
                pageType: page.pageType,
                language: page.language,
                metaKeywords: page.metaKeywords,
                metaDescription: page.metaDescription,
                ogTitle: page.ogTitle,
                ogDescription: page.ogDescription,
                ogImage: page.ogImage,
                version: currentPage.versionNumber,
                configuration: currentPage.configuration,
                theme: currentPage.theme,
                header: headerData,
                footer: footerData,
                // 添加多语言相关信息
                languageInfo: {
                    currentLanguage: page.language,
                    defaultLanguage: page.website?.defaultLanguage || 'EN',
                    supportedLanguages,
                    translatedLanguages: Object.keys(translatedLanguagesMap),
                    languageNames
                },
                languageVersions: translatedLanguagesMap,
                languageUrls: languageUrlMap,
                // 添加 SEO 相关信息
                seo
            }
        };
    }

    async getPubPage(nanoId: string) {
        this.logger.log(`[GetPubPage] Retrieving public page with nanoId: ${nanoId}`);
        
        const page = await this.prisma.page.findFirst({ 
            where: { nanoid: nanoId },
            include: {
                // 公开页面只需要包含已发布版本
                currentHeader: {
                    include: {
                        publishedVersion: true
                    }
                },
                currentFooter: {
                    include: {
                        publishedVersion: true
                    }
                }
            }
        });
        
        if (!page) {
            this.logger.warn(`[GetPubPage] Page not found with nanoId: ${nanoId}`);
            throw new NotFoundException('Page not found');
        }
        
        this.logger.debug(`[GetPubPage] Found page: ${page.id}, status: ${page.status}`);
        
        // 优先使用已发布版本，如果不存在则使用草稿版本
        const versionId = page.publishedVersionId || page.draftVersionId;
        
        // 添加日志记录版本ID
        this.logger.debug(`[GetPubPage] Using version ID: ${versionId}, publishedVersionId: ${page.publishedVersionId}, draftVersionId: ${page.draftVersionId}`);
        
        if (!versionId) {
            this.logger.error(`[GetPubPage] No version ID available for page: ${page.id}`);
            throw new NotFoundException('Page has no available version');
        }
        
        const currentPage = await this.prisma.pageVersion.findFirst({ 
            where: { id: versionId } 
        });
        
        if (!currentPage) {
            this.logger.error(`[GetPubPage] Page version not found for page: ${page.id}, status: ${page.status}`);
            throw new NotFoundException('Page version not found');
        }
        
        const website = await this.prisma.website.findFirst({ where: { id: page.websiteId } });
        const websiteConfig = website?.configuration as { theme?: string } || {};
        
        // 处理头尾数据 - 公开页面只使用已发布版本
        let headerData = null;
        if (page.currentHeader?.publishedVersion) {
            headerData = {
                id: page.currentHeader.id,
                language: page.currentHeader.language,
                variant: page.currentHeader.variant,
                configuration: page.currentHeader.publishedVersion.configuration
            };
            this.logger.debug(`[GetPubPage] Including header: ${page.currentHeader.id}, variant: ${page.currentHeader.variant}`);
        } else if (page.currentHeaderId) {
            this.logger.warn(`[GetPubPage] Header ${page.currentHeaderId} has no published version`);
        }
        
        let footerData = null;
        if (page.currentFooter?.publishedVersion) {
            footerData = {
                id: page.currentFooter.id,
                language: page.currentFooter.language,
                variant: page.currentFooter.variant,
                configuration: page.currentFooter.publishedVersion.configuration
            };
            this.logger.debug(`[GetPubPage] Including footer: ${page.currentFooter.id}, variant: ${page.currentFooter.variant}`);
        } else if (page.currentFooterId) {
            this.logger.warn(`[GetPubPage] Footer ${page.currentFooterId} has no published version`);
        }
        
        this.logger.log(`[GetPubPage] Successfully retrieved page: ${page.id}, title: ${page.title}`);
        
        return {
            success: true,
            data: {
                pageId: page.id,
                nanoid: page.nanoid,
                isPublished: page.status === PageStatus.PUBLISHED,
                title: page.title,
                configuration: currentPage.configuration,
                theme: websiteConfig.theme || currentPage.theme,
                header: headerData,
                footer: footerData
            }
        };
    }

    async updatePage(pageId: string, formUpdateDto: PageUpdateDto) {
        await this.prisma.page.update({
            where: { id: pageId },
            data: formUpdateDto,
        })
        return { success: true, data: { pageId, ...formUpdateDto } };
    }

    async deletePage(userId: string, pageId: string, confirmSlug: string) {
        // 查找页面
        const page = await this.prisma.page.findUnique({
            where: { id: pageId },
            include: {
                versions: true
            }
        });

        if (!page) {
            this.logger.error(`[DeletePage] Page not found with ID: ${pageId}`);
            throw new NotFoundException({
                message: 'Page not found',
                errorCode: ErrorCodes.PAGE.NOT_FOUND.code,
            });
        }

        // 验证用户权限
        await this.verifyUserPermission(userId, page.workspaceId);

        // 验证页面状态，只有归档状态的页面才能被删除
        if (page.status !== PageStatus.ARCHIVED) {
            this.logger.error(`[DeletePage] Cannot delete page that is not archived. Current status: ${page.status}`);
            throw new BadRequestException({
                message: 'Only archived pages can be deleted',
                errorCode: ErrorCodes.PAGE.INVALID_STATUS_TRANSITION.code,
            });
        }

        // 验证确认 slug
        if (page.slug !== confirmSlug) {
            this.logger.error(`[DeletePage] Slug confirmation failed. Expected: ${page.slug}, Received: ${confirmSlug}`);
            throw new BadRequestException({
                message: 'Slug confirmation does not match',
                errorCode: 'PAGE_SLUG_MISMATCH',
            });
        }

        // 执行软删除操作
        await this.prisma.$transaction(async (tx) => {
            // 更新页面状态为 DELETED
            await tx.page.update({
                where: { id: pageId },
                data: {
                    status: PageStatus.DELETED, // 使用枚举值
                }
            });

            // 记录审计日志
            await tx.pageAuditLog.create({
                data: {
                    pageId: page.id,
                    userId,
                    action: PageAuditAction.DELETED, // 使用枚举值
                    metadata: { // 使用 metadata 而不是 details
                        previousStatus: page.status,
                        newStatus: PageStatus.DELETED, // 使用枚举值
                    },
                }
            });
        });

        this.logger.log(`[DeletePage] Page deleted successfully: ${pageId}`);
        return { 
            success: true, 
            data: { 
                pageId, 
                isDeleted: true 
            } 
        };
    }

    async getPageVersion(pageId: string) {
        const data = await this.prisma.pageVersion.findMany({ where: { pageId } });
        return { success: true, data };
    }

    async updatePageVersion(pageId: string, pageVersionUpdateDto: PageVersionUpdateDto) {
        // 实际逻辑应该更新表单的特定版本
        return { success: true, data: { pageId, ...pageVersionUpdateDto } };
    }

    async createPageVersion(userId: string, dto: PageVersionCreateDto) {
        return await this.prisma.$transaction(async (tx) => {
            const page = await tx.page.findUnique({
                where: { id: dto.pageId }
            });

            if (!page) {
                throw new NotFoundException('Page not found');
            }

            // 获取最新版本号
            const latestVersion = await tx.pageVersion.findFirst({
                where: { pageId: page.id },
                orderBy: { versionNumber: 'desc' }
            });

            const newVersion = await tx.pageVersion.create({
                data: {
                    pageId: dto.pageId,
                    versionNumber: (latestVersion?.versionNumber ?? 0) + 1,
                    theme: 'default',
                    configuration: dto.configuration,
                    changelog: dto.changelog
                }
            });

            // 更新页面状态
            const updatedPage = await tx.page.update({
                where: { id: page.id },
                data: {
                    status: page.publishedVersionId ? PageStatus.MODIFIED : PageStatus.DRAFT,
                    draftVersionId: newVersion.id
                }
            });

            // 创建审计日志
            await tx.pageAuditLog.create({
                data: {
                    pageId: page.id,
                    userId,
                    action: PageAuditAction.VERSION_CREATED,
                    versionId: newVersion.id
                }
            });

            return { success: true, data: { page: updatedPage, version: newVersion } };
        });
    }

    async publishPage(userId: string, pageId: string, publishNote?: string) {
        this.logger.log(`Starting publish process for page ${pageId} by user ${userId}`);
        
        return await this.prisma.$transaction(async (tx) => {
            // 1. 获取页面及相关组件信息
            const page = await tx.page.findUnique({
                where: { id: pageId },
                include: {
                    currentHeader: true,
                    currentFooter: true
                }
            });

            if (!page) {
                this.logger.error(`Page not found: ${pageId}`);
                throw new NotFoundException('Page not found');
            }

            // 检查是否有任何组件需要发布
            const hasPageDraft = !!page.draftVersionId;
            let hasHeaderDraft = false;
            let hasFooterDraft = false;

            // 检查头部组件
            if (page.currentHeaderId) {
                const header = await tx.websiteHeader.findUnique({
                    where: { id: page.currentHeaderId }
                });
                hasHeaderDraft = !!(header && header.draftVersionId);
            }

            // 检查尾部组件
            if (page.currentFooterId) {
                const footer = await tx.websiteFooter.findUnique({
                    where: { id: page.currentFooterId }
                });
                hasFooterDraft = !!(footer && footer.draftVersionId);
            }

            // 如果没有任何组件需要发布，则抛出错误
            if (!hasPageDraft && !hasHeaderDraft && !hasFooterDraft) {
                this.logger.error(`No components to publish for page: ${pageId}`);
                throw new BadRequestException('No changes to publish');
            }

            this.logger.log(`Publishing components for page ${pageId}: Page draft: ${hasPageDraft}, Header draft: ${hasHeaderDraft}, Footer draft: ${hasFooterDraft}`);

            // 2. 创建发布记录
            const publishRecord = await tx.publishRecord.create({
                data: {
                    websiteId: page.websiteId,
                    publishedBy: userId,
                    status: 'SUCCESS', // PublishStatus.SUCCESS
                    publishNote: publishNote || null
                }
            });
            
            this.logger.log(`Created publish record: ${publishRecord.id}${publishNote ? ' with publish note' : ''}`);
            
            // 3. 发布页面（如果有草稿版本）
            let updatedPage;
            if (hasPageDraft) {
                updatedPage = await tx.page.update({
                    where: { id: pageId },
                    data: {
                        status: PageStatus.PUBLISHED,
                        publishedVersionId: page.draftVersionId,
                        draftVersionId: null,
                        publishedAt: new Date()
                    },
                    include: {
                        currentHeader: true,
                        currentFooter: true
                    }
                });

                // 创建页面发布组件记录
                await tx.publishedComponent.create({
                    data: {
                        publishRecordId: publishRecord.id,
                        componentType: 'PAGE', // ComponentType.PAGE
                        componentId: pageId,
                        language: page.language
                    }
                });
                
                this.logger.log(`Published page ${pageId}, updated status to PUBLISHED`);
            } else {
                // 如果没有页面草稿，但有头部或尾部草稿，仍需要获取更新后的页面
                updatedPage = await tx.page.findUnique({
                    where: { id: pageId },
                    include: {
                        currentHeader: true,
                        currentFooter: true
                    }
                });
                this.logger.log(`Page ${pageId} has no draft version, skipping page publishing`);
            }

            // 4. 发布头部组件（如果存在且有草稿版本）
            if (page.currentHeaderId) {
                const header = await tx.websiteHeader.findUnique({
                    where: { id: page.currentHeaderId }
                });
                
                if (header && header.draftVersionId) {
                    this.logger.log(`Found header ${header.id} with draft version ${header.draftVersionId}, publishing...`);
                    
                    // 更新头部组件状态
                    await tx.websiteHeader.update({
                        where: { id: header.id },
                        data: {
                            status: 'PUBLISHED', // ComponentStatus.PUBLISHED
                            publishedVersionId: header.draftVersionId,
                            draftVersionId: null
                        }
                    });
                    
                    // 更新头部版本状态
                    await tx.websiteHeaderVersion.update({
                        where: { id: header.draftVersionId },
                        data: {
                            status: 'PUBLISHED' // VersionStatus.PUBLISHED
                        }
                    });
                    
                    // 创建头部发布组件记录
                    await tx.publishedComponent.create({
                        data: {
                            publishRecordId: publishRecord.id,
                            componentType: 'HEADER', // ComponentType.HEADER
                            componentId: header.id,
                            headerVersionId: header.draftVersionId,
                            language: header.language
                        }
                    });
                    
                    this.logger.log(`Published header ${header.id}`);
                } else {
                    this.logger.log(`Header ${page.currentHeaderId} has no draft version to publish, skipping`);
                }
            } else {
                this.logger.log(`No header associated with page ${pageId}, skipping header publishing`);
            }

            // 5. 发布尾部组件（如果存在且有草稿版本）
            if (page.currentFooterId) {
                const footer = await tx.websiteFooter.findUnique({
                    where: { id: page.currentFooterId }
                });
                
                if (footer && footer.draftVersionId) {
                    this.logger.log(`Found footer ${footer.id} with draft version ${footer.draftVersionId}, publishing...`);
                    
                    // 更新尾部组件状态
                    await tx.websiteFooter.update({
                        where: { id: footer.id },
                        data: {
                            status: 'PUBLISHED', // ComponentStatus.PUBLISHED
                            publishedVersionId: footer.draftVersionId,
                            draftVersionId: null
                        }
                    });
                    
                    // 更新尾部版本状态
                    await tx.websiteFooterVersion.update({
                        where: { id: footer.draftVersionId },
                        data: {
                            status: 'PUBLISHED' // VersionStatus.PUBLISHED
                        }
                    });
                    
                    // 创建尾部发布组件记录
                    await tx.publishedComponent.create({
                        data: {
                            publishRecordId: publishRecord.id,
                            componentType: 'FOOTER', // ComponentType.FOOTER
                            componentId: footer.id,
                            footerVersionId: footer.draftVersionId,
                            language: footer.language
                        }
                    });
                    
                    this.logger.log(`Published footer ${footer.id}`);
                } else {
                    this.logger.log(`Footer ${page.currentFooterId} has no draft version to publish, skipping`);
                }
            } else {
                this.logger.log(`No footer associated with page ${pageId}, skipping footer publishing`);
            }

            // 6. 创建审计日志
            await tx.pageAuditLog.create({
                data: {
                    pageId,
                    userId,
                    action: PageAuditAction.PUBLISHED,
                    versionId: page.draftVersionId,
                    metadata: {
                        publishRecordId: publishRecord.id,
                        includedComponents: [
                            ...(hasPageDraft ? ['PAGE'] : []),
                            ...(hasHeaderDraft ? ['HEADER'] : []),
                            ...(hasFooterDraft ? ['FOOTER'] : [])
                        ]
                    }
                }
            });
            
            this.logger.log(`Created audit log for publish operation, publish process completed successfully`);

            return { success: true, data: updatedPage };
        });
    }

    async archivePage(userId: string, pageId: string) {
        return await this.prisma.$transaction(async (tx) => {
            const page = await tx.page.findUnique({
                where: { id: pageId }
            });

            if (!page) {
                throw new NotFoundException('Page not found');
            }

            const updatedPage = await tx.page.update({
                where: { id: pageId },
                data: { 
                    status: PageStatus.ARCHIVED,
                    // archivedAt: new Date()
                }
            });

            await tx.pageAuditLog.create({
                data: {
                    pageId,
                    userId,
                    action: PageAuditAction.ARCHIVED
                }
            });

            return { success: true, data: updatedPage };
        });
    }

    async updatePageStatus(userId: string, pageId: string, dto: PageStatusUpdateDto) {
        return await this.prisma.$transaction(async (tx) => {
            const page = await tx.page.findUnique({
                where: { id: pageId }
            });

            if (!page) {
                throw new NotFoundException('Page not found');
            }

            // 验证状态转换是否合法
            this.validateStatusTransition(page.status, dto.status);

            const updatedPage = await tx.page.update({
                where: { id: pageId },
                data: { status: dto.status }
            });

            await tx.pageAuditLog.create({
                data: {
                    pageId,
                    userId,
                    action: PageAuditAction.MODIFIED,
                    metadata: { 
                        oldStatus: page.status,
                        newStatus: dto.status
                    }
                }
            });

            return { success: true, data: updatedPage };
        });
    }

    private validateStatusTransition(currentStatus: PageStatus, newStatus: PageStatus) {
        const validTransitions = {
            [PageStatus.INITIALIZED]: [PageStatus.DRAFT],
            [PageStatus.DRAFT]: [PageStatus.PUBLISHED, PageStatus.ARCHIVED],
            [PageStatus.PUBLISHED]: [PageStatus.MODIFIED, PageStatus.ARCHIVED],
            [PageStatus.MODIFIED]: [PageStatus.PUBLISHED, PageStatus.ARCHIVED],
            [PageStatus.ARCHIVED]: [] // 归档状态不能转换到其他状态
        };

        if (!validTransitions[currentStatus]?.includes(newStatus)) {
            throw new BadRequestException(`Invalid status transition from ${currentStatus} to ${newStatus}`);
        }
    }

    async getPageConfig(domain: string, lang: string = '', slug: string) {
        console.log(`[getPageConfig] Input - domain: ${domain}, lang: ${lang}, slug: ${slug}`);

        // 检查是否为IP地址访问
        const isIpAddress = /^(\d{1,3}\.){3}\d{1,3}(:\d+)?$/.test(domain);
        if (isIpAddress) {
            console.log(`[getPageConfig] IP address access detected: ${domain}`);
            
            // 检查是否为潜在的路径遍历或敏感文件访问尝试
            if (slug.includes('.env') || slug.includes('/.') || slug.includes('../') || 
                slug.includes('/config') || slug.includes('/database') || slug.includes('/secrets') || 
                /\/(node_modules|core|src|functions|docker|etc|alpha|basic-network|configurations|extension|javascript|laravel|newsite|owncloud|packages|pemerintah|portal2|registration|site-library|spearmint|test|client|templates|saas|content|old|gitlab-ci|dev|blogs)\//.test(slug)) {
                
                console.log(`[getPageConfig] Potential security threat detected in slug: ${slug}`);
                return { 
                    success: false, 
                    message: 'Access denied', 
                    errorCode: 'SECURITY_VIOLATION'
                };
            }
            
            // 返回默认网站配置
            try {
                const defaultWebsite = await this.prisma.website.findFirst({
                    where: {
                        domain: process.env.DEFAULT_WEBSITE_DOMAIN || 'litpage'
                    }
                });

                if (!defaultWebsite) {
                    console.log(`[getPageConfig] Default website not configured`);
                    return { success: false, message: 'Website not found' };
                }

                console.log(`[getPageConfig] Using default website for IP access: ${defaultWebsite.domain}`);
                domain = defaultWebsite.domain;
            } catch (error) {
                console.log(`[getPageConfig] Error handling IP address access: ${error.message}`);
                return { success: false, message: 'Access denied' };
            }
        }

        // 提取子域名
        const resolvedDomain = this.canonicalUrlService.extractSubdomain(domain);
        console.log(`[getPageConfig] Resolved domain: ${resolvedDomain}, original: ${domain}`);

        // 查询网站，支持自定义域名和二级域名
        const website = await this.prisma.website.findFirst({
            where: {
                OR: [
                    { domain: resolvedDomain },                    // 二级域名匹配
                    { 
                        customDomain: { 
                            domain: domain,                        // 自定义域名匹配
                            status: 'ACTIVE'                       // 仅活跃状态
                        } 
                    }
                ]
            },
            include: {
                customDomain: true                                 // 包含自定义域名信息
            }
        });
        console.log('[getPageConfig] Website details:', {
            id: website?.id,
            domain: website?.domain,
            customDomain: website?.customDomain?.domain,
            customDomainStatus: website?.customDomain?.status,
            defaultLanguage: website?.defaultLanguage,
            headers: website?.headers,
            footers: website?.footers,
            configuration: website?.configuration
        });

        const websiteConfig = website?.configuration as { 
            theme?: string; 
            pageWidth?: string; 
            colorMode?: string; 
            [key: string]: any; 
        } || {};

        if (!website) {
            console.log(`[getPageConfig] Error: Website not found for domain: ${domain}`);
            return { success: false, message: 'Website not found' };
        }

        // Log all pages for this website
        const allPages = await this.prisma.page.findMany({
            where: {
                websiteId: website.id,
            },
            select: {
                id: true,
                slug: true,
                name: true,
                language: true,
                status: true,
                createdAt: true,
                nanoid: true,  // 添加 nanoid 字段
            },
            orderBy: {
                createdAt: 'desc'
            }
        });
        console.log('[getPageConfig] All pages for website:', JSON.stringify(allPages, null, 2));

        let language = !lang ? website.defaultLanguage.toLowerCase() : lang;
        console.log(`[getPageConfig] Using language: ${language} (default: ${website.defaultLanguage})`);

        const pageLanguage = languageMap[language.toUpperCase()];
        if (!pageLanguage) {
            console.log(`[getPageConfig] Error: Invalid language: ${language}`);
            return { success: false, message: 'Invalid language' };
        }

        const page = await this.prisma.page.findFirst({
            where: {
                slug,
                websiteId: website.id,
                language: pageLanguage,
            },
            include: {
                currentHeader: {
                    include: {
                        publishedVersion: true
                    }
                },
                currentFooter: {
                    include: {
                        publishedVersion: true
                    }
                },
                seo: true // 同时获取页面 SEO 数据
            }
        });
        console.log(`[getPageConfig] Found page:`, page ? `id: ${page.id}, status: ${page.status}` : 'null');

        if (!page) {
            console.log(`[getPageConfig] Error: Page not found - slug: ${slug}, websiteId: ${website.id}`);
            throw new NotFoundException({
                message: 'Page not found',
                errorCode: ErrorCodes.PAGE.NOT_FOUND.code,
            });
        }

        if (page.status === PageStatus.INITIALIZED) {
            console.log(`[getPageConfig] Error: Page not created - id: ${page.id}`);
            throw new HttpException({
                message: 'Page not created',
                errorCode: ErrorCodes.PAGE.NOT_CREATED.code,
            }, HttpStatus.BAD_REQUEST);
        }

        if (!page.publishedVersionId) {
            console.log(`[getPageConfig] Error: Page not published - id: ${page.id}`);
            throw new HttpException({
                message: 'Page not published',
                errorCode: ErrorCodes.PAGE.NOT_PUBLISHED.code,
            }, HttpStatus.BAD_REQUEST);
        }

        const currentPage = await this.prisma.pageVersion.findFirst({
            where: { 
                id: page.status === PageStatus.PUBLISHED 
                    ? page.publishedVersionId 
                    : page.draftVersionId 
            } 
        });
        console.log(`[getPageConfig] Found page version:`, currentPage ? `id: ${currentPage.id}` : 'null');

        // 获取头部和尾部组件的最新发布版本
        let headerData = null;
        if (page.currentHeader && page.currentHeader.publishedVersion) {
            headerData = page.currentHeader.publishedVersion.configuration;
            console.log(`[getPageConfig] Using published header version: ${page.currentHeader.publishedVersion.id}`);
        }

        let footerData = null;
        if (page.currentFooter && page.currentFooter.publishedVersion) {
            footerData = page.currentFooter.publishedVersion.configuration;
            console.log(`[getPageConfig] Using published footer version: ${page.currentFooter.publishedVersion.id}`);
        }

        // 获取网站所有页面，用于构建语言版本映射
        const allPagesForLanguage = await this.prisma.page.findMany({
            where: {
                websiteId: website.id,
            },
            select: {
                id: true,
                slug: true,
                name: true,
                language: true,
                status: true,
                createdAt: true,
                nanoid: true,  // 添加 nanoid 字段
            },
            orderBy: {
                createdAt: 'desc'
            }
        });
        console.log('[getPageConfig] All pages for website:', JSON.stringify(allPagesForLanguage, null, 2));

        // 获取当前 slug 的所有语言版本
        const translatedVersions = allPagesForLanguage.filter(p => p.slug === slug && p.status !== PageStatus.INITIALIZED);
        console.log(`[getPageConfig] Found ${translatedVersions.length} translated versions for slug: ${slug}`);

        // 获取网站支持的所有语言
        const supportedLanguages = [...new Set(allPagesForLanguage.map(p => p.language))];
        console.log(`[getPageConfig] Website supported languages: ${supportedLanguages.join(', ')}`);

        // 创建已翻译语言的映射
        const translatedLanguagesMap = translatedVersions.reduce((acc, p) => {
            acc[p.language] = {
                id: p.id,
                nanoid: p.nanoid,
                status: p.status
            };
            return acc;
        }, {} as Record<string, { id: string, nanoid: string, status: string }>);

        console.log(`[getPageConfig] Created language versions map with ${Object.keys(translatedLanguagesMap).length} entries`);

        // 4. 构建语言 URL 映射
        const languageUrlMap: Record<string, string> = {};
        
        // 为每种支持的语言构建 URL
        supportedLanguages.forEach(lang => {
            if (translatedLanguagesMap[lang]) {
                // 该语言有对应的页面版本，使用相同的 slug
                if (lang === website.defaultLanguage) {
                    // 默认语言不带语言前缀
                    languageUrlMap[lang] = `${slug}`;
                } else {
                    // 非默认语言带语言前缀
                    languageUrlMap[lang] = `/${lang.toLowerCase()}${slug}`;
                }
            } else {
                // 该语言没有对应的页面版本，使用首页
                if (lang === website.defaultLanguage) {
                    // 默认语言首页不带语言前缀
                    languageUrlMap[lang] = `/`;
                } else {
                    // 非默认语言首页带语言前缀
                    languageUrlMap[lang] = `/${lang.toLowerCase()}/`;
                }
            }
        });

        console.log('[getPageConfig] Language URL map:', languageUrlMap);

        // 获取语言名称映射
        const languageNames = this.getLanguageNames();

        // 获取页面的 SEO 数据和网站 SEO 数据
        const pageSeo = page.seo;

        const websiteSeo = await this.prisma.websiteSEO.findUnique({
            where: { websiteId: website.id }
        });

        // 添加调试日志，跟踪 WebsiteSEO 和 analyticsSettings 数据
        console.log(`[getPageConfig] WebsiteSEO data:`, { 
            found: !!websiteSeo,
            websiteSeoId: websiteSeo?.id,
            hasAnalyticsSettings: !!websiteSeo?.analyticsSettings,
            analyticsSettings: websiteSeo?.analyticsSettings
        });

        // 处理继承逻辑
        const shouldInherit = pageSeo?.inheritFromSite !== false;

        // 处理标题（考虑标题模板和继承）
        let finalTitle = pageSeo?.title || page.title;
        if (websiteSeo?.titleTemplate && (!pageSeo?.useCustomTitle || shouldInherit)) {
            finalTitle = websiteSeo.titleTemplate.replace('%page_title%', finalTitle);
        }

        // 处理描述（考虑默认描述和继承）
        let finalDescription = pageSeo?.description || page.description;
        if (!finalDescription && shouldInherit && websiteSeo?.defaultDescription) {
            finalDescription = websiteSeo.defaultDescription;
        }

        // 使用新的canonical URL服务生成SEO信息
        const domainInfo = this.canonicalUrlService.resolveDomainInfo(website, domain);
        
        // 为canonical服务转换translatedVersions为完整的Page对象
        const translatedPages = translatedVersions.map(tv => ({
            ...tv,
            // 补充缺少的Page字段，使用默认值
            userId: page.userId,
            websiteId: page.websiteId,
            title: tv.name, // 使用name作为title
            description: null,
            metaKeywords: [],
            metaDescription: null,
            ogTitle: null,
            ogDescription: null,
            ogImage: null,
            tags: [],
            isTemplate: false,
            pageType: page.pageType, // 使用当前页面的pageType
            workspaceId: page.workspaceId,
            draftVersionId: null,
            publishedVersionId: null,
            publishedAt: null,
            updatedAt: tv.createdAt,
            currentHeaderId: null,
            currentFooterId: null
        }));
        
        const canonicalResult = this.canonicalUrlService.generateCanonicalResult(
            domainInfo,
            page,
            translatedPages,
            website
        );

        console.log(`[getPageConfig] Generated canonical info`, {
            isCustomDomain: domainInfo.isCustomDomain,
            canonicalDomain: domainInfo.canonicalDomain,
            canonicalUrl: canonicalResult.canonicalUrl,
            alternateCount: canonicalResult.alternateUrls.length
        });

        // 构建完整的 SEO 相关信息
        const seo = {
            // 使用新的canonical逻辑
            canonicalUrl: canonicalResult.canonicalUrl,
            alternateUrls: canonicalResult.alternateUrls,
            
            // 基本 SEO 字段
            title: finalTitle, // 使用处理后的 SEO 标题
            description: finalDescription, // 使用处理后的 SEO 描述
            keywords: pageSeo?.keywords || (shouldInherit ? websiteSeo?.globalKeywords : []) || [],
            
            // 社交媒体字段
            ogTitle: pageSeo?.ogTitle || (shouldInherit && websiteSeo?.ogTitleTemplate ? 
                websiteSeo.ogTitleTemplate.replace('%page_title%', finalTitle) : undefined),
            ogDescription: pageSeo?.ogDescription || (shouldInherit && websiteSeo?.ogDescriptionTemplate ? 
                websiteSeo.ogDescriptionTemplate.replace('%page_description%', finalDescription || '') : undefined),
            ogImage: pageSeo?.ogImage || (shouldInherit ? websiteSeo?.defaultOgImage : undefined),
            
            // X Card 完整数据
            xTitle: pageSeo?.xTitle || pageSeo?.ogTitle || (shouldInherit ? finalTitle : undefined),
            xDescription: pageSeo?.xDescription || pageSeo?.ogDescription || (shouldInherit ? finalDescription : undefined),
            xImage: pageSeo?.xImage || pageSeo?.ogImage || (shouldInherit ? websiteSeo?.defaultOgImage : undefined),
            xCardType: pageSeo?.xCardType || (shouldInherit ? websiteSeo?.defaultXCard : undefined) || 'SUMMARY_LARGE_IMAGE',
            xCardData: pageSeo?.xCardData, // 特殊类型的 X Card 数据
            xUsername: shouldInherit ? websiteSeo?.xUsername : undefined,
            
            // 结构化数据
            schemaType: pageSeo?.schemaType,
            schemaData: pageSeo?.schemaData,
            organizationSchema: shouldInherit ? websiteSeo?.organizationSchema : undefined,
            websiteSchema: shouldInherit ? websiteSeo?.websiteSchema : undefined,
            
            // 高级设置
            robots: pageSeo?.robots,
            priority: pageSeo?.priority,
            changeFrequency: pageSeo?.changeFrequency,
            
            // 网站级设置
            siteName: shouldInherit ? websiteSeo?.siteName : undefined,
            siteDescription: shouldInherit ? websiteSeo?.siteDescription : undefined,
            
            // 分析设置
            analyticsSettings: shouldInherit ? websiteSeo?.analyticsSettings : undefined,
            
            // 品牌设置
            brandSettings: shouldInherit ? websiteSeo?.brandSettings : undefined
        };

        console.log(`[getPageConfig] Generated SEO information`, { 
            canonicalUrl: seo.canonicalUrl,
            alternateUrls: seo.alternateUrls.map(alt => `${alt.language}: ${alt.url}`),
            title: seo.title,
            ogTitle: seo.ogTitle,
            xCardType: seo.xCardType,
            schemaType: seo.schemaType,
            hasSchemaData: !!seo.schemaData
        });

        const response = {
            success: true,
            data: {
                subdomain: website.domain,
                pageId: page.id,
                nanoid: page.nanoid,
                status: page.status,
                title: finalTitle, // 使用处理后的 SEO 标题
                description: finalDescription, // 使用处理后的 SEO 描述
                header: headerData,
                configuration: currentPage.configuration,
                footer: footerData,
                theme: websiteConfig.theme || currentPage.theme,
                // 添加语言相关信息
                languageInfo: {
                    currentLanguage: page.language,
                    defaultLanguage: website.defaultLanguage,
                    supportedLanguages,
                    translatedLanguages: Object.keys(translatedLanguagesMap),
                    languageNames
                },
                languageVersions: translatedLanguagesMap,
                languageUrls: languageUrlMap,
                // 添加完整的 SEO 相关信息
                seo,
                // 添加网站级配置
                websiteConfig: {
                    pageWidth: websiteConfig.pageWidth || 'normal',
                    colorMode: websiteConfig.colorMode || 'light',
                    theme: websiteConfig.theme || 'default',
                    ...websiteConfig
                }
            }
        };
        console.log(`[getPageConfig] Returning successful response for page: ${page.id}`);
        return response;
    }

    async validateSlug(websiteId: string, slug: string, language?: Language): Promise<boolean> {
        // 检查该网站下是否已存在相同的 slug 和语言
        const whereClause: any = {
            websiteId,
            slug,
        };

        // 如果提供了语言，则加入语言条件
        if (language) {
            whereClause.language = language;
        }

        const existingPage = await this.prisma.page.findFirst({
            where: whereClause,
        });

        // 如果没有找到页面，说明 slug 可用
        return !existingPage;
    }

    async restoreVersion(userId: string, pageId: string, versionId: string) {
        return await this.prisma.$transaction(async (tx) => {
            // 1. 验证页面是否存在
            const page = await tx.page.findUnique({
                where: { id: pageId }
            });

            if (!page) {
                throw new NotFoundException('Page not found');
            }

            // 2. 获取目标版本
            const targetVersion = await tx.pageVersion.findUnique({
                where: { id: versionId }
            });

            if (!targetVersion || targetVersion.pageId !== pageId) {
                throw new NotFoundException('Version not found or does not belong to this page');
            }

            // 获取最新版本号
            const latestVersion = await tx.pageVersion.findFirst({
                where: { pageId },
                orderBy: [
                    { createdAt: 'desc' }
                ]
            });

            // 计算新版本号，确保递增且有效
            let newVersionNumber = 1;
            if (latestVersion) {
                newVersionNumber = latestVersion.versionNumber ? 
                    latestVersion.versionNumber + 1 : 
                    Math.floor(Date.now() / 1000); // 使用时间戳作为基础版本号
            }

            const newVersion = await tx.pageVersion.create({
                data: {
                    pageId,
                    versionNumber: newVersionNumber,
                    theme: targetVersion.theme,
                    configuration: targetVersion.configuration,
                    changelog: `Restored from version ${targetVersion.versionNumber || '(legacy)'}`
                }
            });

            // 4. 更新页面状态
            const updatedPage = await tx.page.update({
                where: { id: pageId },
                data: {
                    status: page.publishedVersionId ? PageStatus.MODIFIED : PageStatus.DRAFT,
                    draftVersionId: newVersion.id
                }
            });

            // 5. 创建审计日志
            await tx.pageAuditLog.create({
                data: {
                    pageId: page.id,
                    userId,
                    action: PageAuditAction.VERSION_RESTORED,
                    versionId: newVersion.id,
                    metadata: {
                        restoredFromVersion: targetVersion.versionNumber,
                        restoredFromVersionId: versionId
                    }
                }
            });

            return {
                success: true,
                data: {
                    page: updatedPage,
                    version: newVersion
                },
                message: `Successfully restored from version ${targetVersion.versionNumber}`
            };
        });
    }

    /**
     * 获取页面及其所有语言版本
     * @param domain 网站域名
     * @param slug 页面路径
     * @param lang 请求的语言
     * @returns 页面数据及其所有语言版本的URL映射
     */
    async getPageWithLanguageVersions(domain: string, slug: string, lang: string = '') {
        this.logger.log('[GetPageWithLanguageVersions] Request', { domain, slug, lang });
        
        try {
            // 检查参数有效性
            if (!domain) {
                this.logger.error('[GetPageWithLanguageVersions] Missing required parameter: domain');
                throw new BadRequestException('Domain is required');
            }
            
            if (!slug) {
                this.logger.error('[GetPageWithLanguageVersions] Missing required parameter: slug');
                throw new BadRequestException('Slug is required');
            }
            
            // 检查是否为IP地址访问
            const isIpAddress = /^(\d{1,3}\.){3}\d{1,3}(:\d+)?$/.test(domain);
            if (isIpAddress) {
                this.logger.debug(`[GetPageWithLanguageVersions] IP address access detected: ${domain}`);
                
                // 检查是否为潜在的路径遍历或敏感文件访问尝试
                if (slug.includes('.env') || slug.includes('/.') || slug.includes('../') || 
                    slug.includes('/config') || slug.includes('/database') || slug.includes('/secrets') || 
                    /\/(node_modules|core|src|functions|docker|etc|alpha|basic-network|configurations|extension|javascript|laravel|newsite|owncloud|packages|pemerintah|portal2|registration|site-library|spearmint|test|client|templates|saas|content|old|gitlab-ci|dev|blogs)\//.test(slug)) {
                    
                    this.logger.warn(`[GetPageWithLanguageVersions] Potential security threat detected in slug: ${slug}`);
                    return { 
                        success: false, 
                        message: 'Access denied', 
                        errorCode: 'SECURITY_VIOLATION'
                    };
                }
                
                // 使用默认网站
                const defaultWebsite = await this.prisma.website.findFirst({
                    where: {
                        domain: process.env.DEFAULT_WEBSITE_DOMAIN || 'litpage'
                    }
                });

                if (!defaultWebsite) {
                    this.logger.error(`[GetPageWithLanguageVersions] Default website not configured`);
                    return { success: false, message: 'Website not found' };
                }

                this.logger.debug(`[GetPageWithLanguageVersions] Using default website for IP access: ${defaultWebsite.domain}`);
                domain = defaultWebsite.domain;
            }

            // 使用新的域名解析逻辑
            const resolvedDomain = this.canonicalUrlService.extractSubdomain(domain);
            this.logger.debug(`[GetPageWithLanguageVersions] Resolved domain: ${resolvedDomain}, original: ${domain}`);

            // 1. 获取网站信息，支持自定义域名
            const website = await this.prisma.website.findFirst({
                where: {
                    OR: [
                        { domain: resolvedDomain },                    // 二级域名匹配
                        { 
                            customDomain: { 
                                domain: domain,                        // 自定义域名匹配
                                status: 'ACTIVE'                       // 仅活跃状态
                            } 
                        }
                    ]
                },
                include: {
                    customDomain: true                                 // 包含自定义域名信息
                }
            });

            if (!website) {
                this.logger.error(`[GetPageWithLanguageVersions] Website not found for domain: ${domain}`);
                return { success: false, message: 'Website not found' };
            }

            this.logger.debug('[GetPageWithLanguageVersions] Website details:', {
                id: website.id,
                domain: website.domain,
                defaultLanguage: website.defaultLanguage
            });

            // 2. 确定请求的语言
            let language = !lang ? website.defaultLanguage : lang.toUpperCase();
            this.logger.debug(`[GetPageWithLanguageVersions] Using language: ${language} (default: ${website.defaultLanguage})`);

            const pageLanguage = languageMap[language];
            if (!pageLanguage) {
                this.logger.warn(`[GetPageWithLanguageVersions] Invalid language: ${language}, using default: ${website.defaultLanguage}`);
                language = website.defaultLanguage;
            }

            // 3. 获取请求的语言版本页面
            const page = await this.prisma.page.findFirst({
                where: {
                    slug,
                    websiteId: website.id,
                    language: pageLanguage,
                    status: { in: [PageStatus.PUBLISHED, PageStatus.MODIFIED] }
                }
            });

            this.logger.debug(`[GetPageWithLanguageVersions] Page lookup result:`, {
                found: !!page,
                pageId: page?.id,
                language: page?.language,
                status: page?.status
            });

            // 4. 获取所有已翻译的语言版本
            const translatedVersions = await this.prisma.page.findMany({
                where: {
                    websiteId: website.id,
                    slug,
                    status: { in: [PageStatus.PUBLISHED, PageStatus.MODIFIED] }
                },
                select: { 
                    language: true,
                    nanoid: true,
                    id: true
                }
            });

            this.logger.debug(`[GetPageWithLanguageVersions] Found ${translatedVersions.length} translated versions:`, {
                versions: translatedVersions.map(v => ({ language: v.language, id: v.id }))
            });

            // 5. 获取网站支持的所有语言
            const websiteLanguages = await this.prisma.page.findMany({
                where: {
                    websiteId: website.id,
                    status: { in: [PageStatus.PUBLISHED, PageStatus.MODIFIED] }
                },
                select: { language: true },
                distinct: ['language']
            });

            const supportedLanguages = websiteLanguages.map(p => p.language);
            this.logger.debug(`[GetPageWithLanguageVersions] Website supported languages: ${supportedLanguages.join(', ')}`);
            
            // 创建已翻译语言的映射
            const translatedLanguagesMap = translatedVersions.reduce((acc, p) => {
                acc[p.language] = true;
                return acc;
            }, {} as Record<string, boolean>);

            // 6. 构建语言 URL 映射
            const languageUrlMap: Record<string, string> = {};
            
            // 为每种支持的语言构建 URL
            supportedLanguages.forEach(lang => {
                if (translatedLanguagesMap[lang]) {
                    // 该语言有对应的页面版本，使用相同的 slug
                    if (lang === website.defaultLanguage) {
                        // 默认语言不带语言前缀
                        languageUrlMap[lang] = `${slug}`;
                    } else {
                        // 非默认语言带语言前缀
                        languageUrlMap[lang] = `/${lang.toLowerCase()}${slug}`;
                    }
                } else {
                    // 该语言没有对应的页面版本，使用首页
                    if (lang === website.defaultLanguage) {
                        // 默认语言首页不带语言前缀
                        languageUrlMap[lang] = `/`;
                    } else {
                        // 非默认语言首页带语言前缀
                        languageUrlMap[lang] = `/${lang.toLowerCase()}/`;
                    }
                }
            });

            this.logger.debug('[GetPageWithLanguageVersions] Language URL map', { 
                languageUrlMap,
                currentSlug: slug,
                translatedLanguages: Object.keys(translatedLanguagesMap)
            });

            // 7. 如果请求的语言版本不存在，使用默认语言或任何可用版本
            let fallbackPage = null;
            let languageFallback = null;
            
            if (!page && translatedVersions.length > 0) {
                // 优先使用默认语言，如果没有则使用任何可用版本
                const fallbackLanguage = translatedLanguagesMap[website.defaultLanguage]
                    ? website.defaultLanguage
                    : Object.keys(translatedLanguagesMap)[0];
                    
                this.logger.debug('[GetPageWithLanguageVersions] Using fallback language', { 
                    requestedLanguage: language, 
                    fallbackLanguage 
                });
                    
                fallbackPage = await this.prisma.page.findFirst({
                    where: {
                        websiteId: website.id,
                        slug,
                        language: fallbackLanguage as Language,
                        status: { in: [PageStatus.PUBLISHED, PageStatus.MODIFIED] }
                    }
                });
                
                if (fallbackPage) {
                    this.logger.debug('[GetPageWithLanguageVersions] Found fallback page', {
                        fallbackPageId: fallbackPage.id,
                        fallbackLanguage: fallbackPage.language
                    });
                    
                    languageFallback = {
                        requestedLanguage: language,
                        actualLanguage: fallbackLanguage
                    };
                } else {
                    this.logger.warn('[GetPageWithLanguageVersions] Failed to find fallback page', {
                        websiteId: website.id,
                        slug,
                        fallbackLanguage
                    });
                }
            }

            // 8. 如果页面完全不存在，返回 404
            if (!page && !fallbackPage) {
                this.logger.error('[GetPageWithLanguageVersions] Page not found', { 
                    websiteId: website.id, 
                    slug, 
                    language,
                    supportedLanguages,
                    translatedVersions: translatedVersions.map(v => v.language)
                });
                throw new NotFoundException({
                    message: 'Page not found',
                    errorCode: ErrorCodes.PAGE.NOT_FOUND.code,
                });
            }

            // 使用找到的页面或回退页面
            const activePage = page || fallbackPage;
            this.logger.debug('[GetPageWithLanguageVersions] Using active page', {
                pageId: activePage.id,
                language: activePage.language,
                isOriginalRequest: page !== null,
                isFallback: fallbackPage !== null
            });
            
            // 9. 获取页面版本
            const currentPageVersion = await this.prisma.pageVersion.findFirst({
                where: { 
                    id: activePage.status === PageStatus.PUBLISHED 
                        ? activePage.publishedVersionId 
                        : activePage.draftVersionId 
                } 
            });

            if (!currentPageVersion) {
                this.logger.error('[GetPageWithLanguageVersions] Page version not found', { 
                    pageId: activePage.id,
                    publishedVersionId: activePage.publishedVersionId,
                    draftVersionId: activePage.draftVersionId
                });
                throw new NotFoundException('Page version not found');
            }

            this.logger.debug('[GetPageWithLanguageVersions] Found page version', {
                versionId: currentPageVersion.id,
                versionNumber: currentPageVersion.versionNumber,
                theme: currentPageVersion.theme
            });

            // 10. 获取语言名称映射
            const languageNames = this.getLanguageNames();

            // 11. 构建响应
            const websiteConfig = website.configuration as { theme?: string } || {};
            
            // 使用新的canonical URL服务生成SEO信息
            const domainInfo = this.canonicalUrlService.resolveDomainInfo(website, domain);
            
            // 构建简化的翻译页面数组用于canonical服务
            const simplifiedTranslatedPages = translatedVersions.map(tv => ({
                ...activePage, // 使用activePage作为基础
                id: tv.id,
                nanoid: tv.nanoid,
                language: tv.language
            }));
            
            const canonicalResult = this.canonicalUrlService.generateCanonicalResult(
                domainInfo,
                activePage,
                simplifiedTranslatedPages,
                website
            );
            
            // 构建 SEO 相关信息
            const seo = {
                canonicalUrl: canonicalResult.canonicalUrl,
                alternateUrls: canonicalResult.alternateUrls
            };

            this.logger.debug(`[GetPageWithLanguageVersions] Generated SEO information`, { 
                canonicalUrl: seo.canonicalUrl,
                alternateUrls: seo.alternateUrls
            });
            
            const response = {
                success: true,
                data: {
                    pageId: activePage.id,
                    nanoid: activePage.nanoid,
                    status: activePage.status,
                    title: activePage.title,
                    description: activePage.description,
                    slug: activePage.slug,
                    language: activePage.language,
                    header: website.headers[activePage.language],
                    configuration: currentPageVersion.configuration,
                    footer: website.footers[activePage.language],
                    theme: websiteConfig.theme || currentPageVersion.theme,
                    // 添加语言相关信息
                    languageInfo: {
                        currentLanguage: activePage.language,
                        defaultLanguage: website.defaultLanguage,
                        supportedLanguages,
                        translatedLanguages: Object.keys(translatedLanguagesMap),
                        languageNames
                    },
                    languageVersions: translatedLanguagesMap,
                    languageUrls: languageUrlMap,
                    languageFallback,
                    // 添加SEO相关信息
                    seo
                }
            };

            this.logger.debug(`[GetPageWithLanguageVersions] Returning successful response for page: ${activePage.id}`, {
                language: activePage.language,
                supportedLanguages: supportedLanguages.length,
                translatedLanguages: Object.keys(translatedLanguagesMap).length,
                hasFallback: !!languageFallback
            });
            
            return response;
        } catch (error) {
            this.logger.error('[GetPageWithLanguageVersions] Error', { 
                error: error.message,
                stack: error.stack,
                domain,
                slug,
                lang
            });
            
            // 重新抛出 NestJS 异常
            if (error instanceof HttpException) {
                throw error;
            }
            
            // 包装其他错误
            throw new InternalServerErrorException({
                message: 'Internal server error',
                errorCode: 'INTERNAL_SERVER_ERROR'
            });
        }
    }

    /**
     * 获取页面版本历史
     * @param pageId 页面ID
     * @returns 页面版本列表
     */
    async getPageVersions(pageId: string) {
        try {
            // 验证页面是否存在
            const page = await this.prisma.page.findUnique({
                where: { id: pageId },
            });

            if (!page) {
                throw new NotFoundException('Page not found');
            }

            // 获取页面的所有版本，按版本号降序排列
            const versions = await this.prisma.pageVersion.findMany({
                where: { pageId },
                orderBy: { versionNumber: 'desc' },
                include: {
                    headerVersion: {
                        select: {
                            id: true,
                            versionNumber: true,
                            createdAt: true,
                            createdBy: true,
                        },
                    },
                    footerVersion: {
                        select: {
                            id: true,
                            versionNumber: true,
                            createdAt: true,
                            createdBy: true,
                        },
                    },
                },
            });

            // 获取当前页面的草稿和发布版本ID
            const currentPage = await this.prisma.page.findUnique({
                where: { id: pageId },
                select: {
                    draftVersionId: true,
                    publishedVersionId: true,
                },
            });

            // 为每个版本添加状态标记（是否为当前草稿或发布版本）
            const versionsWithStatus = versions.map(version => ({
                ...version,
                isDraftVersion: version.id === currentPage.draftVersionId,
                isPublishedVersion: version.id === currentPage.publishedVersionId,
            }));

            return versionsWithStatus;
        } catch (error) {
            this.logger.error(`Failed to get page versions: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * 获取特定版本详情
     * @param pageId 页面ID
     * @param versionId 版本ID
     * @returns 版本详情
     */
    async getPageVersionDetail(pageId: string, versionId: string) {
        try {
            // 验证页面是否存在
            const page = await this.prisma.page.findUnique({
                where: { id: pageId },
            });

            if (!page) {
                throw new NotFoundException('Page not found');
            }

            // 获取特定版本详情
            const version = await this.prisma.pageVersion.findUnique({
                where: { id: versionId },
                include: {
                    headerVersion: true,
                    footerVersion: true,
                },
            });

            if (!version || version.pageId !== pageId) {
                throw new NotFoundException('Version not found or does not belong to the specified page');
            }

            // 获取当前页面的草稿和发布版本ID
            const currentPage = await this.prisma.page.findUnique({
                where: { id: pageId },
                select: {
                    draftVersionId: true,
                    publishedVersionId: true,
                },
            });

            // 添加状态标记
            const versionWithStatus = {
                ...version,
                isDraftVersion: version.id === currentPage.draftVersionId,
                isPublishedVersion: version.id === currentPage.publishedVersionId,
            };

            return versionWithStatus;
        } catch (error) {
            this.logger.error(`Failed to get page version detail: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * 回滚到特定版本
     * @param userId 用户ID
     * @param pageId 页面ID
     * @param versionId 版本ID
     * @returns 回滚结果
     */
    async rollbackToVersion(userId: string, pageId: string, versionId: string) {
        try {
            // 验证页面是否存在
            const page = await this.prisma.page.findUnique({
                where: { id: pageId },
                include: {
                    website: true,
                },
            });

            if (!page) {
                throw new NotFoundException('Page not found');
            }

            // 获取目标版本
            const targetVersion = await this.prisma.pageVersion.findUnique({
                where: { id: versionId },
                include: {
                    headerVersion: true,
                    footerVersion: true,
                },
            });

            if (!targetVersion || targetVersion.pageId !== pageId) {
                throw new NotFoundException('Version not found or does not belong to the specified page');
            }

            // 获取最新版本号
            const latestVersion = await this.prisma.pageVersion.findFirst({
                where: { pageId },
                orderBy: { versionNumber: 'desc' },
                select: { versionNumber: true },
            });

            const newVersionNumber = latestVersion ? latestVersion.versionNumber + 1 : 1;

            // 创建新版本（基于目标版本）
            const newVersion = await this.prisma.pageVersion.create({
                data: {
                    pageId,
                    versionNumber: newVersionNumber,
                    theme: targetVersion.theme,
                    configuration: targetVersion.configuration,
                    changelog: `Rolled back to version ${targetVersion.versionNumber}`,
                    headerVersionId: targetVersion.headerVersionId,
                    footerVersionId: targetVersion.footerVersionId,
                    copiedFromVersionId: targetVersion.id,
                },
            });

            // 更新页面状态
            const updatedPage = await this.prisma.page.update({
                where: { id: pageId },
                data: {
                    draftVersionId: newVersion.id,
                    status: PageStatus.MODIFIED,
                },
            });

            // 记录审计日志
            await this.prisma.pageAuditLog.create({
                data: {
                    pageId,
                    userId,
                    action: PageAuditAction.VERSION_ROLLBACK,
                    versionId: newVersion.id,
                    metadata: {
                        rolledBackFromVersionId: targetVersion.id,
                        rolledBackFromVersionNumber: targetVersion.versionNumber,
                        newVersionId: newVersion.id,
                        newVersionNumber: newVersionNumber,
                    },
                },
            });

            return {
                success: true,
                message: 'Successfully rolled back to the specified version',
                newVersion,
            };
        } catch (error) {
            this.logger.error(`Failed to rollback to version: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Discard changes - reset draft version to published version
     * @param userId User ID
     * @param pageId Page ID
     * @returns Operation result
     */
    async discardChanges(userId: string, pageId: string) {
        this.logger.log(`Starting discard changes process for page ${pageId} by user ${userId}`);
        
        return await this.prisma.$transaction(async (tx) => {
            // 1. Get the page and check if it exists
            const page = await tx.page.findUnique({
                where: { id: pageId },
                include: {
                    currentHeader: true,
                    currentFooter: true
                }
            });

            if (!page) {
                this.logger.error(`Page not found: ${pageId}`);
                throw new NotFoundException('Page not found');
            }

            // 2. Check if the page has a draft version and a published version
            if (!page.draftVersionId) {
                this.logger.error(`No draft version to discard for page: ${pageId}`);
                throw new BadRequestException('No changes to discard');
            }

            if (!page.publishedVersionId) {
                this.logger.error(`No published version to revert to for page: ${pageId}`);
                throw new BadRequestException('No published version to revert to');
            }

            // 3. Update the page status and remove the draft version
            const updatedPage = await tx.page.update({
                where: { id: pageId },
                data: {
                    status: PageStatus.PUBLISHED,
                    draftVersionId: null
                },
                include: {
                    currentHeader: true,
                    currentFooter: true
                }
            });

            // 4. Create audit log
            await tx.pageAuditLog.create({
                data: {
                    pageId,
                    userId,
                    action: PageAuditAction.MODIFIED,
                    metadata: { 
                        discardedVersionId: page.draftVersionId,
                        action: 'CHANGES_DISCARDED'
                    }
                }
            });
            
            this.logger.log(`Successfully discarded changes for page ${pageId}`);
            return { success: true, data: updatedPage };
        });
    }

    /**
     * Restore an archived page
     * @param userId User ID
     * @param pageId Page ID
     * @returns Operation result
     */
    async restorePage(userId: string, pageId: string) {
        this.logger.log(`Starting restore process for page ${pageId} by user ${userId}`);
        
        return await this.prisma.$transaction(async (tx) => {
            // 1. Get the page and check if it exists
            const page = await tx.page.findUnique({
                where: { id: pageId }
            });

            if (!page) {
                this.logger.error(`Page not found: ${pageId}`);
                throw new NotFoundException('Page not found');
            }

            // 2. Check if the page is archived
            if (page.status !== PageStatus.ARCHIVED) {
                this.logger.error(`Page ${pageId} is not archived, current status: ${page.status}`);
                throw new BadRequestException('Only archived pages can be restored');
            }

            // 3. Determine the new status based on versions
            let newStatus: PageStatus = PageStatus.DRAFT;
            if (page.publishedVersionId) {
                newStatus = page.draftVersionId ? PageStatus.MODIFIED : PageStatus.PUBLISHED;
            }

            // 4. Update the page status
            const updatedPage = await tx.page.update({
                where: { id: pageId },
                data: { 
                    status: newStatus
                }
            });

            // 5. Create audit log
            await tx.pageAuditLog.create({
                data: {
                    pageId,
                    userId,
                    action: PageAuditAction.RESTORED,
                    metadata: {
                        previousStatus: page.status,
                        newStatus: newStatus
                    }
                }
            });
            
            this.logger.log(`Successfully restored page ${pageId} to status ${newStatus}`);
            return { success: true, data: updatedPage };
        });
    }

    /**
     * 获取受共享组件影响的页面列表
     * @param userId 用户ID
     * @param componentType 组件类型（HEADER 或 FOOTER）
     * @param componentId 组件ID
     * @returns 受影响的页面列表
     */
    async getAffectedPages(userId: string, componentType: string, componentId: string) {
        this.logger.log(`Getting pages affected by ${componentType} component ${componentId}`);
        
        try {
            // 验证组件类型
            if (!['HEADER', 'FOOTER'].includes(componentType.toUpperCase())) {
                throw new BadRequestException('Invalid component type. Must be HEADER or FOOTER');
            }
            
            // 获取组件语言
            let language: string | null = null;
            
            if (componentType.toUpperCase() === 'HEADER') {
                const header = await this.prisma.websiteHeader.findUnique({
                    where: { id: componentId },
                    select: { language: true, websiteId: true }
                });
                
                if (!header) {
                    throw new NotFoundException('Header component not found');
                }
                
                // 验证用户是否有权访问该网站
                await this.verifyWebsiteAccess(userId, header.websiteId);
                language = header.language;
                
            } else if (componentType.toUpperCase() === 'FOOTER') {
                const footer = await this.prisma.websiteFooter.findUnique({
                    where: { id: componentId },
                    select: { language: true, websiteId: true }
                });
                
                if (!footer) {
                    throw new NotFoundException('Footer component not found');
                }
                
                // 验证用户是否有权访问该网站
                await this.verifyWebsiteAccess(userId, footer.websiteId);
                language = footer.language;
            }
            
            if (!language) {
                throw new Error('Failed to determine component language');
            }
            
            // 获取使用该组件的页面
            const pages = await this.prisma.page.findMany({
                where: {
                    ...(componentType.toUpperCase() === 'HEADER' 
                        ? { currentHeaderId: componentId }
                        : { currentFooterId: componentId }),
                    status: {
                        not: 'DELETED'
                    }
                },
                select: {
                    id: true,
                    title: true,
                    slug: true,
                    status: true,
                    language: true,
                    websiteId: true,
                    website: {
                        select: {
                            domain: true
                        }
                    }
                },
                orderBy: {
                    updatedAt: 'desc'
                }
            });
            
            this.logger.log(`Found ${pages.length} pages affected by ${componentType} component ${componentId}`);
            
            return {
                success: true,
                data: pages,
                metadata: {
                    componentType,
                    componentId,
                    language
                }
            };
        } catch (error) {
            this.logger.error(`Error getting affected pages: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * 验证用户是否有权访问该网站
     * @param userId 用户ID
     * @param websiteId 网站ID
     * @returns 网站信息
     */
    private async verifyWebsiteAccess(userId: string, websiteId: string) {
        const website = await this.prisma.website.findFirst({
            where: {
                id: websiteId,
                userId
            }
        });
        
        if (!website) {
            throw new ForbiddenException('You do not have access to this website');
        }
        
        return website;
    }

    /**
     * 确保网站的头尾组件在指定语言下存在，如果不存在则创建
     * @param websiteId 网站ID
     * @param language 目标语言
     * @param defaultLanguage 默认语言
     */
    private async ensureHeaderFooterLanguageVersions(
        websiteId: string,
        language: Language,
        defaultLanguage: Language
    ) {
        this.logger.log(`[EnsureHeaderFooterLanguageVersions] Checking header and footer components for website ${websiteId} in ${language} language`);
        
        if (language === defaultLanguage) {
            this.logger.debug(`[EnsureHeaderFooterLanguageVersions] Current language ${language} is the default language, no need to create`);
            return;
        }

        return this.prisma.$transaction(async (tx) => {
            // 1. Check if header component exists for target language
            const existingHeader = await tx.websiteHeader.findFirst({
                where: { 
                    websiteId,
                    language,
                    isDeleted: false
                }
            });

            if (!existingHeader) {
                this.logger.debug(`[EnsureHeaderFooterLanguageVersions] Website ${websiteId} does not have a header component for ${language} language, preparing to create`);
                
                // Get header component for default language
                const defaultHeader = await tx.websiteHeader.findFirst({
                    where: {
                        websiteId,
                        language: defaultLanguage,
                        isDeleted: false
                    },
                    include: {
                        draftVersion: true
                    }
                });

                if (defaultHeader && defaultHeader.draftVersion) {
                    this.logger.debug(`[EnsureHeaderFooterLanguageVersions] Found header component ${defaultHeader.id} for default language ${defaultLanguage}`);
                    
                    // Create new header component for target language
                    const newHeader = await tx.websiteHeader.create({
                        data: {
                            websiteId,
                            language,
                            variant: defaultHeader.variant,
                            createdBy: defaultHeader.createdBy,
                            status: 'INITIALIZED',
                            isDeleted: false
                        }
                    });
                    
                    this.logger.debug(`[EnsureHeaderFooterLanguageVersions] Successfully created header component ${newHeader.id} for ${language} language`);
                    
                    // Copy configuration from default language to create new version
                    const newVersion = await tx.websiteHeaderVersion.create({
                        data: {
                            headerId: newHeader.id,
                            versionNumber: 1,
                            configuration: defaultHeader.draftVersion.configuration,
                            createdBy: defaultHeader.createdBy,
                            status: 'DRAFT',
                            changelog: `Automatically created ${language} language version`
                        }
                    });
                    
                    // Update header component to reference new version
                    await tx.websiteHeader.update({
                        where: { id: newHeader.id },
                        data: {
                            draftVersionId: newVersion.id
                        }
                    });
                    
                    this.logger.debug(`[EnsureHeaderFooterLanguageVersions] Successfully created header version ${newVersion.id} and linked to header component`);
                } else {
                    this.logger.warn(`[EnsureHeaderFooterLanguageVersions] Could not find header component or its draft version for default language ${defaultLanguage}, cannot create new language version`);
                }
            } else {
                this.logger.debug(`[EnsureHeaderFooterLanguageVersions] Website ${websiteId} already has a header component ${existingHeader.id} for ${language} language`);
            }

            // 2. Check if footer component exists for target language
            const existingFooter = await tx.websiteFooter.findFirst({
                where: { 
                    websiteId,
                    language,
                    isDeleted: false
                }
            });

            if (!existingFooter) {
                this.logger.debug(`[EnsureHeaderFooterLanguageVersions] Website ${websiteId} does not have a footer component for ${language} language, preparing to create`);
                
                // Get footer component for default language
                const defaultFooter = await tx.websiteFooter.findFirst({
                    where: {
                        websiteId,
                        language: defaultLanguage,
                        isDeleted: false
                    },
                    include: {
                        draftVersion: true
                    }
                });

                if (defaultFooter && defaultFooter.draftVersion) {
                    this.logger.debug(`[EnsureHeaderFooterLanguageVersions] Found footer component ${defaultFooter.id} for default language ${defaultLanguage}`);
                    
                    // Create new footer component for target language
                    const newFooter = await tx.websiteFooter.create({
                        data: {
                            websiteId,
                            language,
                            variant: defaultFooter.variant,
                            createdBy: defaultFooter.createdBy,
                            status: 'INITIALIZED',
                            isDeleted: false
                        }
                    });
                    
                    this.logger.debug(`[EnsureHeaderFooterLanguageVersions] Successfully created footer component ${newFooter.id} for ${language} language`);
                    
                    // Copy configuration from default language to create new version
                    const newVersion = await tx.websiteFooterVersion.create({
                        data: {
                            footerId: newFooter.id,
                            versionNumber: 1,
                            configuration: defaultFooter.draftVersion.configuration,
                            createdBy: defaultFooter.createdBy,
                            status: 'DRAFT',
                            changelog: `Automatically created ${language} language version`
                        }
                    });
                    
                    // Update footer component to reference new version
                    await tx.websiteFooter.update({
                        where: { id: newFooter.id },
                        data: {
                            draftVersionId: newVersion.id
                        }
                    });
                    
                    this.logger.debug(`[EnsureHeaderFooterLanguageVersions] Successfully created footer version ${newVersion.id} and linked to footer component`);
                } else {
                    this.logger.warn(`[EnsureHeaderFooterLanguageVersions] Could not find footer component or its draft version for default language ${defaultLanguage}, cannot create new language version`);
                }
            } else {
                this.logger.debug(`[EnsureHeaderFooterLanguageVersions] Website ${websiteId} already has a footer component ${existingFooter.id} for ${language} language`);
            }

            return { success: true };
        });
    }

    /**
     * 获取语言名称映射
     * @returns 语言代码到名称的映射
     */
    private getLanguageNames(): Record<string, string> {
        return {
            EN: 'English',
            CN: 'Simplified Chinese',
            ZH: 'Traditional Chinese',
            ES: 'Spanish',
            HI: 'Hindi',
            FR: 'French',
            DE: 'German',
            RU: 'Russian',
            PT: 'Portuguese',
            AR: 'Arabic',
            JP: 'Japanese',
            KR: 'Korean',
            IT: 'Italian',
            TR: 'Turkish',
            PL: 'Polish',
            NL: 'Dutch',
            ID: 'Indonesian',
            TH: 'Thai',
            VI: 'Vietnamese',
            SV: 'Swedish'
        };
    }

    /**
     * 更新页面使用的头尾组件
     * @param userId 用户ID
     * @param pageId 页面ID
     * @param dto 包含头尾组件ID的DTO
     * @returns 更新后的页面信息
     */
    async updatePageComponents(userId: string, pageId: string, dto: PageComponentsUpdateDto) {
        this.logger.log('[UpdatePageComponents] Starting update process', {
            userId,
            pageId,
            headerId: dto.headerId,
            footerId: dto.footerId
        });

        return await this.prisma.$transaction(async (tx: Prisma.TransactionClient) => {
            // 1. 验证页面存在
            const page = await tx.page.findFirst({
                where: { id: pageId }
            });
            
            if (!page) {
                this.logger.error(`[UpdatePageComponents] Page not found: ${pageId}`);
                throw new NotFoundException('Page not found');
            }
            
            this.logger.debug(`[UpdatePageComponents] Found page: ${pageId}, websiteId: ${page.websiteId}`);
            
            // 2. 验证头尾组件存在且属于同一网站
            if (dto.headerId) {
                const header = await tx.websiteHeader.findFirst({
                    where: { 
                        id: dto.headerId,
                        websiteId: page.websiteId,
                        isDeleted: false
                    }
                });
                
                if (!header) {
                    this.logger.error(`[UpdatePageComponents] Invalid header component: ${dto.headerId}, websiteId: ${page.websiteId}`);
                    throw new BadRequestException('Invalid header component');
                }
                
                this.logger.debug(`[UpdatePageComponents] Validated header component: ${dto.headerId}, variant: ${header.variant}`);
            }
            
            if (dto.footerId) {
                const footer = await tx.websiteFooter.findFirst({
                    where: { 
                        id: dto.footerId,
                        websiteId: page.websiteId,
                        isDeleted: false
                    }
                });
                
                if (!footer) {
                    this.logger.error(`[UpdatePageComponents] Invalid footer component: ${dto.footerId}, websiteId: ${page.websiteId}`);
                    throw new BadRequestException('Invalid footer component');
                }
                
                this.logger.debug(`[UpdatePageComponents] Validated footer component: ${dto.footerId}, variant: ${footer.variant}`);
            }
            
            // 3. 更新页面组件引用
            const updateData: any = {};
            
            if (dto.headerId !== undefined) {
                updateData.currentHeaderId = dto.headerId || null;
            }
            
            if (dto.footerId !== undefined) {
                updateData.currentFooterId = dto.footerId || null;
            }
            
            this.logger.debug('[UpdatePageComponents] Updating page components', updateData);
            
            const updatedPage = await tx.page.update({
                where: { id: pageId },
                data: updateData,
                include: {
                    currentHeader: {
                        include: {
                            draftVersion: true,
                            publishedVersion: true
                        }
                    },
                    currentFooter: {
                        include: {
                            draftVersion: true,
                            publishedVersion: true
                        }
                    }
                }
            });
            
            this.logger.debug(`[UpdatePageComponents] Page updated: ${pageId}, headerID: ${updatedPage.currentHeaderId}, footerId: ${updatedPage.currentFooterId}`);
            
            // 4. 创建审计日志
            const auditLogEntry = await tx.pageAuditLog.create({
                data: {
                    pageId,
                    userId,
                    action: PageAuditAction.MODIFIED,
                    metadata: { 
                        action: 'COMPONENTS_UPDATED',
                        oldHeaderId: page.currentHeaderId,
                        newHeaderId: dto.headerId,
                        oldFooterId: page.currentFooterId,
                        newFooterId: dto.footerId
                    }
                }
            });
            
            this.logger.debug(`[UpdatePageComponents] Created audit log: ${auditLogEntry.id}`);
            
            this.logger.log('[UpdatePageComponents] Components updated successfully', {
                pageId,
                headerId: updatedPage.currentHeaderId,
                footerId: updatedPage.currentFooterId
            });
            
            // 5. 处理返回数据
            // 处理头尾数据
            let headerData = null;
            if (updatedPage.currentHeader) {
                const headerVersion = updatedPage.currentHeader.draftVersion || updatedPage.currentHeader.publishedVersion;
                
                if (headerVersion) {
                    headerData = {
                        id: updatedPage.currentHeader.id,
                        language: updatedPage.currentHeader.language,
                        variant: updatedPage.currentHeader.variant,
                        status: updatedPage.currentHeader.status,
                        configuration: headerVersion.configuration
                    };
                    this.logger.debug(`[UpdatePageComponents] Included header data: ${updatedPage.currentHeader.id}, version: ${headerVersion.id}`);
                } else {
                    this.logger.warn(`[UpdatePageComponents] Header ${updatedPage.currentHeader.id} has no available version`);
                }
            }
            
            let footerData = null;
            if (updatedPage.currentFooter) {
                const footerVersion = updatedPage.currentFooter.draftVersion || updatedPage.currentFooter.publishedVersion;
                
                if (footerVersion) {
                    footerData = {
                        id: updatedPage.currentFooter.id,
                        language: updatedPage.currentFooter.language,
                        variant: updatedPage.currentFooter.variant,
                        status: updatedPage.currentFooter.status,
                        configuration: footerVersion.configuration
                    };
                    this.logger.debug(`[UpdatePageComponents] Included footer data: ${updatedPage.currentFooter.id}, version: ${footerVersion.id}`);
                } else {
                    this.logger.warn(`[UpdatePageComponents] Footer ${updatedPage.currentFooter.id} has no available version`);
                }
            }
            
            return {
                success: true,
                data: {
                    id: updatedPage.id,
                    currentHeaderId: updatedPage.currentHeaderId,
                    currentFooterId: updatedPage.currentFooterId,
                    header: headerData,
                    footer: footerData
                }
            };
        });
    }

    // 验证用户对工作空间的权限
    private async verifyUserPermission(userId: string, workspaceId: string) {
        // 查找页面所属的工作空间
        const workspace = await this.prisma.workspace.findUnique({
            where: { id: workspaceId },
            include: {
                user: true // 获取工作空间的所有者
            }
        });

        if (!workspace) {
            this.logger.error(`[VerifyUserPermission] Workspace not found: ${workspaceId}`);
            throw new NotFoundException({
                message: 'Workspace not found',
                errorCode: ErrorCodes.WEBSITE.NOT_FOUND.code,
            });
        }

        // 检查用户是否是工作空间的所有者
        if (workspace.user.id !== userId) {
            this.logger.error(`[VerifyUserPermission] User ${userId} does not have permission for workspace ${workspaceId}`);
            throw new HttpException({
                message: 'You do not have permission to perform this action',
                errorCode: ErrorCodes.AUTH.FORBIDDEN.code,
            }, HttpStatus.FORBIDDEN);
        }

        return true;
    }
}