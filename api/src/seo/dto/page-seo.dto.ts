import { IsNotEmpty, IsString, IsBoolean, IsArray, IsOptional, IsEnum, IsNumber, IsUrl, <PERSON><PERSON>teNested, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';
import { XCardType, SchemaType, ChangeFrequency } from '@prisma/client';
import { PartialType } from '@nestjs/swagger';

// 基本 SEO 设置 DTO
export class BasicSeoDto {
  @IsOptional()
  @IsString()
  title?: string;

  @IsOptional()
  @IsBoolean()
  useCustomTitle?: boolean;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  keywords?: string[];
}

// 社交媒体设置 DTO
export class SocialMediaDto {
  @IsOptional()
  @IsString()
  ogTitle?: string;

  @IsOptional()
  @IsString()
  ogDescription?: string;

  @IsOptional()
  @IsString()
  ogImage?: string;

  @IsOptional()
  @IsString()
  xTitle?: string;

  @IsOptional()
  @IsString()
  xDescription?: string;

  @IsOptional()
  @IsString()
  xImage?: string;

  @IsOptional()
  @IsEnum(XCardType)
  xCardType?: XCardType;

  @IsOptional()
  xCardData?: any; // 存储 APP 和 PLAYER 卡片特定数据
}

// 结构化数据设置 DTO
export class PageStructuredDataDto {
  @IsOptional()
  @IsEnum(SchemaType)
  schemaType?: SchemaType;

  @IsOptional()
  schemaData?: any; // JSON 类型
}

// 高级设置 DTO
export class AdvancedSeoDto {
  @IsOptional()
  @IsString()
  canonicalUrl?: string;

  @IsOptional()
  robots?: any; // JSON 类型

  @IsOptional()
  hreflangLinks?: any; // JSON 类型

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  priority?: number;

  @IsOptional()
  @IsEnum(ChangeFrequency)
  changeFrequency?: ChangeFrequency;
}

// 页面 SEO 继承设置 DTO
export class PageSeoInheritDto {
  @IsNotEmpty()
  @IsBoolean()
  inheritFromSite: boolean;

  @IsOptional()
  @IsString()
  websiteSeoId?: string;
}

// 完整的页面 SEO 创建 DTO
export class PageSeoCreateDto {
  @IsNotEmpty()
  @IsString()
  pageId: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => BasicSeoDto)
  basic?: BasicSeoDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => SocialMediaDto)
  socialMedia?: SocialMediaDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => PageStructuredDataDto)
  structuredData?: PageStructuredDataDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => AdvancedSeoDto)
  advanced?: AdvancedSeoDto;

  @IsOptional()
  contentAnalysis?: any; // JSON 类型

  @IsOptional()
  @ValidateNested()
  @Type(() => PageSeoInheritDto)
  inherit?: PageSeoInheritDto;
}

// 页面 SEO 更新 DTO (部分更新)
export class PageSeoUpdateDto extends PartialType(PageSeoCreateDto) {}

// 基本 SEO 更新 DTO
export class BasicSeoUpdateDto extends BasicSeoDto {}

// 社交媒体更新 DTO
export class SocialMediaUpdateDto extends SocialMediaDto {}

// 结构化数据更新 DTO
export class PageStructuredDataUpdateDto extends PageStructuredDataDto {}

// 高级设置更新 DTO
export class AdvancedSeoUpdateDto extends AdvancedSeoDto {}

// 页面 SEO 继承切换 DTO
export class PageSeoInheritToggleDto {
  @IsNotEmpty()
  @IsBoolean()
  inheritFromSite: boolean;
}
