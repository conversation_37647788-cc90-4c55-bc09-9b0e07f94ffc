import { IsNotEmpty, IsString, IsBoolean, IsArray, IsOptional, IsEnum, IsNumber, IsUrl, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { XCardType, SchemaType, ChangeFrequency } from '@prisma/client';
import { PartialType } from '@nestjs/swagger';

// 基础元数据 DTO
export class WebsiteMetadataDto {
  @IsOptional()
  @IsString()
  siteName?: string;

  @IsOptional()
  @IsString()
  siteDescription?: string;

  @IsOptional()
  @IsString()
  titleTemplate?: string;

  @IsOptional()
  @IsString()
  defaultDescription?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  globalKeywords?: string[];
}

// Robots 和 Sitemap DTO
export class RobotsSitemapDto {
  @IsOptional()
  @IsString()
  robotsTxt?: string;

  @IsOptional()
  sitemapSettings?: any; // JSON 类型
}

// 社交媒体默认设置 DTO
export class SocialMediaDefaultsDto {
  @IsOptional()
  @IsString()
  defaultOgImage?: string;

  @IsOptional()
  @IsString()
  ogTitleTemplate?: string;

  @IsOptional()
  @IsString()
  ogDescriptionTemplate?: string;

  @IsOptional()
  @IsEnum(XCardType)
  defaultXCard?: XCardType;

  @IsOptional()
  @IsString()
  xUsername?: string;
}

// 安全和性能设置 DTO
export class SecurityPerformanceDto {
  @IsOptional()
  @IsBoolean()
  forceHttps?: boolean;

  @IsOptional()
  @IsBoolean()
  httpToHttpsRedirect?: boolean;

  @IsOptional()
  @IsString()
  cacheControl?: string;

  @IsOptional()
  @IsBoolean()
  resourceCompression?: boolean;

  @IsOptional()
  @IsBoolean()
  browserCaching?: boolean;

  @IsOptional()
  @IsBoolean()
  lazyLoadingImages?: boolean;
}

// 结构化数据 DTO
export class StructuredDataDto {
  @IsOptional()
  organizationSchema?: any; // JSON 类型

  @IsOptional()
  websiteSchema?: any; // JSON 类型
}

// Hreflang 设置 DTO
export class LanguageLinkDto {
  @IsString()
  code: string;

  @IsString()
  region: string;

  @IsString()
  url: string;
}

export class HreflangSettingsDto {
  @IsOptional()
  @IsString()
  defaultLanguage?: string;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => LanguageLinkDto)
  supportedLanguages?: LanguageLinkDto[];
}

// Analytics 设置 DTO
export class AnalyticsSettingsDto {
  @IsOptional()
  @IsString()
  googleAnalyticsId?: string;

  @IsOptional()
  @IsString()
  googleTagManagerId?: string;

  @IsOptional()
  @IsString()
  searchConsoleVerification?: string;

  @IsOptional()
  @IsString()
  customHeadScripts?: string;

  @IsOptional()
  otherAnalyticsTools?: Array<{
    name: string;
    scriptUrl?: string;
    scriptContent?: string;
    position?: string;
  }>;
}

// Brand Settings DTO
export class BrandSettingsDto {
  @IsOptional()
  @IsString()
  brandName?: string;

  @IsOptional()
  @IsString()
  brandTagline?: string;

  @IsOptional()
  @IsString()
  brandDescription?: string;

  @IsOptional()
  @IsString()
  brandLogoUrl?: string;

  @IsOptional()
  @IsString()
  brandFaviconUrl?: string;

  @IsOptional()
  brandColors?: {
    primary: string;
    secondary?: string;
  };

  @IsOptional()
  socialProfiles?: Array<{
    platform: string;
    url: string;
  }>;
}

// 完整的网站 SEO 创建 DTO
export class WebsiteSeoCreateDto {
  @IsNotEmpty()
  @IsString()
  websiteId: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => WebsiteMetadataDto)
  metadata?: WebsiteMetadataDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => RobotsSitemapDto)
  robotsSitemap?: RobotsSitemapDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => SocialMediaDefaultsDto)
  socialDefaults?: SocialMediaDefaultsDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => SecurityPerformanceDto)
  securityPerformance?: SecurityPerformanceDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => StructuredDataDto)
  structuredData?: StructuredDataDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => HreflangSettingsDto)
  hreflangSettings?: HreflangSettingsDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => AnalyticsSettingsDto)
  analyticsSettings?: AnalyticsSettingsDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => BrandSettingsDto)
  brandSettings?: BrandSettingsDto;

  @IsOptional()
  seoScores?: any; // JSON 类型
}

// 网站 SEO 更新 DTO (部分更新)
export class WebsiteSeoUpdateDto extends PartialType(WebsiteSeoCreateDto) {}

// 网站元数据更新 DTO
export class WebsiteMetadataUpdateDto extends WebsiteMetadataDto {}

// Robots 和 Sitemap 更新 DTO
export class RobotsSitemapUpdateDto extends RobotsSitemapDto {}

// 社交媒体默认值更新 DTO
export class SocialMediaDefaultsUpdateDto extends SocialMediaDefaultsDto {}

// 安全和性能更新 DTO
export class SecurityPerformanceUpdateDto extends SecurityPerformanceDto {}

// 结构化数据更新 DTO
export class StructuredDataUpdateDto extends StructuredDataDto {}

// Hreflang 设置更新 DTO
export class HreflangSettingsUpdateDto {
  @IsOptional()
  @IsString()
  defaultLanguage?: string;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => LanguageLinkDto)
  supportedLanguages?: LanguageLinkDto[];
}

// Analytics 设置更新 DTO
export class AnalyticsSettingsUpdateDto {
  @IsOptional()
  @ValidateNested()
  @Type(() => AnalyticsSettingsDto)
  analyticsSettings?: AnalyticsSettingsDto;
}

// Brand Settings 更新 DTO
export class BrandSettingsUpdateDto {
  @IsOptional()
  @ValidateNested()
  @Type(() => BrandSettingsDto)
  brandSettings?: BrandSettingsDto;
}
