# SEO Module

This module provides API endpoints for managing SEO settings at both the website and page levels.

## Features

- Website-level SEO settings management
- Page-level SEO settings management
- Support for both full updates and partial (tab-based) updates
- Public endpoints for sitemap.xml and robots.txt generation
- Inheritance mechanism allowing pages to inherit SEO settings from the website

## API Endpoints

### Website SEO

- `GET /api/v1/seo/website/:websiteId` - Get website SEO settings
- `PUT /api/v1/seo/website/:websiteId` - Update website SEO settings (full update)
- `PATCH /api/v1/seo/website/:websiteId/metadata` - Update website metadata
- `PATCH /api/v1/seo/website/:websiteId/robots-sitemap` - Update robots and sitemap settings
- `PATCH /api/v1/seo/website/:websiteId/social-media` - Update social media defaults
- `PATCH /api/v1/seo/website/:websiteId/security-performance` - Update security and performance settings
- `PATCH /api/v1/seo/website/:websiteId/structured-data` - Update structured data

### Page SEO

- `GET /api/v1/seo/page/:pageId` - Get page SEO settings
- `PUT /api/v1/seo/page/:pageId` - Update page SEO settings (full update)
- `PATCH /api/v1/seo/page/:pageId/basic` - Update basic SEO settings
- `PATCH /api/v1/seo/page/:pageId/social-media` - Update social media settings
- `PATCH /api/v1/seo/page/:pageId/structured-data` - Update structured data settings
- `PATCH /api/v1/seo/page/:pageId/advanced` - Update advanced SEO settings
- `PATCH /api/v1/seo/page/:pageId/inherit` - Toggle page SEO inheritance setting

### Public Endpoints

- `GET /api/v1/seo/public/page` - Get public page SEO settings
- `GET /api/v1/seo/public/sitemap.xml` - Get sitemap.xml
- `GET /api/v1/seo/public/robots.txt` - Get robots.txt

## Data Models

The module uses the following Prisma models:

- `WebsiteSEO` - Website-level SEO settings
- `PageSEO` - Page-level SEO settings

## Usage

Import the `SeoModule` into your application's main module:

```typescript
import { SeoModule } from './seo/seo.module';

@Module({
  imports: [
    // ...other modules
    SeoModule,
  ],
})
export class AppModule {}
```
