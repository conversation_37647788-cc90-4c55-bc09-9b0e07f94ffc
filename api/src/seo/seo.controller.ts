import { Controller, Get, Post, Put, Patch, Delete, Body, Param, Query, UseGuards, Request, Header, HttpCode, NotFoundException, Logger } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { SeoService } from './seo.service';
import {
  WebsiteSeoUpdateDto,
  WebsiteMetadataUpdateDto,
  RobotsSitemapUpdateDto,
  SocialMediaDefaultsUpdateDto,
  SecurityPerformanceUpdateDto,
  StructuredDataUpdateDto,
  PageSeoUpdateDto,
  BasicSeoUpdateDto,
  SocialMediaUpdateDto,
  PageStructuredDataUpdateDto,
  AdvancedSeoUpdateDto,
  PageSeoInheritToggleDto,
  HreflangSettingsUpdateDto,
  AnalyticsSettingsUpdateDto,
  BrandSettingsUpdateDto,
} from './dto';

@Controller('api/v1/seo')
export class SeoController {
  private readonly logger: Logger;

  constructor(private readonly seoService: SeoService) {
    this.logger = new Logger(SeoController.name);
  }

  // ========== Website SEO Endpoints ==========

  /**
   * Get website SEO settings
   */
  @Get('website/:websiteId')
  @UseGuards(AuthGuard('jwt'))
  async getWebsiteSeo(@Param('websiteId') websiteId: string) {
    const seo = await this.seoService.getWebsiteSeo(websiteId);
    return { success: true, data: seo };
  }

  /**
   * Update website SEO settings (full update)
   */
  @Put('website/:websiteId')
  @UseGuards(AuthGuard('jwt'))
  async updateWebsiteSeo(
    @Param('websiteId') websiteId: string,
    @Body() dto: WebsiteSeoUpdateDto
  ) {
    const updatedSeo = await this.seoService.updateWebsiteSeo(websiteId, dto);
    return { success: true, data: updatedSeo };
  }

  /**
   * Update website metadata (partial update)
   */
  @Patch('website/:websiteId/metadata')
  @UseGuards(AuthGuard('jwt'))
  async updateWebsiteMetadata(
    @Param('websiteId') websiteId: string,
    @Body() dto: WebsiteMetadataUpdateDto
  ) {
    const updatedSeo = await this.seoService.updateWebsiteMetadata(websiteId, dto);
    return { success: true, data: updatedSeo };
  }

  /**
   * Update robots and sitemap settings (partial update)
   */
  @Patch('website/:websiteId/robots-sitemap')
  @UseGuards(AuthGuard('jwt'))
  async updateRobotsSitemap(
    @Param('websiteId') websiteId: string,
    @Body() dto: RobotsSitemapUpdateDto
  ) {
    const updatedSeo = await this.seoService.updateRobotsSitemap(websiteId, dto);
    return { success: true, data: updatedSeo };
  }

  /**
   * Update social media defaults (partial update)
   */
  @Patch('website/:websiteId/social-media')
  @UseGuards(AuthGuard('jwt'))
  async updateSocialMediaDefaults(
    @Param('websiteId') websiteId: string,
    @Body() dto: SocialMediaDefaultsUpdateDto
  ) {
    const updatedSeo = await this.seoService.updateSocialMediaDefaults(websiteId, dto);
    return { success: true, data: updatedSeo };
  }

  /**
   * Update security and performance settings (partial update)
   */
  @Patch('website/:websiteId/security-performance')
  @UseGuards(AuthGuard('jwt'))
  async updateSecurityPerformance(
    @Param('websiteId') websiteId: string,
    @Body() dto: SecurityPerformanceUpdateDto
  ) {
    const updatedSeo = await this.seoService.updateSecurityPerformance(websiteId, dto);
    return { success: true, data: updatedSeo };
  }

  /**
   * Update structured data (partial update)
   */
  @Patch('website/:websiteId/structured-data')
  @UseGuards(AuthGuard('jwt'))
  async updateStructuredData(
    @Param('websiteId') websiteId: string,
    @Body() dto: StructuredDataUpdateDto
  ) {
    const updatedSeo = await this.seoService.updateStructuredData(websiteId, dto);
    return { success: true, data: updatedSeo };
  }

  /**
   * Update hreflang settings (partial update)
   */
  @Patch('website/:websiteId/hreflang')
  @UseGuards(AuthGuard('jwt'))
  async updateHreflangSettings(
    @Param('websiteId') websiteId: string,
    @Body() dto: any
  ) {
    // 处理嵌套的 hreflangSettings 结构
    let hreflangData: HreflangSettingsUpdateDto;
    
    if (dto.hreflangSettings) {
      // 前端发送的是嵌套结构 { hreflangSettings: { ... } }
      this.logger.debug('Received nested hreflang structure, extracting...', { dto });
      hreflangData = dto.hreflangSettings;
    } else {
      // 前端直接发送的是 { defaultLanguage, supportedLanguages }
      this.logger.debug('Received flat hreflang structure', { dto });
      hreflangData = dto;
    }
    
    const updatedSeo = await this.seoService.updateHreflangSettings(websiteId, hreflangData);
    return { success: true, data: updatedSeo };
  }

  /**
   * Update analytics settings (partial update)
   */
  @Patch('website/:websiteId/analytics')
  @UseGuards(AuthGuard('jwt'))
  async updateAnalyticsSettings(
    @Param('websiteId') websiteId: string,
    @Body() dto: AnalyticsSettingsUpdateDto
  ) {
    this.logger.log('Updating analytics settings', { websiteId, dto });
    
    const updatedSeo = await this.seoService.updateAnalyticsSettings(websiteId, dto.analyticsSettings);
    return { success: true, data: updatedSeo };
  }

  /**
   * Update brand settings (partial update)
   */
  @Patch('website/:websiteId/brand')
  @UseGuards(AuthGuard('jwt'))
  async updateBrandSettings(
    @Param('websiteId') websiteId: string,
    @Body() dto: BrandSettingsUpdateDto
  ) {
    this.logger.log('Updating brand settings', { websiteId, dto });
    
    const updatedSeo = await this.seoService.updateBrandSettings(websiteId, dto.brandSettings);
    return { success: true, data: updatedSeo };
  }

  // ========== Page SEO Endpoints ==========

  /**
   * Get page SEO settings
   */
  @Get('page/:pageId')
  @UseGuards(AuthGuard('jwt'))
  async getPageSeo(@Param('pageId') pageId: string) {
    const seo = await this.seoService.getPageSeo(pageId);
    return { success: true, data: seo };
  }

  /**
   * Update page SEO settings (full update)
   */
  @Put('page/:pageId')
  @UseGuards(AuthGuard('jwt'))
  async updatePageSeo(
    @Param('pageId') pageId: string,
    @Body() dto: PageSeoUpdateDto
  ) {
    const updatedSeo = await this.seoService.updatePageSeo(pageId, dto);
    return { success: true, data: updatedSeo };
  }

  /**
   * Update basic SEO settings (partial update)
   */
  @Patch('page/:pageId/basic')
  @UseGuards(AuthGuard('jwt'))
  async updateBasicSeo(
    @Param('pageId') pageId: string,
    @Body() dto: BasicSeoUpdateDto
  ) {
    const updatedSeo = await this.seoService.updateBasicSeo(pageId, dto);
    return { success: true, data: updatedSeo };
  }

  /**
   * Update social media settings (partial update)
   */
  @Patch('page/:pageId/social-media')
  @UseGuards(AuthGuard('jwt'))
  async updateSocialMedia(
    @Param('pageId') pageId: string,
    @Body() dto: SocialMediaUpdateDto
  ) {
    const updatedSeo = await this.seoService.updateSocialMedia(pageId, dto);
    return { success: true, data: updatedSeo };
  }

  /**
   * Update structured data settings (partial update)
   */
  @Patch('page/:pageId/structured-data')
  @UseGuards(AuthGuard('jwt'))
  async updatePageStructuredData(
    @Param('pageId') pageId: string,
    @Body() dto: PageStructuredDataUpdateDto
  ) {
    const updatedSeo = await this.seoService.updatePageStructuredData(pageId, dto);
    return { success: true, data: updatedSeo };
  }

  /**
   * Update advanced SEO settings (partial update)
   */
  @Patch('page/:pageId/advanced')
  @UseGuards(AuthGuard('jwt'))
  async updateAdvancedSeo(
    @Param('pageId') pageId: string,
    @Body() dto: AdvancedSeoUpdateDto
  ) {
    const updatedSeo = await this.seoService.updateAdvancedSeo(pageId, dto);
    return { success: true, data: updatedSeo };
  }

  /**
   * Toggle page SEO inheritance setting
   */
  @Patch('page/:pageId/inherit')
  @UseGuards(AuthGuard('jwt'))
  async togglePageSeoInherit(
    @Param('pageId') pageId: string,
    @Body() dto: PageSeoInheritToggleDto
  ) {
    const updatedSeo = await this.seoService.togglePageSeoInherit(pageId, dto);
    return { success: true, data: updatedSeo };
  }

  // ========== Public SEO Endpoints ==========

  /**
   * Get public page SEO settings
   */
  @Get('public/page')
  async getPublicPageSeo(
    @Query('domain') domain: string,
    @Query('slug') slug: string,
    @Query('language') language?: string
  ) {
    if (!domain || !slug) {
      throw new NotFoundException('Domain and slug are required');
    }
    
    const seo = await this.seoService.getPublicPageSeo(domain, slug, language);
    return { success: true, data: seo };
  }

  /**
   * Get sitemap.xml
   */
  @Get('public/sitemap.xml')
  @Header('Content-Type', 'application/xml')
  async getSitemap(@Query('domain') domain: string) {
    if (!domain) {
      throw new NotFoundException('Domain is required');
    }
    
    return await this.seoService.generateSitemap(domain);
  }

  /**
   * Get robots.txt
   */
  @Get('public/robots.txt')
  @Header('Content-Type', 'text/plain')
  async getRobotsTxt(@Query('domain') domain: string) {
    if (!domain) {
      throw new NotFoundException('Domain is required');
    }
    
    return await this.seoService.generateRobotsTxt(domain);
  }

  /**
   * Get security.txt
   */
  @Get('public/security.txt')
  @Header('Content-Type', 'text/plain')
  async getSecurityTxt(@Query('domain') domain: string) {
    if (!domain) {
      throw new NotFoundException('Domain is required');
    }
    
    return await this.seoService.generateSecurityTxt(domain);
  }
}
