import { Injectable, Logger, NotFoundException, BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import {
  WebsiteSeoUpdateDto,
  WebsiteMetadataUpdateDto,
  RobotsSitemapUpdateDto,
  SocialMediaDefaultsUpdateDto,
  SecurityPerformanceUpdateDto,
  StructuredDataUpdateDto,
  PageSeoUpdateDto,
  BasicSeoUpdateDto,
  SocialMediaUpdateDto,
  PageStructuredDataUpdateDto,
  AdvancedSeoUpdateDto,
  PageSeoInheritToggleDto,
  HreflangSettingsUpdateDto,
} from './dto';
import { Prisma } from '@prisma/client';

@Injectable()
export class SeoService {
  private readonly logger = new Logger(SeoService.name);

  constructor(private readonly prisma: PrismaService) {}

  // ========== Website SEO Methods ==========

  /**
   * Get website SEO settings
   * @param websiteId Website ID
   * @returns Website SEO settings
   */
  async getWebsiteSeo(websiteId: string) {
    try {
      const websiteSeo = await this.prisma.websiteSEO.findUnique({
        where: { websiteId },
      });

      if (!websiteSeo) {
        return null; // Return null instead of throwing an error for optional SEO settings
      }

      return websiteSeo;
    } catch (error) {
      this.logger.error(`Error getting website SEO: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to get website SEO settings');
    }
  }

  /**
   * Update website SEO settings (full update)
   * @param websiteId Website ID
   * @param dto Website SEO update data
   * @returns Updated website SEO settings
   */
  async updateWebsiteSeo(websiteId: string, dto: WebsiteSeoUpdateDto) {
    try {
      // Prepare data for update
      const data: Prisma.WebsiteSEOUpdateInput = {};

      // Map nested objects to flat structure
      if (dto.metadata) {
        Object.assign(data, {
          siteName: dto.metadata.siteName,
          siteDescription: dto.metadata.siteDescription,
          titleTemplate: dto.metadata.titleTemplate,
          defaultDescription: dto.metadata.defaultDescription,
          globalKeywords: dto.metadata.globalKeywords,
        });
      }

      if (dto.robotsSitemap) {
        Object.assign(data, {
          robotsTxt: dto.robotsSitemap.robotsTxt,
          sitemapSettings: dto.robotsSitemap.sitemapSettings,
        });
      }

      if (dto.socialDefaults) {
        Object.assign(data, {
          defaultOgImage: dto.socialDefaults.defaultOgImage,
          ogTitleTemplate: dto.socialDefaults.ogTitleTemplate,
          ogDescriptionTemplate: dto.socialDefaults.ogDescriptionTemplate,
          defaultXCard: dto.socialDefaults.defaultXCard,
          xUsername: dto.socialDefaults.xUsername,
        });
      }

      if (dto.securityPerformance) {
        Object.assign(data, {
          forceHttps: dto.securityPerformance.forceHttps,
          httpToHttpsRedirect: dto.securityPerformance.httpToHttpsRedirect,
          cacheControl: dto.securityPerformance.cacheControl,
          resourceCompression: dto.securityPerformance.resourceCompression,
          browserCaching: dto.securityPerformance.browserCaching,
          lazyLoadingImages: dto.securityPerformance.lazyLoadingImages,
        });
      }

      if (dto.structuredData) {
        Object.assign(data, {
          organizationSchema: dto.structuredData.organizationSchema,
          websiteSchema: dto.structuredData.websiteSchema,
        });
      }

      if (dto.hreflangSettings) {
        Object.assign(data, {
          hreflangSettings: dto.hreflangSettings,
        });
      }

      // Add other top-level properties
      if (dto.analyticsSettings) data.analyticsSettings = dto.analyticsSettings as unknown as Prisma.InputJsonValue;
      if (dto.brandSettings) data.brandSettings = dto.brandSettings as unknown as Prisma.InputJsonValue;
      if (dto.seoScores) data.seoScores = dto.seoScores;

      // Update or create the record
      const updateData = data as Prisma.WebsiteSEOUpdateInput;
      
      // 为创建操作准备数据，移除更新操作特有的字段
      const createInput: Record<string, any> = {};
      Object.keys(data).forEach(key => {
        // 跳过 Prisma 更新操作特有的字段类型
        if (typeof data[key] === 'object' && data[key] !== null && 
            ('set' in data[key] || 'increment' in data[key] || 'decrement' in data[key])) {
          // 如果是更新操作字段，获取其值
          if ('set' in data[key]) {
            createInput[key] = data[key].set;
          }
        } else {
          // 直接复制其他字段
          createInput[key] = data[key];
        }
      });
      
      const createData: Prisma.WebsiteSEOUncheckedCreateInput = {
        websiteId,
        ...createInput
      };
      
      return await this.prisma.websiteSEO.upsert({
        where: { websiteId },
        update: updateData,
        create: createData,
      });
    } catch (error) {
      this.logger.error(`Error updating website SEO: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to update website SEO settings');
    }
  }

  /**
   * Update website metadata (partial update)
   * @param websiteId Website ID
   * @param dto Metadata update data
   * @returns Updated website SEO settings
   */
  async updateWebsiteMetadata(websiteId: string, dto: WebsiteMetadataUpdateDto) {
    try {
      const updateData = {
        siteName: dto.siteName,
        siteDescription: dto.siteDescription,
        titleTemplate: dto.titleTemplate,
        defaultDescription: dto.defaultDescription,
        globalKeywords: dto.globalKeywords,
      } as Prisma.WebsiteSEOUpdateInput;
      
      const createData: Prisma.WebsiteSEOUncheckedCreateInput = {
        websiteId,
        siteName: dto.siteName,
        siteDescription: dto.siteDescription,
        titleTemplate: dto.titleTemplate,
        defaultDescription: dto.defaultDescription,
        globalKeywords: dto.globalKeywords,
      };
      
      return await this.prisma.websiteSEO.upsert({
        where: { websiteId },
        update: updateData,
        create: createData,
      });
    } catch (error) {
      this.logger.error(`Error updating website metadata: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to update website metadata');
    }
  }

  /**
   * Update robots and sitemap settings (partial update)
   * @param websiteId Website ID
   * @param dto Robots and sitemap update data
   * @returns Updated website SEO settings
   */
  async updateRobotsSitemap(websiteId: string, dto: RobotsSitemapUpdateDto) {
    try {
      const updateData = {
        robotsTxt: dto.robotsTxt,
        sitemapSettings: dto.sitemapSettings,
      } as Prisma.WebsiteSEOUpdateInput;
      
      const createData: Prisma.WebsiteSEOUncheckedCreateInput = {
        websiteId,
        robotsTxt: dto.robotsTxt,
        sitemapSettings: dto.sitemapSettings,
      };
      
      return await this.prisma.websiteSEO.upsert({
        where: { websiteId },
        update: updateData,
        create: createData,
      });
    } catch (error) {
      this.logger.error(`Error updating robots and sitemap: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to update robots and sitemap settings');
    }
  }

  /**
   * Update social media defaults (partial update)
   * @param websiteId Website ID
   * @param dto Social media defaults update data
   * @returns Updated website SEO settings
   */
  async updateSocialMediaDefaults(websiteId: string, dto: SocialMediaDefaultsUpdateDto) {
    try {
      const updateData = {
        defaultOgImage: dto.defaultOgImage,
        ogTitleTemplate: dto.ogTitleTemplate,
        ogDescriptionTemplate: dto.ogDescriptionTemplate,
        defaultXCard: dto.defaultXCard,
        xUsername: dto.xUsername,
      } as Prisma.WebsiteSEOUpdateInput;
      
      const createData: Prisma.WebsiteSEOUncheckedCreateInput = {
        websiteId,
        defaultOgImage: dto.defaultOgImage,
        ogTitleTemplate: dto.ogTitleTemplate,
        ogDescriptionTemplate: dto.ogDescriptionTemplate,
        defaultXCard: dto.defaultXCard,
        xUsername: dto.xUsername,
      };
      
      return await this.prisma.websiteSEO.upsert({
        where: { websiteId },
        update: updateData,
        create: createData,
      });
    } catch (error) {
      this.logger.error(`Error updating social media defaults: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to update social media defaults');
    }
  }

  /**
   * Update security and performance settings (partial update)
   * @param websiteId Website ID
   * @param dto Security and performance update data
   * @returns Updated website SEO settings
   */
  async updateSecurityPerformance(websiteId: string, dto: SecurityPerformanceUpdateDto) {
    try {
      const updateData = {
        forceHttps: dto.forceHttps,
        httpToHttpsRedirect: dto.httpToHttpsRedirect,
        cacheControl: dto.cacheControl,
        resourceCompression: dto.resourceCompression,
        browserCaching: dto.browserCaching,
        lazyLoadingImages: dto.lazyLoadingImages,
      } as Prisma.WebsiteSEOUpdateInput;
      
      const createData: Prisma.WebsiteSEOUncheckedCreateInput = {
        websiteId,
        forceHttps: dto.forceHttps,
        httpToHttpsRedirect: dto.httpToHttpsRedirect,
        cacheControl: dto.cacheControl,
        resourceCompression: dto.resourceCompression,
        browserCaching: dto.browserCaching,
        lazyLoadingImages: dto.lazyLoadingImages,
      };
      
      return await this.prisma.websiteSEO.upsert({
        where: { websiteId },
        update: updateData,
        create: createData,
      });
    } catch (error) {
      this.logger.error(`Error updating security and performance: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to update security and performance settings');
    }
  }

  /**
   * Update structured data (partial update)
   * @param websiteId Website ID
   * @param dto Structured data update data
   * @returns Updated website SEO settings
   */
  async updateStructuredData(websiteId: string, dto: StructuredDataUpdateDto) {
    try {
      const updateData = {
        organizationSchema: dto.organizationSchema,
        websiteSchema: dto.websiteSchema,
      } as Prisma.WebsiteSEOUpdateInput;
      
      const createData: Prisma.WebsiteSEOUncheckedCreateInput = {
        websiteId,
        organizationSchema: dto.organizationSchema,
        websiteSchema: dto.websiteSchema,
      };
      
      return await this.prisma.websiteSEO.upsert({
        where: { websiteId },
        update: updateData,
        create: createData,
      });
    } catch (error) {
      this.logger.error(`Error updating structured data: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to update structured data');
    }
  }

  /**
   * Update hreflang settings (partial update)
   * @param websiteId Website ID
   * @param dto Hreflang settings update data
   * @returns Updated website SEO settings
   */
  async updateHreflangSettings(websiteId: string, dto: HreflangSettingsUpdateDto) {
    try {
      // 检查网站是否存在
      const website = await this.prisma.website.findUnique({
        where: { id: websiteId },
      });

      if (!website) {
        throw new NotFoundException(`Website with ID ${websiteId} not found`);
      }

      // 记录原始数据，用于调试
      this.logger.debug(`Updating hreflang settings for website ${websiteId}`, { dto });

      // 确保 dto 是一个有效的对象
      const hreflangData = {
        defaultLanguage: dto.defaultLanguage || 'en',
        supportedLanguages: Array.isArray(dto.supportedLanguages) 
          ? dto.supportedLanguages.map(lang => ({
              code: lang.code,
              region: lang.region,
              url: lang.url
            }))
          : []
      };

      // 直接保存 hreflangData
      const updateData = {
        hreflangSettings: hreflangData as unknown as Prisma.InputJsonValue,
      } as Prisma.WebsiteSEOUpdateInput;
      
      const createData: Prisma.WebsiteSEOUncheckedCreateInput = {
        websiteId,
        hreflangSettings: hreflangData as unknown as Prisma.InputJsonValue,
      };
      
      // 检查是否存在嵌套的 hreflangSettings 结构，如果有则修复
      const existingRecord = await this.prisma.websiteSEO.findUnique({
        where: { websiteId },
        select: { hreflangSettings: true }
      });
      
      if (existingRecord && existingRecord.hreflangSettings) {
        const currentSettings = existingRecord.hreflangSettings as any;
        // 检查是否有嵌套的 hreflangSettings
        if (currentSettings.hreflangSettings) {
          this.logger.debug('检测到嵌套的 hreflangSettings 结构，正在修复...');
          // 执行一次额外的更新来修复嵌套结构
          await this.prisma.websiteSEO.update({
            where: { websiteId },
            data: {
              hreflangSettings: currentSettings.hreflangSettings as unknown as Prisma.InputJsonValue
            }
          });
        }
      }
      
      const result = await this.prisma.websiteSEO.upsert({
        where: { websiteId },
        update: updateData,
        create: createData,
      });

      // 记录更新后的结果，用于调试
      this.logger.debug(`Updated hreflang settings for website ${websiteId}`, { 
        result: result.hreflangSettings 
      });

      return result;
    } catch (error) {
      this.logger.error(`Error updating hreflang settings: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to update hreflang settings');
    }
  }

  /**
   * Update analytics settings
   * @param websiteId Website ID
   * @param analyticsSettings Analytics settings
   * @returns Updated website SEO settings
   */
  async updateAnalyticsSettings(websiteId: string, analyticsSettings: any) {
    try {
      // 准备更新数据
      const updateData = {
        analyticsSettings: analyticsSettings ? (analyticsSettings as unknown as Prisma.InputJsonValue) : null,
        updatedAt: new Date(),
      } as Prisma.WebsiteSEOUpdateInput;
      
      // 准备创建数据
      const createData: Prisma.WebsiteSEOUncheckedCreateInput = {
        websiteId,
        analyticsSettings: analyticsSettings ? (analyticsSettings as unknown as Prisma.InputJsonValue) : null,
      };
      
      // 使用 upsert 查找或创建记录
      const updatedSeo = await this.prisma.websiteSEO.upsert({
        where: { websiteId },
        update: updateData,
        create: createData,
      });
      
      return updatedSeo;
    } catch (error) {
      this.logger.error('Error updating analytics settings', { websiteId, error });
      throw new Error(`Failed to update analytics settings: ${error.message}`);
    }
  }

  /**
   * Update brand settings
   * @param websiteId Website ID
   * @param brandSettings Brand settings
   * @returns Updated website SEO settings
   */
  async updateBrandSettings(websiteId: string, brandSettings: any) {
    try {
      // 准备更新数据
      const updateData = {
        brandSettings: brandSettings ? (brandSettings as unknown as Prisma.InputJsonValue) : null,
        updatedAt: new Date(),
      } as Prisma.WebsiteSEOUpdateInput;
      
      // 准备创建数据
      const createData: Prisma.WebsiteSEOUncheckedCreateInput = {
        websiteId,
        brandSettings: brandSettings ? (brandSettings as unknown as Prisma.InputJsonValue) : null,
      };
      
      // 使用 upsert 查找或创建记录
      const updatedSeo = await this.prisma.websiteSEO.upsert({
        where: { websiteId },
        update: updateData,
        create: createData,
      });
      
      return updatedSeo;
    } catch (error) {
      this.logger.error('Error updating brand settings', { websiteId, error });
      throw new Error(`Failed to update brand settings: ${error.message}`);
    }
  }

  // ========== Page SEO Methods ==========

  /**
   * Get page SEO settings
   * @param pageId Page ID
   * @returns Page SEO settings
   */
  async getPageSeo(pageId: string) {
    try {
      const pageSeo = await this.prisma.pageSEO.findUnique({
        where: { pageId },
        include: {
          websiteSeo: true,
        },
      });

      if (!pageSeo) {
        return null; // Return null instead of throwing an error for optional SEO settings
      }

      return pageSeo;
    } catch (error) {
      this.logger.error(`Error getting page SEO: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to get page SEO settings');
    }
  }

  /**
   * Update page SEO settings (full update)
   * @param pageId Page ID
   * @param dto Page SEO update data
   * @returns Updated page SEO settings
   */
  async updatePageSeo(pageId: string, dto: PageSeoUpdateDto) {
    try {
      // Prepare data for update
      const data: Prisma.PageSEOUpdateInput = {};

      // Map nested objects to flat structure
      if (dto.basic) {
        Object.assign(data, {
          title: dto.basic.title,
          useCustomTitle: dto.basic.useCustomTitle,
          description: dto.basic.description,
          keywords: dto.basic.keywords,
        });
      }

      if (dto.socialMedia) {
        Object.assign(data, {
          ogTitle: dto.socialMedia.ogTitle,
          ogDescription: dto.socialMedia.ogDescription,
          ogImage: dto.socialMedia.ogImage,
          xTitle: dto.socialMedia.xTitle,
          xDescription: dto.socialMedia.xDescription,
          xImage: dto.socialMedia.xImage,
          xCardType: dto.socialMedia.xCardType,
        });
      }

      if (dto.structuredData) {
        Object.assign(data, {
          schemaType: dto.structuredData.schemaType,
          schemaData: dto.structuredData.schemaData,
        });
      }

      if (dto.advanced) {
        Object.assign(data, {
          canonicalUrl: dto.advanced.canonicalUrl,
          robots: dto.advanced.robots,
          hreflangLinks: dto.advanced.hreflangLinks,
          priority: dto.advanced.priority,
          changeFrequency: dto.advanced.changeFrequency,
        });
      }

      if (dto.inherit) {
        Object.assign(data, {
          inheritFromSite: dto.inherit.inheritFromSite,
          websiteSeoId: dto.inherit.websiteSeoId,
        });
      }

      // Add other top-level properties
      if (dto.contentAnalysis) data.contentAnalysis = dto.contentAnalysis;

      // Check if page exists
      const page = await this.prisma.page.findUnique({
        where: { id: pageId },
        select: { id: true },
      });

      if (!page) {
        throw new NotFoundException(`Page with ID ${pageId} not found`);
      }

      // Update or create the record
      const updateData = data as Prisma.PageSEOUpdateInput;
      
      // 为创建操作准备数据，移除更新操作特有的字段
      const createInput: Record<string, any> = {};
      Object.keys(data).forEach(key => {
        // 跳过 Prisma 更新操作特有的字段类型
        if (typeof data[key] === 'object' && data[key] !== null && 
            ('set' in data[key] || 'increment' in data[key] || 'decrement' in data[key])) {
          // 如果是更新操作字段，获取其值
          if ('set' in data[key]) {
            createInput[key] = data[key].set;
          }
        } else {
          // 直接复制其他字段
          createInput[key] = data[key];
        }
      });
      
      const createData: Prisma.PageSEOUncheckedCreateInput = {
        pageId,
        ...createInput
      };
      
      return await this.prisma.pageSEO.upsert({
        where: { pageId },
        update: updateData,
        create: createData,
      });
    } catch (error) {
      this.logger.error(`Error updating page SEO: ${error.message}`, error.stack);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to update page SEO settings');
    }
  }

  /**
   * Update basic SEO settings (partial update)
   * @param pageId Page ID
   * @param dto Basic SEO update data
   * @returns Updated page SEO settings
   */
  async updateBasicSeo(pageId: string, dto: BasicSeoUpdateDto) {
    try {
      // Check if page exists
      const page = await this.prisma.page.findUnique({
        where: { id: pageId },
        select: { id: true },
      });

      if (!page) {
        throw new NotFoundException(`Page with ID ${pageId} not found`);
      }

      const updateData = {
        title: dto.title,
        useCustomTitle: dto.useCustomTitle,
        description: dto.description,
        keywords: dto.keywords,
      } as Prisma.PageSEOUpdateInput;
      
      const createData: Prisma.PageSEOUncheckedCreateInput = {
        pageId,
        title: dto.title,
        useCustomTitle: dto.useCustomTitle,
        description: dto.description,
        keywords: dto.keywords,
      };
      
      return await this.prisma.pageSEO.upsert({
        where: { pageId },
        update: updateData,
        create: createData,
      });
    } catch (error) {
      this.logger.error(`Error updating basic SEO: ${error.message}`, error.stack);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to update basic SEO settings');
    }
  }

  /**
   * Update social media settings (partial update)
   * @param pageId Page ID
   * @param dto Social media update data
   * @returns Updated page SEO settings
   */
  async updateSocialMedia(pageId: string, dto: SocialMediaUpdateDto) {
    try {
      // Check if page exists
      const page = await this.prisma.page.findUnique({
        where: { id: pageId },
        select: { id: true },
      });

      if (!page) {
        throw new NotFoundException(`Page with ID ${pageId} not found`);
      }

      const updateData = {
        ogTitle: dto.ogTitle,
        ogDescription: dto.ogDescription,
        ogImage: dto.ogImage,
        xTitle: dto.xTitle,
        xDescription: dto.xDescription,
        xImage: dto.xImage,
        xCardType: dto.xCardType,
        xCardData: dto.xCardData, // 添加 xCardData 字段
      } as Prisma.PageSEOUpdateInput;
      
      const createData: Prisma.PageSEOUncheckedCreateInput = {
        pageId,
        ogTitle: dto.ogTitle,
        ogDescription: dto.ogDescription,
        ogImage: dto.ogImage,
        xTitle: dto.xTitle,
        xDescription: dto.xDescription,
        xImage: dto.xImage,
        xCardType: dto.xCardType,
        xCardData: dto.xCardData, // 添加 xCardData 字段
      };
      
      return await this.prisma.pageSEO.upsert({
        where: { pageId },
        update: updateData,
        create: createData,
      });
    } catch (error) {
      this.logger.error(`Error updating social media: ${error.message}`, error.stack);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to update social media settings');
    }
  }

  /**
   * Update structured data settings (partial update)
   * @param pageId Page ID
   * @param dto Structured data update data
   * @returns Updated page SEO settings
   */
  async updatePageStructuredData(pageId: string, dto: PageStructuredDataUpdateDto) {
    try {
      // Check if page exists
      const page = await this.prisma.page.findUnique({
        where: { id: pageId },
        select: { id: true },
      });

      if (!page) {
        throw new NotFoundException(`Page with ID ${pageId} not found`);
      }

      const updateData = {
        schemaType: dto.schemaType,
        schemaData: dto.schemaData,
      } as Prisma.PageSEOUpdateInput;
      
      const createData: Prisma.PageSEOUncheckedCreateInput = {
        pageId,
        schemaType: dto.schemaType,
        schemaData: dto.schemaData,
      };
      
      return await this.prisma.pageSEO.upsert({
        where: { pageId },
        update: updateData,
        create: createData,
      });
    } catch (error) {
      this.logger.error(`Error updating structured data: ${error.message}`, error.stack);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to update structured data settings');
    }
  }

  /**
   * Update advanced SEO settings (partial update)
   * @param pageId Page ID
   * @param dto Advanced SEO update data
   * @returns Updated page SEO settings
   */
  async updateAdvancedSeo(pageId: string, dto: AdvancedSeoUpdateDto) {
    try {
      // Check if page exists
      const page = await this.prisma.page.findUnique({
        where: { id: pageId },
        select: { id: true },
      });

      if (!page) {
        throw new NotFoundException(`Page with ID ${pageId} not found`);
      }

      const updateData = {
        canonicalUrl: dto.canonicalUrl,
        robots: dto.robots,
        hreflangLinks: dto.hreflangLinks,
        priority: dto.priority,
        changeFrequency: dto.changeFrequency,
      } as Prisma.PageSEOUpdateInput;
      
      const createData: Prisma.PageSEOUncheckedCreateInput = {
        pageId,
        canonicalUrl: dto.canonicalUrl,
        robots: dto.robots,
        hreflangLinks: dto.hreflangLinks,
        priority: dto.priority,
        changeFrequency: dto.changeFrequency,
      };
      
      return await this.prisma.pageSEO.upsert({
        where: { pageId },
        update: updateData,
        create: createData,
      });
    } catch (error) {
      this.logger.error(`Error updating advanced SEO: ${error.message}`, error.stack);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to update advanced SEO settings');
    }
  }

  /**
   * Toggle page SEO inheritance setting
   * @param pageId Page ID
   * @param dto Inheritance toggle data
   * @returns Updated page SEO settings
   */
  async togglePageSeoInherit(pageId: string, dto: PageSeoInheritToggleDto) {
    try {
      // Check if page exists
      const page = await this.prisma.page.findUnique({
        where: { id: pageId },
        select: { id: true, websiteId: true },
      });

      if (!page) {
        throw new NotFoundException(`Page with ID ${pageId} not found`);
      }

      // If enabling inheritance, we need to get the website SEO ID
      let websiteSeoId = null;
      if (dto.inheritFromSite) {
        const websiteSeo = await this.prisma.websiteSEO.findUnique({
          where: { websiteId: page.websiteId },
          select: { id: true },
        });

        if (!websiteSeo) {
          // Create a default website SEO record if it doesn't exist
          const newWebsiteSeo = await this.prisma.websiteSEO.create({
            data: {
              websiteId: page.websiteId,
            },
          });
          websiteSeoId = newWebsiteSeo.id;
        } else {
          websiteSeoId = websiteSeo.id;
        }
      }

      const updateData = {
        inheritFromSite: dto.inheritFromSite,
        websiteSeoId: dto.inheritFromSite ? websiteSeoId : null,
      } as Prisma.PageSEOUpdateInput;
      
      const createData: Prisma.PageSEOUncheckedCreateInput = {
        pageId,
        inheritFromSite: dto.inheritFromSite,
        websiteSeoId: dto.inheritFromSite ? websiteSeoId : null,
      };
      
      return await this.prisma.pageSEO.upsert({
        where: { pageId },
        update: updateData,
        create: createData,
      });
    } catch (error) {
      this.logger.error(`Error toggling page SEO inheritance: ${error.message}`, error.stack);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to toggle page SEO inheritance');
    }
  }

  /**
   * Get public page SEO settings
   * @param domain Website domain
   * @param slug Page slug
   * @param language Page language
   * @returns Page SEO settings for public consumption
   */
  async getPublicPageSeo(domain: string, slug: string, language?: string) {
    try {
      // Find the website by domain
      const website = await this.prisma.website.findFirst({
        where: { domain },
        select: { id: true },
      });

      if (!website) {
        throw new NotFoundException(`Website with domain ${domain} not found`);
      }

      // Find the page by website ID, slug, and optionally language
      const whereClause: any = {
        websiteId: website.id,
        slug,
      };

      if (language) {
        whereClause.language = language;
      }

      const page = await this.prisma.page.findFirst({
        where: whereClause,
        select: {
          id: true,
          title: true,
          description: true,
          seo: {
            include: {
              websiteSeo: true,
            },
          },
        },
      });

      if (!page) {
        throw new NotFoundException(`Page with slug ${slug} not found`);
      }

      // If page has no SEO settings or inherits from website
      if (!page.seo || page.seo.inheritFromSite) {
        // Get website SEO settings
        const websiteSeo = await this.prisma.websiteSEO.findUnique({
          where: { websiteId: website.id },
        });

        // Merge page data with website SEO settings
        return {
          title: page.title,
          description: page.description,
          ...websiteSeo,
        };
      }

      // Return page SEO settings
      return page.seo;
    } catch (error) {
      this.logger.error(`Error getting public page SEO: ${error.message}`, error.stack);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to get public page SEO settings');
    }
  }

  /**
   * 处理域名，包括 IP 地址检测、默认域名处理和子域名提取
   * @param domain 原始域名
   * @param logPrefix 日志前缀
   * @returns 处理后的域名和网站信息
   */
  private async resolveDomain(domain: string, logPrefix: string): Promise<{ domain: string, website: any }> {
    console.log(`[${logPrefix}] Input - domain: ${domain}`);

    // 检查是否为 IP 地址访问
    const isIpAddress = /^(\d{1,3}\.){3}\d{1,3}(:\d+)?$/.test(domain);
    if (isIpAddress) {
      console.log(`[${logPrefix}] IP address access detected: ${domain}`);
      
      // 使用默认网站配置
      try {
        const defaultWebsite = await this.prisma.website.findFirst({
          where: {
            domain: process.env.DEFAULT_WEBSITE_DOMAIN || 'litpage'
          },
          include: {
            seo: true
          }
        });

        if (!defaultWebsite) {
          console.log(`[${logPrefix}] Default website not configured`);
          throw new NotFoundException('Website not found');
        }

        console.log(`[${logPrefix}] Using default website for IP access: ${defaultWebsite.domain}`);
        domain = defaultWebsite.domain;
      } catch (error) {
        console.log(`[${logPrefix}] Error handling IP address access: ${error.message}`);
        throw new NotFoundException('Access denied');
      }
    }

    const sites = {
      'lit.page': 'litpage',
      'www.imgpipe.dev': 'imgpipe',
    };

    let subdomain = sites[domain] ?? domain.split('.').slice(0, -2).join('.');
    console.log(`[${logPrefix}] Resolved subdomain: ${subdomain}`);

    // 查找网站，同时获取 SEO 设置
    const website = await this.prisma.website.findFirst({
      where: {
        domain: subdomain,
        // TODO: 支持自定义域名
        // OR: [
        //   { domain: subdomain },
        //   { customDomains: { some: { domain: subdomain } } }
        // ]
      },
      include: {
        seo: true
      }
    });

    if (!website) {
      throw new NotFoundException(`Website with domain ${domain} not found`);
    }

    return { domain, website };
  }

  /**
   * Generate sitemap.xml content
   * @param domain Website domain
   * @returns Sitemap XML content
   */
  async generateSitemap(domain: string) {
    try {
      // 处理域名
      const { domain: resolvedDomain, website } = await this.resolveDomain(domain, 'generateSitemap');

      // 获取所有已发布页面
      const pages = await this.prisma.page.findMany({
        where: {
          websiteId: website.id,
          status: 'PUBLISHED',
        },
        select: {
          slug: true,
          updatedAt: true,
          language: true,
          seo: {
            select: {
              priority: true,
              changeFrequency: true,
              hreflangLinks: true,
            },
          },
        },
      });

      // 生成 sitemap XML
      let xml = '<?xml version="1.0" encoding="UTF-8"?>\n';
      xml += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xhtml="http://www.w3.org/1999/xhtml">\n';

      // 添加页面到 sitemap
      for (const page of pages) {
        const priority = page.seo?.priority || 0.5;
        const changeFrequency = page.seo?.changeFrequency?.toLowerCase() || 'monthly';
        const lastmod = page.updatedAt.toISOString().split('T')[0];
        const loc = `https://${resolvedDomain}${page.slug}`;

        xml += '  <url>\n';
        xml += `    <loc>${loc}</loc>\n`;
        xml += `    <lastmod>${lastmod}</lastmod>\n`;
        xml += `    <changefreq>${changeFrequency}</changefreq>\n`;
        xml += `    <priority>${priority}</priority>\n`;

        // 添加 hreflang 链接
        if (page.seo?.hreflangLinks) {
          const hreflangLinks = page.seo.hreflangLinks as any[];
          for (const link of hreflangLinks) {
            xml += `    <xhtml:link rel="alternate" hreflang="${link.language}" href="${link.url}" />\n`;
          }
        }

        xml += '  </url>\n';
      }

      xml += '</urlset>';
      return xml;
    } catch (error) {
      this.logger.error(`Error generating sitemap: ${error.message}`, error.stack);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to generate sitemap');
    }
  }

  /**
   * Generate robots.txt content
   * @param domain Website domain
   * @returns Robots.txt content
   */
  async generateRobotsTxt(domain: string) {
    try {
      // 处理域名
      const { domain: resolvedDomain, website } = await this.resolveDomain(domain, 'generateRobotsTxt');

      // 如果网站有自定义 robots.txt 内容，返回它
      if (website.seo?.robotsTxt) {
        return website.seo.robotsTxt;
      }

      // 生成默认 robots.txt
      let robotsTxt = 'User-agent: *\n';
      robotsTxt += 'Allow: /\n\n';
      robotsTxt += `Sitemap: https://${resolvedDomain}/sitemap.xml\n`;

      return robotsTxt;
    } catch (error) {
      this.logger.error(`Error generating robots.txt: ${error.message}`, error.stack);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to generate robots.txt');
    }
  }

  /**
   * Generate security.txt content
   * @param domain Website domain
   * @returns security.txt content
   */
  async generateSecurityTxt(domain: string) {
    try {
      // 处理域名
      const { domain: resolvedDomain, website } = await this.resolveDomain(domain, 'generateSecurityTxt');

      // 检查是否有自定义安全设置
      const analyticsSettings = website.seo?.analyticsSettings as Record<string, any>;
      const securityTxt = analyticsSettings?.securityTxt;
      
      if (securityTxt) {
        // 如果有自定义内容，直接返回
        return securityTxt;
      }

      // 生成默认的 security.txt
      const expiryDate = new Date();
      expiryDate.setFullYear(expiryDate.getFullYear() + 1); // 一年后过期
      
      let defaultSecurityTxt = '';
      defaultSecurityTxt += `# Security Policy for ${resolvedDomain}\n`;
      defaultSecurityTxt += `Contact: mailto:security@${resolvedDomain}\n`;
      defaultSecurityTxt += `Expires: ${expiryDate.toISOString()}\n`;
      defaultSecurityTxt += 'Preferred-Languages: en, zh\n';
      defaultSecurityTxt += `Policy: https://${resolvedDomain}/security-policy\n`;
      defaultSecurityTxt += `Canonical: https://${resolvedDomain}/.well-known/security.txt\n`;
      
      return defaultSecurityTxt;
    } catch (error) {
      this.logger.error(`Error generating security.txt: ${error.message}`, error.stack);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to generate security.txt');
    }
  }
}
