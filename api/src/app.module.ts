import { Module, ValidationPipe } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { APP_PIPE } from '@nestjs/core';
import { HttpModule } from '@nestjs/axios';
import { AuthModule } from './auth/auth.module';
import { UserModule } from './user/user.module';
import { WorkspaceModule } from './workspace/workspace.module';
import { WebsiteModule } from './website/website.module';
import { PageModule } from './page/page.module';
import { QuotaModule } from './quota/quota.module';
import { OpenAiModule } from './openai/openai.module';
import { BuildModule } from './build/build.module';
import { BullModule } from '@nestjs/bull';
import { PrismaModule } from './prisma/prisma.module';
import { ImagesModule } from './images/images.module';
import { HealthModule } from './health/health.module';
import { LitformModule } from './modules/litform/litform.module';
import { SeoModule } from './seo/seo.module';

@Module({
  imports: [
    HttpModule,
    ConfigModule.forRoot(),
    PrismaModule,
    AuthModule,
    UserModule,
    WorkspaceModule,
    WebsiteModule,
    PageModule,
    QuotaModule,
    BullModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        redis: {
          host: configService.get('REDIS_HOST', 'localhost'),
          port: parseInt(configService.get('REDIS_PORT', '6379')),
          password: configService.get('REDIS_PASSWORD', undefined),
          db: parseInt(configService.get('REDIS_DB', '0')),
        },
      }),
      inject: [ConfigService],
    }),
    BuildModule,
    OpenAiModule,
    ImagesModule,
    HealthModule,
    LitformModule,
    SeoModule,
  ],
  providers: [
    {
      provide: APP_PIPE,
      useClass: ValidationPipe,
    },
  ],
})
export class AppModule {}
