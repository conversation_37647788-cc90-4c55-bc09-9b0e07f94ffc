# Universal CMS 数据模型 ER 文档

> 版本：v1.0  
> 最后更新：2025-01-16  
> 状态：设计阶段

---

## 1. 文档概述

### 1.1 设计目标
本文档描述了Universal CMS系统的完整数据模型设计，包括：
- **多内容类型支持**：Blog、Documentation、Changelog、FAQ等
- **版本控制系统**：内容身份与版本数据分离的设计
- **工作空间集成**：与现有工作空间架构的无缝集成
- **权限体系统一**：基于工作空间成员角色的权限管理

### 1.2 设计原则
1. **架构一致性**：与现有Page模型保持一致的关联层次
2. **数据分离**：内容身份管理与版本数据分离
3. **权限统一**：复用现有工作空间权限机制
4. **扩展性强**：支持未来内容类型和功能扩展

---

## 2. 核心实体关系图

### 2.1 整体架构图

```mermaid
erDiagram
    User ||--o{ Workspace : creates
    User ||--o{ ContentNode : creates
    User ||--o{ ContentRevision : creates
    User ||--o{ ContentAuditLog : performs
    
    Workspace ||--o{ Website : contains
    Workspace ||--o{ ContentNode : contains
    Workspace ||--o{ Category : contains
    Workspace ||--o{ Tag : contains
    
    Website ||--o{ ContentNode : hosts
    Website ||--o{ Category : categorizes
    Website ||--o{ Tag : tags
    
    ContentNode ||--o{ ContentRevision : versions
    ContentNode ||--o| Category : belongs_to
    ContentNode }o--o{ Tag : tagged_with
    ContentNode ||--o{ ContentAuditLog : logged
    
    Category ||--o{ Category : parent_child
    ContentNode }o--o{ Tag : ContentNodeTag
    
    ContentNode {
        string id PK
        string userId FK
        string workspaceId FK
        string websiteId FK
        enum type
        string slug
        string title
        string excerpt
                 json authorInfo
        enum status
        string currentRevisionId FK
        string categoryId FK
        enum language
        datetime publishedAt
        datetime createdAt
        datetime updatedAt
    }
    
    ContentRevision {
        string id PK
        string nodeId FK
        string createdById FK
        int version
        json data
        string changelog
        boolean isPublished
        datetime createdAt
        datetime publishedAt
    }
    
    Category {
        string id PK
        string workspaceId FK
        string websiteId FK
        string name
        string slug
        string description
        string parentId FK
        int order
        enum type
        enum language
        boolean isActive
        datetime createdAt
        datetime updatedAt
    }
    
    Tag {
        string id PK
        string workspaceId FK
        string websiteId FK
        string name
        string color
        string description
        enum language
        boolean isActive
        datetime createdAt
        datetime updatedAt
    }
    
    ContentNodeTag {
        string contentId FK
        string tagId FK
    }
    
    ContentAuditLog {
        string id PK
        string contentId FK
        string userId FK
        enum action
        string revisionId FK
        json metadata
        datetime createdAt
    }
```

### 2.2 层级关系图

```mermaid
graph TD
    A[User] --> B[Workspace]
    B --> C[Website]
    C --> D[ContentNode]
    D --> E[ContentRevision]
    
    F[Category] --> D
    G[Tag] -.-> D
    
    H[ContentAuditLog] --> D
    I[ContentNodeTag] --> D
    I --> G
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec
```

---

## 3. 详细数据模型定义

### 3.1 核心内容模型

#### 3.1.1 ContentNode（内容节点）

**作用**：内容的身份管理，存储内容的基本信息和状态

```prisma
model ContentNode {
  id                String @id @default(uuid())
  
  // === 层级关联（参考Page模型） ===
  userId            String                              // 创建用户ID
  user              User @relation(fields: [userId], references: [id])
  workspaceId       String                              // 所属工作空间ID  
  workspace         Workspace @relation(fields: [workspaceId], references: [id])
  websiteId         String                              // 所属网站ID
  website           Website @relation(fields: [websiteId], references: [id])
  
  // === 内容基础信息 ===
  type              ContentType                         // 内容类型
  slug              String                              // URL slug
  title             String                              // 标题
  excerpt           String?                             // 摘要
  language          Language @default(EN)               // 语言
  
  // === 作者显示信息（JSON结构，支持自定义和扩展） ===
  authorInfo        Json                                // 作者信息（JSON格式）
  
  // === 内容状态管理 ===
  status            ContentStatus @default(DRAFT)       // 内容状态
  publishedAt       DateTime?                           // 发布时间
  createdAt         DateTime @default(now())            // 创建时间
  updatedAt         DateTime @updatedAt                 // 更新时间
  
  // === 版本管理 ===
  currentRevisionId String? @unique                     // 当前版本ID
  currentRevision   ContentRevision? @relation("CurrentRevision", fields: [currentRevisionId], references: [id])
  
  // === 分类和标签 ===
  categoryId        String?                             // 分类ID
  category          Category? @relation(fields: [categoryId], references: [id])
  
  // === 关联关系 ===
  revisions         ContentRevision[] @relation("NodeRevisions")  // 版本历史
  tags              ContentNodeTag[]                    // 标签关联
  auditLogs         ContentAuditLog[]                   // 审计日志
  
  // === 唯一性约束和索引 ===
  @@unique([websiteId, slug, language])                 // 同网站同语言下slug唯一
  @@index([userId])                                     // 按用户查询
  @@index([workspaceId])                                // 按工作空间查询
  @@index([websiteId])                                  // 按网站查询
  @@index([websiteId, type, status])                   // 按网站+类型+状态查询
  @@index([websiteId, status, publishedAt])            // 按网站+状态+发布时间查询
  @@index([authorInfo])                                 // 按作者信息查询（JSON索引）
  @@index([categoryId])                                 // 按分类查询
}
```

**字段说明**：
- `userId`：系统创建者，用于权限控制和审计
- `authorInfo`：作者显示信息（JSON格式），支持自定义和扩展
- `currentRevisionId`：指向当前活跃的版本，实现版本分离

**authorInfo 字段结构**：
```json
{
  "name": "作者显示名称",           // 必填，前端显示的作者名
  "avatar": "头像URL",            // 可选，作者头像
  "bio": "作者简介",              // 可选，作者描述
  "email": "联系邮箱",            // 可选，公开联系邮箱
  "website": "个人网站",          // 可选，个人主页链接
  "social": {                     // 可选，社交媒体链接
    "twitter": "@username",
    "github": "username",
    "linkedin": "profile-url"
  },
  "organization": {               // 可选，组织信息
    "name": "组织名称",
    "role": "职位/角色",
    "logo": "组织LOGO"
  },
  "metadata": {                   // 可选，扩展元数据
    "location": "地理位置",
    "timezone": "时区",
    "expertise": ["技能标签"],
    "customFields": {}            // 自定义字段
  }
}
```

#### 3.1.2 ContentRevision（内容版本）

**作用**：存储内容的具体数据和版本历史

```prisma
model ContentRevision {
  id              String @id @default(uuid())
  nodeId          String                                // 关联的内容节点ID
  node            ContentNode @relation("NodeRevisions", fields: [nodeId], references: [id])
  createdById     String                                // 版本创建者ID
  createdBy       User @relation(fields: [createdById], references: [id])
  
  // === 版本信息 ===
  version         Int                                   // 版本号（从1开始递增）
  data            Json                                  // 内容数据（JSON格式）
  changelog       String?                               // 版本变更说明
  isPublished     Boolean @default(false)               // 是否已发布
  
  // === 时间信息 ===
  createdAt       DateTime @default(now())              // 版本创建时间
  publishedAt     DateTime?                             // 版本发布时间
  
  // === 关联关系 ===
  currentNode     ContentNode? @relation("CurrentRevision")  // 作为当前版本的节点
  
  // === 索引 ===
  @@unique([nodeId, version])                           // 同节点下版本号唯一
  @@index([nodeId, isPublished])                       // 按节点和发布状态查询
  @@index([createdAt])                                  // 按创建时间查询
  @@index([createdById])                                // 按创建者查询
}
```

**data字段结构示例**：
```json
{
  "content": "文章内容...",
  "blocks": [...],           // 可视化编辑器的块数据
  "seo": {                   // SEO相关数据
    "metaTitle": "...",
    "metaDescription": "...",
    "keywords": [...]
  },
  "customFields": {          // 自定义字段
    "readingTime": 5,
    "difficulty": "beginner"
  }
}
```

### 3.2 分类和标签模型

#### 3.2.1 Category（分类）

**作用**：内容的层级分类管理，支持树形结构

```prisma
model Category {
  id            String @id @default(uuid())
  workspaceId   String                                  // 关联工作空间
  workspace     Workspace @relation(fields: [workspaceId], references: [id])
  websiteId     String                                  // 关联网站
  website       Website @relation(fields: [websiteId], references: [id])
  
  // === 分类信息 ===
  name          String                                  // 分类名称
  slug          String                                  // URL slug
  description   String?                                 // 分类描述
  
  // === 层级关系 ===
  parentId      String?                                 // 父分类ID（支持层级）
  parent        Category? @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children      Category[] @relation("CategoryHierarchy")
  
  // === 配置信息 ===
  order         Int @default(0)                         // 排序
  type          CategoryType @default(GENERAL)          // 分类类型
  language      Language @default(EN)                   // 语言
  isActive      Boolean @default(true)                  // 是否启用
  
  // === 关联 ===
  contents      ContentNode[]                           // 分类下的内容
  
  // === 时间信息 ===
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  
  // === 索引 ===
  @@unique([workspaceId, slug, language])               // 工作空间内slug唯一
  @@index([workspaceId])
  @@index([websiteId])
  @@index([parentId])
  @@index([type, language])
  @@index([order])
}
```

#### 3.2.2 Tag（标签）

**作用**：内容的横向标签管理，支持多对多关联

```prisma
model Tag {
  id            String @id @default(uuid())
  workspaceId   String                                  // 关联工作空间
  workspace     Workspace @relation(fields: [workspaceId], references: [id])
  websiteId     String                                  // 关联网站
  website       Website @relation(fields: [websiteId], references: [id])
  
  // === 标签信息 ===
  name          String                                  // 标签名称
  color         String?                                 // 标签颜色（十六进制）
  description   String?                                 // 标签描述
  language      Language @default(EN)                   // 语言
  isActive      Boolean @default(true)                  // 是否启用
  
  // === 关联 ===
  contentNodes  ContentNodeTag[]                        // 标签关联的内容
  
  // === 时间信息 ===
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  
  // === 索引 ===
  @@unique([workspaceId, name, language])               // 工作空间内标签名唯一
  @@index([workspaceId])
  @@index([websiteId])
  @@index([language])
}
```

#### 3.2.3 ContentNodeTag（内容标签关联）

**作用**：内容与标签的多对多关联表

```prisma
model ContentNodeTag {
  contentId     String                                  // 内容ID
  tagId         String                                  // 标签ID
  
  // === 关联 ===
  content       ContentNode @relation(fields: [contentId], references: [id], onDelete: Cascade)
  tag           Tag @relation(fields: [tagId], references: [id], onDelete: Cascade)
  
  // === 扩展字段（预留） ===
  weight        Int @default(1)                         // 标签权重（预留）
  createdAt     DateTime @default(now())                // 关联创建时间
  
  // === 主键 ===
  @@id([contentId, tagId])
  @@index([tagId])                                      // 按标签查询内容
}
```

### 3.3 审计和日志模型

#### 3.3.1 ContentAuditLog（内容审计日志）

**作用**：记录内容的所有操作历史，用于审计和追踪

```prisma
model ContentAuditLog {
  id              String @id @default(uuid())
  contentId       String                                // 关联内容ID
  content         ContentNode @relation(fields: [contentId], references: [id])
  userId          String                                // 操作用户ID
  user            User @relation(fields: [userId], references: [id])
  
  // === 操作信息 ===
  action          ContentAuditAction                    // 操作类型
  revisionId      String?                               // 相关版本ID
  metadata        Json?                                 // 额外元数据
  
  // === 时间信息 ===
  createdAt       DateTime @default(now())              // 操作时间
  
  // === 索引 ===
  @@index([contentId])                                  // 按内容查询
  @@index([userId])                                     // 按用户查询
  @@index([action])                                     // 按操作类型查询
  @@index([createdAt])                                  // 按时间查询
  @@index([contentId, createdAt])                      // 内容操作时间线
}
```

---

## 4. 枚举类型定义

### 4.1 内容相关枚举

```prisma
// 内容状态
enum ContentStatus {
  DRAFT           // 草稿状态
  PUBLISHED       // 已发布
  ARCHIVED        // 已归档
}

// 内容类型
enum ContentType {
  BLOG            // 博客文章
  DOC             // 文档
  CHANGELOG       // 更新日志
  FAQ             // 常见问题
  PRODUCT         // 产品介绍
  ANNOUNCEMENT    // 公告通知
  TUTORIAL        // 教程指南
  CASE_STUDY      // 案例研究
  WHITEPAPER      // 白皮书
  CUSTOM          // 自定义类型
}

// 分类类型
enum CategoryType {
  GENERAL         // 通用分类
  BLOG            // 博客分类
  DOC             // 文档分类
  FAQ             // FAQ分类
  CHANGELOG       // 更新日志分类
  PRODUCT         // 产品分类
  TUTORIAL        // 教程分类
  ANNOUNCEMENT    // 公告分类
  CASE_STUDY      // 案例研究分类
  WHITEPAPER      // 白皮书分类
}

// 审计操作类型
enum ContentAuditAction {
  CREATED         // 内容创建
  DRAFT_SAVED     // 草稿保存
  PUBLISHED       // 内容发布
  MODIFIED        // 内容修改
  ARCHIVED        // 内容归档
  RESTORED        // 内容恢复
  DELETED         // 内容删除
  REVISION_CREATED // 新版本创建
  REVISION_PUBLISHED // 版本发布
  METADATA_UPDATED // 元数据更新
  CATEGORY_CHANGED // 分类变更
  TAGS_UPDATED    // 标签更新
  AUTHOR_UPDATED  // 作者信息更新
  SEO_UPDATED     // SEO信息更新
}
```

### 4.2 复用现有枚举

```prisma
// 复用现有的语言枚举
enum Language {
  EN  // 英文
  CN  // 中文
  ZH  // 繁体中文
  ES  // 西班牙语
  // ... 其他语言
}
```

### 4.3 枚举值使用规范

**⚠️ 重要说明**：所有枚举值统一使用大写格式，符合数据库和API最佳实践

**正确用法**：
```typescript
// ✅ 正确 - 使用大写枚举值
status: ContentStatus.PUBLISHED
type: ContentType.BLOG
categoryType: CategoryType.BLOG

// ✅ 正确 - 字符串形式也使用大写
where: { status: 'PUBLISHED' }
```

**错误用法**：
```typescript
// ❌ 错误 - 小写形式已废弃
status: 'published'
type: 'blog'
```

---

## 5. 与现有模型的集成

### 5.1 扩展现有模型

#### 5.1.1 User 模型扩展

```prisma
model User {
  // ... 现有字段
  
  // === 新增内容关联 ===
  createdContents   ContentNode[] @relation("UserCreatedContent")      // 用户创建的内容
  contentRevisions  ContentRevision[]                                  // 用户创建的内容版本
  contentAuditLogs  ContentAuditLog[]                                  // 用户的内容审计日志
}
```

#### 5.1.2 Workspace 模型扩展

```prisma
model Workspace {
  // ... 现有字段
  
  // === 新增内容关联 ===
  contents          ContentNode[]                                      // 工作空间的内容
  contentCategories Category[]                                         // 工作空间的内容分类
  contentTags       Tag[]                                              // 工作空间的内容标签
}
```

#### 5.1.3 Website 模型扩展

```prisma
model Website {
  // ... 现有字段
  
  // === 新增内容关联 ===
  contents          ContentNode[]                                      // 网站的内容
  contentCategories Category[]                                         // 网站的内容分类
  contentTags       Tag[]                                              // 网站的内容标签
}
```

#### 5.1.4 Quota 模型扩展

```prisma
model Quota {
  // ... 现有字段
  
  // === 新增内容配额 ===
  contentsCreated       Int @default(0)                                // 已创建内容数
  contentCreationLimit  Int @default(100)                              // 内容创建限制
  contentRevisionsLimit Int @default(1000)                             // 内容版本数限制
}
```

---

## 6. 索引策略设计

### 6.1 性能关键索引

```prisma
// ContentNode 索引策略
@@index([userId])                                     // 用户内容查询
@@index([workspaceId])                                // 工作空间内容查询
@@index([websiteId])                                  // 网站内容查询
@@index([websiteId, type, status])                   // 网站内容类型状态查询
@@index([websiteId, status, publishedAt])            // 网站发布内容时间线
@@index([authorInfo])                                 // 作者内容查询（JSON索引）
@@index([categoryId])                                 // 分类内容查询
@@index([status, publishedAt])                       // 全局发布内容查询
@@index([type, language])                            // 类型语言查询

// ContentRevision 索引策略
@@index([nodeId, isPublished])                       // 节点发布版本查询
@@index([nodeId, version])                           // 节点版本历史查询
@@index([createdAt])                                  // 时间序列查询
@@index([createdById])                                // 创建者版本查询

// Category 索引策略
@@index([workspaceId])                                // 工作空间分类查询
@@index([websiteId])                                  // 网站分类查询
@@index([parentId])                                   // 子分类查询
@@index([type, language])                            // 类型语言分类查询
@@index([order])                                      // 排序查询

// Tag 索引策略
@@index([workspaceId])                                // 工作空间标签查询
@@index([websiteId])                                  // 网站标签查询
@@index([language])                                   // 语言标签查询

// ContentAuditLog 索引策略
@@index([contentId])                                  // 内容审计查询
@@index([userId])                                     // 用户操作查询
@@index([action])                                     // 操作类型查询
@@index([createdAt])                                  // 时间查询
@@index([contentId, createdAt])                      // 内容操作时间线
```

### 6.2 复合索引优化

```sql
-- 高频查询场景的复合索引
CREATE INDEX idx_content_website_type_status_published 
ON ContentNode(websiteId, type, status, publishedAt DESC);

CREATE INDEX idx_content_workspace_status_created 
ON ContentNode(workspaceId, status, createdAt DESC);

CREATE INDEX idx_revision_node_published_version 
ON ContentRevision(nodeId, isPublished, version DESC);

CREATE INDEX idx_category_workspace_type_order 
ON Category(workspaceId, type, order);

-- 全文搜索索引（PostgreSQL）
CREATE INDEX idx_content_search_text 
ON ContentNode USING gin(to_tsvector('english', title || ' ' || excerpt || ' ' || (authorInfo->>'name')));

-- JSON字段索引（针对作者信息）
CREATE INDEX idx_content_author_name 
ON ContentNode USING gin ((authorInfo->>'name'));

CREATE INDEX idx_content_author_email 
ON ContentNode USING gin ((authorInfo->>'email'));

CREATE INDEX idx_content_author_organization 
ON ContentNode USING gin ((authorInfo->'organization'->>'name'));
```

---

## 7. 查询模式设计

### 7.1 常用查询示例

#### 7.1.1 内容列表查询

```typescript
// 获取网站的已发布内容
async function getPublishedContents(websiteId: string, options?: {
  type?: ContentType;
  categoryId?: string;
  tagIds?: string[];
  language?: Language;
  page?: number;
  limit?: number;
}) {
  return await prisma.contentNode.findMany({
    where: {
      websiteId,
      status: 'PUBLISHED',
      type: options?.type,
      categoryId: options?.categoryId,
      language: options?.language,
      tags: options?.tagIds ? {
        some: {
          tagId: { in: options.tagIds }
        }
      } : undefined,
    },
    include: {
      category: true,
      tags: {
        include: { tag: true }
      },
      currentRevision: {
        select: {
          data: true,
          publishedAt: true,
        }
      },
    },
    orderBy: [
      { publishedAt: 'desc' },
      { createdAt: 'desc' }
    ],
    skip: options?.page ? (options.page - 1) * (options.limit || 10) : 0,
    take: options?.limit || 10,
  });
}
```

#### 7.1.2 内容详情查询

```typescript
// 获取内容详情（包含完整版本信息）
async function getContentDetail(websiteId: string, slug: string, language: Language = 'EN') {
  return await prisma.contentNode.findFirst({
    where: {
      websiteId,
      slug,
      language,
      status: 'PUBLISHED',
    },
    include: {
      category: {
        include: {
          parent: true,
          children: true,
        }
      },
      tags: {
        include: { tag: true }
      },
      currentRevision: true,
      user: {
        select: {
          id: true,
          name: true,
          image: true,
        }
      },
    },
  });
}
```

#### 7.1.3 分类树查询

```typescript
// 获取分类树结构
async function getCategoryTree(websiteId: string, type?: CategoryType, language: Language = 'EN') {
  const categories = await prisma.category.findMany({
    where: {
      websiteId,
      type,
      language,
      isActive: true,
    },
    include: {
      children: {
        where: { isActive: true },
        orderBy: { order: 'asc' },
      },
             _count: {
         select: {
           contents: {
             where: { status: 'PUBLISHED' }
           }
         }
       }
    },
    orderBy: { order: 'asc' },
  });

  // 构建树结构（仅返回根节点，children已包含子节点）
  return categories.filter(cat => !cat.parentId);
}
```

#### 7.1.4 内容搜索查询

```typescript
// 全文搜索内容
async function searchContents(websiteId: string, query: string, options?: {
  type?: ContentType;
  categoryId?: string;
  language?: Language;
}) {
  return await prisma.contentNode.findMany({
         where: {
       websiteId,
       status: 'PUBLISHED',
       type: options?.type,
       categoryId: options?.categoryId,
       language: options?.language,
             OR: [
         { title: { contains: query, mode: 'insensitive' } },
         { excerpt: { contains: query, mode: 'insensitive' } },
         { authorInfo: { path: ['name'], string_contains: query } },
       ],
    },
    include: {
      category: true,
      tags: { include: { tag: true } },
      currentRevision: {
        select: {
          data: true,
          publishedAt: true,
        }
      },
    },
    orderBy: [
      { publishedAt: 'desc' },
      { createdAt: 'desc' }
    ],
  });
}
```

### 7.2 管理端查询

#### 7.2.1 工作空间内容管理

```typescript
// 获取工作空间内容（管理端）
async function getWorkspaceContents(workspaceId: string, userId: string, options?: {
  status?: ContentStatus;
  type?: ContentType;
  authorId?: string;
}) {
  // 首先检查权限
  const hasPermission = await checkWorkspacePermission(userId, workspaceId, 'read');
  if (!hasPermission) throw new Error('Insufficient permissions');

  return await prisma.contentNode.findMany({
    where: {
      workspaceId,
      status: options?.status,
      type: options?.type,
      userId: options?.authorId,
    },
    include: {
      user: { select: { id: true, name: true, image: true } },
      website: { select: { id: true, name: true, domain: true } },
      category: true,
      tags: { include: { tag: true } },
      currentRevision: {
        select: {
          version: true,
          isPublished: true,
          createdAt: true,
          publishedAt: true,
        }
      },
      _count: {
        select: { revisions: true }
      }
    },
    orderBy: [
      { updatedAt: 'desc' },
      { createdAt: 'desc' }
    ],
  });
}
```

#### 7.2.2 版本历史查询

```typescript
// 获取内容版本历史
async function getContentVersionHistory(contentId: string, userId: string) {
  // 权限检查
  const content = await prisma.contentNode.findUnique({
    where: { id: contentId },
    include: { workspace: { include: { members: { where: { userId } } } } }
  });
  
  if (!content?.workspace.members.length) {
    throw new Error('Insufficient permissions');
  }

  return await prisma.contentRevision.findMany({
    where: { nodeId: contentId },
    include: {
      createdBy: {
        select: { id: true, name: true, image: true }
      }
    },
    orderBy: { version: 'desc' },
  });
}
```

---

## 8. 业务逻辑设计

### 8.1 内容创建流程

```typescript
// 创建内容的完整流程
async function createContent(
  data: CreateContentDto,
  userId: string,
  websiteId: string
) {
  return await prisma.$transaction(async (tx) => {
    // 1. 获取网站和工作空间信息
    const website = await tx.website.findUnique({
      where: { id: websiteId },
      include: { workspace: true }
    });
    
    if (!website) throw new Error('Website not found');
    
    // 2. 检查权限
    const hasPermission = await checkWorkspacePermission(
      userId, 
      website.workspaceId, 
      'write'
    );
    if (!hasPermission) throw new Error('Insufficient permissions');
    
    // 3. 检查配额
    await checkContentQuota(website.workspaceId);
    
    // 4. 获取用户信息用于默认作者信息
    const user = await tx.user.findUnique({ where: { id: userId } });
    
    // 5. 创建内容节点
    const contentNode = await tx.contentNode.create({
      data: {
        userId,
        workspaceId: website.workspaceId,
        websiteId,
        type: data.type,
        slug: data.slug,
        title: data.title,
        excerpt: data.excerpt,
        language: data.language || 'EN',
        
                 // 作者信息（支持自定义）
         authorInfo: {
           name: data.authorInfo?.name || user.name,
           avatar: data.authorInfo?.avatar || user.image,
           bio: data.authorInfo?.bio,
           email: data.authorInfo?.email,
           website: data.authorInfo?.website,
           social: data.authorInfo?.social || {},
           organization: data.authorInfo?.organization,
           metadata: data.authorInfo?.metadata || {}
         },
        
        categoryId: data.categoryId,
                 status: 'DRAFT',
      }
    });
    
    // 6. 创建初始版本
    const revision = await tx.contentRevision.create({
      data: {
        nodeId: contentNode.id,
        createdById: userId,
        version: 1,
        data: data.content,
        changelog: 'Initial version',
      }
    });
    
    // 7. 更新当前版本指针
    await tx.contentNode.update({
      where: { id: contentNode.id },
      data: { currentRevisionId: revision.id }
    });
    
    // 8. 处理标签关联
    if (data.tagIds?.length) {
      await tx.contentNodeTag.createMany({
        data: data.tagIds.map(tagId => ({
          contentId: contentNode.id,
          tagId,
        }))
      });
    }
    
    // 9. 记录审计日志
    await tx.contentAuditLog.create({
      data: {
        contentId: contentNode.id,
        userId,
        action: 'CREATED',
        revisionId: revision.id,
        metadata: {
          initialData: {
            type: data.type,
            title: data.title,
            categoryId: data.categoryId,
          }
        }
      }
    });
    
    // 10. 更新配额
    await tx.quota.update({
      where: { workspaceId: website.workspaceId },
      data: { contentsCreated: { increment: 1 } }
    });
    
    return contentNode;
  });
}
```

### 8.2 内容发布流程

```typescript
// 发布内容的完整流程
async function publishContent(contentId: string, userId: string) {
  return await prisma.$transaction(async (tx) => {
    // 1. 获取内容和权限检查
    const content = await tx.contentNode.findUnique({
      where: { id: contentId },
      include: {
        currentRevision: true,
        workspace: {
          include: {
            members: { where: { userId } }
          }
        }
      }
    });
    
    if (!content) throw new Error('Content not found');
    if (!content.workspace.members.length) throw new Error('Insufficient permissions');
    
         // 2. 检查内容状态
     if (content.status === 'PUBLISHED') {
       throw new Error('Content is already published');
     }
    
    if (!content.currentRevision) {
      throw new Error('No revision to publish');
    }
    
    // 3. 更新内容状态
    const publishedAt = new Date();
    await tx.contentNode.update({
      where: { id: contentId },
             data: {
         status: 'PUBLISHED',
         publishedAt,
       }
    });
    
    // 4. 标记版本为已发布
    await tx.contentRevision.update({
      where: { id: content.currentRevisionId },
      data: {
        isPublished: true,
        publishedAt,
      }
    });
    
    // 5. 记录审计日志
    await tx.contentAuditLog.create({
      data: {
        contentId,
        userId,
        action: 'PUBLISHED',
        revisionId: content.currentRevisionId,
        metadata: {
          publishedAt,
          version: content.currentRevision.version,
        }
      }
    });
    
    return content;
  });
}
```

---

## 9. 性能优化建议

### 9.1 查询优化策略

1. **分页查询**：所有列表查询都应支持分页，避免大量数据加载
2. **字段选择**：使用 `select` 仅查询需要的字段，特别是 JSON 字段
3. **预加载关联**：合理使用 `include` 减少 N+1 查询问题
4. **索引覆盖**：确保查询条件都有对应的索引

### 9.2 缓存策略

```typescript
// Redis 缓存策略示例
const CACHE_KEYS = {
  PUBLISHED_CONTENT: (websiteId: string, slug: string, lang: string) => 
    `content:published:${websiteId}:${slug}:${lang}`,
  CATEGORY_TREE: (websiteId: string, type: string, lang: string) => 
    `categories:tree:${websiteId}:${type}:${lang}`,
  CONTENT_LIST: (websiteId: string, type: string, page: number) => 
    `contents:list:${websiteId}:${type}:${page}`,
};

// 缓存失效策略
async function invalidateContentCache(contentNode: ContentNode) {
  const keys = [
    CACHE_KEYS.PUBLISHED_CONTENT(contentNode.websiteId, contentNode.slug, contentNode.language),
    CACHE_KEYS.CONTENT_LIST(contentNode.websiteId, contentNode.type, '*'),
  ];
  
  await redis.del(...keys);
}
```

### 9.3 数据库优化

1. **版本清理**：定期清理过期的草稿版本
2. **审计日志归档**：定期归档老的审计日志
3. **JSON字段索引**：对 JSON 字段中的关键路径创建索引

```sql
-- ContentRevision.data 字段索引示例（PostgreSQL）
CREATE INDEX idx_revision_data_seo_title 
ON ContentRevision USING gin ((data->'seo'->>'title'));

CREATE INDEX idx_revision_data_content_type 
ON ContentRevision USING gin ((data->>'contentType'));

-- ContentNode.authorInfo 字段索引示例
CREATE INDEX idx_content_author_name_gin 
ON ContentNode USING gin ((authorInfo->>'name') gin_trgm_ops);

CREATE INDEX idx_content_author_org_name 
ON ContentNode USING gin ((authorInfo->'organization'->>'name') gin_trgm_ops);

CREATE INDEX idx_content_author_social_twitter 
ON ContentNode USING gin ((authorInfo->'social'->>'twitter'));
```

### 9.4 JSON字段设计最佳实践

#### 9.4.1 authorInfo 字段优化

**查询优化示例**：
```typescript
// 按作者名称查询
const contentsByAuthor = await prisma.contentNode.findMany({
  where: {
    authorInfo: {
      path: ['name'],
      string_contains: 'John Doe'
    }
  }
});

// 按组织查询
const contentsByOrg = await prisma.contentNode.findMany({
  where: {
    authorInfo: {
      path: ['organization', 'name'],
      equals: 'Tech Company'
    }
  }
});

// 按社交媒体查询
const contentsByTwitter = await prisma.contentNode.findMany({
  where: {
    authorInfo: {
      path: ['social', 'twitter'],
      equals: '@username'
    }
  }
});
```

**字段验证建议**：
```typescript
// authorInfo JSON schema 验证
const authorInfoSchema = {
  type: 'object',
  required: ['name'],
  properties: {
    name: { type: 'string', minLength: 1, maxLength: 100 },
    avatar: { type: 'string', format: 'uri' },
    bio: { type: 'string', maxLength: 500 },
    email: { type: 'string', format: 'email' },
    website: { type: 'string', format: 'uri' },
    social: {
      type: 'object',
      properties: {
        twitter: { type: 'string', pattern: '^@[a-zA-Z0-9_]+$' },
        github: { type: 'string' },
        linkedin: { type: 'string', format: 'uri' }
      }
    },
    organization: {
      type: 'object',
      properties: {
        name: { type: 'string', maxLength: 100 },
        role: { type: 'string', maxLength: 100 },
        logo: { type: 'string', format: 'uri' }
      }
    },
    metadata: {
      type: 'object',
      properties: {
        location: { type: 'string' },
        timezone: { type: 'string' },
        expertise: { 
          type: 'array', 
          items: { type: 'string' },
          maxItems: 10
        }
      }
    }
  }
};
```

---

## 10. 安全考虑

### 10.1 权限控制

1. **基于工作空间的权限**：所有操作都要检查用户在对应工作空间的权限
2. **内容可见性**：确保只有已发布的内容对外可见
3. **作者权限**：内容创建者应有额外的编辑权限

### 10.2 数据验证

1. **Slug 唯一性**：确保同网站同语言下 slug 唯一
2. **内容类型验证**：验证内容类型与分类类型的匹配
3. **JSON 数据验证**：对 ContentRevision.data 字段进行结构验证

### 10.3 SQL 注入防护

所有查询都使用 Prisma ORM，天然防护 SQL 注入攻击。

---

## 11. 迁移和部署

### 11.1 数据迁移脚本

```sql
-- 创建新表的迁移脚本
-- prisma/migrations/xxx_add_cms_models/migration.sql

-- 创建内容节点表
CREATE TABLE "ContentNode" (
  "id" TEXT NOT NULL,
  "userId" TEXT NOT NULL,
  "workspaceId" TEXT NOT NULL,
  "websiteId" TEXT NOT NULL,
  "type" "ContentType" NOT NULL,
  "slug" TEXT NOT NULL,
  "title" TEXT NOT NULL,
  "excerpt" TEXT,
     "authorInfo" JSONB NOT NULL,
     "status" "ContentStatus" NOT NULL DEFAULT 'DRAFT',
  "publishedAt" TIMESTAMP(3),
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,
  "currentRevisionId" TEXT,
  "categoryId" TEXT,
  "language" "Language" NOT NULL DEFAULT 'EN',

  CONSTRAINT "ContentNode_pkey" PRIMARY KEY ("id")
);

-- 其他表的创建脚本...
-- （完整的迁移脚本会由 Prisma 自动生成）
```

### 11.2 部署清单

1. **数据库迁移**：运行 `prisma migrate deploy`
2. **索引创建**：确保所有性能关键索引都已创建
3. **缓存预热**：预加载热点内容到缓存
4. **权限验证**：确保权限控制逻辑正确工作

---

## 12. 总结

### 12.1 设计优势

1. **架构一致性**：与现有 Page 模型保持一致的层级关系
2. **数据分离**：内容身份与版本数据分离，版本管理清晰
3. **扩展性强**：支持多种内容类型，易于扩展新功能
4. **权限统一**：复用现有工作空间权限体系
5. **性能优化**：合理的索引设计和查询优化
6. **规范统一**：枚举值统一使用大写格式，符合最佳实践
7. **字段优化**：作者信息使用JSON结构，提供最大扩展性

### 12.2 authorInfo JSON结构的应用场景

#### 12.2.1 个人博客场景
```json
{
  "name": "张三",
  "avatar": "/avatars/zhang-san.jpg",
  "bio": "10年前端开发经验，专注React生态",
  "email": "<EMAIL>",
  "website": "https://zhangsan.dev",
  "social": {
    "twitter": "@zhangsan_dev",
    "github": "zhangsan"
  },
  "metadata": {
    "location": "北京",
    "expertise": ["React", "TypeScript", "Node.js"]
  }
}
```

#### 12.2.2 企业团队场景
```json
{
  "name": "ABC科技团队",
  "avatar": "/logos/abc-tech.png",
  "bio": "专注AI和机器学习技术研发",
  "email": "<EMAIL>",
  "website": "https://abc-tech.com",
  "organization": {
    "name": "ABC科技有限公司",
    "role": "技术团队",
    "logo": "/logos/abc-company.png"
  },
  "social": {
    "twitter": "@abc_tech",
    "linkedin": "https://linkedin.com/company/abc-tech"
  },
  "metadata": {
    "location": "上海",
    "departments": ["AI研发部", "产品部"],
    "established": "2020"
  }
}
```

#### 12.2.3 匿名/笔名场景
```json
{
  "name": "代码诗人",
  "avatar": "/avatars/poet-avatar.jpg",
  "bio": "用代码书写人生",
  "social": {
    "github": "code-poet"
  },
  "metadata": {
    "writingStyle": "技术散文",
    "anonymous": true,
    "expertise": ["算法", "架构设计"]
  }
}
```

### 12.3 后续计划

1. **Phase 1**：实现核心模型和基础 CRUD API（2-3周）
2. **Phase 2**：实现内容编辑器和版本管理界面（2-3周）
3. **Phase 3**：实现搜索、SEO 和性能优化（3-4周）
4. **Phase 4**：实现高级功能如内容模板、批量操作等（按需）

### 12.4 风险控制

1. **向后兼容**：新 CMS 与现有 Page 系统并行，不影响现有功能
2. **渐进迁移**：逐步引导用户使用新 CMS 功能
3. **性能监控**：密切监控查询性能，及时优化慢查询
4. **数据备份**：实施完善的数据备份和恢复策略

---

*本文档将随着实现进度持续更新，确保设计与实现的一致性。*