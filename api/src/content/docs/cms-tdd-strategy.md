# 通用CMS系统测试驱动开发(TDD)策略文档

> 版本：v1.0  
> 最后更新：2025-01-16  
> 状态：实施阶段  
> 技术栈：NestJS + Prisma + Jest + PostgreSQL

---

## 1. 文档概述

### 1.1 目标
本文档为通用CMS系统的测试驱动开发提供完整的实施策略，确保：
- **高质量代码**：通过测试先行保证代码质量和可维护性
- **快速迭代**：建立快速反馈循环，支持敏捷开发
- **重构安全**：完整的测试覆盖为代码重构提供安全网
- **文档价值**：测试用例作为活文档，描述系统行为

### 1.2 适用范围
- CMS核心功能模块（ContentNode、ContentRevision、Category、Tag等）
- API接口层（Controller）
- 业务逻辑层（Service）
- 数据访问层（Repository/Prisma）
- 权限验证和安全机制

---

## 2. TDD实施策略概述

### 2.1 核心原则

#### 2.1.1 Red-Green-Refactor循环
```mermaid
graph LR
    A[🔴 Red<br/>编写失败测试] --> B[🟢 Green<br/>实现最小代码]
    B --> C[🔵 Refactor<br/>重构优化]
    C --> A
```

**实施步骤**：
1. **Red阶段**：编写一个失败的测试用例，明确期望行为
2. **Green阶段**：编写最少的代码让测试通过，不考虑优化
3. **Refactor阶段**：在测试保护下重构代码，提升质量

#### 2.1.2 由外向内开发
```
API层 (Controller) → 业务层 (Service) → 数据层 (Repository)
```

**优势**：
- 从用户需求出发，确保API设计的合理性
- 逐层深入，避免过度设计
- 保持接口稳定性

#### 2.1.3 Mock优先策略
- **隔离测试**：使用Mock隔离外部依赖
- **快速执行**：避免真实数据库操作，提升测试速度
- **专注逻辑**：专注于业务逻辑验证，而非基础设施

### 2.2 开发流程
1. **需求分析** → 确定API接口和行为规范
2. **编写测试** → 基于需求编写测试用例
3. **实现代码** → 编写最小可行代码
4. **验证测试** → 确保测试通过
5. **重构优化** → 在测试保护下优化代码
6. **集成验证** → 运行完整测试套件

---

## 3. 测试层次设计

### 3.1 测试金字塔结构

```
        /\
       /  \
      / E2E \ (20%)
     /______\
    /        \
   /Integration\ (30%)
  /__________\
 /            \
/  Unit Tests  \ (50%)
\______________/
```

### 3.2 单元测试层 (Unit Tests)

#### 3.2.1 覆盖范围
- **Service层业务逻辑**：ContentService、CategoryService、TagService
- **DTO验证逻辑**：输入参数验证、数据转换
- **工具函数**：Slug生成、权限检查、数据格式化
- **业务规则**：版本管理、状态转换、权限验证

#### 3.2.2 Mock策略
```typescript
// PrismaService Mock
const mockPrismaService = {
  contentNode: {
    create: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
  contentRevision: {
    create: jest.fn(),
    findMany: jest.fn(),
  },
  $transaction: jest.fn(),
};

// CacheService Mock
const mockCacheService = {
  get: jest.fn(),
  set: jest.fn(),
  del: jest.fn(),
  invalidate: jest.fn(),
};

// EventEmitter Mock
const mockEventEmitter = {
  emit: jest.fn(),
  on: jest.fn(),
};
```

#### 3.2.3 测试配置
```typescript
// content.service.spec.ts
describe('ContentService', () => {
  let service: ContentService;
  let prisma: jest.Mocked<PrismaService>;
  let cache: jest.Mocked<CacheService>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ContentService,
        { provide: PrismaService, useValue: mockPrismaService },
        { provide: CacheService, useValue: mockCacheService },
        { provide: EventEmitter2, useValue: mockEventEmitter },
      ],
    }).compile();

    service = module.get<ContentService>(ContentService);
    prisma = module.get(PrismaService);
    cache = module.get(CacheService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });
});
```

### 3.3 集成测试层 (Integration Tests)

#### 3.3.1 覆盖范围
- **Controller + Service集成**：完整的请求-响应流程
- **Service + Database集成**：真实数据库操作验证
- **权限守卫集成**：认证和授权流程
- **事务处理**：数据一致性验证

#### 3.3.2 测试数据库配置
```typescript
// test-database.config.ts
export const testDatabaseConfig = {
  url: process.env.TEST_DATABASE_URL || 'postgresql://test:test@localhost:5433/cms_test',
  schema: 'public',
  logging: false,
};

// 测试前清理数据库
beforeEach(async () => {
  await prisma.contentAuditLog.deleteMany();
  await prisma.contentNodeTag.deleteMany();
  await prisma.contentRevision.deleteMany();
  await prisma.contentNode.deleteMany();
  await prisma.tag.deleteMany();
  await prisma.category.deleteMany();
});
```

### 3.4 E2E测试层 (End-to-End Tests)

#### 3.4.1 覆盖范围
- **完整业务流程**：内容生命周期管理
- **用户场景**：多角色权限验证
- **API集成**：跨模块功能验证
- **性能验证**：响应时间和并发处理

#### 3.4.2 测试环境配置
```typescript
// e2e-setup.ts
export const e2eTestConfig = {
  app: null as INestApplication,
  prisma: null as PrismaService,
  
  async setup() {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    })
    .overrideProvider(PrismaService)
    .useValue(testPrismaService)
    .compile();

    this.app = moduleFixture.createNestApplication();
    this.prisma = moduleFixture.get<PrismaService>(PrismaService);
    
    await this.app.init();
  },

  async teardown() {
    await this.app.close();
  }
};
```

---

## 4. CMS核心功能测试用例设计

### 4.1 ContentService测试用例

#### 4.1.1 内容创建测试
```typescript
describe('ContentService.createContent', () => {
  const createContentDto: CreateContentDto = {
    type: ContentType.BLOG,
    title: '测试博客文章',
    slug: 'test-blog-post',
    excerpt: '这是一篇测试博客文章的摘要',
    content: {
      blocks: [
        { type: 'paragraph', data: { text: '文章内容...' } }
      ]
    },
    authorInfo: {
      name: '张三',
      avatar: '/avatars/zhangsan.jpg',
      bio: '资深前端开发工程师'
    },
    categoryId: 'category-1',
    tagIds: ['tag-1', 'tag-2']
  };

  it('应该成功创建博客内容', async () => {
    // Given: Mock数据准备
    const mockUser = MockDataFactory.createUser();
    const mockWebsite = MockDataFactory.createWebsite();
    const mockCreatedContent = MockDataFactory.createContentNode({
      title: createContentDto.title,
      type: createContentDto.type
    });

    prisma.website.findUnique.mockResolvedValue(mockWebsite);
    prisma.user.findUnique.mockResolvedValue(mockUser);
    prisma.contentNode.findFirst.mockResolvedValue(null); // slug不重复
    prisma.$transaction.mockImplementation(async (callback) => {
      return callback(prisma);
    });
    prisma.contentNode.create.mockResolvedValue(mockCreatedContent);

    // When: 调用创建方法
    const result = await service.createContent(
      createContentDto, 
      'user-1', 
      'website-1'
    );

    // Then: 验证结果
    expect(result).toMatchObject({
      title: '测试博客文章',
      type: ContentType.BLOG,
      status: ContentStatus.DRAFT,
      slug: 'test-blog-post'
    });

    // 验证数据库调用
    expect(prisma.contentNode.create).toHaveBeenCalledWith({
      data: expect.objectContaining({
        title: '测试博客文章',
        type: ContentType.BLOG,
        userId: 'user-1',
        websiteId: 'website-1',
        workspaceId: mockWebsite.workspaceId,
        authorInfo: createContentDto.authorInfo,
        status: ContentStatus.DRAFT
      })
    });

    // 验证审计日志
    expect(prisma.contentAuditLog.create).toHaveBeenCalledWith({
      data: expect.objectContaining({
        action: ContentAuditAction.CREATED,
        userId: 'user-1'
      })
    });
  });

  it('应该在slug重复时抛出错误', async () => {
    // Given: 已存在相同slug的内容
    const existingContent = MockDataFactory.createContentNode({
      slug: 'test-blog-post'
    });
    prisma.contentNode.findFirst.mockResolvedValue(existingContent);

    // When & Then: 验证错误抛出
    await expect(
      service.createContent(createContentDto, 'user-1', 'website-1')
    ).rejects.toThrow('Content with this slug already exists');

    expect(prisma.contentNode.create).not.toHaveBeenCalled();
  });

  it('应该在权限不足时抛出错误', async () => {
    // Given: 用户不是工作空间成员
    const mockWebsite = MockDataFactory.createWebsite();
    prisma.website.findUnique.mockResolvedValue(mockWebsite);
    
    // Mock权限检查失败
    jest.spyOn(service as any, 'checkWorkspacePermission')
        .mockResolvedValue(false);

    // When & Then: 验证权限错误
    await expect(
      service.createContent(createContentDto, 'user-1', 'website-1')
    ).rejects.toThrow('Insufficient permissions');
  });

  it('应该在配额超限时抛出错误', async () => {
    // Given: 配额已满
    const mockWebsite = MockDataFactory.createWebsite();
    const mockQuota = {
      contentsCreated: 100,
      contentCreationLimit: 100
    };
    
    prisma.website.findUnique.mockResolvedValue(mockWebsite);
    prisma.quota.findUnique.mockResolvedValue(mockQuota);

    // When & Then: 验证配额错误
    await expect(
      service.createContent(createContentDto, 'user-1', 'website-1')
    ).rejects.toThrow('Content creation limit exceeded');
  });
});
```

#### 4.1.2 内容发布测试
```typescript
describe('ContentService.publishContent', () => {
  it('应该成功发布草稿内容', async () => {
    // Given: 草稿状态的内容
    const mockContent = MockDataFactory.createContentNode({
      status: ContentStatus.DRAFT,
      currentRevisionId: 'revision-1'
    });
    const mockRevision = {
      id: 'revision-1',
      isPublished: false,
      version: 1
    };

    prisma.contentNode.findUnique.mockResolvedValue({
      ...mockContent,
      currentRevision: mockRevision,
      workspace: { members: [{ userId: 'user-1' }] }
    });

    prisma.$transaction.mockImplementation(async (callback) => {
      return callback(prisma);
    });

    // When: 发布内容
    const result = await service.publishContent('content-1', 'user-1');

    // Then: 验证状态更新
    expect(prisma.contentNode.update).toHaveBeenCalledWith({
      where: { id: 'content-1' },
      data: {
        status: ContentStatus.PUBLISHED,
        publishedAt: expect.any(Date)
      }
    });

    expect(prisma.contentRevision.update).toHaveBeenCalledWith({
      where: { id: 'revision-1' },
      data: {
        isPublished: true,
        publishedAt: expect.any(Date)
      }
    });

    // 验证审计日志
    expect(prisma.contentAuditLog.create).toHaveBeenCalledWith({
      data: expect.objectContaining({
        action: ContentAuditAction.PUBLISHED,
        userId: 'user-1',
        revisionId: 'revision-1'
      })
    });
  });

  it('应该在内容已发布时抛出错误', async () => {
    // Given: 已发布的内容
    const mockContent = MockDataFactory.createContentNode({
      status: ContentStatus.PUBLISHED
    });
    prisma.contentNode.findUnique.mockResolvedValue(mockContent);

    // When & Then: 验证错误处理
    await expect(
      service.publishContent('content-1', 'user-1')
    ).rejects.toThrow('Content is already published');

    expect(prisma.contentNode.update).not.toHaveBeenCalled();
  });

  it('应该在没有当前版本时抛出错误', async () => {
    // Given: 没有当前版本的内容
    const mockContent = MockDataFactory.createContentNode({
      status: ContentStatus.DRAFT,
      currentRevisionId: null,
      currentRevision: null
    });
    prisma.contentNode.findUnique.mockResolvedValue(mockContent);

    // When & Then: 验证错误处理
    await expect(
      service.publishContent('content-1', 'user-1')
    ).rejects.toThrow('No revision to publish');
  });
});
```

#### 4.1.3 版本管理测试
```typescript
describe('ContentService.createRevision', () => {
  it('应该成功创建新版本', async () => {
    // Given: 现有内容和版本
    const mockContent = MockDataFactory.createContentNode();
    const existingRevisions = [
      { version: 1 }, { version: 2 }
    ];
    const newRevisionData = {
      content: { blocks: [{ type: 'paragraph', data: { text: '新版本内容' } }] },
      changelog: '更新了文章内容'
    };

    prisma.contentNode.findUnique.mockResolvedValue(mockContent);
    prisma.contentRevision.findMany.mockResolvedValue(existingRevisions);
    prisma.$transaction.mockImplementation(async (callback) => {
      return callback(prisma);
    });

    // When: 创建新版本
    const result = await service.createRevision(
      'content-1',
      newRevisionData,
      'user-1'
    );

    // Then: 验证新版本创建
    expect(prisma.contentRevision.create).toHaveBeenCalledWith({
      data: expect.objectContaining({
        nodeId: 'content-1',
        createdById: 'user-1',
        version: 3, // 下一个版本号
        data: newRevisionData.content,
        changelog: newRevisionData.changelog,
        isPublished: false
      })
    });

    // 验证审计日志
    expect(prisma.contentAuditLog.create).toHaveBeenCalledWith({
      data: expect.objectContaining({
        action: ContentAuditAction.REVISION_CREATED,
        userId: 'user-1'
      })
    });
  });
});
```

### 4.2 Controller层测试用例

```typescript
describe('ContentController', () => {
  let app: INestApplication;
  let contentService: jest.Mocked<ContentService>;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      controllers: [ContentController],
      providers: [
        { provide: ContentService, useValue: createMockContentService() },
        { provide: JwtAuthGuard, useValue: { canActivate: jest.fn(() => true) } },
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    contentService = moduleFixture.get(ContentService);
    await app.init();
  });

  describe('POST /api/v1/contents', () => {
    const createContentDto = {
      type: 'BLOG',
      title: '测试内容',
      slug: 'test-content',
      excerpt: '测试摘要',
      content: { blocks: [] },
      authorInfo: { name: '测试作者' }
    };

    it('应该成功创建内容并返回201状态码', async () => {
      // Given: Mock Service返回
      const mockCreatedContent = MockDataFactory.createContentNode({
        title: '测试内容'
      });
      contentService.createContent.mockResolvedValue(mockCreatedContent);

      // When: 发送POST请求
      const response = await request(app.getHttpServer())
        .post('/api/v1/contents')
        .set('Authorization', 'Bearer valid-token')
        .send(createContentDto);

      // Then: 验证响应
      expect(response.status).toBe(201);
      expect(response.body).toMatchObject({
        success: true,
        data: expect.objectContaining({
          title: '测试内容',
          type: 'BLOG'
        }),
        meta: expect.objectContaining({
          timestamp: expect.any(String),
          version: 'v1'
        })
      });

      // 验证Service调用
      expect(contentService.createContent).toHaveBeenCalledWith(
        createContentDto,
        expect.any(String), // userId
        expect.any(String)  // websiteId
      );
    });

    it('应该在验证失败时返回400状态码', async () => {
      // Given: 无效的请求数据
      const invalidDto = { ...createContentDto, title: '' }; // 空标题

      // When: 发送无效请求
      const response = await request(app.getHttpServer())
        .post('/api/v1/contents')
        .set('Authorization', 'Bearer valid-token')
        .send(invalidDto);

      // Then: 验证错误响应
      expect(response.status).toBe(400);
      expect(response.body).toMatchObject({
        success: false,
        error: expect.objectContaining({
          code: expect.stringMatching(/VALIDATION_ERROR/),
          message: expect.stringContaining('title')
        })
      });
    });

    it('应该在权限不足时返回403状态码', async () => {
      // Given: 权限检查失败
      contentService.createContent.mockRejectedValue(
        new ForbiddenException('Insufficient permissions')
      );

      // When: 发送请求
      const response = await request(app.getHttpServer())
        .post('/api/v1/contents')
        .set('Authorization', 'Bearer valid-token')
        .send(createContentDto);

      // Then: 验证权限错误
      expect(response.status).toBe(403);
      expect(response.body).toMatchObject({
        success: false,
        error: expect.objectContaining({
          code: expect.stringMatching(/ACCESS_DENIED/),
          message: 'Insufficient permissions'
        })
      });
    });
  });

  describe('GET /api/v1/contents/:id', () => {
    it('应该成功获取内容详情', async () => {
      // Given: Mock内容数据
      const mockContent = MockDataFactory.createContentNode({
        id: 'content-1',
        title: '测试内容'
      });
      contentService.getContent.mockResolvedValue(mockContent);

      // When: 发送GET请求
      const response = await request(app.getHttpServer())
        .get('/api/v1/contents/content-1')
        .set('Authorization', 'Bearer valid-token');

      // Then: 验证响应
      expect(response.status).toBe(200);
      expect(response.body.data).toMatchObject({
        id: 'content-1',
        title: '测试内容'
      });
    });

    it('应该在内容不存在时返回404状态码', async () => {
      // Given: 内容不存在
      contentService.getContent.mockRejectedValue(
        new NotFoundException('Content not found')
      );

      // When: 发送GET请求
      const response = await request(app.getHttpServer())
        .get('/api/v1/contents/non-existent')
        .set('Authorization', 'Bearer valid-token');

      // Then: 验证404响应
      expect(response.status).toBe(404);
      expect(response.body.error.code).toMatch(/NOT_FOUND/);
    });
  });

  describe('PUT /api/v1/contents/:id/publish', () => {
    it('应该成功发布内容', async () => {
      // Given: Mock发布结果
      const mockPublishedContent = MockDataFactory.createContentNode({
        status: ContentStatus.PUBLISHED,
        publishedAt: new Date()
      });
      contentService.publishContent.mockResolvedValue(mockPublishedContent);

      // When: 发送发布请求
      const response = await request(app.getHttpServer())
        .put('/api/v1/contents/content-1/publish')
        .set('Authorization', 'Bearer valid-token');

      // Then: 验证响应
      expect(response.status).toBe(200);
      expect(response.body.data.status).toBe('PUBLISHED');
      expect(response.body.data.publishedAt).toBeDefined();
    });
  });
});
```
