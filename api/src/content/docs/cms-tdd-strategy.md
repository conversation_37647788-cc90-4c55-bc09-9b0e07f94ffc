# 通用CMS系统测试驱动开发(TDD)策略文档

> 版本：v1.0  
> 最后更新：2025-01-16  
> 状态：实施阶段  
> 技术栈：NestJS + Prisma + Jest + PostgreSQL

---

## 1. 文档概述

### 1.1 目标
本文档为通用CMS系统的测试驱动开发提供完整的实施策略，确保：
- **高质量代码**：通过测试先行保证代码质量和可维护性
- **快速迭代**：建立快速反馈循环，支持敏捷开发
- **重构安全**：完整的测试覆盖为代码重构提供安全网
- **文档价值**：测试用例作为活文档，描述系统行为

### 1.2 适用范围
- CMS核心功能模块（ContentNode、ContentRevision、Category、Tag等）
- API接口层（Controller）
- 业务逻辑层（Service）
- 数据访问层（Repository/Prisma）
- 权限验证和安全机制

---

## 2. TDD实施策略概述

### 2.1 核心原则

#### 2.1.1 Red-Green-Refactor循环
```mermaid
graph LR
    A[🔴 Red<br/>编写失败测试] --> B[🟢 Green<br/>实现最小代码]
    B --> C[🔵 Refactor<br/>重构优化]
    C --> A
```

**实施步骤**：
1. **Red阶段**：编写一个失败的测试用例，明确期望行为
2. **Green阶段**：编写最少的代码让测试通过，不考虑优化
3. **Refactor阶段**：在测试保护下重构代码，提升质量

#### 2.1.2 由外向内开发
```
API层 (Controller) → 业务层 (Service) → 数据层 (Repository)
```

**优势**：
- 从用户需求出发，确保API设计的合理性
- 逐层深入，避免过度设计
- 保持接口稳定性

#### 2.1.3 Mock优先策略
- **隔离测试**：使用Mock隔离外部依赖
- **快速执行**：避免真实数据库操作，提升测试速度
- **专注逻辑**：专注于业务逻辑验证，而非基础设施

### 2.2 开发流程
1. **需求分析** → 确定API接口和行为规范
2. **编写测试** → 基于需求编写测试用例
3. **实现代码** → 编写最小可行代码
4. **验证测试** → 确保测试通过
5. **重构优化** → 在测试保护下优化代码
6. **集成验证** → 运行完整测试套件

---

## 3. 测试层次设计

### 3.1 测试金字塔结构

```
        /\
       /  \
      / E2E \ (20%)
     /______\
    /        \
   /Integration\ (30%)
  /__________\
 /            \
/  Unit Tests  \ (50%)
\______________/
```

### 3.2 单元测试层 (Unit Tests)

#### 3.2.1 覆盖范围
- **Service层业务逻辑**：ContentService、CategoryService、TagService
- **DTO验证逻辑**：输入参数验证、数据转换
- **工具函数**：Slug生成、权限检查、数据格式化
- **业务规则**：版本管理、状态转换、权限验证

#### 3.2.2 Mock策略
```typescript
// PrismaService Mock
const mockPrismaService = {
  contentNode: {
    create: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
  contentRevision: {
    create: jest.fn(),
    findMany: jest.fn(),
  },
  $transaction: jest.fn(),
};

// CacheService Mock
const mockCacheService = {
  get: jest.fn(),
  set: jest.fn(),
  del: jest.fn(),
  invalidate: jest.fn(),
};

// EventEmitter Mock
const mockEventEmitter = {
  emit: jest.fn(),
  on: jest.fn(),
};
```

#### 3.2.3 测试配置
```typescript
// content.service.spec.ts
describe('ContentService', () => {
  let service: ContentService;
  let prisma: jest.Mocked<PrismaService>;
  let cache: jest.Mocked<CacheService>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ContentService,
        { provide: PrismaService, useValue: mockPrismaService },
        { provide: CacheService, useValue: mockCacheService },
        { provide: EventEmitter2, useValue: mockEventEmitter },
      ],
    }).compile();

    service = module.get<ContentService>(ContentService);
    prisma = module.get(PrismaService);
    cache = module.get(CacheService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });
});
```

### 3.3 集成测试层 (Integration Tests)

#### 3.3.1 覆盖范围
- **Controller + Service集成**：完整的请求-响应流程
- **Service + Database集成**：真实数据库操作验证
- **权限守卫集成**：认证和授权流程
- **事务处理**：数据一致性验证

#### 3.3.2 测试数据库配置
```typescript
// test-database.config.ts
export const testDatabaseConfig = {
  url: process.env.TEST_DATABASE_URL || 'postgresql://test:test@localhost:5433/cms_test',
  schema: 'public',
  logging: false,
};

// 测试前清理数据库
beforeEach(async () => {
  await prisma.contentAuditLog.deleteMany();
  await prisma.contentNodeTag.deleteMany();
  await prisma.contentRevision.deleteMany();
  await prisma.contentNode.deleteMany();
  await prisma.tag.deleteMany();
  await prisma.category.deleteMany();
});
```

### 3.4 E2E测试层 (End-to-End Tests)

#### 3.4.1 覆盖范围
- **完整业务流程**：内容生命周期管理
- **用户场景**：多角色权限验证
- **API集成**：跨模块功能验证
- **性能验证**：响应时间和并发处理

#### 3.4.2 测试环境配置
```typescript
// e2e-setup.ts
export const e2eTestConfig = {
  app: null as INestApplication,
  prisma: null as PrismaService,
  
  async setup() {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    })
    .overrideProvider(PrismaService)
    .useValue(testPrismaService)
    .compile();

    this.app = moduleFixture.createNestApplication();
    this.prisma = moduleFixture.get<PrismaService>(PrismaService);
    
    await this.app.init();
  },

  async teardown() {
    await this.app.close();
  }
};
```

---

## 4. CMS核心功能测试用例设计

### 4.1 ContentService测试用例

#### 4.1.1 内容创建测试
```typescript
describe('ContentService.createContent', () => {
  const createContentDto: CreateContentDto = {
    type: ContentType.BLOG,
    title: '测试博客文章',
    slug: 'test-blog-post',
    excerpt: '这是一篇测试博客文章的摘要',
    content: {
      blocks: [
        { type: 'paragraph', data: { text: '文章内容...' } }
      ]
    },
    authorInfo: {
      name: '张三',
      avatar: '/avatars/zhangsan.jpg',
      bio: '资深前端开发工程师'
    },
    categoryId: 'category-1',
    tagIds: ['tag-1', 'tag-2']
  };

  it('应该成功创建博客内容', async () => {
    // Given: Mock数据准备
    const mockUser = MockDataFactory.createUser();
    const mockWebsite = MockDataFactory.createWebsite();
    const mockCreatedContent = MockDataFactory.createContentNode({
      title: createContentDto.title,
      type: createContentDto.type
    });

    prisma.website.findUnique.mockResolvedValue(mockWebsite);
    prisma.user.findUnique.mockResolvedValue(mockUser);
    prisma.contentNode.findFirst.mockResolvedValue(null); // slug不重复
    prisma.$transaction.mockImplementation(async (callback) => {
      return callback(prisma);
    });
    prisma.contentNode.create.mockResolvedValue(mockCreatedContent);

    // When: 调用创建方法
    const result = await service.createContent(
      createContentDto, 
      'user-1', 
      'website-1'
    );

    // Then: 验证结果
    expect(result).toMatchObject({
      title: '测试博客文章',
      type: ContentType.BLOG,
      status: ContentStatus.DRAFT,
      slug: 'test-blog-post'
    });

    // 验证数据库调用
    expect(prisma.contentNode.create).toHaveBeenCalledWith({
      data: expect.objectContaining({
        title: '测试博客文章',
        type: ContentType.BLOG,
        userId: 'user-1',
        websiteId: 'website-1',
        workspaceId: mockWebsite.workspaceId,
        authorInfo: createContentDto.authorInfo,
        status: ContentStatus.DRAFT
      })
    });

    // 验证审计日志
    expect(prisma.contentAuditLog.create).toHaveBeenCalledWith({
      data: expect.objectContaining({
        action: ContentAuditAction.CREATED,
        userId: 'user-1'
      })
    });
  });

  it('应该在slug重复时抛出错误', async () => {
    // Given: 已存在相同slug的内容
    const existingContent = MockDataFactory.createContentNode({
      slug: 'test-blog-post'
    });
    prisma.contentNode.findFirst.mockResolvedValue(existingContent);

    // When & Then: 验证错误抛出
    await expect(
      service.createContent(createContentDto, 'user-1', 'website-1')
    ).rejects.toThrow('Content with this slug already exists');

    expect(prisma.contentNode.create).not.toHaveBeenCalled();
  });

  it('应该在权限不足时抛出错误', async () => {
    // Given: 用户不是工作空间成员
    const mockWebsite = MockDataFactory.createWebsite();
    prisma.website.findUnique.mockResolvedValue(mockWebsite);
    
    // Mock权限检查失败
    jest.spyOn(service as any, 'checkWorkspacePermission')
        .mockResolvedValue(false);

    // When & Then: 验证权限错误
    await expect(
      service.createContent(createContentDto, 'user-1', 'website-1')
    ).rejects.toThrow('Insufficient permissions');
  });

  it('应该在配额超限时抛出错误', async () => {
    // Given: 配额已满
    const mockWebsite = MockDataFactory.createWebsite();
    const mockQuota = {
      contentsCreated: 100,
      contentCreationLimit: 100
    };
    
    prisma.website.findUnique.mockResolvedValue(mockWebsite);
    prisma.quota.findUnique.mockResolvedValue(mockQuota);

    // When & Then: 验证配额错误
    await expect(
      service.createContent(createContentDto, 'user-1', 'website-1')
    ).rejects.toThrow('Content creation limit exceeded');
  });
});
```

#### 4.1.2 内容发布测试
```typescript
describe('ContentService.publishContent', () => {
  it('应该成功发布草稿内容', async () => {
    // Given: 草稿状态的内容
    const mockContent = MockDataFactory.createContentNode({
      status: ContentStatus.DRAFT,
      currentRevisionId: 'revision-1'
    });
    const mockRevision = {
      id: 'revision-1',
      isPublished: false,
      version: 1
    };

    prisma.contentNode.findUnique.mockResolvedValue({
      ...mockContent,
      currentRevision: mockRevision,
      workspace: { members: [{ userId: 'user-1' }] }
    });

    prisma.$transaction.mockImplementation(async (callback) => {
      return callback(prisma);
    });

    // When: 发布内容
    const result = await service.publishContent('content-1', 'user-1');

    // Then: 验证状态更新
    expect(prisma.contentNode.update).toHaveBeenCalledWith({
      where: { id: 'content-1' },
      data: {
        status: ContentStatus.PUBLISHED,
        publishedAt: expect.any(Date)
      }
    });

    expect(prisma.contentRevision.update).toHaveBeenCalledWith({
      where: { id: 'revision-1' },
      data: {
        isPublished: true,
        publishedAt: expect.any(Date)
      }
    });

    // 验证审计日志
    expect(prisma.contentAuditLog.create).toHaveBeenCalledWith({
      data: expect.objectContaining({
        action: ContentAuditAction.PUBLISHED,
        userId: 'user-1',
        revisionId: 'revision-1'
      })
    });
  });

  it('应该在内容已发布时抛出错误', async () => {
    // Given: 已发布的内容
    const mockContent = MockDataFactory.createContentNode({
      status: ContentStatus.PUBLISHED
    });
    prisma.contentNode.findUnique.mockResolvedValue(mockContent);

    // When & Then: 验证错误处理
    await expect(
      service.publishContent('content-1', 'user-1')
    ).rejects.toThrow('Content is already published');

    expect(prisma.contentNode.update).not.toHaveBeenCalled();
  });

  it('应该在没有当前版本时抛出错误', async () => {
    // Given: 没有当前版本的内容
    const mockContent = MockDataFactory.createContentNode({
      status: ContentStatus.DRAFT,
      currentRevisionId: null,
      currentRevision: null
    });
    prisma.contentNode.findUnique.mockResolvedValue(mockContent);

    // When & Then: 验证错误处理
    await expect(
      service.publishContent('content-1', 'user-1')
    ).rejects.toThrow('No revision to publish');
  });
});
```

#### 4.1.3 版本管理测试
```typescript
describe('ContentService.createRevision', () => {
  it('应该成功创建新版本', async () => {
    // Given: 现有内容和版本
    const mockContent = MockDataFactory.createContentNode();
    const existingRevisions = [
      { version: 1 }, { version: 2 }
    ];
    const newRevisionData = {
      content: { blocks: [{ type: 'paragraph', data: { text: '新版本内容' } }] },
      changelog: '更新了文章内容'
    };

    prisma.contentNode.findUnique.mockResolvedValue(mockContent);
    prisma.contentRevision.findMany.mockResolvedValue(existingRevisions);
    prisma.$transaction.mockImplementation(async (callback) => {
      return callback(prisma);
    });

    // When: 创建新版本
    const result = await service.createRevision(
      'content-1',
      newRevisionData,
      'user-1'
    );

    // Then: 验证新版本创建
    expect(prisma.contentRevision.create).toHaveBeenCalledWith({
      data: expect.objectContaining({
        nodeId: 'content-1',
        createdById: 'user-1',
        version: 3, // 下一个版本号
        data: newRevisionData.content,
        changelog: newRevisionData.changelog,
        isPublished: false
      })
    });

    // 验证审计日志
    expect(prisma.contentAuditLog.create).toHaveBeenCalledWith({
      data: expect.objectContaining({
        action: ContentAuditAction.REVISION_CREATED,
        userId: 'user-1'
      })
    });
  });
});
```

### 4.2 Controller层测试用例

```typescript
describe('ContentController', () => {
  let app: INestApplication;
  let contentService: jest.Mocked<ContentService>;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      controllers: [ContentController],
      providers: [
        { provide: ContentService, useValue: createMockContentService() },
        { provide: JwtAuthGuard, useValue: { canActivate: jest.fn(() => true) } },
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    contentService = moduleFixture.get(ContentService);
    await app.init();
  });

  describe('POST /api/v1/contents', () => {
    const createContentDto = {
      type: 'BLOG',
      title: '测试内容',
      slug: 'test-content',
      excerpt: '测试摘要',
      content: { blocks: [] },
      authorInfo: { name: '测试作者' }
    };

    it('应该成功创建内容并返回201状态码', async () => {
      // Given: Mock Service返回
      const mockCreatedContent = MockDataFactory.createContentNode({
        title: '测试内容'
      });
      contentService.createContent.mockResolvedValue(mockCreatedContent);

      // When: 发送POST请求
      const response = await request(app.getHttpServer())
        .post('/api/v1/contents')
        .set('Authorization', 'Bearer valid-token')
        .send(createContentDto);

      // Then: 验证响应
      expect(response.status).toBe(201);
      expect(response.body).toMatchObject({
        success: true,
        data: expect.objectContaining({
          title: '测试内容',
          type: 'BLOG'
        }),
        meta: expect.objectContaining({
          timestamp: expect.any(String),
          version: 'v1'
        })
      });

      // 验证Service调用
      expect(contentService.createContent).toHaveBeenCalledWith(
        createContentDto,
        expect.any(String), // userId
        expect.any(String)  // websiteId
      );
    });

    it('应该在验证失败时返回400状态码', async () => {
      // Given: 无效的请求数据
      const invalidDto = { ...createContentDto, title: '' }; // 空标题

      // When: 发送无效请求
      const response = await request(app.getHttpServer())
        .post('/api/v1/contents')
        .set('Authorization', 'Bearer valid-token')
        .send(invalidDto);

      // Then: 验证错误响应
      expect(response.status).toBe(400);
      expect(response.body).toMatchObject({
        success: false,
        error: expect.objectContaining({
          code: expect.stringMatching(/VALIDATION_ERROR/),
          message: expect.stringContaining('title')
        })
      });
    });

    it('应该在权限不足时返回403状态码', async () => {
      // Given: 权限检查失败
      contentService.createContent.mockRejectedValue(
        new ForbiddenException('Insufficient permissions')
      );

      // When: 发送请求
      const response = await request(app.getHttpServer())
        .post('/api/v1/contents')
        .set('Authorization', 'Bearer valid-token')
        .send(createContentDto);

      // Then: 验证权限错误
      expect(response.status).toBe(403);
      expect(response.body).toMatchObject({
        success: false,
        error: expect.objectContaining({
          code: expect.stringMatching(/ACCESS_DENIED/),
          message: 'Insufficient permissions'
        })
      });
    });
  });

  describe('GET /api/v1/contents/:id', () => {
    it('应该成功获取内容详情', async () => {
      // Given: Mock内容数据
      const mockContent = MockDataFactory.createContentNode({
        id: 'content-1',
        title: '测试内容'
      });
      contentService.getContent.mockResolvedValue(mockContent);

      // When: 发送GET请求
      const response = await request(app.getHttpServer())
        .get('/api/v1/contents/content-1')
        .set('Authorization', 'Bearer valid-token');

      // Then: 验证响应
      expect(response.status).toBe(200);
      expect(response.body.data).toMatchObject({
        id: 'content-1',
        title: '测试内容'
      });
    });

    it('应该在内容不存在时返回404状态码', async () => {
      // Given: 内容不存在
      contentService.getContent.mockRejectedValue(
        new NotFoundException('Content not found')
      );

      // When: 发送GET请求
      const response = await request(app.getHttpServer())
        .get('/api/v1/contents/non-existent')
        .set('Authorization', 'Bearer valid-token');

      // Then: 验证404响应
      expect(response.status).toBe(404);
      expect(response.body.error.code).toMatch(/NOT_FOUND/);
    });
  });

  describe('PUT /api/v1/contents/:id/publish', () => {
    it('应该成功发布内容', async () => {
      // Given: Mock发布结果
      const mockPublishedContent = MockDataFactory.createContentNode({
        status: ContentStatus.PUBLISHED,
        publishedAt: new Date()
      });
      contentService.publishContent.mockResolvedValue(mockPublishedContent);

      // When: 发送发布请求
      const response = await request(app.getHttpServer())
        .put('/api/v1/contents/content-1/publish')
        .set('Authorization', 'Bearer valid-token');

      // Then: 验证响应
      expect(response.status).toBe(200);
      expect(response.body.data.status).toBe('PUBLISHED');
      expect(response.body.data.publishedAt).toBeDefined();
    });
  });
});
```

### 4.3 权限验证测试

```typescript
describe('ContentPermissionGuard', () => {
  let guard: ContentPermissionGuard;
  let workspaceService: jest.Mocked<WorkspaceService>;

  beforeEach(() => {
    const module = Test.createTestingModule({
      providers: [
        ContentPermissionGuard,
        { provide: WorkspaceService, useValue: createMockWorkspaceService() }
      ]
    }).compile();

    guard = module.get<ContentPermissionGuard>(ContentPermissionGuard);
    workspaceService = module.get(WorkspaceService);
  });

  it('应该允许工作空间成员访问内容', async () => {
    // Given: 用户是工作空间成员
    workspaceService.checkMemberPermission.mockResolvedValue(true);

    // When: 检查权限
    const hasPermission = await guard.canAccess('user-1', 'content-1', 'read');

    // Then: 验证权限通过
    expect(hasPermission).toBe(true);
    expect(workspaceService.checkMemberPermission).toHaveBeenCalledWith(
      'user-1',
      expect.any(String),
      'read'
    );
  });

  it('应该拒绝非工作空间成员访问内容', async () => {
    // Given: 用户不是工作空间成员
    workspaceService.checkMemberPermission.mockResolvedValue(false);

    // When & Then: 验证权限拒绝
    const hasPermission = await guard.canAccess('user-1', 'content-1', 'read');
    expect(hasPermission).toBe(false);
  });

  it('应该允许内容创建者编辑自己的内容', async () => {
    // Given: 用户是内容创建者
    const mockContent = MockDataFactory.createContentNode({
      userId: 'user-1'
    });

    // When: 检查编辑权限
    const hasPermission = await guard.canEdit('user-1', mockContent);

    // Then: 验证权限通过
    expect(hasPermission).toBe(true);
  });

  it('应该拒绝非创建者编辑他人内容', async () => {
    // Given: 用户不是内容创建者
    const mockContent = MockDataFactory.createContentNode({
      userId: 'other-user'
    });

    // When: 检查编辑权限
    const hasPermission = await guard.canEdit('user-1', mockContent);

    // Then: 验证权限拒绝
    expect(hasPermission).toBe(false);
  });
});
```

---

## 5. Mock数据设计策略

### 5.1 分层Mock设计

#### 5.1.1 数据工厂模式
```typescript
// test/factories/mock-data.factory.ts
export class MockDataFactory {
  static createUser(overrides: Partial<User> = {}): User {
    return {
      id: faker.string.uuid(),
      name: faker.person.fullName(),
      email: faker.internet.email(),
      image: faker.image.avatar(),
      subscriptionLevel: SubscriptionLevel.BASIC,
      subscriptionStatus: SubscriptionStatus.ACTIVE,
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides
    };
  }

  static createWorkspace(overrides: Partial<Workspace> = {}): Workspace {
    return {
      id: faker.string.uuid(),
      name: faker.company.name(),
      userId: faker.string.uuid(),
      description: faker.lorem.sentence(),
      isActive: true,
      visibility: WorkspaceVisibility.PRIVATE,
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides
    };
  }

  static createWebsite(overrides: Partial<Website> = {}): Website {
    return {
      id: faker.string.uuid(),
      name: faker.company.name(),
      domain: faker.internet.domainName(),
      defaultLanguage: Language.EN,
      workspaceId: faker.string.uuid(),
      userId: faker.string.uuid(),
      configuration: {},
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides
    };
  }

  static createContentNode(overrides: Partial<ContentNode> = {}): ContentNode {
    return {
      id: faker.string.uuid(),
      userId: faker.string.uuid(),
      workspaceId: faker.string.uuid(),
      websiteId: faker.string.uuid(),
      type: ContentType.BLOG,
      slug: faker.lorem.slug(),
      title: faker.lorem.sentence(),
      excerpt: faker.lorem.paragraph(),
      language: Language.EN,
      authorInfo: {
        name: faker.person.fullName(),
        avatar: faker.image.avatar(),
        bio: faker.lorem.sentence()
      },
      status: ContentStatus.DRAFT,
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides
    };
  }

  static createContentRevision(overrides: Partial<ContentRevision> = {}): ContentRevision {
    return {
      id: faker.string.uuid(),
      nodeId: faker.string.uuid(),
      createdById: faker.string.uuid(),
      version: faker.number.int({ min: 1, max: 10 }),
      data: {
        blocks: [
          {
            type: 'paragraph',
            data: { text: faker.lorem.paragraph() }
          }
        ]
      },
      changelog: faker.lorem.sentence(),
      isPublished: false,
      createdAt: new Date(),
      ...overrides
    };
  }

  static createCategory(overrides: Partial<Category> = {}): Category {
    return {
      id: faker.string.uuid(),
      workspaceId: faker.string.uuid(),
      websiteId: faker.string.uuid(),
      name: faker.lorem.word(),
      slug: faker.lorem.slug(),
      description: faker.lorem.sentence(),
      order: faker.number.int({ min: 0, max: 100 }),
      type: CategoryType.GENERAL,
      language: Language.EN,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides
    };
  }

  static createTag(overrides: Partial<Tag> = {}): Tag {
    return {
      id: faker.string.uuid(),
      workspaceId: faker.string.uuid(),
      websiteId: faker.string.uuid(),
      name: faker.lorem.word(),
      color: faker.internet.color(),
      description: faker.lorem.sentence(),
      language: Language.EN,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides
    };
  }
}
```

#### 5.1.2 Mock服务工厂
```typescript
// test/factories/mock-services.factory.ts
export function createMockContentService(): jest.Mocked<ContentService> {
  return {
    createContent: jest.fn(),
    getContent: jest.fn(),
    getContentList: jest.fn(),
    updateContent: jest.fn(),
    deleteContent: jest.fn(),
    publishContent: jest.fn(),
    unpublishContent: jest.fn(),
    archiveContent: jest.fn(),
    createRevision: jest.fn(),
    getRevisionHistory: jest.fn(),
    publishRevision: jest.fn(),
    revertToRevision: jest.fn(),
  } as jest.Mocked<ContentService>;
}

export function createMockCategoryService(): jest.Mocked<CategoryService> {
  return {
    createCategory: jest.fn(),
    getCategory: jest.fn(),
    getCategoryTree: jest.fn(),
    updateCategory: jest.fn(),
    deleteCategory: jest.fn(),
    moveCategory: jest.fn(),
  } as jest.Mocked<CategoryService>;
}

export function createMockTagService(): jest.Mocked<TagService> {
  return {
    createTag: jest.fn(),
    getTag: jest.fn(),
    getTagList: jest.fn(),
    updateTag: jest.fn(),
    deleteTag: jest.fn(),
    getTagUsage: jest.fn(),
  } as jest.Mocked<TagService>;
}

export function createMockPrismaService(): jest.Mocked<PrismaService> {
  return {
    contentNode: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    contentRevision: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    category: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    tag: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    contentNodeTag: {
      create: jest.fn(),
      createMany: jest.fn(),
      deleteMany: jest.fn(),
    },
    contentAuditLog: {
      create: jest.fn(),
      findMany: jest.fn(),
    },
    user: {
      findUnique: jest.fn(),
    },
    website: {
      findUnique: jest.fn(),
    },
    workspace: {
      findUnique: jest.fn(),
    },
    quota: {
      findUnique: jest.fn(),
      update: jest.fn(),
    },
    $transaction: jest.fn(),
  } as any;
}
```

### 5.2 场景化Mock数据

```typescript
// test/scenarios/content-scenarios.ts
export class ContentTestScenarios {
  // 内容生命周期场景
  static contentLifecycle = {
    draftContent: MockDataFactory.createContentNode({
      status: ContentStatus.DRAFT,
      publishedAt: null
    }),

    publishedContent: MockDataFactory.createContentNode({
      status: ContentStatus.PUBLISHED,
      publishedAt: new Date('2024-01-15T10:00:00Z')
    }),

    archivedContent: MockDataFactory.createContentNode({
      status: ContentStatus.ARCHIVED,
      publishedAt: new Date('2024-01-10T10:00:00Z')
    })
  };

  // 权限场景
  static permissions = {
    workspaceOwner: MockDataFactory.createUser({
      id: 'owner-user-id'
    }),

    workspaceMember: MockDataFactory.createUser({
      id: 'member-user-id'
    }),

    outsideUser: MockDataFactory.createUser({
      id: 'outside-user-id'
    }),

    contentCreator: MockDataFactory.createUser({
      id: 'creator-user-id'
    })
  };

  // 版本管理场景
  static versioning = {
    contentWithMultipleRevisions: {
      content: MockDataFactory.createContentNode({
        id: 'content-with-versions',
        currentRevisionId: 'revision-3'
      }),
      revisions: [
        MockDataFactory.createContentRevision({
          id: 'revision-1',
          nodeId: 'content-with-versions',
          version: 1,
          isPublished: false,
          changelog: '初始版本'
        }),
        MockDataFactory.createContentRevision({
          id: 'revision-2',
          nodeId: 'content-with-versions',
          version: 2,
          isPublished: true,
          changelog: '发布版本',
          publishedAt: new Date('2024-01-10T10:00:00Z')
        }),
        MockDataFactory.createContentRevision({
          id: 'revision-3',
          nodeId: 'content-with-versions',
          version: 3,
          isPublished: false,
          changelog: '修复错误'
        })
      ]
    }
  };

  // 分类和标签场景
  static categoriesAndTags = {
    categoryTree: [
      MockDataFactory.createCategory({
        id: 'cat-1',
        name: '技术',
        slug: 'tech',
        parentId: null,
        order: 1
      }),
      MockDataFactory.createCategory({
        id: 'cat-2',
        name: '前端开发',
        slug: 'frontend',
        parentId: 'cat-1',
        order: 1
      }),
      MockDataFactory.createCategory({
        id: 'cat-3',
        name: '后端开发',
        slug: 'backend',
        parentId: 'cat-1',
        order: 2
      })
    ],

    popularTags: [
      MockDataFactory.createTag({
        id: 'tag-1',
        name: 'React',
        color: '#61DAFB'
      }),
      MockDataFactory.createTag({
        id: 'tag-2',
        name: 'TypeScript',
        color: '#3178C6'
      }),
      MockDataFactory.createTag({
        id: 'tag-3',
        name: 'NestJS',
        color: '#E0234E'
      })
    ]
  };

  // 复杂业务场景
  static complexScenarios = {
    blogWithCategoryAndTags: {
      content: MockDataFactory.createContentNode({
        type: ContentType.BLOG,
        title: '深入理解React Hooks',
        categoryId: 'cat-2', // 前端开发
        status: ContentStatus.PUBLISHED
      }),
      category: MockDataFactory.createCategory({
        id: 'cat-2',
        name: '前端开发'
      }),
      tags: [
        MockDataFactory.createTag({ id: 'tag-1', name: 'React' }),
        MockDataFactory.createTag({ id: 'tag-2', name: 'TypeScript' })
      ],
      tagRelations: [
        { contentId: 'content-id', tagId: 'tag-1' },
        { contentId: 'content-id', tagId: 'tag-2' }
      ]
    }
  };
}
```

---

## 6. 分阶段实施计划

### 6.1 实施时间表

```mermaid
gantt
    title CMS TDD实施计划
    dateFormat  YYYY-MM-DD
    section 阶段1: 核心CRUD
    内容创建功能    :a1, 2024-01-16, 3d
    内容查询功能    :a2, after a1, 3d
    内容更新功能    :a3, after a2, 2d
    内容删除功能    :a4, after a3, 2d
    section 阶段2: 版本管理
    版本创建        :b1, after a4, 2d
    版本发布        :b2, after b1, 2d
    版本回滚        :b3, after b2, 2d
    section 阶段3: 分类标签
    分类管理        :c1, after b3, 3d
    标签管理        :c2, after c1, 2d
    section 阶段4: 高级功能
    权限验证        :d1, after c2, 3d
    审计日志        :d2, after d1, 2d
    性能优化        :d3, after d2, 2d
```

### 6.2 阶段1：核心CRUD功能 (10天)

#### 6.2.1 Week 1: 内容创建和查询 (5天)

**Day 1-3: 内容创建功能**
- [ ] 编写ContentService.createContent测试用例
- [ ] 实现内容创建的基础逻辑
- [ ] 编写Controller层创建接口测试
- [ ] 实现API接口和DTO验证
- [ ] 集成测试验证完整流程

**测试覆盖目标**：
- 成功创建各种类型内容
- Slug重复验证
- 权限检查
- 配额限制验证
- 数据验证错误处理

**Day 4-5: 内容查询功能**
- [ ] 编写内容详情查询测试
- [ ] 编写内容列表查询测试
- [ ] 实现查询逻辑和分页
- [ ] 实现过滤和排序功能
- [ ] 性能测试和缓存验证

#### 6.2.2 Week 2: 内容更新和删除 (5天)

**Day 6-8: 内容更新功能**
- [ ] 编写内容更新测试用例
- [ ] 实现部分更新逻辑
- [ ] 编写authorInfo更新测试
- [ ] 实现分类和标签关联更新
- [ ] 版本自动创建测试

**Day 9-10: 内容删除功能**
- [ ] 编写软删除测试用例
- [ ] 实现级联删除逻辑
- [ ] 编写权限验证测试
- [ ] 实现审计日志记录
- [ ] 数据一致性验证

### 6.3 阶段2：版本管理功能 (6天)

**Day 11-12: 版本创建**
- [ ] 编写版本创建测试用例
- [ ] 实现版本号自动递增
- [ ] 编写版本数据存储测试
- [ ] 实现变更日志功能

**Day 13-14: 版本发布**
- [ ] 编写版本发布测试用例
- [ ] 实现发布状态管理
- [ ] 编写当前版本指针更新测试
- [ ] 实现发布时间记录

**Day 15-16: 版本回滚**
- [ ] 编写版本回滚测试用例
- [ ] 实现回滚逻辑
- [ ] 编写回滚权限验证测试
- [ ] 实现回滚审计日志

### 6.4 阶段3：分类标签功能 (5天)

**Day 17-19: 分类管理**
- [ ] 编写分类CRUD测试用例
- [ ] 实现分类树形结构
- [ ] 编写层级关系验证测试
- [ ] 实现分类排序功能
- [ ] 编写分类内容关联测试

**Day 20-21: 标签管理**
- [ ] 编写标签CRUD测试用例
- [ ] 实现标签多对多关联
- [ ] 编写标签使用统计测试
- [ ] 实现标签颜色管理
- [ ] 编写标签搜索功能测试

### 6.5 阶段4：高级功能 (7天)

**Day 22-24: 权限验证**
- [ ] 编写权限守卫测试用例
- [ ] 实现基于角色的权限控制
- [ ] 编写跨工作空间权限隔离测试
- [ ] 实现内容创建者特殊权限
- [ ] 编写权限缓存测试

**Day 25-26: 审计日志**
- [ ] 编写审计日志记录测试
- [ ] 实现操作类型分类
- [ ] 编写日志查询和过滤测试
- [ ] 实现日志数据压缩
- [ ] 编写日志清理策略测试

**Day 27-28: 性能优化**
- [ ] 编写性能基准测试
- [ ] 实现查询优化
- [ ] 编写缓存策略测试
- [ ] 实现数据库索引优化
- [ ] 编写并发处理测试

---

## 7. 测试工具配置建议

### 7.1 Jest配置

```javascript
// jest.config.js
module.exports = {
  moduleFileExtensions: ['js', 'json', 'ts'],
  rootDir: 'src',
  testRegex: '.*\\.spec\\.ts$',
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  collectCoverageFrom: [
    '**/*.(t|j)s',
    '!**/*.spec.ts',
    '!**/*.interface.ts',
    '!**/*.dto.ts',
    '!**/node_modules/**',
  ],
  coverageDirectory: '../coverage',
  testEnvironment: 'node',
  setupFilesAfterEnv: ['<rootDir>/../test/setup.ts'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/$1',
    '^@test/(.*)$': '<rootDir>/../test/$1',
  },
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 90,
      lines: 90,
      statements: 90,
    },
    './src/content/': {
      branches: 90,
      functions: 95,
      lines: 95,
      statements: 95,
    },
  },
  testTimeout: 10000,
};
```

### 7.2 测试环境配置

```typescript
// test/setup.ts
import { ConfigService } from '@nestjs/config';

// 全局测试配置
global.testConfig = {
  database: {
    url: process.env.TEST_DATABASE_URL || 'postgresql://test:test@localhost:5433/cms_test',
    logging: false,
  },
  redis: {
    host: process.env.TEST_REDIS_HOST || 'localhost',
    port: parseInt(process.env.TEST_REDIS_PORT) || 6380,
    db: 1, // 使用独立的测试数据库
  },
  jwt: {
    secret: 'test-jwt-secret',
    expiresIn: '1h',
  },
  cache: {
    enabled: false, // 测试环境禁用缓存
  },
};

// Jest全局设置
beforeAll(async () => {
  // 设置测试数据库连接
  process.env.DATABASE_URL = global.testConfig.database.url;
  process.env.REDIS_URL = `redis://${global.testConfig.redis.host}:${global.testConfig.redis.port}/${global.testConfig.redis.db}`;
});

afterAll(async () => {
  // 清理测试环境
});

// 每个测试前的清理
beforeEach(async () => {
  // 清理Mock调用记录
  jest.clearAllMocks();
});
```

### 7.3 测试数据库配置

```typescript
// test/database/test-database.config.ts
import { PrismaClient } from '@prisma/client';

export class TestDatabaseManager {
  private static instance: TestDatabaseManager;
  private prisma: PrismaClient;

  private constructor() {
    this.prisma = new PrismaClient({
      datasources: {
        db: {
          url: global.testConfig.database.url,
        },
      },
      log: global.testConfig.database.logging ? ['query', 'info', 'warn', 'error'] : [],
    });
  }

  static getInstance(): TestDatabaseManager {
    if (!TestDatabaseManager.instance) {
      TestDatabaseManager.instance = new TestDatabaseManager();
    }
    return TestDatabaseManager.instance;
  }

  async cleanDatabase(): Promise<void> {
    // 按依赖关系顺序清理表
    const tables = [
      'ContentAuditLog',
      'ContentNodeTag',
      'ContentRevision',
      'ContentNode',
      'Tag',
      'Category',
    ];

    for (const table of tables) {
      await this.prisma.$executeRawUnsafe(`DELETE FROM "${table}";`);
    }
  }

  async seedTestData(): Promise<void> {
    // 创建测试基础数据
    const testUser = await this.prisma.user.create({
      data: MockDataFactory.createUser({
        id: 'test-user-id',
        email: '<EMAIL>'
      })
    });

    const testWorkspace = await this.prisma.workspace.create({
      data: MockDataFactory.createWorkspace({
        id: 'test-workspace-id',
        userId: testUser.id
      })
    });

    const testWebsite = await this.prisma.website.create({
      data: MockDataFactory.createWebsite({
        id: 'test-website-id',
        workspaceId: testWorkspace.id,
        userId: testUser.id
      })
    });
  }

  getPrismaClient(): PrismaClient {
    return this.prisma;
  }

  async disconnect(): Promise<void> {
    await this.prisma.$disconnect();
  }
}
```

### 7.4 E2E测试配置

```typescript
// test/e2e/e2e-setup.ts
import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { AppModule } from '@/app.module';
import { TestDatabaseManager } from '../database/test-database.config';

export class E2ETestSetup {
  private app: INestApplication;
  private dbManager: TestDatabaseManager;

  async setup(): Promise<INestApplication> {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    })
    .overrideProvider('DATABASE_URL')
    .useValue(global.testConfig.database.url)
    .compile();

    this.app = moduleFixture.createNestApplication();
    this.dbManager = TestDatabaseManager.getInstance();

    // 应用全局管道和过滤器
    this.app.useGlobalPipes(new ValidationPipe());
    this.app.useGlobalFilters(new HttpExceptionFilter());

    await this.app.init();
    return this.app;
  }

  async teardown(): Promise<void> {
    await this.dbManager.cleanDatabase();
    await this.app.close();
  }

  async resetDatabase(): Promise<void> {
    await this.dbManager.cleanDatabase();
    await this.dbManager.seedTestData();
  }
}
```

---

## 8. 测试覆盖率目标和最佳实践

### 8.1 覆盖率目标

#### 8.1.1 整体覆盖率目标
- **单元测试覆盖率**: ≥ 90%
- **集成测试覆盖率**: ≥ 80%
- **E2E测试覆盖率**: ≥ 70%
- **关键业务逻辑**: 100%覆盖

#### 8.1.2 模块级覆盖率目标

| 模块 | 单元测试 | 集成测试 | E2E测试 | 说明 |
|------|----------|----------|---------|------|
| ContentService | 95% | 90% | 80% | 核心业务逻辑 |
| ContentController | 90% | 85% | 75% | API接口层 |
| CategoryService | 90% | 85% | 70% | 分类管理 |
| TagService | 90% | 85% | 70% | 标签管理 |
| PermissionGuard | 100% | 95% | 80% | 安全关键 |
| AuditService | 85% | 80% | 60% | 审计日志 |

#### 8.1.3 关键路径100%覆盖
- 内容创建和发布流程
- 权限验证逻辑
- 版本管理操作
- 数据验证和错误处理
- 事务处理逻辑

### 8.2 TDD最佳实践

#### 8.2.1 测试编写原则

**1. 测试先行 (Test First)**
```typescript
// ❌ 错误：先写实现再写测试
class ContentService {
  async createContent(data: CreateContentDto) {
    // 实现逻辑...
  }
}

// ✅ 正确：先写测试再写实现
describe('ContentService.createContent', () => {
  it('应该成功创建内容', async () => {
    // 测试用例...
  });
});
```

**2. 小步迭代 (Small Steps)**
```typescript
// ✅ 每次只让一个测试通过
it('应该创建内容节点', async () => {
  const result = await service.createContent(validDto, 'user-1', 'website-1');
  expect(result).toBeDefined();
  expect(result.id).toBeDefined();
});

it('应该设置正确的内容状态', async () => {
  const result = await service.createContent(validDto, 'user-1', 'website-1');
  expect(result.status).toBe(ContentStatus.DRAFT);
});
```

**3. 描述性测试名称**
```typescript
// ❌ 不清晰的测试名称
it('test create content', () => {});

// ✅ 清晰的测试名称
it('应该在slug重复时抛出ConflictException错误', () => {});
it('应该在权限不足时抛出ForbiddenException错误', () => {});
it('应该在配额超限时抛出BadRequestException错误', () => {});
```

#### 8.2.2 Mock使用原则

**1. 隔离外部依赖**
```typescript
// ✅ Mock外部服务
const mockPrismaService = {
  contentNode: {
    create: jest.fn(),
    findUnique: jest.fn(),
  }
};

// ✅ Mock复杂计算
const mockSlugGenerator = {
  generate: jest.fn().mockReturnValue('test-slug'),
};
```

**2. 避免过度Mock**
```typescript
// ❌ 过度Mock内部逻辑
const mockValidateDto = jest.fn();

// ✅ 只Mock外部依赖
const mockDatabaseService = jest.fn();
```

#### 8.2.3 测试数据管理

**1. 使用工厂模式**
```typescript
// ✅ 使用数据工厂
const testContent = MockDataFactory.createContentNode({
  title: '特定测试标题'
});
```

**2. 测试数据隔离**
```typescript
// ✅ 每个测试独立的数据
beforeEach(() => {
  testData = MockDataFactory.createContentNode();
});
```

#### 8.2.4 断言最佳实践

**1. 具体断言**
```typescript
// ❌ 模糊断言
expect(result).toBeTruthy();

// ✅ 具体断言
expect(result.status).toBe(ContentStatus.DRAFT);
expect(result.createdAt).toBeInstanceOf(Date);
expect(result.authorInfo.name).toBe('Test Author');
```

**2. 多层次验证**
```typescript
// ✅ 验证返回值
expect(result).toMatchObject({
  title: '测试内容',
  status: ContentStatus.DRAFT
});

// ✅ 验证副作用
expect(mockPrisma.contentNode.create).toHaveBeenCalledWith({
  data: expect.objectContaining({
    title: '测试内容'
  })
});

// ✅ 验证审计日志
expect(mockPrisma.contentAuditLog.create).toHaveBeenCalledWith({
  data: expect.objectContaining({
    action: ContentAuditAction.CREATED
  })
});
```

### 8.3 持续集成配置

#### 8.3.1 GitHub Actions配置
```yaml
# .github/workflows/test.yml
name: CMS Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_PASSWORD: test
          POSTGRES_USER: test
          POSTGRES_DB: cms_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5433:5432

    steps:
    - uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: api/package-lock.json

    - name: Install dependencies
      run: |
        cd api
        npm ci

    - name: Run database migrations
      run: |
        cd api
        npx prisma migrate deploy
      env:
        DATABASE_URL: postgresql://test:test@localhost:5433/cms_test

    - name: Run unit tests
      run: |
        cd api
        npm run test:unit
      env:
        DATABASE_URL: postgresql://test:test@localhost:5433/cms_test

    - name: Run integration tests
      run: |
        cd api
        npm run test:integration
      env:
        DATABASE_URL: postgresql://test:test@localhost:5433/cms_test

    - name: Run E2E tests
      run: |
        cd api
        npm run test:e2e
      env:
        DATABASE_URL: postgresql://test:test@localhost:5433/cms_test

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./api/coverage/lcov.info
        flags: unittests
        name: codecov-umbrella
```

#### 8.3.2 测试脚本配置
```json
{
  "scripts": {
    "test": "jest",
    "test:watch": "jest --watch",
    "test:unit": "jest --testPathPattern=\\.spec\\.ts$",
    "test:integration": "jest --testPathPattern=\\.integration\\.spec\\.ts$",
    "test:e2e": "jest --config ./test/jest-e2e.json",
    "test:coverage": "jest --coverage",
    "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand",
    "test:ci": "jest --ci --coverage --watchAll=false"
  }
}
```

### 8.4 质量门禁

#### 8.4.1 代码提交前检查
- 所有测试必须通过
- 代码覆盖率不能下降
- 新增代码必须有对应测试
- 测试执行时间不能超过阈值

#### 8.4.2 Pull Request检查
- 自动运行完整测试套件
- 覆盖率报告生成
- 性能回归检测
- 代码质量评分

---

## 9. 总结

### 9.1 TDD实施收益

1. **代码质量提升**：测试驱动确保每行代码都有测试覆盖
2. **设计改进**：测试先行促进更好的API设计和模块解耦
3. **重构安全**：完整的测试套件为代码重构提供安全网
4. **文档价值**：测试用例本身就是最好的使用文档
5. **快速反馈**：自动化测试提供即时的代码质量反馈

### 9.2 成功关键因素

1. **团队承诺**：全团队对TDD方法论的理解和执行
2. **工具支持**：完善的测试工具链和CI/CD流程
3. **持续改进**：定期回顾和优化测试策略
4. **质量文化**：将测试质量作为交付标准的一部分

### 9.3 风险控制

1. **测试维护成本**：合理设计测试结构，避免过度测试
2. **开发速度平衡**：在质量和速度之间找到最佳平衡点
3. **技术债务管理**：及时重构测试代码，保持测试套件的健康

通过严格执行这套TDD策略，我们将构建一个高质量、可维护、可扩展的通用CMS系统，为用户提供稳定可靠的内容管理服务。

---

*本文档将随着项目进展持续更新，确保TDD策略与实际开发保持同步。*
