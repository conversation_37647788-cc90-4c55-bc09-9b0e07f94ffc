# Universal CMS REST API 接口规范

> 版本：v1.0  
> 最后更新：2025-01-16  
> 状态：设计阶段

---

## 📋 目录

1. [API概述](#1-api概述)
2. [认证授权](#2-认证授权)
3. [请求响应格式](#3-请求响应格式)
4. [错误处理](#4-错误处理)
5. [内容管理API](#5-内容管理api)
6. [版本管理API](#6-版本管理api)
7. [分类标签API](#7-分类标签api)
8. [批量操作API](#8-批量操作api)
9. [审计日志API](#9-审计日志api)
10. [OpenAPI规范](#10-openapi规范)

---

## 1. API概述

### 1.1 基本信息

- **Base URL**: `https://api.litpage.com/api/v1`
- **协议**: HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8
- **API风格**: RESTful

### 1.2 设计原则

- **Headless CMS**: API First，完全分离前后端
- **资源导向**: 以内容、分类、标签等资源为核心
- **扁平路径**: 简洁的URL设计，缩短API长度
- **统一响应**: 标准化的响应格式和错误处理

### 1.3 核心资源

| 资源 | 描述 | 端点前缀 | 对应数据模型 |
|------|------|----------|-------------|
| **Contents** | 内容管理（Blog、Doc、FAQ等） | `/contents` | ContentNode + ContentRevision |
| **Revisions** | 版本管理 | `/contents/{id}/revisions` | ContentRevision |
| **Categories** | 分类管理 | `/categories` | Category |
| **Tags** | 标签管理 | `/tags` | Tag + ContentNodeTag |

### 1.4 API与数据模型对应关系

> **重要**：本API设计严格遵循数据模型的分离设计原则：

**ContentNode（内容节点）**：
- 存储内容的基本信息和状态（title, slug, authorInfo, status等）
- 通过API的基本字段进行操作

**ContentRevision（内容版本）**：
- 存储具体的内容数据（content, blocks, seo等）
- 通过API的`revisionData`字段进行操作
- 每次内容更新创建新版本，实现版本历史管理

**关联关系**：
- ContentNode.currentRevisionId 指向当前活跃版本
- 分类通过 categoryId 关联
- 标签通过 ContentNodeTag 中间表关联

---

## 2. 认证授权

### 2.1 认证方式

支持两种认证方式：

#### 2.1.1 JWT Bearer Token（用户认证）
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 2.1.2 API Key（第三方集成）
```http
Authorization: ApiKey lp_live_1234567890abcdef1234567890abcdef12
```

### 2.2 API Key格式规范

```bash
# 格式：lp_{environment}_{32位随机字符}

# 生产环境
lp_live_1234567890abcdef1234567890abcdef12

# 测试环境  
lp_test_1234567890abcdef1234567890abcdef12

# 只读权限
lp_readonly_1234567890abcdef1234567890abcdef12
```

### 2.3 权限范围

| API Key类型 | 权限说明 | 可执行操作 |
|-------------|----------|------------|
| `lp_live_*` | 生产环境完整权限 | 所有CRUD操作 |
| `lp_test_*` | 测试环境完整权限 | 所有CRUD操作 |
| `lp_readonly_*` | 只读权限 | 仅GET操作 |

### 2.4 网站权限验证

所有API调用都需要指定 `websiteId` 参数，系统会验证当前认证用户/API Key对该网站的访问权限。

```bash
GET /api/v1/contents?websiteId=123
```

---

## 3. 请求响应格式

### 3.1 统一响应格式

#### 3.1.1 成功响应
```json
{
  "success": true,
  "data": {
    // 实际业务数据
  },
  "meta": {
    "timestamp": "2024-01-16T10:30:00Z",
    "requestId": "req_1234567890abcdef",
    "version": "v1"
  },
  "pagination": {  // 仅列表接口返回
    "page": 1,
    "limit": 20,
    "total": 150,
    "totalPages": 8,
    "hasNext": true,
    "hasPrev": false
  }
}
```

#### 3.1.2 错误响应
```json
{
  "success": false,
  "error": {
    "code": "CONTENT_NOT_FOUND",
    "message": "Content not found",
    "details": {
      "contentId": "123",
      "websiteId": "456"
    }
  },
  "meta": {
    "timestamp": "2024-01-16T10:30:00Z",
    "requestId": "req_1234567890abcdef",
    "version": "v1"
  }
}
```

### 3.2 分页格式

所有列表接口支持分页：

```bash
GET /api/v1/contents?websiteId=123&page=1&limit=20
```

**分页参数：**
- `page`: 页码（从1开始，默认1）
- `limit`: 每页数量（默认20，最大100）

### 3.3 排序格式

```bash
GET /api/v1/contents?websiteId=123&sort=publishedAt:desc,createdAt:asc
```

**排序格式：** `字段名:方向`
- 方向：`asc`（升序）或 `desc`（降序）
- 多字段排序用逗号分隔

---

## 4. 错误处理

### 4.1 HTTP状态码

| 状态码 | 说明 | 使用场景 |
|--------|------|----------|
| **200** | 成功 | GET、PUT成功 |
| **201** | 已创建 | POST成功创建资源 |
| **204** | 无内容 | DELETE成功 |
| **400** | 请求错误 | 参数验证失败 |
| **401** | 未授权 | 认证失败 |
| **403** | 禁止访问 | 权限不足 |
| **404** | 资源不存在 | 请求的资源不存在 |
| **409** | 冲突 | 资源已存在（如slug重复） |
| **422** | 无法处理 | 业务逻辑错误 |
| **429** | 频率限制 | 请求过于频繁 |
| **500** | 服务器错误 | 内部错误 |

### 4.2 业务错误码

#### 4.2.1 通用错误码 (1000-1999)
```typescript
INVALID_REQUEST = "INVALID_REQUEST"           // 1001: 请求参数无效
UNAUTHORIZED = "UNAUTHORIZED"                 // 1002: 未授权
FORBIDDEN = "FORBIDDEN"                       // 1003: 禁止访问
RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED"   // 1004: 频率限制
INTERNAL_ERROR = "INTERNAL_ERROR"             // 1005: 服务器内部错误
```

#### 4.2.2 内容相关错误码 (2000-2999)
```typescript
CONTENT_NOT_FOUND = "CONTENT_NOT_FOUND"             // 2001: 内容不存在
CONTENT_ALREADY_EXISTS = "CONTENT_ALREADY_EXISTS"   // 2002: 内容已存在（slug冲突）
CONTENT_INVALID_STATUS = "CONTENT_INVALID_STATUS"   // 2003: 内容状态无效
CONTENT_CANNOT_PUBLISH = "CONTENT_CANNOT_PUBLISH"   // 2004: 内容无法发布
CONTENT_CANNOT_DELETE = "CONTENT_CANNOT_DELETE"     // 2005: 内容无法删除
CONTENT_INVALID_TYPE = "CONTENT_INVALID_TYPE"       // 2006: 内容类型无效
```

#### 4.2.3 版本相关错误码 (3000-3999)
```typescript
REVISION_NOT_FOUND = "REVISION_NOT_FOUND"           // 3001: 版本不存在
REVISION_ALREADY_PUBLISHED = "REVISION_ALREADY_PUBLISHED" // 3002: 版本已发布
REVISION_CANNOT_EDIT = "REVISION_CANNOT_EDIT"       // 3003: 版本无法编辑
```

#### 4.2.4 分类相关错误码 (4000-4999)
```typescript
CATEGORY_NOT_FOUND = "CATEGORY_NOT_FOUND"           // 4001: 分类不存在
CATEGORY_HAS_CHILDREN = "CATEGORY_HAS_CHILDREN"     // 4002: 分类有子分类
CATEGORY_HAS_CONTENTS = "CATEGORY_HAS_CONTENTS"     // 4003: 分类下有内容
CATEGORY_INVALID_PARENT = "CATEGORY_INVALID_PARENT" // 4004: 无效的父分类
```

#### 4.2.5 标签相关错误码 (5000-5999)
```typescript
TAG_NOT_FOUND = "TAG_NOT_FOUND"                     // 5001: 标签不存在
TAG_ALREADY_EXISTS = "TAG_ALREADY_EXISTS"           // 5002: 标签已存在
```

#### 4.2.6 权限相关错误码 (6000-6999)
```typescript
WEBSITE_NOT_FOUND = "WEBSITE_NOT_FOUND"             // 6001: 网站不存在
WEBSITE_ACCESS_DENIED = "WEBSITE_ACCESS_DENIED"     // 6002: 网站访问被拒绝
WORKSPACE_ACCESS_DENIED = "WORKSPACE_ACCESS_DENIED" // 6003: 工作空间访问被拒绝
INVALID_API_KEY = "INVALID_API_KEY"                 // 6004: API密钥无效
API_KEY_EXPIRED = "API_KEY_EXPIRED"                 // 6005: API密钥已过期
```

### 4.3 错误响应示例

```json
{
  "success": false,
  "error": {
    "code": "CONTENT_NOT_FOUND",
    "message": "The requested content does not exist",
    "details": {
      "contentId": "123",
      "websiteId": "456"
    }
  },
  "meta": {
    "timestamp": "2024-01-16T10:30:00Z",
    "requestId": "req_1234567890abcdef",
    "version": "v1"
  }
}
```

---

## 5. 内容管理API

### 5.1 获取内容列表

#### 5.1.1 获取所有类型内容
```http
GET /api/v1/contents
```

**查询参数：**
```bash
websiteId=123           # 必需：网站ID
type=BLOG              # 可选：内容类型（BLOG,DOC,FAQ,CHANGELOG,PRODUCT,ANNOUNCEMENT,TUTORIAL,CASE_STUDY,WHITEPAPER,CUSTOM）
status=PUBLISHED       # 可选：状态（DRAFT,PUBLISHED,ARCHIVED）
categoryId=456         # 可选：分类ID
tags=tag1,tag2         # 可选：标签（逗号分隔）
search=关键词           # 可选：关键词搜索
language=EN            # 可选：语言（EN,CN,ZH,ES等）
authorName=张三         # 可选：作者名称
page=1                 # 可选：页码（默认1）
limit=20               # 可选：每页数量（默认20，最大100）
sort=publishedAt:desc  # 可选：排序
```

**响应示例：**
```json
{
  "success": true,
  "data": [
    {
      "id": "content_123",
      "type": "BLOG",
      "slug": "hello-world",
      "title": "Hello World",
      "excerpt": "我的第一篇博客文章",
      "language": "CN",
      "status": "PUBLISHED",
      "publishedAt": "2024-01-15T10:00:00Z",
      "createdAt": "2024-01-15T09:00:00Z",
      "updatedAt": "2024-01-15T10:00:00Z",
      "authorInfo": {
        "name": "张三",
        "avatar": "/avatars/zhangsan.jpg",
        "bio": "前端开发工程师"
      },
      "category": {
        "id": "cat_456",
        "name": "技术分享",
        "slug": "tech"
      },
      "tags": [
        {
          "id": "tag_789",
          "name": "JavaScript",
          "color": "#f7df1e"
        }
      ],
      "currentRevision": {
        "id": "rev_abc",
        "version": 2,
        "publishedAt": "2024-01-15T10:00:00Z"
      }
    }
  ],
  "meta": {
    "timestamp": "2024-01-16T10:30:00Z",
    "requestId": "req_1234567890abcdef",
    "version": "v1"
  },
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "totalPages": 8,
    "hasNext": true,
    "hasPrev": false
  }
}
```

#### 5.1.2 按类型获取内容（推荐）
```http
GET /api/v1/contents/blog
GET /api/v1/contents/doc
GET /api/v1/contents/faq
GET /api/v1/contents/changelog
```

### 5.2 获取内容详情

```http
GET /api/v1/contents/{id}
```

**查询参数：**
```bash
websiteId=123          # 必需：网站ID
workspaceId=456        # 必需：工作空间ID
includeRevisionData=true    # 可选：是否包含当前版本的内容数据（默认false）
includeRevisionHistory=false # 可选：是否包含版本历史（默认false）
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    // === ContentNode 基本信息 ===
    "id": "content_123",
    "userId": "user_xyz",
    "workspaceId": "workspace_456",
    "websiteId": "website_123",
    "type": "BLOG",
    "slug": "hello-world",
    "title": "Hello World",
    "excerpt": "我的第一篇博客文章",
    "language": "CN",
    "status": "PUBLISHED",
    "publishedAt": "2024-01-15T10:00:00Z",
    "createdAt": "2024-01-15T09:00:00Z",
    "updatedAt": "2024-01-15T10:00:00Z",
    "currentRevisionId": "rev_abc",
    "authorInfo": {
      "name": "张三",
      "avatar": "/avatars/zhangsan.jpg",
      "bio": "前端开发工程师",
      "email": "<EMAIL>",
      "social": {
        "twitter": "@zhangsan_dev",
        "github": "zhangsan"
      }
    },
    "category": {
      "id": "cat_456",
      "name": "技术分享",
      "slug": "tech",
      "parent": {
        "id": "cat_123",
        "name": "博客",
        "slug": "blog"
      }
    },
    "tags": [
      {
        "id": "tag_789",
        "name": "JavaScript",
        "color": "#f7df1e"
      }
    ],
    "currentRevision": {
      "id": "rev_abc",
      "version": 2,
      "isPublished": true,
      "changelog": "修复了一些错别字",
      "createdAt": "2024-01-15T09:30:00Z",
      "publishedAt": "2024-01-15T10:00:00Z",
      "createdBy": {
        "id": "user_xyz",
        "name": "张三",
        "image": "/avatars/zhangsan.jpg"
      },
      "data": {  // 仅当includeRevisionData=true时返回
        "content": "# Hello World\n\n这是我的第一篇博客文章...",
        "blocks": [...],
        "seo": {
          "metaTitle": "Hello World - 张三的博客",
          "metaDescription": "我的第一篇博客文章，分享我的学习经历",
          "keywords": ["博客", "学习", "分享"]
        },
        "customFields": {
          "readingTime": 5,
          "difficulty": "beginner"
        }
      }
    },
    "user": {
      "id": "user_xyz",
      "name": "张三",
      "image": "/avatars/zhangsan.jpg"
    }
  },
  "meta": {
    "timestamp": "2024-01-16T10:30:00Z",
    "requestId": "req_1234567890abcdef",
    "version": "v1"
  }
}
```

### 5.3 创建内容

> **重要说明**：创建内容时会同时创建 `ContentNode`（内容节点）和 `ContentRevision`（初始版本），这与数据模型的设计保持一致。

#### 5.3.1 创建指定类型内容（推荐）
```http
POST /api/v1/contents/blog
POST /api/v1/contents/doc
POST /api/v1/contents/faq
POST /api/v1/contents/changelog
POST /api/v1/contents/product
POST /api/v1/contents/announcement
POST /api/v1/contents/tutorial
POST /api/v1/contents/case-study
POST /api/v1/contents/whitepaper
```

#### 5.3.2 通用创建端点
```http
POST /api/v1/contents
```

#### 5.3.3 创建流程说明

**后端处理流程：**
1. **验证权限**：检查用户对指定工作空间和网站的权限
2. **创建ContentNode**：存储基本信息（title, slug, authorInfo等）
3. **创建ContentRevision**：存储内容数据（content, blocks, seo等）
4. **建立关联**：设置ContentNode.currentRevisionId指向新创建的版本
5. **处理标签**：创建ContentNodeTag关联记录
6. **返回结果**：返回完整的内容信息

**查询参数：**
```bash
websiteId=123          # 必需：网站ID
workspaceId=456        # 必需：工作空间ID
```

**请求体：**
```json
{
  // === ContentNode 基本信息字段 ===
  "type": "BLOG",                    // 内容类型（仅通用端点需要）
  "slug": "hello-world",             // URL slug
  "title": "Hello World",            // 标题
  "excerpt": "我的第一篇博客文章",    // 摘要（可选）
  "language": "CN",                  // 语言（默认EN）
  "authorInfo": {                    // 作者信息（可选，默认使用当前用户信息）
    "name": "张三",
    "avatar": "/avatars/zhangsan.jpg",
    "bio": "前端开发工程师",
    "email": "<EMAIL>",
    "website": "https://zhangsan.dev",
    "social": {
      "twitter": "@zhangsan_dev",
      "github": "zhangsan"
    }
  },
  "categoryId": "cat_456",           // 分类ID（可选）
  "tagIds": ["tag_789", "tag_101"],  // 标签ID数组（可选）
  "status": "DRAFT",                 // 初始状态（可选，默认DRAFT）
  "publishedAt": "2024-01-15T10:00:00Z",  // 定时发布时间（可选）
  
  // === ContentRevision 内容数据字段 ===
  "revisionData": {                  // 版本内容数据
    "content": "# Hello World\n\n这是我的第一篇博客文章...",
    "blocks": [...],                 // 可视化编辑器块数据
    "seo": {                         // SEO设置（可选）
      "metaTitle": "Hello World - 张三的博客",
      "metaDescription": "我的第一篇博客文章",
      "keywords": ["博客", "学习"]
    },
    "customFields": {                // 自定义字段（可选）
      "readingTime": 5,
      "difficulty": "beginner"
    }
  },
  "changelog": "初始版本"             // 版本变更说明（可选）
}
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    // === ContentNode 信息 ===
    "id": "content_123",
    "userId": "user_xyz",              // 创建用户ID（从token获取）
    "workspaceId": "workspace_456",    // 工作空间ID
    "websiteId": "website_123",        // 网站ID
    "type": "BLOG",
    "slug": "hello-world",
    "title": "Hello World",
    "excerpt": "我的第一篇博客文章",
    "language": "CN",
    "status": "DRAFT",
    "authorInfo": {
      "name": "张三",
      "avatar": "/avatars/zhangsan.jpg",
      "bio": "前端开发工程师"
    },
    "categoryId": "cat_456",
    "publishedAt": null,
    "createdAt": "2024-01-15T09:00:00Z",
    "updatedAt": "2024-01-15T09:00:00Z",
    
    // === 当前版本信息 ===
    "currentRevisionId": "rev_abc",
    "currentRevision": {
      "id": "rev_abc",
      "version": 1,
      "isPublished": false,
      "createdAt": "2024-01-15T09:00:00Z",
      "createdBy": {
        "id": "user_xyz",
        "name": "张三"
      }
    },
    
    // === 关联信息 ===
    "tags": []                         // 标签关联（初始为空）
  },
  "meta": {
    "timestamp": "2024-01-16T10:30:00Z",
    "requestId": "req_1234567890abcdef",
    "version": "v1"
  }
}
```

### 5.4 更新内容

#### 5.4.1 更新基本信息（不创建新版本）
```http
PUT /api/v1/contents/{id}
```

**查询参数：**
```bash
websiteId=123          # 必需：网站ID
workspaceId=456        # 必需：工作空间ID
```

**请求体：**（仅更新ContentNode字段）
```json
{
  "slug": "updated-slug",            // 可选：更新slug
  "title": "Updated Title",          // 可选：更新标题
  "excerpt": "更新的摘要",            // 可选：更新摘要
  "language": "CN",                  // 可选：更新语言
  "authorInfo": {                    // 可选：更新作者信息
    "name": "张三",
    "bio": "高级前端开发工程师"
  },
  "categoryId": "cat_789",           // 可选：更新分类
  "tagIds": ["tag_101", "tag_202"]   // 可选：更新标签
}
```

#### 5.4.2 更新内容并创建新版本
```http
PUT /api/v1/contents/{id}/content
```

**查询参数：**
```bash
websiteId=123          # 必需：网站ID
workspaceId=456        # 必需：工作空间ID
```

**请求体：**
```json
{
  // === 可选：同时更新ContentNode基本信息 ===
  "title": "Updated Title",          // 可选：更新标题
  "excerpt": "更新的摘要",            // 可选：更新摘要
  
  // === 必需：ContentRevision内容数据 ===
  "revisionData": {                  // 新版本内容数据
    "content": "# Updated Content\n\n更新后的内容...",
    "blocks": [...],                 // 更新的块数据
    "seo": {                         // 更新的SEO设置
      "metaTitle": "Updated Title",
      "metaDescription": "更新的描述"
    }
  },
  "changelog": "更新了内容和SEO设置"   // 版本变更说明
}
```

### 5.5 删除内容

```http
DELETE /api/v1/contents/{id}
```

**查询参数：**
```bash
websiteId=123          # 必需：网站ID
workspaceId=456        # 必需：工作空间ID
```

### 5.6 内容状态管理

#### 5.6.1 发布内容
```http
POST /api/v1/contents/{id}/publish
```

**查询参数：**
```bash
websiteId=123          # 必需：网站ID
workspaceId=456        # 必需：工作空间ID
```

**请求体：**
```json
{
  "publishedAt": "2024-01-15T10:00:00Z"  // 可选：指定发布时间（未来时间表示定时发布）
}
```

#### 5.6.4 定时发布管理

**获取定时发布列表：**
```http
GET /api/v1/contents/scheduled
```

**查询参数：**
```bash
websiteId=123          # 必需：网站ID
workspaceId=456        # 必需：工作空间ID
startDate=2024-01-16   # 可选：开始时间
endDate=2024-01-31     # 可选：结束时间
```

**取消定时发布：**
```http
DELETE /api/v1/contents/{id}/schedule
```

**查询参数：**
```bash
websiteId=123          # 必需：网站ID
workspaceId=456        # 必需：工作空间ID
```

**更新定时发布时间：**
```http
PUT /api/v1/contents/{id}/schedule
```

**请求体：**
```json
{
  "publishedAt": "2024-01-20T14:00:00Z"  // 新的定时发布时间
}
```

#### 5.6.2 取消发布
```http
POST /api/v1/contents/{id}/unpublish
```

**查询参数：**
```bash
websiteId=123          # 必需：网站ID
workspaceId=456        # 必需：工作空间ID
```

#### 5.6.3 归档内容
```http
POST /api/v1/contents/{id}/archive
```

**查询参数：**
```bash
websiteId=123          # 必需：网站ID
workspaceId=456        # 必需：工作空间ID
```

### 5.7 按slug获取内容

```http
GET /api/v1/contents/by-slug/{slug}
```

**查询参数：**
```bash
websiteId=123          # 必需：网站ID
workspaceId=456        # 必需：工作空间ID
language=EN            # 可选：语言（默认EN）
status=PUBLISHED       # 可选：状态过滤
includeRevisionData=false # 可选：是否包含内容数据
```

---

## 6. 版本管理API

### 6.1 获取版本历史

```http
GET /api/v1/contents/{id}/revisions
```

**查询参数：**
```bash
websiteId=123          # 必需：网站ID
workspaceId=456        # 必需：工作空间ID
page=1                 # 可选：页码
limit=10               # 可选：每页数量
includeContent=false   # 可选：是否包含内容数据
```

**响应示例：**
```json
{
  "success": true,
  "data": [
    {
      "id": "rev_abc",
      "version": 2,
      "changelog": "修复了一些错别字",
      "isPublished": true,
      "createdAt": "2024-01-15T10:00:00Z",
      "publishedAt": "2024-01-15T10:00:00Z",
      "createdBy": {
        "id": "user_xyz",
        "name": "张三"
      },
      "data": {  // 仅当includeContent=true时返回
        "content": "...",
        "seo": {...}
      }
    }
  ],
  "meta": {...},
  "pagination": {...}
}
```

### 6.2 获取特定版本

```http
GET /api/v1/contents/{id}/revisions/{version}
```

**查询参数：**
```bash
websiteId=123          # 必需：网站ID
workspaceId=456        # 必需：工作空间ID
includeContent=true    # 可选：是否包含内容数据（默认true）
```

### 6.3 创建新版本

```http
POST /api/v1/contents/{id}/revisions
```

**查询参数：**
```bash
websiteId=123          # 必需：网站ID
workspaceId=456        # 必需：工作空间ID
```

**请求体：**
```json
{
  "data": {
    "content": "更新后的内容...",
    "seo": {...}
  },
  "changelog": "更新了内容和SEO设置"
}
```

### 6.4 发布特定版本

```http
POST /api/v1/contents/{id}/revisions/{version}/publish
```

**查询参数：**
```bash
websiteId=123          # 必需：网站ID
workspaceId=456        # 必需：工作空间ID
```

### 6.5 版本对比

```http
GET /api/v1/contents/{id}/revisions/compare
```

**查询参数：**
```bash
websiteId=123          # 必需：网站ID
workspaceId=456        # 必需：工作空间ID
from=1                 # 源版本号
to=2                   # 目标版本号
```

### 6.6 内容预览

#### 6.6.1 预览当前版本
```http
GET /api/v1/contents/{id}/preview
```

**查询参数：**
```bash
websiteId=123          # 必需：网站ID
workspaceId=456        # 必需：工作空间ID
```

#### 6.6.2 预览特定版本
```http
GET /api/v1/contents/{id}/revisions/{version}/preview
```

**查询参数：**
```bash
websiteId=123          # 必需：网站ID
workspaceId=456        # 必需：工作空间ID
```

#### 6.6.3 预览草稿内容
```http
POST /api/v1/contents/preview
```

**查询参数：**
```bash
websiteId=123          # 必需：网站ID
workspaceId=456        # 必需：工作空间ID
```

**请求体：**（临时预览未保存的内容）
```json
{
  "type": "BLOG",
  "title": "预览标题",
  "excerpt": "预览摘要",
  "revisionData": {
    "content": "预览内容...",
    "blocks": [...],
    "seo": {...}
  }
}
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "previewUrl": "https://preview.litpage.com/content/temp_preview_token",
    "previewToken": "temp_preview_token_123",
    "expiresAt": "2024-01-16T11:30:00Z"
  },
  "meta": {...}
}
```

---

## 7. 分类标签API

### 7.1 分类管理

#### 7.1.1 获取分类列表
```http
GET /api/v1/categories
```

**查询参数：**
```bash
websiteId=123          # 必需：网站ID
type=GENERAL           # 可选：分类类型
language=EN            # 可选：语言
parentId=456           # 可选：父分类ID
includeChildren=true   # 可选：是否包含子分类
includeCount=true      # 可选：是否包含内容数量
```

#### 7.1.2 获取分类树
```http
GET /api/v1/categories/tree
```

#### 7.1.3 创建分类
```http
POST /api/v1/categories
```

**请求体：**
```json
{
  "name": "技术分享",
  "slug": "tech",
  "description": "技术相关的文章分类",
  "parentId": "cat_123",     // 可选：父分类ID
  "type": "GENERAL",         // 分类类型
  "language": "CN",          // 语言
  "order": 1                 // 排序
}
```

#### 7.1.4 更新分类
```http
PUT /api/v1/categories/{id}
```

#### 7.1.5 删除分类
```http
DELETE /api/v1/categories/{id}
```

### 7.2 标签管理

#### 7.2.1 获取标签列表
```http
GET /api/v1/tags
```

**查询参数：**
```bash
websiteId=123          # 必需：网站ID
language=EN            # 可选：语言
search=JavaScript      # 可选：标签名搜索
includeCount=true      # 可选：是否包含使用次数
```

#### 7.2.2 创建标签
```http
POST /api/v1/tags
```

**请求体：**
```json
{
  "name": "JavaScript",
  "color": "#f7df1e",        // 可选：标签颜色
  "description": "JavaScript相关内容",  // 可选：描述
  "language": "EN"           // 语言
}
```

#### 7.2.3 更新标签
```http
PUT /api/v1/tags/{id}
```

#### 7.2.4 删除标签
```http
DELETE /api/v1/tags/{id}
```

#### 7.2.5 标签使用统计
```http
GET /api/v1/tags/{id}/stats
```

---

## 8. 批量操作API

### 8.1 批量内容操作

```http
POST /api/v1/contents/batch
```

**查询参数：**
```bash
websiteId=123          # 必需：网站ID
```

**请求体：**
```json
{
  "action": "publish",           // 操作类型：publish, unpublish, archive, delete
  "contentIds": [                // 内容ID数组
    "content_123",
    "content_456",
    "content_789"
  ],
  "options": {                   // 可选：操作选项
    "publishedAt": "2024-01-15T10:00:00Z"  // 批量发布时间
  }
}
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "processed": 3,              // 已处理数量
    "successful": 2,             // 成功数量
    "failed": 1,                 // 失败数量
    "results": [
      {
        "contentId": "content_123",
        "success": true
      },
      {
        "contentId": "content_456",
        "success": true
      },
      {
        "contentId": "content_789",
        "success": false,
        "error": {
          "code": "CONTENT_CANNOT_PUBLISH",
          "message": "Content cannot be published"
        }
      }
    ]
  },
  "meta": {...}
}
```

### 8.2 批量标签操作

```http
POST /api/v1/contents/batch/tags
```

**请求体：**
```json
{
  "action": "add",               // 操作类型：add, remove, replace
  "contentIds": ["content_123", "content_456"],
  "tagIds": ["tag_789", "tag_101"]
}
```

### 8.3 批量分类操作

```http
POST /api/v1/contents/batch/category
```

**请求体：**
```json
{
  "contentIds": ["content_123", "content_456"],
  "categoryId": "cat_789"        // null 表示移除分类
}
```

---

## 9. 审计日志API

### 9.1 获取内容审计日志

```http
GET /api/v1/contents/{id}/audit-logs
```

**查询参数：**
```bash
websiteId=123          # 必需：网站ID
workspaceId=456        # 必需：工作空间ID
action=PUBLISHED       # 可选：操作类型过滤
userId=user_123        # 可选：操作用户过滤
page=1                 # 可选：页码
limit=20               # 可选：每页数量
```

**响应示例：**
```json
{
  "success": true,
  "data": [
    {
      "id": "audit_123",
      "contentId": "content_123",
      "action": "PUBLISHED",
      "revisionId": "rev_abc",
      "metadata": {
        "previousStatus": "DRAFT",
        "currentStatus": "PUBLISHED",
        "publishedAt": "2024-01-15T10:00:00Z"
      },
      "createdAt": "2024-01-15T10:00:00Z",
      "user": {
        "id": "user_xyz",
        "name": "张三",
        "image": "/avatars/zhangsan.jpg"
      }
    }
  ],
  "meta": {...},
  "pagination": {...}
}
```

### 9.2 获取工作空间审计日志

```http
GET /api/v1/audit-logs
```

**查询参数：**
```bash
workspaceId=456        # 必需：工作空间ID
websiteId=123          # 可选：网站ID过滤
contentId=content_123  # 可选：内容ID过滤
action=CREATED         # 可选：操作类型过滤
userId=user_123        # 可选：操作用户过滤
startDate=2024-01-01   # 可选：开始时间
endDate=2024-01-31     # 可选：结束时间
page=1                 # 可选：页码
limit=50               # 可选：每页数量
```

### 9.3 审计操作类型

根据ER文档定义，支持以下审计操作类型：

- `CREATED` - 内容创建
- `DRAFT_SAVED` - 草稿保存  
- `PUBLISHED` - 内容发布
- `MODIFIED` - 内容修改
- `ARCHIVED` - 内容归档
- `RESTORED` - 内容恢复
- `DELETED` - 内容删除
- `REVISION_CREATED` - 新版本创建
- `REVISION_PUBLISHED` - 版本发布
- `METADATA_UPDATED` - 元数据更新
- `CATEGORY_CHANGED` - 分类变更
- `TAGS_UPDATED` - 标签更新
- `AUTHOR_UPDATED` - 作者信息更新
- `SEO_UPDATED` - SEO信息更新

---

## 10. OpenAPI规范

### 10.1 基础信息

```yaml
openapi: 3.0.3
info:
  title: Universal CMS API
  description: Headless CMS API for content management
  version: 1.0.0
  contact:
    name: LitPage API Support
    url: https://docs.litpage.com
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://api.litpage.com/api/v1
    description: Production server
  - url: https://api-staging.litpage.com/api/v1
    description: Staging server

security:
  - BearerAuth: []
  - ApiKeyAuth: []
```

### 10.2 认证方案

```yaml
components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token for user authentication
    
    ApiKeyAuth:
      type: http
      scheme: bearer
      bearerFormat: ApiKey
      description: API key for third-party integration (format: lp_{env}_{key})
```

### 10.3 数据模型

```yaml
components:
  schemas:
    Content:
      type: object
      required:
        - id
        - type
        - slug
        - title
        - status
        - language
        - authorInfo
      properties:
        id:
          type: string
          example: "content_123"
        type:
          type: string
          enum: [BLOG, DOC, FAQ, CHANGELOG, PRODUCT, ANNOUNCEMENT, TUTORIAL, CASE_STUDY, WHITEPAPER, CUSTOM]
          example: "BLOG"
        slug:
          type: string
          example: "hello-world"
        title:
          type: string
          example: "Hello World"
        excerpt:
          type: string
          nullable: true
          example: "我的第一篇博客文章"
        language:
          type: string
          enum: [EN, CN, ZH, ES]
          default: "EN"
          example: "CN"
        status:
          type: string
          enum: [DRAFT, PUBLISHED, ARCHIVED]
          example: "PUBLISHED"
        authorInfo:
          $ref: '#/components/schemas/AuthorInfo'
        category:
          $ref: '#/components/schemas/CategorySummary'
          nullable: true
        tags:
          type: array
          items:
            $ref: '#/components/schemas/TagSummary'
        currentRevision:
          $ref: '#/components/schemas/RevisionSummary'
        publishedAt:
          type: string
          format: date-time
          nullable: true
          example: "2024-01-15T10:00:00Z"
        createdAt:
          type: string
          format: date-time
          example: "2024-01-15T09:00:00Z"
        updatedAt:
          type: string
          format: date-time
          example: "2024-01-15T10:00:00Z"

    AuthorInfo:
      type: object
      required:
        - name
      properties:
        name:
          type: string
          example: "张三"
        avatar:
          type: string
          nullable: true
          example: "/avatars/zhangsan.jpg"
        bio:
          type: string
          nullable: true
          example: "前端开发工程师"
        email:
          type: string
          nullable: true
          example: "<EMAIL>"
        website:
          type: string
          nullable: true
          example: "https://zhangsan.dev"
        social:
          type: object
          nullable: true
          properties:
            twitter:
              type: string
              example: "@zhangsan_dev"
            github:
              type: string
              example: "zhangsan"
            linkedin:
              type: string
              example: "https://linkedin.com/in/zhangsan"

    ContentRevision:
      type: object
      required:
        - id
        - version
        - data
        - isPublished
        - createdAt
      properties:
        id:
          type: string
          example: "rev_abc"
        version:
          type: integer
          example: 2
        data:
          type: object
          description: "Content data in JSON format"
        changelog:
          type: string
          nullable: true
          example: "修复了一些错别字"
        isPublished:
          type: boolean
          example: true
        createdAt:
          type: string
          format: date-time
          example: "2024-01-15T10:00:00Z"
        publishedAt:
          type: string
          format: date-time
          nullable: true
          example: "2024-01-15T10:00:00Z"
        createdBy:
          $ref: '#/components/schemas/UserSummary'

    Category:
      type: object
      required:
        - id
        - name
        - slug
        - type
        - language
      properties:
        id:
          type: string
          example: "cat_456"
        name:
          type: string
          example: "技术分享"
        slug:
          type: string
          example: "tech"
        description:
          type: string
          nullable: true
          example: "技术相关的文章分类"
        parentId:
          type: string
          nullable: true
          example: "cat_123"
        type:
          type: string
          enum: [GENERAL, BLOG, DOC, FAQ, CHANGELOG, PRODUCT, TUTORIAL, ANNOUNCEMENT, CASE_STUDY, WHITEPAPER]
          example: "GENERAL"
        language:
          type: string
          enum: [EN, CN, ZH, ES]
          example: "CN"
        order:
          type: integer
          example: 1
        isActive:
          type: boolean
          example: true
        children:
          type: array
          items:
            $ref: '#/components/schemas/Category'
        contentCount:
          type: integer
          example: 15
        createdAt:
          type: string
          format: date-time
          example: "2024-01-15T09:00:00Z"
        updatedAt:
          type: string
          format: date-time
          example: "2024-01-15T10:00:00Z"

    Tag:
      type: object
      required:
        - id
        - name
        - language
      properties:
        id:
          type: string
          example: "tag_789"
        name:
          type: string
          example: "JavaScript"
        color:
          type: string
          nullable: true
          example: "#f7df1e"
        description:
          type: string
          nullable: true
          example: "JavaScript相关内容"
        language:
          type: string
          enum: [EN, ZH_CN, ZH_TW, JA, KO]
          example: "EN"
        isActive:
          type: boolean
          example: true
        usageCount:
          type: integer
          example: 25
        createdAt:
          type: string
          format: date-time
          example: "2024-01-15T09:00:00Z"
        updatedAt:
          type: string
          format: date-time
          example: "2024-01-15T10:00:00Z"

    ApiResponse:
      type: object
      required:
        - success
        - meta
      properties:
        success:
          type: boolean
          example: true
        data:
          description: "Response data (varies by endpoint)"
        error:
          $ref: '#/components/schemas/ApiError'
        meta:
          $ref: '#/components/schemas/ResponseMeta'
        pagination:
          $ref: '#/components/schemas/Pagination'

    ApiError:
      type: object
      required:
        - code
        - message
      properties:
        code:
          type: string
          example: "CONTENT_NOT_FOUND"
        message:
          type: string
          example: "Content not found"
        details:
          type: object
          description: "Additional error details"

    ResponseMeta:
      type: object
      required:
        - timestamp
        - requestId
        - version
      properties:
        timestamp:
          type: string
          format: date-time
          example: "2024-01-16T10:30:00Z"
        requestId:
          type: string
          example: "req_1234567890abcdef"
        version:
          type: string
          example: "v1"

    Pagination:
      type: object
      required:
        - page
        - limit
        - total
        - totalPages
        - hasNext
        - hasPrev
      properties:
        page:
          type: integer
          example: 1
        limit:
          type: integer
          example: 20
        total:
          type: integer
          example: 150
        totalPages:
          type: integer
          example: 8
        hasNext:
          type: boolean
          example: true
        hasPrev:
          type: boolean
          example: false
```

### 10.4 示例端点定义

```yaml
paths:
  /contents:
    get:
      tags:
        - Content Management
      summary: Get content list
      description: Retrieve a paginated list of contents
      parameters:
        - name: websiteId
          in: query
          required: true
          schema:
            type: string
          example: "123"
        - name: type
          in: query
          schema:
            type: string
            enum: [BLOG, DOC, FAQ, CHANGELOG, PRODUCT, ANNOUNCEMENT, TUTORIAL, CASE_STUDY, WHITEPAPER, CUSTOM]
          example: "BLOG"
        - name: status
          in: query
          schema:
            type: string
            enum: [DRAFT, PUBLISHED, ARCHIVED]
          example: "PUBLISHED"
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
          example: 1
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
          example: 20
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/Content'
                      pagination:
                        $ref: '#/components/schemas/Pagination'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden

    post:
      tags:
        - Content Management
      summary: Create content
      description: Create a new content item
      parameters:
        - name: websiteId
          in: query
          required: true
          schema:
            type: string
          example: "123"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - type
                - slug
                - title
              properties:
                type:
                  type: string
                  enum: [BLOG, DOC, FAQ, CHANGELOG, PRODUCT, ANNOUNCEMENT, TUTORIAL, CASE_STUDY, WHITEPAPER, CUSTOM]
                slug:
                  type: string
                title:
                  type: string
                excerpt:
                  type: string
                language:
                  type: string
                  enum: [EN, CN, ZH, ES]
                  default: "EN"
                authorInfo:
                  $ref: '#/components/schemas/AuthorInfo'
                categoryId:
                  type: string
                tagIds:
                  type: array
                  items:
                    type: string
                content:
                  type: object
                status:
                  type: string
                  enum: [DRAFT, PUBLISHED]
                  default: "DRAFT"
      responses:
        '201':
          description: Content created successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/Content'
```

---

## 📝 总结

这份API规范文档提供了Universal CMS的完整REST API接口设计，包括：

### ✅ 核心特性
- **Headless架构**：API First设计，完全分离前后端
- **统一响应格式**：标准化的成功/错误响应结构
- **完整错误处理**：业务相关的错误码和标准HTTP状态码
- **认证授权**：支持JWT和API Key双重认证方式
- **版本控制**：完善的内容版本管理系统
- **分类标签**：灵活的内容组织和标记系统
- **批量操作**：高效的批量内容管理功能

### 🎯 设计优势
1. **简洁高效**：扁平路径设计，API长度最短
2. **类型安全**：完整的OpenAPI 3.0规范定义
3. **可扩展性**：模块化设计，易于添加新功能
4. **开发友好**：清晰的文档和示例代码
5. **生产就绪**：包含错误处理、分页、排序等生产必需功能

### 🚀 下一步实施
1. **API实现**：基于此规范实现后端API服务
2. **客户端SDK**：生成多语言客户端SDK
3. **API文档**：部署交互式API文档网站
4. **测试套件**：编写完整的API测试用例
5. **监控告警**：实施API性能监控和错误告警

这套API规范为构建现代化的Headless CMS系统提供了完整的技术基础，确保了高性能、可扩展性和开发者友好的API体验。 