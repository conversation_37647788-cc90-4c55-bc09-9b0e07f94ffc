#!/bin/bash

# CMS内容模块测试运行脚本
# 基于TDD策略文档中的测试执行流程

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 打印标题
print_title() {
    echo
    print_message $BLUE "=================================================="
    print_message $BLUE "$1"
    print_message $BLUE "=================================================="
    echo
}

# 检查依赖
check_dependencies() {
    print_title "检查测试依赖"
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        print_message $RED "错误: Node.js 未安装"
        exit 1
    fi
    
    # 检查pnpm
    if ! command -v pnpm &> /dev/null; then
        print_message $RED "错误: pnpm 未安装"
        exit 1
    fi
    
    # 检查Jest (检查根目录的package.json)
    if ! grep -q '"jest"' ../../../package.json; then
        print_message $RED "错误: Jest 未安装"
        exit 1
    fi
    
    print_message $GREEN "✓ 所有依赖检查通过"
}

# 设置测试环境
setup_test_env() {
    print_title "设置测试环境"
    
    # 设置环境变量
    export NODE_ENV=test
    export TEST_DATABASE_URL="postgresql://test:test@localhost:5433/cms_test"
    export JEST_TIMEOUT=10000
    
    print_message $GREEN "✓ 测试环境设置完成"
}

# 运行单元测试
run_unit_tests() {
    print_title "运行单元测试"
    
    print_message $YELLOW "执行ContentService单元测试..."
    
    pnpm jest \
        --config=src/content/__tests__/jest.config.js \
        --testPathPattern="unit.*\.spec\.ts$" \
        --coverage \
        --coverageDirectory=src/content/__tests__/coverage/unit \
        --verbose
    
    if [ $? -eq 0 ]; then
        print_message $GREEN "✓ 单元测试通过"
    else
        print_message $RED "✗ 单元测试失败"
        exit 1
    fi
}

# 运行集成测试
run_integration_tests() {
    print_title "运行集成测试"
    
    print_message $YELLOW "执行ContentController集成测试..."
    
    pnpm jest \
        --config=src/content/__tests__/jest.config.js \
        --testPathPattern="integration.*\.spec\.ts$" \
        --coverage \
        --coverageDirectory=src/content/__tests__/coverage/integration \
        --verbose
    
    if [ $? -eq 0 ]; then
        print_message $GREEN "✓ 集成测试通过"
    else
        print_message $RED "✗ 集成测试失败"
        exit 1
    fi
}

# 运行所有测试
run_all_tests() {
    print_title "运行所有测试"
    
    print_message $YELLOW "执行完整测试套件..."
    
    pnpm jest \
        --config=src/content/__tests__/jest.config.js \
        --coverage \
        --coverageDirectory=src/content/__tests__/coverage/all \
        --verbose \
        --detectOpenHandles \
        --forceExit
    
    if [ $? -eq 0 ]; then
        print_message $GREEN "✓ 所有测试通过"
    else
        print_message $RED "✗ 测试失败"
        exit 1
    fi
}

# 生成覆盖率报告
generate_coverage_report() {
    print_title "生成覆盖率报告"
    
    # 合并覆盖率报告
    if [ -d "src/content/__tests__/coverage" ]; then
        print_message $YELLOW "生成HTML覆盖率报告..."
        
        # 打开覆盖率报告（如果在本地环境）
        if [ "$CI" != "true" ]; then
            if command -v open &> /dev/null; then
                open src/content/__tests__/coverage/all/lcov-report/index.html
            elif command -v xdg-open &> /dev/null; then
                xdg-open src/content/__tests__/coverage/all/lcov-report/index.html
            fi
        fi
        
        print_message $GREEN "✓ 覆盖率报告生成完成"
        print_message $BLUE "报告位置: src/content/__tests__/coverage/all/lcov-report/index.html"
    fi
}

# 清理测试环境
cleanup() {
    print_title "清理测试环境"
    
    # 清理临时文件
    if [ -d "src/content/__tests__/temp" ]; then
        rm -rf src/content/__tests__/temp
    fi
    
    print_message $GREEN "✓ 清理完成"
}

# 显示帮助信息
show_help() {
    echo "CMS内容模块测试运行脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  unit        只运行单元测试"
    echo "  integration 只运行集成测试"
    echo "  all         运行所有测试 (默认)"
    echo "  coverage    生成覆盖率报告"
    echo "  clean       清理测试环境"
    echo "  help        显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0 unit                # 只运行单元测试"
    echo "  $0 integration         # 只运行集成测试"
    echo "  $0 all                 # 运行所有测试"
    echo "  $0 coverage            # 生成覆盖率报告"
}

# 主函数
main() {
    local command=${1:-all}
    
    case $command in
        unit)
            check_dependencies
            setup_test_env
            run_unit_tests
            ;;
        integration)
            check_dependencies
            setup_test_env
            run_integration_tests
            ;;
        all)
            check_dependencies
            setup_test_env
            run_all_tests
            generate_coverage_report
            ;;
        coverage)
            generate_coverage_report
            ;;
        clean)
            cleanup
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_message $RED "错误: 未知命令 '$command'"
            echo
            show_help
            exit 1
            ;;
    esac
}

# 捕获退出信号，确保清理
trap cleanup EXIT

# 执行主函数
main "$@"
