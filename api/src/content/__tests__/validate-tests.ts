/**
 * 测试用例验证脚本
 * 用于验证测试用例的完整性和正确性
 */

import * as fs from 'fs';
import * as path from 'path';

interface TestValidationResult {
  totalTests: number;
  passedValidations: number;
  failedValidations: string[];
  warnings: string[];
  summary: string;
}

class TestValidator {
  private testDir: string;
  private result: TestValidationResult;

  constructor() {
    this.testDir = __dirname;
    this.result = {
      totalTests: 0,
      passedValidations: 0,
      failedValidations: [],
      warnings: [],
      summary: '',
    };
  }

  /**
   * 验证测试文件结构
   */
  validateTestStructure(): void {
    console.log('🔍 验证测试文件结构...');

    const requiredDirs = [
      'factories',
      'scenarios', 
      'setup',
      'unit/services',
      'integration/controllers',
    ];

    const requiredFiles = [
      'factories/mock-data.factory.ts',
      'factories/mock-services.factory.ts',
      'scenarios/content-scenarios.ts',
      'setup/test-setup.ts',
      'unit/services/content.service.spec.ts',
      'integration/controllers/content.controller.integration.spec.ts',
      'jest.config.js',
      'run-tests.sh',
    ];

    // 检查目录结构
    for (const dir of requiredDirs) {
      const dirPath = path.join(this.testDir, dir);
      if (fs.existsSync(dirPath)) {
        this.result.passedValidations++;
        console.log(`✅ 目录存在: ${dir}`);
      } else {
        this.result.failedValidations.push(`目录不存在: ${dir}`);
        console.log(`❌ 目录不存在: ${dir}`);
      }
    }

    // 检查必需文件
    for (const file of requiredFiles) {
      const filePath = path.join(this.testDir, file);
      if (fs.existsSync(filePath)) {
        this.result.passedValidations++;
        console.log(`✅ 文件存在: ${file}`);
      } else {
        this.result.failedValidations.push(`文件不存在: ${file}`);
        console.log(`❌ 文件不存在: ${file}`);
      }
    }
  }

  /**
   * 验证测试用例覆盖度
   */
  validateTestCoverage(): void {
    console.log('\n🎯 验证测试用例覆盖度...');

    const serviceTestFile = path.join(this.testDir, 'unit/services/content.service.spec.ts');
    const controllerTestFile = path.join(this.testDir, 'integration/controllers/content.controller.integration.spec.ts');

    // 检查ContentService测试覆盖
    if (fs.existsSync(serviceTestFile)) {
      const serviceTestContent = fs.readFileSync(serviceTestFile, 'utf-8');
      
      const requiredServiceTests = [
        'createContent',
        'getContent', 
        'updateContent',
        'deleteContent',
        'publishContent',
      ];

      for (const testName of requiredServiceTests) {
        if (serviceTestContent.includes(`describe('${testName}'`)) {
          this.result.passedValidations++;
          console.log(`✅ ContentService测试覆盖: ${testName}`);
        } else {
          this.result.failedValidations.push(`ContentService缺少测试: ${testName}`);
          console.log(`❌ ContentService缺少测试: ${testName}`);
        }
      }

      // 检查错误场景测试
      const errorScenarios = [
        'slug重复',
        '权限不足',
        '配额超限',
        '验证错误',
      ];

      for (const scenario of errorScenarios) {
        if (serviceTestContent.includes(scenario)) {
          this.result.passedValidations++;
          console.log(`✅ 错误场景测试: ${scenario}`);
        } else {
          this.result.warnings.push(`建议添加错误场景测试: ${scenario}`);
          console.log(`⚠️  建议添加错误场景测试: ${scenario}`);
        }
      }
    }

    // 检查Controller测试覆盖
    if (fs.existsSync(controllerTestFile)) {
      const controllerTestContent = fs.readFileSync(controllerTestFile, 'utf-8');
      
      const requiredControllerTests = [
        'POST /api/v1/contents',
        'GET /api/v1/contents/:id',
        'GET /api/v1/contents',
        'PUT /api/v1/contents/:id',
        'DELETE /api/v1/contents/:id',
        'PUT /api/v1/contents/:id/publish',
      ];

      for (const testName of requiredControllerTests) {
        if (controllerTestContent.includes(testName)) {
          this.result.passedValidations++;
          console.log(`✅ Controller测试覆盖: ${testName}`);
        } else {
          this.result.failedValidations.push(`Controller缺少测试: ${testName}`);
          console.log(`❌ Controller缺少测试: ${testName}`);
        }
      }

      // 检查HTTP状态码测试
      const statusCodes = ['200', '201', '400', '401', '403', '404', '409'];
      for (const code of statusCodes) {
        if (controllerTestContent.includes(`HttpStatus.`) && controllerTestContent.includes(code)) {
          this.result.passedValidations++;
          console.log(`✅ HTTP状态码测试: ${code}`);
        } else {
          this.result.warnings.push(`建议添加HTTP状态码测试: ${code}`);
          console.log(`⚠️  建议添加HTTP状态码测试: ${code}`);
        }
      }
    }
  }

  /**
   * 验证Mock数据工厂
   */
  validateMockFactories(): void {
    console.log('\n🏭 验证Mock数据工厂...');

    const mockDataFile = path.join(this.testDir, 'factories/mock-data.factory.ts');
    const mockServicesFile = path.join(this.testDir, 'factories/mock-services.factory.ts');

    // 检查Mock数据工厂
    if (fs.existsSync(mockDataFile)) {
      const mockDataContent = fs.readFileSync(mockDataFile, 'utf-8');
      
      const requiredFactories = [
        'createUser',
        'createWorkspace',
        'createWebsite',
        'createContentNode',
        'createContentRevision',
        'createCategory',
        'createTag',
        'createContentNodeTag',
        'createContentAuditLog',
      ];

      for (const factory of requiredFactories) {
        if (mockDataContent.includes(`static ${factory}`)) {
          this.result.passedValidations++;
          console.log(`✅ Mock数据工厂: ${factory}`);
        } else {
          this.result.failedValidations.push(`Mock数据工厂缺少: ${factory}`);
          console.log(`❌ Mock数据工厂缺少: ${factory}`);
        }
      }
    }

    // 检查Mock服务工厂
    if (fs.existsSync(mockServicesFile)) {
      const mockServicesContent = fs.readFileSync(mockServicesFile, 'utf-8');
      
      if (mockServicesContent.includes('createMockPrismaService')) {
        this.result.passedValidations++;
        console.log(`✅ Mock服务工厂: PrismaService`);
      } else {
        this.result.failedValidations.push(`Mock服务工厂缺少: PrismaService`);
        console.log(`❌ Mock服务工厂缺少: PrismaService`);
      }
    }
  }

  /**
   * 验证测试场景数据
   */
  validateTestScenarios(): void {
    console.log('\n📋 验证测试场景数据...');

    const scenariosFile = path.join(this.testDir, 'scenarios/content-scenarios.ts');

    if (fs.existsSync(scenariosFile)) {
      const scenariosContent = fs.readFileSync(scenariosFile, 'utf-8');
      
      const requiredScenarios = [
        'contentLifecycle',
        'permissions',
        'versioning',
        'categoriesAndTags',
        'complexScenarios',
        'errorScenarios',
      ];

      for (const scenario of requiredScenarios) {
        if (scenariosContent.includes(`static ${scenario}`)) {
          this.result.passedValidations++;
          console.log(`✅ 测试场景: ${scenario}`);
        } else {
          this.result.failedValidations.push(`测试场景缺少: ${scenario}`);
          console.log(`❌ 测试场景缺少: ${scenario}`);
        }
      }
    }
  }

  /**
   * 验证Jest配置
   */
  validateJestConfig(): void {
    console.log('\n⚙️  验证Jest配置...');

    const jestConfigFile = path.join(this.testDir, 'jest.config.js');

    if (fs.existsSync(jestConfigFile)) {
      const jestConfigContent = fs.readFileSync(jestConfigFile, 'utf-8');
      
      const requiredConfigs = [
        'coverageThreshold',
        'testMatch',
        'moduleNameMapping',
        'setupFilesAfterEnv',
        'collectCoverageFrom',
      ];

      for (const config of requiredConfigs) {
        if (jestConfigContent.includes(config)) {
          this.result.passedValidations++;
          console.log(`✅ Jest配置: ${config}`);
        } else {
          this.result.failedValidations.push(`Jest配置缺少: ${config}`);
          console.log(`❌ Jest配置缺少: ${config}`);
        }
      }

      // 检查覆盖率阈值
      if (jestConfigContent.includes('functions: 90') && jestConfigContent.includes('lines: 90')) {
        this.result.passedValidations++;
        console.log(`✅ 覆盖率阈值设置正确`);
      } else {
        this.result.warnings.push(`覆盖率阈值可能需要调整`);
        console.log(`⚠️  覆盖率阈值可能需要调整`);
      }
    }
  }

  /**
   * 生成验证报告
   */
  generateReport(): TestValidationResult {
    console.log('\n📊 生成验证报告...');

    this.result.totalTests = this.result.passedValidations + this.result.failedValidations.length;
    
    const successRate = this.result.totalTests > 0 
      ? (this.result.passedValidations / this.result.totalTests * 100).toFixed(1)
      : '0';

    this.result.summary = `
测试验证完成！

📈 验证统计:
- 总验证项: ${this.result.totalTests}
- 通过验证: ${this.result.passedValidations}
- 失败验证: ${this.result.failedValidations.length}
- 警告信息: ${this.result.warnings.length}
- 成功率: ${successRate}%

${this.result.failedValidations.length > 0 ? `
❌ 失败的验证项:
${this.result.failedValidations.map(item => `  - ${item}`).join('\n')}
` : ''}

${this.result.warnings.length > 0 ? `
⚠️  警告信息:
${this.result.warnings.map(item => `  - ${item}`).join('\n')}
` : ''}

${this.result.failedValidations.length === 0 ? '🎉 所有验证项都通过了！' : '🔧 请修复失败的验证项'}
`;

    console.log(this.result.summary);
    return this.result;
  }

  /**
   * 运行完整验证
   */
  runValidation(): TestValidationResult {
    console.log('🚀 开始验证CMS测试用例...\n');

    this.validateTestStructure();
    this.validateTestCoverage();
    this.validateMockFactories();
    this.validateTestScenarios();
    this.validateJestConfig();

    return this.generateReport();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const validator = new TestValidator();
  const result = validator.runValidation();
  
  // 根据验证结果设置退出码
  process.exit(result.failedValidations.length > 0 ? 1 : 0);
}

export { TestValidator, TestValidationResult };
