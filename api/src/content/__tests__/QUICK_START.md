# 🚀 CMS内容模块TDD开发快速启动指南

> 基于已完成的测试用例，立即开始Red-Green-Refactor开发循环

## 📋 前置条件检查

### 1. 环境准备
```bash
# 确保在正确的目录
cd /Users/<USER>/Projects/litpage/litpage/api

# 检查依赖
pnpm install

# 验证测试环境
cd src/content/__tests__
npx ts-node validate-tests.ts
```

### 2. 测试验证
```bash
# 运行测试验证（应该显示100%验证通过）
./run-tests.sh unit
# 预期结果：测试失败（因为还没有实现ContentService）
```

## 🔴 Red阶段：运行失败的测试

### Step 1: 查看当前测试状态
```bash
# 运行ContentService单元测试
cd src/content/__tests__
./run-tests.sh unit

# 预期输出：
# ❌ ContentService is not defined
# ❌ 所有测试用例失败
```

### Step 2: 分析失败原因
当前失败原因：
- `ContentService` 类不存在
- `CreateContentDto` 接口不存在
- 相关模块和依赖未实现

## 🟢 Green阶段：实现最小可行代码

### Step 1: 创建基础目录结构
```bash
# 回到api根目录
cd /Users/<USER>/Projects/litpage/litpage/api

# 创建必要的目录
mkdir -p src/content/services
mkdir -p src/content/controllers  
mkdir -p src/content/dto
mkdir -p src/content/interfaces
```

### Step 2: 创建DTO定义
```typescript
// src/content/dto/create-content.dto.ts
import { IsString, IsOptional, IsEnum, IsObject, IsArray } from 'class-validator';
import { ContentType, Language } from '@prisma/client';

export class CreateContentDto {
  @IsEnum(ContentType)
  type: ContentType;

  @IsString()
  title: string;

  @IsString()
  slug: string;

  @IsOptional()
  @IsString()
  excerpt?: string;

  @IsObject()
  content: any;

  @IsObject()
  authorInfo: any;

  @IsOptional()
  @IsString()
  categoryId?: string;

  @IsOptional()
  @IsArray()
  tagIds?: string[];

  @IsOptional()
  @IsEnum(Language)
  language?: Language;
}
```

### Step 3: 创建ContentService基础实现
```typescript
// src/content/services/content.service.ts
import { Injectable, ConflictException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '@/prisma/prisma.service';
import { CreateContentDto } from '../dto/create-content.dto';
import { ContentStatus } from '@prisma/client';

@Injectable()
export class ContentService {
  constructor(private prisma: PrismaService) {}

  async createContent(data: CreateContentDto, userId: string, websiteId: string) {
    // 最小实现 - 让第一个测试通过
    return {
      id: 'mock-id',
      title: data.title,
      type: data.type,
      slug: data.slug,
      status: ContentStatus.DRAFT,
      userId,
      websiteId,
      workspaceId: 'mock-workspace-id',
      createdAt: new Date(),
      updatedAt: new Date(),
      authorInfo: data.authorInfo,
      // ... 其他必需字段
    };
  }

  async getContent(contentId: string, userId: string) {
    throw new Error('Method not implemented');
  }

  async updateContent(contentId: string, data: Partial<CreateContentDto>, userId: string) {
    throw new Error('Method not implemented');
  }

  async deleteContent(contentId: string, userId: string) {
    throw new Error('Method not implemented');
  }

  async publishContent(contentId: string, userId: string) {
    throw new Error('Method not implemented');
  }
}
```

### Step 4: 运行测试验证
```bash
cd src/content/__tests__
./run-tests.sh unit

# 预期结果：第一个测试通过，其他测试失败
# ✅ 应该成功创建博客内容
# ❌ 其他测试用例失败
```

## 🔵 Refactor阶段：逐步完善实现

### Step 1: 完善createContent方法
```typescript
async createContent(data: CreateContentDto, userId: string, websiteId: string) {
  // 1. 验证网站和用户权限
  const website = await this.prisma.website.findUnique({
    where: { id: websiteId },
    include: { workspace: true }
  });

  if (!website) {
    throw new BadRequestException('Website not found');
  }

  // 2. 检查slug重复
  const existingContent = await this.prisma.contentNode.findFirst({
    where: {
      slug: data.slug,
      websiteId,
      language: data.language || 'EN'
    }
  });

  if (existingContent) {
    throw new ConflictException('Content with this slug already exists');
  }

  // 3. 检查配额
  const quota = await this.prisma.quota.findUnique({
    where: { workspaceId: website.workspaceId }
  });

  if (quota && quota.contentsCreated >= quota.contentCreationLimit) {
    throw new BadRequestException('Content creation limit exceeded');
  }

  // 4. 创建内容
  return this.prisma.$transaction(async (tx) => {
    const content = await tx.contentNode.create({
      data: {
        type: data.type,
        title: data.title,
        slug: data.slug,
        excerpt: data.excerpt,
        authorInfo: data.authorInfo,
        status: ContentStatus.DRAFT,
        language: data.language || 'EN',
        userId,
        websiteId,
        workspaceId: website.workspaceId,
        categoryId: data.categoryId,
      }
    });

    // 创建初始版本
    const revision = await tx.contentRevision.create({
      data: {
        nodeId: content.id,
        createdById: userId,
        version: 1,
        data: data.content,
        changelog: '初始版本',
        isPublished: false,
      }
    });

    // 更新当前版本指针
    await tx.contentNode.update({
      where: { id: content.id },
      data: { currentRevisionId: revision.id }
    });

    // 处理标签关联
    if (data.tagIds && data.tagIds.length > 0) {
      await tx.contentNodeTag.createMany({
        data: data.tagIds.map(tagId => ({
          contentId: content.id,
          tagId,
        }))
      });
    }

    // 创建审计日志
    await tx.contentAuditLog.create({
      data: {
        contentId: content.id,
        userId,
        action: 'CREATED',
        revisionId: revision.id,
        metadata: {}
      }
    });

    // 更新配额
    if (quota) {
      await tx.quota.update({
        where: { workspaceId: website.workspaceId },
        data: { contentsCreated: quota.contentsCreated + 1 }
      });
    }

    return content;
  });
}
```

### Step 2: 逐个实现其他方法
按照测试用例的顺序，逐个实现：
1. `getContent` - 让查询测试通过
2. `updateContent` - 让更新测试通过
3. `deleteContent` - 让删除测试通过
4. `publishContent` - 让发布测试通过

### Step 3: 持续验证
每实现一个方法后：
```bash
./run-tests.sh unit
# 观察通过的测试数量增加
```

## 🎯 开发节奏建议

### Day 1: createContent完整实现
- 目标：让所有createContent相关测试通过
- 重点：权限验证、slug重复检查、配额限制

### Day 2: getContent实现
- 目标：让所有getContent相关测试通过
- 重点：权限验证、数据关联查询

### Day 3: updateContent实现
- 目标：让所有updateContent相关测试通过
- 重点：版本管理、权限验证

### Day 4: deleteContent和publishContent实现
- 目标：让所有剩余测试通过
- 重点：软删除、级联处理、状态管理

### Day 5: Controller实现和集成测试
- 目标：让所有集成测试通过
- 重点：API接口、错误处理、响应格式

## 📊 进度跟踪

### 测试通过率监控
```bash
# 每次实现后运行
./run-tests.sh unit | grep "✅\|❌" | wc -l

# 目标进度：
# Day 1: 7/24 测试通过 (createContent)
# Day 2: 10/24 测试通过 (+ getContent)  
# Day 3: 13/24 测试通过 (+ updateContent)
# Day 4: 24/24 测试通过 (所有Service测试)
# Day 5: 全部测试通过 (+ Controller测试)
```

### 覆盖率监控
```bash
./run-tests.sh coverage
# 目标：达到90%+覆盖率
```

## 🚨 常见问题解决

### 1. 测试运行失败
```bash
# 检查TypeScript编译错误
npx tsc --noEmit

# 检查依赖安装
pnpm install
```

### 2. Mock数据问题
```bash
# 重置Mock状态
# 在测试中确保调用 MockServicesFactory.resetAllMocks()
```

### 3. 数据库相关错误
```bash
# 确保使用Mock PrismaService
# 不要连接真实数据库
```

## 🎉 成功标志

当看到以下输出时，表示TDD第一轮成功：
```bash
./run-tests.sh all

# 预期输出：
# ✅ 所有单元测试通过 (24/24)
# ✅ 所有集成测试通过  
# ✅ 覆盖率达到90%+
# 🎉 TDD第一轮完成！
```

## 🚨 常见问题解决

### 1. 测试运行失败
```bash
# 检查TypeScript编译错误
npx tsc --noEmit

# 检查依赖安装
pnpm install
```

### 2. Mock数据问题
```bash
# 重置Mock状态
# 在测试中确保调用 MockServicesFactory.resetAllMocks()
```

### 3. 数据库相关错误
```bash
# 确保使用Mock PrismaService
# 不要连接真实数据库
```

## 🔄 TDD循环实践示例

### 示例：实现createContent方法

#### 🔴 Red - 运行失败测试
```bash
./run-tests.sh unit
# 输出：❌ 应该成功创建博客内容 - ContentService is not defined
```

#### 🟢 Green - 最小实现
```typescript
// 在 src/content/services/content.service.ts 中
async createContent(data: CreateContentDto, userId: string, websiteId: string) {
  // 最小实现，只让测试通过
  return MockDataFactory.createContentNode({
    title: data.title,
    type: data.type,
    slug: data.slug,
    userId,
    websiteId,
  });
}
```

#### 🔵 Refactor - 完善实现
```typescript
async createContent(data: CreateContentDto, userId: string, websiteId: string) {
  // 1. 验证权限
  const website = await this.prisma.website.findUnique({
    where: { id: websiteId },
    include: { workspace: { include: { members: true } } }
  });

  if (!website) {
    throw new BadRequestException('Website not found');
  }

  const isMember = website.workspace.members.some(m => m.userId === userId);
  if (!isMember) {
    throw new ForbiddenException('Insufficient permissions');
  }

  // 2. 检查slug重复
  const existing = await this.prisma.contentNode.findFirst({
    where: { slug: data.slug, websiteId, language: data.language || 'EN' }
  });

  if (existing) {
    throw new ConflictException('Content with this slug already exists');
  }

  // 3. 检查配额
  const quota = await this.prisma.quota.findUnique({
    where: { workspaceId: website.workspaceId }
  });

  if (quota?.contentsCreated >= quota?.contentCreationLimit) {
    throw new BadRequestException('Content creation limit exceeded');
  }

  // 4. 创建内容
  return this.prisma.$transaction(async (tx) => {
    const content = await tx.contentNode.create({
      data: {
        type: data.type,
        title: data.title,
        slug: data.slug,
        excerpt: data.excerpt,
        authorInfo: data.authorInfo,
        status: ContentStatus.DRAFT,
        language: data.language || 'EN',
        userId,
        websiteId,
        workspaceId: website.workspaceId,
        categoryId: data.categoryId,
      }
    });

    // 创建初始版本
    const revision = await tx.contentRevision.create({
      data: {
        nodeId: content.id,
        createdById: userId,
        version: 1,
        data: data.content,
        changelog: '初始版本',
        isPublished: false,
      }
    });

    // 更新当前版本指针
    await tx.contentNode.update({
      where: { id: content.id },
      data: { currentRevisionId: revision.id }
    });

    // 处理标签关联
    if (data.tagIds?.length > 0) {
      await tx.contentNodeTag.createMany({
        data: data.tagIds.map(tagId => ({
          contentId: content.id,
          tagId,
        }))
      });
    }

    // 创建审计日志
    await tx.contentAuditLog.create({
      data: {
        contentId: content.id,
        userId,
        action: 'CREATED',
        revisionId: revision.id,
        metadata: { initialData: { type: data.type, title: data.title } }
      }
    });

    // 更新配额
    if (quota) {
      await tx.quota.update({
        where: { workspaceId: website.workspaceId },
        data: { contentsCreated: quota.contentsCreated + 1 }
      });
    }

    return content;
  });
}
```

#### 验证结果
```bash
./run-tests.sh unit
# 输出：✅ 应该成功创建博客内容
#       ✅ 应该在slug重复时抛出ConflictException错误
#       ✅ 应该在权限不足时抛出ForbiddenException错误
#       ✅ 应该在配额超限时抛出BadRequestException错误
```

## 📈 实施里程碑

### 里程碑1：基础CRUD (Day 1-4)
- [ ] createContent 完整实现
- [ ] getContent 完整实现
- [ ] updateContent 完整实现
- [ ] deleteContent 完整实现
- [ ] publishContent 完整实现
- [ ] 所有单元测试通过

### 里程碑2：API集成 (Day 5)
- [ ] ContentController 实现
- [ ] 所有集成测试通过
- [ ] 错误处理完善
- [ ] 响应格式标准化

### 里程碑3：质量保证 (Day 6-7)
- [ ] 代码覆盖率 ≥ 90%
- [ ] 性能测试通过
- [ ] 代码Review完成
- [ ] 文档更新

## 🎯 最终验收标准

### 功能完整性
```bash
# 所有测试通过
./run-tests.sh all
# 预期：56个验证点全部通过

# 覆盖率达标
./run-tests.sh coverage
# 预期：≥90% 覆盖率
```

### 代码质量
- 遵循Airbnb Style Guide
- 所有方法都有对应测试
- 错误处理完善
- 类型安全（无any类型）

### 性能要求
- 单个API响应时间 < 200ms
- 并发处理能力 > 100 req/s
- 内存使用稳定

现在开始你的TDD之旅吧！🚀

---

## 📞 支持和帮助

如果在实施过程中遇到问题：

1. **查看测试输出** - 测试失败信息通常很明确
2. **检查Mock配置** - 确保Mock数据符合预期
3. **参考测试用例** - 测试用例就是最好的需求文档
4. **运行验证脚本** - `npx ts-node validate-tests.ts`

记住：**测试先行，小步迭代，持续重构** 🎯
