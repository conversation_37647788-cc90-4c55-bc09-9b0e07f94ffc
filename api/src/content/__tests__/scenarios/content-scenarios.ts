import { MockDataFactory } from '../factories/mock-data.factory';
import {
  ContentType,
  ContentStatus,
  CategoryType,
  Language,
  ContentAuditAction,
} from '@prisma/client';

/**
 * 内容测试场景数据类
 * 基于TDD策略文档中的场景化Mock数据设计
 */
export class ContentTestScenarios {
  /**
   * 内容生命周期场景
   */
  static contentLifecycle = {
    // 草稿状态内容
    draftContent: MockDataFactory.createContentNode({
      id: 'draft-content-id',
      title: '草稿状态的博客文章',
      slug: 'draft-blog-post',
      status: ContentStatus.DRAFT,
      publishedAt: null,
      type: ContentType.BLOG,
    }),

    // 已发布内容
    publishedContent: MockDataFactory.createContentNode({
      id: 'published-content-id',
      title: '已发布的博客文章',
      slug: 'published-blog-post',
      status: ContentStatus.PUBLISHED,
      publishedAt: new Date('2024-01-15T10:00:00Z'),
      type: ContentType.BLOG,
    }),

    // 已归档内容
    archivedContent: MockDataFactory.createContentNode({
      id: 'archived-content-id',
      title: '已归档的博客文章',
      slug: 'archived-blog-post',
      status: ContentStatus.ARCHIVED,
      publishedAt: new Date('2024-01-10T10:00:00Z'),
      type: ContentType.BLOG,
    }),
  };

  /**
   * 权限场景数据
   */
  static permissions = {
    // 工作空间所有者
    workspaceOwner: MockDataFactory.createUser({
      id: 'workspace-owner-id',
      name: '工作空间所有者',
      email: '<EMAIL>',
    }),

    // 工作空间成员
    workspaceMember: MockDataFactory.createUser({
      id: 'workspace-member-id',
      name: '工作空间成员',
      email: '<EMAIL>',
    }),

    // 外部用户
    outsideUser: MockDataFactory.createUser({
      id: 'outside-user-id',
      name: '外部用户',
      email: '<EMAIL>',
    }),

    // 内容创建者
    contentCreator: MockDataFactory.createUser({
      id: 'content-creator-id',
      name: '内容创建者',
      email: '<EMAIL>',
    }),

    // 测试工作空间
    testWorkspace: MockDataFactory.createWorkspace({
      id: 'test-workspace-id',
      name: '测试工作空间',
      userId: 'workspace-owner-id',
    }),

    // 测试网站
    testWebsite: MockDataFactory.createWebsite({
      id: 'test-website-id',
      name: '测试网站',
      domain: 'test-site.lit.page',
      workspaceId: 'test-workspace-id',
      userId: 'workspace-owner-id',
    }),
  };

  /**
   * 版本管理场景
   */
  static versioning = {
    // 包含多个版本的内容
    contentWithMultipleRevisions: {
      content: MockDataFactory.createContentNode({
        id: 'content-with-versions',
        title: '多版本内容',
        slug: 'multi-version-content',
        currentRevisionId: 'revision-3',
        status: ContentStatus.PUBLISHED,
      }),

      revisions: [
        MockDataFactory.createContentRevision({
          id: 'revision-1',
          nodeId: 'content-with-versions',
          version: 1,
          isPublished: false,
          changelog: '初始版本',
        }),

        MockDataFactory.createContentRevision({
          id: 'revision-2',
          nodeId: 'content-with-versions',
          version: 2,
          isPublished: true,
          changelog: '发布版本',
          publishedAt: new Date('2024-01-10T10:00:00Z'),
        }),

        MockDataFactory.createContentRevision({
          id: 'revision-3',
          nodeId: 'content-with-versions',
          version: 3,
          isPublished: false,
          changelog: '修复错误',
        }),
      ],
    },
  };

  /**
   * 分类和标签场景
   */
  static categoriesAndTags = {
    // 分类树结构
    categoryTree: [
      MockDataFactory.createCategory({
        id: 'cat-tech',
        name: '技术',
        slug: 'tech',
        parentId: null,
        order: 1,
        type: CategoryType.GENERAL,
      }),

      MockDataFactory.createCategory({
        id: 'cat-frontend',
        name: '前端开发',
        slug: 'frontend',
        parentId: 'cat-tech',
        order: 1,
        type: CategoryType.BLOG,
      }),
    ],

    // 热门标签
    popularTags: [
      MockDataFactory.createTag({
        id: 'tag-react',
        name: 'React',
        color: '#61DAFB',
        description: 'React相关内容',
      }),

      MockDataFactory.createTag({
        id: 'tag-typescript',
        name: 'TypeScript',
        color: '#3178C6',
        description: 'TypeScript相关内容',
      }),
    ],
  };

  /**
   * 复杂业务场景
   */
  static complexScenarios = {
    // 完整的博客文章（包含分类和标签）
    blogWithCategoryAndTags: {
      content: MockDataFactory.createContentNode({
        id: 'blog-with-relations',
        type: ContentType.BLOG,
        title: '深入理解React Hooks',
        slug: 'understanding-react-hooks',
        excerpt: '本文将深入探讨React Hooks的原理和最佳实践',
        categoryId: 'cat-frontend',
        status: ContentStatus.PUBLISHED,
        publishedAt: new Date('2024-01-15T10:00:00Z'),
        authorInfo: {
          name: '张三',
          avatar: '/avatars/zhangsan.jpg',
          bio: '资深前端开发工程师，专注React生态',
          email: '<EMAIL>',
          social: {
            twitter: '@zhangsan_dev',
            github: 'zhangsan',
          },
          organization: {
            name: 'ABC科技有限公司',
            role: '高级前端工程师',
          },
        },
      }),

      category: MockDataFactory.createCategory({
        id: 'cat-frontend',
        name: '前端开发',
        slug: 'frontend',
        type: CategoryType.BLOG,
      }),

      tags: [
        MockDataFactory.createTag({
          id: 'tag-react',
          name: 'React',
          color: '#61DAFB',
        }),
        MockDataFactory.createTag({
          id: 'tag-typescript',
          name: 'TypeScript',
          color: '#3178C6',
        }),
      ],

      tagRelations: [
        MockDataFactory.createContentNodeTag({
          contentId: 'blog-with-relations',
          tagId: 'tag-react',
        }),
        MockDataFactory.createContentNodeTag({
          contentId: 'blog-with-relations',
          tagId: 'tag-typescript',
        }),
      ],

      revision: MockDataFactory.createContentRevision({
        id: 'blog-revision-1',
        nodeId: 'blog-with-relations',
        version: 1,
        isPublished: true,
        changelog: '初始发布版本',
        data: {
          blocks: [
            {
              type: 'header',
              data: {
                text: '深入理解React Hooks',
                level: 1,
              },
            },
            {
              type: 'paragraph',
              data: {
                text: 'React Hooks是React 16.8引入的新特性...',
              },
            },
          ],
        },
      }),
    },

    // 配额限制场景
    quotaScenarios: {
      // 配额已满的工作空间
      fullQuotaWorkspace: {
        quota: {
          id: 'quota-full',
          workspaceId: 'test-workspace-id',
          contentsCreated: 100,
          contentCreationLimit: 100,
          contentRevisionsLimit: 1000,
        },
      },

      // 配额正常的工作空间
      normalQuotaWorkspace: {
        quota: {
          id: 'quota-normal',
          workspaceId: 'test-workspace-id',
          contentsCreated: 50,
          contentCreationLimit: 100,
          contentRevisionsLimit: 1000,
        },
      },
    },
  };

  /**
   * 错误场景数据
   */
  static errorScenarios = {
    // 重复slug场景
    duplicateSlug: {
      existingContent: MockDataFactory.createContentNode({
        id: 'existing-content',
        slug: 'duplicate-slug',
        websiteId: 'test-website-id',
        language: Language.EN,
      }),
    },

    // 不存在的资源
    nonExistentResources: {
      contentId: 'non-existent-content-id',
      websiteId: 'non-existent-website-id',
      userId: 'non-existent-user-id',
    },
  };
}
