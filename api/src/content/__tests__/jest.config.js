/**
 * Jest配置文件 - 专门用于CMS内容模块测试
 * 基于TDD策略文档中的Jest配置设计
 */

module.exports = {
  // 基础配置
  displayName: 'CMS Content Module Tests',
  testEnvironment: 'node',
  
  // 文件扩展名
  moduleFileExtensions: ['js', 'json', 'ts'],
  
  // 测试文件匹配模式
  testMatch: [
    '<rootDir>/**/*.spec.ts',
    '<rootDir>/**/*.integration.spec.ts',
  ],
  
  // TypeScript转换
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  
  // 模块路径映射
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/../../../$1',
    '^@test/(.*)$': '<rootDir>/$1',
  },
  
  // 测试覆盖率配置
  collectCoverageFrom: [
    '../**/*.(t|j)s',
    '!../**/*.spec.ts',
    '!../**/*.integration.spec.ts',
    '!../**/*.interface.ts',
    '!../**/*.dto.ts',
    '!../**/*.module.ts',
    '!**/node_modules/**',
    '!**/__tests__/**',
  ],
  
  // 覆盖率输出目录
  coverageDirectory: '<rootDir>/coverage',
  
  // 覆盖率报告格式
  coverageReporters: [
    'text',
    'text-summary',
    'html',
    'lcov',
    'json',
  ],
  
  // 覆盖率阈值（基于TDD策略文档要求）
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 90,
      lines: 90,
      statements: 90,
    },
    // CMS核心模块要求更高覆盖率
    '../services/': {
      branches: 90,
      functions: 95,
      lines: 95,
      statements: 95,
    },
    '../controllers/': {
      branches: 85,
      functions: 90,
      lines: 90,
      statements: 90,
    },
  },
  
  // 测试设置文件
  setupFilesAfterEnv: [
    '<rootDir>/setup/test-setup.ts',
  ],
  
  // 测试超时时间
  testTimeout: 10000,
  
  // 清理Mock
  clearMocks: true,
  restoreMocks: true,
  
  // 详细输出
  verbose: true,
  
  // 错误时停止
  bail: false,
  
  // 最大并发数
  maxConcurrency: 5,
  
  // 全局变量
  globals: {
    'ts-jest': {
      tsconfig: {
        compilerOptions: {
          module: 'commonjs',
          target: 'es2020',
          experimentalDecorators: true,
          emitDecoratorMetadata: true,
          allowSyntheticDefaultImports: true,
          esModuleInterop: true,
          skipLibCheck: true,
          strict: true,
        },
      },
    },
  },
  
  // 忽略的路径
  testPathIgnorePatterns: [
    '/node_modules/',
    '/dist/',
    '/coverage/',
  ],
  
  // 监视模式忽略的路径
  watchPathIgnorePatterns: [
    '/node_modules/',
    '/dist/',
    '/coverage/',
  ],
  
  // 测试结果处理器
  reporters: [
    'default',
    [
      'jest-junit',
      {
        outputDirectory: '<rootDir>/coverage',
        outputName: 'junit.xml',
        suiteName: 'CMS Content Module Tests',
      },
    ],
  ],
  
  // 自定义匹配器
  setupFilesAfterEnv: [
    '<rootDir>/setup/test-setup.ts',
    'jest-extended/all',
  ],
};
