import { Test, TestingModule } from '@nestjs/testing';
import { ForbiddenException, ConflictException, BadRequestException } from '@nestjs/common';
import { MockServicesFactory } from '../../factories/mock-services.factory';
import { MockDataFactory } from '../../factories/mock-data.factory';
import { ContentTestScenarios } from '../../scenarios/content-scenarios';
import { TestUtils, TestAssertions } from '../../setup/test-setup';
import {
  ContentType,
  ContentStatus,
  ContentAuditAction,
  Language,
} from '@prisma/client';

// 这里我们先定义接口，实际的ContentService将在后续实现
interface CreateContentDto {
  type: ContentType;
  title: string;
  slug: string;
  excerpt?: string;
  content: any;
  authorInfo: any;
  categoryId?: string;
  tagIds?: string[];
  language?: Language;
}

interface ContentService {
  createContent(data: CreateContentDto, userId: string, websiteId: string): Promise<any>;
  getContent(contentId: string, userId: string): Promise<any>;
  updateContent(contentId: string, data: Partial<CreateContentDto>, userId: string): Promise<any>;
  deleteContent(contentId: string, userId: string): Promise<void>;
  publishContent(contentId: string, userId: string): Promise<any>;
}

// Mock PrismaService类型定义
interface PrismaService {
  contentNode: any;
  contentRevision: any;
  category: any;
  tag: any;
  contentNodeTag: any;
  contentAuditLog: any;
  user: any;
  website: any;
  workspace: any;
  quota: any;
  $transaction: any;
}

/**
 * ContentService 单元测试
 * 基于TDD策略文档中的测试用例设计
 * 
 * 测试覆盖范围：
 * - 内容创建功能
 * - 内容查询功能  
 * - 内容更新功能
 * - 内容删除功能
 * - 版本管理功能
 */
describe('ContentService', () => {
  let service: ContentService;
  let prisma: jest.Mocked<PrismaService>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        // ContentService, // 将在实际实现时添加
        {
          provide: 'PrismaService',
          useValue: MockServicesFactory.createMockPrismaService(),
        },
      ],
    }).compile();

    // service = module.get<ContentService>(ContentService); // 将在实际实现时启用
    prisma = module.get<PrismaService>('PrismaService') as jest.Mocked<PrismaService>;
  });

  afterEach(() => {
    MockServicesFactory.resetAllMocks();
  });

  /**
   * 内容创建测试套件
   */
  describe('createContent', () => {
    const createContentDto: CreateContentDto = {
      type: ContentType.BLOG,
      title: '测试博客文章',
      slug: 'test-blog-post',
      excerpt: '这是一篇测试博客文章的摘要',
      content: {
        blocks: [
          {
            type: 'paragraph',
            data: { text: '文章内容...' },
          },
        ],
      },
      authorInfo: {
        name: '张三',
        avatar: '/avatars/zhangsan.jpg',
        bio: '资深前端开发工程师',
        email: '<EMAIL>',
        social: {
          twitter: '@zhangsan_dev',
          github: 'zhangsan',
        },
      },
      categoryId: 'category-1',
      tagIds: ['tag-1', 'tag-2'],
      language: Language.EN,
    };

    it('应该成功创建博客内容', async () => {
      // Given: Mock数据准备
      const mockUser = ContentTestScenarios.permissions.contentCreator;
      const mockWebsite = ContentTestScenarios.permissions.testWebsite;
      const mockWorkspace = ContentTestScenarios.permissions.testWorkspace;
      const mockCreatedContent = MockDataFactory.createContentNode({
        title: createContentDto.title,
        type: createContentDto.type,
        slug: createContentDto.slug,
        status: ContentStatus.DRAFT,
        userId: mockUser.id,
        websiteId: mockWebsite.id,
        workspaceId: mockWorkspace.id,
      });
      const mockRevision = MockDataFactory.createContentRevision({
        nodeId: mockCreatedContent.id,
        version: 1,
        data: createContentDto.content,
        createdById: mockUser.id,
      });

      // Mock数据库调用
      prisma.website.findUnique.mockResolvedValue({
        ...mockWebsite,
        workspace: mockWorkspace,
      } as any);
      prisma.user.findUnique.mockResolvedValue(mockUser as any);
      prisma.contentNode.findFirst.mockResolvedValue(null); // slug不重复
      prisma.quota.findUnique.mockResolvedValue({
        contentsCreated: 50,
        contentCreationLimit: 100,
      } as any);

      // Mock事务操作
      prisma.$transaction.mockImplementation(async (callback: any) => {
        return callback(prisma);
      });

      prisma.contentNode.create.mockResolvedValue(mockCreatedContent as any);
      prisma.contentRevision.create.mockResolvedValue(mockRevision as any);
      prisma.contentNodeTag.createMany.mockResolvedValue({ count: 2 } as any);
      prisma.contentAuditLog.create.mockResolvedValue({} as any);
      prisma.quota.update.mockResolvedValue({} as any);

      // When: 调用创建方法 (这里先用Mock实现)
      const mockCreateContent = jest.fn().mockResolvedValue(mockCreatedContent);
      const result = await mockCreateContent(createContentDto, mockUser.id, mockWebsite.id);

      // Then: 验证结果
      expect(result).toMatchObject({
        title: '测试博客文章',
        type: ContentType.BLOG,
        status: ContentStatus.DRAFT,
        slug: 'test-blog-post',
        userId: mockUser.id,
        websiteId: mockWebsite.id,
      });

      // 验证基本结构
      TestAssertions.expectValidContentNode(result);

      // 验证UUID格式
      expect(result.id).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i);
    });

    it('应该在slug重复时抛出ConflictException错误', async () => {
      // Given: 已存在相同slug的内容
      const existingContent = ContentTestScenarios.errorScenarios.duplicateSlug.existingContent;
      const mockWebsite = ContentTestScenarios.permissions.testWebsite;

      prisma.website.findUnique.mockResolvedValue(mockWebsite as any);
      prisma.contentNode.findFirst.mockResolvedValue(existingContent as any);

      // When & Then: 验证错误抛出
      const mockCreateContent = jest.fn().mockRejectedValue(
        new ConflictException('Content with this slug already exists')
      );

      await expect(
        mockCreateContent(createContentDto, 'user-1', 'website-1')
      ).rejects.toThrow(ConflictException);

      await expect(
        mockCreateContent(createContentDto, 'user-1', 'website-1')
      ).rejects.toThrow('Content with this slug already exists');
    });

    it('应该在权限不足时抛出ForbiddenException错误', async () => {
      // Given: 用户不是工作空间成员
      const mockWebsite = ContentTestScenarios.permissions.testWebsite;
      const outsideUser = ContentTestScenarios.permissions.outsideUser;

      prisma.website.findUnique.mockResolvedValue({
        ...mockWebsite,
        workspace: {
          members: [], // 空成员列表，表示用户不是成员
        },
      } as any);

      // When & Then: 验证权限错误
      const mockCreateContent = jest.fn().mockRejectedValue(
        new ForbiddenException('Insufficient permissions')
      );

      await expect(
        mockCreateContent(createContentDto, outsideUser.id, mockWebsite.id)
      ).rejects.toThrow(ForbiddenException);

      await expect(
        mockCreateContent(createContentDto, outsideUser.id, mockWebsite.id)
      ).rejects.toThrow('Insufficient permissions');
    });

    it('应该在配额超限时抛出BadRequestException错误', async () => {
      // Given: 配额已满
      const mockWebsite = ContentTestScenarios.permissions.testWebsite;
      const mockUser = ContentTestScenarios.permissions.contentCreator;
      const fullQuota = ContentTestScenarios.complexScenarios.quotaScenarios.fullQuotaWorkspace.quota;

      prisma.website.findUnique.mockResolvedValue(mockWebsite as any);
      prisma.user.findUnique.mockResolvedValue(mockUser as any);
      prisma.contentNode.findFirst.mockResolvedValue(null);
      prisma.quota.findUnique.mockResolvedValue(fullQuota as any);

      // When & Then: 验证配额错误
      const mockCreateContent = jest.fn().mockRejectedValue(
        new BadRequestException('Content creation limit exceeded')
      );

      await expect(
        mockCreateContent(createContentDto, mockUser.id, mockWebsite.id)
      ).rejects.toThrow(BadRequestException);

      await expect(
        mockCreateContent(createContentDto, mockUser.id, mockWebsite.id)
      ).rejects.toThrow('Content creation limit exceeded');
    });

    it('应该在标题为空时抛出验证错误', async () => {
      // Given: 无效的请求数据
      const invalidDto = {
        ...createContentDto,
        title: '', // 空标题
      };

      // When & Then: 验证数据验证错误
      const mockCreateContent = jest.fn().mockRejectedValue(
        new BadRequestException('Title is required')
      );

      await expect(
        mockCreateContent(invalidDto, 'user-1', 'website-1')
      ).rejects.toThrow(BadRequestException);

      await expect(
        mockCreateContent(invalidDto, 'user-1', 'website-1')
      ).rejects.toThrow('Title is required');
    });

    it('应该在authorInfo格式无效时抛出验证错误', async () => {
      // Given: 无效的authorInfo
      const invalidDto = {
        ...createContentDto,
        authorInfo: {
          // 缺少必需的name字段
          bio: '测试简介',
        },
      };

      // When & Then: 验证authorInfo验证错误
      const mockCreateContent = jest.fn().mockRejectedValue(
        new BadRequestException('Author name is required')
      );

      await expect(
        mockCreateContent(invalidDto, 'user-1', 'website-1')
      ).rejects.toThrow(BadRequestException);

      await expect(
        mockCreateContent(invalidDto, 'user-1', 'website-1')
      ).rejects.toThrow('Author name is required');
    });

    it('应该成功创建不同类型的内容', async () => {
      // Given: 不同类型的内容
      const contentTypes = [
        ContentType.BLOG,
        ContentType.DOC,
        ContentType.FAQ,
        ContentType.CHANGELOG,
      ];

      for (const type of contentTypes) {
        const typeSpecificDto = {
          ...createContentDto,
          type,
          title: `测试${type}内容`,
          slug: `test-${type.toLowerCase()}-content`,
        };

        const mockContent = MockDataFactory.createContentNode({
          type,
          title: typeSpecificDto.title,
          slug: typeSpecificDto.slug,
        });

        // When: 创建不同类型的内容
        const mockCreateContent = jest.fn().mockResolvedValue(mockContent);
        const result = await mockCreateContent(typeSpecificDto, 'user-1', 'website-1');

        // Then: 验证内容类型正确
        expect(result.type).toBe(type);
        expect(result.title).toBe(typeSpecificDto.title);
        expect(result.slug).toBe(typeSpecificDto.slug);
      }
    });
  });

  /**
   * 内容查询测试套件
   */
  describe('getContent', () => {
    it('应该成功获取内容详情', async () => {
      // Given: 存在的内容
      const mockContent = ContentTestScenarios.contentLifecycle.publishedContent;
      const mockUser = ContentTestScenarios.permissions.contentCreator;
      const mockRevision = MockDataFactory.createContentRevision({
        nodeId: mockContent.id,
        isPublished: true,
      });

      prisma.contentNode.findUnique.mockResolvedValue({
        ...mockContent,
        currentRevision: mockRevision,
        category: ContentTestScenarios.categoriesAndTags.categoryTree[0],
        tags: [
          {
            tag: ContentTestScenarios.categoriesAndTags.popularTags[0],
          },
        ],
        workspace: {
          members: [{ userId: mockUser.id }],
        },
      } as any);

      // When: 获取内容详情
      const mockGetContent = jest.fn().mockResolvedValue({
        ...mockContent,
        currentRevision: mockRevision,
      });
      const result = await mockGetContent(mockContent.id, mockUser.id);

      // Then: 验证结果
      expect(result).toMatchObject({
        id: mockContent.id,
        title: mockContent.title,
        status: ContentStatus.PUBLISHED,
      });

      TestAssertions.expectValidContentNode(result);
    });

    it('应该在内容不存在时抛出NotFound错误', async () => {
      // Given: 不存在的内容ID
      const nonExistentId = ContentTestScenarios.errorScenarios.nonExistentResources.contentId;

      prisma.contentNode.findUnique.mockResolvedValue(null);

      // When & Then: 验证错误抛出
      const mockGetContent = jest.fn().mockRejectedValue(
        new Error('Content not found')
      );

      await expect(
        mockGetContent(nonExistentId, 'user-1')
      ).rejects.toThrow('Content not found');
    });

    it('应该在权限不足时拒绝访问', async () => {
      // Given: 用户无权访问的内容
      const mockContent = ContentTestScenarios.contentLifecycle.publishedContent;
      const outsideUser = ContentTestScenarios.permissions.outsideUser;

      prisma.contentNode.findUnique.mockResolvedValue({
        ...mockContent,
        workspace: {
          members: [], // 用户不是成员
        },
      } as any);

      // When & Then: 验证权限错误
      const mockGetContent = jest.fn().mockRejectedValue(
        new ForbiddenException('Access denied')
      );

      await expect(
        mockGetContent(mockContent.id, outsideUser.id)
      ).rejects.toThrow(ForbiddenException);
    });
  });

  /**
   * 内容更新测试套件
   */
  describe('updateContent', () => {
    const updateData = {
      title: '更新后的标题',
      excerpt: '更新后的摘要',
      authorInfo: {
        name: '李四',
        bio: '更新后的简介',
      },
      categoryId: 'new-category-id',
      tagIds: ['new-tag-1', 'new-tag-2'],
    };

    it('应该成功更新内容基本信息', async () => {
      // Given: 存在的内容和有权限的用户
      const mockContent = ContentTestScenarios.contentLifecycle.draftContent;
      const mockUser = ContentTestScenarios.permissions.contentCreator;
      const updatedContent = {
        ...mockContent,
        ...updateData,
        updatedAt: new Date(),
      };

      prisma.contentNode.findUnique.mockResolvedValue({
        ...mockContent,
        userId: mockUser.id, // 用户是内容创建者
        workspace: {
          members: [{ userId: mockUser.id }],
        },
      } as any);

      prisma.$transaction.mockImplementation(async (callback: any) => {
        return callback(prisma);
      });

      prisma.contentNode.update.mockResolvedValue(updatedContent as any);
      prisma.contentAuditLog.create.mockResolvedValue({} as any);

      // When: 更新内容
      const mockUpdateContent = jest.fn().mockResolvedValue(updatedContent);
      const result = await mockUpdateContent(mockContent.id, updateData, mockUser.id);

      // Then: 验证更新结果
      expect(result).toMatchObject({
        id: mockContent.id,
        title: updateData.title,
        excerpt: updateData.excerpt,
      });

      TestAssertions.expectValidContentNode(result);
    });

    it('应该在更新时自动创建新版本', async () => {
      // Given: 已发布的内容需要更新
      const mockContent = ContentTestScenarios.contentLifecycle.publishedContent;
      const mockUser = ContentTestScenarios.permissions.contentCreator;

      prisma.contentNode.findUnique.mockResolvedValue({
        ...mockContent,
        userId: mockUser.id,
        workspace: { members: [{ userId: mockUser.id }] },
      } as any);

      prisma.contentRevision.findMany.mockResolvedValue([
        { version: 1 }, { version: 2 }
      ] as any);

      prisma.$transaction.mockImplementation(async (callback: any) => {
        return callback(prisma);
      });

      const newRevision = MockDataFactory.createContentRevision({
        nodeId: mockContent.id,
        version: 3,
        changelog: '内容更新',
      });

      prisma.contentRevision.create.mockResolvedValue(newRevision as any);
      prisma.contentNode.update.mockResolvedValue({
        ...mockContent,
        currentRevisionId: newRevision.id,
      } as any);

      // When: 更新已发布的内容
      const mockUpdateContent = jest.fn().mockResolvedValue({
        ...mockContent,
        currentRevisionId: newRevision.id,
      });
      const result = await mockUpdateContent(mockContent.id, updateData, mockUser.id);

      // Then: 验证新版本创建
      expect(result.currentRevisionId).toBe(newRevision.id);
    });

    it('应该在非创建者更新时抛出权限错误', async () => {
      // Given: 非创建者尝试更新内容
      const mockContent = ContentTestScenarios.contentLifecycle.draftContent;
      const otherUser = ContentTestScenarios.permissions.workspaceMember;

      prisma.contentNode.findUnique.mockResolvedValue({
        ...mockContent,
        userId: 'other-creator-id', // 不是当前用户创建的
        workspace: {
          members: [{ userId: otherUser.id }], // 但是工作空间成员
        },
      } as any);

      // When & Then: 验证权限错误
      const mockUpdateContent = jest.fn().mockRejectedValue(
        new ForbiddenException('Only content creator can update this content')
      );

      await expect(
        mockUpdateContent(mockContent.id, updateData, otherUser.id)
      ).rejects.toThrow(ForbiddenException);
    });
  });

  /**
   * 内容删除测试套件
   */
  describe('deleteContent', () => {
    it('应该成功软删除内容', async () => {
      // Given: 存在的内容和有权限的用户
      const mockContent = ContentTestScenarios.contentLifecycle.draftContent;
      const mockUser = ContentTestScenarios.permissions.contentCreator;

      prisma.contentNode.findUnique.mockResolvedValue({
        ...mockContent,
        userId: mockUser.id,
        workspace: {
          members: [{ userId: mockUser.id }],
        },
      } as any);

      prisma.$transaction.mockImplementation(async (callback: any) => {
        return callback(prisma);
      });

      prisma.contentNode.update.mockResolvedValue({
        ...mockContent,
        status: ContentStatus.ARCHIVED,
      } as any);
      prisma.contentAuditLog.create.mockResolvedValue({} as any);

      // When: 删除内容
      const mockDeleteContent = jest.fn().mockResolvedValue(undefined);
      await mockDeleteContent(mockContent.id, mockUser.id);

      // Then: 验证删除成功（无异常抛出）
      expect(mockDeleteContent).toHaveBeenCalledWith(mockContent.id, mockUser.id);
    });

    it('应该在删除时级联处理相关数据', async () => {
      // Given: 包含关联数据的内容
      const mockContent = ContentTestScenarios.complexScenarios.blogWithCategoryAndTags.content;
      const mockUser = ContentTestScenarios.permissions.contentCreator;

      prisma.contentNode.findUnique.mockResolvedValue({
        ...mockContent,
        userId: mockUser.id,
        workspace: { members: [{ userId: mockUser.id }] },
        tags: [
          { tagId: 'tag-1' },
          { tagId: 'tag-2' },
        ],
      } as any);

      prisma.$transaction.mockImplementation(async (callback: any) => {
        return callback(prisma);
      });

      prisma.contentNodeTag.deleteMany.mockResolvedValue({ count: 2 } as any);
      prisma.contentNode.update.mockResolvedValue({} as any);

      // When: 删除包含关联数据的内容
      const mockDeleteContent = jest.fn().mockResolvedValue(undefined);
      await mockDeleteContent(mockContent.id, mockUser.id);

      // Then: 验证级联删除
      expect(mockDeleteContent).toHaveBeenCalledWith(mockContent.id, mockUser.id);
    });

    it('应该在权限不足时拒绝删除', async () => {
      // Given: 用户无权删除的内容
      const mockContent = ContentTestScenarios.contentLifecycle.publishedContent;
      const outsideUser = ContentTestScenarios.permissions.outsideUser;

      prisma.contentNode.findUnique.mockResolvedValue({
        ...mockContent,
        userId: 'other-user-id',
        workspace: {
          members: [], // 用户不是成员
        },
      } as any);

      // When & Then: 验证权限错误
      const mockDeleteContent = jest.fn().mockRejectedValue(
        new ForbiddenException('Insufficient permissions to delete this content')
      );

      await expect(
        mockDeleteContent(mockContent.id, outsideUser.id)
      ).rejects.toThrow(ForbiddenException);
    });
  });

  /**
   * 版本管理测试套件
   */
  describe('publishContent', () => {
    it('应该成功发布草稿内容', async () => {
      // Given: 草稿状态的内容
      const mockContent = ContentTestScenarios.contentLifecycle.draftContent;
      const mockUser = ContentTestScenarios.permissions.contentCreator;
      const mockRevision = MockDataFactory.createContentRevision({
        id: 'revision-1',
        nodeId: mockContent.id,
        isPublished: false,
        version: 1,
      });

      prisma.contentNode.findUnique.mockResolvedValue({
        ...mockContent,
        currentRevisionId: mockRevision.id,
        currentRevision: mockRevision,
        userId: mockUser.id,
        workspace: {
          members: [{ userId: mockUser.id }],
        },
      } as any);

      prisma.$transaction.mockImplementation(async (callback: any) => {
        return callback(prisma);
      });

      const publishedContent = {
        ...mockContent,
        status: ContentStatus.PUBLISHED,
        publishedAt: new Date(),
      };

      prisma.contentNode.update.mockResolvedValue(publishedContent as any);
      prisma.contentRevision.update.mockResolvedValue({
        ...mockRevision,
        isPublished: true,
        publishedAt: new Date(),
      } as any);
      prisma.contentAuditLog.create.mockResolvedValue({} as any);

      // When: 发布内容
      const mockPublishContent = jest.fn().mockResolvedValue(publishedContent);
      const result = await mockPublishContent(mockContent.id, mockUser.id);

      // Then: 验证发布结果
      expect(result).toMatchObject({
        id: mockContent.id,
        status: ContentStatus.PUBLISHED,
        publishedAt: expect.any(Date),
      });

      TestAssertions.expectValidContentNode(result);
    });

    it('应该在内容已发布时抛出错误', async () => {
      // Given: 已发布的内容
      const mockContent = ContentTestScenarios.contentLifecycle.publishedContent;
      const mockUser = ContentTestScenarios.permissions.contentCreator;

      prisma.contentNode.findUnique.mockResolvedValue({
        ...mockContent,
        userId: mockUser.id,
        workspace: {
          members: [{ userId: mockUser.id }],
        },
      } as any);

      // When & Then: 验证错误处理
      const mockPublishContent = jest.fn().mockRejectedValue(
        new BadRequestException('Content is already published')
      );

      await expect(
        mockPublishContent(mockContent.id, mockUser.id)
      ).rejects.toThrow(BadRequestException);

      await expect(
        mockPublishContent(mockContent.id, mockUser.id)
      ).rejects.toThrow('Content is already published');
    });

    it('应该在没有当前版本时抛出错误', async () => {
      // Given: 没有当前版本的内容
      const mockContent = {
        ...ContentTestScenarios.contentLifecycle.draftContent,
        currentRevisionId: null,
        currentRevision: null,
      };
      const mockUser = ContentTestScenarios.permissions.contentCreator;

      prisma.contentNode.findUnique.mockResolvedValue({
        ...mockContent,
        userId: mockUser.id,
        workspace: {
          members: [{ userId: mockUser.id }],
        },
      } as any);

      // When & Then: 验证错误处理
      const mockPublishContent = jest.fn().mockRejectedValue(
        new BadRequestException('No revision to publish')
      );

      await expect(
        mockPublishContent(mockContent.id, mockUser.id)
      ).rejects.toThrow(BadRequestException);

      await expect(
        mockPublishContent(mockContent.id, mockUser.id)
      ).rejects.toThrow('No revision to publish');
    });

    it('应该在权限不足时拒绝发布', async () => {
      // Given: 用户无权发布的内容
      const mockContent = ContentTestScenarios.contentLifecycle.draftContent;
      const outsideUser = ContentTestScenarios.permissions.outsideUser;

      prisma.contentNode.findUnique.mockResolvedValue({
        ...mockContent,
        userId: 'other-user-id',
        workspace: {
          members: [], // 用户不是成员
        },
      } as any);

      // When & Then: 验证权限错误
      const mockPublishContent = jest.fn().mockRejectedValue(
        new ForbiddenException('Insufficient permissions to publish this content')
      );

      await expect(
        mockPublishContent(mockContent.id, outsideUser.id)
      ).rejects.toThrow(ForbiddenException);
    });
  });

  /**
   * 版本创建测试套件
   */
  describe('createRevision', () => {
    const newRevisionData = {
      content: {
        blocks: [
          {
            type: 'paragraph',
            data: { text: '新版本内容' },
          },
        ],
      },
      changelog: '更新了文章内容',
    };

    it('应该成功创建新版本', async () => {
      // Given: 现有内容和版本
      const mockContent = ContentTestScenarios.versioning.contentWithMultipleRevisions.content;
      const existingRevisions = ContentTestScenarios.versioning.contentWithMultipleRevisions.revisions;
      const mockUser = ContentTestScenarios.permissions.contentCreator;

      prisma.contentNode.findUnique.mockResolvedValue({
        ...mockContent,
        userId: mockUser.id,
        workspace: {
          members: [{ userId: mockUser.id }],
        },
      } as any);

      prisma.contentRevision.findMany.mockResolvedValue(existingRevisions as any);

      prisma.$transaction.mockImplementation(async (callback: any) => {
        return callback(prisma);
      });

      const newRevision = MockDataFactory.createContentRevision({
        nodeId: mockContent.id,
        version: 4, // 下一个版本号
        data: newRevisionData.content,
        changelog: newRevisionData.changelog,
        createdById: mockUser.id,
      });

      prisma.contentRevision.create.mockResolvedValue(newRevision as any);
      prisma.contentAuditLog.create.mockResolvedValue({} as any);

      // When: 创建新版本
      const mockCreateRevision = jest.fn().mockResolvedValue(newRevision);
      const result = await mockCreateRevision(mockContent.id, newRevisionData, mockUser.id);

      // Then: 验证新版本创建
      expect(result).toMatchObject({
        nodeId: mockContent.id,
        version: 4,
        data: newRevisionData.content,
        changelog: newRevisionData.changelog,
        createdById: mockUser.id,
        isPublished: false,
      });

      TestAssertions.expectValidContentRevision(result);
    });

    it('应该正确计算版本号', async () => {
      // Given: 包含多个版本的内容
      const mockContent = ContentTestScenarios.versioning.contentWithMultipleRevisions.content;
      const existingRevisions = [
        { version: 1 },
        { version: 2 },
        { version: 3 },
        { version: 5 }, // 跳号版本
      ];
      const mockUser = ContentTestScenarios.permissions.contentCreator;

      prisma.contentNode.findUnique.mockResolvedValue({
        ...mockContent,
        userId: mockUser.id,
        workspace: { members: [{ userId: mockUser.id }] },
      } as any);

      prisma.contentRevision.findMany.mockResolvedValue(existingRevisions as any);

      // When: 创建新版本
      const mockCreateRevision = jest.fn().mockImplementation(() => {
        const maxVersion = Math.max(...existingRevisions.map(r => r.version));
        return Promise.resolve({
          version: maxVersion + 1, // 应该是6
        });
      });

      const result = await mockCreateRevision(mockContent.id, newRevisionData, mockUser.id);

      // Then: 验证版本号正确
      expect(result.version).toBe(6);
    });
  });

  /**
   * 复杂业务场景测试
   */
  describe('复杂业务场景', () => {
    it('应该正确处理包含分类和标签的内容创建', async () => {
      // Given: 完整的内容数据
      const complexScenario = ContentTestScenarios.complexScenarios.blogWithCategoryAndTags;
      const mockUser = ContentTestScenarios.permissions.contentCreator;

      // Mock所有相关数据
      prisma.website.findUnique.mockResolvedValue({
        id: 'website-1',
        workspace: { id: 'workspace-1' },
      } as any);
      prisma.user.findUnique.mockResolvedValue(mockUser as any);
      prisma.contentNode.findFirst.mockResolvedValue(null);
      prisma.category.findUnique.mockResolvedValue(complexScenario.category as any);
      prisma.tag.findMany.mockResolvedValue(complexScenario.tags as any);
      prisma.quota.findUnique.mockResolvedValue({
        contentsCreated: 50,
        contentCreationLimit: 100,
      } as any);

      prisma.$transaction.mockImplementation(async (callback: any) => {
        return callback(prisma);
      });

      prisma.contentNode.create.mockResolvedValue(complexScenario.content as any);
      prisma.contentRevision.create.mockResolvedValue(complexScenario.revision as any);
      prisma.contentNodeTag.createMany.mockResolvedValue({ count: 2 } as any);

      // When: 创建复杂内容
      const createDto = {
        type: complexScenario.content.type,
        title: complexScenario.content.title,
        slug: complexScenario.content.slug,
        excerpt: complexScenario.content.excerpt,
        content: complexScenario.revision.data,
        authorInfo: complexScenario.content.authorInfo,
        categoryId: complexScenario.content.categoryId,
        tagIds: complexScenario.tags.map(tag => tag.id),
      };

      const mockCreateContent = jest.fn().mockResolvedValue(complexScenario.content);
      const result = await mockCreateContent(createDto, mockUser.id, 'website-1');

      // Then: 验证复杂内容创建成功
      expect(result).toMatchObject({
        type: ContentType.BLOG,
        title: '深入理解React Hooks',
        categoryId: 'cat-frontend',
        authorInfo: expect.objectContaining({
          name: '张三',
          organization: expect.objectContaining({
            name: 'ABC科技有限公司',
          }),
        }),
      });
    });

    it('应该正确处理多语言内容', async () => {
      // Given: 多语言内容数据
      const languages = [Language.EN, Language.CN];
      const mockUser = ContentTestScenarios.permissions.contentCreator;

      for (const language of languages) {
        const multiLangContent = MockDataFactory.createContentNode({
          title: language === Language.EN ? 'English Title' : '中文标题',
          slug: language === Language.EN ? 'english-title' : 'chinese-title',
          language,
        });

        // When: 创建多语言内容
        const mockCreateContent = jest.fn().mockResolvedValue(multiLangContent);
        const result = await mockCreateContent({
          type: ContentType.BLOG,
          title: multiLangContent.title,
          slug: multiLangContent.slug,
          language,
          content: { blocks: [] },
          authorInfo: { name: 'Test Author' },
        }, mockUser.id, 'website-1');

        // Then: 验证语言设置正确
        expect(result.language).toBe(language);
        expect(result.title).toBe(multiLangContent.title);
      }
    });
  });
});
