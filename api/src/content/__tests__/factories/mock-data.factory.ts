import { faker } from '@faker-js/faker';
import {
  User,
  Workspace,
  Website,
  ContentNode,
  ContentRevision,
  Category,
  Tag,
  ContentNodeTag,
  ContentAuditLog,
  ContentType,
  ContentStatus,
  CategoryType,
  ContentAuditAction,
  Language,
  WorkspaceVisibility,
  SubscriptionLevel,
  SubscriptionStatus,
} from '@prisma/client';

/**
 * Mock数据工厂类 - 用于生成测试数据
 * 基于TDD策略文档中的MockDataFactory设计
 */
export class MockDataFactory {
  /**
   * 创建用户Mock数据
   */
  static createUser(overrides: Partial<User> = {}): User {
    return {
      id: faker.string.uuid(),
      name: faker.person.fullName(),
      email: faker.internet.email(),
      password: faker.internet.password(),
      image: faker.image.avatar(),
      subscriptionLevel: SubscriptionLevel.BASIC,
      subscriptionStartDate: faker.date.past(),
      subscriptionEndDate: faker.date.future(),
      subscriptionStatus: SubscriptionStatus.ACTIVE,
      createdAt: faker.date.past(),
      updatedAt: faker.date.recent(),
      thirdPartyId: null,
      provider: null,
      oauthAccessToken: null,
      oauthRefreshToken: null,
      tokenExpiresAt: null,
      lastLoginAt: faker.date.recent(),
      ...overrides,
    } as User;
  }

  /**
   * 创建工作空间Mock数据
   */
  static createWorkspace(overrides: Partial<Workspace> = {}): Workspace {
    return {
      id: faker.string.uuid(),
      name: faker.company.name(),
      userId: faker.string.uuid(),
      description: faker.lorem.sentence(),
      tags: [faker.lorem.word(), faker.lorem.word()],
      isActive: true,
      visibility: WorkspaceVisibility.PRIVATE,
      createdAt: faker.date.past(),
      updatedAt: faker.date.recent(),
      ...overrides,
    } as Workspace;
  }

  /**
   * 创建网站Mock数据
   */
  static createWebsite(overrides: Partial<Website> = {}): Website {
    return {
      id: faker.string.uuid(),
      name: faker.company.name(),
      domain: faker.internet.domainName(),
      logo: faker.image.url(),
      defaultLanguage: Language.EN,
      description: faker.lorem.sentence(),
      tags: [faker.lorem.word(), faker.lorem.word()],
      workspaceId: faker.string.uuid(),
      userId: faker.string.uuid(),
      configuration: {
        theme: 'default',
        primaryColor: faker.internet.color(),
      },
      headers: null,
      footers: null,
      createdAt: faker.date.past(),
      updatedAt: faker.date.recent(),
      ...overrides,
    } as Website;
  }

  /**
   * 创建内容节点Mock数据
   */
  static createContentNode(overrides: Partial<ContentNode> = {}): ContentNode {
    const title = faker.lorem.sentence();
    return {
      id: faker.string.uuid(),
      userId: faker.string.uuid(),
      workspaceId: faker.string.uuid(),
      websiteId: faker.string.uuid(),
      type: ContentType.BLOG,
      slug: faker.lorem.slug(),
      title,
      excerpt: faker.lorem.paragraph(),
      language: Language.EN,
      authorInfo: {
        name: faker.person.fullName(),
        avatar: faker.image.avatar(),
        bio: faker.lorem.sentence(),
        email: faker.internet.email(),
        website: faker.internet.url(),
        social: {
          twitter: `@${faker.internet.userName()}`,
          github: faker.internet.userName(),
        },
        organization: {
          name: faker.company.name(),
          role: faker.person.jobTitle(),
        },
        metadata: {
          location: faker.location.city(),
          expertise: [faker.lorem.word(), faker.lorem.word()],
        },
      },
      status: ContentStatus.DRAFT,
      publishedAt: null,
      createdAt: faker.date.past(),
      updatedAt: faker.date.recent(),
      currentRevisionId: null,
      categoryId: null,
      ...overrides,
    } as ContentNode;
  }

  /**
   * 创建内容版本Mock数据
   */
  static createContentRevision(overrides: Partial<ContentRevision> = {}): ContentRevision {
    return {
      id: faker.string.uuid(),
      nodeId: faker.string.uuid(),
      createdById: faker.string.uuid(),
      version: faker.number.int({ min: 1, max: 10 }),
      data: {
        blocks: [
          {
            type: 'paragraph',
            data: {
              text: faker.lorem.paragraph(),
            },
          },
          {
            type: 'header',
            data: {
              text: faker.lorem.sentence(),
              level: 2,
            },
          },
        ],
        seo: {
          metaTitle: faker.lorem.sentence(),
          metaDescription: faker.lorem.paragraph(),
          keywords: [faker.lorem.word(), faker.lorem.word()],
        },
      },
      changelog: faker.lorem.sentence(),
      isPublished: false,
      createdAt: faker.date.past(),
      publishedAt: null,
      ...overrides,
    } as ContentRevision;
  }

  /**
   * 创建分类Mock数据
   */
  static createCategory(overrides: Partial<Category> = {}): Category {
    return {
      id: faker.string.uuid(),
      workspaceId: faker.string.uuid(),
      websiteId: faker.string.uuid(),
      name: faker.lorem.word(),
      slug: faker.lorem.slug(),
      description: faker.lorem.sentence(),
      parentId: null,
      order: faker.number.int({ min: 0, max: 100 }),
      type: CategoryType.GENERAL,
      language: Language.EN,
      isActive: true,
      createdAt: faker.date.past(),
      updatedAt: faker.date.recent(),
      ...overrides,
    } as Category;
  }

  /**
   * 创建标签Mock数据
   */
  static createTag(overrides: Partial<Tag> = {}): Tag {
    return {
      id: faker.string.uuid(),
      workspaceId: faker.string.uuid(),
      websiteId: faker.string.uuid(),
      name: faker.lorem.word(),
      color: faker.internet.color(),
      description: faker.lorem.sentence(),
      language: Language.EN,
      isActive: true,
      createdAt: faker.date.past(),
      updatedAt: faker.date.recent(),
      ...overrides,
    } as Tag;
  }

  /**
   * 创建内容标签关联Mock数据
   */
  static createContentNodeTag(overrides: Partial<ContentNodeTag> = {}): ContentNodeTag {
    return {
      contentId: faker.string.uuid(),
      tagId: faker.string.uuid(),
      weight: 1,
      createdAt: faker.date.past(),
      ...overrides,
    } as ContentNodeTag;
  }

  /**
   * 创建内容审计日志Mock数据
   */
  static createContentAuditLog(overrides: Partial<ContentAuditLog> = {}): ContentAuditLog {
    return {
      id: faker.string.uuid(),
      contentId: faker.string.uuid(),
      userId: faker.string.uuid(),
      action: ContentAuditAction.CREATED,
      revisionId: faker.string.uuid(),
      metadata: {
        userAgent: faker.internet.userAgent(),
        ipAddress: faker.internet.ip(),
        timestamp: faker.date.recent().toISOString(),
      },
      createdAt: faker.date.past(),
      ...overrides,
    } as ContentAuditLog;
  }

  /**
   * 创建完整的内容数据（包含关联数据）
   */
  static createCompleteContent(overrides: {
    content?: Partial<ContentNode>;
    revision?: Partial<ContentRevision>;
    category?: Partial<Category>;
    tags?: Partial<Tag>[];
  } = {}) {
    const contentId = faker.string.uuid();
    const revisionId = faker.string.uuid();
    const categoryId = faker.string.uuid();

    const content = this.createContentNode({
      id: contentId,
      currentRevisionId: revisionId,
      categoryId,
      ...overrides.content,
    });

    const revision = this.createContentRevision({
      id: revisionId,
      nodeId: contentId,
      ...overrides.revision,
    });

    const category = this.createCategory({
      id: categoryId,
      ...overrides.category,
    });

    const tags = (overrides.tags || []).map((tagOverride, index) =>
      this.createTag({
        id: `tag-${index + 1}`,
        ...tagOverride,
      })
    );

    return {
      content,
      revision,
      category,
      tags,
    };
  }
}
