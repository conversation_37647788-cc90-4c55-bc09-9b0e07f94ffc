import { MockServicesFactory } from '../factories/mock-services.factory';

/**
 * 测试环境配置
 * 基于TDD策略文档中的测试配置设计
 */

// 全局测试配置
declare global {
  var testConfig: {
    database: {
      url: string;
      logging: boolean;
    };
    cache: {
      enabled: boolean;
    };
    jwt: {
      secret: string;
      expiresIn: string;
    };
  };
}

// 设置全局测试配置
global.testConfig = {
  database: {
    url: process.env.TEST_DATABASE_URL || 'postgresql://test:test@localhost:5433/cms_test',
    logging: false,
  },
  cache: {
    enabled: false, // 测试环境禁用缓存
  },
  jwt: {
    secret: 'test-jwt-secret',
    expiresIn: '1h',
  },
};

/**
 * Jest全局设置
 */
beforeAll(async () => {
  // 设置测试数据库连接
  process.env.DATABASE_URL = global.testConfig.database.url;
});

afterAll(async () => {
  // 清理测试环境
});

/**
 * 每个测试前的清理
 */
beforeEach(async () => {
  // 清理Mock调用记录
  MockServicesFactory.resetAllMocks();
});

/**
 * 测试工具函数
 */
export class TestUtils {
  /**
   * 创建测试用的JWT Token
   */
  static createTestJwtToken(payload: any = { userId: 'test-user-id' }): string {
    // 在实际实现中，这里会使用真实的JWT库
    // 现在返回一个模拟的token
    return `test-jwt-token-${JSON.stringify(payload)}`;
  }

  /**
   * 等待指定时间（用于异步测试）
   */
  static async wait(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 创建测试用的请求头
   */
  static createTestHeaders(token?: string) {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    return headers;
  }

  /**
   * 验证Mock函数的调用
   */
  static expectMockCalledWith(mockFn: jest.Mock, expectedArgs: any[]) {
    expect(mockFn).toHaveBeenCalledWith(...expectedArgs);
  }

  /**
   * 验证Mock函数的调用次数
   */
  static expectMockCalledTimes(mockFn: jest.Mock, times: number) {
    expect(mockFn).toHaveBeenCalledTimes(times);
  }

  /**
   * 重置单个Mock函数
   */
  static resetMock(mockFn: jest.Mock) {
    mockFn.mockReset();
  }
}

/**
 * 测试断言辅助函数
 */
export class TestAssertions {
  /**
   * 验证内容节点的基本结构
   */
  static expectValidContentNode(content: any) {
    expect(content).toMatchObject({
      id: expect.any(String),
      title: expect.any(String),
      slug: expect.any(String),
      type: expect.any(String),
      status: expect.any(String),
      createdAt: expect.any(Date),
      updatedAt: expect.any(Date),
      authorInfo: expect.any(Object),
    });
  }

  /**
   * 验证内容版本的基本结构
   */
  static expectValidContentRevision(revision: any) {
    expect(revision).toMatchObject({
      id: expect.any(String),
      nodeId: expect.any(String),
      version: expect.any(Number),
      data: expect.any(Object),
      createdAt: expect.any(Date),
      isPublished: expect.any(Boolean),
    });
  }

  /**
   * 验证API响应的基本结构
   */
  static expectValidApiResponse(response: any) {
    expect(response).toMatchObject({
      success: expect.any(Boolean),
      meta: expect.objectContaining({
        timestamp: expect.any(String),
        version: expect.any(String),
      }),
    });

    if (response.success) {
      expect(response).toHaveProperty('data');
    } else {
      expect(response).toHaveProperty('error');
      expect(response.error).toMatchObject({
        code: expect.any(String),
        message: expect.any(String),
      });
    }
  }

  /**
   * 验证错误响应的结构
   */
  static expectValidErrorResponse(response: any, expectedCode?: string) {
    expect(response.success).toBe(false);
    expect(response.error).toMatchObject({
      code: expect.any(String),
      message: expect.any(String),
    });

    if (expectedCode) {
      expect(response.error.code).toMatch(new RegExp(expectedCode));
    }
  }

  /**
   * 验证分页响应的结构
   */
  static expectValidPaginatedResponse(response: any) {
    this.expectValidApiResponse(response);
    expect(response.data).toMatchObject({
      items: expect.any(Array),
      pagination: expect.objectContaining({
        page: expect.any(Number),
        limit: expect.any(Number),
        total: expect.any(Number),
        totalPages: expect.any(Number),
      }),
    });
  }
}

/**
 * 测试数据验证器
 */
export class TestValidators {
  /**
   * 验证UUID格式
   */
  static isValidUUID(uuid: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }

  /**
   * 验证日期格式
   */
  static isValidDate(date: any): boolean {
    return date instanceof Date && !isNaN(date.getTime());
  }

  /**
   * 验证Slug格式
   */
  static isValidSlug(slug: string): boolean {
    const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
    return slugRegex.test(slug);
  }

  /**
   * 验证Email格式
   */
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * 验证JSON结构
   */
  static isValidJSON(obj: any): boolean {
    try {
      JSON.stringify(obj);
      return true;
    } catch {
      return false;
    }
  }
}
