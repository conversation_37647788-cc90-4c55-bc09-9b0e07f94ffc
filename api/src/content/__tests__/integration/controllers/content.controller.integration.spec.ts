import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import { HttpStatus } from '@nestjs/common';
import request from 'supertest';
import { MockServicesFactory } from '../../factories/mock-services.factory';
import { MockDataFactory } from '../../factories/mock-data.factory';
import { ContentTestScenarios } from '../../scenarios/content-scenarios';
import { TestUtils, TestAssertions } from '../../setup/test-setup';
import {
  ContentType,
  ContentStatus,
  Language,
} from '@prisma/client';

// 这里我们先定义接口，实际的Controller将在后续实现
interface ContentController {
  createContent(body: any, req: any): Promise<any>;
  getContent(id: string, req: any): Promise<any>;
  getContentList(query: any, req: any): Promise<any>;
  updateContent(id: string, body: any, req: any): Promise<any>;
  deleteContent(id: string, req: any): Promise<any>;
  publishContent(id: string, req: any): Promise<any>;
}

/**
 * ContentController 集成测试
 * 基于TDD策略文档中的Controller层测试设计
 * 
 * 测试覆盖范围：
 * - API接口的完整请求-响应流程
 * - 数据验证和错误处理
 * - 权限验证集成
 * - HTTP状态码验证
 */
describe('ContentController (Integration)', () => {
  let app: INestApplication;
  let mockContentService: any;

  beforeEach(async () => {
    // 创建Mock ContentService
    mockContentService = {
      createContent: jest.fn(),
      getContent: jest.fn(),
      getContentList: jest.fn(),
      updateContent: jest.fn(),
      deleteContent: jest.fn(),
      publishContent: jest.fn(),
      unpublishContent: jest.fn(),
      archiveContent: jest.fn(),
      createRevision: jest.fn(),
      getRevisionHistory: jest.fn(),
    };

    const moduleFixture: TestingModule = await Test.createTestingModule({
      controllers: [
        // ContentController, // 将在实际实现时添加
      ],
      providers: [
        {
          provide: 'ContentService',
          useValue: mockContentService,
        },
        {
          provide: 'JwtAuthGuard',
          useValue: {
            canActivate: jest.fn(() => true), // Mock认证通过
          },
        },
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    
    // 应用全局管道
    app.useGlobalPipes(new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }));

    await app.init();
  });

  afterEach(async () => {
    await app.close();
    MockServicesFactory.resetAllMocks();
  });

  /**
   * POST /api/v1/contents - 创建内容
   */
  describe('POST /api/v1/contents', () => {
    const createContentDto = {
      type: ContentType.BLOG,
      title: '测试内容',
      slug: 'test-content',
      excerpt: '测试摘要',
      content: {
        blocks: [
          {
            type: 'paragraph',
            data: { text: '测试内容' },
          },
        ],
      },
      authorInfo: {
        name: '测试作者',
        bio: '测试简介',
      },
      categoryId: 'category-1',
      tagIds: ['tag-1', 'tag-2'],
      language: Language.EN,
    };

    it('应该成功创建内容并返回201状态码', async () => {
      // Given: Mock Service返回
      const mockCreatedContent = MockDataFactory.createContentNode({
        title: createContentDto.title,
        type: createContentDto.type,
        slug: createContentDto.slug,
      });
      mockContentService.createContent.mockResolvedValue(mockCreatedContent);

      // When: 发送POST请求
      const response = await request(app.getHttpServer())
        .post('/api/v1/contents')
        .set('Authorization', `Bearer ${TestUtils.createTestJwtToken()}`)
        .send(createContentDto);

      // Then: 验证响应
      expect(response.status).toBe(HttpStatus.CREATED);
      
      // 验证响应结构
      TestAssertions.expectValidApiResponse(response.body);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toMatchObject({
        title: createContentDto.title,
        type: createContentDto.type,
        slug: createContentDto.slug,
      });

      // 验证Service调用
      expect(mockContentService.createContent).toHaveBeenCalledWith(
        expect.objectContaining(createContentDto),
        expect.any(String), // userId
        expect.any(String)  // websiteId
      );
    });

    it('应该在验证失败时返回400状态码', async () => {
      // Given: 无效的请求数据
      const invalidDto = {
        ...createContentDto,
        title: '', // 空标题
        type: 'INVALID_TYPE', // 无效类型
      };

      // When: 发送无效请求
      const response = await request(app.getHttpServer())
        .post('/api/v1/contents')
        .set('Authorization', `Bearer ${TestUtils.createTestJwtToken()}`)
        .send(invalidDto);

      // Then: 验证错误响应
      expect(response.status).toBe(HttpStatus.BAD_REQUEST);
      TestAssertions.expectValidErrorResponse(response.body, 'VALIDATION_ERROR');
      
      expect(response.body.error.message).toMatch(/title|type/i);
      expect(mockContentService.createContent).not.toHaveBeenCalled();
    });

    it('应该在权限不足时返回403状态码', async () => {
      // Given: 权限检查失败
      mockContentService.createContent.mockRejectedValue(
        new Error('Insufficient permissions')
      );

      // When: 发送请求
      const response = await request(app.getHttpServer())
        .post('/api/v1/contents')
        .set('Authorization', `Bearer ${TestUtils.createTestJwtToken()}`)
        .send(createContentDto);

      // Then: 验证权限错误
      expect(response.status).toBe(HttpStatus.FORBIDDEN);
      TestAssertions.expectValidErrorResponse(response.body, 'ACCESS_DENIED');
    });

    it('应该在slug冲突时返回409状态码', async () => {
      // Given: Slug冲突错误
      mockContentService.createContent.mockRejectedValue(
        new Error('Content with this slug already exists')
      );

      // When: 发送请求
      const response = await request(app.getHttpServer())
        .post('/api/v1/contents')
        .set('Authorization', `Bearer ${TestUtils.createTestJwtToken()}`)
        .send(createContentDto);

      // Then: 验证冲突错误
      expect(response.status).toBe(HttpStatus.CONFLICT);
      TestAssertions.expectValidErrorResponse(response.body, 'CONFLICT');
    });

    it('应该在未认证时返回401状态码', async () => {
      // When: 发送未认证请求
      const response = await request(app.getHttpServer())
        .post('/api/v1/contents')
        .send(createContentDto);

      // Then: 验证认证错误
      expect(response.status).toBe(HttpStatus.UNAUTHORIZED);
      TestAssertions.expectValidErrorResponse(response.body, 'UNAUTHORIZED');
    });

    it('应该正确处理不同内容类型', async () => {
      // Given: 不同类型的内容
      const contentTypes = [
        ContentType.BLOG,
        ContentType.DOC,
        ContentType.FAQ,
        ContentType.CHANGELOG,
      ];

      for (const type of contentTypes) {
        const typeSpecificDto = {
          ...createContentDto,
          type,
          title: `测试${type}内容`,
          slug: `test-${type.toLowerCase()}-content`,
        };

        const mockContent = MockDataFactory.createContentNode({
          type,
          title: typeSpecificDto.title,
        });

        mockContentService.createContent.mockResolvedValue(mockContent);

        // When: 创建不同类型的内容
        const response = await request(app.getHttpServer())
          .post('/api/v1/contents')
          .set('Authorization', `Bearer ${TestUtils.createTestJwtToken()}`)
          .send(typeSpecificDto);

        // Then: 验证创建成功
        expect(response.status).toBe(HttpStatus.CREATED);
        expect(response.body.data.type).toBe(type);
      }
    });
  });

  /**
   * GET /api/v1/contents/:id - 获取内容详情
   */
  describe('GET /api/v1/contents/:id', () => {
    it('应该成功获取内容详情', async () => {
      // Given: Mock内容数据
      const mockContent = ContentTestScenarios.contentLifecycle.publishedContent;
      const mockRevision = MockDataFactory.createContentRevision({
        nodeId: mockContent.id,
        isPublished: true,
      });

      mockContentService.getContent.mockResolvedValue({
        ...mockContent,
        currentRevision: mockRevision,
      });

      // When: 发送GET请求
      const response = await request(app.getHttpServer())
        .get(`/api/v1/contents/${mockContent.id}`)
        .set('Authorization', `Bearer ${TestUtils.createTestJwtToken()}`);

      // Then: 验证响应
      expect(response.status).toBe(HttpStatus.OK);
      TestAssertions.expectValidApiResponse(response.body);
      
      expect(response.body.data).toMatchObject({
        id: mockContent.id,
        title: mockContent.title,
        status: ContentStatus.PUBLISHED,
      });

      // 验证Service调用
      expect(mockContentService.getContent).toHaveBeenCalledWith(
        mockContent.id,
        expect.any(String) // userId
      );
    });

    it('应该在内容不存在时返回404状态码', async () => {
      // Given: 内容不存在
      const nonExistentId = ContentTestScenarios.errorScenarios.nonExistentResources.contentId;
      mockContentService.getContent.mockRejectedValue(
        new Error('Content not found')
      );

      // When: 发送GET请求
      const response = await request(app.getHttpServer())
        .get(`/api/v1/contents/${nonExistentId}`)
        .set('Authorization', `Bearer ${TestUtils.createTestJwtToken()}`);

      // Then: 验证404响应
      expect(response.status).toBe(HttpStatus.NOT_FOUND);
      TestAssertions.expectValidErrorResponse(response.body, 'NOT_FOUND');
    });

    it('应该在权限不足时返回403状态码', async () => {
      // Given: 权限不足
      const mockContent = ContentTestScenarios.contentLifecycle.publishedContent;
      mockContentService.getContent.mockRejectedValue(
        new Error('Access denied')
      );

      // When: 发送GET请求
      const response = await request(app.getHttpServer())
        .get(`/api/v1/contents/${mockContent.id}`)
        .set('Authorization', `Bearer ${TestUtils.createTestJwtToken()}`);

      // Then: 验证权限错误
      expect(response.status).toBe(HttpStatus.FORBIDDEN);
      TestAssertions.expectValidErrorResponse(response.body, 'ACCESS_DENIED');
    });
  });

  /**
   * GET /api/v1/contents - 获取内容列表
   */
  describe('GET /api/v1/contents', () => {
    it('应该成功获取内容列表', async () => {
      // Given: Mock内容列表数据
      const mockContents = [
        ContentTestScenarios.contentLifecycle.publishedContent,
        ContentTestScenarios.contentLifecycle.draftContent,
      ];

      const mockPaginatedResponse = {
        items: mockContents,
        pagination: {
          page: 1,
          limit: 10,
          total: 2,
          totalPages: 1,
        },
      };

      mockContentService.getContentList.mockResolvedValue(mockPaginatedResponse);

      // When: 发送GET请求
      const response = await request(app.getHttpServer())
        .get('/api/v1/contents')
        .query({
          page: 1,
          limit: 10,
          type: ContentType.BLOG,
          status: ContentStatus.PUBLISHED,
        })
        .set('Authorization', `Bearer ${TestUtils.createTestJwtToken()}`);

      // Then: 验证响应
      expect(response.status).toBe(HttpStatus.OK);
      TestAssertions.expectValidPaginatedResponse(response.body);
      
      expect(response.body.data.items).toHaveLength(2);
      expect(response.body.data.pagination.total).toBe(2);

      // 验证Service调用
      expect(mockContentService.getContentList).toHaveBeenCalledWith(
        expect.objectContaining({
          page: 1,
          limit: 10,
          type: ContentType.BLOG,
          status: ContentStatus.PUBLISHED,
        }),
        expect.any(String) // userId
      );
    });

    it('应该支持分页查询', async () => {
      // Given: 分页数据
      const mockPaginatedResponse = {
        items: [],
        pagination: {
          page: 2,
          limit: 5,
          total: 20,
          totalPages: 4,
        },
      };

      mockContentService.getContentList.mockResolvedValue(mockPaginatedResponse);

      // When: 发送分页请求
      const response = await request(app.getHttpServer())
        .get('/api/v1/contents')
        .query({ page: 2, limit: 5 })
        .set('Authorization', `Bearer ${TestUtils.createTestJwtToken()}`);

      // Then: 验证分页响应
      expect(response.status).toBe(HttpStatus.OK);
      expect(response.body.data.pagination).toMatchObject({
        page: 2,
        limit: 5,
        total: 20,
        totalPages: 4,
      });
    });
  });

  /**
   * PUT /api/v1/contents/:id - 更新内容
   */
  describe('PUT /api/v1/contents/:id', () => {
    const updateContentDto = {
      title: '更新后的标题',
      excerpt: '更新后的摘要',
      content: {
        blocks: [
          {
            type: 'paragraph',
            data: { text: '更新后的内容' },
          },
        ],
      },
      authorInfo: {
        name: '更新后的作者',
        bio: '更新后的简介',
      },
      categoryId: 'new-category-id',
      tagIds: ['new-tag-1', 'new-tag-2'],
    };

    it('应该成功更新内容', async () => {
      // Given: 存在的内容
      const mockContent = ContentTestScenarios.contentLifecycle.draftContent;
      const updatedContent = {
        ...mockContent,
        ...updateContentDto,
        updatedAt: new Date(),
      };

      mockContentService.updateContent.mockResolvedValue(updatedContent);

      // When: 发送PUT请求
      const response = await request(app.getHttpServer())
        .put(`/api/v1/contents/${mockContent.id}`)
        .set('Authorization', `Bearer ${TestUtils.createTestJwtToken()}`)
        .send(updateContentDto);

      // Then: 验证响应
      expect(response.status).toBe(HttpStatus.OK);
      TestAssertions.expectValidApiResponse(response.body);

      expect(response.body.data).toMatchObject({
        id: mockContent.id,
        title: updateContentDto.title,
        excerpt: updateContentDto.excerpt,
      });

      // 验证Service调用
      expect(mockContentService.updateContent).toHaveBeenCalledWith(
        mockContent.id,
        expect.objectContaining(updateContentDto),
        expect.any(String) // userId
      );
    });

    it('应该在内容不存在时返回404状态码', async () => {
      // Given: 不存在的内容
      const nonExistentId = ContentTestScenarios.errorScenarios.nonExistentResources.contentId;
      mockContentService.updateContent.mockRejectedValue(
        new Error('Content not found')
      );

      // When: 发送PUT请求
      const response = await request(app.getHttpServer())
        .put(`/api/v1/contents/${nonExistentId}`)
        .set('Authorization', `Bearer ${TestUtils.createTestJwtToken()}`)
        .send(updateContentDto);

      // Then: 验证404响应
      expect(response.status).toBe(HttpStatus.NOT_FOUND);
      TestAssertions.expectValidErrorResponse(response.body, 'NOT_FOUND');
    });

    it('应该在权限不足时返回403状态码', async () => {
      // Given: 权限不足
      const mockContent = ContentTestScenarios.contentLifecycle.draftContent;
      mockContentService.updateContent.mockRejectedValue(
        new Error('Only content creator can update this content')
      );

      // When: 发送PUT请求
      const response = await request(app.getHttpServer())
        .put(`/api/v1/contents/${mockContent.id}`)
        .set('Authorization', `Bearer ${TestUtils.createTestJwtToken()}`)
        .send(updateContentDto);

      // Then: 验证权限错误
      expect(response.status).toBe(HttpStatus.FORBIDDEN);
      TestAssertions.expectValidErrorResponse(response.body, 'ACCESS_DENIED');
    });
  });

  /**
   * DELETE /api/v1/contents/:id - 删除内容
   */
  describe('DELETE /api/v1/contents/:id', () => {
    it('应该成功删除内容', async () => {
      // Given: 存在的内容
      const mockContent = ContentTestScenarios.contentLifecycle.draftContent;
      mockContentService.deleteContent.mockResolvedValue(undefined);

      // When: 发送DELETE请求
      const response = await request(app.getHttpServer())
        .delete(`/api/v1/contents/${mockContent.id}`)
        .set('Authorization', `Bearer ${TestUtils.createTestJwtToken()}`);

      // Then: 验证响应
      expect(response.status).toBe(HttpStatus.NO_CONTENT);

      // 验证Service调用
      expect(mockContentService.deleteContent).toHaveBeenCalledWith(
        mockContent.id,
        expect.any(String) // userId
      );
    });

    it('应该在内容不存在时返回404状态码', async () => {
      // Given: 不存在的内容
      const nonExistentId = ContentTestScenarios.errorScenarios.nonExistentResources.contentId;
      mockContentService.deleteContent.mockRejectedValue(
        new Error('Content not found')
      );

      // When: 发送DELETE请求
      const response = await request(app.getHttpServer())
        .delete(`/api/v1/contents/${nonExistentId}`)
        .set('Authorization', `Bearer ${TestUtils.createTestJwtToken()}`);

      // Then: 验证404响应
      expect(response.status).toBe(HttpStatus.NOT_FOUND);
      TestAssertions.expectValidErrorResponse(response.body, 'NOT_FOUND');
    });

    it('应该在权限不足时返回403状态码', async () => {
      // Given: 权限不足
      const mockContent = ContentTestScenarios.contentLifecycle.publishedContent;
      mockContentService.deleteContent.mockRejectedValue(
        new Error('Insufficient permissions to delete this content')
      );

      // When: 发送DELETE请求
      const response = await request(app.getHttpServer())
        .delete(`/api/v1/contents/${mockContent.id}`)
        .set('Authorization', `Bearer ${TestUtils.createTestJwtToken()}`);

      // Then: 验证权限错误
      expect(response.status).toBe(HttpStatus.FORBIDDEN);
      TestAssertions.expectValidErrorResponse(response.body, 'ACCESS_DENIED');
    });
  });

  /**
   * PUT /api/v1/contents/:id/publish - 发布内容
   */
  describe('PUT /api/v1/contents/:id/publish', () => {
    it('应该成功发布内容', async () => {
      // Given: 草稿状态的内容
      const mockContent = ContentTestScenarios.contentLifecycle.draftContent;
      const publishedContent = {
        ...mockContent,
        status: ContentStatus.PUBLISHED,
        publishedAt: new Date(),
      };

      mockContentService.publishContent.mockResolvedValue(publishedContent);

      // When: 发送发布请求
      const response = await request(app.getHttpServer())
        .put(`/api/v1/contents/${mockContent.id}/publish`)
        .set('Authorization', `Bearer ${TestUtils.createTestJwtToken()}`);

      // Then: 验证响应
      expect(response.status).toBe(HttpStatus.OK);
      TestAssertions.expectValidApiResponse(response.body);

      expect(response.body.data).toMatchObject({
        id: mockContent.id,
        status: ContentStatus.PUBLISHED,
        publishedAt: expect.any(String),
      });

      // 验证Service调用
      expect(mockContentService.publishContent).toHaveBeenCalledWith(
        mockContent.id,
        expect.any(String) // userId
      );
    });

    it('应该在内容已发布时返回400状态码', async () => {
      // Given: 已发布的内容
      const mockContent = ContentTestScenarios.contentLifecycle.publishedContent;
      mockContentService.publishContent.mockRejectedValue(
        new Error('Content is already published')
      );

      // When: 发送发布请求
      const response = await request(app.getHttpServer())
        .put(`/api/v1/contents/${mockContent.id}/publish`)
        .set('Authorization', `Bearer ${TestUtils.createTestJwtToken()}`);

      // Then: 验证错误响应
      expect(response.status).toBe(HttpStatus.BAD_REQUEST);
      TestAssertions.expectValidErrorResponse(response.body, 'BAD_REQUEST');
    });

    it('应该在权限不足时返回403状态码', async () => {
      // Given: 权限不足
      const mockContent = ContentTestScenarios.contentLifecycle.draftContent;
      mockContentService.publishContent.mockRejectedValue(
        new Error('Insufficient permissions to publish this content')
      );

      // When: 发送发布请求
      const response = await request(app.getHttpServer())
        .put(`/api/v1/contents/${mockContent.id}/publish`)
        .set('Authorization', `Bearer ${TestUtils.createTestJwtToken()}`);

      // Then: 验证权限错误
      expect(response.status).toBe(HttpStatus.FORBIDDEN);
      TestAssertions.expectValidErrorResponse(response.body, 'ACCESS_DENIED');
    });
  });

  /**
   * 错误处理和边界条件测试
   */
  describe('错误处理和边界条件', () => {
    it('应该正确处理无效的UUID格式', async () => {
      // Given: 无效的UUID
      const invalidId = 'invalid-uuid-format';

      // When: 发送请求
      const response = await request(app.getHttpServer())
        .get(`/api/v1/contents/${invalidId}`)
        .set('Authorization', `Bearer ${TestUtils.createTestJwtToken()}`);

      // Then: 验证错误响应
      expect(response.status).toBe(HttpStatus.BAD_REQUEST);
      TestAssertions.expectValidErrorResponse(response.body, 'VALIDATION_ERROR');
    });

    it('应该正确处理超大请求体', async () => {
      // Given: 超大的内容数据
      const largeContent = {
        type: ContentType.BLOG,
        title: '测试内容',
        slug: 'test-content',
        content: {
          blocks: Array(1000).fill({
            type: 'paragraph',
            data: { text: 'A'.repeat(1000) },
          }),
        },
        authorInfo: { name: '测试作者' },
      };

      // When: 发送超大请求
      const response = await request(app.getHttpServer())
        .post('/api/v1/contents')
        .set('Authorization', `Bearer ${TestUtils.createTestJwtToken()}`)
        .send(largeContent);

      // Then: 验证处理结果（可能是413 Payload Too Large或正常处理）
      expect([HttpStatus.CREATED, HttpStatus.PAYLOAD_TOO_LARGE]).toContain(response.status);
    });

    it('应该正确处理并发请求', async () => {
      // Given: 并发创建相同slug的内容
      const createDto = {
        type: ContentType.BLOG,
        title: '并发测试内容',
        slug: 'concurrent-test-content',
        content: { blocks: [] },
        authorInfo: { name: '测试作者' },
      };

      // 第一个请求成功，后续请求失败
      mockContentService.createContent
        .mockResolvedValueOnce(MockDataFactory.createContentNode())
        .mockRejectedValue(new Error('Content with this slug already exists'));

      // When: 发送并发请求
      const requests = Array(3).fill(null).map(() =>
        request(app.getHttpServer())
          .post('/api/v1/contents')
          .set('Authorization', `Bearer ${TestUtils.createTestJwtToken()}`)
          .send(createDto)
      );

      const responses = await Promise.all(requests);

      // Then: 验证只有一个成功，其他失败
      const successCount = responses.filter(r => r.status === HttpStatus.CREATED).length;
      const conflictCount = responses.filter(r => r.status === HttpStatus.CONFLICT).length;

      expect(successCount).toBe(1);
      expect(conflictCount).toBe(2);
    });

    it('应该正确处理特殊字符和多语言内容', async () => {
      // Given: 包含特殊字符的内容
      const specialCharContent = {
        type: ContentType.BLOG,
        title: '测试内容 🚀 with émojis and 中文',
        slug: 'test-content-with-special-chars',
        excerpt: 'Content with special characters: @#$%^&*()',
        content: {
          blocks: [
            {
              type: 'paragraph',
              data: { text: '这是包含特殊字符的内容：<script>alert("xss")</script>' },
            },
          ],
        },
        authorInfo: {
          name: 'Tëst Authör 测试作者',
          bio: 'Bio with émojis 🎉 and special chars',
        },
      };

      mockContentService.createContent.mockResolvedValue(
        MockDataFactory.createContentNode({
          title: specialCharContent.title,
        })
      );

      // When: 发送包含特殊字符的请求
      const response = await request(app.getHttpServer())
        .post('/api/v1/contents')
        .set('Authorization', `Bearer ${TestUtils.createTestJwtToken()}`)
        .send(specialCharContent);

      // Then: 验证正确处理
      expect(response.status).toBe(HttpStatus.CREATED);
      expect(response.body.data.title).toBe(specialCharContent.title);
    });
  });
});
