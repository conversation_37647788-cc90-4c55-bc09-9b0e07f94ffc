import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { CreateContentDto } from '../dto/content-create.dto';
import { ContentNode, ContentRevision, ContentStatus, Language, Prisma } from '@prisma/client';

@Injectable()
export class ContentService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * 创建内容
   */
  async createContent(
    data: CreateContentDto,
    userId: string,
    websiteId: string
  ): Promise<ContentNode> {
    return await this.prisma.$transaction(async (tx) => {
      // 1. 获取网站和工作空间信息
      const website = await tx.website.findUnique({
        where: { id: websiteId },
        include: { workspace: true }
      });
      
      if (!website) {
        throw new NotFoundException('Website not found');
      }
      
      // 2. 检查权限 (简化版本，实际应该检查工作空间成员权限)
      // TODO: 实现完整的权限检查
      
      // 3. 检查slug唯一性
      const existingContent = await tx.contentNode.findFirst({
        where: {
          websiteId,
          slug: data.slug,
          language: data.language || 'EN',
        }
      });
      
      if (existingContent) {
        throw new BadRequestException('Content with this slug already exists');
      }
      
      // 4. 获取用户信息用于默认作者信息
      const user = await tx.user.findUnique({ where: { id: userId } });
      
      // 5. 创建内容节点
      const contentNode = await tx.contentNode.create({
        data: {
          userId,
          workspaceId: website.workspaceId,
          websiteId,
          type: data.type,
          slug: data.slug,
          title: data.title,
          excerpt: data.excerpt,
          language: data.language || 'EN',
          authorInfo: {
            name: data.authorInfo.name,
            avatar: data.authorInfo.avatar || user?.image,
            bio: data.authorInfo.bio,
            email: data.authorInfo.email,
            website: data.authorInfo.website,
            social: data.authorInfo.social || {},
            organization: data.authorInfo.organization,
            metadata: data.authorInfo.metadata || {}
          },
          categoryId: data.categoryId,
          status: 'DRAFT',
        }
      });
      
      // 6. 创建初始版本
      const revision = await tx.contentRevision.create({
        data: {
          nodeId: contentNode.id,
          createdById: userId,
          version: 1,
          data: data.content,
          changelog: data.changelog || 'Initial version',
        }
      });
      
      // 7. 更新当前版本指针
      const updatedContentNode = await tx.contentNode.update({
        where: { id: contentNode.id },
        data: { currentRevisionId: revision.id }
      });
      
      // 8. 处理标签关联
      if (data.tagIds?.length) {
        await tx.contentNodeTag.createMany({
          data: data.tagIds.map(tagId => ({
            contentId: contentNode.id,
            tagId,
          }))
        });
      }
      
      // 9. 记录审计日志
      await tx.contentAuditLog.create({
        data: {
          contentId: contentNode.id,
          userId,
          action: 'CREATED',
          revisionId: revision.id,
          metadata: {
            initialData: {
              type: data.type,
              title: data.title,
              categoryId: data.categoryId,
            }
          }
        }
      });
      
      return updatedContentNode;
    });
  }

  /**
   * 获取已发布的内容
   */
  async getContent(
    websiteId: string,
    slug: string,
    language: Language = 'EN'
  ): Promise<ContentNode & { currentRevision: ContentRevision }> {
    const content = await this.prisma.contentNode.findFirst({
      where: {
        websiteId,
        slug,
        language,
        status: 'PUBLISHED',
      },
      include: {
        currentRevision: true,
        category: true,
        tags: {
          include: { tag: true }
        },
        user: {
          select: {
            id: true,
            name: true,
            image: true,
          }
        },
      },
    });

    if (!content) {
      throw new NotFoundException('Content not found');
    }

    return content as any;
  }

  /**
   * 发布内容
   */
  async publishContent(contentId: string, userId: string): Promise<ContentNode> {
    return await this.prisma.$transaction(async (tx) => {
      // 1. 获取内容和权限检查
      const content = await tx.contentNode.findUnique({
        where: { id: contentId },
        include: {
          currentRevision: true,
          workspace: {
            include: {
              members: { where: { userId } }
            }
          }
        }
      });
      
      if (!content) {
        throw new NotFoundException('Content not found');
      }
      
      // 简化的权限检查
      if (!content.workspace.members.length && content.userId !== userId) {
        throw new ForbiddenException('Insufficient permissions');
      }
      
      // 2. 检查内容状态
      if (content.status === 'PUBLISHED') {
        throw new BadRequestException('Content is already published');
      }
      
      if (!content.currentRevision) {
        throw new BadRequestException('No revision to publish');
      }
      
      // 3. 更新内容状态
      const publishedAt = new Date();
      const updatedContent = await tx.contentNode.update({
        where: { id: contentId },
        data: {
          status: 'PUBLISHED',
          publishedAt,
        }
      });
      
      // 4. 标记版本为已发布
      await tx.contentRevision.update({
        where: { id: content.currentRevisionId! },
        data: {
          isPublished: true,
          publishedAt,
        }
      });
      
      // 5. 记录审计日志
      await tx.contentAuditLog.create({
        data: {
          contentId,
          userId,
          action: 'PUBLISHED',
          revisionId: content.currentRevisionId,
          metadata: {
            publishedAt,
            version: content.currentRevision.version,
          }
        }
      });
      
      return updatedContent;
    });
  }
}
