import { Injectable, Logger, ConflictException, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { WebsiteCreateDto, WebsiteUpdateDto, WebsiteConfigDto, WebsiteQuotaDto, WebsiteQuotaUsageDto, WebsiteUpdateNavDto } from './dto';
import { customAlphabet } from 'nanoid';
import { 
  Language, 
  PageType, 
  DomainStatus, 
  ComponentStatus, 
  VersionStatus, 
  Prisma 
} from '@prisma/client';
import { promises as dns } from 'dns';
import { createHash } from 'crypto';

const nanoid = customAlphabet('1234567890abcdef', 10)
// const execAsync = promisify(exec);

@Injectable()
export class WebsiteService {
 
  private readonly logger = new Logger(WebsiteService.name);

  // 保留域名列表
  private readonly protectedDomains = [
    // 系统和品牌相关
    'lit', 'www', 'build', 'render', 'api', 'app', 'page', 'form', 'table', 'widget', 'landing', 'backup',
    
    // 内容和媒体相关
    'img', 'cdn', 'images', 'video', 'media', 'assets', 'static', 'files', 'storage', 'uploads',
    
    // 管理和功能相关
    'crm', 'scrm', 'dash', 'dashboard', 'mail', 'cname', 'seo', 'admin', 'docs', 'pay',
    
    // 安全相关
    'auth', 'login', 'security', 'account', 'accounts', 'password', 'verify', 'verification', 'oauth',
    
    // 支持和服务相关
    'help', 'support', 'status', 'service', 'services', 'contact', 'feedback',
    
    // 开发和测试相关
    'dev', 'test', 'staging', 'beta', 'alpha', 'api-docs', 'swagger', 'graphql', 'sandbox',
    
    // 营销和内容相关
    'blog', 'news', 'press', 'events', 'about', 'info', 'newsletter', 'campaign',
    
    // 法律相关
    'legal', 'privacy', 'terms', 'policy', 'cookies', 'gdpr', 'compliance',
    
    // 电子商务相关
    'shop', 'store', 'cart', 'checkout', 'order', 'orders', 'product', 'products',
    
    // 社区和用户相关
    'community', 'forum', 'forums', 'user', 'users', 'member', 'members', 'profile',
    
    // 其他常见保留词
    'web', 'site', 'portal', 'host', 'hosting', 'server', 'cloud', 'analytics', 'stats',
    'public', 'private', 'internal', 'external', 'local', 'global', 'main', 'default'
  ];

  constructor(
    private readonly prisma: PrismaService,
  ) {}

  async getWebsites(userId: string) {

    const websites = await this.prisma.website.findMany({ where: { userId }});

    return {
      success: true,
      data: websites,
    };
  }
  
  async getWebsite(websiteId: string) {
    const data = await this.prisma.website.findFirst({ 
      where: { id: websiteId },
      include: {
        customDomain: true // 包含自定义域名信息
      }
    });
    
    if (!data) {
      throw new NotFoundException(`Website with ID ${websiteId} not found`);
    }
    
    return {
      success: true,
      data,
    };
  }

  async deleteWebsite(websiteId: string) {
    this.logger.log(`[DeleteWebsite] Starting deletion process for website`, { websiteId });
    
    try {
      // Verify website exists
      const website = await this.prisma.website.findUnique({
        where: { id: websiteId }
      });
      
      if (!website) {
        this.logger.error(`[DeleteWebsite] Website not found`, { websiteId });
        throw new NotFoundException('Website not found');
      }
      
      // Use transaction to ensure atomicity
      return await this.prisma.$transaction(async (tx) => {
        // 1. First delete PageAuditLog records (due to foreign key constraint)
        this.logger.debug(`[DeleteWebsite] Deleting page audit logs for website`, { websiteId });
        await tx.pageAuditLog.deleteMany({
          where: {
            page: {
              websiteId
            }
          }
        });
        
        // 2. Delete ABTestParticipant and ABTestResult records
        this.logger.debug(`[DeleteWebsite] Deleting AB test participants and results for website`, { websiteId });
        await tx.aBTestParticipant.deleteMany({
          where: {
            abTest: {
              page: {
                websiteId
              }
            }
          }
        });
        
        await tx.aBTestResult.deleteMany({
          where: {
            abTest: {
              page: {
                websiteId
              }
            }
          }
        });
        
        // 3. Delete ABTest records
        this.logger.debug(`[DeleteWebsite] Deleting AB tests for website`, { websiteId });
        await tx.aBTest.deleteMany({
          where: {
            page: {
              websiteId
            }
          }
        });
        
        // 4. Delete page versions
        this.logger.debug(`[DeleteWebsite] Deleting page versions for website`, { websiteId });
        await tx.pageVersion.deleteMany({
          where: {
            page: {
              websiteId
            }
          }
        });
        
        // 5. Delete all pages
        this.logger.debug(`[DeleteWebsite] Deleting pages for website`, { websiteId });
        await tx.page.deleteMany({
          where: { websiteId }
        });
        
        // 6. Delete header versions
        this.logger.debug(`[DeleteWebsite] Deleting header versions for website`, { websiteId });
        await tx.websiteHeaderVersion.deleteMany({
          where: {
            header: {
              websiteId
            }
          }
        });
        
        // 7. Delete headers
        this.logger.debug(`[DeleteWebsite] Deleting headers for website`, { websiteId });
        await tx.websiteHeader.deleteMany({
          where: { websiteId }
        });
        
        // 8. Delete footer versions
        this.logger.debug(`[DeleteWebsite] Deleting footer versions for website`, { websiteId });
        await tx.websiteFooterVersion.deleteMany({
          where: {
            footer: {
              websiteId
            }
          }
        });
        
        // 9. Delete footers
        this.logger.debug(`[DeleteWebsite] Deleting footers for website`, { websiteId });
        await tx.websiteFooter.deleteMany({
          where: { websiteId }
        });
        
        // 10. Delete published components
        this.logger.debug(`[DeleteWebsite] Deleting published components for website`, { websiteId });
        await tx.publishedComponent.deleteMany({
          where: {
            publishRecord: {
              websiteId
            }
          }
        });
        
        // 11. Delete publish records
        this.logger.debug(`[DeleteWebsite] Deleting publish records for website`, { websiteId });
        await tx.publishRecord.deleteMany({
          where: { websiteId }
        });
        
        // 12. Delete integrations
        this.logger.debug(`[DeleteWebsite] Deleting integrations for website`, { websiteId });
        await tx.integration.deleteMany({
          where: { websiteId }
        });
        
        // 13. Delete custom domain (has onDelete: Cascade, but delete explicitly for clarity)
        this.logger.debug(`[DeleteWebsite] Deleting custom domain for website`, { websiteId });
        await tx.customDomain.deleteMany({
          where: { websiteId }
        });
        
        // 14. Finally delete the website itself
        this.logger.debug(`[DeleteWebsite] Deleting website record`, { websiteId });
        await tx.website.delete({
          where: { id: websiteId }
        });
        
        this.logger.log(`[DeleteWebsite] Successfully deleted website and all related data`, { 
          websiteId,
          name: website.name,
          domain: website.domain
        });
        
        return {
          success: true,
          data: {
            id: websiteId,
            name: website.name
          }
        };
      });
    } catch (error) {
      this.logger.error(`[DeleteWebsite] Failed to delete website`, {
        websiteId,
        error: error.message,
        stack: error.stack
      });
      
      if (error instanceof NotFoundException) {
        throw error;
      }
      
      throw new BadRequestException(`Failed to delete website: ${error.message}`);
    }
  }

  async createWebsite(userId: string, websiteCreateDto: WebsiteCreateDto) {
    const { domain, name, description, language = Language.EN } = websiteCreateDto;

    try {
      this.logger.log('Step 1: Validating domain');
      await this.validateDomain(domain);
      this.logger.debug(`Domain validation successful for: ${domain}`);

      // 2. 获取或验证工作区
      this.logger.log('Step 2: Validating workspace');
      let workspaceId = websiteCreateDto.workspaceId;
      if (!workspaceId) {
        this.logger.debug('No workspace ID provided, finding active workspace');
        const workspace = await this.prisma.workspace.findFirst({ 
          where: { 
            userId,
            isActive: true
          },
          orderBy: {
            createdAt: 'desc'
          }
        });
        if (!workspace) {
          this.logger.error(`No active workspace found for user: ${userId}`);
          throw new BadRequestException('No workspace found for this user');
        }
        workspaceId = workspace.id;
        this.logger.debug(`Using default workspace: ${workspaceId}`);
      } else {
        this.logger.debug(`Validating provided workspace: ${workspaceId}`);
        const workspace = await this.prisma.workspace.findFirst({
          where: { 
            id: workspaceId,
            userId,
            isActive: true
          }
        });
        if (!workspace) {
          this.logger.error(`Invalid workspace ID: ${workspaceId} for user: ${userId}`);
          throw new BadRequestException('Invalid workspace');
        }
        this.logger.debug('Workspace validation successful');
      }

      try {
        // 3. 使用事务创建网站及相关组件
        this.logger.log('Step 3: Creating website and components');
        const result = await this.prisma.$transaction(async (tx: Prisma.TransactionClient) => {
          // 创建网站记录
          this.logger.debug('Creating website record');
          const website = await tx.website.create({
            data: {
              userId,
              workspaceId,
              domain: domain.toLowerCase(),
              name,
              description: description || '',
              defaultLanguage: language,
              tags: [],
              configuration: {},
              headers: {},
              footers: {}
            },
          });
          
          // 创建默认语言的 Header
          this.logger.debug(`Creating default Header for language: ${language}`);
          const header = await tx.websiteHeader.create({
            data: {
              websiteId: website.id,
              language,
              variant: 'default',
              status: ComponentStatus.INITIALIZED,
              isDeleted: false,
              createdBy: userId
            },
          });
          
          // 创建 Header 初始版本
          this.logger.debug('Creating Header initial version');
          const headerVersion = await tx.websiteHeaderVersion.create({
            data: {
              headerId: header.id,
              versionNumber: 1,
              configuration: this.getDefaultHeaderConfig(name),
              createdBy: userId,
              status: VersionStatus.DRAFT,
            },
          });
          
          // 更新 Header 的草稿版本ID
          this.logger.debug('Updating Header draft version reference');
          await tx.websiteHeader.update({
            where: { id: header.id },
            data: {
              draftVersionId: headerVersion.id,
              status: ComponentStatus.DRAFT
            }
          });
          
          // 创建默认语言的 Footer
          this.logger.debug(`Creating default Footer for language: ${language}`);
          const footer = await tx.websiteFooter.create({
            data: {
              websiteId: website.id,
              language,
              variant: 'default',
              status: ComponentStatus.INITIALIZED,
              isDeleted: false,
              createdBy: userId
            },
          });
          
          // 创建 Footer 初始版本
          this.logger.debug('Creating Footer initial version');
          const footerVersion = await tx.websiteFooterVersion.create({
            data: {
              footerId: footer.id,
              versionNumber: 1,
              configuration: this.getDefaultFooterConfig(name),
              createdBy: userId,
              status: VersionStatus.DRAFT,
            },
          });
          
          // 更新 Footer 的草稿版本ID
          this.logger.debug('Updating Footer draft version reference');
          await tx.websiteFooter.update({
            where: { id: footer.id },
            data: {
              draftVersionId: footerVersion.id,
              status: ComponentStatus.DRAFT
            }
          });
          
          this.logger.log(`Website created successfully with ID: ${website.id}`);
          return {
            success: true,
            data: {
              id: website.id,
              domain: website.domain,
              name: website.name,
              description: website.description,
              defaultLanguage: website.defaultLanguage,
              userId: website.userId,
              workspaceId: website.workspaceId,
              configuration: website.configuration,
              createdAt: website.createdAt,
              updatedAt: website.updatedAt
            }
          };
        });
        
        return result;
      } catch (txError) {
        this.logger.error('Transaction failed:', txError.stack);
        throw new BadRequestException('Failed to create website components');
      }
    } catch (error) {
      if (error instanceof BadRequestException || error instanceof ConflictException) {
        throw error;
      }
      
      // 记录未预期的错误
      this.logger.error('Failed to create website:', error.stack);
      throw new BadRequestException('Failed to create website');
    }
  }

  // 获取默认 Header 配置
  private getDefaultHeaderConfig(websiteName: string) {
    return {
      logo: websiteName,
      links: [
        { text: 'Home', slug: '/', icon: 'Home' },
        { text: 'Features', slug: '/features', icon: 'Zap' },
        { text: 'Pricing', slug: '/pricing', icon: 'Tag' },
        { text: 'About', slug: '/about', icon: 'FileText' }
      ],
      actions: [
        { text: 'Log in', slug: '/login', style: 'link' },
        { text: 'Sign up', slug: '/signup', style: 'primary' }
      ],
      languages: {
        enabled: false
      },
      theme: {
        enabled: false,
        defaultTheme: 'system'
      }
    };
  }

  // 获取默认 Footer 配置
  private getDefaultFooterConfig(websiteName: string) {
    return {
      logo: {
        url: '',
        alt: 'Footer Logo'
      },
      copyright: ` ${new Date().getFullYear()} ${websiteName}. All rights reserved.`,
      links: [
        { text: 'Home', buttonTarget: { type: 'internal', value: '/' } },
        { text: 'Features', buttonTarget: { type: 'internal', value: '/features' } },
        { text: 'Pricing', buttonTarget: { type: 'internal', value: '/pricing' } },
        { text: 'About', buttonTarget: { type: 'internal', value: '/about' } }
      ],
      socialMedia: []
    };
  }
  
  async updateWebsiteConfig(websiteId: string, websiteUpdateDto: WebsiteUpdateDto) {
    try {
      // 先检查网站是否存在
      const website = await this.prisma.website.findUnique({
        where: { id: websiteId }
      });

      if (!website) {
        return {
          success: false,
          error: 'Website not found'
        };
      }

      // 获取现有的配置
      const currentConfig = website.configuration as any || {};
      
      // 合并新的配置，支持 pageWidth, colorMode, theme 等字段
      const updatedConfiguration = {
        ...(currentConfig as object),
        ...(websiteUpdateDto.configuration || {})
      };
      
      // 更新网站
      const data = await this.prisma.website.update({
        where: { id: websiteId },
        data: {
          ...websiteUpdateDto,
          configuration: updatedConfiguration
        }
      });
      
      return {
        success: true,
        data,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  async updateWebsiteNav(websiteId: string, websiteUpdateNavDto: WebsiteUpdateNavDto) {
    this.logger.debug('Updating website nav', {
      websiteId,
      type: websiteUpdateNavDto.type,
      lang: websiteUpdateNavDto.lang,
      variant: websiteUpdateNavDto.data.variant || 'default'
    });

    // 获取网站信息，确认网站存在
    const website = await this.prisma.website.findFirst({ 
      where: { id: websiteId },
      include: { user: true } // 包含用户信息以获取 createdBy
    });

    if (!website) {
      this.logger.error(`Website not found with ID: ${websiteId}`);
      throw new Error(`Website not found with ID: ${websiteId}`);
    }

    // 确定是更新 Header 还是 Footer
    const isHeader = websiteUpdateNavDto.type === 'Header';
    const language = websiteUpdateNavDto.lang as Language; // 类型转换为 Language
    const variant = websiteUpdateNavDto.data.variant || 'default';
    const userId = website.userId; // 使用 userId 而不是 createdBy

    try {
      return await this.prisma.$transaction(async (tx) => {
        // 1. 查找对应语言的 Header/Footer 记录
        let component;
        
        if (isHeader) {
          component = await tx.websiteHeader.findFirst({
            where: {
              websiteId,
              language,
              isDeleted: false
            },
            include: {
              draftVersion: true // 包含草稿版本
            }
          });
        } else {
          component = await tx.websiteFooter.findFirst({
            where: {
              websiteId,
              language,
              isDeleted: false
            },
            include: {
              draftVersion: true // 包含草稿版本
            }
          });
        }

        // 2. 如果不存在，创建新记录
        if (!component) {
          this.logger.debug(`Creating new ${isHeader ? 'Header' : 'Footer'} for language: ${language}`);
          
          if (isHeader) {
            component = await tx.websiteHeader.create({
              data: {
                websiteId,
                language,
                variant, // 初始设置变体
                status: ComponentStatus.INITIALIZED,
                isDeleted: false,
                createdBy: userId
              }
            });
          } else {
            component = await tx.websiteFooter.create({
              data: {
                websiteId,
                language,
                variant, // 初始设置变体
                status: ComponentStatus.INITIALIZED,
                isDeleted: false,
                createdBy: userId
              }
            });
          }
        }

        // 3. 获取当前最高版本号
        let currentVersion = 1;
        
        if (isHeader) {
          const latestVersion = await tx.websiteHeaderVersion.findFirst({
            where: { headerId: component.id },
            orderBy: { versionNumber: 'desc' }
          });
          
          if (latestVersion) {
            currentVersion = latestVersion.versionNumber + 1;
          }
        } else {
          const latestVersion = await tx.websiteFooterVersion.findFirst({
            where: { footerId: component.id },
            orderBy: { versionNumber: 'desc' }
          });
          
          if (latestVersion) {
            currentVersion = latestVersion.versionNumber + 1;
          }
        }

        // 4. 创建新版本，包含变体信息
        let newVersionId;
        
        if (isHeader) {
          // 使用类型断言直接转换，避免序列化/反序列化
          const configData = websiteUpdateNavDto.data as unknown as Prisma.JsonValue;
          
          const newVersion = await tx.websiteHeaderVersion.create({
            data: {
              headerId: component.id,
              versionNumber: currentVersion,
              variant: variant,
              configuration: configData,
              createdBy: userId,
              status: VersionStatus.DRAFT
            }
          });
          newVersionId = newVersion.id;
          
          // 5. 更新 Header 记录，指向新版本
          component = await tx.websiteHeader.update({
            where: { id: component.id },
            data: { 
              draftVersionId: newVersion.id,
              variant: variant, // 同时更新主记录的变体字段
              status: component.status === ComponentStatus.INITIALIZED ? 
                ComponentStatus.DRAFT : 
                (component.publishedVersionId ? ComponentStatus.MODIFIED : ComponentStatus.DRAFT)
            },
            include: {
              draftVersion: true
            }
          });
        } else {
          // 使用类型断言直接转换，避免序列化/反序列化
          const configData = websiteUpdateNavDto.data as unknown as Prisma.JsonValue;
          
          const newVersion = await tx.websiteFooterVersion.create({
            data: {
              footerId: component.id,
              versionNumber: currentVersion,
              variant: variant,
              configuration: configData,
              createdBy: userId,
              status: VersionStatus.DRAFT
            }
          });
          newVersionId = newVersion.id;
          
          // 5. 更新 Footer 记录，指向新版本
          component = await tx.websiteFooter.update({
            where: { id: component.id },
            data: { 
              draftVersionId: newVersion.id,
              variant: variant, // 同时更新主记录的变体字段
              status: component.status === ComponentStatus.INITIALIZED ? 
                ComponentStatus.DRAFT : 
                (component.publishedVersionId ? ComponentStatus.MODIFIED : ComponentStatus.DRAFT)
            },
            include: {
              draftVersion: true
            }
          });
        }
        
        // 6. 返回更新后的信息
        return {
          success: true,
          data: {
            id: component.id,
            type: websiteUpdateNavDto.type,
            language,
            variant,
            versionId: newVersionId,
            versionNumber: currentVersion
          }
        };
      });
    } catch (error) {
      this.logger.error(`Error updating ${isHeader ? 'header' : 'footer'}:`, error);
      throw new Error(`Failed to update ${isHeader ? 'header' : 'footer'}: ${error.message}`);
    }
  }

  async setWebsiteConfig(websiteId: string, websiteConfig: WebsiteConfigDto) {
    let language: any = Language.EN;
    const sitemap = websiteConfig.sitemap;

    const links = [];

    sitemap.forEach((item) => {
      links.push({
        id: nanoid(7),
        text: item.name,
        buttonTarget: {
          value: item.slug, 
        },
        // slug: item.slug
      })
    })
    const actions: any = websiteConfig.actions.map((item) => {
      return {
        id: nanoid(7),
        text: item.name,
        appearance: {
          displayType: 'button',
          size: 'small',
        },
        buttonTarget: {
          value: item.slug,
        },
        // slug: item.slug
      };
    });
    const copyright: string = websiteConfig.copyright;

    delete websiteConfig.sitemap;
    delete websiteConfig.actions;
    delete websiteConfig.copyright;

    const languageMap: { [key: string]: Language } = {
      'EN': Language.EN,
      'CN': Language.CN,
      'ZH': Language.ZH,
      'ES': Language.ES,
      'HI': Language.HI,
      'FR': Language.FR, 
      'DE': Language.DE,
      'RU': Language.RU,
      'PT': Language.PT,
      'AR': Language.AR,
      'JP': Language.JP,
      'KR': Language.KR,
      'IT': Language.IT,
      'TR': Language.TR,
      'PL': Language.PL,
      'NL': Language.NL,
      'ID': Language.ID,
      'TH': Language.TH,
      'VI': Language.VI,
      'SV': Language.SV,
    };

    language = languageMap[sitemap[0].language];

    const website = await this.prisma.website.update({
     where: {
      id: websiteId,
     },
     data: {
      configuration: websiteConfig as any,
      // language,
      headers: {
        [language]: {
          id: nanoid(7),
          links,
          actions,
        }
      },
      footers: {
        [language]: {
          id: nanoid(7),
          links,
          copyright,
        }
      },
     },
    });

    const pages = sitemap.map((item) => {
      
      let pageType: any = PageType.LANDING;
      let metaKeywords = item.keyword;
          
      const pageTypeMap: { [key: string]: PageType } = {
        'landingPage': PageType.LANDING,
        'aboutPage': PageType.ABOUT,
        'featurePage': PageType.FEATURE,
        'faqPage': PageType.FAQ,
        'pricingPage': PageType.PRICING,
        'termsPage': PageType.TERMS,
        'blogPage': PageType.BLOG,
      };
      
      pageType = pageTypeMap[item.type];
      
      delete item.type;
      delete item.keyword;
     
      return {
        ...item,
        language,
        pageType,
        metaKeywords,
        nanoid: nanoid(),
        userId: website.userId,
        workspaceId: website.workspaceId,
        websiteId,
      };
    });

    await this.prisma.page.createMany({
      data: pages,
    });

    return {
      success: true,
      data: { website, pages },
    };
  }

  async setWebsiteQuota(websiteId: string, websiteQuotaDto: WebsiteQuotaDto) {
    // 示例逻辑，实际应用中应连接数据库并设置工作空间配额
    return {
      success: true,
      data: {
        websiteId: websiteId,
        ...websiteQuotaDto,
      },
    };
  }

  async updateWebsiteQuotaUsage(websiteId: string, websiteQuotaUsageDto: WebsiteQuotaUsageDto) {
    // 示例逻辑，实际应用中应连接数据库并更新工作空间配额使用情况
    return {
      success: true,
      data: {
        websiteId: websiteId,
        ...websiteQuotaUsageDto,
      },
    };
  }

  // 获取网站的自定义域名
  async getCustomDomain(websiteId: string) {
    const customDomain = await this.prisma.customDomain.findFirst({
      where: { websiteId },
    });

    if (!customDomain) {
      return {
        success: true,
        data: null,
        message: 'No custom domain found'
      };
    }

    return {
      success: true,
      data: {
        ...customDomain,
        verificationCode: this.generateVerificationCode(websiteId, customDomain.domain),
      },
      message: 'Custom domain retrieved successfully'
    };
  }

  // 设置自定义域名
  async setCustomDomain(websiteId: string, domain: string) {
    // 检查域名是否已被使用
    const existingDomain = await this.prisma.customDomain.findUnique({
      where: { domain },
    });

    if (existingDomain) {
      throw new ConflictException('Domain is already in use');
    }

    // 检查网站是否已有自定义域名
    const existingCustomDomain = await this.prisma.customDomain.findFirst({
      where: { websiteId },
    });

    let customDomain;
    if (existingCustomDomain) {
      // 如果已有，则更新
      customDomain = await this.prisma.customDomain.update({
        where: { id: existingCustomDomain.id },
        data: {
          domain,
          status: DomainStatus.PENDING,
        },
      });
    } else {
      // 如果没有，则创建新的
      customDomain = await this.prisma.customDomain.create({
        data: {
          domain,
          websiteId,
          status: DomainStatus.PENDING,
        },
      });
    }

    return {
      success: true,
      data: {
        ...customDomain,
        verificationCode: this.generateVerificationCode(websiteId, customDomain.domain),
      },
      message: 'Custom domain set successfully'
    };
  }

  // 删除自定义域名
  async deleteCustomDomain(websiteId: string) {
    const customDomain = await this.prisma.customDomain.findFirst({
      where: { websiteId },
    });

    if (!customDomain) {
      throw new NotFoundException('Custom domain not found');
    }

    return this.prisma.customDomain.delete({
      where: { id: customDomain.id },
    });
  }

  // 生成域名验证码
  private generateVerificationCode(websiteId: string, domain: string): string {
    // 使用 websiteId、域名和密钥生成唯一的验证码
    const data = `${websiteId}-${domain}-${process.env.DOMAIN_VERIFICATION_SECRET || 'default-secret'}`;
    const hash = createHash('sha256').update(data).digest('hex');
    return `litpage-verify-${hash.substring(0, 16)}`; // 使用前16位以保持简短
  }

  // 验证域名 DNS 设置
  async verifyCustomDomain(websiteId: string) {
    const customDomain = await this.prisma.customDomain.findFirst({
      where: { websiteId },
    });

    if (!customDomain) {
      throw new NotFoundException('Custom domain not found');
    }

    const domain = customDomain.domain;
    const verificationCode = this.generateVerificationCode(websiteId, domain);

    try {
      // 获取 TXT 记录
      const txtRecords = await dns.resolveTxt(domain).catch(() => []);
      
      console.log('DNS verification:', {
        domain,
        txtRecords,
        verificationCode,
      });

      const isVerified = txtRecords.some(records => 
        records.some(record => record === verificationCode)
      );

      // 如果没有找到验证记录
      if (!isVerified) {
        await this.prisma.customDomain.update({
          where: { id: customDomain.id },
          data: {
            status: DomainStatus.PENDING,
          },
        });

        return {
          success: false,
          message: `Please add a TXT record to verify domain ownership:
Host: ${domain}
Type: TXT
Value: ${verificationCode}`,
          data: {
            domain: customDomain.domain,
            status: DomainStatus.PENDING,
            verificationCode,
          }
        };
      }

      // 域名已验证，更新状态为激活
      await this.prisma.customDomain.update({
        where: { id: customDomain.id },
        data: {
          status: DomainStatus.ACTIVE,
        },
      });

      return {
        success: true,
        message: 'Domain verified successfully',
        data: {
          domain: customDomain.domain,
          status: DomainStatus.ACTIVE,
        }
      };

    } catch (error) {
      console.error('DNS verification error:', {
        domain: customDomain.domain,
        error: error.message,
        stack: error.stack,
      });

      // DNS 解析错误
      await this.prisma.customDomain.update({
        where: { id: customDomain.id },
        data: {
          status: DomainStatus.FAILED,
        },
      });

      const errorMessage = error.code === 'ENODATA' 
        ? 'No TXT records found. Please add the verification TXT record and try again.'
        : error.message;

      return {
        success: false,
        message: `DNS verification failed: ${errorMessage}`,
        error: error.message,
        data: {
          domain: customDomain.domain,
          status: DomainStatus.FAILED,
          verificationCode,
        }
      };
    }
  }

  async validateDomain(domain: string) {
    // 基本格式验证
    const domainRegex = /^[a-z0-9][a-z0-9-]{1,61}[a-z0-9]$/;
    if (!domainRegex.test(domain)) {
      throw new BadRequestException('Domain can only contain lowercase letters, numbers, and hyphens');
    }

    // 长度验证
    if (domain.length < 3 || domain.length > 63) {
      throw new BadRequestException('Domain length must be between 3 and 63 characters');
    }

    // 检查连续的连字符
    if (domain.includes('--')) {
      throw new BadRequestException('Domain cannot contain consecutive hyphens');
    }

    // 检查保留关键词
    if (this.protectedDomains.includes(domain.toLowerCase())) {
      throw new BadRequestException('This domain name is reserved');
    }

    // 检查域名是否已被使用
    const existingDomain = await this.prisma.website.findFirst({
      where: { domain: domain.toLowerCase() }
    });

    if (existingDomain) {
      throw new ConflictException('Domain is already taken');
    }

    // 返回成功响应
    return {
      success: true,
      data: {
        available: true,
        domain: domain.toLowerCase()
      }
    };
  }

  /**
   * 获取网站的头尾组件列表
   * @param websiteId 网站ID
   * @param language 可选的语言过滤
   * @returns 头尾组件列表
   */
  async getWebsiteComponents(websiteId: string, language?: string) {
    this.logger.log('[GetWebsiteComponents] Retrieving website components', { 
      websiteId, 
      language: language || 'all languages' 
    });
    
    const query: any = {
      websiteId,
      isDeleted: false
    };
    
    if (language) {
      query.language = language;
      this.logger.debug(`[GetWebsiteComponents] Filtering by language: ${language}`);
    }
    
    try {
      // 获取头部组件
      this.logger.debug('[GetWebsiteComponents] Fetching header components');
      const headers = await this.prisma.websiteHeader.findMany({
        where: query,
        include: {
          draftVersion: true,
          publishedVersion: true
        }
      });
      
      this.logger.debug(`[GetWebsiteComponents] Found ${headers.length} header components`);
      
      // 获取尾部组件
      this.logger.debug('[GetWebsiteComponents] Fetching footer components');
      const footers = await this.prisma.websiteFooter.findMany({
        where: query,
        include: {
          draftVersion: true,
          publishedVersion: true
        }
      });
      
      this.logger.debug(`[GetWebsiteComponents] Found ${footers.length} footer components`);
      
      // 转换为前端友好的格式
      this.logger.debug('[GetWebsiteComponents] Transforming header components data');
      const headersData = headers.map(header => {
        const version = header.status === ComponentStatus.PUBLISHED 
          ? header.publishedVersion 
          : header.draftVersion;
          
        if (!version) {
          this.logger.warn(`[GetWebsiteComponents] Header ${header.id} has no available version`);
        }
          
        return {
          id: header.id,
          language: header.language,
          variant: header.variant,
          status: header.status,
          // 简化的配置信息，用于预览
          previewData: version
            ? {
                title: (version.configuration as any)?.brandName || header.variant,
                // 其他预览所需字段
              }
            : null
        };
      });
      
      this.logger.debug('[GetWebsiteComponents] Transforming footer components data');
      const footersData = footers.map(footer => {
        const version = footer.status === ComponentStatus.PUBLISHED 
          ? footer.publishedVersion 
          : footer.draftVersion;
          
        if (!version) {
          this.logger.warn(`[GetWebsiteComponents] Footer ${footer.id} has no available version`);
        }
          
        return {
          id: footer.id,
          language: footer.language,
          variant: footer.variant,
          status: footer.status,
          // 简化的配置信息，用于预览
          previewData: version
            ? {
                title: (version.configuration as any)?.brandName || footer.variant,
                // 其他预览所需字段
              }
            : null
        };
      });
      
      this.logger.log('[GetWebsiteComponents] Successfully retrieved website components', { 
        websiteId,
        headersCount: headersData.length, 
        footersCount: footersData.length 
      });
      
      return {
        success: true,
        data: {
          headers: headersData,
          footers: footersData
        }
      };
    } catch (error) {
      this.logger.error('[GetWebsiteComponents] Failed to get website components', {
        websiteId,
        language,
        error: error.message,
        stack: error.stack
      });
      
      throw new BadRequestException('Failed to get website components');
    }
  }
}
