import { Injectable, Logger } from '@nestjs/common';
import { Website, CustomDomain, Page, DomainStatus, Language } from '@prisma/client';

export interface DomainInfo {
  isCustomDomain: boolean;
  canonicalDomain: string;
  accessedDomain: string;
  protocol: string;
}

export interface CanonicalResult {
  canonicalUrl: string;
  alternateUrls: Array<{
    language: string;
    url: string;
  }>;
}

@Injectable()
export class CanonicalUrlService {
  private readonly logger = new Logger(CanonicalUrlService.name);

  /**
   * 解析当前访问域名信息，确定canonical应该使用的域名
   */
  resolveDomainInfo(
    website: Website & { customDomain?: CustomDomain | null },
    currentDomain: string
  ): DomainInfo {
    const protocol = process.env.NODE_ENV === 'production' ? 'https' : 'http';
    
    this.logger.debug(`[resolveDomainInfo] Processing domain: ${currentDomain} for website: ${website.domain}`);

    // 优先级1: 自定义域名且状态为ACTIVE
    if (this.isCustomDomainReady(website.customDomain)) {
      this.logger.debug(`[resolveDomainInfo] Using custom domain: ${website.customDomain!.domain}`);
      return {
        isCustomDomain: true,
        canonicalDomain: website.customDomain!.domain,
        accessedDomain: currentDomain,
        protocol
      };
    }
    
    // 优先级2: 二级域名
    const litPageDomain = `${website.domain}.lit.page`;
    this.logger.debug(`[resolveDomainInfo] Using subdomain: ${litPageDomain}`);
    
    return {
      isCustomDomain: false,
      canonicalDomain: litPageDomain,
      accessedDomain: currentDomain,
      protocol
    };
  }

  /**
   * 构建单个页面的canonical URL
   */
  buildCanonicalUrl(
    domainInfo: DomainInfo, 
    page: Page, 
    website: Website
  ): string {
    const { protocol, canonicalDomain } = domainInfo;
    
    // 处理语言路径
    let pathSegment = '';
    if (page.language !== website.defaultLanguage) {
      pathSegment = `/${page.language.toLowerCase()}`;
    }
    
    // 处理页面slug - 确保以/开头，但根页面除外
    let slug = page.slug;
    if (slug === '/') {
      slug = '';
    } else if (!slug.startsWith('/')) {
      slug = `/${slug}`;
    }
    
    const canonicalUrl = `${protocol}://${canonicalDomain}${pathSegment}${slug}`;
    
    this.logger.debug(`[buildCanonicalUrl] Generated: ${canonicalUrl}`, {
      domain: canonicalDomain,
      language: page.language,
      slug: page.slug,
      isCustomDomain: domainInfo.isCustomDomain
    });
    
    return canonicalUrl;
  }

  /**
   * 构建页面的所有语言版本的alternate URLs（用于hreflang）
   */
  buildAlternateUrls(
    domainInfo: DomainInfo,
    currentPage: Page,
    translatedPages: Page[],
    website: Website
  ): Array<{language: string, url: string}> {
    const alternateUrls: Array<{language: string, url: string}> = [];
    
    // 为每个翻译版本生成URL
    translatedPages.forEach(page => {
      const url = this.buildCanonicalUrl(domainInfo, page, website);
      alternateUrls.push({
        language: page.language.toLowerCase(),
        url
      });
    });
    
    // 确保当前页面也包含在内
    const currentPageUrl = this.buildCanonicalUrl(domainInfo, currentPage, website);
    const currentLanguage = currentPage.language.toLowerCase();
    
    if (!alternateUrls.find(alt => alt.language === currentLanguage)) {
      alternateUrls.push({
        language: currentLanguage,
        url: currentPageUrl
      });
    }
    
    this.logger.debug(`[buildAlternateUrls] Generated ${alternateUrls.length} alternate URLs`, {
      currentLanguage,
      alternateLanguages: alternateUrls.map(alt => alt.language)
    });
    
    return alternateUrls;
  }

  /**
   * 生成完整的canonical结果，包含canonical URL和alternate URLs
   */
  generateCanonicalResult(
    domainInfo: DomainInfo,
    currentPage: Page,
    translatedPages: Page[],
    website: Website
  ): CanonicalResult {
    const canonicalUrl = this.buildCanonicalUrl(domainInfo, currentPage, website);
    const alternateUrls = this.buildAlternateUrls(domainInfo, currentPage, translatedPages, website);
    
    return {
      canonicalUrl,
      alternateUrls
    };
  }

  /**
   * 验证自定义域名是否可用于canonical
   */
  isCustomDomainReady(customDomain?: CustomDomain | null): boolean {
    if (!customDomain) {
      return false;
    }
    
    const isReady = customDomain.status === DomainStatus.ACTIVE;
    
    if (!isReady) {
      this.logger.debug(`[isCustomDomainReady] Custom domain not ready`, {
        domain: customDomain.domain,
        status: customDomain.status
      });
    }
    
    return isReady;
  }

  /**
   * 构建fallback canonical URL（当自定义域名不可用时）
   */
  buildFallbackCanonical(website: Website, page: Page): string {
    const protocol = process.env.NODE_ENV === 'production' ? 'https' : 'http';
    const litPageDomain = `${website.domain}.lit.page`;
    
    let pathSegment = '';
    if (page.language !== website.defaultLanguage) {
      pathSegment = `/${page.language.toLowerCase()}`;
    }
    
    let slug = page.slug;
    if (slug === '/') {
      slug = '';
    } else if (!slug.startsWith('/')) {
      slug = `/${slug}`;
    }
    
    const fallbackUrl = `${protocol}://${litPageDomain}${pathSegment}${slug}`;
    
    this.logger.warn(`[buildFallbackCanonical] Using fallback canonical: ${fallbackUrl}`);
    
    return fallbackUrl;
  }

  /**
   * 验证域名的可访问性（可选的额外验证）
   */
  async verifyDomainAccessibility(domain: string): Promise<boolean> {
    try {
      // 这里可以添加实际的域名可访问性检查
      // 例如发送HTTP请求验证域名是否正常解析
      
      // 简单的域名格式验证
      const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])*$/;
      return domainRegex.test(domain);
    } catch (error) {
      this.logger.error(`[verifyDomainAccessibility] Domain verification failed for ${domain}:`, error);
      return false;
    }
  }

  /**
   * 从域名字符串中提取子域名部分
   */
  extractSubdomain(domain: string): string {
    // 处理特殊域名映射
    const sites = {
      'lit.page': 'litpage',
      'www.imgpipe.dev': 'imgpipe',
    };

    if (sites[domain]) {
      return sites[domain];
    }

    // 提取子域名 - 假设格式为 subdomain.lit.page
    const parts = domain.split('.');
    if (parts.length >= 3) {
      // 移除最后两部分 (.lit.page)，剩下的就是子域名
      return parts.slice(0, -2).join('.');
    }

    // 如果不是标准格式，返回原域名
    return domain;
  }
} 