# LitPage 落地页按钮设计令牌规范 V2

本文档定义了 LitPage 落地页系统中按钮组件的设计令牌规范，旨在创建视觉一致、提升转化率且易于维护的按钮系统。本规范基于现有的设计令牌系统，确保按钮组件与整体设计语言协调一致。

## 目录

- [设计原则](#设计原则)
- [设计令牌结构](#设计令牌结构)
- [按钮变体](#按钮变体)
- [按钮尺寸](#按钮尺寸)
- [按钮状态](#按钮状态)
- [主题系统](#主题系统)
- [响应式设计](#响应式设计)
- [明暗模式](#明暗模式)
- [转化率优化](#转化率优化)
- [可访问性](#可访问性)
- [实施指南](#实施指南)
- [Tailwind CSS 集成](#tailwind-css-集成)

## 设计原则

LitPage 按钮设计遵循以下核心原则：

1. **视觉层级明确**：主次按钮通过视觉差异清晰传达重要性
2. **一致性与可识别性**：在整个系统中保持一致的按钮风格
3. **响应式适应**：按钮在不同设备和屏幕尺寸上保持最佳体验
4. **转化率导向**：按钮设计优先考虑提高转化率
5. **可访问性保障**：符合 WCAG AA 标准的可访问性要求

## 设计令牌结构

LitPage 按钮设计令牌采用三层结构，与现有设计系统保持一致：

1. **全局令牌**：基础颜色、尺寸、间距等，如 `--primary`、`--radius`
2. **组件令牌**：按钮专用变量，如 `--button-radius`、`--button-animation-duration`
3. **Tailwind 类**：直接应用的工具类，如 `text-center`、`font-medium`

### 核心变量定义

```css
:root {
  /* 基础变量 - 继承自全局设计系统 */
  --button-radius: var(--radius);
  --button-animation-duration: 200ms;
  
  /* 尺寸变量 */
  --button-large-height: 3.5rem;     /* 56px */
  --button-medium-height: 2.75rem;   /* 44px */
  --button-small-height: 2.25rem;    /* 36px */
  
  /* 内边距变量 */
  --button-large-padding-x: 2rem;    /* 32px */
  --button-medium-padding-x: 1.5rem; /* 24px */
  --button-small-padding-x: 1rem;    /* 16px */
  
  /* 内容密度比例 */
  --button-padding-to-height-ratio: 0.57;
  
  /* 状态变量 */
  --button-hover-opacity: 0.9;
  --button-active-opacity: 0.8;
  --button-disabled-opacity: 0.65;
  
  /* 渐变变量 */
  --button-gradient: linear-gradient(to right, var(--primary), var(--accent));
  --button-gradient-hover: linear-gradient(to right, var(--primary), var(--secondary));
  
  /* 动画变量 */
  --button-transition: all var(--button-animation-duration) cubic-bezier(0.4, 0, 0.2, 1);
  --button-pulse-scale: 1.05;
  --button-pulse-duration: 2s;
}
```

## 按钮变体

按钮变体定义了不同用途和重要性的按钮样式。

### 主要按钮 (Primary)

用于主要转化行动，每个区块通常只有一个。

```css
.btn-primary {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}
```

### 次要按钮 (Secondary)

用于次要行动，辅助主要按钮。

```css
.btn-secondary {
  background-color: hsl(var(--secondary));
  color: hsl(var(--secondary-foreground));
}
```

### 轮廓按钮 (Outline)

用于低强调度选项，通常用于取消或返回。

```css
.btn-outline {
  background-color: transparent;
  border: 2px solid hsl(var(--primary));
  color: hsl(var(--primary));
}
```

### 文本按钮 (Text)

用于最低强调度，辅助导航。

```css
.btn-text {
  background-color: transparent;
  color: hsl(var(--primary));
}
```

### 危险按钮 (Destructive)

用于警告性操作。

```css
.btn-destructive {
  background-color: hsl(var(--destructive));
  color: hsl(var(--destructive-foreground));
}
```

### 渐变按钮 (Gradient)

使用渐变背景，增强视觉吸引力。

```css
.btn-gradient {
  background: var(--button-gradient);
  color: hsl(var(--primary-foreground));
}
```

## 按钮尺寸

按钮尺寸定义了不同场景下的按钮大小。

### 大型按钮 (Large)

用于主要CTA，页面顶部或底部。

```css
.btn-large {
  height: var(--button-large-height);
  padding-left: var(--button-large-padding-x);
  padding-right: var(--button-large-padding-x);
  font-size: var(--body-large-size);
  border-radius: var(--button-radius);
}
```

### 中型按钮 (Medium)

用于页面主体中的CTA。

```css
.btn-medium {
  height: var(--button-medium-height);
  padding-left: var(--button-medium-padding-x);
  padding-right: var(--button-medium-padding-x);
  font-size: var(--body-base-size);
  border-radius: var(--button-radius);
}
```

### 小型按钮 (Small)

用于辅助功能，内嵌在内容中。

```css
.btn-small {
  height: var(--button-small-height);
  padding-left: var(--button-small-padding-x);
  padding-right: var(--button-small-padding-x);
  font-size: var(--body-small-size);
  border-radius: calc(var(--button-radius) - 2px);
}
```

## 按钮状态

按钮状态定义了不同交互阶段的视觉反馈。

### 默认状态

基础外观，定义按钮的初始样式。

### 悬停状态 (Hover)

微妙变化，提示可交互。

```css
.btn:hover {
  opacity: var(--button-hover-opacity);
}

.btn-gradient:hover {
  background: var(--button-gradient-hover);
}
```

### 活动状态 (Active)

按下效果，提供触觉反馈。

```css
.btn:active {
  opacity: var(--button-active-opacity);
}
```

### 聚焦状态 (Focus)

键盘导航时的焦点指示。

```css
.btn:focus-visible {
  outline: none;
  ring: 2px solid hsl(var(--ring));
  ring-offset: 2px;
}
```

### 禁用状态 (Disabled)

降低对比度，表示不可用。

```css
.btn:disabled {
  opacity: var(--button-disabled-opacity);
  cursor: not-allowed;
}
```

## 主题系统

LitPage 按钮系统支持全局主题，通过 HTML 根元素类名控制。

### 主题应用方式

```html
<!-- 默认主题 + 亮模式 -->
<html lang="zh">

<!-- 默认主题 + 暗模式 -->
<html lang="zh" class="dark">

<!-- 科技主题 + 亮模式 -->
<html lang="zh" class="theme-tech">

<!-- 科技主题 + 暗模式 -->
<html lang="zh" class="dark theme-tech">
```

### 主题变量定义

```css
/* 科技主题 */
.theme-tech {
  --primary: hsl(230, 80%, 50%);
  --secondary: hsl(200, 75%, 55%);
  --button-radius: 0.125rem;
  --button-animation-duration: 150ms;
  --button-gradient: linear-gradient(to right, hsl(230, 80%, 50%), hsl(200, 75%, 55%));
}

/* 创意主题 */
.theme-creative {
  --primary: hsl(320, 80%, 55%);
  --secondary: hsl(280, 75%, 60%);
  --button-radius: 1.5rem;
  --button-gradient: linear-gradient(to right bottom, hsl(320, 80%, 55%), hsl(280, 75%, 60%));
}

/* 金融主题 */
.theme-finance {
  --primary: hsl(210, 70%, 35%);
  --secondary: hsl(180, 50%, 40%);
  --button-radius: 0.25rem;
  --button-gradient: linear-gradient(to right, hsl(210, 70%, 35%), hsl(180, 50%, 40%));
}

/* 教育主题 */
.theme-education {
  --primary: hsl(190, 70%, 45%);
  --secondary: hsl(150, 60%, 50%);
  --button-radius: 0.75rem;
  --button-gradient: linear-gradient(to right, hsl(190, 70%, 45%), hsl(150, 60%, 50%));
}
```

### 季节与活动主题

```css
/* 新年主题 */
.theme-newyear {
  --primary: hsl(0, 80%, 50%);
  --secondary: hsl(30, 90%, 50%);
  --button-gradient: linear-gradient(to right, hsl(0, 80%, 50%), hsl(30, 90%, 50%));
}

/* 促销主题 */
.theme-sale {
  --primary: hsl(350, 100%, 60%);
  --secondary: hsl(300, 100%, 45%);
  --button-gradient: linear-gradient(to right, hsl(350, 100%, 60%), hsl(300, 100%, 45%));
  --button-pulse-scale: 1.08;
  --button-pulse-duration: 1.5s;
}
```

### 局部主题覆盖

为特定区块提供主题覆盖机制：

```css
/* 局部主题覆盖类 */
.theme-override-tech {
  --primary: hsl(230, 80%, 50%);
  --secondary: hsl(200, 75%, 55%);
  --button-radius: 0.125rem;
  --button-gradient: linear-gradient(to right, hsl(230, 80%, 50%), hsl(200, 75%, 55%));
}
```

## 响应式设计

按钮在不同屏幕尺寸下的适应性调整。

### 移动端优化

```css
@media (max-width: 640px) {
  :root {
    --button-large-padding-x: 1.75rem;  /* 28px */
    --button-medium-padding-x: 1.25rem; /* 20px */
    --button-small-padding-x: 0.875rem; /* 14px */
  }
}
```

### 宽屏模式优化

```css
/* 宽屏模式 */
html.page-width-wide {
  --button-large-height: 3.75rem;      /* 60px */
  --button-medium-height: 3rem;        /* 48px */
  --button-small-height: 2.5rem;       /* 40px */
  --button-large-padding-x: 2.25rem;   /* 36px */
  --button-medium-padding-x: 1.75rem;  /* 28px */
  --button-small-padding-x: 1.25rem;   /* 20px */
}

/* 满屏模式 */
html.page-width-full {
  --button-large-height: 4rem;         /* 64px */
  --button-medium-height: 3.25rem;     /* 52px */
  --button-small-height: 2.75rem;      /* 44px */
  --button-large-padding-x: 2.5rem;    /* 40px */
  --button-medium-padding-x: 2rem;     /* 32px */
  --button-small-padding-x: 1.5rem;    /* 24px */
}
```

## 明暗模式

按钮在明暗模式下的适应性调整。

### 暗模式变量调整

```css
.dark {
  /* 暗模式下的状态增强 */
  --button-hover-opacity: 0.8;
  --button-active-opacity: 0.7;
  --button-focus-ring-offset: 3px;
  
  /* 暗模式下的渐变调整 */
  --button-gradient: linear-gradient(to right, hsl(var(--primary)), hsl(var(--accent) / 0.8));
  --button-gradient-hover: linear-gradient(to right, hsl(var(--primary)), hsl(var(--ring)));
}
```

### 主题与暗模式组合

```css
/* 科技主题 + 暗模式 */
.dark.theme-tech {
  --primary: hsl(230, 70%, 60%);
  --secondary: hsl(200, 65%, 65%);
}

/* 创意主题 + 暗模式 */
.dark.theme-creative {
  --primary: hsl(320, 70%, 65%);
  --secondary: hsl(280, 65%, 70%);
}
```

## 转化率优化

提高按钮转化率的设计考量。

### 视觉突出

```css
.btn-cta {
  animation: pulse var(--button-pulse-duration) infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(var(--button-pulse-scale)); }
}
```

### 紧迫感指示

```css
.btn-urgency {
  position: relative;
}

.btn-urgency::after {
  content: "";
  position: absolute;
  inset: -4px;
  border: 2px solid hsl(var(--primary));
  border-radius: calc(var(--button-radius) + 4px);
  animation: pulse var(--button-pulse-duration) infinite;
}
```

## 可访问性

确保按钮符合可访问性标准。

### 触摸目标尺寸

```css
:root {
  --button-min-touch-target: 44px; /* 符合WCAG标准 */
}

/* 确保小按钮在触摸设备上有足够大的点击区域 */
@media (pointer: coarse) {
  .btn-small {
    min-height: var(--button-min-touch-target);
    min-width: var(--button-min-touch-target);
  }
}
```

### 焦点指示器

```css
.btn:focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px hsl(var(--background)), 0 0 0 4px hsl(var(--ring));
}
```

### 色彩对比度

确保按钮文本与背景的对比度符合 WCAG AA 标准（4.5:1）。

## 实施指南

### 按钮布局最佳实践

1. **主次按钮并排放置时**：主按钮应放在右侧（LTR布局）或左侧（RTL布局）
2. **垂直堆叠时**：主按钮应放在顶部
3. **按钮间距**：并排按钮间距应为 `1rem`（16px），垂直堆叠时为 `0.75rem`（12px）
4. **按钮密度**：每个区块通常不超过2个按钮，最多不超过3个

### 按钮文案最佳实践

1. **行动导向**：使用"开始免费试用"而非"免费试用"
2. **简洁明了**：通常3-5个字，最多不超过8个字
3. **一致性**：相似功能的按钮使用相似文案
4. **紧迫感**：适当添加时间限制或数量限制，如"限时优惠"

## Tailwind CSS 集成

### 扩展 Tailwind 主题

```js
// tailwind.config.js
module.exports = {
  theme: {
    extend: {
      height: {
        'btn-large': 'var(--button-large-height)',
        'btn-medium': 'var(--button-medium-height)',
        'btn-small': 'var(--button-small-height)',
      },
      padding: {
        'btn-large-x': 'var(--button-large-padding-x)',
        'btn-medium-x': 'var(--button-medium-padding-x)',
        'btn-small-x': 'var(--button-small-padding-x)',
      },
      borderRadius: {
        'btn': 'var(--button-radius)',
      },
      transitionDuration: {
        'btn': 'var(--button-animation-duration)',
      },
      backgroundImage: {
        'btn-gradient': 'var(--button-gradient)',
        'btn-gradient-hover': 'var(--button-gradient-hover)',
      },
      animation: {
        'btn-pulse': 'pulse var(--button-pulse-duration) infinite',
      },
      keyframes: {
        'pulse': {
          '0%, 100%': { transform: 'scale(1)' },
          '50%': { transform: 'scale(var(--button-pulse-scale))' },
        },
      },
    },
  },
  // 启用暗模式类
  darkMode: 'class',
}
```

### 组件实现示例

```jsx
// Button.tsx
const Button = ({
  variant = 'primary',
  size = 'medium',
  children,
  className,
  ...props
}) => {
  // 基础类 - 所有按钮共享
  const baseClasses = 'inline-flex items-center justify-center font-medium transition-all duration-btn';
  
  // 变体类
  const variantClasses = {
    primary: 'bg-primary text-primary-foreground hover:opacity-90 active:opacity-80',
    secondary: 'bg-secondary text-secondary-foreground hover:opacity-90 active:opacity-80',
    outline: 'bg-transparent border-2 border-primary text-primary hover:bg-primary/10',
    text: 'bg-transparent text-primary hover:text-primary/80',
    destructive: 'bg-destructive text-destructive-foreground hover:opacity-90',
    gradient: 'bg-btn-gradient text-primary-foreground hover:bg-btn-gradient-hover',
  };
  
  // 尺寸类
  const sizeClasses = {
    large: 'h-btn-large px-btn-large-x text-body-large rounded-btn',
    medium: 'h-btn-medium px-btn-medium-x text-body-base rounded-btn',
    small: 'h-btn-small px-btn-small-x text-body-small rounded-btn',
  };
  
  // 状态类
  const stateClasses = 'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 dark:focus-visible:ring-offset-3 disabled:opacity-65 disabled:cursor-not-allowed';
  
  return (
    <button
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${stateClasses} ${className}`}
      {...props}
    >
      {children}
    </button>
  );
};

export default Button;
```

### 使用示例

```jsx
// 默认主题下的按钮变体
<div className="flex gap-4">
  <Button variant="primary">主要按钮</Button>
  <Button variant="secondary">次要按钮</Button>
  <Button variant="outline">轮廓按钮</Button>
  <Button variant="text">文本按钮</Button>
  <Button variant="destructive">危险按钮</Button>
  <Button variant="gradient">渐变按钮</Button>
</div>

// 不同尺寸的按钮
<div className="flex items-center gap-4">
  <Button variant="primary" size="large">大型按钮</Button>
  <Button variant="primary" size="medium">中型按钮</Button>
  <Button variant="primary" size="small">小型按钮</Button>
</div>

// 带图标的按钮
<Button variant="primary" size="medium">
  开始免费试用 <ArrowRight className="ml-2" />
</Button>

// 转化率优化按钮
<Button variant="primary" size="large" className="animate-btn-pulse">
  立即注册
</Button>

// 局部主题覆盖示例
<div className="theme-override-tech">
  <Button variant="primary">科技主题按钮</Button>
  <Button variant="gradient">科技渐变按钮</Button>
</div>
```

### 主题切换实现

```jsx
// ThemeSwitcher.jsx
import { useState, useEffect } from 'react';

const ThemeSwitcher = () => {
  const themes = ['default', 'tech', 'creative', 'finance', 'education'];
  const [currentTheme, setCurrentTheme] = useState('default');
  const [isDark, setIsDark] = useState(false);
  
  useEffect(() => {
    // 移除所有主题类
    document.documentElement.classList.remove(...themes.map(t => t === 'default' ? '' : `theme-${t}`));
    
    // 添加当前主题类（如果不是默认主题）
    if (currentTheme !== 'default') {
      document.documentElement.classList.add(`theme-${currentTheme}`);
    }
    
    // 处理暗模式
    if (isDark) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
    
    // 保存用户偏好
    localStorage.setItem('theme', currentTheme);
    localStorage.setItem('darkMode', isDark ? 'dark' : 'light');
  }, [currentTheme, isDark]);
  
  return (
    <div className="theme-switcher">
      <div className="theme-options">
        {themes.map(theme => (
          <button
            key={theme}
            className={`theme-option ${theme === currentTheme ? 'active' : ''}`}
            onClick={() => setCurrentTheme(theme)}
          >
            {theme === 'default' ? '默认主题' : `${theme}主题`}
          </button>
        ))}
      </div>
      
      <div className="dark-mode-toggle">
        <button onClick={() => setIsDark(!isDark)}>
          {isDark ? '切换到亮模式' : '切换到暗模式'}
        </button>
      </div>
    </div>
  );
};

export default ThemeSwitcher;
```

---

本规范文档旨在指导 LitPage 落地页系统中按钮的设计和实现，确保视觉一致性和最佳用户体验。设计令牌系统的目标是平衡美观性、功能性和一致性，同时优化转化率。

本规范与现有的设计令牌系统保持一致，采用基于 HTML 根元素类名的全局主题控制机制，确保按钮组件能够无缝集成到整体设计语言中，并在不同主题、明暗模式、屏幕尺寸和用户交互状态下保持一致的体验。
