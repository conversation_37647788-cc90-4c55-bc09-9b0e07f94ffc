# LitPage 落地页按钮设计令牌规范

本文档定义了 LitPage 落地页系统中按钮组件的设计令牌规范，旨在创建视觉一致、提升转化率且易于维护的按钮系统。本规范基于现有的设计令牌系统，确保按钮组件与整体设计语言协调一致。

## 目录

- [设计原则](#设计原则)
- [设计令牌层次结构](#设计令牌层次结构)
- [按钮层级与意图](#按钮层级与意图)
- [按钮尺寸](#按钮尺寸)
- [按钮内容适应性](#按钮内容适应性)
- [内容密度一致性](#内容密度一致性)
- [按钮状态](#按钮状态)
- [特殊变体](#特殊变体)
- [按钮主题设置](#按钮主题设置)
- [明暗模式支持](#明暗模式支持)
- [响应式设计](#响应式设计)
- [转化率优化](#转化率优化)
- [可访问性](#可访问性)
- [实施指南](#实施指南)
- [Tailwind CSS 集成](#tailwind-css-集成)

## 设计原则

LitPage 按钮设计遵循以下核心原则：

1. **视觉层级明确**：主次按钮通过视觉差异清晰传达重要性
2. **一致性与可识别性**：在整个系统中保持一致的按钮风格
3. **响应式适应**：按钮在不同设备和屏幕尺寸上保持最佳体验
4. **转化率导向**：按钮设计优先考虑提高转化率
5. **可访问性保障**：符合 WCAG AA 标准的可访问性要求

## 设计令牌层次结构

LitPage 按钮设计令牌采用三层结构，与现有设计系统保持一致：

1. **全局令牌**：基础颜色、尺寸、间距等，如 `--primary`、`--radius`
2. **组件令牌**：按钮专用变量，如 `--button-primary-bg`、`--button-large-height`
3. **Tailwind 类**：直接应用的工具类，如 `text-center`、`font-medium`

设计令牌优先级原则：

- **高优先级**：颜色、尺寸、间距、圆角、动画等高变化频率、高复用价值的属性
- **中优先级**：阴影、边框、焦点状态等中等变化频率的属性
- **低优先级**：文本对齐、字体粗细等低变化频率的属性，优先使用 Tailwind 类

## 按钮层级与意图

### 主要按钮 (Primary)

用于主要转化行动，每个区块通常只有一个。

```css
--button-primary-bg: var(--primary);
--button-primary-text: var(--primary-foreground);
--button-primary-border: transparent;
--button-primary-hover-bg: hsl(var(--primary) / 0.9);
--button-primary-hover-text: var(--primary-foreground);
```

### 次要按钮 (Secondary)

用于次要行动，辅助主要按钮。

```css
--button-secondary-bg: var(--secondary);
--button-secondary-text: var(--secondary-foreground);
--button-secondary-border: transparent;
--button-secondary-hover-bg: hsl(var(--secondary) / 0.8);
--button-secondary-hover-text: var(--secondary-foreground);
```

### 轮廓按钮 (Outline)

用于低强调度选项，通常用于取消或返回。

```css
--button-outline-bg: transparent;
--button-outline-text: var(--primary);
--button-outline-border: var(--primary);
--button-outline-hover-bg: hsl(var(--primary) / 0.1);
--button-outline-hover-text: var(--primary);
```

### 文本按钮 (Text)

用于最低强调度，辅助导航。

```css
--button-text-text: var(--primary);
--button-text-hover-text: hsl(var(--primary) / 0.8);
```

### 危险按钮 (Destructive)

用于警告性操作。

```css
--button-destructive-bg: var(--destructive);
--button-destructive-text: var(--destructive-foreground);
--button-destructive-hover-bg: hsl(var(--destructive) / 0.9);
```

## 按钮尺寸

### 大型按钮 (Large)

用于主要CTA，页面顶部或底部。

```css
--button-large-height: 3.5rem;      /* 56px */
--button-large-padding-x: 2rem;     /* 32px */
--button-large-font-size: var(--body-large-size);
--button-large-border-radius: var(--radius);
```

### 中型按钮 (Medium)

用于页面主体中的CTA。

```css
--button-medium-height: 2.75rem;    /* 44px */
--button-medium-padding-x: 1.5rem;  /* 24px */
--button-medium-font-size: var(--body-base-size);
--button-medium-border-radius: var(--radius);
```

### 小型按钮 (Small)

用于辅助功能，内嵌在内容中。

```css
--button-small-height: 2.25rem;     /* 36px */
--button-small-padding-x: 1rem;     /* 16px */
--button-small-font-size: var(--body-small-size);
--button-small-border-radius: calc(var(--radius) - 2px);
```

## 按钮内容适应性

根据按钮文本长度和内容类型调整基础内边距。

```css
/* 短文本按钮 (1-2个字) */
--button-short-text-padding-x: 1.25rem;  /* 20px */

/* 中等文本按钮 (3-5个字) */
--button-medium-text-padding-x: 1.75rem; /* 28px */

/* 长文本按钮 (6+个字) */
--button-long-text-padding-x: 2rem;      /* 32px */
```

短文本按钮需要更多内边距以避免按钮看起来过小，长文本按钮可以适当减少内边距以控制整体宽度。

## 内容密度一致性

保持不同尺寸按钮的内容密度一致，创造和谐的视觉体验。

```css
/* 内容密度比例 */
--button-padding-to-height-ratio: 0.57; /* padding-x ≈ height * 0.57 */
```

这一比例可用于计算不同尺寸按钮的内边距，确保视觉一致性：
- 大型按钮：3.5rem × 0.57 ≈ 2rem
- 中型按钮：2.75rem × 0.57 ≈ 1.5rem
- 小型按钮：2.25rem × 0.57 ≈ 1.25rem

## 主次按钮内边距差异化

主按钮的内边距略大于次按钮，创造视觉重量差异。

```css
/* 主按钮 */
--button-primary-padding-x-small: 1.125rem;  /* 18px */
--button-primary-padding-x-medium: 1.75rem;  /* 28px */
--button-primary-padding-x-large: 2.25rem;   /* 36px */

/* 次按钮 */
--button-secondary-padding-x-small: 1rem;    /* 16px */
--button-secondary-padding-x-medium: 1.5rem; /* 24px */
--button-secondary-padding-x-large: 2rem;    /* 32px */
```

## 按钮状态

### 默认状态

基础外观，定义按钮的初始样式。

### 悬停状态 (Hover)

微妙变化，提示可交互。

```css
--button-hover-transform: translateY(-1px);
--button-hover-shadow: var(--shadow-md);
--button-hover-transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
```

### 活动状态 (Active)

按下效果，提供触觉反馈。

```css
--button-active-transform: translateY(1px);
--button-active-shadow: var(--shadow-sm);
```

### 聚焦状态 (Focus)

键盘导航时的焦点指示。

```css
--button-focus-ring-color: var(--ring);
--button-focus-ring-width: 3px;
--button-focus-ring-offset: 2px;
```

### 禁用状态 (Disabled)

降低对比度，表示不可用。

```css
--button-disabled-opacity: 0.65;
--button-disabled-cursor: not-allowed;
```

## 特殊变体

### 图标按钮

包含图标，可有可无文本。

```css
--button-icon-spacing: 0.5rem;       /* 图标与文本间距 */
--button-icon-only-padding: 0.75rem; /* 仅图标按钮的内边距 */
```

### 渐变按钮

使用渐变背景，增强视觉吸引力。与页面中的渐变高亮文本协调一致。

```css
/* 渐变基础组件 - 原子化设计 */
--gradient-direction: to right;
--gradient-primary-start: var(--primary);
--gradient-primary-end: var(--accent);
--gradient-secondary-start: var(--primary);
--gradient-secondary-end: var(--secondary);
--gradient-emphasis-start: var(--primary);
--gradient-emphasis-end: var(--destructive);

/* 预定义渐变 - 组件化设计 */
--gradient-primary: linear-gradient(var(--gradient-direction), var(--gradient-primary-start), var(--gradient-primary-end));
--gradient-secondary: linear-gradient(var(--gradient-direction), var(--gradient-secondary-start), var(--gradient-secondary-end));
--gradient-emphasis: linear-gradient(var(--gradient-direction), var(--gradient-emphasis-start), var(--gradient-emphasis-end));

/* 按钮特定渐变 */
--button-gradient: var(--gradient-primary);
--button-gradient-hover: var(--gradient-secondary);
```

#### 渐变文本与按钮协调

确保页面中的渐变高亮文本与渐变按钮保持视觉一致性，强化品牌识别度。

```css
/* 文本渐变使用相同的令牌 */
.text-gradient-primary {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* 按钮使用相同的渐变 */
.btn-gradient {
  background: var(--gradient-primary);
  border: none;
}
```

这种折中方案结合了原子化设计的灵活性和组件化设计的便利性：

1. **灵活性**：可以单独修改渐变的方向、起点或终点颜色
2. **一致性**：通过预定义渐变确保按钮和文本使用相同的视觉效果
3. **可维护性**：只需修改基础变量，即可全局更新所有渐变效果

这种协调一致的方法可以在以下场景中使用：

1. 页面标题中的高亮文本与主要CTA按钮使用相同渐变
2. 特定区块中的子标题和按钮使用主题相关的渐变
3. 响应式设计中保持渐变方向和颜色一致性

### 动画按钮

包含微动画，吸引注意力。

```css
--button-animation-pulse-scale: 1.05;
--button-animation-pulse-duration: 2s;
```

## 按钮主题设置

为了满足不同行业、场景和目标受众的需求，按钮系统需要支持多种主题。这些主题可以增强落地页的转化率和品牌一致性。

### 主题令牌结构

按钮主题采用嵌套结构，确保灵活性和可维护性：

```css
/* 主题基础变量 */
:root {
  /* 默认主题变量（与全局变量相同） */
  --button-theme-default-primary: var(--primary);
  --button-theme-default-secondary: var(--secondary);
  --button-theme-default-radius: var(--radius);
  --button-theme-default-animation-speed: 200ms;
  
  /* 科技主题 - 锐利、现代、高对比度 */
  --button-theme-tech-primary: hsl(230, 80%, 50%);
  --button-theme-tech-secondary: hsl(200, 75%, 55%);
  --button-theme-tech-radius: 0.125rem;
  --button-theme-tech-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  --button-theme-tech-animation-speed: 150ms;
  
  /* 创意主题 - 活泼、大胆、非传统 */
  --button-theme-creative-primary: hsl(320, 80%, 55%);
  --button-theme-creative-secondary: hsl(280, 75%, 60%);
  --button-theme-creative-radius: 1.5rem;
  --button-theme-creative-animation: cubic-bezier(0.34, 1.56, 0.64, 1);
  
  /* 金融主题 - 稳重、专业、信任 */
  --button-theme-finance-primary: hsl(210, 70%, 35%);
  --button-theme-finance-secondary: hsl(180, 50%, 40%);
  --button-theme-finance-radius: 0.25rem;
  --button-theme-finance-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  
  /* 教育主题 - 友好、圆润、柔和 */
  --button-theme-education-primary: hsl(190, 70%, 45%);
  --button-theme-education-secondary: hsl(150, 60%, 50%);
  --button-theme-education-radius: 0.75rem;
}
```

### 渐变按钮主题

不同主题的渐变按钮可以创造独特的视觉效果：

```css
/* 主题渐变设置 */
:root {
  /* 默认渐变 */
  --gradient-theme-default-direction: var(--gradient-direction);
  --gradient-theme-default-primary-start: var(--gradient-primary-start);
  --gradient-theme-default-primary-end: var(--gradient-primary-end);
  
  /* 科技主题渐变 - 蓝色调 */
  --gradient-theme-tech-direction: to right;
  --gradient-theme-tech-primary-start: hsl(230, 80%, 50%);
  --gradient-theme-tech-primary-end: hsl(200, 75%, 55%);
  --gradient-theme-tech-secondary-start: hsl(210, 70%, 45%);
  --gradient-theme-tech-secondary-end: hsl(240, 60%, 60%);
  
  /* 创意主题渐变 - 紫红色调 */
  --gradient-theme-creative-direction: to right bottom;
  --gradient-theme-creative-primary-start: hsl(320, 80%, 55%);
  --gradient-theme-creative-primary-end: hsl(280, 75%, 60%);
  --gradient-theme-creative-secondary-start: hsl(340, 80%, 55%);
  --gradient-theme-creative-secondary-end: hsl(260, 70%, 65%);
  
  /* 金融主题渐变 - 深蓝色调 */
  --gradient-theme-finance-direction: to right;
  --gradient-theme-finance-primary-start: hsl(210, 70%, 35%);
  --gradient-theme-finance-primary-end: hsl(180, 50%, 40%);
  
  /* 教育主题渐变 - 青绿色调 */
  --gradient-theme-education-direction: to right;
  --gradient-theme-education-primary-start: hsl(190, 70%, 45%);
  --gradient-theme-education-primary-end: hsl(150, 60%, 50%);
}

/* 生成主题渐变 */
.theme-tech {
  --gradient-primary: linear-gradient(
    var(--gradient-theme-tech-direction),
    var(--gradient-theme-tech-primary-start),
    var(--gradient-theme-tech-primary-end)
  );
  --gradient-secondary: linear-gradient(
    var(--gradient-theme-tech-direction),
    var(--gradient-theme-tech-secondary-start),
    var(--gradient-theme-tech-secondary-end)
  );
}

.theme-creative {
  --gradient-primary: linear-gradient(
    var(--gradient-theme-creative-direction),
    var(--gradient-theme-creative-primary-start),
    var(--gradient-theme-creative-primary-end)
  );
  --gradient-secondary: linear-gradient(
    var(--gradient-theme-creative-direction),
    var(--gradient-theme-creative-secondary-start),
    var(--gradient-theme-creative-secondary-end)
  );
}
```

### 季节与活动主题

针对特定时间或活动的临时主题：

```css
/* 季节主题 */
:root {
  /* 新年主题 */
  --button-theme-newyear-primary: hsl(0, 80%, 50%);
  --button-theme-newyear-secondary: hsl(30, 90%, 50%);
  --gradient-theme-newyear-primary-start: hsl(0, 80%, 50%);
  --gradient-theme-newyear-primary-end: hsl(30, 90%, 50%);
  
  /* 夏季主题 */
  --button-theme-summer-primary: hsl(200, 100%, 50%);
  --button-theme-summer-secondary: hsl(150, 80%, 50%);
  --gradient-theme-summer-primary-start: hsl(200, 100%, 50%);
  --gradient-theme-summer-primary-end: hsl(150, 80%, 50%);
  
  /* 促销主题 */
  --button-theme-sale-primary: hsl(350, 100%, 60%);
  --button-theme-sale-secondary: hsl(300, 100%, 45%);
  --gradient-theme-sale-primary-start: hsl(350, 100%, 60%);
  --gradient-theme-sale-primary-end: hsl(300, 100%, 45%);
  --button-theme-sale-animation-pulse-scale: 1.08;
  --button-theme-sale-animation-pulse-duration: 1.5s;
}
```

### 情感主题按钮

根据需要传达的情感和紧迫感：

```css
/* 情感主题 */
:root {
  /* 信任主题 - 稳定、保守的色调和过渡效果 */
  --button-theme-trust-primary: hsl(210, 60%, 40%);
  --button-theme-trust-secondary: hsl(220, 40%, 50%);
  --button-theme-trust-animation-speed: 300ms;
  
  /* 兴奋主题 - 鲜艳色彩、跳动动画、强烈对比 */
  --button-theme-excitement-primary: hsl(350, 80%, 55%);
  --button-theme-excitement-secondary: hsl(30, 90%, 50%);
  --button-theme-excitement-animation-pulse-scale: 1.1;
  --button-theme-excitement-animation-pulse-duration: 1.2s;
  
  /* 独家主题 - 高级感、金属质感、精致细节 */
  --button-theme-exclusive-primary: hsl(45, 80%, 50%);
  --button-theme-exclusive-secondary: hsl(30, 50%, 40%);
  --button-theme-exclusive-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
  --gradient-theme-exclusive-primary-start: hsl(45, 80%, 50%);
  --gradient-theme-exclusive-primary-end: hsl(30, 50%, 40%);
}
```

## 明暗模式支持

按钮设计令牌需要在明暗模式之间无缝切换，确保在不同环境下保持最佳可读性和美观性。

### 明暗模式颜色定义

根据 `globals.css` 中的定义，明暗模式下的颜色变量有显著差异：

```css
/* 明模式颜色变量 (:root) */
--primary: 221.2 83.2% 53.3%;
--primary-foreground: 210 40% 98%;
--secondary: 210 40% 96.1%;
--secondary-foreground: 222.2 47.4% 11.2%;
--destructive: 0 84.2% 60.2%;
--ring: 221.2 83.2% 53.3%;

/* 暗模式颜色变量 (.dark) */
--primary: 217.2 91.2% 59.8%;
--primary-foreground: 222.2 47.4% 11.2%;
--secondary: 217.2 32.6% 17.5%;
--secondary-foreground: 210 40% 98%;
--destructive: 0 62.8% 30.6%;
--ring: 224.3 76.3% 48%;
```

### 按钮明暗模式适配

按钮组件应自动适应明暗模式，无需额外代码：

```css
/* 按钮使用基础颜色变量，自动继承明暗模式设置 */
--button-primary-bg: var(--primary);
--button-primary-text: var(--primary-foreground);
```

### 渐变按钮明暗模式调整

渐变按钮在明暗模式下需要特别考虑对比度和可读性：

```css
/* 渐变按钮明暗模式适配 */
.dark {
  --gradient-primary-start: var(--primary);
  --gradient-primary-end: hsl(var(--accent) / 0.8);
  --gradient-secondary-start: var(--primary);
  --gradient-secondary-end: var(--ring);
  --gradient-emphasis-start: var(--primary);
  --gradient-emphasis-end: var(--destructive);
}
```

### 明暗模式下的状态变化

在暗模式下，按钮的悬停、聚焦和活动状态需要增强视觉反馈：

```css
/* 暗模式下的状态增强 */
.dark {
  --button-hover-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
  --button-focus-ring-color: hsl(var(--ring) / 0.8);
  --button-focus-ring-offset: 3px; /* 增大偏移以增强可见性 */
}
```

## 响应式设计

### 移动端优化

移动端上适当减少内边距，但保持按钮可触摸性。

```css
/* 移动端调整 */
@media (max-width: 640px) {
  :root {
    --button-primary-padding-x-medium: 1.5rem;   /* 24px */
    --button-secondary-padding-x-medium: 1.25rem; /* 20px */
    --button-mobile-scale-factor: 0.9;           /* 移动端尺寸缩放因子 */
  }
}
```

### 宽屏模式优化

宽屏模式下适当增加内边距，提高视觉平衡性，与现有的响应式宽度控制系统保持一致。

```css
/* 宽屏模式 */
html.page-width-wide {
  --button-primary-padding-x-medium: 2rem;      /* 32px */
  --button-secondary-padding-x-medium: 1.75rem; /* 28px */
  --button-large-height: 3.75rem;               /* 60px */
  --button-medium-height: 3rem;                 /* 48px */
  --button-small-height: 2.5rem;                /* 40px */
}

/* 满屏模式 */
html.page-width-full {
  --button-primary-padding-x-medium: 2.25rem;   /* 36px */
  --button-secondary-padding-x-medium: 2rem;    /* 32px */
  --button-large-height: 4rem;                  /* 64px */
  --button-medium-height: 3.25rem;              /* 52px */
  --button-small-height: 2.75rem;               /* 44px */
}
```

这些调整与现有的宽屏和满屏模式下的字体大小和间距变化保持一致，确保按钮在不同宽度模式下的视觉平衡。

## 转化率优化

### 对比度增强

确保按钮在页面中脱颖而出。

```css
--button-cta-contrast-ratio: 4.5;          /* 最小对比度 */
--button-cta-isolation-space: 2rem;        /* 按钮周围的空白空间 */
--button-cta-highlight-glow: 0 0 15px rgba(var(--primary-rgb), 0.4);
```

### 紧迫感指示器

创造行动紧迫感。

```css
--button-urgency-border-pulse: 2px solid var(--primary);
--button-urgency-animation-duration: 1.5s;
```

## 可访问性

### 触摸目标尺寸

确保按钮足够大，易于点击。

```css
--button-min-touch-target: 44px;     /* 符合WCAG标准 */
--button-touch-target-spacing: 8px;  /* 按钮间最小间距 */
```

### 焦点指示器

确保键盘用户可以清晰看到当前焦点。

```css
--button-focus-visible-ring-color: var(--ring);
--button-focus-visible-ring-width: 3px;
--button-focus-visible-ring-offset: 2px;
```

### 色彩对比度

确保文本与背景有足够对比度。

```css
--button-text-contrast-ratio-min: 4.5;      /* AA标准 */
--button-text-contrast-ratio-preferred: 7;  /* AAA标准 */
```

## 实施指南

### 按钮布局最佳实践

1. **主次按钮并排放置时**：主按钮应放在右侧（LTR布局）或左侧（RTL布局）
2. **垂直堆叠时**：主按钮应放在顶部
3. **按钮间距**：并排按钮间距应为 `1rem`（16px），垂直堆叠时为 `0.75rem`（12px）
4. **按钮密度**：每个区块通常不超过2个按钮，最多不超过3个

### 按钮文案最佳实践

1. **行动导向**：使用"开始免费试用"而非"免费试用"
2. **简洁明了**：通常3-5个字，最多不超过8个字
3. **一致性**：相似功能的按钮使用相似文案
4. **紧迫感**：适当添加时间限制或数量限制，如"限时优惠"

## Tailwind CSS 集成

### 扩展 Tailwind 主题

```js
// tailwind.config.js
module.exports = {
  theme: {
    extend: {
      colors: {
        'btn-primary': 'var(--primary)',
        'btn-primary-foreground': 'var(--primary-foreground)',
        'btn-primary-hover': 'hsl(var(--primary) / 0.9)',
        // 主题颜色
        'theme-tech-primary': 'var(--button-theme-tech-primary)',
        'theme-creative-primary': 'var(--button-theme-creative-primary)',
        'theme-finance-primary': 'var(--button-theme-finance-primary)',
        'theme-education-primary': 'var(--button-theme-education-primary)',
        'theme-sale-primary': 'var(--button-theme-sale-primary)',
      },
      spacing: {
        'btn-large-padding-x': 'var(--button-large-padding-x)',
        'btn-medium-padding-x': 'var(--button-medium-padding-x)',
        'btn-small-padding-x': 'var(--button-small-padding-x)',
      },
      height: {
        'btn-large': 'var(--button-large-height)',
        'btn-medium': 'var(--button-medium-height)',
        'btn-small': 'var(--button-small-height)',
      },
      transitionDuration: {
        'btn': '200ms',
        'btn-theme-tech': 'var(--button-theme-tech-animation-speed)',
        'btn-theme-trust': 'var(--button-theme-trust-animation-speed)',
      },
      boxShadow: {
        'btn': 'var(--button-shadow)',
        'btn-hover': 'var(--button-hover-shadow)',
        'btn-theme-tech': 'var(--button-theme-tech-shadow)',
        'btn-theme-finance': 'var(--button-theme-finance-shadow)',
        'btn-theme-exclusive': 'var(--button-theme-exclusive-shadow)',
      },
      borderRadius: {
        'theme-tech': 'var(--button-theme-tech-radius)',
        'theme-creative': 'var(--button-theme-creative-radius)',
        'theme-finance': 'var(--button-theme-finance-radius)',
        'theme-education': 'var(--button-theme-education-radius)',
      },
      // 渐变相关配置
      backgroundImage: {
        'gradient-primary': 'var(--gradient-primary)',
        'gradient-secondary': 'var(--gradient-secondary)',
        'gradient-emphasis': 'var(--gradient-emphasis)',
        // 主题渐变
        'gradient-theme-tech': 'linear-gradient(var(--gradient-theme-tech-direction), var(--gradient-theme-tech-primary-start), var(--gradient-theme-tech-primary-end))',
        'gradient-theme-creative': 'linear-gradient(var(--gradient-theme-creative-direction), var(--gradient-theme-creative-primary-start), var(--gradient-theme-creative-primary-end))',
        'gradient-theme-finance': 'linear-gradient(var(--gradient-theme-finance-direction), var(--gradient-theme-finance-primary-start), var(--gradient-theme-finance-primary-end))',
        'gradient-theme-education': 'linear-gradient(var(--gradient-theme-education-direction), var(--gradient-theme-education-primary-start), var(--gradient-theme-education-primary-end))',
        'gradient-theme-sale': 'linear-gradient(to right, var(--gradient-theme-sale-primary-start), var(--gradient-theme-sale-primary-end))',
        'gradient-theme-exclusive': 'linear-gradient(to right, var(--gradient-theme-exclusive-primary-start), var(--gradient-theme-exclusive-primary-end))',
      },
      animation: {
        'pulse-sale': 'pulse var(--button-theme-sale-animation-pulse-duration) cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'pulse-excitement': 'pulse var(--button-theme-excitement-animation-pulse-duration) cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
      keyframes: {
        'pulse-theme': {
          '0%, 100%': { transform: 'scale(1)' },
          '50%': { transform: 'scale(var(--button-theme-excitement-animation-pulse-scale))' },
        },
      },
    },
  },
  // 启用暗模式类
  darkMode: 'class',
}
```

### 组件实现示例

```jsx
// Button.tsx
const Button = ({
  variant = 'primary',
  size = 'medium',
  theme = 'default', // 新增主题参数
  children,
  className,
  ...props
}) => {
  // 基础类 - 所有按钮共享
  const baseClasses = 'inline-flex items-center justify-center font-medium transition-all duration-300';
  
  // 变体类 - 使用现有的颜色令牌，自动适应明暗模式
  const variantClasses = {
    primary: 'bg-primary text-primary-foreground hover:bg-primary/90 dark:hover:bg-primary/80',
    secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80 dark:hover:bg-secondary/70',
    outline: 'bg-transparent border-2 border-primary text-primary hover:bg-primary/10 dark:hover:bg-primary/20',
    text: 'bg-transparent text-primary hover:text-primary/80 dark:hover:text-primary/70',
    destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90 dark:hover:bg-destructive/80',
    gradient: 'bg-gradient-to-r from-[var(--gradient-primary-start)] to-[var(--gradient-primary-end)] text-primary-foreground hover:from-[var(--gradient-secondary-start)] hover:to-[var(--gradient-secondary-end)]'
  };
  
  // 尺寸类 - 结合现有的字体大小和行高令牌
  const sizeClasses = {
    large: 'h-[3.5rem] px-8 text-body-large/body-large rounded-[var(--radius)]',
    medium: 'h-[2.75rem] px-6 text-body-base/body-base rounded-[var(--radius)]',
    small: 'h-[2.25rem] px-4 text-body-small/body-small rounded-[calc(var(--radius)-2px)]'
  };
  
  // 状态类 - 使用现有的环形令牌，并为暗模式增强视觉反馈
  const stateClasses = 'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 dark:focus-visible:ring-offset-3 disabled:opacity-65 disabled:cursor-not-allowed dark:shadow-none dark:hover:shadow-btn-hover';
  
  // 主题类 - 处理不同主题的样式
  const themeClasses = {
    default: '',
    tech: 'rounded-theme-tech shadow-btn-theme-tech duration-btn-theme-tech',
    creative: 'rounded-theme-creative',
    finance: 'rounded-theme-finance shadow-btn-theme-finance',
    education: 'rounded-theme-education',
    sale: 'animate-pulse-sale',
    excitement: 'animate-pulse-excitement',
    exclusive: 'shadow-btn-theme-exclusive',
  };
  
  // 主题渐变类 - 用于渐变按钮
  const themeGradientClasses = {
    default: '',
    tech: 'bg-gradient-theme-tech',
    creative: 'bg-gradient-theme-creative',
    finance: 'bg-gradient-theme-finance',
    education: 'bg-gradient-theme-education',
    sale: 'bg-gradient-theme-sale',
    exclusive: 'bg-gradient-theme-exclusive',
  };
  
  // 组合变体和主题类
  const getVariantWithTheme = () => {
    // 如果是渐变按钮且有特定主题，使用主题渐变
    if (variant === 'gradient' && theme !== 'default') {
      return themeGradientClasses[theme] + ' text-primary-foreground';
    }
    
    // 否则返回标准变体类
    return variantClasses[variant];
  };
  
  return (
    <button
      className={`${baseClasses} ${getVariantWithTheme()} ${sizeClasses[size]} ${themeClasses[theme]} ${stateClasses} ${className}`}
      {...props}
    >
      {children}
    </button>
  );
};

export default Button;
```

这个实现示例增加了对不同主题的支持，包括：

1. 新增 `theme` 参数，默认值为 'default'
2. 添加 `themeClasses` 对象，定义不同主题的基础样式
3. 添加 `themeGradientClasses` 对象，定义不同主题的渐变样式
4. 增加 `getVariantWithTheme()` 方法，根据变体和主题组合生成最终的样式类

这种实现方式允许按钮组件在不同行业、场景和目标受众下保持一致的视觉语言，同时提供足够的定制灵活性。

### 使用示例

```jsx
// Hero 区块中的主要 CTA 按钮
<Button 
  variant="primary" 
  size="large" 
  className="btn-cta-animation"
>
  开始免费试用 <ArrowRight className="ml-2" />
</Button>

// 次要按钮
<Button 
  variant="outline" 
  size="large" 
  className="ml-4 sm:ml-6"
>
  了解更多
</Button>

// 渐变按钮，与页面标题中的渐变文本协调
<Button 
  variant="gradient" 
  size="large" 
  className="mt-6"
>
  立即注册
</Button>

// 不同主题的按钮示例
<div className="flex flex-wrap gap-4 my-8">
  <Button variant="primary" theme="tech" size="medium">科技主题</Button>
  <Button variant="primary" theme="creative" size="medium">创意主题</Button>
  <Button variant="primary" theme="finance" size="medium">金融主题</Button>
  <Button variant="primary" theme="education" size="medium">教育主题</Button>
</div>

// 不同主题的渐变按钮
<div className="flex flex-wrap gap-4 my-8">
  <Button variant="gradient" theme="tech" size="medium">科技渐变</Button>
  <Button variant="gradient" theme="creative" size="medium">创意渐变</Button>
  <Button variant="gradient" theme="finance" size="medium">金融渐变</Button>
  <Button variant="gradient" theme="education" size="medium">教育渐变</Button>
</div>

// 情感主题按钮
<div className="flex flex-wrap gap-4 my-8">
  <Button variant="primary" theme="trust" size="medium">信任主题</Button>
  <Button variant="primary" theme="excitement" size="medium">兴奋主题</Button>
  <Button variant="gradient" theme="exclusive" size="medium">独家主题</Button>
  <Button variant="gradient" theme="sale" size="medium" className="animate-pulse-sale">促销主题</Button>
</div>

// 移动端全宽按钮
<Button 
  variant="primary" 
  size="large" 
  className="w-full sm:w-auto"
>
  免费开始使用
</Button>

// 渐变文本与渐变按钮协调示例
<div>
  <h2 className="text-3xl font-bold mb-4">
    探索我们的<span className="bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">创新解决方案</span>
  </h2>
  <Button variant="gradient" size="medium">了解更多</Button>
</div>

// 季节主题按钮示例
<div className="flex flex-wrap gap-4 my-8">
  <Button variant="gradient" theme="newyear" size="medium">新年主题</Button>
  <Button variant="gradient" theme="summer" size="medium">夏季主题</Button>
</div>

// 明暗模式切换示例
<div className="flex items-center space-x-4">
  <Button variant="primary" size="medium">默认按钮</Button>
  <div className="dark">
    <Button variant="primary" size="medium">暗模式按钮</Button>
  </div>
</div>
```

---

本规范文档旨在指导 LitPage 落地页系统中按钮的设计和实现，确保视觉一致性和最佳用户体验。设计令牌系统的目标是平衡美观性、功能性和一致性，同时优化转化率。

本规范与现有的设计令牌系统（如 `--primary`、`--radius`、`--body-large-size` 等）保持一致，确保按钮组件能够无缝集成到整体设计语言中。通过丰富的主题设置和变体选项，按钮组件可以适应不同行业、场景和目标受众，并在明暗模式、不同屏幕尺寸和用户交互状态下保持一致的体验。
