# 响应式页面宽度控制系统设计文档

## 概述

本文档详细介绍了 LitPage 项目中实现的响应式页面宽度控制系统。该系统通过 CSS 变量和 Tailwind CSS 自定义类，实现了灵活且一致的页面布局控制，包括容器宽度、间距、字体大小和行高的自适应调整，无需添加额外的容器元素。

## 设计目标

1. **减少 DOM 复杂性**：避免添加额外的容器元素，保持 DOM 结构简洁
2. **灵活的宽度控制**：支持三种宽度模式（普通、宽屏、满屏）
3. **一致的布局体验**：确保所有组件在不同宽度模式下保持一致的布局和间距
4. **响应式设计**：适应不同屏幕尺寸，提供最佳的用户体验
5. **易于维护**：通过 CSS 变量集中管理宽度、间距、字体大小和行高，便于后续调整
6. **排版一致性**：确保文本在不同宽度模式下保持适当的字体大小和行高比例，提高可读性

## 实现方案

### 1. CSS 变量定义

在全局样式文件中定义了四组 CSS 变量，分别控制水平宽度、垂直间距、字体大小和行高：

```css
/* 页面宽度相关变量 - 默认为普通宽度 */
--container-max-width: 80rem; /* 1280px */
--container-content-width: 65rem; /* 1040px */
--container-padding-x: 1.5rem; /* 24px */

/* 垂直间距相关变量 - 默认为普通间距 */
--section-spacing-y: 4rem; /* 64px, 区块之间的垂直间距 */
--content-spacing-y: 2rem; /* 32px, 内容元素之间的垂直间距 */
--element-spacing-y: 1rem; /* 16px, 元素内部的垂直间距 */

/* 字体大小相关变量 - 默认为普通字体大小 */
--heading-1-size: 3rem;      /* 48px */
--heading-2-size: 2.25rem;   /* 36px */
--heading-3-size: 1.875rem;  /* 30px */
--heading-4-size: 1.5rem;    /* 24px */
--body-large-size: 1.125rem; /* 18px */
--body-base-size: 1rem;      /* 16px */
--body-small-size: 0.875rem; /* 14px */

/* 行高相关变量 - 默认为普通行高 */
--heading-1-line-height: 1.1;  /* 标题通常使用较紧凑的行高 */
--heading-2-line-height: 1.2;
--heading-3-line-height: 1.25;
--heading-4-line-height: 1.3;
--body-large-line-height: 1.5;
--body-base-line-height: 1.5;  /* 正文使用较宽松的行高提高可读性 */
--body-small-line-height: 1.4;
```

### 2. 宽度模式定义

通过在 `<html>` 元素上添加不同的类名，切换三种宽度模式：

#### 普通宽度模式（默认）
```css
/* 默认值，无需额外类名 */
--container-max-width: 80rem; /* 1280px */
--container-content-width: 65rem; /* 1040px */
--container-padding-x: 1.5rem; /* 24px */
```

#### 宽屏模式
```css
/* 添加 html.page-width-wide 类 */
--container-max-width: 96rem; /* 1536px */
--container-content-width: 76rem; /* 1216px */
--container-padding-x: 2rem; /* 32px */

/* 宽屏模式下的垂直间距 */
--section-spacing-y: 5rem; /* 80px */
--content-spacing-y: 2.5rem; /* 40px */
--element-spacing-y: 1.25rem; /* 20px */

/* 宽屏模式下的字体大小 - 优化后 */
--heading-1-size: 3.75rem;   /* 60px */
--heading-2-size: 2.75rem;   /* 44px */
--heading-3-size: 2.25rem;   /* 36px */
--heading-4-size: 1.75rem;   /* 28px */
--body-large-size: 1.375rem; /* 22px */
--body-base-size: 1.1875rem; /* 19px */
--body-small-size: 1.0625rem;/* 17px */

/* 宽屏模式下的行高 */
--heading-1-line-height: 1.15;
--heading-2-line-height: 1.225;
--heading-3-line-height: 1.275;
--heading-4-line-height: 1.325;
--body-large-line-height: 1.55;
--body-base-line-height: 1.55;
--body-small-line-height: 1.45;
```

#### 满屏模式
```css
/* 添加 html.page-width-full 类 */
--container-max-width: 100%;
--container-content-width: 85%;
--container-padding-x: 3rem; /* 48px */

/* 满屏模式下的垂直间距 */
--section-spacing-y: 6rem; /* 96px */
--content-spacing-y: 3rem; /* 48px */
--element-spacing-y: 1.5rem; /* 24px */

/* 满屏模式下的字体大小 */
--heading-1-size: 4rem;      /* 64px */
--heading-2-size: 2.75rem;   /* 44px */
--heading-3-size: 2.125rem;  /* 34px */
--heading-4-size: 1.75rem;   /* 28px */
--body-large-size: 1.375rem; /* 22px */
--body-base-size: 1.125rem;  /* 18px */
--body-small-size: 1rem;     /* 16px */
```

### 3. Tailwind CSS 配置扩展

在 `tailwind.config.ts` 中扩展了配置，添加了自定义的最大宽度、间距和字体大小类：

```typescript
extend: {
  maxWidth: {
    'container': 'var(--container-max-width, 80rem)',
    'content': 'var(--container-content-width, 65rem)',
  },
  padding: {
    'container-x': 'var(--container-padding-x, 1.5rem)',
  },
  spacing: {
    'section-y': 'var(--section-spacing-y, 4rem)',
    'content-y': 'var(--content-spacing-y, 2rem)',
    'element-y': 'var(--element-spacing-y, 1rem)',
  },
  fontSize: {
    'heading-1': 'var(--heading-1-size, 3rem)',
    'heading-2': 'var(--heading-2-size, 2.25rem)',
    'heading-3': 'var(--heading-3-size, 1.875rem)',
    'heading-4': 'var(--heading-4-size, 1.5rem)',
    'body-large': 'var(--body-large-size, 1.125rem)',
    'body-base': 'var(--body-base-size, 1rem)',
    'body-small': 'var(--body-small-size, 0.875rem)',
  },
  lineHeight: {
    'heading-1': 'var(--heading-1-line-height, 1.1)',
    'heading-2': 'var(--heading-2-line-height, 1.2)',
    'heading-3': 'var(--heading-3-line-height, 1.25)',
    'heading-4': 'var(--heading-4-line-height, 1.3)',
    'body-large': 'var(--body-large-line-height, 1.5)',
    'body-base': 'var(--body-base-line-height, 1.5)',
    'body-small': 'var(--body-small-line-height, 1.4)',
  },
}
```

### 4. 应用示例

以 `HeroImage/Simple.tsx` 组件为例，展示如何使用自定义类实现响应式宽度控制：

```tsx
<section className="relative isolate overflow-hidden bg-background text-foreground" aria-labelledby="simple-hero-image-title">
  {/* 背景 SVG */}
  <div className="mx-auto max-w-container px-container-x pb-section-y pt-content-y sm:pb-section-y lg:flex lg:gap-x-8 xl:gap-x-12 2xl:gap-x-16 lg:px-container-x lg:py-section-y">
    {/* 内容区域 */}
    <div className="mx-auto max-w-content lg:mx-0 lg:w-[45%] xl:w-[48%] 2xl:w-[45%] lg:flex-shrink-0 lg:pt-8">
      {/* 公告区域 */}
      <div className="mt-content-y sm:mt-section-y lg:mt-content-y">
        {/* 公告内容 */}
      </div>
      
      {/* 标题 */}
      <h1 className="mt-element-y text-pretty text-5xl font-semibold tracking-tight text-foreground sm:text-7xl">
        {/* 标题内容 */}
      </h1>
      
      {/* 描述 */}
      <p className="mt-element-y text-pretty text-lg font-medium text-muted-foreground sm:text-xl/8">
        {/* 描述内容 */}
      </p>
      
      {/* 按钮组 */}
      <div className="mt-content-y flex items-center gap-x-6">
        {/* 按钮内容 */}
      </div>
    </div>
    
    {/* 图片区域 */}
    <div className="mx-auto mt-content-y w-full sm:mt-section-y lg:mt-0 lg:flex-1 lg:max-w-[50%] xl:max-w-[48%] 2xl:max-w-[50%] lg:flex lg:items-center">
      {/* 图片内容 */}
    </div>
  </div>
</section>
```

## 关键类名说明

### 水平宽度控制

- **`max-w-container`**: 设置最大宽度为 `--container-max-width`
- **`max-w-content`**: 设置最大宽度为 `--container-content-width`
- **`px-container-x`**: 设置水平内边距为 `--container-padding-x`

### 垂直间距控制

- **`pb-section-y` / `pt-section-y` / `py-section-y`**: 区块级别的垂直间距
- **`mt-content-y` / `mb-content-y` / `my-content-y`**: 内容级别的垂直间距
- **`mt-element-y` / `mb-element-y` / `my-element-y`**: 元素级别的垂直间距

### 字体大小控制

- **`text-heading-1`**: 一级标题字体大小，对应 `--heading-1-size`
- **`text-heading-2`**: 二级标题字体大小，对应 `--heading-2-size`
- **`text-heading-3`**: 三级标题字体大小，对应 `--heading-3-size`
- **`text-heading-4`**: 四级标题字体大小，对应 `--heading-4-size`
- **`text-body-large`**: 大号正文字体大小，对应 `--body-large-size`
- **`text-body-base`**: 基础正文字体大小，对应 `--body-base-size`
- **`text-body-small`**: 小号正文字体大小，对应 `--body-small-size`

### 行高控制

- **`leading-heading-1`**: 一级标题行高，对应 `--heading-1-line-height`
- **`leading-heading-2`**: 二级标题行高，对应 `--heading-2-line-height`
- **`leading-heading-3`**: 三级标题行高，对应 `--heading-3-line-height`
- **`leading-heading-4`**: 四级标题行高，对应 `--heading-4-line-height`
- **`leading-body-large`**: 大号正文行高，对应 `--body-large-line-height`
- **`leading-body-base`**: 基础正文行高，对应 `--body-base-line-height`
- **`leading-body-small`**: 小号正文行高，对应 `--body-small-line-height`

### Tailwind 斜线语法（Slash Syntax）

Tailwind CSS 提供了一种简洁的方式来同时设置字体大小和行高，即“斜线语法”。在我们的响应式排版系统中，可以使用这种语法来组合语义化的字体大小和行高类：

- **`text-heading-1/heading-1`**: 设置一级标题字体大小和对应的行高
- **`text-heading-2/heading-2`**: 设置二级标题字体大小和对应的行高
- **`text-body-base/body-base`**: 设置基础正文字体大小和对应的行高

这种语法的优点是：

1. **简洁明了**：一个类名同时设置字体大小和行高
2. **保持一致性**：确保字体大小和行高始终匹配
3. **自适应性**：当宽度模式变化时，字体大小和行高同时调整

**使用示例**：

```jsx
<h1 className="text-heading-1/heading-1 font-bold">主标题</h1>
<p className="text-body-base/body-base text-muted-foreground">正文内容</p>
```

与分开写法的等效形式：

```jsx
<h1 className="text-heading-1 leading-heading-1 font-bold">主标题</h1>
<p className="text-body-base leading-body-base text-muted-foreground">正文内容</p>
```

两种写法都是有效的，可以根据个人偏好选择。

## 响应式布局策略

### 1. 内容与图片比例

在不同宽度模式下，内容区域和图片区域的宽度比例会自动调整：

| 宽度模式 | 内容区域宽度 | 图片区域宽度 | 水平间距 |
|---------|------------|------------|--------|
| 普通模式 | 45% | 50% | 2rem (8) |
| 宽屏模式 | 48% | 48% | 3rem (12) |
| 满屏模式 | 45% | 50% | 4rem (16) |

### 2. 垂直间距调整

垂直间距也会随着宽度模式的变化而自动调整：

| 宽度模式 | 区块间距 | 内容间距 | 元素间距 |
|---------|---------|---------|---------|
| 普通模式 | 4rem (64px) | 2rem (32px) | 1rem (16px) |
| 宽屏模式 | 5rem (80px) | 2.5rem (40px) | 1.25rem (20px) |
| 满屏模式 | 6rem (96px) | 3rem (48px) | 1.5rem (24px) |

### 3. 字体大小调整

字体大小也会随着宽度模式的变化而自动调整：

| 宽度模式 | 一级标题 | 二级标题 | 三级标题 | 四级标题 | 大号正文 | 基础正文 | 小号正文 |
|---------|---------|---------|---------|---------|---------|---------|----------|
| 普通模式 | 3rem (48px) | 2.25rem (36px) | 1.875rem (30px) | 1.5rem (24px) | 1.125rem (18px) | 1rem (16px) | 0.875rem (14px) |
| 宽屏模式 | 3.5rem (56px) | 2.5rem (40px) | 2rem (32px) | 1.625rem (26px) | 1.25rem (20px) | 1.0625rem (17px) | 0.9375rem (15px) |
| 满屏模式 | 4rem (64px) | 2.75rem (44px) | 2.125rem (34px) | 1.75rem (28px) | 1.375rem (22px) | 1.125rem (18px) | 1rem (16px) |

## 使用指南

### 1. 切换宽度模式

通过在 `<html>` 元素上添加或移除类名来切换宽度模式：

```html
<!-- 普通模式（默认） -->
<html>
  <head>...</head>
  <body>...</body>
</html>

<!-- 宽屏模式 -->
<html class="page-width-wide">
  <head>...</head>
  <body>...</body>
</html>

<!-- 满屏模式 -->
<html class="page-width-full">
  <head>...</head>
  <body>...</body>
</html>
```

使用 JavaScript 动态切换模式：

```javascript
// 切换到宽屏模式
document.documentElement.classList.add('page-width-wide');
document.documentElement.classList.remove('page-width-full');

// 切换到满屏模式
document.documentElement.classList.add('page-width-full');
document.documentElement.classList.remove('page-width-wide');

// 切换回普通模式
document.documentElement.classList.remove('page-width-wide');
document.documentElement.classList.remove('page-width-full');
```

### 2. 在组件中应用

在组件中使用自定义类名来应用响应式宽度控制：

1. 使用 `max-w-container` 和 `px-container-x` 设置外层容器的最大宽度和水平内边距
2. 使用 `max-w-content` 设置内容区域的最大宽度
3. 使用 `pb-section-y`、`mt-content-y`、`mt-element-y` 等类控制垂直间距

#### 常见区块示例

##### 常规内容区块

```html
<section className="bg-background text-foreground">
  <div className="mx-auto max-w-container px-container-x py-section-y">
    <div className="max-w-content mx-auto">
      <h2 className="text-heading-2 font-bold">区块标题</h2>
      <p className="mt-element-y text-body-base text-muted-foreground">区块描述文本...</p>
      <div className="mt-content-y">
        <!-- 区块内容 -->
      </div>
    </div>
  </div>
</section>
```

##### 左文本右图片布局

```html
<section className="bg-background text-foreground">
  <div className="mx-auto max-w-container px-container-x py-section-y">
    <div className="lg:flex lg:items-center lg:gap-x-8 xl:gap-x-12 2xl:gap-x-16">
      <!-- 左侧文本区域 -->
      <div className="max-w-content lg:w-[45%] xl:w-[48%] 2xl:w-[45%] lg:flex-shrink-0">
        <h2 className="text-heading-2 font-bold">区块标题</h2>
        <p className="mt-element-y text-body-base text-muted-foreground">区块描述文本...</p>
        <div className="mt-content-y">
          <!-- 按钮或其他交互元素 -->
        </div>
      </div>
      
      <!-- 右侧图片区域 -->
      <div className="mt-content-y lg:mt-0 lg:flex-1 lg:max-w-[50%] xl:max-w-[48%] 2xl:max-w-[50%]">
        <div className="overflow-hidden rounded-xl bg-muted/20 p-2 ring-1 ring-inset ring-border">
          <img src="/path/to/image.jpg" alt="图片描述" className="w-full rounded-md" />
        </div>
      </div>
    </div>
  </div>
</section>
```

##### 全宽背景区块

```html
<section className="relative bg-muted overflow-hidden">
  <!-- 背景元素 -->
  <div className="absolute inset-0 -z-10">
    <!-- 背景图片或者装饰元素 -->
  </div>
  
  <div className="relative mx-auto max-w-container px-container-x py-section-y">
    <div className="max-w-content mx-auto text-center">
      <h2 className="text-heading-2 font-bold">区块标题</h2>
      <p className="mt-element-y text-body-base text-muted-foreground mx-auto">区块描述文本...</p>
      <div className="mt-content-y flex justify-center gap-x-4">
        <!-- 按钮或其他交互元素 -->
      </div>
    </div>
  </div>
</section>
```

##### 网格布局区块

```html
<section className="bg-background text-foreground">
  <div className="mx-auto max-w-container px-container-x py-section-y">
    <div className="max-w-content mx-auto">
      <h2 className="text-heading-2 font-bold">区块标题</h2>
      <p className="mt-element-y text-body-base text-muted-foreground">区块描述文本...</p>
    </div>
    
    <div className="mt-content-y grid grid-cols-1 gap-x-8 gap-y-content-y sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
      <!-- 网格项目 -->
      <div className="rounded-lg border p-6">
        <h3 className="text-heading-4 font-semibold">项目标题</h3>
        <p className="mt-element-y text-body-small text-muted-foreground">项目描述...</p>
      </div>
      <!-- 重复网格项目... -->
    </div>
  </div>
</section>
```

## 最佳实践

1. **保持一致性**：在所有组件中使用相同的类名和模式
2. **避免硬编码**：不要使用固定的宽度、间距和字体大小值，而是使用自定义类
3. **考虑内容长度**：测试不同长度的内容，确保布局在各种情况下都合理
4. **测试不同屏幕尺寸**：确保在各种设备上的显示效果都良好
5. **保持简洁**：避免添加不必要的容器元素
6. **使用语义化字体类**：使用 `text-heading-1` 而不是 `text-4xl`，以保持一致性和可维护性
7. **考虑行高**：使用 `/` 语法设置行高，如 `text-heading-2/tight` 或 `text-body-base/relaxed`

## 未来扩展

1. **更多宽度模式**：可以添加更多的宽度模式，如超宽屏模式
2. **组件特定变量**：为特定组件添加自定义的宽度、间距和字体大小变量
3. **用户自定义**：允许用户自定义宽度、间距和字体大小值
4. **动态调整**：根据内容长度和屏幕尺寸动态调整宽度、间距和字体大小
5. **字体比例系统**：实现基于比例的字体大小系统，如黄金比例或调制比例
6. **响应式排版**：添加更多排版控制，如字间距、行间距等的响应式调整

## 总结

本文档详细介绍了 LitPage 项目中实现的响应式页面宽度控制系统。通过 CSS 变量和 Tailwind CSS 自定义类，该系统提供了灵活且一致的页面布局控制和字体大小调整，无需添加额外的容器元素。这种实现方式使得页面布局更加灵活、可维护，同时保持了良好的用户体验。

响应式宽度控制系统不仅关注容器宽度和间距，还包括字体大小的自动调整，确保在不同宽度模式下文本的可读性和视觉平衡。这种全面的响应式设计方案使得页面在各种设备和屏幕尺寸上都能提供最佳的用户体验。
