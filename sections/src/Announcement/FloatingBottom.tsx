import { XMarkIcon } from '@heroicons/react/20/solid'

export default function Example() {
  return (
    <>
      {/*
        Make sure you add some bottom padding to pages that include a sticky banner like this to prevent
        your content from being obscured when the user scrolls to the bottom of the page.
      */}
      <section className="pointer-events-none fixed inset-x-0 bottom-0 sm:px-container-x sm:pb-element-y lg:px-container-x">
        <div className="pointer-events-auto flex items-center justify-between gap-x-element-x bg-gray-900 px-container-x py-element-y sm:rounded-xl sm:py-element-y sm:pl-content-y sm:pr-element-y">
          <p className="text-body-small/body-small text-white">
            <a href="#">
              <strong className="font-semibold">GeneriCon 2023</strong>
              <svg viewBox="0 0 2 2" aria-hidden="true" className="mx-element-x inline size-0.5 fill-current">
                <circle r={1} cx={1} cy={1} />
              </svg>
              Join us in Denver from June 7 – 9 to see what's coming next&nbsp;<span aria-hidden="true">&rarr;</span>
            </a>
          </p>
          <button type="button" className="-m-element-y flex-none p-element-y focus-visible:outline-offset-[-4px]">
            <span className="sr-only">Dismiss</span>
            <XMarkIcon aria-hidden="true" className="size-5 text-white" />
          </button>
        </div>
      </section>
    </>
  )
}
