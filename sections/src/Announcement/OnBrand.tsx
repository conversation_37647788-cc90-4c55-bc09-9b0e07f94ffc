import { XMarkIcon } from '@heroicons/react/20/solid'

export default function Example() {
  return (
    <section className="flex items-center gap-x-element-x bg-indigo-600 px-container-x py-element-y sm:px-container-x sm:before:flex-1">
      <p className="text-body-small/body-small text-white">
        <a href="#">
          <strong className="font-semibold">GeneriCon 2023</strong>
          <svg viewBox="0 0 2 2" aria-hidden="true" className="mx-element-x inline size-0.5 fill-current">
            <circle r={1} cx={1} cy={1} />
          </svg>
          Join us in Denver from June 7 – 9 to see what's coming next&nbsp;<span aria-hidden="true">&rarr;</span>
        </a>
      </p>
      <div className="flex flex-1 justify-end">
        <button type="button" className="-m-element-y p-element-y focus-visible:outline-offset-[-4px]">
          <span className="sr-only">Dismiss</span>
          <XMarkIcon aria-hidden="true" className="size-5 text-white" />
        </button>
      </div>
    </section>
  )
}
