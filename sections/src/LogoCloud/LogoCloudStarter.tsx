import type { LogoCloudSectionProps } from './types';
import { Picture } from '../components/Picture';

export default function LogoCloudStarter({ tagline, title, logos }: LogoCloudSectionProps) {
  return (
    <section className="py-section-y bg-background" aria-labelledby={title ? "logo-cloud-starter-title" : undefined}>
      <div className="mx-auto max-w-container px-container-x">
        {(tagline || title) && (
          <div className="mx-auto text-center mb-content-y">
            {tagline && <p className="text-body-small/body-small sm:text-body-base/body-base font-semibold text-muted-foreground text-balance">{tagline}</p>}
            {title && <h2 id="logo-cloud-starter-title" className="text-body-base/body-base sm:text-heading-4/heading-4 font-semibold text-foreground text-balance">{title}</h2>}
          </div>
        )}
        <div className="mx-auto mt-element-y grid max-w-content grid-cols-2 items-center gap-x-element-x gap-y-content-y sm:grid-cols-3 md:grid-cols-4 lg:mx-0 lg:max-w-none lg:grid-cols-6">
          {logos.map((logo, index) => (
            <div key={index} className="flex items-center justify-center py-element-y">
              <Picture
                src={logo.imageUrl}
                alt={logo.altText}
                width={158}
                height={48}
                densities={[1, 2, 3]}
                formats={['avif', 'webp', 'png', 'jpeg']}
                fit="contain"
                quality={{ avif: 85, webp: 85, png: 90, jpeg: 80 }}
                className="col-span-1 max-h-8 sm:max-h-10 md:max-h-12 w-full transition-all duration-300 hover:opacity-80 hover:scale-105 dark:invert dark:brightness-200 dark:contrast-50"
                style={{ display: 'block' }}
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
