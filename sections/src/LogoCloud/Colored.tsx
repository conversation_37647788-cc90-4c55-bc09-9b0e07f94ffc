import type { LogoCloudSectionProps } from './types';
import { Picture } from '../components/Picture';

export default function Colored({ tagline, title, logos }: LogoCloudSectionProps) {
  // 定义一组背景颜色类名 - 保持鲜艳的彩色效果
  const bgColors = [
    'bg-blue-400/20 dark:bg-blue-500/30',
    'bg-green-400/20 dark:bg-green-500/30',
    'bg-purple-400/20 dark:bg-purple-500/30',
    'bg-amber-400/20 dark:bg-amber-500/30',
    'bg-pink-400/20 dark:bg-pink-500/30',
    'bg-indigo-400/20 dark:bg-indigo-500/30'
  ];

  return (
    <section className="py-section-y bg-background" aria-labelledby={title ? "logo-cloud-colored-title" : undefined}>
      <div className="mx-auto max-w-container px-container-x">
        {(tagline || title) && (
          <div className="mx-auto text-center mb-content-y">
            {tagline && <p className="text-body-small/body-small font-semibold text-muted-foreground">{tagline}</p>}
            {title && <h2 id="logo-cloud-colored-title" className="text-body-base/body-base font-semibold text-foreground">{title}</h2>}
          </div>
        )}
        <div className="mx-auto grid max-w-content grid-cols-2 gap-element-y sm:grid-cols-3 md:grid-cols-3 lg:mx-0 lg:max-w-none lg:grid-cols-6">
          {logos.map((logo, index) => (
            <div 
              key={index} 
              className={`${bgColors[index % bgColors.length]} flex items-center justify-center p-element-y rounded-lg transition-all duration-300 hover:scale-105 hover:shadow-md backdrop-blur-sm`}
            >
              <Picture
                src={logo.imageUrl}
                alt={logo.altText}
                width={158}
                height={48}
                densities={[1, 2, 3]}
                formats={['avif', 'webp', 'png', 'jpeg']}
                fit="contain"
                quality={{ avif: 85, webp: 85, png: 90, jpeg: 80 }}
                className="max-h-12 w-auto dark:invert dark:brightness-200 dark:contrast-50"
                style={{ display: 'block' }}
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
