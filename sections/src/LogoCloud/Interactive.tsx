'use client'

import { useState, useRef } from 'react';
import { motion } from 'motion/react';
import type { LogoCloudSectionProps } from './types';
import { Picture } from '../components/Picture';

export default function Interactive({ tagline, title, logos }: LogoCloudSectionProps) {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  return (
    <section className="relative overflow-hidden bg-background py-section-y" aria-labelledby={title ? "logo-cloud-interactive-title" : undefined}>
      {/* 背景装饰 */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute inset-0 bg-[radial-gradient(40%_40%_at_50%_50%,theme(colors.primary.500/0.13),theme(colors.background.DEFAULT/0))] dark:bg-[radial-gradient(40%_40%_at_50%_50%,theme(colors.primary.400/0.13),theme(colors.background.DEFAULT/0))]" />
        <svg
          className="absolute inset-0 h-full w-full stroke-border [mask-image:radial-gradient(100%_100%_at_top_right,white,transparent)] dark:[mask-image:radial-gradient(100%_100%_at_top_right,black,transparent)]"
          aria-hidden="true"
        >
          <defs>
            <pattern
              id="logo-cloud-pattern"
              width={200}
              height={200}
              x="50%"
              y={-1}
              patternUnits="userSpaceOnUse"
            >
              <path d="M.5 200V.5H200" fill="none" />
            </pattern>
          </defs>
          <rect width="100%" height="100%" strokeWidth={0} fill="url(#logo-cloud-pattern)" />
          <svg x="50%" y={-1} className="overflow-visible fill-muted">
            <path
              d="M-200 0h201v201h-201Z M600 0h201v201h-201Z M-400 600h201v201h-201Z M200 800h201v201h-201Z"
              strokeWidth={0}
            />
          </svg>
        </svg>
      </div>

      <div className="mx-auto max-w-container px-container-x">
        {(tagline || title) && (
          <div className="mx-auto max-w-content text-center mb-content-y">
            {tagline && (
              <h2 className="inline-flex items-center rounded-full bg-foreground/10 dark:bg-foreground/20 px-element-x py-1.5 text-body-small/body-small font-medium text-muted-foreground ring-1 ring-inset ring-foreground/20">
                {tagline}
              </h2>
            )}
            {title && (
              <p id="logo-cloud-interactive-title" className="mt-element-y text-heading-2/heading-2 font-bold tracking-tight text-foreground">
                {title}
              </p>
            )}
          </div>
        )}

        <div 
          ref={containerRef}
          className="relative mx-auto grid max-w-content grid-cols-2 gap-element-y sm:grid-cols-3 lg:mx-0 lg:max-w-none lg:grid-cols-6"
        >
          {logos.map((logo, index) => (
            <motion.div 
              key={index}
              className="group relative flex h-24 items-center justify-center rounded-xl bg-card p-4 shadow-sm ring-1 ring-border dark:shadow-primary/5"
              role="img"
              aria-label={logo.name}
              onMouseEnter={() => setHoveredIndex(index)}
              onMouseLeave={() => setHoveredIndex(null)}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ 
                duration: 0.5, 
                delay: index * 0.1,
                ease: [0.21, 0.45, 0.27, 0.9]
              }}
              whileHover={{ 
                scale: 1.05, 
                boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)",
                y: -5
              }}
            >
              <motion.div
                className="relative flex items-center justify-center w-full h-full"
                initial={{ opacity: 1 }}
                whileHover={{ scale: 1.1 }}
                transition={{ duration: 0.2 }}
              >
                <Picture
                  src={logo.imageUrl}
                  alt={logo.altText}
                  width={158}
                  height={48}
                  densities={[1, 2, 3]}
                  formats={['avif', 'webp', 'png', 'jpeg']}
                  fit="contain"
                  quality={{ avif: 85, webp: 85, png: 90, jpeg: 80 }}
                  className="max-h-12 w-auto dark:invert dark:opacity-80"
                  style={{ display: 'block' }}
                />
              </motion.div>
              
              {/* 悬停时显示的标签 */}
              <motion.div
                className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-primary to-primary/80 text-primary-foreground text-center py-1.5 rounded-b-xl"
                initial={{ opacity: 0, y: 10 }}
                animate={{ 
                  opacity: hoveredIndex === index ? 1 : 0,
                  y: hoveredIndex === index ? 0 : 10
                }}
                transition={{ duration: 0.2 }}
              >
                <span className="text-xs font-medium">{logo.name}</span>
              </motion.div>
            </motion.div>
          ))}
        </div>
        
        {/* 底部装饰 */}
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-border to-transparent" aria-hidden="true" />
      </div>
    </section>
  );
}
