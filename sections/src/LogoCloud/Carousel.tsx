import type { LogoCloudSectionProps } from './types';
import { Picture } from '../components/Picture';

export default function Carousel({ tagline, title, logos }: LogoCloudSectionProps) {
  return (
    <section className="py-section-y bg-background" aria-labelledby={title ? "logo-cloud-carousel-title" : undefined}>
      <div className="mx-auto max-w-container px-container-x">
        {(tagline || title) && (
          <div className="mx-auto text-center mb-content-y">
            {tagline && <p className="text-body-small/body-small sm:text-body-base/body-base font-semibold text-muted-foreground text-balance">{tagline}</p>}
            {title && <h2 id="logo-cloud-carousel-title" className="text-body-base/body-base sm:text-heading-4/heading-4 font-semibold text-foreground text-balance">{title}</h2>}
          </div>
        )}
        <div className="relative">
          <div className="relative overflow-hidden py-element-y">
            <div 
              className="flex gap-element-x sm:gap-content-x pb-element-y overflow-x-auto scrollbar-hide" 
              role="region" 
              aria-label="合作伙伴徽标轮播"
              style={{ scrollBehavior: 'smooth', WebkitOverflowScrolling: 'touch' }}
            >
              {logos.map((logo, index) => (
                <div 
                  key={index} 
                  className="flex-none py-element-y px-element-x sm:px-content-x bg-muted/30 rounded-xl hover:bg-muted/50 transition-all duration-300 hover:shadow-md min-h-[80px] sm:min-h-[100px] flex items-center justify-center"
                >
                  <Picture
                    src={logo.imageUrl}
                    alt={logo.altText}
                    width={158}
                    height={48}
                    densities={[1, 2, 3]}
                    formats={['avif', 'webp', 'png', 'jpeg']}
                    fit="contain"
                    quality={{ avif: 85, webp: 85, png: 90, jpeg: 80 }}
                    className="max-h-8 sm:max-h-10 md:max-h-12 w-auto dark:invert dark:opacity-80"
                    style={{ display: 'block' }}
                  />
                </div>
              ))}
            </div>
          </div>
          <div className="absolute pointer-events-none inset-y-0 left-0 w-12 sm:w-16 md:w-24 bg-gradient-to-r from-background"></div>
          <div className="absolute pointer-events-none inset-y-0 right-0 w-12 sm:w-16 md:w-24 bg-gradient-to-l from-background"></div>
        </div>
      </div>
    </section>
  );
}
