import type { LogoCloudSectionProps } from './types';
import { Picture } from '../components/Picture';

export default function LogoCloudBasic({ tagline, title, logos }: LogoCloudSectionProps) {
  return (
    <section className="py-section-y bg-background" aria-labelledby={title ? "logo-cloud-basic-title" : undefined}>
      <div className="mx-auto max-w-container px-container-x">
        <div className="mx-auto max-w-content lg:max-w-none">
          {tagline && <p className="text-body-small/body-small font-semibold text-muted-foreground">{tagline}</p>}
          {title && (
            <h2 id="logo-cloud-basic-title" className="text-body-base/body-base font-semibold text-foreground">
              {title}
            </h2>
          )}
          <div className="mx-auto mt-element-y grid grid-cols-3 items-start gap-x-element-x gap-y-element-y sm:grid-cols-6 lg:mx-0 lg:grid-cols-6">
            {logos.map((logo, index) => (
              <Picture
                key={index}
                src={logo.imageUrl}
                alt={logo.altText}
                width={158}
                height={48}
                densities={[1, 2, 3]}
                formats={['avif', 'webp', 'png', 'jpeg']}
                fit="contain"
                quality={{ avif: 85, webp: 85, png: 90, jpeg: 80 }}
                className="col-span-1 max-h-12 w-full object-left sm:col-span-1 dark:invert dark:opacity-80"
                style={{ display: 'block' }}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
