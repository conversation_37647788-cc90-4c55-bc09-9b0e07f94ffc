import type { LogoCloudSectionProps } from './types';
import { Picture } from '../components/Picture';

export default function Dark({ tagline, title, logos }: LogoCloudSectionProps) {
  return (
    <section className="py-section-y bg-background" aria-labelledby={title ? "logo-cloud-dark-title" : undefined}>
      <div className="mx-auto max-w-container px-container-x">
        {(tagline || title) && (
          <div className="mx-auto text-center mb-content-y">
            {tagline && <p className="text-body-small/body-small font-semibold text-muted-foreground">{tagline}</p>}
            {title && <h2 id="logo-cloud-dark-title" className="text-body-base/body-base font-semibold text-foreground">{title}</h2>}
          </div>
        )}
        <div className="mx-auto grid max-w-content grid-cols-2 gap-x-element-x gap-y-content-y sm:grid-cols-3 lg:mx-0 lg:max-w-none lg:grid-cols-6">
          {logos.map((logo, index) => (
            <div 
              key={index}
              className="flex items-center justify-center col-span-1 transition-opacity duration-300 hover:opacity-100 opacity-80"
            >
              <Picture
                src={logo.imageUrl}
                alt={logo.altText}
                width={158}
                height={48}
                densities={[1, 2, 3]}
                formats={['avif', 'webp', 'png', 'jpeg']}
                fit="contain"
                quality={{ avif: 85, webp: 85, png: 90, jpeg: 80 }}
                className="max-h-12 w-auto dark:invert dark:brightness-200 dark:contrast-50"
                style={{ display: 'block' }}
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
