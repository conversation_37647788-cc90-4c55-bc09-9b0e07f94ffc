import type { LogoCloudSectionProps } from './types';
import { Picture } from '../components/Picture';

export default function Grid({ tagline, title, logos }: LogoCloudSectionProps) {
  return (
    <section className="py-section-y bg-background" aria-labelledby={title ? "logo-cloud-grid-title" : undefined}>
      <div className="mx-auto max-w-container px-container-x">
        {(tagline || title) && (
          <div className="mx-auto text-center mb-content-y">
            {tagline && <p className="text-body-small/body-small sm:text-body-base/body-base font-semibold text-muted-foreground text-balance">{tagline}</p>}
            {title && <h2 id="logo-cloud-grid-title" className="text-body-base/body-base sm:text-heading-4/heading-4 font-semibold text-foreground text-balance">{title}</h2>}
          </div>
        )}
        <div className="-mx-container-x grid grid-cols-2 gap-0.5 overflow-hidden sm:mx-0 sm:rounded-xl md:rounded-2xl md:grid-cols-3">
          {logos.slice(0, 6).map((logo, index) => (
            <div 
              key={index} 
              className="bg-muted/30 p-element-y sm:p-content-y hover:bg-muted/50 transition-all duration-300 hover:shadow-inner min-h-[80px] sm:min-h-[100px] md:min-h-[120px] flex items-center justify-center"
            >
              <Picture
                src={logo.imageUrl}
                alt={logo.altText}
                width={158}
                height={48}
                densities={[1, 2, 3]}
                formats={['avif', 'webp', 'png', 'jpeg']}
                fit="contain"
                quality={{ avif: 85, webp: 85, png: 90, jpeg: 80 }}
                className="max-h-8 sm:max-h-10 md:max-h-12 w-full dark:invert dark:opacity-80 transition-transform duration-300 hover:scale-105"
                style={{ display: 'block' }}
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
