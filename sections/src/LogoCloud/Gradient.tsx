import type { LogoCloudSectionProps } from './types';
import { Picture } from '../components/Picture';

export default function Gradient({ tagline, title, logos }: LogoCloudSectionProps) {
  return (
    <section className="relative isolate overflow-hidden bg-gradient-to-b from-primary/10 via-background to-background dark:from-primary/5 dark:via-background dark:to-background py-section-y" aria-labelledby={title ? "logo-cloud-gradient-title" : undefined}>
      {/* 背景装饰 */}
      <div className="absolute inset-0 -z-10 bg-[radial-gradient(45rem_50rem_at_top,theme(colors.primary.100),theme(colors.background.DEFAULT))] dark:bg-[radial-gradient(45rem_50rem_at_top,theme(colors.primary.900),theme(colors.background.DEFAULT))] opacity-20" />
      <div className="absolute inset-y-0 right-1/2 -z-10 mr-16 w-[200%] origin-bottom-left skew-x-[-30deg] bg-card shadow-xl shadow-primary/10 ring-1 ring-border sm:mr-28 lg:mr-0 xl:mr-16 xl:origin-center" />
      
      <div className="mx-auto max-w-container px-container-x">
        {(tagline || title) && (
          <div className="mx-auto text-center mb-content-y">
            {tagline && (
              <h2 className="inline-flex items-center rounded-full text-muted-foreground px-element-x py-1.5 text-body-small/body-small font-medium ring-1 ring-inset ring-foreground/20">
                {tagline}
              </h2>
            )}
            {title && <p id="logo-cloud-gradient-title" className="mt-element-y text-body-large/body-large font-semibold text-foreground">{title}</p>}
          </div>
        )}
        <div className="mx-auto grid max-w-content grid-cols-2 items-center gap-x-element-x gap-y-content-y sm:grid-cols-3 lg:mx-0 lg:max-w-none lg:grid-cols-6">
          {logos.map((logo, index) => (
            <div 
              key={index}
              className="col-span-1 flex justify-center transition-all duration-300 hover:scale-110 hover:opacity-80"
            >
              <Picture
                src={logo.imageUrl}
                alt={logo.altText}
                width={158}
                height={48}
                densities={[1, 2, 3]}
                formats={['avif', 'webp', 'png', 'jpeg']}
                fit="contain"
                quality={{ avif: 85, webp: 85, png: 90, jpeg: 80 }}
                className="max-h-12 w-auto dark:invert dark:opacity-80"
                style={{ display: 'block' }}
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
