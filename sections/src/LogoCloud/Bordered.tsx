import type { LogoCloudSectionProps } from './types';
import { Picture } from '../components/Picture';

export default function Bordered({ tagline, title, logos }: LogoCloudSectionProps) {
  return (
    <section className="py-section-y bg-background" aria-labelledby={title ? "logo-cloud-bordered-title" : undefined}>
      <div className="mx-auto max-w-container px-container-x">
        {(tagline || title) && (
          <div className="mx-auto text-center mb-content-y">
            {tagline && <p className="text-body-small/body-small sm:text-body-base/body-base font-semibold text-muted-foreground text-balance">{tagline}</p>}
            {title && <h2 id="logo-cloud-bordered-title" className="text-body-base/body-base sm:text-heading-4/heading-4 font-semibold text-foreground text-balance">{title}</h2>}
          </div>
        )}
        <div className="mx-auto grid max-w-content grid-cols-2 gap-element-x gap-y-element-y sm:grid-cols-3 md:grid-cols-4 lg:mx-0 lg:max-w-none lg:grid-cols-6">
          {logos.map((logo, index) => (
            <div 
              key={index} 
              className="flex items-center justify-center p-element-y sm:p-content-y border border-border rounded-lg bg-card shadow-sm hover:shadow-md transition-all duration-300 hover:border-primary/30 min-h-[80px] sm:min-h-[100px]"
            >
              <Picture
                src={logo.imageUrl}
                alt={logo.altText}
                width={158}
                height={48}
                densities={[1, 2, 3]}
                formats={['avif', 'webp', 'png', 'jpeg']}
                fit="contain"
                quality={{ avif: 85, webp: 85, png: 90, jpeg: 80 }}
                className="max-h-8 sm:max-h-10 md:max-h-12 w-auto dark:invert dark:opacity-80 transition-transform duration-300 hover:scale-105"
                style={{ display: 'block' }}
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
