import React from 'react';
import type { FeaturesSectionProps } from './types';
import { BlockContainer } from '../components/BlockContainer';
import { IconRenderer } from '../components/IconRenderer';

const PrimaryFeatures: React.FC<FeaturesSectionProps> = ({
  id,
  title,
  description,
  features,
  resources,
  customize,
  variants,
}) => {
  return (
    <BlockContainer id={id} resources={resources} customize={customize}>
      <div className="relative mx-auto max-w-7xl">
        <div className="mx-auto max-w-2xl sm:text-center">
          <h2 className="text-3xl font-medium tracking-tight">
            {title}
          </h2>
          <p className="mt-2 text-lg">
            {description}
          </p>
        </div>
        <ul
          role="list"
          className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-6 text-sm sm:mt-20 sm:grid-cols-2 md:gap-y-10 lg:max-w-none lg:grid-cols-3"
        >
          {features.map((feature: any, index: number) => (
            <li
              key={feature.id || index}
              className="rounded-2xl border border-gray-200 p-8"
            >
              <IconRenderer  name={variants?.basic?.features?.[index]?.icon ?? ''} className="h-8 w-8" />
              <h3 className="mt-6 font-semibold">
                {feature.title ?? feature.feature}
              </h3>
              <p className="mt-2">
                {feature.description}
              </p>
            </li>
          ))}
        </ul>
      </div>
    </BlockContainer>
  );
};

export default PrimaryFeatures;