import React from 'react';
import type { FeaturesSectionProps } from './types';
import ImgPipe from '../components/ImgPipe';
import { BlockContainer } from '../components/BlockContainer';

const ImageFeature: React.FC<FeaturesSectionProps> = ({
  id,
  title,
  description,
  features,
  resources,
  customize,
  variants
}) => {
  const coverImage = variants?.image?.image?.url ?? '/images_8bmWwQtW_KgMW6ymgvJn3Vsl.jpg';

  return (
    <BlockContainer id={id} resources={resources} customize={customize}>
      <div className="relative mx-auto max-w-7xl">
        <div className="mx-auto grid max-w-2xl grid-cols-1 gap-x-8 gap-y-16 sm:gap-y-20 lg:mx-0 lg:max-w-none lg:grid-cols-2">
          <div className="lg:pr-8 lg:pt-4">
            <div className="lg:max-w-lg">
              <h2 className="text-base font-semibold leading-7 text-indigo-600">
                Deploy faster
              </h2>
              <p className="mt-2 text-3xl font-bold tracking-tight sm:text-4xl">
                {title}
              </p>
              <p className="mt-6 text-lg leading-8">
                {description}
              </p>
              <dl className="mt-10 max-w-xl space-y-8 text-base leading-7 lg:max-w-none">
                {features.map((feature: any) => (
                  <div key={feature.id} className="relative pl-9">
                    <dt className="inline font-semibold">
                      {feature.icon && (
                        <feature.icon
                          className="absolute left-1 top-1 h-5 w-5 text-indigo-600"
                          aria-hidden="true"
                        />
                      )}
                      <span className="inline">
                        {feature.name ?? feature.title}
                      </span>
                    </dt>{' '}
                    <dd className="inline">
                      {feature.description}
                    </dd>
                  </div>
                ))}
              </dl>
            </div>
          </div>
          <ImgPipe
            at={`${id}.cover`}
            src={`${coverImage}?fm=webp&w=1500`}
            alt="Product screenshot"
            className="w-[48rem] max-w-none rounded-xl shadow-xl ring-1 ring-gray-400/10 sm:w-[57rem] md:-ml-4 lg:-ml-0"
            width={2432}
            height={1442}
            sizes="(min-width: 1024px) 57rem, (min-width: 768px) 48rem, 100vw"
            srcSet={`
              ${coverImage}?fm=webp&w=640 640w,
              ${coverImage}?fm=webp&w=768 768w,
              ${coverImage}?fm=webp&w=1024 1024w,
              ${coverImage}?fm=webp&w=1500 1500w
            `}
          />
        </div>
      </div>
    </BlockContainer>
  );
};

export default ImageFeature;