import React, { useEffect, useState } from 'react';
import type { FeaturesSectionProps } from './types';
import { Tab } from '@headlessui/react';
import clsx from 'clsx';
import ImgPipe from '../components/ImgPipe';
import { BlockContainer } from '../components/BlockContainer';

interface Feature {
  id: string;
  name?: string;
  title?: string;
  description: string;
}

const FeatureTab: React.FC<{ feature: Feature; isSelected: boolean; customize?: any; id: string }> = React.memo(
  ({ feature, isSelected }) => (
    <div
      className={clsx(
        'group relative rounded-full px-4 py-1 lg:rounded-l-xl lg:rounded-r-none lg:p-6',
        isSelected
          ? 'bg-base-100 lg:bg-base-200 lg:ring-1 lg:ring-inset lg:ring-base-content/10'
          : 'hover:bg-base-200 lg:hover:bg-base-200/50',
      )}
    >
      <h3>
        <Tab
          className={clsx(
            'font-display text-lg ui-not-focus-visible:outline-none focus:outline-none',
            isSelected
              ? 'text-primary lg:text-base-content'
              : 'text-base-content/70 hover:text-base-content lg:text-base-content',
          )}
        >
          <span>
            {feature.name ?? feature.title}
          </span>
        </Tab>
      </h3>
      <p
        className={clsx(
          'mt-2 hidden text-sm lg:block',
          isSelected
            ? 'text-base-content'
            : 'text-base-content/70 group-hover:text-base-content',
        )}
      >
        {feature.description}
      </p>
    </div>
  )
);

const useTabOrientation = () => {
  const [tabOrientation, setTabOrientation] = useState<'horizontal' | 'vertical'>('horizontal');

  useEffect(() => {
    const lgMediaQuery = window.matchMedia('(min-width: 1024px)');

    const onMediaQueryChange = ({ matches }: { matches: boolean }) => {
      setTabOrientation(matches ? 'vertical' : 'horizontal');
    };

    onMediaQueryChange(lgMediaQuery);
    lgMediaQuery.addEventListener('change', onMediaQueryChange);

    return () => {
      lgMediaQuery.removeEventListener('change', onMediaQueryChange);
    };
  }, []);

  return tabOrientation;
};

const PrimaryFeatures: React.FC<FeaturesSectionProps> = ({ id, title, description, features, resources, customize, variants }) => {
  const tabOrientation = useTabOrientation();

  return (
    <BlockContainer id={id} resources={resources} customize={customize}>
      <div className="relative w-full max-w-7xl mx-auto">
        <div className="max-w-2xl md:mx-auto md:text-center xl:max-w-none">
          <h2 className="font-display text-3xl tracking-tight sm:text-4xl md:text-5xl">
            {title}
          </h2>
          <p className="mt-6 text-lg tracking-tight">
            {description}
          </p>
        </div>
        <Tab.Group
          as="div"
          className="mt-16 grid grid-cols-1 items-center gap-y-2 pt-10 sm:gap-y-6 md:mt-20 lg:grid-cols-12 lg:pt-0"
          vertical={tabOrientation === 'horizontal'}
        >
          {({ selectedIndex }) => (
            <>
              <div className="-mx-4 flex overflow-x-auto pb-4 sm:mx-0 sm:overflow-visible sm:pb-0 lg:col-span-5">
                <Tab.List className="relative z-10 flex gap-x-4 whitespace-nowrap px-4 sm:mx-auto sm:px-0 lg:mx-0 lg:block lg:gap-x-0 lg:gap-y-1 lg:whitespace-normal">
                  {features.map((feature: any, index: number) => (
                    <FeatureTab
                      key={feature.id}
                      feature={feature}
                      isSelected={selectedIndex === index}
                      customize={customize}
                      id={id}
                    />
                  ))}
                </Tab.List>
              </div>
              <Tab.Panels className="lg:col-span-7">
                {features.map((feature: any, index) => (
                  <Tab.Panel key={feature.id} unmount={false}>
                    <div className="relative sm:px-6 lg:hidden">
                      <div className="absolute -inset-x-4 bottom-[-4.25rem] top-[-6.5rem] bg-base-200/50 ring-1 ring-inset ring-base-content/10 sm:inset-x-0 sm:rounded-t-xl" />
                      <p className="relative mx-auto max-w-2xl text-base text-base-content sm:text-center">
                        {feature.description}
                      </p>
                    </div>
                    <div className="mt-10 w-[45rem] overflow-hidden rounded-xl bg-base-100 shadow-xl shadow-base-content/20 sm:w-auto lg:mt-0 lg:w-[67.8125rem]">
                      <ImgPipe
                        at={`${id}.${feature.id}`}
                        className="w-full"
                        src={variants?.default?.features?.[index]?.image?.url ?? '/images_8bmWwQtW_KgMW6ymgvJn3Vsl.jpg'}
                        alt={`Feature: ${feature.name ?? feature.title}`}
                        sizes="(min-width: 1024px) 67.8125rem, (min-width: 640px) 100vw, 45rem"
                                      srcSet={variants?.default?.features?.[index]?.image?.url ?? '/images_8bmWwQtW_KgMW6ymgvJn3Vsl.jpg'}
                      />
                    </div>
                  </Tab.Panel>
                ))}
              </Tab.Panels>
            </>
          )}
        </Tab.Group>
      </div>
    </BlockContainer>
  );
};

export default React.memo(PrimaryFeatures);