import type { TeamMember, TeamMemberSocialLink, HiringSection, HiringButton, TeamSectionProps } from './types';

// 默认团队成员数据
export const defaultTeamMembers: TeamMember[] = [
  {
    name: '<PERSON>',
    role: 'Co-Founder / CEO',
    imageUrl: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&q=80',
    socialLinks: [
      { platform: 'linkedin', url: '#' },
      { platform: 'x', url: '#' },
      { platform: 'github', url: '#' }
    ] as TeamMemberSocialLink[]
  },
  {
    name: '<PERSON>',
    role: 'Co-Founder / CTO',
    imageUrl: 'https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&q=80',
    socialLinks: [
      { platform: 'linkedin', url: '#' },
      { platform: 'github', url: '#' }
    ] as TeamMemberSocialLink[]
  },
  {
    name: 'Dries Vincent',
    role: 'Product Design Lead',
    imageUrl: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&q=80',
    socialLinks: [
      { platform: 'linkedin', url: '#' },
      { platform: 'x', url: '#' },
      { platform: 'instagram', url: '#' }
    ] as TeamMemberSocialLink[]
  },
  {
    name: 'Lindsay Walton',
    role: 'Front-end Developer',
    imageUrl: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&q=80',
    socialLinks: [
      { platform: 'github', url: '#' },
      { platform: 'linkedin', url: '#' }
    ] as TeamMemberSocialLink[]
  },
  {
    name: 'Courtney Henry',
    role: 'Marketing Lead',
    imageUrl: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&q=80',
    socialLinks: [
      { platform: 'linkedin', url: '#' },
      { platform: 'x', url: '#' },
      { platform: 'facebook', url: '#' },
      { platform: 'instagram', url: '#' }
    ] as TeamMemberSocialLink[]
  }
];

// 默认招聘部分数据
export const defaultHiringSection: HiringSection = {
  enabled: true,
  title: 'We are hiring!',
  subtitle: 'Join our team',
  imageUrl: 'https://images.unsplash.com/photo-1568992687947-868a62a9f521?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&q=80',
  button: {
    label: 'View open positions',
    href: '#',
    urlType: 'external'
  } as HiringButton
};

// 默认 TeamSimple 属性
export const defaultTeamSimpleProps: TeamSectionProps = {
  tagline: 'Our Team',
  title: 'Meet our leadership',
  description: "We're a dynamic group of individuals who are passionate about what we do and dedicated to delivering the best results for our clients.",
  people: defaultTeamMembers,
  hiringSection: defaultHiringSection
};
