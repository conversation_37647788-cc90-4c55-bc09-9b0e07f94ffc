import React from 'react';
import SocialIcons from '../components/SocialIcons';
import { TeamSectionProps, TeamMemberSocialLink } from './types';
import { Picture } from '../components/Picture';

const SmallCircledImage: React.FC<TeamSectionProps> = ({
  tagline = '',
  title = '',
  description = '',
  people = [],
  hiringSection = { enabled: false }
}) => {
  // 将社交媒体平台映射到对应的URL属性
  const getSocialUrls = (socialLinks: TeamMemberSocialLink[] = []) => {
    const urls: {
      xUrl?: string;
      linkedinUrl?: string;
      githubUrl?: string;
      facebookUrl?: string;
      instagramUrl?: string;
      youtubeUrl?: string;
    } = {
      xUrl: undefined,
      linkedinUrl: undefined,
      githubUrl: undefined,
      facebookUrl: undefined,
      instagramUrl: undefined,
      youtubeUrl: undefined
    };
    
    socialLinks.forEach(link => {
      if (link.platform === 'x') urls.xUrl = link.url;
      if (link.platform === 'linkedin') urls.linkedinUrl = link.url;
      if (link.platform === 'github') urls.githubUrl = link.url;
      if (link.platform === 'facebook') urls.facebookUrl = link.url;
      if (link.platform === 'instagram') urls.instagramUrl = link.url;
      if (link.platform === 'youtube') urls.youtubeUrl = link.url;
    });
    
    return urls;
  };

  return (
    <section className="bg-background py-section-y" aria-labelledby="team-simple-circled-title">
      <div className="mx-auto max-w-container px-container-x">
        {/* Title */}
        <div className="mx-auto max-w-content text-center mb-content-y">
          {tagline && <p className="text-body-small/body-small font-semibold text-primary uppercase">{tagline}</p>}
          <h2 id="team-simple-circled-title" className="mt-element-y text-heading-2/heading-2 font-bold text-foreground">{title}</h2>
          <p className="mt-element-y text-body-base/body-base text-muted-foreground">{description}</p>
        </div>
        {/* End Title */}

        {/* Grid */}
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-element-y gap-x-element-x">
          {people && people.map((person) => {
            const socialUrls = getSocialUrls(person.socialLinks);
            
            return (
              <div key={person.name} className="text-center">
                <div className="mx-auto size-24 rounded-full overflow-hidden">
                  <Picture
                    src={person.imageUrl || ''}
                    alt={`${person.name} avatar`}
                    width={96}
                    height={96}
                    densities={[1, 2, 3]}
                    fit="cover"
                    quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="mt-element-y">
                  <h3 className="text-heading-4/heading-4 font-medium text-foreground">
                    {person.name}
                  </h3>
                  <p className="mt-element-y text-body-small/body-small text-secondary-foreground">
                    {person.role}
                  </p>
                  <div className="mt-element-y flex justify-center">
                    <SocialIcons
                      {...socialUrls}
                      size="xs"
                      variant="simple"
                      className="text-muted-foreground hover:text-foreground gap-x-3"
                    />
                  </div>
                </div>
              </div>
            );
          })}
          
          {hiringSection && hiringSection.enabled && (
            <div className="text-center">
              <div className="mx-auto size-24 rounded-full overflow-hidden">
                <Picture
                  src={hiringSection.imageUrl || ''}
                  alt={`${hiringSection.title} - ${hiringSection.subtitle}`}
                  width={96}
                  height={96}
                  densities={[1, 2, 3]}
                  fit="cover"
                  quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="mt-element-y">
                <h3 className="text-heading-4/heading-4 font-medium text-foreground">
                  {hiringSection.title}
                </h3>
                <p className="mt-element-y text-body-small/body-small text-secondary-foreground">
                  {hiringSection.subtitle}
                </p>
                {hiringSection.button && (
                  <div className="mt-element-y">
                    <a 
                      className="inline-block text-body-small/body-small text-primary decoration-2 hover:underline hover:text-primary/90" 
                      href={hiringSection.button.href}
                    >
                      {hiringSection.button.label}
                    </a>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
        {/* End Grid */}
      </div>
    </section>
  );
};

export default SmallCircledImage;