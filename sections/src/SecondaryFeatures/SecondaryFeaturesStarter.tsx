import React from 'react';
import { Tab } from '@headlessui/react';
import clsx from 'clsx';
import { BlockContainer } from '../components/BlockContainer';
import { Container } from '../components/Container';
import ImgPipe from "../components/ImgPipe";
import type { FeaturesSectionProps, FeatureItem } from './types';

interface CustomizeType {
  bgColor?: string;
  textColor?: string;
  [key: string]: any;
}

interface FeatureProps {
  id: string;
  feature: FeatureItem;
  isActive: boolean;
  customize?: CustomizeType;
}

function ReportingIcon() {
  let id = React.useId();
  return (
    <>
      <defs>
        <linearGradient
          id={id}
          x1="11.5"
          y1={18}
          x2={36}
          y2="15.5"
          gradientUnits="userSpaceOnUse"
        >
          <stop offset=".194" stopColor="#fff" />
          <stop offset={1} stopColor="#6692F1" />
        </linearGradient>
      </defs>
      <path
        d="m30 15-4 5-4-11-4 18-4-11-4 7-4-5"
        stroke={`url(#${id})`}
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </>
  );
}

const Feature: React.FC<FeatureProps> = React.memo(({ id, feature, isActive }: any) => {
  return (
    <div className={clsx(!isActive && 'opacity-75 hover:opacity-100')}>
      <div className={clsx('w-9 rounded-lg', isActive ? 'bg-blue-600' : 'bg-slate-500')}>
        <svg aria-hidden="true" className="h-9 w-9" fill="none">
          {feature.icon ? <feature.icon /> : <ReportingIcon />}
        </svg>
      </div>
      <h3 className={clsx('mt-6 text-sm font-medium', isActive ? 'text-blue-600' : 'text-slate-600')}>
        {feature.name || feature.title}
      </h3>
      <p className="mt-2 font-display text-xl">
        {feature.summary || ''}
      </p>
      <p className="mt-4 text-sm">
        {feature.description}
      </p>
    </div>
  );
});

const FeatureImage: React.FC<{ id: string; feature: any; resources: any }> = React.memo(({ id, feature, resources }) => {
          const imgSrc = resources?.images?.[`${id}_${feature.id}`]?.src || '/images_8bmWwQtW_KgMW6ymgvJn3Vsl.jpg';
  return (
    <ImgPipe
      at={`${id}.features@${feature.id}.image`}
      className="w-full"
      src={`${imgSrc}?fm=webp&w=1688&h=856&fit=cover`}
      alt={`Feature: ${feature.name || feature.title}`}
      width={1688}
      height={856}
      sizes="(min-width: 1024px) 52.75rem, 100vw"
      srcSet={`
        ${imgSrc}?fm=webp&w=640&fit=cover 640w,
        ${imgSrc}?fm=webp&w=1024&fit=cover 1024w,
        ${imgSrc}?fm=webp&w=1688&fit=cover 1688w
      `}
    />
  );
});

const FeaturesMobile: React.FC<any> = React.memo(({ id, features, resources, customize }) => {
  return (
    <div className="-mx-4 mt-20 flex flex-col gap-y-10 overflow-hidden px-4 sm:-mx-6 sm:px-6 lg:hidden">
      {features.map((feature: any) => (
        <div key={feature.id}>
          <Feature id={id} feature={feature} isActive={true} customize={customize} />
          <div className="relative mt-10 pb-10">
            <div className="absolute -inset-x-4 bottom-0 top-8 bg-slate-200 sm:-inset-x-6" />
            <div className="relative mx-auto w-full max-w-[52.75rem] overflow-hidden rounded-xl bg-white shadow-lg shadow-slate-900/5 ring-1 ring-slate-500/10">
              <FeatureImage id={id} feature={feature} resources={resources} />
            </div>
          </div>
        </div>
      ))}
    </div>
  );
});

const FeaturesDesktop: React.FC<any> = React.memo(({ id, features, resources, customize }) => {
  return (
    <Tab.Group as="div" className="hidden lg:mt-20 lg:block">
      {({ selectedIndex }) => (
        <>
          <Tab.List className="grid grid-cols-3 gap-x-8">
            {features.map((feature: any, featureIndex: number) => (
              <Feature
                key={feature.id}
                id={id}
                feature={{
                  ...feature,
                }}
                isActive={featureIndex === selectedIndex}
                customize={customize}
              />
            ))}
          </Tab.List>
          <Tab.Panels className="relative mt-20 overflow-hidden rounded-4xl bg-slate-200 px-14 py-16 xl:px-16">
            <div className="-mx-5 flex">
              {features.map((feature: any, featureIndex: number) => (
                <Tab.Panel
                  static
                  key={feature.id}
                  className={clsx(
                    'px-5 transition duration-500 ease-in-out ui-not-focus-visible:outline-none',
                    featureIndex !== selectedIndex && 'opacity-60',
                  )}
                  style={{ transform: `translateX(-${selectedIndex * 100}%)` }}
                  aria-hidden={featureIndex !== selectedIndex}
                >
                  <div className="w-[52.75rem] overflow-hidden rounded-xl bg-white shadow-lg shadow-slate-900/5 ring-1 ring-slate-500/10">
                    <FeatureImage id={id} feature={feature} resources={resources} />
                  </div>
                </Tab.Panel>
              ))}
            </div>
            <div className="pointer-events-none absolute inset-0 rounded-4xl ring-1 ring-inset ring-slate-900/10" />
          </Tab.Panels>
        </>
      )}
    </Tab.Group>
  );
});

const SecondaryFeatures: React.FC<FeaturesSectionProps> = ({ id, title, description, features, resources, customize }) => {
  return (
    <BlockContainer id={id} resources={resources} customize={customize}>
      <Container>
        <div className="mx-auto max-w-2xl md:text-center">
          <h2 className="font-display text-3xl tracking-tight sm:text-4xl">
            {title}
          </h2>
          <p className="mt-4 text-lg tracking-tight">
            {description}
          </p>
        </div>
        <FeaturesMobile id={id} features={features} resources={resources} customize={customize} />
        <FeaturesDesktop id={id} features={features} resources={resources} customize={customize} />
      </Container>
    </BlockContainer>
  );
};

export default React.memo(SecondaryFeatures);