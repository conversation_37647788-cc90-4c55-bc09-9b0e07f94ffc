import React from 'react';
import type { FeaturesSectionProps, IconProps } from './types';
import { BlockContainer } from '../components/BlockContainer';
import { IconRenderer } from '../components/IconRenderer';

interface Feature {
  id: string;
  name?: string;
  feature?: string;
  description: string;
  icon?: React.ComponentType<IconProps>;
}

const FeatureItem: React.FC<Feature & { sid: string; customize?: any; index: number; variants: any }> = ({ 
  sid, id, name, feature, description, index, customize, variants
}) => (
  <li className="rounded-2xl border border-gray-200 p-8">
    <IconRenderer name={variants?.basic?.features?.[index]?.icon ?? ''} className="h-8 w-8" />
    <h3 className="mt-6 font-semibold">
      {name ?? feature}
    </h3>
    <p className="mt-2">
      {description}
    </p>
  </li>
);

const SecondaryFeatures: React.FC<FeaturesSectionProps> = ({
  id,
  title,
  description,
  features,
  resources,
  customize,
  variants
}) => {
  return (
    <BlockContainer id={id} resources={resources} customize={customize}>
      <div className="relative w-full max-w-7xl mx-auto">
        <div className="mx-auto max-w-2xl sm:text-center">
          <h2 className="text-3xl font-medium tracking-tight">
            {title}
          </h2>
          <p className="mt-2 text-lg">
            {description}
          </p>
        </div>
        <ul
          role="list"
          className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-6 text-sm sm:mt-20 sm:grid-cols-2 md:gap-y-10 lg:max-w-none lg:grid-cols-3"
        >
          {features.map((feature: any, index) => (
            <FeatureItem
              key={feature.id}
              sid={id}
              index={index}
              {...feature}
              customize={customize}
              variants={variants}
            />
          ))}
        </ul>
      </div>
    </BlockContainer>
  );
};

export default React.memo(SecondaryFeatures);