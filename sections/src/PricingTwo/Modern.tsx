import React, { useState, useEffect } from 'react';
import { CheckIcon, SparklesIcon } from '@heroicons/react/20/solid';
import PriceDisplay, { formatPrice } from './PriceDisplay';
import { PricingTwoSectionProps, Tier, Frequency } from './types';
import { Button } from '../components/ButtonV2';

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ');
}

export default function Modern({
  id,
  tagline = 'Pricing',
  title = 'Choose the right plan for you',
  description = 'Choose an affordable plan that\'s packed with the best features for engaging your audience, creating customer loyalty, and driving sales.',
  frequencies,
  tiers = [],
  discounted
}: PricingTwoSectionProps) {
  // 默认频率为 monthly
  const [selectedFrequency, setSelectedFrequency] = useState<string>('monthly');
  
  // 根据 billingPeriod 设置默认频率
  useEffect(() => {
    if (!frequencies?.enabled && frequencies?.billingPeriod) {
      const periodType = frequencies.billingPeriod.type;
      if (periodType === 'monthly') {
        setSelectedFrequency('monthly');
      } else if (periodType === 'yearly') {
        setSelectedFrequency('annually');
      }
    }
  }, [frequencies]);
  
  // 获取频率选项
  const frequencyOptions: Frequency[] = frequencies?.enabled && frequencies.options 
    ? frequencies.options 
    : [{ value: 'monthly', label: 'Monthly', priceSuffix: '/mo' }];
  
  // 获取当前频率的后缀
  const getCurrentSuffix = () => {
    // 如果频率选择器关闭但设置了 billingPeriod
    if (!frequencies?.enabled && frequencies?.billingPeriod) {
      const periodType = frequencies.billingPeriod.type;
      if (periodType === 'one-time') return '';
      if (periodType === 'monthly') return '/mo';
      if (periodType === 'yearly') return '/yr';
      if (periodType === 'per-user') return '/user';
      if (periodType === 'custom' && frequencies.billingPeriod.customText) {
        return frequencies.billingPeriod.customText;
      }
    }
    
    const option = frequencyOptions.find(opt => opt.value === selectedFrequency);
    return option?.priceSuffix || '/mo';
  };
  
  // 获取价格显示
  const getPriceDisplay = (tier: Tier): number => {
    // 如果频率选择器关闭
    if (!frequencies?.enabled) {
      // 优先使用 amount 字段
      if ('amount' in tier.price && typeof tier.price.amount === 'number') {
        return tier.price.amount;
      }
      
      // 单一价格结构
      if ('value' in tier.price) {
        return tier.price.value;
      }
      
      // 如果没有 amount 字段，但有多频率结构
      if ('monthly' in tier.price) {
        return tier.price.monthly;
      }
      
      return 0;
    }
    
    // 频率选择器开启 - 原有逻辑
    if ('monthly' in tier.price) {
      if (selectedFrequency === 'monthly') {
        return tier.price.monthly;
      } else if ('annually' in tier.price) {
        return tier.price.annually;
      }
    }
    
    // 单一价格结构
    if ('value' in tier.price) {
      return tier.price.value;
    }
    
    // 默认返回0
    return 0;
  };
  
  // 获取币种
  const getCurrency = (tier: Tier): string => {
    if ('currency' in tier.price) {
      return tier.price.currency;
    }
    return 'USD';
  };

  // 计算年度节省金额
  const calculateAnnualSavings = (tier: Tier): string | null => {
    if ('monthly' in tier.price && 'annually' in tier.price && selectedFrequency === 'annually') {
      const monthlyCost = tier.price.monthly;
      const annualCost = tier.price.annually;
      const savings = (monthlyCost * 12) - annualCost;
      
      if (savings > 0) {
        return formatPrice(savings, getCurrency(tier), '');
      }
    }
    return null;
  };

  return (
    <section 
      className="pricing-modern relative isolate bg-background px-container-x py-section-y" 
      aria-labelledby="pricing-title"
      style={{
        // Modern风格的按钮样式覆盖
        '--button-radius': '0.375rem',
        '--button-hover-scale': '1.02',
        '--button-transition-duration-normal': '250ms',
        '--button-hover-shadow': '0 4px 12px rgba(0, 0, 0, 0.1)',
      } as React.CSSProperties}
    >
      {/* 背景装饰 */}
      <div className="absolute inset-0 -z-10 bg-[radial-gradient(45rem_50rem_at_top,theme(colors.primary.100),theme(colors.background))] dark:bg-[radial-gradient(45rem_50rem_at_top,theme(colors.primary.900),theme(colors.background))] opacity-20" />
      <div className="absolute inset-y-0 right-1/2 -z-10 mr-16 w-[200%] origin-bottom-left skew-x-[-30deg] bg-background shadow-xl shadow-primary/10 ring-1 ring-primary/10 dark:ring-primary/5 sm:mr-28 lg:mr-0 xl:mr-16 xl:origin-center" />
      
      <div className="mx-auto max-w-content text-center">
        {tagline && (
          <p className="inline-flex items-center rounded-full bg-primary/10 dark:bg-primary/5 px-element-x py-1.5 text-body-small/body-small font-medium text-primary dark:text-primary-300 ring-1 ring-inset ring-primary/20 dark:ring-primary/10">
            {tagline}
          </p>
        )}
        <h2 
          id="pricing-title"
          className="mt-element-y text-balance text-heading-2/heading-2 font-medium tracking-tight text-foreground dark:text-primary-foreground"
        >
          {title}
        </h2>
      </div>
      <p className="mx-auto mt-element-y max-w-content text-pretty text-center text-body-large/body-large text-muted-foreground dark:text-muted-foreground/90">
        {description}
      </p>
      
      {/* 频率选择开关 */}
      {frequencies?.enabled && frequencyOptions.length > 1 && (
        <div className="mx-auto mt-content-y flex max-w-lg justify-center">
          <div className="flex rounded-full bg-card dark:bg-card/80 p-1 shadow-sm ring-1 ring-inset ring-border dark:ring-border/50">
            {frequencyOptions.map((option) => (
              <button
                key={option.value}
                type="button"
                className={classNames(
                  option.value === selectedFrequency
                    ? 'bg-primary text-primary-foreground shadow-sm'
                    : 'text-muted-foreground hover:text-primary',
                  'relative rounded-full px-element-x py-element-y text-body-small/body-small font-semibold transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2'
                )}
                onClick={() => setSelectedFrequency(option.value)}
              >
                {option.value === 'annually' && option.value === selectedFrequency && (
                  <span className="absolute -top-3 right-0 flex items-center justify-center rounded-full bg-green-100 px-2 py-0.5 text-body-small/body-small font-medium text-green-800 ring-1 ring-inset ring-green-600/20">
                    Save 16%
                  </span>
                )}
                {option.label}
              </button>
            ))}
          </div>
        </div>
      )}
      
      <div 
        className="mx-auto mt-content-y sm:mt-section-y grid max-w-lg grid-cols-1 gap-element-y lg:max-w-content lg:grid-cols-2 lg:gap-x-element-x"
        aria-label="Pricing plans"
      >
        {tiers.map((tier, tierIdx) => (
          <div
            key={`tier-${tierIdx}`}
            className={classNames(
              'relative rounded-2xl p-element-x py-element-y sm:p-content-y transition-all duration-300',
              tier.mostPopular 
                ? 'bg-gradient-to-br from-indigo-500 to-purple-600 dark:from-indigo-600 dark:to-purple-700 text-white shadow-xl ring-1 ring-indigo-500/50 dark:ring-indigo-400/30 hover:shadow-indigo-500/20' 
                : 'bg-white dark:bg-card/90 text-gray-900 dark:text-foreground ring-1 ring-gray-200 dark:ring-primary-800/30 hover:ring-gray-300 dark:hover:ring-primary-700/40 hover:shadow-lg dark:shadow-primary-900/10'
            )}
          >
            {tier.mostPopular && (
              <div className="absolute -top-3.5 left-1/2 -translate-x-1/2 transform">
                <span className="inline-flex items-center gap-1 rounded-full bg-gradient-to-r from-amber-400 to-orange-500 px-element-x py-1 text-body-small/body-small font-medium text-white shadow-md">
                  <SparklesIcon className="h-3.5 w-3.5" aria-hidden="true" />
                  <span className="relative top-px">Most popular</span>
                </span>
              </div>
            )}
            <h3
              id={`tier-${tierIdx}`}
              className={classNames(
                tier.mostPopular ? 'text-white' : 'text-indigo-600 dark:text-primary-400', 
                'text-body-large/body-large font-semibold'
              )}
            >
              {tier.name}
            </h3>
            <div className={classNames(
              tier.mostPopular ? 'text-white' : 'text-gray-900 dark:text-foreground/90',
              'mt-element-y flex items-baseline gap-x-2'
            )}>
              <span className="text-heading-1/heading-1 font-bold tracking-tight">
                {formatPrice(getPriceDisplay(tier), getCurrency(tier), '')}
              </span>
              <span className={tier.mostPopular ? 'text-indigo-200' : 'text-gray-500 dark:text-primary-300/70'}>
                {getCurrentSuffix()}
              </span>
            </div>
            
            {/* 年度节省提示 */}
            {calculateAnnualSavings(tier) && (
              <p className={classNames(
                tier.mostPopular ? 'text-indigo-200' : 'text-green-600 dark:text-green-400',
                'mt-element-y text-body-small/body-small font-medium'
              )}>
                Save {calculateAnnualSavings(tier)} per year
              </p>
            )}
            
            <p className={classNames(
              tier.mostPopular ? 'text-indigo-200' : 'text-gray-600 dark:text-muted-foreground/90', 
              'mt-element-y text-body-base/body-base'
            )}>
              {tier.description}
            </p>
            <div className="mt-content-y mb-content-y">
              <Button
                href={tier.button.url}
                variant={tier.mostPopular ? "secondary" : "primary"}
                className="w-full"
                aria-describedby={`tier-${tierIdx}`}
                aria-label={`Subscribe to the ${tier.name} plan`}
                style={tier.mostPopular ? {
                  backgroundColor: '#ffffff',
                  color: '#4f46e5', // indigo-600
                  border: '2px solid #ffffff',
                  fontWeight: '600',
                  '--button-hover-bg': '#f8fafc',
                  '--button-hover-shadow': '0 8px 25px rgba(79, 70, 229, 0.3)',
                  boxShadow: '0 4px 14px rgba(255, 255, 255, 0.3)'
                } as React.CSSProperties : undefined}
              >
                {tier.button.label}
              </Button>
            </div>
            <ul
              role="list"
              className={classNames(
                tier.mostPopular ? 'text-indigo-200' : 'text-gray-600 dark:text-muted-foreground/90',
                'space-y-element-y text-body-small/body-small',
              )}
              aria-label={`Features included in the ${tier.name} plan`}
            >
              {tier.features.map((feature, featureIdx) => (
                <li key={`feature-${tierIdx}-${featureIdx}`} className="flex gap-x-element-x">
                  <CheckIcon
                    aria-hidden="true"
                    className={classNames(
                      tier.mostPopular ? 'text-indigo-200' : 'text-indigo-600 dark:text-primary-400', 
                      'h-6 w-5 flex-none'
                    )}
                  />
                  {feature}
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>
      
      {/* Discounted 区域 */}
      {discounted?.enabled && (
        <div className="mx-auto mt-content-y sm:mt-section-y max-w-lg lg:max-w-content">
          <div className="flex flex-col items-start gap-x-content-y gap-y-element-y rounded-3xl bg-gradient-to-r from-indigo-50 to-indigo-100 dark:from-primary-950/30 dark:to-primary-900/20 p-element-x py-element-y sm:p-content-y ring-1 ring-indigo-100 dark:ring-primary-800/20 lg:flex-row lg:items-center backdrop-blur-sm">
            <div className="lg:min-w-0 lg:flex-1">
              <h3 className="text-body-large/body-large font-semibold tracking-tight text-indigo-600 dark:text-primary-300">
                {discounted.title || 'Discounted'}
              </h3>
              <p className="mt-element-y text-body-base/body-base text-gray-600 dark:text-muted-foreground">
                {discounted.description || 'Get a discounted license for your team.'}
              </p>
            </div>
            {discounted.button && (
              <Button
                href={discounted.button.url}
                variant="primary"
                className="whitespace-nowrap"
                style={{
                  '--button-radius': '9999px', // rounded-full
                  backgroundColor: '#4f46e5', // indigo-600
                  '--button-hover-opacity': '0.9'
                } as React.CSSProperties}
              >
                {discounted.button.label} <span aria-hidden="true">→</span>
              </Button>
            )}
          </div>
        </div>
      )}
    </section>
  );
}
