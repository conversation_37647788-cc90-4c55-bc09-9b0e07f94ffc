import React from 'react';
import { Currency } from './types';

// 支持的货币代码
export type CurrencyCode = 'USD' | 'EUR' | 'GBP' | 'JPY' | 'CNY' | 'CAD' | 'AUD' | 'NONE';

// 货币映射表
export const currencyMap: Record<CurrencyCode, Currency> = {
  USD: { symbol: "$", code: "USD" },
  EUR: { symbol: "€", code: "EUR" },
  GBP: { symbol: "£", code: "GBP" },
  JPY: { symbol: "¥", code: "JPY" },
  CNY: { symbol: "¥", code: "CNY" },
  CAD: { symbol: "C$", code: "CAD" },
  AUD: { symbol: "A$", code: "AUD" },
  NONE: { symbol: "", code: "" }
};

// 格式化价格
export const formatPrice = (price: number, currency: string = 'USD', suffix: string = ''): string => {
  const currencyObj = currencyMap[currency as CurrencyCode] || currencyMap.USD;
  
  // 处理不同货币的格式
  let formattedPrice = '';
  
  // 日元不使用小数点
  if (currency === 'JPY') {
    formattedPrice = Math.round(price).toString();
  } else {
    // 其他货币保留两位小数
    formattedPrice = price.toFixed(2);
    // 如果小数部分为 .00，则移除
    if (formattedPrice.endsWith('.00')) {
      formattedPrice = formattedPrice.slice(0, -3);
    }
  }
  
  return `${currencyObj.symbol}${formattedPrice}${suffix}`;
};

interface PriceDisplayProps {
  price: number;
  currency?: string;
  suffix?: string;
  featured?: boolean;
}

const PriceDisplay: React.FC<PriceDisplayProps> = ({ 
  price, 
  currency = 'USD', 
  suffix = '/mo',
  featured = false
}) => {
  return (
    <p className="mt-element-y flex items-baseline gap-x-element-x">
      <span
        className={`text-heading-1/heading-1 font-semibold tracking-tight ${featured ? 'text-primary-foreground' : 'text-foreground'}`}
      >
        {formatPrice(price, currency, '')}
      </span>
      <span className={`text-body-base/body-base ${featured ? 'text-primary-foreground/70' : 'text-muted-foreground'}`}>{suffix}</span>
    </p>
  );
};

export default PriceDisplay;
