import React, { useState, useEffect } from 'react';
import { CheckIcon } from '@heroicons/react/20/solid';
import PriceDisplay, { formatPrice } from './PriceDisplay';
import { PricingTwoSectionProps, Tier, Frequency } from './types';
import { Button } from '../components/ButtonV2';

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ');
}

export default function Basic({
  id,
  tagline = 'Pricing',
  title = 'Choose the right plan for you',
  description = 'Choose an affordable plan that\'s packed with the best features for engaging your audience, creating customer loyalty, and driving sales.',
  frequencies,
  tiers = [],
  discounted
}: PricingTwoSectionProps) {
  // 默认频率为 monthly
  const [selectedFrequency, setSelectedFrequency] = useState<string>('monthly');
  
  // 根据 billingPeriod 设置默认频率
  useEffect(() => {
    if (!frequencies?.enabled && frequencies?.billingPeriod) {
      const periodType = frequencies.billingPeriod.type;
      if (periodType === 'monthly') {
        setSelectedFrequency('monthly');
      } else if (periodType === 'yearly') {
        setSelectedFrequency('annually');
      }
    }
  }, [frequencies]);
  
  // 获取频率选项
  const frequencyOptions: Frequency[] = frequencies?.enabled && frequencies.options 
    ? frequencies.options 
    : [{ value: 'monthly', label: 'Monthly', priceSuffix: '/mo' }];
  
  // 获取当前频率的后缀
  const getCurrentSuffix = () => {
    // 如果频率选择器关闭但设置了 billingPeriod
    if (!frequencies?.enabled && frequencies?.billingPeriod) {
      const periodType = frequencies.billingPeriod.type;
      if (periodType === 'one-time') return '';
      if (periodType === 'monthly') return '/mo';
      if (periodType === 'yearly') return '/yr';
      if (periodType === 'per-user') return '/user';
      if (periodType === 'custom' && frequencies.billingPeriod.customText) {
        return frequencies.billingPeriod.customText;
      }
    }
    
    const option = frequencyOptions.find(opt => opt.value === selectedFrequency);
    return option?.priceSuffix || '/mo';
  };
  
  // 获取价格显示
  const getPriceDisplay = (tier: Tier): number => {
    // 如果频率选择器关闭
    if (!frequencies?.enabled) {
      // 优先使用 amount 字段
      if ('amount' in tier.price && typeof tier.price.amount === 'number') {
        return tier.price.amount;
      }
      
      // 单一价格结构
      if ('value' in tier.price) {
        return tier.price.value;
      }
      
      // 如果没有 amount 字段，但有多频率结构
      if ('monthly' in tier.price) {
        return tier.price.monthly;
      }
      
      return 0;
    }
    
    // 频率选择器开启 - 原有逻辑
    if ('monthly' in tier.price) {
      if (selectedFrequency === 'monthly') {
        return tier.price.monthly;
      } else if ('annually' in tier.price) {
        return tier.price.annually;
      }
    }
    
    // 单一价格结构
    if ('value' in tier.price) {
      return tier.price.value;
    }
    
    // 默认返回0
    return 0;
  };
  
  // 获取币种
  const getCurrency = (tier: Tier): string => {
    if ('currency' in tier.price) {
      return tier.price.currency;
    }
    return 'USD';
  };

  return (
    <section 
      className="pricing-basic relative isolate bg-gradient-to-b from-background to-muted/50 px-container-x py-section-y" 
      aria-labelledby="pricing-title"
      style={{
        // Basic风格的按钮样式覆盖
        '--button-shadow': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
        '--button-hover-shadow': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
      } as React.CSSProperties}
    >
      {/* 装饰性背景元素 */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <svg
          className="absolute left-[max(50%,25rem)] top-0 h-[64rem] w-[128rem] -translate-x-1/2 stroke-border [mask-image:radial-gradient(64rem_64rem_at_top,white,transparent)]"
          aria-hidden="true"
        >
          <defs>
            <pattern
              id="e813992c-7d03-4cc4-a2bd-151760b470a0"
              width={200}
              height={200}
              x="50%"
              y="-1"
              patternUnits="userSpaceOnUse"
            >
              <path d="M100 200V.5M.5 .5H200" fill="none" />
            </pattern>
          </defs>
          <svg x="50%" y={-1} className="overflow-visible fill-muted/50">
            <path
              d="M-100.5 0h201v201h-201Z M699.5 0h201v201h-201Z M499.5 400h201v201h-201Z M-300.5 600h201v201h-201Z"
              strokeWidth={0}
            />
          </svg>
          <rect width="100%" height="100%" strokeWidth={0} fill="url(#e813992c-7d03-4cc4-a2bd-151760b470a0)" />
        </svg>
      </div>
      
      <div className="mx-auto max-w-content text-center">
        {tagline && (
          <p className="text-body-base/body-base font-semibold text-primary">{tagline}</p>
        )}
        <h2 
          id="pricing-title"
          className="mt-element-y text-balance text-heading-2/heading-2 font-medium tracking-tight text-foreground"
        >
          {title}
        </h2>
      </div>
      <p className="mx-auto mt-element-y max-w-content text-pretty text-center text-body-large/body-large text-muted-foreground">
        {description}
      </p>
      
      {/* 频率选择开关 */}
      {frequencies?.enabled && frequencyOptions.length > 1 && (
        <div className="mx-auto mt-content-y flex max-w-lg justify-center">
          <div className="flex rounded-full bg-primary/10 p-1">
            {frequencyOptions.map((option) => (
              <button
                key={option.value}
                type="button"
                className={classNames(
                  option.value === selectedFrequency
                    ? 'bg-primary text-primary-foreground shadow-sm'
                    : 'text-primary hover:text-primary/90',
                  'rounded-full px-element-x py-2 text-body-small/body-small font-semibold focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2'
                )}
                onClick={() => setSelectedFrequency(option.value)}
              >
                {option.label}
              </button>
            ))}
          </div>
        </div>
      )}
      
      <div 
        className="mx-auto mt-content-y sm:mt-section-y grid max-w-lg grid-cols-1 gap-element-y sm:gap-element-x lg:max-w-content lg:grid-cols-2"
        aria-label="Pricing plans"
      >
        {tiers.map((tier, tierIdx) => (
          <div
            key={`tier-${tierIdx}`}
            className={classNames(
              tier.mostPopular 
                ? 'relative border-2 border-primary bg-card shadow-xl' 
                : 'border border-border bg-card shadow',
              'rounded-2xl p-element-x py-element-y sm:p-content-y',
            )}
          >
            {tier.mostPopular && (
              <div className="absolute -top-3.5 left-1/2 -translate-x-1/2 transform">
                <span className="inline-flex items-center rounded-full bg-primary px-element-x py-1 text-body-small/body-small font-semibold text-primary-foreground shadow-md">
                  <span className="relative top-px">Most popular</span>
                </span>
              </div>
            )}
            <h3 
              id={`tier-${tierIdx}`} 
              className="text-body-base/body-base font-semibold text-foreground"
            >
              {tier.name}
            </h3>
            <PriceDisplay 
              price={getPriceDisplay(tier)}
              currency={getCurrency(tier)}
              suffix={getCurrentSuffix()}
              featured={false}
            />
            <p className="mt-element-y text-body-base/body-base text-muted-foreground">{tier.description}</p>
            <div className="mt-content-y mb-content-y">
              <Button
                href={tier.button.url}
                variant={tier.mostPopular ? "primary" : "outline"}
                className="w-full"
                aria-describedby={`tier-${tierIdx}`}
                aria-label={`Subscribe to the ${tier.name} plan`}
                style={!tier.mostPopular ? {
                  backgroundColor: 'hsl(var(--card))',
                  color: 'hsl(var(--foreground))',
                  '--button-border-color': 'hsl(var(--border))',
                  '--button-hover-bg': 'hsl(var(--muted) / 0.5)'
                } as React.CSSProperties : undefined}
              >
                {tier.button.label}
              </Button>
            </div>
            <ul 
              role="list" 
              className="space-y-element-y text-body-small/body-small text-muted-foreground"
              aria-label={`Features included in the ${tier.name} plan`}
            >
              {tier.features.map((feature, featureIdx) => (
                <li key={`feature-${tierIdx}-${featureIdx}`} className="flex gap-x-element-x">
                  <CheckIcon aria-hidden="true" className="h-6 w-5 flex-none text-primary" />
                  {feature}
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>
      
      {/* Discounted 区域 */}
      {discounted?.enabled && (
        <div className="mx-auto mt-content-y sm:mt-section-y max-w-lg lg:max-w-content">
          <div className="flex flex-col items-start gap-x-content-y gap-y-element-y rounded-2xl bg-primary/10 p-element-x py-element-y sm:p-content-y lg:flex-row lg:items-center">
            <div className="lg:min-w-0 lg:flex-1">
              <h3 className="text-body-base/body-base font-semibold text-primary">{discounted.title || 'Discounted'}</h3>
              <p className="mt-element-y text-body-base/body-base text-muted-foreground">
                {discounted.description || 'Get a discounted license for your team.'}
              </p>
            </div>
            {discounted.button && (
              <Button
                href={discounted.button.url}
                variant="secondary"
                className="whitespace-nowrap"
                style={{
                  backgroundColor: 'hsl(var(--background))',
                  color: 'hsl(var(--primary))',
                  '--button-hover-bg': 'hsl(var(--primary) / 0.1)'
                } as React.CSSProperties}
              >
                {discounted.button.label} <span aria-hidden="true">&rarr;</span>
              </Button>
            )}
          </div>
        </div>
      )}
    </section>
  );
}
