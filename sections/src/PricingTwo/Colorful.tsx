'use client'

import React, { useState, useEffect } from 'react';
import { CheckIcon, StarIcon } from '@heroicons/react/20/solid';
import PriceDisplay, { formatPrice } from './PriceDisplay';
import { PricingTwoSectionProps, Tier, Frequency } from './types';
import { Button } from '../components/ButtonV2';

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ');
}

// 预定义的卡片主题
const cardThemes = [
  {
    bgGradient: 'bg-gradient-to-br from-pink-500 to-rose-500',
    textColor: 'text-white',
    secondaryTextColor: 'text-pink-100',
    ringColor: 'ring-pink-300/50',
    hoverShadow: 'hover:shadow-pink-500/20',
    checkIconColor: 'text-pink-200',
    featureBgColor: 'bg-pink-500/20',
    buttonColor: '#dc2626', // red-600
  },
  {
    bgGradient: 'bg-gradient-to-br from-indigo-500 to-purple-500',
    textColor: 'text-white',
    secondaryTextColor: 'text-indigo-100',
    ringColor: 'ring-indigo-300/50',
    hoverShadow: 'hover:shadow-indigo-500/20',
    checkIconColor: 'text-indigo-200',
    featureBgColor: 'bg-indigo-500/20',
    buttonColor: '#7c3aed', // violet-600
  },
];

export default function Colorful({
  id,
  tagline = 'Pricing',
  title = 'Choose the right plan for you',
  description = 'Choose an affordable plan that\'s packed with the best features for engaging your audience, creating customer loyalty, and driving sales.',
  frequencies,
  tiers = [],
  discounted
}: PricingTwoSectionProps) {
  // 默认频率为 monthly
  const [selectedFrequency, setSelectedFrequency] = useState<string>('monthly');
  
  // 根据 billingPeriod 设置默认频率
  useEffect(() => {
    if (!frequencies?.enabled && frequencies?.billingPeriod) {
      const periodType = frequencies.billingPeriod.type;
      if (periodType === 'monthly') {
        setSelectedFrequency('monthly');
      } else if (periodType === 'yearly') {
        setSelectedFrequency('annually');
      }
    }
  }, [frequencies]);
  
  // 获取频率选项
  const frequencyOptions: Frequency[] = frequencies?.enabled && frequencies.options 
    ? frequencies.options 
    : [{ value: 'monthly', label: 'Monthly', priceSuffix: '/month' }];
  
  const getCurrentSuffix = () => {
    // 如果频率选择器关闭但设置了 billingPeriod
    if (!frequencies?.enabled && frequencies?.billingPeriod) {
      const periodType = frequencies.billingPeriod.type;
      if (periodType === 'one-time') return '';
      if (periodType === 'monthly') return '/mo';
      if (periodType === 'yearly') return '/yr';
      if (periodType === 'per-user') return '/user';
      if (periodType === 'custom' && frequencies.billingPeriod.customText) {
        return frequencies.billingPeriod.customText;
      }
    }
    
    const option = frequencyOptions.find(opt => opt.value === selectedFrequency);
    return option?.priceSuffix || '/mo';
  };
  
  const getPriceDisplay = (tier: Tier): number => {
    // 如果频率选择器关闭，使用 billingPeriod 的价格
    if (!frequencies?.enabled && frequencies?.billingPeriod) {
      const periodType = frequencies.billingPeriod.type;
      if (periodType === 'one-time') {
        // 对于一次性付费，使用单一价格结构
        if ('value' in tier.price) {
          return tier.price.value;
        }
      }
      if (periodType === 'monthly') {
        if ('monthly' in tier.price) {
          return tier.price.monthly;
        }
      }
      if (periodType === 'yearly') {
        if ('annually' in tier.price) {
          return tier.price.annually;
        }
      }
      if (periodType === 'per-user' || periodType === 'custom') {
        // 对于按用户或自定义，使用单一价格结构
        if ('value' in tier.price) {
          return tier.price.value;
        }
      }
    }
    
    // 使用频率选择器的逻辑
    if (selectedFrequency === 'monthly' && 'monthly' in tier.price) {
      return tier.price.monthly;
    }
    if (selectedFrequency === 'annually' && 'annually' in tier.price) {
      return tier.price.annually;
    }
    if (selectedFrequency === 'one-time' && 'value' in tier.price) {
      return tier.price.value;
    }
    if (selectedFrequency === 'per-user' && 'value' in tier.price) {
      return tier.price.value;
    }
    if (selectedFrequency === 'custom' && 'value' in tier.price) {
      return tier.price.value;
    }
    
    // 默认返回 monthly 价格或单一价格
    if ('monthly' in tier.price) {
      return tier.price.monthly;
    }
    if ('value' in tier.price) {
      return tier.price.value;
    }
    
    return 0;
  };
  
  const getCurrency = (tier: Tier): string => {
    // 获取货币符号
    return tier.price.currency || '$';
  };

  const calculateAnnualSavings = (tier: Tier): string | null => {
    if (!('monthly' in tier.price && 'annually' in tier.price)) return null;
    
    const monthlyTotal = tier.price.monthly * 12;
    const annualPrice = tier.price.annually;
    const savings = monthlyTotal - annualPrice;
    
    if (savings > 0) {
      return `${getCurrency(tier)}${savings}`;
    }
    
    return null;
  };

  return (
    <section 
      className="pricing-colorful relative isolate bg-background px-container-x py-section-y" 
      aria-labelledby="pricing-title"
      style={{
        // Colorful风格的按钮样式覆盖
        '--button-radius': '9999px', // rounded-full
        '--button-shadow': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        '--button-hover-shadow': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        '--button-transition-duration-normal': '200ms',
        '--button-font-weight': '600',
      } as React.CSSProperties}
    >
      {/* 背景装饰 */}
      <div
        className="absolute inset-0 -z-10 h-full w-full opacity-30"
        style={{
          backgroundImage: 'radial-gradient(circle at 30% 20%, rgba(255, 128, 128, 0.8), transparent 35%), radial-gradient(circle at 70% 60%, rgba(128, 128, 255, 0.8), transparent 35%)',
          backgroundSize: '100% 100%',
          filter: 'blur(50px)'
        }}
        aria-hidden="true"
      />
      <div
        className="absolute inset-y-0 right-1/2 -z-10 mr-16 w-[200%] origin-bottom-left skew-x-[-30deg] bg-muted/80 shadow-xl shadow-primary/10 ring-1 ring-border/10"
        aria-hidden="true"
      />
      
      <div className="mx-auto max-w-content text-center">
        {tagline && (
          <p className="inline-flex items-center justify-center">
            <span className="relative inline-flex overflow-hidden rounded-full p-[1px] transition-all duration-300 hover:scale-105 motion-reduce:hover:scale-100">
              <span className="absolute inset-0 rounded-full bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-500 opacity-75" aria-hidden="true" />
              <span className="inline-flex h-full w-full items-center justify-center rounded-full bg-background px-element-x py-1 text-body-small/body-small font-medium text-primary backdrop-blur-3xl relative z-10">
                {tagline}
              </span>
            </span>
          </p>
        )}
        <h2 
          id="pricing-title"
          className="mt-element-y text-balance text-heading-2/heading-2 font-medium tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-500 dark:from-pink-300 dark:via-purple-300 dark:to-indigo-300"
        >
          {title}
        </h2>
      </div>
      <p className="mx-auto mt-element-y max-w-content text-pretty text-center text-body-large/body-large text-muted-foreground/90 dark:text-muted-foreground">
        {description}
      </p>
      
      {/* 频率选择开关 */}
      {frequencies?.enabled && frequencyOptions.length > 1 && (
        <div className="mx-auto mt-content-y flex max-w-lg justify-center">
          <div className="relative flex rounded-full bg-muted/80 p-1 backdrop-blur-xl ring-1 ring-border/10 transition-all duration-200 hover:ring-border/20">
            <div
              className="absolute inset-0 overflow-hidden rounded-full"
              style={{
                background: 'linear-gradient(90deg, rgba(255,128,181,0.3) 0%, rgba(144,137,252,0.3) 50%, rgba(128,255,234,0.3) 100%)',
                opacity: 0.3
              }}
            />
            {frequencyOptions.map((option) => (
              <button
                key={option.value}
                type="button"
                className={classNames(
                  option.value === selectedFrequency
                    ? 'relative bg-gradient-to-r from-indigo-500 to-purple-500 text-white shadow-lg'
                    : 'text-foreground/70 hover:text-foreground dark:text-muted-foreground dark:hover:text-primary-foreground',
                  'z-10 rounded-full px-element-x py-element-y text-body-small/body-small font-semibold transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-foreground focus:ring-offset-2 focus:ring-offset-background'
                )}
                onClick={() => setSelectedFrequency(option.value)}
              >
                {option.value === 'annually' && option.value === selectedFrequency && (
                  <span className="absolute -top-3 right-0 flex items-center justify-center rounded-full bg-success/80 px-2 py-0.5 text-body-small/body-small font-medium text-background dark:text-primary-foreground ring-1 ring-inset ring-success/30 transition-transform duration-200 hover:scale-105 motion-reduce:hover:scale-100">
                    Save 16%
                  </span>
                )}
                {option.label}
              </button>
            ))}
          </div>
        </div>
      )}
      
      <div 
        className="mx-auto mt-content-y sm:mt-section-y grid max-w-lg grid-cols-1 gap-element-y lg:max-w-content lg:grid-cols-2 lg:gap-x-element-x"
        aria-label="Pricing plans"
      >
        {tiers.map((tier, tierIdx) => {
          // 使用预定义的主题或默认主题
          const theme = cardThemes[tierIdx % cardThemes.length];
          
          return (
            <div
              key={`tier-${tierIdx}`}
              className={classNames(
                theme.bgGradient,
                theme.ringColor,
                theme.hoverShadow,
                'group relative rounded-3xl p-element-x py-element-y sm:p-content-y transform transition-all duration-300 hover:scale-[1.02] hover:shadow-2xl ring-1 motion-reduce:hover:scale-100 motion-reduce:transition-none',
                tier.mostPopular 
                  ? 'shadow-2xl ring-2 ring-yellow-400/50' 
                  : ''
              )}
            >
              {tier.mostPopular && (
                <div className="absolute -top-6 -right-6 flex h-20 w-20 items-center justify-center rounded-full bg-gradient-to-r from-yellow-400 via-amber-500 to-orange-500 text-body-small/body-small font-black uppercase text-black shadow-2xl ring-4 ring-yellow-300/50 transition-transform duration-200 group-hover:scale-105 motion-reduce:group-hover:scale-100">
                  <div className="relative">
                    <StarIcon className="h-7 w-7 text-black drop-shadow-sm" aria-hidden="true" />
                    <span className="absolute inset-0 flex items-center justify-center text-[11px] font-black drop-shadow-sm">
                      TOP
                    </span>
                  </div>
                </div>
              )}
            <h3 
              id={`tier-${tierIdx}`} 
              className={classNames(theme.textColor, 'text-body-large/body-large font-semibold')}
            >
              {tier.name}
            </h3>
            <div className={classNames(theme.textColor, 'mt-element-y flex items-baseline gap-x-element-x')}>
              <span className="text-heading-1/heading-1 font-extrabold tracking-tight">
                {formatPrice(getPriceDisplay(tier), getCurrency(tier), '')}
              </span>
              <span className={theme.secondaryTextColor}>
                {getCurrentSuffix()}
              </span>
            </div>
            
            {/* 年度节省提示 */}
            {calculateAnnualSavings(tier) && (
              <p className={classNames(
                theme.secondaryTextColor,
                'mt-element-y text-body-small/body-small font-medium'
              )}>
                Save {calculateAnnualSavings(tier)} per year
              </p>
            )}
            
            <p className={classNames(theme.secondaryTextColor, 'mt-element-y text-body-base/body-base')}>
              {tier.description}
            </p>
            
            {/* 使用ButtonV2重构主要按钮 */}
            <Button
              href={tier.button.url}
              variant="secondary"
              className="mt-content-y w-full transition-transform duration-200 hover:scale-[1.02] motion-reduce:hover:scale-100 motion-reduce:transition-none"
              aria-describedby={`tier-${tierIdx}`}
              aria-label={`Subscribe to the ${tier.name} plan`}
              style={{
                backgroundColor: 'rgba(255, 255, 255, 0.9)',
                color: theme.buttonColor,
                '--button-hover-bg': 'white',
                backdropFilter: 'blur(4px)',
                border: 'none'
              } as React.CSSProperties}
            >
              {tier.button.label}
            </Button>
            
            <ul 
              role="list" 
                className={classNames(
                  theme.secondaryTextColor,
                  theme.featureBgColor,
                  'mt-content-y space-y-element-y text-body-small/body-small rounded-2xl p-element-x py-element-y'
                )}
            >
              {tier.features.map((feature, featureIdx) => (
                  <li key={`feature-${tierIdx}-${featureIdx}`} className="flex gap-x-element-x items-center">
                    <CheckIcon
                      aria-hidden="true"
                      className={classNames(theme.checkIconColor, 'h-5 w-5 flex-none')}
                    />
                    <span>{feature}</span>
                </li>
              ))}
            </ul>
          </div>
        );
      })}
    </div>
    
    {/* Discounted 区域 - 保留复杂渐变效果 */}
      {discounted?.enabled && (
        <div className="mx-auto mt-content-y sm:mt-section-y max-w-lg lg:max-w-content">
          <div className="relative overflow-hidden rounded-3xl bg-gray-800/80 p-element-x py-element-y sm:p-content-y shadow-lg backdrop-blur-sm ring-1 ring-white/10 transition-all duration-300 hover:shadow-xl hover:ring-white/20">
            <div
              className="absolute inset-0 overflow-hidden"
              style={{
                background: 'linear-gradient(90deg, rgba(255,128,181,0.1) 0%, rgba(144,137,252,0.1) 50%, rgba(128,255,234,0.1) 100%)',
              }}
            />
            <div className="relative flex flex-col items-start gap-element-y lg:flex-row lg:items-center lg:justify-between lg:gap-x-element-x">
              <div>
                <h3 className="text-body-large/body-large font-bold bg-clip-text text-transparent bg-gradient-to-r from-pink-300 to-purple-300">
                  {discounted.title || 'Discounted'}
                </h3>
                <p className="mt-element-y text-body-base/body-base text-gray-300">
                  {discounted.description || 'Get a discounted license for your team.'}
                </p>
              </div>
              {discounted.button && (
                <a
                  href={discounted.button.url}
                  className="group relative inline-flex items-center justify-center overflow-hidden rounded-full bg-gradient-to-r from-purple-500 to-indigo-500 p-[1px] font-medium text-white hover:from-purple-600 hover:to-indigo-600 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:ring-offset-2 focus:ring-offset-gray-900 transition-all duration-200 cursor-pointer hover:scale-[1.02] motion-reduce:hover:scale-100"
                >
                  <span className="relative rounded-full bg-gray-900 px-element-x py-element-y text-body-small/body-small transition-all duration-200 ease-in-out group-hover:bg-opacity-0">
                    {discounted.button.label} <span aria-hidden="true">→</span>
                  </span>
                </a>
              )}
            </div>
          </div>
        </div>
      )}
    </section>
  );
}
