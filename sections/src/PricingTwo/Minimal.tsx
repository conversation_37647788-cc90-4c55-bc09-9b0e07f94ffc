import React, { useState, useEffect } from 'react';
import { CheckIcon, ArrowRightIcon } from '@heroicons/react/20/solid';
import PriceDisplay, { formatPrice } from './PriceDisplay';
import { PricingTwoSectionProps, Tier, Frequency } from './types';
import { Button } from '../components/ButtonV2';

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ');
}

export default function Minimal({
  id,
  tagline = 'Pricing',
  title = 'Choose the right plan for you',
  description = 'Choose an affordable plan that\'s packed with the best features for engaging your audience, creating customer loyalty, and driving sales.',
  frequencies,
  tiers = [],
  discounted
}: PricingTwoSectionProps) {
  // 默认频率为 monthly
  const [selectedFrequency, setSelectedFrequency] = useState<string>('monthly');
  
  // 根据 billingPeriod 设置默认频率
  useEffect(() => {
    if (!frequencies?.enabled && frequencies?.billingPeriod) {
      const periodType = frequencies.billingPeriod.type;
      if (periodType === 'monthly') {
        setSelectedFrequency('monthly');
      } else if (periodType === 'yearly') {
        setSelectedFrequency('annually');
      }
    }
  }, [frequencies]);
  
  // 获取频率选项
  const frequencyOptions: Frequency[] = frequencies?.enabled && frequencies.options 
    ? frequencies.options 
    : [{ value: 'monthly', label: 'Monthly', priceSuffix: '/mo' }];
  
  // 获取当前频率的后缀
  const getCurrentSuffix = () => {
    // 如果频率选择器关闭但设置了 billingPeriod
    if (!frequencies?.enabled && frequencies?.billingPeriod) {
      const periodType = frequencies.billingPeriod.type;
      if (periodType === 'one-time') return '';
      if (periodType === 'monthly') return '/mo';
      if (periodType === 'yearly') return '/yr';
      if (periodType === 'per-user') return '/user';
      if (periodType === 'custom' && frequencies.billingPeriod.customText) {
        return frequencies.billingPeriod.customText;
      }
    }
    
    const option = frequencyOptions.find(opt => opt.value === selectedFrequency);
    return option?.priceSuffix || '/mo';
  };
  
  // 获取价格显示
  const getPriceDisplay = (tier: Tier): number => {
    // 如果频率选择器关闭
    if (!frequencies?.enabled) {
      // 优先使用 amount 字段
      if ('amount' in tier.price && typeof tier.price.amount === 'number') {
        return tier.price.amount;
      }
      
      // 单一价格结构
      if ('value' in tier.price) {
        return tier.price.value;
      }
      
      // 如果没有 amount 字段，但有多频率结构
      if ('monthly' in tier.price) {
        return tier.price.monthly;
      }
      
      return 0;
    }
    
    // 频率选择器开启 - 原有逻辑
    if ('monthly' in tier.price) {
      if (selectedFrequency === 'monthly') {
        return tier.price.monthly;
      } else if ('annually' in tier.price) {
        return tier.price.annually;
      }
    }
    
    // 单一价格结构
    if ('value' in tier.price) {
      return tier.price.value;
    }
    
    // 默认返回0
    return 0;
  };
  
  // 获取币种
  const getCurrency = (tier: Tier): string => {
    if ('currency' in tier.price) {
      return tier.price.currency;
    }
    return 'USD';
  };

  // 计算年度节省百分比
  const calculateAnnualSavingsPercent = (tier: Tier): number | null => {
    if ('monthly' in tier.price && 'annually' in tier.price && selectedFrequency === 'annually') {
      const monthlyCost = tier.price.monthly * 12;
      const annualCost = tier.price.annually;
      
      if (monthlyCost > 0) {
        return Math.round((1 - (annualCost / monthlyCost)) * 100);
      }
    }
    return null;
  };

  return (
    <section 
      className="pricing-minimal relative isolate bg-background px-container-x py-section-y" 
      aria-labelledby="pricing-title"
      style={{
        // Minimal风格的按钮样式覆盖
        '--button-font-weight': '500',
        '--button-text-transform': 'none',
        '--button-letter-spacing': 'normal',
        '--button-transition-duration-normal': '200ms',
        // 增加顶部和底部的padding
        paddingTop: 'calc(var(--section-spacing-y, 4rem) * 1.5)',
        paddingBottom: 'calc(var(--section-spacing-y, 4rem) * 1.5)',
      } as React.CSSProperties}
    >
      <div className="mx-auto max-w-content text-center pb-content-y">
        {tagline && (
          <p className="text-body-small/body-small font-medium uppercase tracking-wider text-muted-foreground">{tagline}</p>
        )}
        <h2 
          id="pricing-title" 
          className="mt-element-y text-balance text-heading-2/heading-2 font-light tracking-tight text-foreground"
        >
          {title}
        </h2>
      </div>
      <p className="mx-auto mt-element-y max-w-content text-pretty text-center text-body-base/body-base text-muted-foreground pb-element-y">
        {description}
      </p>
      
      {/* 频率选择开关 */}
      {frequencies?.enabled && frequencyOptions.length > 1 && (
        <div className="mx-auto mt-content-y flex max-w-lg justify-center">
          <div className="relative inline-flex items-center rounded-full border border-border p-0.5 text-body-small/body-small">
            {frequencyOptions.map((option, index) => (
              <button
                key={option.value}
                type="button"
                className={classNames(
                  option.value === selectedFrequency
                    ? 'bg-foreground text-background'
                    : 'text-muted-foreground hover:text-foreground',
                  'relative rounded-full px-element-x py-element-y font-medium transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-foreground focus:ring-offset-2'
                )}
                onClick={() => setSelectedFrequency(option.value)}
              >
                {option.value === 'annually' && (
                  <span className={classNames(
                    option.value === selectedFrequency ? 'bg-success/20 text-success' : 'bg-muted text-muted-foreground',
                    'absolute -top-2 -right-2 rounded-full px-2 py-0.5 text-body-small/body-small font-medium'
                  )}>
                    Save
                  </span>
                )}
                {option.label}
              </button>
            ))}
          </div>
        </div>
      )}
      
      <div 
        className="mx-auto mt-content-y sm:mt-section-y grid max-w-lg grid-cols-1 items-start gap-element-y lg:max-w-content lg:grid-cols-2 lg:gap-x-element-x"
        aria-label="Pricing plans"
      >
        {tiers.map((tier, tierIdx) => {
          const savingsPercent = calculateAnnualSavingsPercent(tier);
          
          return (
            <div
              key={`tier-${tierIdx}`}
              className={classNames(
                'relative rounded-xl bg-card p-element-x py-element-y sm:p-content-y transition-all duration-200',
                tier.mostPopular 
                  ? 'ring-2 ring-foreground hover:shadow-lg' 
                  : 'ring-1 ring-border hover:ring-border/80'
              )}
            >
              {tier.mostPopular && (
                <div className="absolute -top-3.5 left-1/2 -translate-x-1/2 transform">
                  <span className="inline-flex items-center rounded-full bg-foreground px-element-x py-1 text-body-small/body-small font-medium text-background">
                    <span className="relative top-px">Most popular</span>
                  </span>
                </div>
              )}
              <h3
                id={`tier-${tierIdx}`}
                className="text-body-small/body-small font-medium uppercase tracking-wide text-foreground"
              >
                {tier.name}
              </h3>
              <div className="mt-element-y flex items-baseline">
                <span className="text-heading-2/heading-2 font-light tracking-tight text-foreground">
                  {formatPrice(getPriceDisplay(tier), getCurrency(tier), '')}
                </span>
                <span className="ml-1 text-body-large/body-large font-light text-muted-foreground">
                  {getCurrentSuffix()}
                </span>
              </div>
              
              {/* 年度节省提示 */}
              {savingsPercent && (
                <p className="mt-element-y text-body-small/body-small font-medium text-success">
                  Save {savingsPercent}% with annual billing
                </p>
              )}
              
              <p className="mt-element-y text-body-small/body-small text-muted-foreground min-h-[3rem]">
                {tier.description}
              </p>
              
              <Button
                href={tier.button.url}
                variant={tier.mostPopular ? "primary" : "outline"}
                className="mt-content-y w-full"
                aria-describedby={`tier-${tierIdx}`}
                aria-label={`Subscribe to the ${tier.name} plan`}
                style={tier.mostPopular ? {
                  backgroundColor: 'hsl(var(--foreground))',
                  color: 'hsl(var(--background))',
                  '--button-hover-opacity': '0.9'
                } as React.CSSProperties : {
                  backgroundColor: 'hsl(var(--card))',
                  color: 'hsl(var(--foreground))',
                  '--button-border-color': 'hsl(var(--border))',
                  '--button-hover-bg': 'hsl(var(--muted) / 0.5)'
                } as React.CSSProperties}
              >
                {tier.button.label}
              </Button>
              
              <ul
                role="list"
                className="mt-content-y space-y-element-y text-body-small/body-small text-muted-foreground border-t border-border pt-content-y"
                aria-label={`Features included in the ${tier.name} plan`}
              >
                {tier.features.map((feature, featureIdx) => (
                  <li key={`feature-${tierIdx}-${featureIdx}`} className="flex items-start">
                    <CheckIcon
                      className={classNames(
                        tier.mostPopular ? 'text-foreground' : 'text-muted-foreground',
                        'h-5 w-5 flex-shrink-0'
                      )}
                      aria-hidden="true"
                    />
                    <span className="ml-element-x">{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
          );
        })}
      </div>
      
      {/* Discounted 区域 */}
      {discounted?.enabled && (
        <div className="mx-auto mt-section-y max-w-lg border-t border-border pt-content-y pb-content-y lg:max-w-content">
          <div className="flex flex-col items-start gap-element-y rounded-xl bg-muted p-content-y lg:flex-row lg:items-center lg:justify-between lg:gap-x-element-x">
            <div>
              <h3 className="text-body-small/body-small font-medium uppercase tracking-wide text-foreground">
                {discounted.title || 'Discounted'}
              </h3>
              <p className="mt-element-y text-body-small/body-small text-muted-foreground">
                {discounted.description || 'Get a discounted license for your team.'}
              </p>
            </div>
            {discounted.button && (
              <Button
                href={discounted.button.url}
                variant="outline"
                className="group whitespace-nowrap"
                aria-label="Get discounted pricing"
                style={{
                  backgroundColor: 'hsl(var(--card))',
                  '--button-border-color': 'hsl(var(--border))',
                  '--button-hover-bg': 'hsl(var(--muted) / 0.5)'
                } as React.CSSProperties}
              >
                {discounted.button.label}
                <ArrowRightIcon className="ml-element-x h-4 w-4 text-muted-foreground group-hover:text-foreground transition-transform duration-200 group-hover:translate-x-1" aria-hidden="true" />
              </Button>
            )}
          </div>
        </div>
      )}
    </section>
  );
}
