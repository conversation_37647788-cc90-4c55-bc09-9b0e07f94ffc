import React, { useState, useEffect } from 'react';
import { CheckIcon } from '@heroicons/react/20/solid';
import PriceDisplay, { formatPrice } from './PriceDisplay';
import { PricingTwoSectionProps, Tier, Frequency } from './types';
import { Button } from '../components/ButtonV2';

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ');
}

export default function Background({
  id,
  tagline = 'Pricing',
  title = 'Choose the right plan for you',
  description = 'Choose an affordable plan that\'s packed with the best features for engaging your audience, creating customer loyalty, and driving sales.',
  frequencies,
  tiers = [],
  discounted
}: PricingTwoSectionProps) {
  // 默认频率为 monthly
  const [selectedFrequency, setSelectedFrequency] = useState<string>('monthly');
  
  // 根据 billingPeriod 设置默认频率
  useEffect(() => {
    if (!frequencies?.enabled && frequencies?.billingPeriod) {
      const periodType = frequencies.billingPeriod.type;
      if (periodType === 'monthly') {
        setSelectedFrequency('monthly');
      } else if (periodType === 'yearly') {
        setSelectedFrequency('annually');
      }
    }
  }, [frequencies]);
  
  // 获取频率选项
  const frequencyOptions: Frequency[] = frequencies?.enabled && frequencies.options 
    ? frequencies.options 
    : [{ value: 'monthly', label: 'Monthly', priceSuffix: '/mo' }];
  
  // 获取当前频率的后缀
  const getCurrentSuffix = () => {
    // 如果频率选择器关闭但设置了 billingPeriod
    if (!frequencies?.enabled && frequencies?.billingPeriod) {
      const periodType = frequencies.billingPeriod.type;
      if (periodType === 'one-time') return '';
      if (periodType === 'monthly') return '/mo';
      if (periodType === 'yearly') return '/yr';
      if (periodType === 'per-user') return '/user';
      if (periodType === 'custom' && frequencies.billingPeriod.customText) {
        return frequencies.billingPeriod.customText;
      }
    }
    
    const option = frequencyOptions.find(opt => opt.value === selectedFrequency);
    return option?.priceSuffix || '/mo';
  };
  
  // 获取价格显示
  const getPriceDisplay = (tier: Tier): number => {
    // 如果频率选择器关闭
    if (!frequencies?.enabled) {
      // 优先使用 amount 字段
      if ('amount' in tier.price && typeof tier.price.amount === 'number') {
        return tier.price.amount;
      }
      
      // 单一价格结构
      if ('value' in tier.price) {
        return tier.price.value;
      }
      
      // 如果没有 amount 字段，但有多频率结构
      if ('monthly' in tier.price) {
        return tier.price.monthly;
      }
      
      return 0;
    }
    
    // 频率选择器开启 - 原有逻辑
    if ('monthly' in tier.price) {
      if (selectedFrequency === 'monthly') {
        return tier.price.monthly;
      } else if ('annually' in tier.price) {
        return tier.price.annually;
      }
    }
    
    // 单一价格结构
    if ('value' in tier.price) {
      return tier.price.value;
    }
    
    // 默认返回0
    return 0;
  };
  
  // 获取币种
  const getCurrency = (tier: Tier): string => {
    if ('currency' in tier.price) {
      return tier.price.currency;
    }
    return 'USD';
  };

  return (
    <section 
      className="pricing-background isolate overflow-hidden bg-background dark:bg-background/95 relative before:absolute before:inset-0 before:bg-[radial-gradient(circle_500px_at_50%_200px,var(--primary-50),transparent)] dark:before:bg-[radial-gradient(circle_500px_at_50%_200px,var(--primary-900),transparent)] before:opacity-20 before:pointer-events-none before:z-[-2] after:absolute after:inset-0 after:bg-[linear-gradient(to_bottom,transparent_30%,var(--background)_100%)] dark:after:bg-[linear-gradient(to_bottom,transparent_30%,var(--background)_100%)] after:pointer-events-none after:z-[-1]"
      aria-labelledby="pricing-title"
      style={{
        // Background风格的按钮样式覆盖
        '--button-shadow': '0 4px 14px 0 rgba(0, 0, 0, 0.1)',
        '--button-hover-shadow': '0 6px 20px 0 rgba(0, 0, 0, 0.15)',
        '--button-transition-duration-normal': '200ms',
      } as React.CSSProperties}
    >
      {/* 头部内容区域 - 确保背景图形只影响这个区域 */}
      <div className="relative mx-auto max-w-container px-container-x pb-96 pt-section-y text-center">
        <div className="mx-auto max-w-content">
          {tagline && (
            <p className="text-body-base/body-base font-semibold text-primary">{tagline}</p>
          )}
          <h2 
            id="pricing-title"
            className="mt-element-y text-balance text-heading-2/heading-2 font-medium tracking-tight text-foreground"
          >
            {title}
          </h2>
        </div>
        <div className="relative mt-element-y">
          <p className="mx-auto max-w-content text-pretty text-body-large/body-large font-medium text-muted-foreground dark:text-muted-foreground/90">
            {description}
          </p>
          {/* 修复：限制背景图形的影响范围，确保不会遮盖下方的Tier区块 */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            <svg
              viewBox="0 0 1208 1024"
              className="absolute -top-10 left-1/2 z-[-10] h-[48rem] max-h-[600px] -translate-x-1/2 [mask-image:radial-gradient(closest-side,white,transparent)] dark:[mask-image:radial-gradient(closest-side,white,transparent)] sm:-top-12 md:-top-20 lg:-top-12 xl:top-0 opacity-60 dark:opacity-30"
              style={{ 
                clipPath: 'inset(0 0 20% 0)', // 裁剪底部20%，避免影响下方内容
                maxWidth: '100vw' // 限制最大宽度
              }}
            >
              <ellipse cx={604} cy={512} rx={604} ry={512} fill="url(#6d1bd035-0dd1-437e-93fa-59d316231eb0)" />
              <defs>
                <radialGradient id="6d1bd035-0dd1-437e-93fa-59d316231eb0">
                  <stop stopColor="var(--color-primary-400, #7775D6)" />
                  <stop offset={1} stopColor="var(--color-primary-600, #E935C1)" />
                </radialGradient>
              </defs>
            </svg>
          </div>
        </div>
        
        {/* Frequency selector */}
        {frequencies?.enabled && frequencyOptions.length > 1 && (
          <div className="relative z-10 mx-auto mt-content-y flex max-w-lg justify-center">
            <div className="flex rounded-full bg-muted p-1">
              {frequencyOptions.map((option) => (
                <button
                  key={option.value}
                  type="button"
                  role="menuitemradio"
                  aria-checked={option.value === selectedFrequency}
                  aria-label={`Select ${option.label} frequency`}
                  className={classNames(
                    option.value === selectedFrequency
                      ? 'bg-primary text-primary-foreground shadow-sm'
                      : 'text-muted-foreground hover:text-primary-foreground',
                    'rounded-full px-element-x py-2 text-body-small/body-small font-semibold focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-background'
                  )}
                  onClick={() => setSelectedFrequency(option.value)}
                >
                  {option.label}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
      
      {/* Tier卡片区域 - 确保在最高层级 */}
      <div className="relative z-20 flow-root bg-card dark:bg-card/95 pb-section-y backdrop-blur-sm">
        <div className="-mt-80">
          <div className="mx-auto max-w-container px-container-x">
            <div 
              className="mx-auto grid max-w-lg grid-cols-1 gap-element-y lg:max-w-content lg:grid-cols-2 lg:gap-x-element-x"
              aria-label="Pricing plans"
            >
              {tiers.map((tier, tierIdx) => (
                <div
                  key={`tier-${tierIdx}`}
                  className={classNames(
                    "relative z-30 flex flex-col justify-between rounded-3xl p-element-x py-element-y sm:p-content-y shadow-xl ring-1",
                    tier.mostPopular 
                      ? "bg-gradient-to-b from-card to-primary/5 dark:from-card dark:to-primary/10 ring-primary/30 shadow-primary/10" 
                      : "bg-card dark:bg-card/95 ring-border dark:ring-border/50"
                  )}
                >
                  {tier.mostPopular && (
                    <div className="absolute -top-3.5 left-1/2 -translate-x-1/2 transform z-40">
                      <span className="inline-flex items-center gap-1 rounded-full bg-primary px-element-x py-1 text-body-small/body-small font-semibold text-primary-foreground shadow-md">
                        <svg className="h-3.5 w-3.5" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10.868 2.884c-.321-.772-1.415-.772-1.736 0l-1.83 4.401-4.753.381c-.833.067-1.171 1.107-.536 1.651l3.62 3.102-1.106 4.637c-.194.813.691 1.456 1.405 1.02L10 15.591l4.069 2.485c.713.436 1.598-.207 1.404-1.02l-1.106-4.637 3.62-3.102c.635-.544.297-1.584-.536-1.65l-4.752-.382-1.831-4.401z" clipRule="evenodd" />
                        </svg>
                        <span className="relative top-px">Most popular</span>
                      </span>
                    </div>
                  )}
                  <div>
                    <h3 
                      id={`tier-${tierIdx}`} 
                      className={classNames(
                        "text-body-base/body-base font-semibold",
                        tier.mostPopular ? "text-primary-900 dark:text-primary-300" : "text-primary dark:text-primary-400"
                      )}
                    >
                      {tier.name}
                    </h3>
                    <PriceDisplay 
                      price={getPriceDisplay(tier)}
                      currency={getCurrency(tier)}
                      suffix={getCurrentSuffix()}
                      featured={tier.featured}
                    />
                    <p className="mt-element-y text-body-base/body-base text-muted-foreground dark:text-muted-foreground/90">{tier.description}</p>
                    <div className="mt-content-y mb-content-y">
                      <Button
                        href={tier.button.url}
                        variant="primary"
                        className="w-full"
                        aria-describedby={`tier-${tierIdx}`}
                      >
                        {tier.button.label}
                      </Button>
                    </div>
                    <ul 
                      role="list" 
                      className="space-y-element-y text-body-small/body-small text-muted-foreground dark:text-muted-foreground/90"
                      aria-label={`Features included in the ${tier.name} plan`}
                    >
                      {tier.features.map((feature, featureIdx) => (
                        <li key={`feature-${tierIdx}-${featureIdx}`} className="flex gap-x-element-x">
                          <CheckIcon aria-hidden="true" className="h-6 w-5 flex-none text-primary dark:text-primary-300" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              ))}
              
              {/* Discounted 区域 - 确保在正确层级 */}
              {discounted?.enabled && (
                <div className="relative z-30 flex flex-col items-start gap-x-content-y gap-y-element-y rounded-3xl p-element-x py-element-y sm:p-content-y ring-1 ring-border dark:ring-border/50 bg-card/50 dark:bg-card/30 backdrop-blur-sm lg:col-span-2 lg:flex-row lg:items-center">
                  <div className="lg:min-w-0 lg:flex-1">
                    <h3 className="text-body-base/body-base font-semibold text-primary dark:text-primary-300">{discounted.title || 'Discounted'}</h3>
                    <p className="mt-element-y text-body-base/body-base text-muted-foreground dark:text-muted-foreground/90">
                      {discounted.description || 'Get a discounted license for your team.'}
                    </p>
                  </div>
                  {discounted.button && (
                    <Button
                      href={discounted.button.url}
                      variant="outline"
                      className="whitespace-nowrap"
                    >
                      {discounted.button.label} <span aria-hidden="true">&rarr;</span>
                    </Button>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
