import React, { useState, useEffect } from 'react';
import { CheckIcon } from '@heroicons/react/20/solid';
import PriceDisplay, { formatPrice } from './PriceDisplay';
import { PricingTwoSectionProps, Tier, Frequency } from './types';
import { Button } from '../components/ButtonV2';

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ');
}

export default function Simple({
  id,
  tagline = 'Pricing',
  title = 'Choose the right plan for you',
  description = 'Choose an affordable plan that\'s packed with the best features for engaging your audience, creating customer loyalty, and driving sales.',
  frequencies,
  tiers = [],
  discounted
}: PricingTwoSectionProps) {
  // 默认频率为 monthly
  const [selectedFrequency, setSelectedFrequency] = useState<string>('monthly');
  
  // 根据 billingPeriod 设置默认频率
  useEffect(() => {
    if (!frequencies?.enabled && frequencies?.billingPeriod) {
      const periodType = frequencies.billingPeriod.type;
      if (periodType === 'monthly') {
        setSelectedFrequency('monthly');
      } else if (periodType === 'yearly') {
        setSelectedFrequency('annually');
      }
    }
  }, [frequencies]);
  
  // 获取频率选项
  const frequencyOptions: Frequency[] = frequencies?.enabled && frequencies.options 
    ? frequencies.options 
    : [{ value: 'monthly', label: 'Monthly', priceSuffix: '/mo' }];
  
  // 获取当前频率的后缀
  const getCurrentSuffix = () => {
    // 如果频率选择器关闭但设置了 billingPeriod
    if (!frequencies?.enabled && frequencies?.billingPeriod) {
      const periodType = frequencies.billingPeriod.type;
      if (periodType === 'one-time') return '';
      if (periodType === 'monthly') return '/mo';
      if (periodType === 'yearly') return '/yr';
      if (periodType === 'per-user') return '/user';
      if (periodType === 'custom' && frequencies.billingPeriod.customText) {
        return frequencies.billingPeriod.customText;
      }
    }
    
    const option = frequencyOptions.find(opt => opt.value === selectedFrequency);
    return option?.priceSuffix || '/mo';
  };
  
  // 获取价格显示
  const getPriceDisplay = (tier: Tier): number => {
    // 如果频率选择器关闭
    if (!frequencies?.enabled) {
      // 优先使用 amount 字段
      if ('amount' in tier.price && typeof tier.price.amount === 'number') {
        return tier.price.amount;
      }
      
      // 单一价格结构
      if ('value' in tier.price) {
        return tier.price.value;
      }
      
      // 如果没有 amount 字段，但有多频率结构
      if ('monthly' in tier.price) {
        return tier.price.monthly;
      }
      
      return 0;
    }
    
    // 频率选择器开启 - 原有逻辑
    if ('monthly' in tier.price) {
      if (selectedFrequency === 'monthly') {
        return tier.price.monthly;
      } else if ('annually' in tier.price) {
        return tier.price.annually;
      }
    }
    
    // 单一价格结构
    if ('value' in tier.price) {
      return tier.price.value;
    }
    
    // 默认返回0
    return 0;
  };
  
  // 获取币种
  const getCurrency = (tier: Tier): string => {
    if ('currency' in tier.price) {
      return tier.price.currency;
    }
    return 'USD';
  };

  return (
    <section className="relative isolate bg-background px-container-x py-section-y" aria-labelledby="pricing-title">
      <div aria-hidden="true" className="absolute inset-x-0 -top-3 -z-10 transform-gpu overflow-hidden px-36 blur-3xl">
        <div
          style={{
            clipPath:
              'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)',
          }}
          className="mx-auto aspect-[1155/678] w-[72.1875rem] bg-gradient-to-tr from-[#ff80b5] to-[#9089fc] opacity-30"
        />
      </div>
      <div className="mx-auto max-w-content text-center">
        {tagline && (
          <p className="text-body-base/body-base font-semibold text-primary">{tagline}</p>
        )}
        <h2 
          id="pricing-title"
          className="mt-element-y text-balance text-heading-2/heading-2 font-medium tracking-tight text-foreground"
        >
          {title}
        </h2>
      </div>
      <p className="mx-auto mt-element-y max-w-content text-pretty text-center text-body-large/body-large text-muted-foreground">
        {description}
      </p>
      
      {/* Frequency selector */}
      {frequencies?.enabled && frequencyOptions.length > 1 && (
        <div className="mx-auto mt-content-y flex max-w-lg justify-center">
          <div className="flex rounded-full bg-muted p-1">
            {frequencyOptions.map((option) => (
              <button
                key={option.value}
                type="button"
                className={classNames(
                  option.value === selectedFrequency
                    ? 'bg-card text-primary shadow-sm'
                    : 'text-muted-foreground hover:text-primary',
                  'rounded-full px-element-x py-2 text-body-small/body-small font-semibold focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2'
                )}
                onClick={() => setSelectedFrequency(option.value)}
              >
                {option.label}
              </button>
            ))}
          </div>
        </div>
      )}
      
      <div 
        className="mx-auto mt-content-y sm:mt-section-y grid max-w-lg grid-cols-1 items-center gap-y-element-y sm:gap-y-0 lg:max-w-content lg:grid-cols-2 lg:gap-x-element-x"
        aria-label="Pricing plans"
      >
        {tiers.map((tier, tierIdx) => (
          <div
            key={`tier-${tierIdx}`}
            className={classNames(
              tier.featured ? 'bg-primary/90 ring-primary/90 dark:bg-primary/80' : 'bg-card ring-border',
              'relative rounded-3xl p-element-x py-element-y xl:p-content-y backdrop-blur-sm ring-1'
            )}
          >
            {tier.mostPopular && (
              <div className="absolute -top-3.5 left-1/2 -translate-x-1/2 transform">
                <span className="inline-flex items-center rounded-full bg-primary px-element-x py-1 text-body-small/body-small font-semibold text-primary-foreground shadow-md">
                  <span className="relative top-px">Most popular</span>
                </span>
              </div>
            )}
            <h3
              id={`tier-${tierIdx}`}
              className={classNames(tier.featured ? 'text-primary-foreground/80' : 'text-primary', 'text-body-base/body-base font-semibold')}
            >
              {tier.name}
            </h3>
            <PriceDisplay 
              price={getPriceDisplay(tier)}
              currency={getCurrency(tier)}
              suffix={getCurrentSuffix()}
              featured={tier.featured}
            />
            <p className={classNames(tier.featured ? 'text-primary-foreground/80' : 'text-muted-foreground', 'mt-element-y text-body-base/body-base')}>
              {tier.description}
            </p>
            <div className="mt-content-y mb-content-y">
              <Button
                href={tier.button.url}
                variant={tier.featured ? "secondary" : "outline"}
                className="w-full"
                aria-describedby={`tier-${tierIdx}`}
                aria-label={`Subscribe to the ${tier.name} plan`}
                style={tier.featured ? {
                  '--button-secondary-bg': 'hsl(var(--primary-foreground))',
                  '--button-secondary-text': 'hsl(var(--primary))',
                  '--button-secondary-hover-bg': 'hsl(var(--primary-foreground) / 0.9)'
                } as React.CSSProperties : undefined}
              >
                {tier.button.label}
              </Button>
            </div>
            <ul
              role="list"
              className={classNames(
                tier.featured ? 'text-primary-foreground/80' : 'text-muted-foreground',
                'space-y-element-y text-body-small/body-small',
              )}
              aria-label={`Features included in the ${tier.name} plan`}
            >
              {tier.features.map((feature, featureIdx) => (
                <li key={`feature-${tierIdx}-${featureIdx}`} className="flex gap-x-element-x">
                  <CheckIcon
                    aria-hidden="true"
                    className={classNames(tier.featured ? 'text-primary-foreground/80' : 'text-primary', 'h-6 w-5 flex-none')}
                  />
                  {feature}
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>
      
      {/* Discounted section */}
      {discounted?.enabled && (
        <div className="mx-auto mt-content-y sm:mt-section-y max-w-lg lg:max-w-content">
          <div className="flex flex-col items-start gap-x-content-y gap-y-element-y rounded-3xl p-element-x py-element-y sm:p-content-y ring-1 ring-border lg:flex-row lg:items-center">
            <div className="lg:min-w-0 lg:flex-1">
              <h3 className="text-body-base/body-base font-semibold text-primary">{discounted.title || 'Discounted'}</h3>
              <p className="mt-element-y text-body-base/body-base text-muted-foreground">
                {discounted.description || 'Get a discounted license for your team.'}
              </p>
            </div>
            {discounted.button && (
              <Button
                href={discounted.button.url}
                variant="outline"
                className="whitespace-nowrap"
              >
                {discounted.button.label} <span aria-hidden="true">&rarr;</span>
              </Button>
            )}
          </div>
        </div>
      )}
    </section>
  );
}
