'use client'

import { useState, useEffect } from 'react'
import { CheckIcon } from '@heroicons/react/20/solid'
import { Switch } from '@headlessui/react'
import { PricingFourSectionProps, Tier } from './types'
import { getPriceDisplay, getCurrency, getPriceDescription } from './utils'
import { formatPrice } from './PriceDisplay'
import { Button } from '../components/ButtonV2'

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ')
}

export default function BasicToggle({
  id,
  tagline = 'Pricing',
  title = 'Choose the right plan for you',
  description = 'Choose an affordable plan that\'s packed with the best features for engaging your audience, creating customer loyalty, and driving sales.',
  frequencies,
  tiers = [],
  customSolution
}: PricingFourSectionProps) {
  // 默认频率为 monthly
  const [annual, setAnnual] = useState<boolean>(false);
  const selectedFrequency = annual ? 'annually' : 'monthly';
  
  // 获取价格显示
  const getPriceDisplayForTier = (tier: Tier): number => {
    return getPriceDisplay(tier, selectedFrequency, frequencies);
  };

  // 获取价格描述
  const getPriceDescriptionForTier = (tier: Tier): string | undefined => {
    return getPriceDescription(tier, selectedFrequency, frequencies);
  };
  
  // 获取当前频率的后缀
  const getCurrentSuffix = () => {
    return annual ? '/yr' : '/mo';
  };
  
  // 计算年度节省百分比
  const calculateAnnualSavingsPercent = (tier: Tier): number | null => {
    if ('monthly' in tier.price && 'annually' in tier.price && annual) {
      const monthlyCost = tier.price.monthly * 12;
      const annualCost = tier.price.annually;
      
      if (monthlyCost > 0) {
        return Math.round((1 - (annualCost / monthlyCost)) * 100);
      }
    }
    return null;
  };

  return (
    <section 
      className="bg-background py-section-y"
      style={{
        // BasicToggle风格的按钮样式覆盖
        '--button-radius': '0.375rem',
        '--button-shadow': '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
        '--button-hover-shadow': '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
        '--button-transition-duration-normal': '200ms',
        '--button-font-weight': '600',
      } as React.CSSProperties}
    >
      <div className="mx-auto max-w-container px-container-x">
        <div className="mx-auto max-w-content text-center">
          {tagline && (
            <p className="text-body-base/body-base font-semibold text-primary">{tagline}</p>
          )}
          <h2 id={id || 'pricing'} className="mt-element-y text-heading-1/heading-1 font-bold tracking-tight text-foreground">
            {title}
          </h2>
        </div>
        <p className="mx-auto mt-element-y max-w-content text-center text-body-large/body-large text-muted-foreground">
          {description}
        </p>
        
        {/* 频率切换开关 */}
        {frequencies?.enabled && (
          <div className="mt-content-y flex justify-center">
            <div className="relative flex items-center" aria-labelledby="billing-toggle-label">
              <div id="billing-toggle-label" className="sr-only">Select your preferred billing frequency</div>
              <span className="text-body-small/body-small font-medium text-foreground mr-element-x">Monthly</span>
              <Switch
                checked={annual}
                onChange={setAnnual}
                className={classNames(
                  annual ? 'bg-primary' : 'bg-muted',
                  'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2'
                )}
              >
                <span className="sr-only">Toggle between monthly and annual billing</span>
                <span
                  aria-hidden="true"
                  className={classNames(
                    annual ? 'translate-x-5' : 'translate-x-0',
                    'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-background shadow ring-0 transition duration-200 ease-in-out'
                  )}
                />
              </Switch>
              <span className="text-body-small/body-small font-medium text-foreground ml-element-x">
                Annual <span className="text-green-500 dark:text-green-400 font-semibold">(Save up to 16%)</span>
              </span>
            </div>
          </div>
        )}
        
        <div className="isolate mx-auto mt-content-y grid max-w-container grid-cols-1 gap-content-y lg:grid-cols-4">
          {tiers.map((tier, tierIdx) => {
            const savingsPercent = calculateAnnualSavingsPercent(tier);
            
            return (
              <div
                key={`tier-${tierIdx}`}
                className={classNames(
                  tier.mostPopular ? 'ring-2 ring-primary' : 'ring-1 ring-border',
                  'rounded-3xl p-content-y'
                )}
              >
                <div className="flex items-center justify-between gap-x-element-x">
                  <h3
                    id={`tier-${tierIdx}`}
                    className={classNames(
                      tier.mostPopular ? 'text-primary' : 'text-foreground',
                      'text-heading-4/heading-4 font-semibold'
                    )}
                  >
                    {tier.name}
                  </h3>
                  {tier.mostPopular && (
                    <p className="rounded-full bg-primary/10 px-element-x py-0.5 text-body-small/body-small font-semibold text-primary" aria-label="Most popular plan">
                      Most popular
                    </p>
                  )}
                </div>
                <p className="mt-element-y text-body-small/body-small text-muted-foreground">{tier.description}</p>
                <p className="mt-element-y flex items-baseline gap-x-element-x">
                  <span className="text-heading-2/heading-2 font-bold tracking-tight text-foreground">
                    {formatPrice(getPriceDisplayForTier(tier), getCurrency(tier), '')}
                  </span>
                  <span className="text-body-small/body-small font-semibold text-muted-foreground">
                    {getCurrentSuffix()}
                  </span>
                </p>
                
                {/* 年度节省提示 */}
                {savingsPercent && (
                  <p className="mt-element-y text-body-small/body-small font-medium text-green-600 dark:text-green-400">
                    Save {savingsPercent}% with annual billing
                  </p>
                )}
                
                {/* 价格描述 */}
                {getPriceDescriptionForTier(tier) && (
                  <p className="mt-element-y text-body-small/body-small text-muted-foreground min-h-[40px]">
                    {getPriceDescriptionForTier(tier)}
                  </p>
                )}
                
                <Button
                  href={tier.button.href}
                  variant={tier.mostPopular ? "primary" : "outline"}
                  className="mt-element-y w-full"
                  aria-describedby={`tier-${tierIdx}`}
                  aria-label={`Get ${tier.name} plan`}
                >
                  {tier.button.label}
                </Button>
                
                <ul role="list" className="mt-content-y space-y-element-y text-body-small/body-small text-muted-foreground">
                  {tier.features.map((feature, featureIdx) => (
                    <li key={`feature-${tierIdx}-${featureIdx}`} className="flex gap-x-element-x">
                      <CheckIcon className="h-6 w-5 flex-none text-primary" aria-hidden="true" />
                      <span className="break-words">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            );
          })}
        </div>
        
        {/* 自定义解决方案区域 */}
        {customSolution?.enabled && (
          <div className="mx-auto mt-section-y max-w-container rounded-2xl bg-muted p-content-y px-container-x lg:flex lg:items-center lg:justify-between">
            <div className="lg:w-2/3">
              <h3 id="custom-solution" className="text-heading-3/heading-3 font-bold tracking-tight text-foreground">
                {customSolution.title || "Need a custom solution?"}
              </h3>
              <p className="mt-element-y text-body-large/body-large text-muted-foreground">
                {customSolution.description || "Get in touch with our sales team and we'll help you find the perfect solution for your business needs."}
              </p>
            </div>
            <div className="mt-content-y lg:mt-0 lg:ml-content-x">
              <Button
                href={customSolution.button?.href || "#"}
                variant="primary"
                aria-describedby="custom-solution"
              >
                {customSolution.button?.label || "Contact sales"}
              </Button>
            </div>
          </div>
        )}
      </div>
    </section>
  );
}
