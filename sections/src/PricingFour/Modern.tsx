'use client'

import { useState, useEffect } from 'react'
import { CheckIcon, SparklesIcon } from '@heroicons/react/20/solid'
import PriceDisplay, { formatPrice } from './PriceDisplay'
import { PricingFourSectionProps, Tier, Frequency } from './types'
import { getPriceDisplay, getCurrency, getPriceDescription } from './utils'
import { Button } from '../components/ButtonV2'

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ')
}

export default function Modern({
  id,
  tagline = 'Pricing',
  title = 'Choose the right plan for you',
  description = 'Choose an affordable plan that\'s packed with the best features for engaging your audience, creating customer loyalty, and driving sales.',
  frequencies,
  tiers = [],
  customSolution
}: PricingFourSectionProps) {
  // 默认频率为 monthly
  const [selectedFrequency, setSelectedFrequency] = useState<string>('monthly');
  
  // 根据 billingPeriod 设置默认频率
  useEffect(() => {
    if (!frequencies?.enabled && frequencies?.billingPeriod) {
      const periodType = frequencies.billingPeriod.type;
      if (periodType === 'monthly') {
        setSelectedFrequency('monthly');
      } else if (periodType === 'annually') {
        setSelectedFrequency('annually');
      }
    }
  }, [frequencies]);
  
  // 获取频率选项
  const frequencyOptions: Frequency[] = frequencies?.enabled && frequencies.options 
    ? frequencies.options 
    : [{ value: 'monthly', label: 'Monthly', priceSuffix: '/month' }];
  
  // 获取当前频率的后缀
  const getCurrentSuffix = () => {
    // 如果频率选择器关闭但设置了 billingPeriod
    if (!frequencies?.enabled && frequencies?.billingPeriod) {
      const periodType = frequencies.billingPeriod.type;
      if (periodType === 'one-time') return '';
      if (periodType === 'monthly') return '/mo';
      if (periodType === 'annually') return '/yr';
      if (periodType === 'per-user') return '/user';
      if (periodType === 'custom' && frequencies.billingPeriod.customText) {
        return frequencies.billingPeriod.customText;
      }
    }
    
    const option = frequencyOptions.find(opt => opt.value === selectedFrequency);
    return option?.priceSuffix || '/mo';
  };
  
  // 获取价格显示
  const getPriceDisplayForTier = (tier: Tier): number => {
    return getPriceDisplay(tier, selectedFrequency, frequencies);
  };

  // 获取价格描述
  const getPriceDescriptionForTier = (tier: Tier): string | undefined => {
    return getPriceDescription(tier, selectedFrequency, frequencies);
  };
  
  // 计算年度节省金额
  const calculateAnnualSavings = (tier: Tier): string | null => {
    if ('monthly' in tier.price && 'annually' in tier.price && selectedFrequency === 'annually') {
      const monthlyCost = tier.price.monthly;
      const annualCost = tier.price.annually;
      const savings = (monthlyCost * 12) - annualCost;
      
      if (savings > 0) {
        return formatPrice(savings, getCurrency(tier), '');
      }
    }
    return null;
  };

  return (
    <section 
      className="relative isolate overflow-hidden bg-gradient-to-b from-primary/5 via-background to-background px-container-x py-section-y"
      style={{
        // Modern风格的按钮样式覆盖
        '--button-radius': '9999px', // rounded-full
        '--button-shadow': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        '--button-hover-shadow': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        '--button-transition-duration-normal': '200ms',
        '--button-font-weight': '600',
      } as React.CSSProperties}
    >
      {/* 背景装饰 */}
      <div className="absolute inset-0 -z-10 bg-[radial-gradient(45rem_50rem_at_top,theme(colors.primary.100),theme(colors.background))] opacity-20" />
      <div className="absolute inset-y-0 right-1/2 -z-10 mr-16 w-[200%] origin-bottom-left skew-x-[-30deg] bg-background shadow-xl shadow-primary/10 ring-1 ring-primary/5 sm:mr-28 lg:mr-0 xl:mr-16 xl:origin-center" />
      
      <div className="mx-auto max-w-content text-center">
        {tagline && (
          <p className="inline-flex items-center rounded-full bg-primary/10 px-element-x py-element-y text-body-small/body-small font-medium text-primary ring-1 ring-inset ring-primary/10">
            {tagline}
          </p>
        )}
        <h2 id={id || 'pricing'} className="mt-element-y text-balance text-heading-1/heading-1 font-bold tracking-tight text-foreground">
          {title}
        </h2>
      </div>
      <p className="mx-auto mt-element-y max-w-content text-pretty text-center text-body-large/body-large text-muted-foreground">
        {description}
      </p>
      
      {/* 频率选择开关 */}
      {frequencies?.enabled && frequencyOptions.length > 1 && (
        <div className="mx-auto mt-content-y flex max-w-content justify-center">
          <div aria-label="Payment frequency" aria-describedby="frequency-description" className="flex rounded-full bg-card p-element-x shadow-sm ring-1 ring-inset ring-border">
            <div id="frequency-description" className="sr-only">Select your preferred billing frequency</div>
            {frequencyOptions.map((option) => (
              <button
                key={option.value}
                type="button"
                className={classNames(
                  option.value === selectedFrequency
                    ? 'bg-primary text-primary-foreground shadow-sm'
                    : 'text-muted-foreground hover:text-primary',
                  'relative rounded-full px-content-x py-element-y text-body-small/body-small font-semibold transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2'
                )}
                onClick={() => setSelectedFrequency(option.value)}
              >
                {option.value === 'annually' && option.value === selectedFrequency && (
                  <span className="absolute -top-3 right-0 flex items-center justify-center rounded-full bg-green-100 px-element-x py-0.5 text-body-small/body-small font-medium text-green-800 dark:bg-green-900 dark:text-green-100 ring-1 ring-inset ring-green-600/20">
                    Save 16%
                  </span>
                )}
                {option.label}
              </button>
            ))}
          </div>
        </div>
      )}
      
      <div className="mx-auto mt-content-y grid max-w-container grid-cols-1 gap-content-y sm:mt-section-y lg:grid-cols-4">
        {tiers.map((tier, tierIdx) => (
          <div
            key={`tier-${tierIdx}`}
            className={classNames(
              'relative rounded-2xl p-content-y transition-all duration-300',
              tier.mostPopular 
                ? 'bg-gradient-to-br from-primary to-primary/80 text-primary-foreground shadow-xl ring-1 ring-primary/50 hover:shadow-primary/20' 
                : 'bg-card text-foreground ring-1 ring-border hover:ring-border/80 hover:shadow-lg'
            )}
          >
            {tier.mostPopular && (
              <div className="absolute -top-3.5 left-1/2 transform -translate-x-1/2 flex justify-center">
                <span className="inline-flex items-center justify-center gap-element-x rounded-full bg-gradient-to-r from-amber-400 to-orange-500 px-element-x py-0.5 text-body-small/body-small font-medium text-white shadow-md" aria-label="Most popular plan">
                  <SparklesIcon className="h-3.5 w-3.5" aria-hidden="true" />
                  <span className="relative top-px">Most popular</span>
                </span>
              </div>
            )}
            <h3
              id={`tier-${tierIdx}`}
              className={classNames(
                'text-foreground text-heading-4/heading-4 font-semibold'
              )}
            >
              {tier.name}
            </h3>
            <div className={classNames(
              tier.mostPopular ? 'text-foreground' : 'text-foreground',
              'mt-element-y flex items-baseline gap-x-element-x'
            )}>
              <span className="text-heading-1/heading-1 font-bold tracking-tight">
                {formatPrice(getPriceDisplayForTier(tier), getCurrency(tier), '')}
              </span>
              <span className={tier.mostPopular ? 'text-foreground/80' : 'text-muted-foreground'}>
                {getCurrentSuffix()}
              </span>
            </div>
            
            {/* 年度节省提示 */}
            {calculateAnnualSavings(tier) && (
              <p className={classNames(
                tier.mostPopular ? 'text-foreground/80' : 'text-green-600 dark:text-green-400',
                'mt-element-y text-body-small/body-small font-medium'
              )}>
                Save {calculateAnnualSavings(tier)} per year
              </p>
            )}
            
            {/* 价格描述 */}
            {getPriceDescriptionForTier(tier) && (
              <p className={classNames(
                tier.mostPopular ? 'text-foreground/80' : 'text-muted-foreground',
                'mt-element-y text-body-small/body-small min-h-[40px]'
              )}>
                {getPriceDescriptionForTier(tier)}
              </p>
            )}
            
            <p className={classNames(
              tier.mostPopular ? 'text-foreground/80' : 'text-muted-foreground', 
              'mt-element-y text-body-base/body-base min-h-[56px]'
            )}>
              {tier.description}
            </p>
            <Button
              href={tier.button.href}
              variant={tier.mostPopular ? "primary" : "primary"}
              className="mt-content-y w-full"
              aria-describedby={`tier-${tierIdx}`}
              aria-label={`Get ${tier.name} plan`}
              style={tier.mostPopular ? {
                background: 'linear-gradient(90deg, rgba(251, 191, 36, 1) 0%, rgba(249, 115, 22, 1) 100%)',
                color: 'white',
                '--button-hover-bg': 'linear-gradient(90deg, rgba(245, 158, 11, 1) 0%, rgba(234, 88, 12, 1) 100%)',
                border: 'none'
              } as React.CSSProperties : undefined}
            >
              {tier.button.label}
            </Button>
            <ul
              role="list"
              className={classNames(
                tier.mostPopular ? 'text-foreground/80' : 'text-muted-foreground',
                'mt-content-y space-y-element-y text-body-small/body-small',
              )}
            >
              {tier.features.map((feature, featureIdx) => (
                <li key={`feature-${tierIdx}-${featureIdx}`} className="flex gap-x-element-x">
                  <CheckIcon
                    aria-hidden="true"
                    className={classNames(
                      'text-foreground/80 h-6 w-5 flex-none'
                    )}
                  />
                  <span className="break-words">{feature}</span>
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>

      {/* 自定义解决方案区域 */}
      {customSolution?.enabled && (
        <div className="mx-auto mt-section-y max-w-container rounded-2xl bg-gradient-to-r from-primary/10 to-primary/5 p-content-y">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div className="lg:w-2/3">
              <h3 id="custom-solution" className="text-heading-3/heading-3 font-bold tracking-tight text-foreground">
                {customSolution.title || "Need a custom solution?"}
              </h3>
              <p className="mt-element-y text-body-large/body-large text-muted-foreground">
                {customSolution.description || "Get in touch with our sales team and we'll help you find the perfect solution for your business needs."}
              </p>
            </div>
            <div className="mt-content-y lg:mt-0 lg:ml-content-x">
              <Button
                href={customSolution.button?.href || "#"}
                variant="primary"
                aria-describedby="custom-solution"
                style={{
                  background: 'linear-gradient(90deg, rgba(var(--primary), 1) 0%, rgba(var(--primary), 0.8) 100%)',
                  '--button-hover-bg': 'linear-gradient(90deg, rgba(var(--primary), 0.9) 0%, rgba(var(--primary), 0.7) 100%)',
                } as React.CSSProperties}
              >
                {customSolution.button?.label || "Contact sales"}
              </Button>
            </div>
          </div>
        </div>
      )}
    </section>
  );
}
