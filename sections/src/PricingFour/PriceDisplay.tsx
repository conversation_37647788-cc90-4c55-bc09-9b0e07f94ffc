import React from 'react';
import { Currency } from './types';

// 支持的货币代码
export type CurrencyCode = 'USD' | 'EUR' | 'GBP' | 'JPY' | 'CNY' | 'CAD' | 'AUD' | 'NONE';

// 货币映射表
export const currencyMap: Record<CurrencyCode, Currency> = {
  USD: { symbol: "$", code: "USD" },
  EUR: { symbol: "€", code: "EUR" },
  GBP: { symbol: "£", code: "GBP" },
  JPY: { symbol: "¥", code: "JPY" },
  CNY: { symbol: "¥", code: "CNY" },
  CAD: { symbol: "C$", code: "CAD" },
  AUD: { symbol: "A$", code: "AUD" },
  NONE: { symbol: "", code: "" }
};

// 格式化价格
export const formatPrice = (price: number, currency: string = 'USD', suffix: string = ''): string => {
  const currencyObj = currencyMap[currency as CurrencyCode] || currencyMap.USD;
  
  // 处理不同货币的格式
  let formattedPrice = '';
  
  // 日元不使用小数点
  if (currency === 'JPY') {
    formattedPrice = Math.round(price).toString();
  } else {
    // 其他货币保留两位小数
    formattedPrice = price.toFixed(2);
    // 如果小数部分为 .00，则移除
    if (formattedPrice.endsWith('.00')) {
      formattedPrice = formattedPrice.slice(0, -3);
    }
  }
  
  return `${currencyObj.symbol}${formattedPrice}${suffix}`;
};

interface PriceDisplayProps {
  price: number;
  currency?: string;
  suffix?: string;
  featured?: boolean;
  description?: string;
  className?: string;
  priceClassName?: string;
  descriptionClassName?: string;
}

const PriceDisplay: React.FC<PriceDisplayProps> = ({ 
  price, 
  currency = 'USD', 
  suffix = '/mo',
  featured = false,
  description,
  className = '',
  priceClassName = '',
  descriptionClassName = ''
}) => {
  return (
    <>
      <p className={`mt-element-y flex items-baseline gap-x-element-x ${className}`}>
        <span className={`text-heading-2/heading-2 font-semibold tracking-tight text-foreground ${priceClassName}`}>
          {formatPrice(price, currency, '')}
        </span>
        <span className={`text-body-small/body-small font-semibold text-muted-foreground`}>{suffix}</span>
      </p>
      {description && (
        <p className={`mt-element-y text-body-small/body-small text-muted-foreground ${descriptionClassName}`}>
          {description}
        </p>
      )}
    </>
  );
};

export default PriceDisplay;
