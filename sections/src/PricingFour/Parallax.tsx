'use client'

import { useState, useEffect, useRef } from 'react'
import { Radio, RadioGroup } from '@headlessui/react'
import { CheckIcon, SparklesIcon } from '@heroicons/react/20/solid'
import { motion } from 'motion/react'
import PriceDisplay from './PriceDisplay'
import { PricingFourSectionProps, Tier, Frequency } from './types'
import { getPriceDisplay, getCurrency, getPriceDescription } from './utils'
import { Button } from '../components/ButtonV2'

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ')
}

export default function Parallax({
  id,
  tagline = 'Pricing',
  title = 'Pricing that grows with you',
  description = 'Choose an affordable plan that\'s packed with the best features for engaging your audience, creating customer loyalty, and driving sales.',
  frequencies,
  tiers = [],
  customSolution
}: PricingFourSectionProps) {
  // 默认频率为 monthly
  const [selectedFrequency, setSelectedFrequency] = useState<string>('monthly');
  
  // 根据 billingPeriod 设置默认频率
  useEffect(() => {
    if (!frequencies?.enabled && frequencies?.billingPeriod) {
      const periodType = frequencies.billingPeriod.type;
      if (periodType === 'monthly') {
        setSelectedFrequency('monthly');
      } else if (periodType === 'annually') {
        setSelectedFrequency('annually');
      }
    }
  }, [frequencies]);
  
  // 获取频率选项
  const frequencyOptions: Frequency[] = frequencies?.enabled && frequencies.options 
    ? frequencies.options 
    : [{ value: 'monthly', label: 'Monthly', priceSuffix: '/month' }];
  
  // 获取当前频率的后缀
  const getCurrentSuffix = () => {
    // 如果频率选择器关闭但设置了 billingPeriod
    if (!frequencies?.enabled && frequencies?.billingPeriod) {
      const periodType = frequencies.billingPeriod.type;
      if (periodType === 'one-time') return '';
      if (periodType === 'monthly') return '/mo';
      if (periodType === 'annually') return '/yr';
      if (periodType === 'per-user') return '/user';
      if (periodType === 'custom' && frequencies.billingPeriod.customText) {
        return frequencies.billingPeriod.customText;
      }
    }
    
    const option = frequencyOptions.find(opt => opt.value === selectedFrequency);
    return option?.priceSuffix || '/mo';
  };
  
  // 获取价格显示
  const getPriceDisplayForTier = (tier: Tier) => {
    return getPriceDisplay(tier, selectedFrequency, frequencies);
  };

  // 获取价格描述
  const getPriceDescriptionForTier = (tier: Tier) => {
    return getPriceDescription(tier, selectedFrequency, frequencies);
  };

  return (
    <section 
      className="relative overflow-hidden bg-background py-section-y"
      style={{
        // Parallax风格的按钮样式覆盖
        '--button-radius': '0.375rem',
        '--button-shadow': '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
        '--button-hover-shadow': '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
        '--button-transition-duration-normal': '200ms',
        '--button-font-weight': '600',
      } as React.CSSProperties}
    >
      {/* 背景装饰 */}
      <div className="absolute inset-0 -z-10 bg-[radial-gradient(45rem_50rem_at_top,theme(colors.primary.100),theme(colors.background))] opacity-20" />
      <div className="absolute inset-y-0 right-1/2 -z-10 mr-16 w-[200%] origin-bottom-left skew-x-[-30deg] bg-background shadow-xl shadow-primary/10 ring-1 ring-primary/5 sm:mr-28 lg:mr-0 xl:mr-16 xl:origin-center" />
      
      <div className="mx-auto max-w-container px-container-x">
        <motion.div 
          className="mx-auto max-w-content text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {tagline && (
            <motion.p 
              className="text-body-base/body-base font-semibold text-primary"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2, duration: 0.4 }}
            >
              {tagline}
            </motion.p>
          )}
          <motion.h2 
            id={id || 'pricing'} 
            className="mt-element-y text-heading-1/heading-1 font-bold tracking-tight text-foreground"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3, duration: 0.5 }}
          >
            {title}
          </motion.h2>
        </motion.div>
        
        <motion.p 
          className="mx-auto mt-element-y max-w-content text-center text-body-large/body-large text-muted-foreground"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4, duration: 0.4 }}
        >
          {description}
        </motion.p>
        
        {/* 频率选择开关 */}
        {frequencies?.enabled && frequencyOptions.length > 1 && (
          <motion.div
            className="mt-content-y flex justify-center"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.5, duration: 0.4 }}
          >
            <fieldset aria-label="Payment frequency" aria-describedby="frequency-description">
              <div id="frequency-description" className="sr-only">Select your preferred billing frequency</div>
              <RadioGroup
                value={frequencyOptions.find(opt => opt.value === selectedFrequency) || frequencyOptions[0]}
                onChange={(option) => setSelectedFrequency(option.value)}
                className="grid grid-cols-2 gap-x-element-x rounded-full p-element-x text-center text-body-small/body-small font-semibold ring-1 ring-inset ring-border"
              >
                {frequencyOptions.map((option) => (
                  <Radio
                    key={option.value}
                    value={option}
                    className="cursor-pointer rounded-full px-content-x py-element-y text-muted-foreground data-[checked]:bg-primary data-[checked]:text-primary-foreground transition-colors duration-200"
                  >
                    {option.label}
                  </Radio>
                ))}
              </RadioGroup>
            </fieldset>
          </motion.div>
        )}
        
        <div className="isolate mx-auto mt-content-y grid max-w-container grid-cols-1 gap-content-y md:grid-cols-2 xl:grid-cols-4">
          {tiers.map((tier, tierIdx) => (
            <motion.div
              key={`tier-${tierIdx}`}
              className={classNames(
                tier.mostPopular ? 'ring-2 ring-primary shadow-lg' : 'ring-1 ring-border',
                'rounded-3xl p-content-y bg-card relative overflow-hidden',
              )}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ 
                delay: 0.2 + tierIdx * 0.1, 
                duration: 0.5,
                ease: [0.25, 0.1, 0.25, 1] 
              }}
              whileHover={{ 
                y: -5,
                boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.03)',
                transition: { duration: 0.2 }
              }}
            >
              {tier.mostPopular && (
                <motion.div 
                  className="absolute top-0 right-0 -mt-2 -mr-2 flex h-10 w-10 items-center justify-center rounded-full bg-primary"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.6 + tierIdx * 0.1, duration: 0.3, type: "spring" }}
                  aria-label="Most popular plan"
                >
                  <SparklesIcon className="h-5 w-5 text-primary-foreground" aria-hidden="true" />
                </motion.div>
              )}
              
              <motion.h3
                id={`tier-${tierIdx}`}
                className={classNames(
                  tier.mostPopular ? 'text-primary' : 'text-foreground',
                  'text-heading-4/heading-4 font-semibold',
                )}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.3 + tierIdx * 0.1, duration: 0.4 }}
              >
                {tier.name}
              </motion.h3>
              
              <motion.p 
                className="mt-element-y text-body-small/body-small text-muted-foreground"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.4 + tierIdx * 0.1, duration: 0.4 }}
              >
                {tier.description}
              </motion.p>
              
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.5 + tierIdx * 0.1, duration: 0.4 }}
              >
                <PriceDisplay 
                  price={getPriceDisplayForTier(tier)}
                  currency={getCurrency(tier)}
                  suffix={getCurrentSuffix()}
                  description={getPriceDescriptionForTier(tier)}
                />
              </motion.div>
              
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.6 + tierIdx * 0.1, duration: 0.4 }}
                whileHover={{ 
                  scale: 1.03,
                  transition: { duration: 0.2 }
                }}
                whileTap={{ scale: 0.98 }}
              >
                <Button
                  href={tier.button.href}
                  variant={tier.mostPopular ? "primary" : "outline"}
                  className="mt-element-y w-full"
                  aria-describedby={`tier-${tierIdx}`}
                  aria-label={`Get ${tier.name} plan`}
                >
                  {tier.button.label}
                </Button>
              </motion.div>
              
              <motion.ul 
                role="list" 
                className="mt-content-y space-y-element-y text-body-small/body-small text-muted-foreground"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.7 + tierIdx * 0.1, duration: 0.4 }}
              >
                {tier.features.map((feature, featureIdx) => (
                  <motion.li 
                    key={`feature-${tierIdx}-${featureIdx}`} 
                    className="flex gap-x-element-x"
                    initial={{ opacity: 0, x: -5 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ 
                      delay: 0.8 + tierIdx * 0.05 + featureIdx * 0.03, 
                      duration: 0.3 
                    }}
                  >
                    <CheckIcon aria-hidden="true" className="h-6 w-5 flex-none text-primary" />
                    <span className="break-words">{feature}</span>
                  </motion.li>
                ))}
              </motion.ul>
            </motion.div>
          ))}
        </div>
      </div>

      {/* 自定义解决方案区域 */}
      {customSolution?.enabled && (
        <motion.div 
          className="mx-auto mt-section-y max-w-container rounded-lg border border-border bg-card p-content-y lg:flex lg:items-center lg:justify-between"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.5 }}
          whileHover={{ 
            boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.03)',
            transition: { duration: 0.2 }
          }}
        >
          <div className="lg:w-2/3">
            <motion.h3 
              id="custom-solution"
              className="text-heading-4/heading-4 font-semibold text-foreground"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.9, duration: 0.4 }}
            >
              {customSolution.title || "Need a custom solution?"}
            </motion.h3>
            <motion.p 
              className="mt-element-y text-body-base/body-base text-muted-foreground"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1.0, duration: 0.4 }}
            >
              {customSolution.description || "Get in touch with our sales team and we'll help you find the perfect solution for your business needs."}
            </motion.p>
          </div>
          <motion.div 
            className="mt-content-y lg:mt-0 lg:ml-content-x"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.1, duration: 0.4 }}
          >
            <motion.div
              whileHover={{ 
                scale: 1.05,
                transition: { duration: 0.2 }
              }}
              whileTap={{ scale: 0.98 }}
            >
              <Button
                href={customSolution.button?.href || "#"}
                variant="primary"
                aria-describedby="custom-solution"
              >
                {customSolution.button?.label || "Contact sales"}
              </Button>
            </motion.div>
          </motion.div>
        </motion.div>
      )}
    </section>
  )
}
