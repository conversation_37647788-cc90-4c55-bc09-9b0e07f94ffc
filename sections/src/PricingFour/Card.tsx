'use client'

import { useState, useEffect } from 'react'
import { CheckIcon } from '@heroicons/react/20/solid'
import { PricingFourSectionProps, Tier, Frequency } from './types'
import { getPriceDisplay, getCurrency, getPriceDescription } from './utils'
import { formatPrice } from './PriceDisplay'
import { Button } from '../components/ButtonV2'

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ')
}

export default function Card({
  id,
  tagline = 'Pricing',
  title = 'Choose the right plan for you',
  description = 'Choose an affordable plan that\'s packed with the best features for engaging your audience, creating customer loyalty, and driving sales.',
  frequencies,
  tiers = [],
  customSolution
}: PricingFourSectionProps) {
  // 默认频率为 monthly
  const [selectedFrequency, setSelectedFrequency] = useState<string>('monthly');
  
  // 根据 billingPeriod 设置默认频率
  useEffect(() => {
    if (!frequencies?.enabled && frequencies?.billingPeriod) {
      const periodType = frequencies.billingPeriod.type;
      if (periodType === 'monthly') {
        setSelectedFrequency('monthly');
      } else if (periodType === 'annually') {
        setSelectedFrequency('annually');
      }
    }
  }, [frequencies]);
  
  // 获取频率选项
  const frequencyOptions: Frequency[] = frequencies?.enabled && frequencies.options 
    ? frequencies.options 
    : [{ value: 'monthly', label: 'Monthly', priceSuffix: '/month' }];
  
  // 获取当前频率的后缀
  const getCurrentSuffix = () => {
    // 如果频率选择器关闭但设置了 billingPeriod
    if (!frequencies?.enabled && frequencies?.billingPeriod) {
      const periodType = frequencies.billingPeriod.type;
      if (periodType === 'one-time') return '';
      if (periodType === 'monthly') return '/mo';
      if (periodType === 'annually') return '/yr';
      if (periodType === 'per-user') return '/user';
      if (periodType === 'custom' && frequencies.billingPeriod.customText) {
        return frequencies.billingPeriod.customText;
      }
    }
    
    const option = frequencyOptions.find(opt => opt.value === selectedFrequency);
    return option?.priceSuffix || '/mo';
  };
  
  // 获取价格显示
  const getPriceDisplayForTier = (tier: Tier): number => {
    return getPriceDisplay(tier, selectedFrequency, frequencies);
  };

  // 获取价格描述
  const getPriceDescriptionForTier = (tier: Tier): string | undefined => {
    return getPriceDescription(tier, selectedFrequency, frequencies);
  };
  
  // 计算年度节省百分比
  const calculateAnnualSavingsPercent = (tier: Tier): number | null => {
    if ('monthly' in tier.price && 'annually' in tier.price && selectedFrequency === 'annually') {
      const monthlyCost = tier.price.monthly * 12;
      const annualCost = tier.price.annually;
      
      if (monthlyCost > 0) {
        return Math.round((1 - (annualCost / monthlyCost)) * 100);
      }
    }
    return null;
  };

  return (
    <section 
      className="bg-background py-section-y"
      style={{
        // Card风格的按钮样式覆盖
        '--button-radius': '0.375rem',
        '--button-shadow': '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
        '--button-hover-shadow': '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
        '--button-transition-duration-normal': '200ms',
        '--button-font-weight': '600',
      } as React.CSSProperties}
    >
      <div className="mx-auto max-w-container px-container-x">
        <div className="mx-auto max-w-content text-center">
          {tagline && (
            <p className="text-body-base/body-base font-semibold text-primary">{tagline}</p>
          )}
          <h2 id={id || 'pricing'} className="mt-element-y text-heading-1/heading-1 font-bold tracking-tight text-foreground">
            {title}
          </h2>
        </div>
        <p className="mx-auto mt-element-y max-w-content text-center text-body-large/body-large text-muted-foreground">
          {description}
        </p>
        
        {/* 频率选择开关 */}
        {frequencies?.enabled && frequencyOptions.length > 1 && (
          <div className="mt-content-y flex justify-center">
            <div aria-label="Payment frequency" aria-describedby="frequency-description" className="grid grid-cols-2 gap-x-element-x rounded-md p-element-x text-center text-body-small/body-small font-semibold ring-1 ring-inset ring-border">
              <div id="frequency-description" className="sr-only">Select your preferred billing frequency</div>
              {frequencyOptions.map((option) => (
                <button
                  key={option.value}
                  className={classNames(
                    option.value === selectedFrequency
                      ? 'bg-primary text-primary-foreground'
                      : 'text-foreground hover:bg-muted',
                    'cursor-pointer rounded-md px-content-x py-element-y text-body-small/body-small font-medium transition-colors'
                  )}
                  onClick={() => setSelectedFrequency(option.value)}
                >
                  {option.label}
                </button>
              ))}
            </div>
          </div>
        )}
        
        <div className="mx-auto mt-content-y grid max-w-container grid-cols-1 gap-content-y sm:gap-y-0 lg:grid-cols-2">
          {tiers.slice(0, 2).map((tier, tierIdx) => {
            const savingsPercent = calculateAnnualSavingsPercent(tier);
            
            return (
              <div
                key={`tier-${tierIdx}`}
                className={classNames(
                  tierIdx === 0 ? 'rounded-t-3xl sm:rounded-tr-none sm:rounded-l-3xl' : 'sm:rounded-tr-3xl sm:rounded-bl-none rounded-b-3xl',
                  'flex flex-col p-content-y bg-card shadow-xl ring-1 ring-border'
                )}
              >
                <div className="flex items-center justify-between gap-x-element-x">
                  <h3
                    id={`tier-${tierIdx}`}
                    className={classNames(
                      tier.mostPopular ? 'text-primary' : 'text-foreground',
                      'text-heading-4/heading-4 font-semibold'
                    )}
                  >
                    {tier.name}
                  </h3>
                  {tier.mostPopular && (
                    <p className="rounded-full bg-primary/10 px-element-x py-0.5 text-body-small/body-small font-semibold text-primary">
                      Most popular
                    </p>
                  )}
                </div>
                <p className="mt-element-y text-body-small/body-small text-muted-foreground">{tier.description}</p>
                <div className="mt-element-y flex items-baseline gap-x-element-x">
                  <span className="text-heading-2/heading-2 font-bold tracking-tight text-foreground">
                    {formatPrice(getPriceDisplayForTier(tier), getCurrency(tier), '')}
                  </span>
                  <span className="text-body-small/body-small font-semibold text-muted-foreground">
                    {getCurrentSuffix()}
                  </span>
                </div>
                
                {/* 年度节省提示 */}
                {savingsPercent && (
                  <p className="mt-element-y text-body-small/body-small font-medium text-green-600 dark:text-green-400">
                    Save {savingsPercent}% with annual billing
                  </p>
                )}
                
                {/* 价格描述 */}
                {getPriceDescriptionForTier(tier) && (
                  <p className="mt-element-y text-body-small/body-small text-muted-foreground min-h-[40px]">
                    {getPriceDescriptionForTier(tier)}
                  </p>
                )}
                
                <Button
                  href={tier.button.href}
                  variant={tier.mostPopular ? "primary" : "outline"}
                  className="mt-element-y w-full"
                  aria-describedby={`tier-${tierIdx}`}
                  aria-label={`Get ${tier.name} plan`}
                >
                  {tier.button.label}
                </Button>
                
                <ul role="list" className="mt-content-y space-y-element-y text-body-small/body-small text-muted-foreground flex-1">
                  {tier.features.map((feature, featureIdx) => (
                    <li key={`feature-${tierIdx}-${featureIdx}`} className="flex gap-x-element-x">
                      <CheckIcon className="h-6 w-5 flex-none text-primary" aria-hidden="true" />
                      <span className="break-words">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            );
          })}
        </div>
        
        {/* 第三和第四个定价方案 */}
        <div className="mx-auto mt-content-y grid max-w-container grid-cols-1 gap-content-y sm:gap-y-0 lg:grid-cols-2">
          {tiers.slice(2, 4).map((tier, tierIdx) => {
            const savingsPercent = calculateAnnualSavingsPercent(tier);
            const actualIdx = tierIdx + 2; // 调整索引以匹配原始数组中的位置
            
            return (
              <div
                key={`tier-${actualIdx}`}
                className={classNames(
                  actualIdx === 2 ? 'rounded-t-3xl sm:rounded-tr-none sm:rounded-l-3xl' : 'sm:rounded-tr-3xl sm:rounded-bl-none rounded-b-3xl',
                  'flex flex-col p-content-y bg-card shadow-xl ring-1 ring-border'
                )}
              >
                <div className="flex items-center justify-between gap-x-element-x">
                  <h3
                    id={`tier-${actualIdx}`}
                    className={classNames(
                      tier.mostPopular ? 'text-primary' : 'text-foreground',
                      'text-heading-4/heading-4 font-semibold'
                    )}
                  >
                    {tier.name}
                  </h3>
                  {tier.mostPopular && (
                    <p className="rounded-full bg-primary/10 px-element-x py-0.5 text-body-small/body-small font-semibold text-primary">
                      Most popular
                    </p>
                  )}
                </div>
                <p className="mt-element-y text-body-small/body-small text-muted-foreground">{tier.description}</p>
                <div className="mt-element-y flex items-baseline gap-x-element-x">
                  <span className="text-heading-2/heading-2 font-bold tracking-tight text-foreground">
                    {formatPrice(getPriceDisplayForTier(tier), getCurrency(tier), '')}
                  </span>
                  <span className="text-body-small/body-small font-semibold text-muted-foreground">
                    {getCurrentSuffix()}
                  </span>
                </div>
                
                {/* 年度节省提示 */}
                {savingsPercent && (
                  <p className="mt-element-y text-body-small/body-small font-medium text-green-600 dark:text-green-400">
                    Save {savingsPercent}% with annual billing
                  </p>
                )}
                
                {/* 价格描述 */}
                {getPriceDescriptionForTier(tier) && (
                  <p className="mt-element-y text-body-small/body-small text-muted-foreground min-h-[40px]">
                    {getPriceDescriptionForTier(tier)}
                  </p>
                )}
                
                <Button
                  href={tier.button.href}
                  variant={tier.mostPopular ? "primary" : "outline"}
                  className="mt-element-y w-full"
                  aria-describedby={`tier-${actualIdx}`}
                  aria-label={`Get ${tier.name} plan`}
                >
                  {tier.button.label}
                </Button>
                
                <ul role="list" className="mt-content-y space-y-element-y text-body-small/body-small text-muted-foreground flex-1">
                  {tier.features.map((feature, featureIdx) => (
                    <li key={`feature-${actualIdx}-${featureIdx}`} className="flex gap-x-element-x">
                      <CheckIcon className="h-6 w-5 flex-none text-primary" aria-hidden="true" />
                      <span className="break-words">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            );
          })}
        </div>
        
        {/* 自定义解决方案区域 */}
        {customSolution?.enabled && (
          <div className="mx-auto mt-section-y max-w-container rounded-2xl bg-muted p-content-y px-container-x lg:flex lg:items-center lg:justify-between">
            <div className="lg:w-2/3">
              <h3 id="custom-solution" className="text-heading-3/heading-3 font-bold tracking-tight text-foreground">
                {customSolution.title || "Need a custom solution?"}
              </h3>
              <p className="mt-element-y text-body-large/body-large text-muted-foreground">
                {customSolution.description || "Get in touch with our sales team and we'll help you find the perfect solution for your business needs."}
              </p>
            </div>
            <div className="mt-content-y lg:mt-0 lg:ml-content-x">
              <Button
                href={customSolution.button?.href || "#"}
                variant="primary"
                aria-describedby="custom-solution"
              >
                {customSolution.button?.label || "Contact sales"}
              </Button>
            </div>
          </div>
        )}
      </div>
    </section>
  );
}
