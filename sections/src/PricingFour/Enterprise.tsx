'use client'

import { useState, useEffect } from 'react'
import { Check<PERSON>con, ShieldCheckIcon } from '@heroicons/react/20/solid'
import PriceDisplay, { formatPrice } from './PriceDisplay'
import { PricingFourSectionProps, Tier, Frequency } from './types'
import { getPriceDisplay, getCurrency, getPriceDescription } from './utils'
import { Button } from '../components/ButtonV2'

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ')
}

export default function Enterprise({
  id,
  tagline = 'Pricing',
  title = 'Choose the right plan for you',
  description = 'Choose an affordable plan that\'s packed with the best features for engaging your audience, creating customer loyalty, and driving sales.',
  frequencies,
  tiers = [],
  customSolution
}: PricingFourSectionProps) {
  // 默认频率为 monthly
  const [selectedFrequency, setSelectedFrequency] = useState<string>('monthly');
  
  // 根据 billingPeriod 设置默认频率
  useEffect(() => {
    if (!frequencies?.enabled && frequencies?.billingPeriod) {
      const periodType = frequencies.billingPeriod.type;
      if (periodType === 'monthly') {
        setSelectedFrequency('monthly');
      } else if (periodType === 'annually') {
        setSelectedFrequency('annually');
      }
    }
  }, [frequencies]);
  
  // 获取频率选项
  const frequencyOptions: Frequency[] = frequencies?.enabled && frequencies.options 
    ? frequencies.options 
    : [{ value: 'monthly', label: 'Monthly', priceSuffix: '/month' }];
  
  const getCurrentSuffix = () => {
    // 如果频率选择器关闭但设置了 billingPeriod
    if (!frequencies?.enabled && frequencies?.billingPeriod) {
      const periodType = frequencies.billingPeriod.type;
      if (periodType === 'one-time') return '';
      if (periodType === 'monthly') return '/mo';
      if (periodType === 'annually') return '/yr';
      if (periodType === 'per-user') return '/user';
      if (periodType === 'custom' && frequencies.billingPeriod.customText) {
        return frequencies.billingPeriod.customText;
      }
    }
    
    const option = frequencyOptions.find(opt => opt.value === selectedFrequency);
    return option?.priceSuffix || '/mo';
  };
  
  // 获取价格显示
  const getPriceDisplayForTier = (tier: Tier): number => {
    return getPriceDisplay(tier, selectedFrequency, frequencies);
  };

  // 获取价格描述
  const getPriceDescriptionForTier = (tier: Tier): string | undefined => {
    return getPriceDescription(tier, selectedFrequency, frequencies);
  };
  
  // 计算年度节省百分比
  const calculateAnnualSavingsPercent = (tier: Tier): number | null => {
    if ('monthly' in tier.price && 'annually' in tier.price && selectedFrequency === 'annually') {
      const monthlyCost = tier.price.monthly * 12;
      const annualCost = tier.price.annually;
      
      if (monthlyCost > 0) {
        return Math.round((1 - (annualCost / monthlyCost)) * 100);
      }
    }
    return null;
  };

  return (
    <section 
      className="bg-gradient-to-b from-slate-50 to-white dark:from-slate-900 dark:to-slate-800 py-section-y"
      style={{
        // Enterprise风格的按钮样式覆盖
        '--button-radius': '0.5rem',
        '--button-shadow': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        '--button-hover-shadow': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        '--button-transition-duration-normal': '200ms',
        '--button-font-weight': '600',
      } as React.CSSProperties}
    >
      <div className="mx-auto max-w-container px-container-x">
        <div className="mx-auto max-w-content text-center">
          {tagline && (
            <div className="inline-flex items-center gap-x-element-x rounded-full bg-slate-100 dark:bg-slate-800 px-element-x py-element-y text-body-small/body-small font-semibold text-slate-700 dark:text-slate-300 ring-1 ring-inset ring-slate-200 dark:ring-slate-700">
              <ShieldCheckIcon className="h-4 w-4" aria-hidden="true" />
              {tagline}
            </div>
          )}
          <h2 id={id || 'pricing'} className="mt-element-y text-heading-1/heading-1 font-bold tracking-tight text-slate-900 dark:text-slate-100">
            {title}
          </h2>
        </div>
        <p className="mx-auto mt-element-y max-w-content text-center text-body-large/body-large text-slate-600 dark:text-slate-400">
          {description}
        </p>
        
        {/* 频率选择开关 */}
        {frequencies?.enabled && frequencyOptions.length > 1 && (
          <div className="mt-content-y flex justify-center">
            <div aria-label="Payment frequency" aria-describedby="frequency-description" className="grid grid-cols-2 gap-x-element-x rounded-lg bg-slate-100 dark:bg-slate-800 p-element-x text-center text-body-small/body-small font-semibold ring-1 ring-slate-200 dark:ring-slate-700">
              <div id="frequency-description" className="sr-only">Select your preferred billing frequency</div>
              {frequencyOptions.map((option) => (
                <button
                  key={option.value}
                  className={classNames(
                    option.value === selectedFrequency
                      ? 'bg-slate-900 text-white dark:bg-slate-100 dark:text-slate-900 shadow-sm'
                      : 'text-slate-600 hover:text-slate-900 dark:text-slate-400 dark:hover:text-slate-100',
                    'cursor-pointer rounded-md px-element-x py-element-y transition-all duration-200'
                  )}
                  onClick={() => setSelectedFrequency(option.value)}
                >
                  {option.label}
                </button>
              ))}
            </div>
          </div>
        )}
        
        <div className="isolate mx-auto mt-content-y grid max-w-container grid-cols-1 gap-content-y lg:grid-cols-4">
          {tiers.map((tier, tierIdx) => {
            const savingsPercent = calculateAnnualSavingsPercent(tier);
            
            return (
              <div
                key={`tier-${tierIdx}`}
                className={classNames(
                  tier.mostPopular 
                    ? 'bg-slate-900 text-white dark:bg-slate-100 dark:text-slate-900 ring-2 ring-slate-900 dark:ring-slate-100 shadow-xl' 
                    : 'bg-white dark:bg-slate-800 text-slate-900 dark:text-slate-100 ring-1 ring-slate-200 dark:ring-slate-700 shadow-lg',
                  'relative rounded-2xl p-content-y transition-all duration-300 hover:shadow-xl'
                )}
              >
                <div className="flex items-center justify-between gap-x-element-x">
                  <h3
                    id={`tier-${tierIdx}`}
                    className="text-heading-4/heading-4 font-bold"
                  >
                    {tier.name}
                  </h3>
                  {tier.mostPopular && (
                    <p className={classNames(
                      tier.mostPopular ? 'bg-white/20 text-white dark:bg-slate-900/20 dark:text-slate-900' : '',
                      'rounded-full px-element-x py-0.5 text-body-small/body-small font-semibold'
                    )} aria-label="Most popular plan">
                      Most popular
                    </p>
                  )}
                </div>
                <p className={classNames(
                  tier.mostPopular ? 'text-white/80 dark:text-slate-900/80' : 'text-slate-600 dark:text-slate-400',
                  'mt-element-y text-body-small/body-small'
                )}>
                  {tier.description}
                </p>
                <p className="mt-element-y flex items-baseline gap-x-element-x">
                  <span className="text-heading-2/heading-2 font-bold tracking-tight">
                    {formatPrice(getPriceDisplayForTier(tier), getCurrency(tier), '')}
                  </span>
                  <span className={classNames(
                    tier.mostPopular ? 'text-white/80 dark:text-slate-900/80' : 'text-slate-600 dark:text-slate-400',
                    'text-body-small/body-small font-semibold'
                  )}>
                    {getCurrentSuffix()}
                  </span>
                </p>
                
                {/* 年度节省提示 */}
                {savingsPercent && (
                  <p className={classNames(
                    tier.mostPopular ? 'text-white/80 dark:text-slate-900/80' : 'text-emerald-600 dark:text-emerald-400',
                    'mt-element-y text-body-small/body-small font-medium'
                  )}>
                    Save {savingsPercent}% with annual billing
                  </p>
                )}
                
                {/* 价格描述 */}
                {getPriceDescriptionForTier(tier) && (
                  <p className={classNames(
                    tier.mostPopular ? 'text-white/80 dark:text-slate-900/80' : 'text-slate-600 dark:text-slate-400',
                    'mt-element-y text-body-small/body-small min-h-[40px]'
                  )}>
                    {getPriceDescriptionForTier(tier)}
                  </p>
                )}
                
                <Button
                  href={tier.button.href}
                  variant={tier.mostPopular ? "secondary" : "primary"}
                  className="mt-element-y w-full"
                  aria-describedby={`tier-${tierIdx}`}
                  aria-label={`Get ${tier.name} plan`}
                  style={tier.mostPopular ? {
                    backgroundColor: 'rgba(255, 255, 255, 0.9)',
                    color: '#0f172a',
                    '--button-hover-bg': 'white',
                    border: 'none'
                  } as React.CSSProperties : {
                    backgroundColor: '#0f172a',
                    color: 'white',
                    '--button-hover-bg': '#1e293b',
                    border: 'none'
                  } as React.CSSProperties}
                >
                  {tier.button.label}
                </Button>
                
                <ul role="list" className={classNames(
                  tier.mostPopular ? 'text-white/80 dark:text-slate-900/80' : 'text-slate-600 dark:text-slate-400',
                  'mt-content-y space-y-element-y text-body-small/body-small'
                )}>
                  {tier.features.map((feature, featureIdx) => (
                    <li key={`feature-${tierIdx}-${featureIdx}`} className="flex gap-x-element-x">
                      <CheckIcon className={classNames(
                        tier.mostPopular ? 'text-white dark:text-slate-900' : 'text-slate-900 dark:text-slate-100',
                        'h-6 w-5 flex-none'
                      )} aria-hidden="true" />
                      <span className="break-words">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            );
          })}
        </div>
        
        {/* 自定义解决方案区域 */}
        {customSolution?.enabled && (
          <div className="mx-auto mt-section-y max-w-container rounded-2xl bg-slate-100 dark:bg-slate-800 p-content-y px-container-x lg:flex lg:items-center lg:justify-between ring-1 ring-slate-200 dark:ring-slate-700">
            <div className="lg:w-2/3">
              <h3 id="custom-solution" className="text-heading-3/heading-3 font-bold tracking-tight text-slate-900 dark:text-slate-100">
                {customSolution.title || "Need a custom solution?"}
              </h3>
              <p className="mt-element-y text-body-large/body-large text-slate-600 dark:text-slate-400">
                {customSolution.description || "Get in touch with our sales team and we'll help you find the perfect solution for your business needs."}
              </p>
            </div>
            <div className="mt-content-y lg:mt-0 lg:ml-content-x">
              <Button
                href={customSolution.button?.href || "#"}
                variant="primary"
                aria-describedby="custom-solution"
                style={{
                  backgroundColor: '#0f172a',
                  color: 'white',
                  '--button-hover-bg': '#1e293b',
                  border: 'none'
                } as React.CSSProperties}
              >
                {customSolution.button?.label || "Contact sales"}
              </Button>
            </div>
          </div>
        )}
      </div>
    </section>
  );
}
