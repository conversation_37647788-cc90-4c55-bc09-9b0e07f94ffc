'use client'

import { useState, useEffect } from 'react'
import { CheckIcon } from '@heroicons/react/20/solid'
import { PricingFourSectionProps, Tier, Frequency } from './types'
import { getPriceDisplay, getCurrency, getPriceDescription } from './utils'
import { formatPrice } from './PriceDisplay'
import { Button } from '../components/ButtonV2'

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ')
}

export default function Background({
  id,
  tagline = 'Pricing',
  title = 'Choose the right plan for you',
  description = 'Choose an affordable plan that\'s packed with the best features for engaging your audience, creating customer loyalty, and driving sales.',
  frequencies,
  tiers = [],
  customSolution
}: PricingFourSectionProps) {
  // 默认频率为 monthly
  const [selectedFrequency, setSelectedFrequency] = useState<string>('monthly');
  
  // 根据 billingPeriod 设置默认频率
  useEffect(() => {
    if (!frequencies?.enabled && frequencies?.billingPeriod) {
      const periodType = frequencies.billingPeriod.type;
      if (periodType === 'monthly') {
        setSelectedFrequency('monthly');
      } else if (periodType === 'annually') {
        setSelectedFrequency('annually');
      }
    }
  }, [frequencies]);
  
  // 获取频率选项
  const frequencyOptions: Frequency[] = frequencies?.enabled && frequencies.options 
    ? frequencies.options 
    : [{ value: 'monthly', label: 'Monthly', priceSuffix: '/month' }];
  
  // 获取当前频率的后缀
  const getCurrentSuffix = () => {
    // 如果频率选择器关闭但设置了 billingPeriod
    if (!frequencies?.enabled && frequencies?.billingPeriod) {
      const periodType = frequencies.billingPeriod.type;
      if (periodType === 'one-time') return '';
      if (periodType === 'monthly') return '/mo';
      if (periodType === 'annually') return '/yr';
      if (periodType === 'per-user') return '/user';
      if (periodType === 'custom' && frequencies.billingPeriod.customText) {
        return frequencies.billingPeriod.customText;
      }
    }
    
    const option = frequencyOptions.find(opt => opt.value === selectedFrequency);
    return option?.priceSuffix || '/mo';
  };
  
  // 获取价格显示
  const getPriceDisplayForTier = (tier: Tier): number => {
    return getPriceDisplay(tier, selectedFrequency, frequencies);
  };

  // 获取价格描述
  const getPriceDescriptionForTier = (tier: Tier): string | undefined => {
    return getPriceDescription(tier, selectedFrequency, frequencies);
  };
  
  // 计算年度节省百分比
  const calculateAnnualSavingsPercent = (tier: Tier): number | null => {
    if ('monthly' in tier.price && 'annually' in tier.price && selectedFrequency === 'annually') {
      const monthlyCost = tier.price.monthly * 12;
      const annualCost = tier.price.annually;
      
      if (monthlyCost > 0) {
        return Math.round((1 - (annualCost / monthlyCost)) * 100);
      }
    }
    return null;
  };

  return (
    <section 
      className="bg-background py-section-y"
      style={{
        // Background风格的按钮样式覆盖
        '--button-radius': '0.375rem',
        '--button-shadow': '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
        '--button-hover-shadow': '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
        '--button-transition-duration-normal': '200ms',
        '--button-font-weight': '600',
      } as React.CSSProperties}
    >
      <div className="mx-auto max-w-container px-container-x">
        <div className="mx-auto max-w-content text-center">
          {tagline && (
            <p className="text-body-base/body-base font-semibold text-primary">{tagline}</p>
          )}
          <h2 id={id || 'pricing'} className="mt-element-y text-heading-1/heading-1 font-bold tracking-tight text-foreground">
            {title}
          </h2>
        </div>
        <p className="mx-auto mt-element-y max-w-content text-center text-body-large/body-large text-muted-foreground">
          {description}
        </p>
        
        {/* 频率选择开关 */}
        {frequencies?.enabled && frequencyOptions.length > 1 && (
          <div className="mt-content-y flex justify-center">
            <div aria-label="Payment frequency" aria-describedby="frequency-description" className="grid grid-cols-2 gap-x-element-x rounded-full p-element-x text-center text-body-small/body-small font-semibold ring-1 ring-inset ring-border bg-muted/50">
              <div id="frequency-description" className="sr-only">Select your preferred billing frequency</div>
              {frequencyOptions.map((option) => (
                <button
                  key={option.value}
                  className={classNames(
                    option.value === selectedFrequency
                      ? 'bg-primary text-primary-foreground'
                      : 'text-muted-foreground hover:text-foreground',
                    'cursor-pointer rounded-full px-element-x py-element-y'
                  )}
                  onClick={() => setSelectedFrequency(option.value)}
                >
                  {option.label}
                </button>
              ))}
            </div>
          </div>
        )}
        
        <div className="isolate mx-auto mt-content-y grid max-w-container grid-cols-1 gap-content-y lg:grid-cols-4">
          {tiers.map((tier, tierIdx) => {
            const savingsPercent = calculateAnnualSavingsPercent(tier);
            
            return (
              <div
                key={`tier-${tierIdx}`}
                className={classNames(
                  tier.mostPopular ? 'bg-foreground/5 ring-2 ring-primary' : 'ring-1 ring-foreground/10',
                  'rounded-3xl p-content-y'
                )}
              >
                <div className="flex items-center justify-between gap-x-element-x">
                  <h3
                    id={`tier-${tierIdx}`}
                    className={classNames(
                      tier.mostPopular ? 'text-primary' : 'text-foreground',
                      'text-heading-4/heading-4 font-semibold'
                    )}
                  >
                    {tier.name}
                  </h3>
                  {tier.mostPopular && (
                    <p className="rounded-full bg-primary px-element-x py-0.5 text-body-small/body-small font-semibold text-primary-foreground" aria-label="Most popular plan">
                      Most popular
                    </p>
                  )}
                </div>
                <p className="mt-element-y text-body-small/body-small text-muted-foreground">{tier.description}</p>
                <p className="mt-element-y flex items-baseline gap-x-element-x">
                  <span className="text-heading-2/heading-2 font-bold tracking-tight text-foreground">
                    {formatPrice(getPriceDisplayForTier(tier), getCurrency(tier), '')}
                  </span>
                  <span className="text-body-small/body-small font-semibold text-muted-foreground">
                    {getCurrentSuffix()}
                  </span>
                </p>
                
                {/* 年度节省提示 */}
                {savingsPercent && (
                  <p className="mt-element-y text-body-small/body-small font-medium text-green-400 dark:text-green-300">
                    Save {savingsPercent}% with annual billing
                  </p>
                )}
                
                {/* 价格描述 */}
                {getPriceDescriptionForTier(tier) && (
                  <p className="mt-element-y text-body-small/body-small text-muted-foreground min-h-[40px]">
                    {getPriceDescriptionForTier(tier)}
                  </p>
                )}
                
                <Button
                  href={tier.button.href}
                  variant={tier.mostPopular ? "primary" : "outline"}
                  className="mt-element-y w-full"
                  aria-describedby={`tier-${tierIdx}`}
                  aria-label={`Get ${tier.name} plan`}
                >
                  {tier.button.label}
                </Button>
                
                <ul role="list" className="mt-content-y space-y-element-y text-body-small/body-small text-muted-foreground">
                  {tier.features.map((feature, featureIdx) => (
                    <li key={`feature-${tierIdx}-${featureIdx}`} className="flex gap-x-element-x">
                      <CheckIcon className="h-6 w-5 flex-none text-primary" aria-hidden="true" />
                      <span className="break-words">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            );
          })}
        </div>
        
        {/* 自定义解决方案区域 */}
        {customSolution?.enabled && (
          <div className="mx-auto mt-section-y max-w-container rounded-2xl bg-foreground/5 p-content-y px-container-x lg:flex lg:items-center lg:justify-between">
            <div className="lg:w-2/3">
              <h3 id="custom-solution" className="text-heading-3/heading-3 font-bold tracking-tight text-foreground">
                {customSolution.title || "Need a custom solution?"}
              </h3>
              <p className="mt-element-y text-body-large/body-large text-muted-foreground">
                {customSolution.description || "Get in touch with our sales team and we'll help you find the perfect solution for your business needs."}
              </p>
            </div>
            <div className="mt-content-y lg:mt-0 lg:ml-content-x">
              <Button
                href={customSolution.button?.href || "#"}
                variant="primary"
                aria-describedby="custom-solution"
              >
                {customSolution.button?.label || "Contact sales"}
              </Button>
            </div>
          </div>
        )}
      </div>
    </section>
  );
}
