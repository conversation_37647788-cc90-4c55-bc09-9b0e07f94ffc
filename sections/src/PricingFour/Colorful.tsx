'use client'

import React, { useState, useEffect } from 'react'
import { CheckIcon, SparklesIcon, StarIcon } from '@heroicons/react/20/solid'
import { PricingFourSectionProps, Tier, Frequency } from './types'
import { getPriceDisplay, getCurrency, getPriceDescription } from './utils'
import { formatPrice } from './PriceDisplay'
import { Button } from '../components/ButtonV2'

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ')
}

// 为每个卡片定义不同的颜色主题
const cardThemes = [
  {
    bgGradient: 'bg-gradient-to-br from-pink-500 to-rose-500',
    textColor: 'text-white',
    secondaryTextColor: 'text-pink-100',
    buttonBg: 'bg-white/90',
    buttonText: 'text-pink-600',
    buttonHover: 'hover:bg-white',
    checkIconColor: 'text-pink-200',
    ringColor: 'ring-pink-300/50',
    hoverShadow: 'hover:shadow-pink-500/20',
    featureBgColor: 'bg-pink-500/20',
  },
  {
    bgGradient: 'bg-gradient-to-br from-indigo-500 to-purple-500',
    textColor: 'text-white',
    secondaryTextColor: 'text-indigo-100',
    buttonBg: 'bg-white/90',
    buttonText: 'text-indigo-600',
    buttonHover: 'hover:bg-white',
    checkIconColor: 'text-indigo-200',
    ringColor: 'ring-indigo-300/50',
    hoverShadow: 'hover:shadow-indigo-500/20',
    featureBgColor: 'bg-indigo-500/20',
  },
  {
    bgGradient: 'bg-gradient-to-br from-emerald-500 to-teal-500',
    textColor: 'text-white',
    secondaryTextColor: 'text-emerald-100',
    buttonBg: 'bg-white/90',
    buttonText: 'text-emerald-600',
    buttonHover: 'hover:bg-white',
    checkIconColor: 'text-emerald-200',
    ringColor: 'ring-emerald-300/50',
    hoverShadow: 'hover:shadow-emerald-500/20',
    featureBgColor: 'bg-emerald-500/20',
  },
  {
    bgGradient: 'bg-gradient-to-br from-amber-500 to-orange-500',
    textColor: 'text-white',
    secondaryTextColor: 'text-amber-100',
    buttonBg: 'bg-white/90',
    buttonText: 'text-amber-600',
    buttonHover: 'hover:bg-white',
    checkIconColor: 'text-amber-200',
    ringColor: 'ring-amber-300/50',
    hoverShadow: 'hover:shadow-amber-500/20',
    featureBgColor: 'bg-amber-500/20',
  }
];

export default function Colorful({
  id,
  tagline = 'Pricing',
  title = 'Choose the right plan for you',
  description = 'Choose an affordable plan that\'s packed with the best features for engaging your audience, creating customer loyalty, and driving sales.',
  frequencies,
  tiers = [],
  customSolution
}: PricingFourSectionProps) {
  // 默认频率为 monthly
  const [selectedFrequency, setSelectedFrequency] = useState<string>('monthly');
  
  // 根据 billingPeriod 设置默认频率
  useEffect(() => {
    if (!frequencies?.enabled && frequencies?.billingPeriod) {
      const periodType = frequencies.billingPeriod.type;
      if (periodType === 'monthly') {
        setSelectedFrequency('monthly');
      } else if (periodType === 'annually') {
        setSelectedFrequency('annually');
      }
    }
  }, [frequencies]);
  
  // 获取频率选项
  const frequencyOptions: Frequency[] = frequencies?.enabled && frequencies.options 
    ? frequencies.options 
    : [{ value: 'monthly', label: 'Monthly', priceSuffix: '/month' }];
  
  // 获取当前频率的后缀
  const getCurrentSuffix = () => {
    // 如果频率选择器关闭但设置了 billingPeriod
    if (!frequencies?.enabled && frequencies?.billingPeriod) {
      const periodType = frequencies.billingPeriod.type;
      if (periodType === 'one-time') return '';
      if (periodType === 'monthly') return '/mo';
      if (periodType === 'annually') return '/yr';
      if (periodType === 'per-user') return '/user';
      if (periodType === 'custom' && frequencies.billingPeriod.customText) {
        return frequencies.billingPeriod.customText;
      }
    }
    
    const option = frequencyOptions.find(opt => opt.value === selectedFrequency);
    return option?.priceSuffix || '/mo';
  };

  // 计算年度节省金额
  const calculateAnnualSavings = (tier: Tier): string | null => {
    if (!('monthly' in tier.price && 'annually' in tier.price)) return null;
    
    const monthlyTotal = tier.price.monthly * 12;
    const annualPrice = tier.price.annually;
    const savings = monthlyTotal - annualPrice;
    
    if (savings > 0) {
      return `${getCurrency(tier)}${savings}`;
    }
    
    return null;
  };

  return (
    <section 
      className="pricing-colorful relative isolate bg-background px-container-x py-section-y" 
      aria-labelledby="pricing-title"
      style={{
        // Colorful风格的按钮样式覆盖
        '--button-radius': '9999px', // rounded-full
        '--button-shadow': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        '--button-hover-shadow': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        '--button-transition-duration-normal': '200ms',
        '--button-font-weight': '600',
      } as React.CSSProperties}
    >
      {/* 背景装饰 */}
      <div
        className="absolute inset-0 -z-10 h-full w-full opacity-30"
        style={{
          backgroundImage: 'radial-gradient(circle at 30% 20%, rgba(255, 128, 128, 0.8), transparent 35%), radial-gradient(circle at 70% 60%, rgba(128, 128, 255, 0.8), transparent 35%)',
          backgroundSize: '100% 100%',
          filter: 'blur(50px)'
        }}
        aria-hidden="true"
      />
      <div
        className="absolute inset-y-0 right-1/2 -z-10 mr-16 w-[200%] origin-bottom-left skew-x-[-30deg] bg-muted/80 shadow-xl shadow-primary/10 ring-1 ring-foreground/10"
        aria-hidden="true"
      />
      
      <div className="mx-auto max-w-content text-center">
        {tagline && (
          <div className="inline-flex items-center justify-center">
            <span className="relative inline-flex overflow-hidden rounded-full p-[1px] transition-all duration-300 hover:scale-105 motion-reduce:hover:scale-100">
              <span className="absolute inset-0 rounded-full bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-500 opacity-75" aria-hidden="true" />
              <p className="inline-flex h-full w-full cursor-pointer items-center justify-center rounded-full bg-background px-element-x py-element-y text-body-small/body-small font-medium text-purple-400 backdrop-blur-3xl relative z-10">
                {tagline}
              </p>
            </span>
          </div>
        )}
        <h2 id={id || 'pricing'} className="mt-element-y text-heading-1/heading-1 font-bold tracking-tight text-balance bg-clip-text text-transparent bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-500 dark:from-pink-300 dark:via-purple-300 dark:to-indigo-300">
          {title}
        </h2>
      </div>
      <p className="mx-auto mt-element-y max-w-content text-pretty text-center text-body-large/body-large text-muted-foreground">
        {description}
      </p>
      
      {/* 频率选择开关 */}
      {frequencies?.enabled && frequencyOptions.length > 1 && (
        <div className="mx-auto mt-content-y flex max-w-container justify-center">
          <div aria-label="Payment frequency" aria-describedby="frequency-description" className="relative flex rounded-full bg-muted/80 p-element-x backdrop-blur-xl ring-1 ring-foreground/10 transition-all duration-200 hover:ring-foreground/20">
            <div id="frequency-description" className="sr-only">Select your preferred billing frequency</div>
            <div
              className="absolute inset-0 overflow-hidden rounded-full"
              style={{
                background: 'linear-gradient(90deg, rgba(255,128,181,0.3) 0%, rgba(144,137,252,0.3) 50%, rgba(128,255,234,0.3) 100%)',
                opacity: 0.3
              }}
            />
            {frequencyOptions.map((option) => (
              <button
                key={option.value}
                type="button"
                className={classNames(
                  option.value === selectedFrequency
                    ? 'relative bg-gradient-to-r from-indigo-500 to-purple-500 text-primary-foreground shadow-lg'
                    : 'text-muted-foreground hover:text-foreground',
                  'z-10 rounded-full px-content-x py-element-y text-body-small/body-small font-semibold transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:ring-offset-background'
                )}
                onClick={() => setSelectedFrequency(option.value)}
              >
                {option.value === 'annually' && (
                  <span className="absolute -top-3 right-0 flex items-center justify-center rounded-full bg-green-400 px-element-x py-element-y text-body-small/body-small font-medium text-background ring-1 ring-inset ring-green-600/20 transition-transform duration-200 hover:scale-105 motion-reduce:hover:scale-100">
                    Save 16%
                  </span>
                )}
                {option.label}
              </button>
            ))}
          </div>
        </div>
      )}
      
      <div className="mx-auto mt-content-y grid max-w-container grid-cols-1 gap-content-y lg:grid-cols-4">
        {tiers.map((tier, tierIdx) => {
          // 使用预定义的主题或默认主题
          const theme = cardThemes[tierIdx % cardThemes.length];
          
          return (
            <div
              key={`tier-${tierIdx}`}
              className={classNames(
                theme.bgGradient,
                theme.ringColor,
                theme.hoverShadow,
                'group relative rounded-3xl p-content-y shadow-2xl transform transition-all duration-300 hover:scale-[1.02] hover:shadow-2xl ring-1 motion-reduce:hover:scale-100 motion-reduce:transition-none'
              )}
            >
              {tier.mostPopular && (
                <div className="absolute -top-5 -right-5 flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-r from-yellow-400 to-amber-400 text-body-small/body-small font-bold uppercase text-gray-900 shadow-lg transition-transform duration-200 group-hover:scale-105 motion-reduce:group-hover:scale-100" aria-label="Most popular plan">
                  <div className="relative">
                    <StarIcon className="h-6 w-6 text-gray-900" aria-hidden="true" />
                    <span className="absolute inset-0 flex items-center justify-center text-[10px] font-bold">
                      TOP
                    </span>
                  </div>
                </div>
              )}
              <h3
                id={`tier-${tierIdx}`}
                className={classNames(theme.textColor, 'text-heading-4/heading-4 font-bold')}
              >
                {tier.name}
              </h3>
              <div className={classNames(theme.textColor, 'mt-element-y flex items-baseline gap-x-element-x')}>
                <span className="text-heading-1/heading-1 font-extrabold tracking-tight">
                  {formatPrice(getPriceDisplay(tier, selectedFrequency, frequencies), getCurrency(tier), '')}
                </span>
                <span className={theme.secondaryTextColor}>
                  {getCurrentSuffix()}
                </span>
              </div>
              
              {/* 年度节省提示 */}
              {calculateAnnualSavings(tier) && (
                <p className={classNames(
                  theme.secondaryTextColor,
                  'mt-element-y text-body-small/body-small font-medium'
                )}>
                  Save {calculateAnnualSavings(tier)} per year
                </p>
              )}
              
              {/* 价格描述 */}
              {getPriceDescription(tier, selectedFrequency, frequencies) && (
                <p className={classNames(
                  theme.secondaryTextColor,
                  'mt-element-y text-body-small/body-small min-h-[40px]'
                )}>
                  {getPriceDescription(tier, selectedFrequency, frequencies)}
                </p>
              )}
              
              <p className={classNames(theme.secondaryTextColor, 'mt-element-y text-body-base/body-base')}>
                {tier.description}
              </p>
              
              <Button
                href={tier.button.href}
                variant="secondary"
                className="mt-content-y w-full transition-transform duration-200 hover:scale-[1.02] motion-reduce:hover:scale-100 motion-reduce:transition-none"
                aria-describedby={`tier-${tierIdx}`}
                aria-label={`Get ${tier.name} plan`}
                style={{
                  backgroundColor: theme.buttonBg,
                  color: theme.buttonText,
                  '--button-hover-bg': theme.buttonHover.replace('hover:', ''),
                  backdropFilter: 'blur(4px)',
                  border: 'none'
                } as React.CSSProperties}
              >
                {tier.button.label}
              </Button>
              
              <ul
                role="list"
                className={classNames(
                  theme.secondaryTextColor,
                  theme.featureBgColor,
                  'mt-content-y space-y-element-y text-body-small/body-small rounded-2xl p-content-y'
                )}
              >
                {tier.features.map((feature, featureIdx) => (
                  <li key={`feature-${tierIdx}-${featureIdx}`} className="flex gap-x-element-x items-center">
                    <CheckIcon
                      aria-hidden="true"
                      className={classNames(theme.checkIconColor, 'h-5 w-5 flex-none')}
                    />
                    <span className="break-words">{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
          );
        })}
      </div>
      
      {/* 自定义解决方案区域 */}
      {customSolution?.enabled && (
        <div className="mx-auto mt-section-y max-w-container px-container-x">
          <div className="relative overflow-hidden rounded-3xl p-content-y shadow-lg ring-1 ring-white/10 transition-all duration-300 hover:shadow-xl hover:ring-white/20">
            <div
              className="absolute inset-0 overflow-hidden"
              style={{
                background: 'linear-gradient(90deg, rgba(255,128,181,0.15) 0%, rgba(144,137,252,0.15) 50%, rgba(128,255,234,0.15) 100%)',
                backdropFilter: 'blur(10px)',
              }}
            />
            <div className="relative flex flex-col items-start gap-content-y lg:flex-row lg:items-center lg:justify-between">
              <div>
                <h3 id="custom-solution" className="text-heading-4/heading-4 font-bold bg-clip-text text-transparent bg-gradient-to-r from-pink-300 to-purple-300">
                  {customSolution.title || 'Need a custom solution?'}
                </h3>
                <p className="mt-element-y text-body-base/body-base text-muted-foreground">
                  {customSolution.description || 'Get in touch with our sales team and we\'ll help you find the perfect solution for your business needs.'}
                </p>
              </div>
              {customSolution.button && (
                <Button
                  href={customSolution.button.href || "#"}
                  variant="secondary"
                  className="transition-transform duration-200 hover:scale-[1.02] motion-reduce:hover:scale-100"
                  aria-describedby="custom-solution"
                  style={{
                    background: 'linear-gradient(90deg, rgba(147, 51, 234, 0.9) 0%, rgba(79, 70, 229, 0.9) 100%)',
                    color: 'white',
                    '--button-hover-bg': 'linear-gradient(90deg, rgba(147, 51, 234, 1) 0%, rgba(79, 70, 229, 1) 100%)',
                    border: 'none'
                  } as React.CSSProperties}
                >
                  {customSolution.button.label || "Contact sales"} <span aria-hidden="true">→</span>
                </Button>
              )}
            </div>
          </div>
        </div>
      )}
    </section>
  );
}
