import React from 'react';
import { ThemedIconV2 as ThemedIcon } from '../components/ThemedIconV2';
import { IconSectionProps } from './types';

/**
 * IconSolidBlue 组件
 * 
 * 一个蓝色实心图标的链接组件，支持动态内容
 * 支持可选的 tagline、标题和描述
 */
export default function IconSolidBlue({
  id,
  tagline,
  title,
  description,
  items = [],
  customize
}: IconSectionProps) {
  return (
    <section className="bg-background" id={id} aria-labelledby={title ? `${id}-title` : undefined}>
      <div className="mx-auto max-w-container px-container-x py-section-y">
        {/* 标题区域 */}
        {(tagline || title || description) && (
          <div className="mx-auto max-w-content md:max-w-[85%] lg:max-w-content text-center mb-content-y">
            {tagline && (
              <p className="text-body-small/body-small sm:text-body-base/body-base font-semibold text-primary text-balance">
                {tagline}
              </p>
            )}
            {title && (
              <h2 id={`${id}-title`} className="text-heading-4/heading-4 sm:text-heading-3/heading-3 font-bold text-foreground text-balance">
                {title}
              </h2>
            )}
            {description && (
              <p className="mt-element-y text-body-small/body-small sm:text-body-base/body-base text-muted-foreground text-pretty">
                {description}
              </p>
            )}
          </div>
        )}

        <div className="grid grid-cols-1 gap-element-y sm:gap-element-x md:gap-content-y sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 items-center">
          {items.map((item, index) => (
            <a 
              key={`icon-solid-blue-${index}`} 
              className="group" 
              href={item.link.url}
              target={item.link.urlType === 'external' ? '_blank' : undefined}
              rel={item.link.urlType === 'external' ? 'noopener noreferrer' : undefined}
              aria-label={`${item.title}${item.link.urlType === 'external' ? ' - Opens in a new window' : ''}`}
            >
              <div className="relative flex flex-col justify-center hover:bg-accent focus:outline-none focus:bg-accent rounded-xl p-element-x sm:p-element-y md:p-content-y transition-all duration-300 min-h-[180px] sm:min-h-[200px] md:min-h-[220px]">
                <div className="flex items-center justify-between mb-element-y">
                  <ThemedIcon 
                    icon={item.icon} 
                    theme="solidBlue"
                    className="size-5 sm:size-6 md:size-7"
                  />
                </div>
                <div className="mt-element-y">
                  <h3 className="group-hover:text-foreground/80 text-body-base/body-base sm:text-body-large/body-large font-semibold text-foreground text-balance">
                    {item.title}
                  </h3>
                  <p className="mt-element-y text-body-small/body-small sm:text-body-base/body-base text-muted-foreground text-pretty">
                    {item.description}
                  </p>
                  {item.link.label && (
                    <span className="mt-element-y inline-flex items-center gap-x-1 sm:gap-x-1.5 text-body-small/body-small sm:text-body-base/body-base text-primary decoration-2 group-hover:underline group-focus:underline font-medium">
                      {item.link.label}
                      <svg className="shrink-0 size-3 sm:size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" aria-hidden="true"><path d="m9 18 6-6-6-6"/></svg>
                    </span>
                  )}
                </div>
              </div>
            </a>
          ))}
        </div>
      </div>
    </section>
  )
}