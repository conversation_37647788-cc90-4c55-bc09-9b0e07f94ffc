import React from 'react';
import { ThemedIconV2 as ThemedIcon } from '../components/ThemedIconV2';
import { IconSectionProps } from './types';

/**
 * StackedCards 组件
 * 
 * 一个卡片式布局的图标链接组件，支持动态内容
 * 支持可选的 tagline、标题和描述
 */
export default function StackedCards({
  id,
  tagline,
  title,
  description,
  items = [],
  customize
}: IconSectionProps) {
  // 为每个项目分配不同的背景和前景色
  const colors = [
    { bg: 'bg-teal-100 dark:bg-teal-950/30', fg: 'text-teal-600 dark:text-teal-300', border: 'border-teal-200 dark:border-teal-800/30' },
    { bg: 'bg-purple-100 dark:bg-purple-950/30', fg: 'text-purple-600 dark:text-purple-300', border: 'border-purple-200 dark:border-purple-800/30' },
    { bg: 'bg-sky-100 dark:bg-sky-950/30', fg: 'text-sky-600 dark:text-sky-300', border: 'border-sky-200 dark:border-sky-800/30' },
    { bg: 'bg-yellow-100 dark:bg-yellow-950/30', fg: 'text-yellow-600 dark:text-yellow-300', border: 'border-yellow-200 dark:border-yellow-800/30' },
    { bg: 'bg-rose-100 dark:bg-rose-950/30', fg: 'text-rose-600 dark:text-rose-300', border: 'border-rose-200 dark:border-rose-800/30' },
    { bg: 'bg-indigo-100 dark:bg-indigo-950/30', fg: 'text-indigo-600 dark:text-indigo-300', border: 'border-indigo-200 dark:border-indigo-800/30' },
    { bg: 'bg-green-100 dark:bg-green-950/30', fg: 'text-green-600 dark:text-green-300', border: 'border-green-200 dark:border-green-800/30' },
    { bg: 'bg-blue-100 dark:bg-blue-950/30', fg: 'text-blue-600 dark:text-blue-300', border: 'border-blue-200 dark:border-blue-800/30' }
  ];
  return (
    <section className="bg-background" id={id} aria-labelledby={title ? `${id}-title` : undefined}>
      <div className="mx-auto max-w-container px-container-x py-section-y">
      {/* 标题区域 */}
      {(tagline || title || description) && (
        <div className="mx-auto max-w-content md:max-w-[85%] lg:max-w-content text-center mb-content-y">
          {tagline && (
            <p className="text-body-small/body-small sm:text-body-base/body-base font-semibold text-primary text-balance">
              {tagline}
            </p>
          )}
          {title && (
            <h2 id={`${id}-title`} className="text-heading-4/heading-4 sm:text-heading-3/heading-3 font-bold text-foreground text-balance">
              {title}
            </h2>
          )}
          {description && (
            <p className="mt-element-y text-body-small/body-small sm:text-body-base/body-base text-muted-foreground text-pretty">
              {description}
            </p>
          )}
        </div>
      )}

      <div className="grid grid-cols-1 gap-element-y sm:gap-element-x md:gap-content-y sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 items-center">
        {items.map((item, index) => (
          <a
            key={`card-${index}`}
            href={item.link.url}
            target={item.link.urlType === 'external' ? '_blank' : undefined}
            rel={item.link.urlType === 'external' ? 'noopener noreferrer' : undefined}
            aria-label={`${item.title}${item.link.urlType === 'external' ? ' - Opens in a new window' : ''}`}
            className="group size-full bg-card shadow-md sm:shadow-lg rounded-lg p-element-x sm:p-element-y hover:bg-accent/50 transition-all duration-300 hover:shadow-xl min-h-[180px] sm:min-h-[200px] md:min-h-[220px] flex flex-col"
          >
            <div className="flex items-center gap-x-element-x mb-element-y">
              {/* ThemedIcon */}
              <div className={`inline-flex justify-center items-center rounded-full border-2 sm:border-3 md:border-4 ${colors[index % colors.length].border} ${colors[index % colors.length].bg} ${colors[index % colors.length].fg} p-1 sm:p-1.5 md:p-2`}>
                <ThemedIcon 
                  icon={item.icon} 
                  theme="stackedCards"
                  className="size-4 sm:size-5 md:size-6"
                />
              </div>
              
              <div className="shrink-0">
                <h3 className="block text-body-base/body-base sm:text-body-large/body-large font-semibold text-card-foreground text-balance">
                  {item.title}
                </h3>
              </div>
            </div>
            <p className="text-body-small/body-small sm:text-body-base/body-base text-muted-foreground text-pretty">{item.description}</p>
            {item.link.label && (
              <div className="mt-element-y mt-auto">
                <span className="inline-flex items-center gap-x-1 sm:gap-x-1.5 text-body-small/body-small sm:text-body-base/body-base text-primary decoration-2 hover:underline font-medium">
                  {item.link.label}
                  <svg className="shrink-0 size-3 sm:size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" aria-hidden="true"><path d="m9 18 6-6-6-6"/></svg>
                </span>
              </div>
            )}
          </a>
        ))}
      </div>
      </div>
    </section>
  );
}