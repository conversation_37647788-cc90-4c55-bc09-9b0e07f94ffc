import React from 'react';
import { ThemedIconV2 as ThemedIcon } from '../components/ThemedIconV2';
import { IconSectionProps } from './types';

/**
 * IconTwoCols 组件
 * 
 * 一个两列布局的图标链接组件，支持动态内容
 * 支持可选的 tagline、标题和描述
 */
export default function IconTwoCols({ 
  id, 
  tagline, 
  title, 
  description, 
  items = [],
  customize 
}: IconSectionProps) {
  // 为每个项目分配不同的背景和前景色
  const colors = [
    { bg: 'bg-teal-100 dark:bg-teal-950/30', fg: 'text-teal-600 dark:text-teal-300' },
    { bg: 'bg-purple-100 dark:bg-purple-950/30', fg: 'text-purple-600 dark:text-purple-300' },
    { bg: 'bg-sky-100 dark:bg-sky-950/30', fg: 'text-sky-600 dark:text-sky-300' },
    { bg: 'bg-yellow-100 dark:bg-yellow-950/30', fg: 'text-yellow-600 dark:text-yellow-300' },
    { bg: 'bg-rose-100 dark:bg-rose-950/30', fg: 'text-rose-600 dark:text-rose-300' },
    { bg: 'bg-indigo-100 dark:bg-indigo-950/30', fg: 'text-indigo-600 dark:text-indigo-300' },
    { bg: 'bg-green-100 dark:bg-green-950/30', fg: 'text-green-600 dark:text-green-300' },
    { bg: 'bg-blue-100 dark:bg-blue-950/30', fg: 'text-blue-600 dark:text-blue-300' }
  ];
  return (
    <section className="bg-background" id={id} aria-labelledby={title ? `${id}-title` : undefined}>
      <div className="mx-auto max-w-container px-container-x py-section-y">
      {/* 标题区域 */}
      {(tagline || title || description) && (
        <div className="mx-auto max-w-content text-center mb-content-y">
          {tagline && (
            <p className="text-body-small/body-small font-semibold text-primary">
              {tagline}
            </p>
          )}
          {title && (
            <h2 id={`${id}-title`} className="text-heading-3/heading-3 font-bold text-foreground">
              {title}
            </h2>
          )}
          {description && (
            <p className="mt-element-y text-body-base/body-base text-muted-foreground">
              {description}
            </p>
          )}
        </div>
      )}

      <div className="mx-auto max-w-content">
        <div className="grid md:grid-cols-2 gap-element-y">
          {/* 左列 */}
          <div className="space-y-element-y">
            {items.slice(0, Math.ceil(items.length / 2)).map((item, index) => (
              <a 
                key={`icon-left-${index}`}
                className="group flex gap-x-element-x hover:bg-accent focus:outline-none focus:bg-accent rounded-xl p-element-x transition-colors duration-200" 
                href={item.link.url}
                target={item.link.urlType === 'external' ? '_blank' : undefined}
                rel={item.link.urlType === 'external' ? 'noopener noreferrer' : undefined}
                aria-label={`${item.title}${item.link.urlType === 'external' ? ' - Opens in a new window' : ''}`}
              >
                <div className="flex flex-col gap-element-y">
                  <div className={`${colors[index % colors.length].bg} ${colors[index % colors.length].fg} p-3 rounded-full`}>
                    <ThemedIcon 
                      icon={item.icon} 
                      theme="twoCols"
                    />
                  </div>
                </div>
                <div className="grow">
                  <h3 className="text-body-base/body-base sm:text-body-large/body-large font-semibold text-foreground group-hover:text-foreground/80">
                    {item.title}
                  </h3>
                  <p className="mt-element-y text-body-base/body-base text-muted-foreground">
                    {item.description}
                  </p>
                  {item.link.label && (
                    <span className="mt-element-y inline-flex items-center gap-x-1.5 text-body-small/body-small text-primary decoration-2 hover:underline font-medium">
                      {item.link.label}
                      <svg className="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="m9 18 6-6-6-6"/></svg>
                    </span>
                  )}
                </div>
              </a>
            ))}
          </div>

          {/* 右列 */}
          <div className="space-y-element-y">
            {items.slice(Math.ceil(items.length / 2)).map((item, index) => (
              <a 
                key={`icon-right-${index}`}
                className="group flex gap-x-element-x hover:bg-accent focus:outline-none focus:bg-accent rounded-xl p-element-x transition-colors duration-200" 
                href={item.link.url}
                target={item.link.urlType === 'external' ? '_blank' : undefined}
                rel={item.link.urlType === 'external' ? 'noopener noreferrer' : undefined}
                aria-label={`${item.title}${item.link.urlType === 'external' ? ' - Opens in a new window' : ''}`}
              >
                <div className="flex flex-col gap-element-y">
                  <div className={`${colors[index % colors.length].bg} ${colors[index % colors.length].fg} p-3 rounded-full`}>
                    <ThemedIcon 
                      icon={item.icon} 
                      theme="twoCols"
                    />
                  </div>
                </div>
                <div className="grow">
                  <h3 className="text-body-base/body-base sm:text-body-large/body-large font-semibold text-foreground group-hover:text-foreground/80">
                    {item.title}
                  </h3>
                  <p className="mt-element-y text-body-base/body-base text-muted-foreground">
                    {item.description}
                  </p>
                  {item.link.label && (
                    <span className="mt-element-y inline-flex items-center gap-x-1.5 text-body-small/body-small text-primary decoration-2 hover:underline font-medium">
                      {item.link.label}
                      <svg className="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="m9 18 6-6-6-6"/></svg>
                    </span>
                  )}
                </div>
              </a>
            ))}
          </div>
        </div>
      </div>
      </div>
    </section>
  )
}
