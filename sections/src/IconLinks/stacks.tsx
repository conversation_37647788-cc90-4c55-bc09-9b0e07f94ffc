import React from 'react';
import { ThemedIconV2 as ThemedIcon } from '../components/ThemedIconV2';
import { IconSectionProps } from './types';

/**
 * Stacks 组件
 * 
 * 一个堆叠式布局的图标链接组件，支持动态内容
 * 支持可选的 tagline、标题和描述
 */
export default function Stacks({
  id,
  tagline,
  title,
  description,
  items = [],
  customize
}: IconSectionProps) {
  // 辅助函数，用于组合类名
  function classNames(...classes: string[]) {
    return classes.filter(Boolean).join(' ')
  }

  return (
    <section className="bg-background" id={id} aria-labelledby={title ? `${id}-title` : undefined}>
      <div className="mx-auto max-w-container px-container-x py-section-y">
      {/* 标题区域 */}
      {(tagline || title || description) && (
        <div className="mx-auto max-w-content text-center mb-content-y">
          {tagline && (
            <p className="text-body-small/body-small font-semibold text-primary">
              {tagline}
            </p>
          )}
          {title && (
            <h2 id={`${id}-title`} className="text-heading-3/heading-3 font-bold text-foreground">
              {title}
            </h2>
          )}
          {description && (
            <p className="mt-element-y text-body-base/body-base text-muted-foreground">
              {description}
            </p>
          )}
        </div>
      )}

      <div className="mt-element-y divide-y divide-border overflow-hidden rounded-lg bg-muted shadow sm:grid sm:grid-cols-2 sm:gap-px sm:divide-y-0">
        {items.map((item, index) => {
          // 为每个项目确定合适的圆角样式
          const isFirst = index === 0;
          const isSecond = index === 1;
          const isSecondLast = index === items.length - 2;
          const isLast = index === items.length - 1;
          
          // 根据项目的位置确定样式
          const positionClasses = classNames(
            isFirst ? 'rounded-tl-lg rounded-tr-lg sm:rounded-tr-none' : '',
            isSecond ? 'sm:rounded-tr-lg' : '',
            isSecondLast ? 'sm:rounded-bl-lg' : '',
            isLast ? 'rounded-bl-lg rounded-br-lg sm:rounded-bl-none' : '',
            'group relative bg-card p-element-y focus-within:ring-2 focus-within:ring-inset focus-within:ring-primary hover:bg-accent/50 transition-colors duration-200'
          );

          // 为每个项目分配不同的背景和前景色
          const colors = [
            { bg: 'bg-teal-50 dark:bg-teal-950/30', fg: 'text-teal-700 dark:text-teal-300' },
            { bg: 'bg-purple-50 dark:bg-purple-950/30', fg: 'text-purple-700 dark:text-purple-300' },
            { bg: 'bg-sky-50 dark:bg-sky-950/30', fg: 'text-sky-700 dark:text-sky-300' },
            { bg: 'bg-yellow-50 dark:bg-yellow-950/30', fg: 'text-yellow-700 dark:text-yellow-300' },
            { bg: 'bg-rose-50 dark:bg-rose-950/30', fg: 'text-rose-700 dark:text-rose-300' },
            { bg: 'bg-indigo-50 dark:bg-indigo-950/30', fg: 'text-indigo-700 dark:text-indigo-300' },
            { bg: 'bg-green-50 dark:bg-green-950/30', fg: 'text-green-700 dark:text-green-300' },
            { bg: 'bg-blue-50 dark:bg-blue-950/30', fg: 'text-blue-700 dark:text-blue-300' }
          ];
          
          // 使用循环索引来选择颜色
          const colorIndex = index % colors.length;
          const { bg, fg } = colors[colorIndex];
          
          return (
            <div
              key={`stack-item-${index}`}
              className={positionClasses}
            >
              <div className="flex flex-row items-center gap-element-x">
                <span
                  className={classNames(
                    bg,
                    fg,
                    'inline-flex rounded-lg p-3 ring-4 ring-card'
                  )}
                >
                  <ThemedIcon 
                    icon={item.icon} 
                    theme="stacks"
                  />
                </span>
              </div>
              <div className="mt-element-y">
                <h3 className="text-body-base/body-base font-semibold text-card-foreground">
                  <a 
                    href={item.link.url} 
                    className="focus:outline-none"
                    target={item.link.urlType === 'external' ? '_blank' : undefined}
                    rel={item.link.urlType === 'external' ? 'noopener noreferrer' : undefined}
                    aria-label={`${item.title}${item.link.urlType === 'external' ? ' - Opens in a new window' : ''}`}
                  >
                    {/* Extend touch target to entire panel */}
                    <span className="absolute inset-0" aria-hidden="true" />
                    {item.title}
                    {item.link.label && (
                      <span className="ml-2 inline-flex items-center rounded-md bg-green-50 dark:bg-green-950/30 px-2 py-1 text-body-small/body-small font-medium text-green-700 dark:text-green-300 ring-1 ring-inset ring-green-600/20 dark:ring-green-500/30">
                        {item.link.label}
                      </span>
                    )}
                  </a>
                </h3>
                <p className="mt-element-y text-body-small/body-small text-muted-foreground">
                  {item.description}
                </p>
              </div>
              <span
                className="pointer-events-none absolute right-element-x top-element-y text-muted group-hover:text-muted-foreground transition-colors duration-200"
                aria-hidden="true"
              >
                <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path d="M20 4h1a1 1 0 00-1-1v1zm-1 12a1 1 0 102 0h-2zM8 3a1 1 0 000 2V3zM3.293 19.293a1 1 0 101.414 1.414l-1.414-1.414zM19 4v12h2V4h-2zm1-1H8v2h12V3zm-.707.293l-16 16 1.414 1.414 16-16-1.414-1.414z" />
                </svg>
              </span>
            </div>
          );
        })}
      </div>
      </div>
    </section>
  )
}
