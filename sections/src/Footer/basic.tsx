import React from 'react';
import { FooterSectionProps } from './types';
import { Button } from '../Button';
import { BlockContainer } from '../components/BlockContainer';

/**
 * FooterBasic 组件
 * 
 * 优化的基础 Footer 组件，支持：
 * - 完整的暗色模式支持
 * - 主题色系统集成
 * - 响应式设计
 * - 可访问性增强
 */
export default function FooterBasic({ id, copyright, customize, logo, slogan, socialMedia, linkGroups, resources }: FooterSectionProps) {
  return (
    <BlockContainer id={id} resources={resources} customize={customize}>
      <footer className="bg-background" aria-labelledby="footer-heading">
        <h2 id="footer-heading" className="sr-only">
          Footer
        </h2>
        <div className="mx-auto max-w-container px-container-x py-section-y">
          <div className="xl:grid xl:grid-cols-4 xl:gap-element-x">
            {/* Logo 和标语区域 */}
            <div className="space-y-element-y">
              {logo && (
                <a href={logo.linkUrl} className="focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 rounded-md">
                  <img
                    className="h-7"
                    src={logo.imageUrl}
                    alt="Company logo"
                  />
                </a>
              )}
              {slogan && (
                <p className="text-body-small/body-small text-muted-foreground">
                  {slogan}
                </p>
              )}
              {/* 社交媒体链接 */}
              {socialMedia && socialMedia.length > 0 && (
                <div className="flex space-x-element-x">
                  {socialMedia.map((item: any) => (
                    <a 
                      key={item.name} 
                      href={item.link} 
                      className="text-muted-foreground hover:text-foreground hover:bg-accent transition-colors duration-200 rounded-lg px-element-x py-1 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                      aria-label={`Visit our ${item.name} page${item.link?.startsWith('http') ? ' (opens in new window)' : ''}`}
                      target={item.link?.startsWith('http') ? '_blank' : undefined}
                      rel={item.link?.startsWith('http') ? 'noopener noreferrer' : undefined}
                    >
                      <span className="sr-only">{item.name}</span>
                      <i className={`h-6 w-6 ${item.icon}`} aria-hidden="true" />
                    </a>
                  ))}
                </div>
              )}
            </div>
            
            {/* 链接组区域 */}
            {linkGroups && linkGroups.length > 0 && (
              <div className="mt-content-y grid grid-cols-1 gap-element-y xl:col-span-3 xl:mt-0 sm:grid-cols-4">
                {linkGroups.map((group: any, groupIndex) => (
                  <div key={groupIndex}>
                    <h3 className="text-body-small/body-small font-semibold text-foreground mb-element-y">{group.groupName}</h3>
                    <ul className="space-y-element-y">
                      {group.links.map((item: any, itemIndex: number) => (
                        <li key={itemIndex}>
                          <Button
                            buttonTarget={item?.buttonTarget}
                            appearance={item?.appearance}
                            className="text-body-small/body-small text-muted-foreground hover:text-foreground hover:bg-accent transition-colors duration-200 rounded-lg px-element-x py-1 w-full text-left focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                          >
                            {item.text}
                          </Button>
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            )}
          </div>
          
          {/* 版权信息 */}
          <div className="mt-content-y border-t border-border pt-element-y sm:mt-content-y lg:mt-content-y">
            <p className="text-body-small/body-small text-muted-foreground">{copyright}</p>
          </div>
        </div>
      </footer>
    </BlockContainer>
  );
}
