import React from 'react';
import SocialIcons from '../components/SocialIcons';
import { TeamSectionProps, TeamMemberSocialLink, TeamMember } from './types';
import { Picture } from '../components/Picture';

const LargeImages: React.FC<TeamSectionProps> = ({
  tagline,
  title,
  description,
  people,
  hiringSection
}) => {
  // 将社交媒体平台映射到对应的URL属性
  const getSocialUrls = (socialLinks: TeamMemberSocialLink[] = []) => {
    const urls: {
      xUrl?: string;
      linkedinUrl?: string;
      githubUrl?: string;
      facebookUrl?: string;
      instagramUrl?: string;
      youtubeUrl?: string;
    } = {
      xUrl: undefined,
      linkedinUrl: undefined,
      githubUrl: undefined,
      facebookUrl: undefined,
      instagramUrl: undefined,
      youtubeUrl: undefined
    };
    
    socialLinks.forEach(link => {
      if (link.platform === 'x') urls.xUrl = link.url;
      if (link.platform === 'linkedin') urls.linkedinUrl = link.url;
      if (link.platform === 'github') urls.githubUrl = link.url;
      if (link.platform === 'facebook') urls.facebookUrl = link.url;
      if (link.platform === 'instagram') urls.instagramUrl = link.url;
      if (link.platform === 'youtube') urls.youtubeUrl = link.url;
    });
    
    return urls;
  };

  return (
    <section className="bg-background py-section-y" aria-labelledby="team-large-images-title">
      <div className="mx-auto max-w-container px-container-x">
        <div className="mx-auto max-w-content text-center mb-content-y">
          {tagline && <p className="text-body-base/body-base font-semibold text-primary">{tagline}</p>}
          <h2 id="team-large-images-title" className="mt-element-y text-heading-2/heading-2 font-bold text-pretty text-foreground">{title}</h2>
          {description && <p className="mt-element-y text-body-base/body-base text-muted-foreground">{description}</p>}
        </div>
        
        <div className="mt-content-y grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-element-y gap-x-element-x">
          {people.map((person: TeamMember, index: number) => (
            <div key={index} className="bg-card rounded-xl overflow-hidden shadow-md ring-1 ring-border transform transition-all duration-300 hover:-translate-y-2 hover:shadow-xl">
              <div className="p-6 pb-4">
                <Picture
                  src={person.imageUrl || ''}
                  alt={person.name}
                  width={400}
                  height={384}
                  densities={[1, 2, 3]}
                  quality={{ avif: 70, webp: 75, jpeg: 80, png: 85 }}
                  fit="cover"
                  className="w-full h-64 md:h-72 lg:h-80 xl:h-96 object-cover rounded-lg mx-auto"
                  style={{ display: 'block' }}
                />
              </div>
              <div className="p-6 pt-0 text-center">
                <h3 className="text-heading-4/heading-4 font-bold text-foreground">{person.name}</h3>
                <p className="mt-element-y text-body-base/body-base text-secondary-foreground">{person.role}</p>
                <p className="mt-element-y text-body-small/body-small text-muted-foreground line-clamp-3">{person.bio}</p>
                <div className="mt-content-y flex justify-center">
                  <SocialIcons
                    {...getSocialUrls(person.socialLinks)}
                    size="md"
                    variant="simple"
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
        
        {hiringSection && hiringSection.enabled && (
          <div className="mt-section-y bg-card rounded-xl overflow-hidden shadow-lg ring-1 ring-border" aria-labelledby="hiring-section-title">
            <div className="p-8 text-center">
              <h3 id="hiring-section-title" className="text-heading-3/heading-3 font-bold text-foreground">{hiringSection.title}</h3>
              <p className="mt-element-y text-body-base/body-base text-secondary-foreground">{hiringSection.subtitle}</p>
              <p className="mt-element-y text-body-base/body-base text-muted-foreground max-w-content mx-auto">{hiringSection.description}</p>
              {hiringSection.button && (
                <a
                  href={hiringSection.button.href}
                  className="mt-content-y inline-flex items-center justify-center py-3 px-8 text-primary-foreground font-semibold bg-primary rounded-md hover:bg-primary/90 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary transition-colors duration-200"
                >
                  {hiringSection.button.label}
                </a>
              )}
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default LargeImages;