import React from 'react';
import SocialIcons from '../components/SocialIcons';
import { TeamSectionProps, TeamMemberSocialLink } from './types';
import { Picture } from '../components/Picture';
import { defaultTagline, defaultTitle, defaultDescription, defaultTeamMembers, defaultHiringSection } from './defaults';

const GridLargeImages: React.FC<TeamSectionProps> = ({
  tagline = defaultTagline,
  title = defaultTitle,
  description = defaultDescription,
  people = defaultTeamMembers,
  hiringSection = defaultHiringSection
}) => {
  // 将社交媒体平台映射到对应的URL属性
  const getSocialUrls = (socialLinks: TeamMemberSocialLink[] = []) => {
    const urls: {
      xUrl?: string;
      linkedinUrl?: string;
      githubUrl?: string;
      facebookUrl?: string;
      instagramUrl?: string;
      youtubeUrl?: string;
    } = {
      xUrl: undefined,
      linkedinUrl: undefined,
      githubUrl: undefined,
      facebookUrl: undefined,
      instagramUrl: undefined,
      youtubeUrl: undefined
    };
    
    socialLinks.forEach(link => {
      if (link.platform === 'x') urls.xUrl = link.url;
      if (link.platform === 'linkedin') urls.linkedinUrl = link.url;
      if (link.platform === 'github') urls.githubUrl = link.url;
      if (link.platform === 'facebook') urls.facebookUrl = link.url;
      if (link.platform === 'instagram') urls.instagramUrl = link.url;
      if (link.platform === 'youtube') urls.youtubeUrl = link.url;
    });
    
    return urls;
  };

  return (
    <section className="bg-background py-section-y" aria-labelledby="team-grid-large-images-title">
      <div className="mx-auto max-w-container px-container-x">
        <div className="mx-auto max-w-content text-center mb-content-y">
          {tagline && <p className="text-body-base/body-base font-semibold text-primary">{tagline}</p>}
          <h2 id="team-grid-large-images-title" className="mt-element-y text-heading-2/heading-2 font-semibold text-pretty text-foreground">
            {title}
          </h2>
          {description && (
            <p className="mt-element-y text-body-base/body-base text-muted-foreground">
              {description}
            </p>
          )}
        </div>
        <ul
          role="list"
          className="mt-content-y grid grid-cols-1 gap-x-element-x gap-y-content-y sm:grid-cols-2 lg:grid-cols-3"
        >
          {people && people.map((person, index) => {
            const socialUrls = getSocialUrls(person.socialLinks);
            
            return (
              <li key={index} className="bg-card rounded-xl overflow-hidden shadow-md ring-1 ring-border transform transition-all duration-300 hover:-translate-y-2 hover:shadow-xl text-center">
                <div className="p-6 pb-0">
                  <div className="mx-auto w-32 h-32 sm:w-36 sm:h-36 md:w-40 md:h-40 lg:w-44 lg:h-44 max-w-[calc(100%-2rem)] max-h-[calc(100%-2rem)] rounded-full overflow-hidden">
                    <Picture
                      src={person.imageUrl || ''}
                      alt={`${person.name} avatar`}
                      width={384}
                      height={384}
                      densities={[1, 2, 3]}
                      fit="cover"
                      quality={{ avif: 70, webp: 75, jpeg: 80, png: 85 }}
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-heading-4/heading-4 font-semibold text-foreground">
                    {person.name}
                  </h3>
                  <p className="mt-element-y text-body-base/body-base text-secondary-foreground">{person.role}</p>
                  {person.bio && (
                    <p className="mt-element-y text-body-small/body-small text-muted-foreground mx-auto line-clamp-3">
                      {person.bio}
                    </p>
                  )}
                  <div className="mt-content-y flex justify-center">
                    <SocialIcons 
                      {...socialUrls}
                      size="lg" 
                      variant="simple" 
                    />
                  </div>
                </div>
              </li>
            );
          })}
          
          {/* Hiring Card */}
          {hiringSection && hiringSection.enabled && (
            <li className="bg-card rounded-xl overflow-hidden shadow-md ring-1 ring-border ring-dashed transform transition-all duration-300 hover:-translate-y-2 hover:shadow-xl text-center">
              <div className="p-6 pb-0">
                <div className="mx-auto w-32 h-32 sm:w-36 sm:h-36 md:w-40 md:h-40 lg:w-44 lg:h-44 max-w-[calc(100%-2rem)] max-h-[calc(100%-2rem)] rounded-full overflow-hidden">
                  <Picture
                    src={hiringSection.imageUrl || ''}
                    alt={`${hiringSection.title} - ${hiringSection.subtitle}`}
                    width={384}
                    height={384}
                    densities={[1, 2, 3]}
                    fit="cover"
                    quality={{ avif: 70, webp: 75, jpeg: 80, png: 85 }}
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
              <div className="p-6">
                <h3 className="text-heading-4/heading-4 font-semibold text-foreground">
                  {hiringSection.title}
                </h3>
                <p className="mt-element-y text-body-base/body-base text-secondary-foreground">{hiringSection.subtitle}</p>
                <p className="mt-element-y text-body-small/body-small text-muted-foreground mx-auto line-clamp-3">
                  {hiringSection.description}
                </p>
                <div className="mt-content-y">
                  {hiringSection.button && (
                    <a className="inline-flex items-center justify-center gap-x-2 py-2 px-4 text-body-small/body-small font-medium text-primary hover:text-primary-foreground hover:bg-primary rounded-md transition-colors duration-200" href={hiringSection.button.href}>
                      {hiringSection.button.label}
                      <svg className="shrink-0 h-4 w-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" aria-hidden="true"><path d="m9 18 6-6-6-6"/></svg>
                    </a>
                  )}
                </div>
              </div>
            </li>
          )}
          {/* End Hiring Card */}
        </ul>
      </div>
    </section>
  );
};

export default GridLargeImages;