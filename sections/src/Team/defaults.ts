import { TeamSectionProps, TeamMember, TeamMemberSocialLink, HiringSection } from './types';

// 默认标语
export const defaultTagline = 'Our team';

// 默认标题
export const defaultTitle = 'Meet our team';

// 默认描述
export const defaultDescription = 'We\'re a dynamic group of individuals who are passionate about what we do and dedicated to delivering the best results for our clients.';

// 默认团队成员
export const defaultTeamMembers: TeamMember[] = [
  {
    name: "<PERSON>",
    role: "Founder / CEO",
    bio: "<PERSON> has over 15 years of experience in software development and product management. He founded our company with a vision to create innovative solutions that solve real-world problems.",
    imageUrl: "https://images.unsplash.com/photo-1568602471122-7832951cc4c5?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=2&q=80",
    socialLinks: [
      { platform: "x", url: "#" },
      { platform: "linkedin", url: "#" },
      { platform: "github", url: "#" }
    ] as TeamMemberSocialLink[]
  },
  {
    name: "Amil <PERSON>ra",
    role: "UI/UX Designer",
    bio: "Amil is a talented designer with a passion for creating beautiful and functional user interfaces. She brings a unique perspective to our team and is dedicated to enhancing user experiences.",
    imageUrl: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=2&q=80",
    socialLinks: [
      { platform: "x", url: "#" },
      { platform: "linkedin", url: "#" },
      { platform: "instagram", url: "#" }
    ] as TeamMemberSocialLink[]
  },
  {
    name: "Ebele Egbuna",
    role: "Support Consultant",
    bio: "Ebele specializes in customer support and satisfaction. With her excellent communication skills and technical knowledge, she ensures our clients receive the best possible assistance.",
    imageUrl: "https://images.unsplash.com/photo-1548142813-c348350df52b?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=2&q=80",
    socialLinks: [
      { platform: "x", url: "#" },
      { platform: "linkedin", url: "#" },
      { platform: "github", url: "#" }
    ] as TeamMemberSocialLink[]
  },
  {
    name: "Michael Chen",
    role: "Lead Developer",
    bio: "Michael is a full-stack developer with expertise in modern web technologies. He leads our development team and is passionate about creating scalable and efficient applications.",
    imageUrl: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=2&q=80",
    socialLinks: [
      { platform: "github", url: "#" },
      { platform: "linkedin", url: "#" },
      { platform: "youtube", url: "#" }
    ] as TeamMemberSocialLink[]
  },
  {
    name: "Sarah Johnson",
    role: "Marketing Director",
    bio: "Sarah has a background in digital marketing and brand strategy. She oversees all marketing initiatives and helps our clients build strong brand identities in the digital landscape.",
    imageUrl: "https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=2&q=80",
    socialLinks: [
      { platform: "x", url: "#" },
      { platform: "linkedin", url: "#" },
      { platform: "instagram", url: "#" },
      { platform: "facebook", url: "#" }
    ] as TeamMemberSocialLink[]
  },
  {
    name: "Carlos Rodriguez",
    role: "Product Manager",
    bio: "Carlos bridges the gap between technical and business aspects of our products. With his strategic thinking and user-centered approach, he ensures our solutions meet market needs.",
    imageUrl: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=2&q=80",
    socialLinks: [
      { platform: "linkedin", url: "#" },
      { platform: "github", url: "#" },
      { platform: "facebook", url: "#" }
    ] as TeamMemberSocialLink[]
  }
];

// 默认招聘部分
export const defaultHiringSection: HiringSection = {
  enabled: true,
  title: "We are hiring!",
  subtitle: "Join our team",
  description: "We're looking for passionate individuals to join our team. If you're interested in creating exceptional products and experiences, we'd love to hear from you.",
  button: {
    label: "See all opening positions",
    href: "#",
    urlType: "internal"
  },
  imageUrl: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=2&q=80"
};

// 默认团队区块属性
export const defaultTeamSectionProps: TeamSectionProps = {
  tagline: defaultTagline,
  title: defaultTitle,
  description: defaultDescription,
  people: defaultTeamMembers,
  hiringSection: defaultHiringSection
};
