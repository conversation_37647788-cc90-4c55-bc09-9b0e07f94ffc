import React from 'react';
import SocialIcons from '../components/SocialIcons';
import { TeamSectionProps, TeamMemberSocialLink } from './types';
import { Picture } from '../components/Picture';

const SquaredMediumImage: React.FC<TeamSectionProps> = ({
  tagline,
  title,
  description,
  people,
  hiringSection
}) => {
  // 将社交媒体平台映射到对应的URL属性
  const getSocialUrls = (socialLinks: TeamMemberSocialLink[] = []) => {
    const urls: {
      xUrl?: string;
      linkedinUrl?: string;
      githubUrl?: string;
      facebookUrl?: string;
      instagramUrl?: string;
      youtubeUrl?: string;
    } = {
      xUrl: undefined,
      linkedinUrl: undefined,
      githubUrl: undefined,
      facebookUrl: undefined,
      instagramUrl: undefined,
      youtubeUrl: undefined
    };
    
    socialLinks.forEach(link => {
      if (link.platform === 'x') urls.xUrl = link.url;
      if (link.platform === 'linkedin') urls.linkedinUrl = link.url;
      if (link.platform === 'github') urls.githubUrl = link.url;
      if (link.platform === 'facebook') urls.facebookUrl = link.url;
      if (link.platform === 'instagram') urls.instagramUrl = link.url;
      if (link.platform === 'youtube') urls.youtubeUrl = link.url;
    });
    
    return urls;
  };

  return (
    <section className="bg-background py-section-y" aria-labelledby="team-squared-medium-title">
      <div className="mx-auto max-w-container px-container-x">
        {/* Title */}
        <div className="mx-auto max-w-content text-center mb-content-y">
          {tagline && <p className="text-body-base/body-base font-semibold text-primary">{tagline}</p>}
          <h2 id="team-squared-medium-title" className="mt-element-y text-heading-2/heading-2 font-bold text-pretty text-foreground">{title}</h2>
          {description && <p className="mt-element-y text-body-base/body-base text-muted-foreground">{description}</p>}
        </div>
        {/* End Title */}

        {/* Grid */}
        <div className="mt-content-y grid grid-cols-2 md:grid-cols-3 gap-element-y gap-x-element-x">
          {people && people.map((person, index) => {
            const socialUrls = getSocialUrls(person.socialLinks);
            
            return (
              <div key={index} className="bg-card rounded-xl overflow-hidden shadow-md ring-1 ring-border transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl text-center p-6">
                <Picture
                  src={person.imageUrl || ''}
                  alt={`${person.name} avatar`}
                  width={256}
                  height={256}
                  densities={[1, 2, 3]}
                  quality={{ avif: 70, webp: 75, jpeg: 80, png: 85 }}
                  fit="cover"
                  className="rounded-xl sm:size-48 md:size-52 lg:size-60 xl:size-64 mx-auto object-cover"
                  style={{ display: 'block' }}
                />
                <div className="mt-content-y">
                  <h3 className="text-heading-4/heading-4 font-medium text-foreground">
                    {person.name}
                  </h3>
                  <p className="mt-element-y text-body-base/body-base text-secondary-foreground">
                    {person.role}
                  </p>
                  {person.bio && (
                    <p className="mt-element-y text-body-small/body-small text-muted-foreground line-clamp-3 mx-auto">
                      {person.bio}
                    </p>
                  )}
                  <div className="mt-content-y flex justify-center">
                    <SocialIcons
                      {...socialUrls}
                      size="sm"
                      variant="simple"
                      className="text-muted-foreground hover:text-foreground"
                    />
                  </div>
                </div>
              </div>
            );
          })}
          
          {/* Hiring Card */}
          {hiringSection && hiringSection.enabled && (
            <div className="bg-card rounded-xl overflow-hidden shadow-md ring-1 ring-border ring-dashed transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl text-center p-6">
              <Picture
                src={hiringSection.imageUrl || ''}
                alt={`${hiringSection.title} - ${hiringSection.subtitle}`}
                width={256}
                height={256}
                densities={[1, 2, 3]}
                quality={{ avif: 70, webp: 75, jpeg: 80, png: 85 }}
                fit="cover"
                className="rounded-xl sm:size-48 md:size-52 lg:size-60 xl:size-64 mx-auto object-cover"
                style={{ display: 'block' }}
              />
              <div className="mt-content-y">
                <h3 className="text-heading-4/heading-4 font-medium text-foreground">
                  {hiringSection.title}
                </h3>
                <p className="mt-element-y text-body-base/body-base text-secondary-foreground">
                  {hiringSection.subtitle}
                </p>
                <p className="mt-element-y text-body-small/body-small text-muted-foreground line-clamp-3 mx-auto">
                  {hiringSection.description}
                </p>
                <div className="mt-content-y">
                  {hiringSection.button && (
                    <a className="inline-flex items-center justify-center gap-x-2 py-2 px-4 text-body-small/body-small font-medium text-primary hover:text-primary-foreground hover:bg-primary rounded-md transition-colors duration-200" href={hiringSection.button.href}>
                      {hiringSection.button.label}
                      <svg className="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" aria-hidden="true"><path d="m9 18 6-6-6-6"/></svg>
                    </a>
                  )}
                </div>
              </div>
            </div>
          )}
          {/* End Hiring Card */}
        </div>
        {/* End Grid */}
      </div>
    </section>
  );
};

export default SquaredMediumImage;