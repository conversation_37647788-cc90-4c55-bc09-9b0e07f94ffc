import React from 'react';
import SocialIcons from '../components/SocialIcons';
import { TeamSectionProps, TeamMemberSocialLink } from './types';
import { Picture } from '../components/Picture';

const ImageWithBio: React.FC<TeamSectionProps> = ({
  tagline,
  title,
  description,
  people,
  hiringSection
}) => {
  // 将社交媒体平台映射到对应的URL属性
  const getSocialUrls = (socialLinks: TeamMemberSocialLink[] = []) => {
    const urls: {
      xUrl?: string;
      linkedinUrl?: string;
      githubUrl?: string;
      facebookUrl?: string;
      instagramUrl?: string;
      youtubeUrl?: string;
    } = {
      xUrl: undefined,
      linkedinUrl: undefined,
      githubUrl: undefined,
      facebookUrl: undefined,
      instagramUrl: undefined,
      youtubeUrl: undefined
    };
    
    socialLinks.forEach(link => {
      if (link.platform === 'x') urls.xUrl = link.url;
      if (link.platform === 'linkedin') urls.linkedinUrl = link.url;
      if (link.platform === 'github') urls.githubUrl = link.url;
      if (link.platform === 'facebook') urls.facebookUrl = link.url;
      if (link.platform === 'instagram') urls.instagramUrl = link.url;
      if (link.platform === 'youtube') urls.youtubeUrl = link.url;
    });
    
    return urls;
  };

  return (
    <section className="bg-background py-section-y" aria-labelledby="team-image-with-bio-title">
      <div className="mx-auto max-w-container px-container-x">
        <div className="grid grid-cols-1 gap-content-y xl:grid-cols-12">
          <div className="mx-auto max-w-content xl:col-span-3 xl:max-w-none xl:pr-content-x">
          {tagline && <p className="text-body-base/body-base font-semibold text-primary">{tagline}</p>}
          <h2 id="team-image-with-bio-title" className="mt-element-y text-heading-2/heading-2 font-semibold text-pretty text-foreground">{title}</h2>
          {description && (
            <p className="mt-element-y text-body-base/body-base text-muted-foreground">
              {description}
            </p>
          )}
        </div>
          <ul
            role="list"
            className="mx-auto grid max-w-content grid-cols-1 gap-x-8 gap-y-content-y sm:grid-cols-2 lg:mx-0 xl:col-span-9 xl:max-w-none xl:grid-cols-3"
          >
          {people && people.map((person, index) => {
            const socialUrls = getSocialUrls(person.socialLinks);
            
            return (
              <li key={index}>
                <Picture
                  src={person.imageUrl || ''}
                  alt={`${person.name} avatar`}
                  widths={[200, 300, 400, 600]}
                  aspectRatio="3/2"
                  sizes="(max-width: 640px) 300px, (max-width: 1024px) 350px, 400px"
                  quality={{ avif: 70, webp: 75, jpeg: 80, png: 85 }}
                  className="aspect-[3/2] w-full rounded-2xl object-cover"
                  style={{ display: 'block' }}
                />
                <h3 className="mt-content-y text-heading-4/heading-4 font-semibold text-foreground">{person.name}</h3>
                <p className="mt-element-y text-body-base/body-base text-secondary-foreground">{person.role}</p>
                {person.bio && <p className="mt-element-y text-body-small/body-small text-muted-foreground">{person.bio}</p>}
                <div className="mt-content-y">
                  <SocialIcons 
                    {...socialUrls}
                    size="lg" 
                    variant="simple" 
                  />
                </div>
              </li>
            );
          })}
          
          {/* Hiring Card */}
          {hiringSection && hiringSection.enabled && (
            <li className="border border-dashed border-border rounded-2xl p-6 hover:shadow-sm">
              <Picture
                src={hiringSection.imageUrl || ''}
                alt={`${hiringSection.title} - ${hiringSection.subtitle}`}
                widths={[200, 300, 400, 600]}
                aspectRatio="3/2"
                sizes="(max-width: 640px) 300px, (max-width: 1024px) 350px, 400px"
                quality={{ avif: 70, webp: 75, jpeg: 80, png: 85 }}
                className="aspect-[3/2] w-full rounded-2xl object-cover"
                style={{ display: 'block' }}
              />
              <h3 className="mt-content-y text-heading-4/heading-4 font-semibold text-foreground">{hiringSection.title}</h3>
              <p className="mt-element-y text-body-base/body-base text-secondary-foreground">{hiringSection.subtitle}</p>
              <p className="mt-element-y text-body-small/body-small text-muted-foreground">
                {hiringSection.description}
              </p>
              <div className="mt-content-y">
                {hiringSection.button && (
                  <a className="inline-flex items-center gap-x-2 text-body-small/body-small font-medium text-primary hover:text-primary/80" href={hiringSection.button.href}>
                    {hiringSection.button.label}
                    <svg className="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" aria-hidden="true"><path d="m9 18 6-6-6-6"/></svg>
                  </a>
                )}
              </div>
            </li>
          )}
          {/* End Hiring Card */}
        </ul>
        </div>
      </div>
    </section>
  );
};

export default ImageWithBio;