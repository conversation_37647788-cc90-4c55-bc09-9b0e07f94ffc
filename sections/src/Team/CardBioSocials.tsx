import React from 'react';
import SocialIcons from '../components/SocialIcons';
import { TeamSectionProps, TeamMemberSocialLink } from './types';
import { Picture } from '../components/Picture';

const CardBioSocials: React.FC<TeamSectionProps> = ({
  tagline,
  title,
  description,
  people,
  hiringSection
}) => {
  // 将社交媒体平台映射到对应的URL属性
  const getSocialUrls = (socialLinks: TeamMemberSocialLink[] = []) => {
    const urls: {
      xUrl?: string;
      linkedinUrl?: string;
      githubUrl?: string;
      facebookUrl?: string;
      instagramUrl?: string;
      youtubeUrl?: string;
    } = {
      xUrl: undefined,
      linkedinUrl: undefined,
      githubUrl: undefined,
      facebookUrl: undefined,
      instagramUrl: undefined,
      youtubeUrl: undefined
    };
    
    socialLinks.forEach(link => {
      if (link.platform === 'x') urls.xUrl = link.url;
      if (link.platform === 'linkedin') urls.linkedinUrl = link.url;
      if (link.platform === 'github') urls.githubUrl = link.url;
      if (link.platform === 'facebook') urls.facebookUrl = link.url;
      if (link.platform === 'instagram') urls.instagramUrl = link.url;
      if (link.platform === 'youtube') urls.youtubeUrl = link.url;
    });
    
    return urls;
  };

  return (
    <section className="bg-background py-section-y" aria-labelledby="team-card-bio-title">
      <div className="mx-auto max-w-container px-container-x">
        {/* Title */}
        <div className="mx-auto max-w-content text-center mb-content-y">
          {tagline && <p className="text-body-base/body-base font-semibold text-primary">{tagline}</p>}
          <h2 id="team-card-bio-title" className="mt-element-y text-heading-2/heading-2 font-bold text-pretty text-foreground">{title}</h2>
          {description && <p className="mt-element-y text-body-base/body-base text-muted-foreground">{description}</p>}
        </div>
        {/* End Title */}

        {/* Grid */}
        <div className="mt-content-y grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-element-y gap-x-element-x">
          {people && people.map((person, index) => {
            const socialUrls = getSocialUrls(person.socialLinks);
            
            return (
              <div key={index} className="flex flex-col rounded-xl p-4 md:p-6 bg-card border border-border">
                <div className="flex items-center gap-x-4">
                  <Picture
                    src={person.imageUrl || ''}
                    alt={`${person.name} avatar`}
                    width={80}
                    height={80}
                    densities={[1, 2, 3]}
                    quality={{ avif: 70, webp: 75, jpeg: 80, png: 85 }}
                    fit="cover"
                    className="rounded-full size-20"
                    style={{ display: 'block' }}
                  />
                  <div className="grow">
                    <h3 className="text-body-base/body-base font-medium text-foreground">
                      {person.name}
                    </h3>
                    <p className="text-body-small/body-small uppercase text-secondary-foreground">
                      {person.role}
                    </p>
                  </div>
                </div>

                {person.bio && (
                  <p className="mt-element-y text-body-small/body-small text-muted-foreground">
                    {person.bio}
                  </p>
                )}

                {/* Social Brands */}
                <div className="mt-element-y">
                  <SocialIcons 
                    {...socialUrls}
                    size="md" 
                    variant="bordered" 
                  />
                </div>
                {/* End Social Brands */}
              </div>
            );
          })}

          {/* Hiring Card */}
          {hiringSection && hiringSection.enabled && (
            <div className="col-span-full lg:col-span-1 flex flex-col rounded-xl p-4 md:p-6 bg-card border border-dashed border-border">
              <div className="flex items-center gap-x-4">
                <Picture
                  src={hiringSection.imageUrl || ''}
                  alt={`${hiringSection.title} - ${hiringSection.subtitle}`}
                  width={80}
                  height={80}
                  densities={[1, 2, 3]}
                  quality={{ avif: 70, webp: 75, jpeg: 80, png: 85 }}
                  fit="cover"
                  className="rounded-full size-20"
                  style={{ display: 'block' }}
                />
                <div className="grow">
                  <h3 className="text-body-base/body-base font-medium text-foreground">
                    {hiringSection.title}
                  </h3>
                  <p className="text-body-small/body-small uppercase text-secondary-foreground">
                    {hiringSection.subtitle}
                  </p>
                </div>
              </div>

              <p className="mt-element-y text-body-small/body-small text-muted-foreground">
                {hiringSection.description}
              </p>

              {/* Button Link */}
              <div className="mt-element-y">
                {hiringSection.button && (
                  <a 
                    href={hiringSection.button.href}
                    className="inline-flex items-center gap-x-2 text-body-small/body-small font-medium text-primary hover:text-primary/90 transition-colors"
                  >
                    {hiringSection.button.label}
                    <svg className="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="m9 18 6-6-6-6"/></svg>
                  </a>
                )}
              </div>
              {/* End Button Link */}
            </div>
          )}
          {/* End Hiring Card */}
        </div>
        {/* End Grid */}
      </div>
    </section>
  );
};

export default CardBioSocials;