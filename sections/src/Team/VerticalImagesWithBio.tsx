import React from 'react';
import SocialIcons from '../components/SocialIcons';
import { TeamSectionProps, TeamMemberSocialLink } from './types';
import { Picture } from '../components/Picture';

const VerticalImagesWithBio: React.FC<TeamSectionProps> = ({
  tagline,
  title,
  description,
  people,
  hiringSection
}) => {
  // 将社交媒体平台映射到对应的URL属性
  const getSocialUrls = (socialLinks: TeamMemberSocialLink[] = []) => {
    const urls: {
      xUrl?: string;
      linkedinUrl?: string;
      githubUrl?: string;
      facebookUrl?: string;
      instagramUrl?: string;
      youtubeUrl?: string;
    } = {
      xUrl: undefined,
      linkedinUrl: undefined,
      githubUrl: undefined,
      facebookUrl: undefined,
      instagramUrl: undefined,
      youtubeUrl: undefined
    };
    
    socialLinks.forEach(link => {
      if (link.platform === 'x') urls.xUrl = link.url;
      if (link.platform === 'linkedin') urls.linkedinUrl = link.url;
      if (link.platform === 'github') urls.githubUrl = link.url;
      if (link.platform === 'facebook') urls.facebookUrl = link.url;
      if (link.platform === 'instagram') urls.instagramUrl = link.url;
      if (link.platform === 'youtube') urls.youtubeUrl = link.url;
    });
    
    return urls;
  };

  return (
    <section className="bg-background py-section-y" aria-labelledby="team-vertical-images-title">
      <div className="mx-auto max-w-container px-container-x">
        {/* 优化外层布局，调整列数和间距 */}
        <div className="grid grid-cols-1 gap-y-content-y gap-x-element-x xl:grid-cols-5 2xl:grid-cols-6">
          <div className="max-w-content xl:col-span-2 2xl:col-span-2">
            {tagline && <p className="text-body-base/body-base font-semibold text-primary">{tagline}</p>}
            <h2 id="team-vertical-images-title" className="mt-element-y text-heading-2/heading-2 font-bold text-pretty text-foreground">
              {title}
            </h2>
            {description && (
              <p className="mt-element-y text-body-base/body-base text-muted-foreground">
                {description}
              </p>
            )}
          </div>
          {/* 优化列表布局，调整列数 */}
          <ul role="list" className="space-y-content-y divide-y divide-border xl:col-span-3 2xl:col-span-4">
            {people && people.map((person, index) => {
              const socialUrls = getSocialUrls(person.socialLinks);
              
              return (
                <li key={index} className="flex flex-col gap-element-y pt-content-y sm:flex-row">
                  {/* 优化图片尺寸，添加更多响应式断点 */}
                                    <Picture
                    src={person.imageUrl || ''}
                    alt={`${person.name} avatar`}
                    width={288}
                    height={360}
                    densities={[1, 2, 3]}
                    quality={{ avif: 70, webp: 75, jpeg: 80, png: 85 }}
                    fit="cover"
                    className="aspect-[4/5] w-52 md:w-56 lg:w-60 xl:w-64 2xl:w-72 flex-none rounded-2xl object-cover"
                    style={{ display: 'block' }}
                  />
                  <div className="max-w-xl flex-auto lg:pl-element-x">
                    <h3 className="text-heading-3/heading-3 font-medium text-foreground">{person.name}</h3>
                    <p className="mt-element-y text-body-base/body-base text-secondary-foreground">{person.role}</p>
                    {person.bio && <p className="mt-element-y text-body-base/body-base text-muted-foreground">{person.bio}</p>}
                    <div className="mt-content-y">
                      <SocialIcons
                        {...socialUrls}
                        size="md"
                        variant="simple"
                        className="text-muted-foreground hover:text-foreground"
                      />
                    </div>
                  </div>
                </li>
              );
            })}
            
            {/* Hiring Card - 同样优化图片尺寸 */}
            {hiringSection && hiringSection.enabled && (
              <li className="flex flex-col gap-element-y pt-content-y sm:flex-row border-t border-dashed border-border">
                                <Picture
                  src={hiringSection.imageUrl || ''}
                  alt={`${hiringSection.title} - ${hiringSection.subtitle}`}
                  width={288}
                  height={360}
                  densities={[1, 2, 3]}
                  quality={{ avif: 70, webp: 75, jpeg: 80, png: 85 }}
                  fit="cover"
                  className="aspect-[4/5] w-52 md:w-56 lg:w-60 xl:w-64 2xl:w-72 flex-none rounded-2xl object-cover"
                  style={{ display: 'block' }}
                />
                <div className="max-w-xl flex-auto lg:pl-element-x">
                  <h3 className="text-heading-3/heading-3 font-medium text-foreground">{hiringSection.title}</h3>
                  <p className="mt-element-y text-body-base/body-base text-secondary-foreground">{hiringSection.subtitle}</p>
                  <p className="mt-element-y text-body-base/body-base text-muted-foreground">
                    {hiringSection.description}
                  </p>
                  <div className="mt-content-y">
                    {hiringSection.button && (
                      <a className="inline-flex items-center gap-x-2 text-body-small/body-small font-medium text-primary hover:text-primary/90 transition-colors" href={hiringSection.button.href}>
                        {hiringSection.button.label}
                        <svg className="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" aria-hidden="true"><path d="m9 18 6-6-6-6"/></svg>
                      </a>
                    )}
                  </div>
                </div>
              </li>
            )}
            {/* End Hiring Card */}
          </ul>
        </div>
      </div>
    </section>
  );
};

export default VerticalImagesWithBio;