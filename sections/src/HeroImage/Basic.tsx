import type { HeroImageSectionProps } from './types';
import { HighlightedTitle } from '../components/HighlightedTitle';
import { ButtonAdapter } from '../components/ButtonAdapter';
import { Picture } from '../components/Picture';

export default function Example({ title, description, buttons, announcement, image }: HeroImageSectionProps) {

    return (
        <section className="bg-background text-foreground" aria-labelledby="basic-hero-image-title">
            <div className="mx-auto max-w-container px-container-x py-section-y">
                {/* Grid */}
                <div className="grid md:grid-cols-2 gap-x-8 gap-y-content-y md:gap-x-12 xl:gap-x-16 md:items-center">

                <div>
                    {
                        announcement?.enabled && (
                            <div className="hidden sm:mb-content-y sm:flex">
                                <a href={announcement?.url} 
                                   target={announcement?.urlType === 'external' ? '_blank' : undefined}
                                   rel={announcement?.urlType === 'external' ? 'noopener noreferrer' : undefined}
                                   aria-label={`${announcement?.text}${announcement?.urlType === 'external' ? ' - Opens in a new window' : ''}`}
                                   className="relative rounded-full px-3 py-1 text-body-small/body-small text-muted-foreground ring-1 ring-border hover:ring-border/80 hover:text-foreground flex items-center">
                                    <span>{announcement?.text}</span>
                                    <span className="ml-2 p-1 inline-flex justify-center items-center rounded-full bg-muted text-primary">
                                        <svg className="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" aria-hidden="true"><path d="m9 18 6-6-6-6" /></svg>
                                    </span>
                                </a>
                            </div>
                        )
                    }
                    <h1 id="basic-hero-image-title" className="block text-heading-3/heading-3 font-bold text-foreground sm:text-heading-2/heading-2 lg:text-heading-1/heading-1">
                        <HighlightedTitle title={title} />
                    </h1>
                    <p className="mt-element-y text-body-base/body-base text-muted-foreground">{description}</p>

                    {/* Buttons */}
                    <div className="mt-content-y flex flex-col sm:flex-row items-center gap-3 sm:gap-x-4 w-full">
                        {buttons?.map((button, index) => (
                            <ButtonAdapter 
                                key={index}
                                button={button}
                                index={index}
                                className="w-full sm:w-auto px-8 py-3"
                            />
                        ))}
                    </div>
                    {/* End Buttons */}
                </div>
                {/* End Col */}

                <div className="relative mt-content-y md:mt-0 flex justify-center">
                    <div className="w-[85%] md:w-[90%] lg:w-[85%] xl:w-[80%] relative">
                        {/* 图片容器 - 确保SVG遮盖层正确定位 */}
                        <div className="relative">
                            {/* Picture组件迁移 - Basic Hero图片 */}
                            <Picture
                                src={image?.url || "https://images.unsplash.com/photo-1624571409412-1f253e1ecc89?ixlib=rb-4.0.3&auto=format&fit=crop&q=80&w=700&h=600"}
                                alt={image?.alt || 'Product showcase image with modern interface design'}
                                
                                // 📱 移动优先响应式配置 - Basic Hero，700×600基准
                                widths={[320, 480, 640, 700, 900, 1200]}
                                sizes="(max-width: 480px) 85vw, (max-width: 768px) 90vw, (max-width: 1024px) 40vw, (max-width: 1280px) 35vw, 30vw"
                                aspectRatio="7/6"  // 700×600 = 7:6宽高比
                                
                                // 性能优化 - 首屏图片使用高优先级
                                priority={true}
                                formats={['avif', 'webp', 'jpeg']}
                                quality={{ avif: 70, webp: 75, jpeg: 80, png: 85 }}  // Basic Hero质量配置
                                
                                className="w-full rounded-md shadow-xl block"  // 添加block确保Picture组件为块级
                                style={{ display: 'block' }}  // 确保块级显示，避免定位问题
                            />
                            
                            {/* SVG遮盖层 - 现在相对于Picture组件容器定位 */}
                            <div className="absolute bottom-0 start-0">
                                <svg className="w-2/3 ms-auto h-auto text-background" width="630" height="451" viewBox="0 0 630 451" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                                    <rect x="531" y="352" width="99" height="99" fill="currentColor" />
                                    <rect x="140" y="352" width="106" height="99" fill="currentColor" />
                                    <rect x="482" y="402" width="64" height="49" fill="currentColor" />
                                    <rect x="433" y="402" width="63" height="49" fill="currentColor" />
                                    <rect x="384" y="352" width="49" height="50" fill="currentColor" />
                                    <rect x="531" y="328" width="50" height="50" fill="currentColor" />
                                    <rect x="99" y="303" width="49" height="58" fill="currentColor" />
                                    <rect x="99" y="352" width="49" height="50" fill="currentColor" />
                                    <rect x="99" y="392" width="49" height="59" fill="currentColor" />
                                    <rect x="44" y="402" width="66" height="49" fill="currentColor" />
                                    <rect x="234" y="402" width="62" height="49" fill="currentColor" />
                                    <rect x="334" y="303" width="50" height="49" fill="currentColor" />
                                    <rect x="581" width="49" height="49" fill="currentColor" />
                                    <rect x="581" width="49" height="64" fill="currentColor" />
                                    <rect x="482" y="123" width="49" height="49" fill="currentColor" />
                                    <rect x="507" y="124" width="49" height="24" fill="currentColor" />
                                    <rect x="531" y="49" width="99" height="99" fill="currentColor" />
                                </svg>
                            </div>
                            {/* End SVG*/}
                        </div>
                        
                        {/* 装饰渐变背景 - 移到外层，保持原有效果 */}
                        <div className="absolute inset-0 -z-[1] bg-gradient-to-tr from-muted via-background/0 to-background/0 size-full rounded-md mt-4 -mb-4 me-4 -ms-4 lg:mt-6 lg:-mb-6 lg:me-6 lg:-ms-6"></div>
                    </div>
                </div>
                {/* End Col */}
                </div>
                {/* End Grid */}
            </div>
        </section>
    )
}