'use client'

import { useState } from 'react'
import type { HeroImageSectionProps } from './types';
import { Dialog, DialogPanel } from '@headlessui/react'
import { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline'
import { HighlightedTitle } from '../components/HighlightedTitle';
import { ButtonAdapter } from '../components/ButtonAdapter';
import { Picture } from '../components/Picture';

const navigation = [
  { name: 'Product', href: '#' },
  { name: 'Features', href: '#' },
  { name: 'Marketplace', href: '#' },
  { name: 'Company', href: '#' },
]

export default function Example({ title, description, buttons, announcement, image }: HeroImageSectionProps) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  return (
    <section className="bg-background text-foreground overflow-hidden" aria-labelledby="screenshot-dark-hero-image-title">
      {/* <PERSON><PERSON> commented out */}

      <div className="relative isolate pt-14">
        <div
          aria-hidden="true"
          className="absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80"
        >
          <div
            style={{
              clipPath:
                'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)',
            }}
            className="relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-primary/40 to-primary opacity-20 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]"
          />
        </div>
        <div className="py-section-y">
          <div className="mx-auto max-w-container px-container-x">
            <div className="mx-auto max-w-content text-center">
              {
                announcement?.enabled && (
                  <div className="flex justify-center">
                    <a className="inline-flex items-center gap-x-2 bg-accent/10 border border-accent/20 text-body-small/body-small text-accent-foreground p-1 ps-3 rounded-full transition hover:border-accent/30 focus:outline-none focus:border-accent/30" 
                       href={announcement?.url}
                       target={announcement?.urlType === 'external' ? '_blank' : undefined}
                       rel={announcement?.urlType === 'external' ? 'noopener noreferrer' : undefined}
                       aria-label={`${announcement?.text}${announcement?.urlType === 'external' ? ' - Opens in a new window' : ''}`}>
                      {announcement?.text}
                      <span className="py-1.5 px-2.5 inline-flex justify-center items-center gap-x-2 rounded-full bg-accent text-accent-foreground font-semibold text-body-small/body-small">
                        <svg className="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" aria-hidden="true"><path d="m9 18 6-6-6-6" /></svg>
                      </span>
                    </a>
                  </div>
                )
              }
              <h1 id="screenshot-dark-hero-image-title" className="text-balance mt-content-y text-heading-1/heading-1 font-semibold tracking-tight text-foreground">
                <HighlightedTitle title={title} />
              </h1>
              <p className="mt-element-y text-pretty text-body-base/body-base font-medium text-muted-foreground sm:text-body-large/body-large">
                {description}
              </p>
              <div className="mt-content-y text-center space-y-3 sm:space-y-0 sm:space-x-4 sm:inline-flex">
                        {buttons?.map((button, index) => (
                            <ButtonAdapter 
                                key={index}
                                button={button}
                                index={index}
                                className="w-full sm:w-auto px-8 py-3 block sm:inline-block"
                            />
                        ))}
              </div>
            </div>
            <div className="mt-section-y mx-auto max-w-[90%] xl:max-w-[85%] 2xl:max-w-[80%]">
              <div className="w-full aspect-[1.687/1] object-cover rounded-md bg-muted/10 shadow-2xl ring-1 ring-border overflow-hidden">
                <Picture
                  src={image?.url || "https://images.unsplash.com/photo-1618788372246-79faff0c3742?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2432&h=1442&q=80"}
                  alt={image?.alt || 'Application screenshot with dark theme showing the user interface and key features of the product'}
                  
                  // 📱 移动优先响应式配置 - ScreenshotDark大图 (复用Screenshot配置)
                  widths={[375, 480, 640, 768, 1024, 1280, 1600, 2048]}
                  sizes="(max-width: 480px) 375px, (max-width: 640px) 480px, (max-width: 768px) 640px, (max-width: 1024px) 768px, (max-width: 1280px) 1024px, (max-width: 1600px) 1280px, (max-width: 2048px) 1600px, 2048px"
                  // ❌ 移除aspectRatio - 让外层容器控制比例 aspect-[1.687/1]
                  
                  // 性能优化 - 首屏图片使用高优先级
                  priority={true}
                  formats={['avif', 'webp', 'jpeg']}
                  quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}  // 移动优先质量配置
                  
                  className="w-full h-full object-cover"
                  style={{ display: 'block' }}  // 确保容器-图片尺寸一致
                />
              </div>
            </div>
          </div>
        </div>
        <div
          aria-hidden="true"
          className="absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]"
        >
          <div
            style={{
              clipPath:
                'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)',
            }}
            className="relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-primary/40 to-primary opacity-20 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]"
          />
        </div>
      </div>
    </section>
  )
}
