import type { 
  HeroImageSectionProps, 
  Image
} from './types';
import { 
  Button, 
  Title, 
  TitleHighlight,
  Announcement,
  ButtonIcon
} from '../HeroText/types';

// Default image
export const defaultImage: Image = {
  url: "https://litpage.imgpipe.net/pixabay-5671778",
  alt: "Hero section image"
};

// Default button icon
export const defaultButtonIcon: ButtonIcon = {
  enabled: true,
  name: "arrow-right",
  position: "right"
};

// Default title highlight
export const defaultTitleHighlight: TitleHighlight = {
  enabled: true,
  text: "Beautiful",
  style: "gradient-blue"
};

// Default title
export const defaultTitle: Title = {
  text: "Build Beautiful Landing Pages in Minutes",
  highlight: defaultTitleHighlight
};

// Default announcement
export const defaultAnnouncement: Announcement = {
  enabled: false,
  text: "New Feature: AI-powered landing page builder is now available!",
  url: "/whats-new",
  urlType: "internal"
};

// Default buttons
export const defaultButtons: Button[] = [
  {
    label: "Get Started",
    url: "/",
    urlType: "internal",
    style: "primary",
    size: "medium",
    icon: {
      enabled: false
    }
  },
  {
    label: "Watch Demo",
    url: "/",
    urlType: "internal",
    style: "outline",
    size: "medium",
    icon: {
      enabled: true,
      name: "play",
      position: "left"
    }
  }
];

// Default HeroImage component props
export const defaultHeroImageProps: HeroImageSectionProps = {
  id: "",
  title: defaultTitle,
  description: "Create stunning, high-converting landing pages without writing a single line of code.",
  buttons: defaultButtons,
  announcement: defaultAnnouncement,
  image: defaultImage
};
