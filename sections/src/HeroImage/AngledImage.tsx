'use client'

import { useState } from 'react'
import type { HeroImageSectionProps } from './types';
import { Dialog, DialogPanel } from '@headlessui/react'
import { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline'
import { HighlightedTitle } from '../components/HighlightedTitle';
import { ButtonAdapter } from '../components/ButtonAdapter';
import { Picture } from '../components/Picture';

const navigation = [
  { name: 'Product', href: '#' },
  { name: 'Features', href: '#' },
  { name: 'Marketplace', href: '#' },
  { name: 'Company', href: '#' },
  { name: 'Log in', href: '#' },
]

export default function Example({ title, description, buttons, announcement, image }: HeroImageSectionProps) {
  // const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  return (
    <section className="relative bg-background text-foreground" aria-labelledby="angled-hero-image-title">
      {/* 移除了导航部分的注释代码 */}

      <div className="relative mx-auto max-w-container px-container-x">
        <div className="lg:flex lg:items-stretch">
          {/* 左侧内容区域 */}
          <div className="relative z-10 pt-14 lg:w-[45%] xl:w-[48%] 2xl:w-[45%] lg:pr-8 xl:pr-12 2xl:pr-16">
            {/* 斜角装饰 SVG */}
            <div className="absolute right-0 top-0 bottom-0 hidden w-40 translate-x-full transform lg:block overflow-hidden">
              <svg
                viewBox="0 0 100 100"
                preserveAspectRatio="none"
                aria-hidden="true"
                className="h-full w-full fill-background"
                style={{ maxHeight: '100%' }}
              >
                <polygon points="-1,0 100,0 -1,100" />
              </svg>
            </div>

            <div className="py-section-y sm:py-section-y lg:py-section-y">
              {/* 公告区域 */}
              {announcement?.enabled && (
                <div className="hidden sm:mb-content-y sm:flex">
                  <a href={announcement?.url} 
                     target={announcement?.urlType === 'external' ? '_blank' : undefined}
                     rel={announcement?.urlType === 'external' ? 'noopener noreferrer' : undefined}
                     aria-label={`${announcement?.text}${announcement?.urlType === 'external' ? ' - Opens in a new window' : ''}`}
                     className="relative rounded-full px-3 py-1 text-body-small/body-small text-muted-foreground ring-1 ring-border hover:ring-border/80 hover:text-foreground flex items-center">
                    <span>{announcement?.text}</span>
                    <span className="ml-2 p-1 inline-flex justify-center items-center rounded-full bg-muted text-primary">
                      <svg className="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" aria-hidden="true"><path d="m9 18 6-6-6-6" /></svg>
                    </span>
                  </a>
                </div>
              )}

              {/* 标题 */}
              <h1 id="angled-hero-image-title" className="text-pretty text-heading-1/heading-1 font-semibold tracking-tight text-foreground">
                <HighlightedTitle title={title} />
              </h1>
              
              {/* 描述 */}
              <p className="mt-element-y text-pretty text-body-base/body-base font-medium text-muted-foreground sm:text-body-large/body-large">
                {description}
              </p>
              
              {/* 按钮组 */}
              <div className="mt-content-y space-y-3 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-start">
                        {buttons?.map((button, index) => (
                            <ButtonAdapter 
                                key={index}
                                button={button}
                                index={index}
                                className="w-full sm:w-auto px-8 py-3 block sm:inline-block"
                            />
                        ))}
              </div>
            </div>
          </div>
          
          {/* 右侧图片区域 */}
          <div className="mt-content-y bg-muted lg:mt-0 lg:w-[55%] xl:w-[52%] 2xl:w-[55%]">
            <div className="aspect-[3/2] w-full shadow-xl overflow-hidden lg:h-full lg:aspect-auto">
              <Picture
                src={image?.url || "https://images.unsplash.com/photo-1624571409412-1f253e1ecc89?ixlib=rb-4.0.3&auto=format&fit=crop&q=80&w=1587&h=1054"}
                alt={image?.alt || 'Product interface displayed at an angle, showcasing the application design and key features'}
                
                // 📱 移动优先响应式配置 - AngledImage大图，保持遮盖层兼容
                widths={[375, 480, 640, 768, 1024, 1280, 1587]}
                sizes="(max-width: 480px) 375px, (max-width: 640px) 480px, (max-width: 768px) 640px, (max-width: 1024px) 768px, (max-width: 1280px) 1024px, (max-width: 1587px) 1280px, 1587px"
                // ❌ 移除aspectRatio - 让外层容器控制比例 (移动端3:2，桌面端auto)
                
                // 🔧 混合模式：响应式 + 填充高度
                forceFillHeight={true}  // 新增：强制填充高度，解决h-full问题
                
                // 性能优化 - 首屏图片使用高优先级
                priority={true}
                formats={['avif', 'webp', 'jpeg']}
                quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}  // 移动优先质量配置
                
                // 样式配置
                className="w-full h-full"
                style={{ 
                  display: 'block',
                  '--picture-fill-object-fit': 'cover' // CSS变量控制object-fit
                } as React.CSSProperties}
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
