'use client'

import { useState } from 'react'
import type { HeroImageSectionProps } from './types';
import { Dialog, DialogPanel } from '@headlessui/react'
import { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline'
import { ChevronRightIcon } from '@heroicons/react/20/solid'
import { HighlightedTitle } from '../components/HighlightedTitle';
import { ButtonAdapter } from '../components/ButtonAdapter';
import { Picture } from '../components/Picture';

const navigation = [
  { name: 'Product', href: '#' },
  { name: 'Features', href: '#' },
  { name: 'Marketplace', href: '#' },
  { name: 'Company', href: '#' },
]

export default function Example({ title, description, buttons, announcement, image }: HeroImageSectionProps) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  return (
    <section className="bg-background text-foreground" aria-labelledby="phone-hero-image-title">
      {/* 移除了导航部分的注释代码 */}

      <div className="relative isolate pt-14">
        <svg
          aria-hidden="true"
          className="absolute inset-0 -z-10 size-full stroke-border [mask-image:radial-gradient(100%_100%_at_top_right,white,transparent)]"
        >
          <defs>
            <pattern
              x="50%"
              y={-1}
              id="83fd4e5a-9d52-42fc-97b6-718e5d7ee527"
              width={200}
              height={200}
              patternUnits="userSpaceOnUse"
            >
              <path d="M100 200V.5M.5 .5H200" fill="none" />
            </pattern>
          </defs>
          <svg x="50%" y={-1} className="overflow-visible fill-muted">
            <path
              d="M-100.5 0h201v201h-201Z M699.5 0h201v201h-201Z M499.5 400h201v201h-201Z M-300.5 600h201v201h-201Z"
              strokeWidth={0}
            />
          </svg>
          <rect fill="url(#83fd4e5a-9d52-42fc-97b6-718e5d7ee527)" width="100%" height="100%" strokeWidth={0} />
        </svg>
        <div className="mx-auto max-w-container px-container-x py-section-y lg:flex lg:items-center lg:gap-x-6 xl:gap-x-8 2xl:gap-x-10">
          <div className="mx-auto max-w-content lg:mx-0 lg:flex-1 lg:max-w-[60%] xl:max-w-[55%] 2xl:max-w-[50%]">
            {
              announcement?.enabled && (
                <div className="flex mb-10">
                  <div className="relative flex items-center gap-x-4 rounded-full bg-background px-4 py-1 text-body-small/body-small text-muted-foreground ring-1 ring-border hover:ring-border/80">
                    <a 
                      href={announcement?.url} 
                      className="flex items-center gap-x-1"
                      target={announcement?.urlType === 'external' ? '_blank' : undefined}
                      rel={announcement?.urlType === 'external' ? 'noopener noreferrer' : undefined}
                      aria-label={`${announcement?.text}${announcement?.urlType === 'external' ? ' - Opens in a new window' : ''}`}
                    >
                      <span aria-hidden="true" className="absolute inset-0" />
                      {announcement?.text}
                      <ChevronRightIcon aria-hidden="true" className="-mr-2 size-5 text-muted-foreground/70" />
                    </a>
                  </div>
                </div>
              )
            }

            <h1 id="phone-hero-image-title" className="mt-content-y text-pretty text-heading-1/heading-1 font-semibold tracking-tight text-foreground">
              <HighlightedTitle title={title} />
            </h1>
            <p className="mt-element-y text-pretty text-body-base/body-base font-medium text-muted-foreground sm:text-body-large/body-large">
              {description}
            </p>
            <div className="mt-content-y flex flex-col sm:flex-row items-center gap-3 sm:gap-x-4">
                        {buttons?.map((button, index) => (
                            <ButtonAdapter 
                                key={index}
                                button={button}
                                index={index}
                                className="w-full sm:w-auto px-8 py-3"
                            />
                        ))}
            </div>
          </div>
          <div className="mt-section-y lg:mt-0 lg:shrink-0 lg:flex-1 lg:flex lg:items-center lg:justify-center xl:justify-center 2xl:justify-center">
            <svg role="img" viewBox="0 0 366 729" className="mx-auto w-full max-w-[320px] lg:max-w-[340px] xl:max-w-[360px] 2xl:max-w-[380px] drop-shadow-xl">
              <title>App screenshot</title>
              <defs>
                <clipPath id="2ade4387-9c63-4fc4-b754-10e687a0d332">
                  <rect rx={36} width={316} height={684} />
                </clipPath>
              </defs>
              <path
                d="M363.315 64.213C363.315 22.99 341.312 1 300.092 1H66.751C25.53 1 3.528 22.99 3.528 64.213v44.68l-.857.143A2 2 0 0 0 1 111.009v24.611a2 2 0 0 0 1.671 1.973l.95.158a2.26 2.26 0 0 1-.093.236v26.173c.212.1.398.296.541.643l-1.398.233A2 2 0 0 0 1 167.009v47.611a2 2 0 0 0 1.671 1.973l1.368.228c-.139.319-.314.533-.511.653v16.637c.221.104.414.313.56.689l-1.417.236A2 2 0 0 0 1 237.009v47.611a2 2 0 0 0 1.671 1.973l1.347.225c-.135.294-.302.493-.49.607v377.681c0 41.213 22 63.208 63.223 63.208h95.074c.947-.504 2.717-.843 4.745-.843l.141.001h.194l.086-.001 33.704.005c1.849.043 3.442.37 4.323.838h95.074c41.222 0 63.223-21.999 63.223-63.212v-394.63c-.259-.275-.48-.796-.63-1.47l-.011-.133 1.655-.276A2 2 0 0 0 366 266.62v-77.611a2 2 0 0 0-1.671-1.973l-1.712-.285c.148-.839.396-1.491.698-1.811V64.213Z"
                fill="#4B5563"
              />
              <path
                d="M16 59c0-23.748 19.252-43 43-43h246c23.748 0 43 19.252 43 43v615c0 23.196-18.804 42-42 42H58c-23.196 0-42-18.804-42-42V59Z"
                fill="#343E4E"
              />
              <foreignObject
                width={316}
                height={684}
                clipPath="url(#2ade4387-9c63-4fc4-b754-10e687a0d332)"
                transform="translate(24 24)"
              >
                {/* 图片URL现在统一存储为相对路径格式，Picture组件会自动处理URL转换 */}
                <Picture 
                  alt={image?.alt || "Mobile app screenshot showing the application interface on a smartphone device"} 
                  src={image?.url || "https://images.unsplash.com/photo-1551650975-87deedd944c3?ixlib=rb-1.2.1&auto=format&fit=crop&w=316&h=684&q=90"} 
                  width={316}
                  height={684}
                  priority={true}
                  style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                  sizes="316px"
                />
              </foreignObject>
            </svg>
          </div>
        </div>
      </div>
    </section>
  )
}
