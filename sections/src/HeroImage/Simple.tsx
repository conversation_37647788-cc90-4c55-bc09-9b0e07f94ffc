import { ChevronRightIcon } from '@heroicons/react/20/solid'
import type { HeroImageSectionProps } from './types';
import { HighlightedTitle } from '../components/HighlightedTitle';
import { ButtonAdapter } from '../components/ButtonAdapter';
import { Picture } from '../components/Picture';

export default function Example({ title, description, buttons, announcement, image }: HeroImageSectionProps) {

  return (
    <section className="relative isolate overflow-hidden bg-background text-foreground" aria-labelledby="simple-hero-image-title">
      <svg
        aria-hidden="true"
        className="absolute inset-0 -z-10 size-full stroke-border [mask-image:radial-gradient(100%_100%_at_top_right,white,transparent)]"
      >
        <defs>
          <pattern
            x="50%"
            y={-1}
            id="0787a7c5-978c-4f66-83c7-11c213f99cb7"
            width={200}
            height={200}
            patternUnits="userSpaceOnUse"
          >
            <path d="M.5 200V.5H200" fill="none" />
          </pattern>
        </defs>
        <rect fill="url(#0787a7c5-978c-4f66-83c7-11c213f99cb7)" width="100%" height="100%" strokeWidth={0} />
      </svg>
      <div className="mx-auto max-w-container px-container-x pb-section-y pt-content-y sm:pb-section-y lg:flex lg:justify-center lg:gap-x-8 xl:gap-x-12 2xl:gap-x-16 lg:px-container-x lg:py-section-y">
        <div className="mx-auto max-w-content lg:mx-0 lg:w-[45%] xl:w-[48%] 2xl:w-[45%] lg:flex-shrink-0 lg:pt-8">
          {
            announcement?.enabled && (
              <div className="mt-content-y sm:mt-section-y lg:mt-content-y">
                <a 
                  href={announcement?.url} 
                  className="inline-flex space-x-6"
                  aria-label={`${announcement?.text}${announcement?.urlType === 'external' ? ' - Opens in a new window' : ''}`}
                  target={announcement?.urlType === 'external' ? '_blank' : undefined}
                  rel={announcement?.urlType === 'external' ? 'noopener noreferrer' : undefined}
                >
                  <span className="inline-flex items-center space-x-2 text-body-small/body-small font-medium text-muted-foreground">
                    <span>{announcement?.text}</span>
                    <ChevronRightIcon aria-hidden="true" className="size-5 text-muted-foreground/70" />
                  </span>
                </a>
              </div>
            )
          }


          <h1 id="simple-hero-image-title" className="mt-element-y text-pretty text-heading-1/heading-1 font-semibold tracking-tight text-foreground">
            <HighlightedTitle title={title} />
          </h1>
          <p className="mt-element-y text-pretty text-body-base/body-base font-medium text-muted-foreground sm:text-body-large/body-large">
            {description}
          </p>
          <div className="mt-content-y flex flex-col sm:flex-row items-center gap-3 sm:gap-x-4">
            {buttons?.map((button, index) => (
              <ButtonAdapter 
                key={index}
                button={button}
                index={index}
                className="w-full sm:w-auto px-8 py-3"
              />
            ))}
          </div>
        </div>
        <div className="mx-auto mt-content-y w-full sm:mt-section-y lg:mt-0 lg:w-[45%] xl:w-[48%] 2xl:w-[45%] lg:flex lg:items-center">
          <div className="relative w-full overflow-hidden rounded-xl bg-muted/20 p-2 ring-1 ring-inset ring-border lg:rounded-2xl lg:p-4">
            <div className="relative w-full aspect-[1.69/1] overflow-hidden rounded-md shadow-2xl ring-1 ring-border">
              <Picture
                src={image?.url || "https://images.unsplash.com/photo-1624571409412-1f253e1ecc89?ixlib=rb-4.0.3&auto=format&fit=crop&q=80"}
                alt={image?.alt || 'Screenshot of the product interface showing key features and functionality'}
                widths={[375, 480, 640, 768, 1024, 1280, 1600, 2048]}
                sizes="(max-width: 480px) 375px, (max-width: 640px) 480px, (max-width: 768px) 640px, (max-width: 1024px) 768px, (max-width: 1280px) 1024px, (max-width: 1600px) 1280px, (max-width: 2048px) 1600px, 2048px"
                forceFillHeight={true}
                quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                formats={['avif', 'webp', 'jpeg']}
                priority={true}
                className="w-full h-full object-cover md:object-contain"
                style={{ 
                  display: 'block',
                  '--picture-fill-object-fit': 'cover'
                } as React.CSSProperties}
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
