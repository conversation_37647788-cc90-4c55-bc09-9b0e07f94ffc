import type { HeroImageSectionProps } from './types';
import { HighlightedTitle } from '../components/HighlightedTitle';
import { ButtonAdapter } from '../components/ButtonAdapter';
import { Picture } from '../components/Picture';

export default function Example({ title, description, buttons, announcement, image }: HeroImageSectionProps) {

  return (
    <section className="relative bg-background text-foreground" aria-labelledby="right-image-hero-title">
      <div className="mx-auto max-w-container px-container-x lg:grid lg:grid-cols-12 lg:gap-x-8 lg:items-center">
        <div className="pb-section-y pt-content-y lg:col-span-7 lg:py-section-y xl:col-span-6">
          <div className="mx-auto max-w-content lg:mx-0">
            {
              announcement?.enabled && (
                <div className="hidden sm:mt-32 sm:flex lg:mt-16">
                  <a href={announcement?.url} 
                     target={announcement?.urlType === 'external' ? '_blank' : undefined}
                     rel={announcement?.urlType === 'external' ? 'noopener noreferrer' : undefined}
                     aria-label={`${announcement?.text}${announcement?.urlType === 'external' ? ' - Opens in a new window' : ''}`}
                     className="relative rounded-full px-3 py-1 text-body-small/6 text-muted-foreground ring-1 ring-border hover:ring-ring hover:text-foreground flex items-center">
                    <span>{announcement?.text}</span>
                    <span className="ml-2 p-1 inline-flex justify-center items-center rounded-full bg-accent text-primary">
                      <svg className="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" aria-hidden="true"><path d="m9 18 6-6-6-6" /></svg>
                    </span>
                  </a>
                </div>
              )
            }

            <h1 id="right-image-hero-title" className="mt-24 text-pretty text-heading-1/heading-1 font-semibold tracking-tight text-foreground sm:mt-10">
              <HighlightedTitle title={title} />
            </h1>
            <p className="mt-element-y text-pretty text-body-base/body-base font-medium text-muted-foreground sm:text-body-large/body-large">
              {description}
            </p>
            <div className="mt-content-y space-y-3 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-start">
              {buttons?.map((button, index) => (
                <ButtonAdapter 
                  key={index}
                  button={button}
                  index={index}
                  className="w-full sm:w-auto px-8 py-3 block sm:inline-block"
                />
              ))}
            </div>
          </div>
        </div>
        <div className="relative lg:col-span-5 lg:-mr-8 xl:col-span-6 xl:relative xl:inset-auto xl:left-auto xl:mr-0">
          <div className="w-full bg-muted rounded-lg overflow-hidden aspect-[3/2]">
            <Picture
              src={image?.url || "https://images.unsplash.com/photo-1498050108023-c5249f4df085?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1200&h=800&q=80"}
              alt={image?.alt || 'Product showcase image displaying the application interface and key features'}
              
              // 📱 移动优先响应式配置 - RightImage布局 (3:2比例)
              widths={[375, 480, 640, 768, 1024, 1200]}
              sizes="(max-width: 480px) 375px, (max-width: 640px) 480px, (max-width: 768px) 640px, (max-width: 1024px) 768px, (max-width: 1200px) 1024px, 1200px"
              // ❌ 移除aspectRatio - 让外层容器控制比例 aspect-[3/2]
              
              // 性能优化 - 首屏图片使用高优先级
              priority={true}
              formats={['avif', 'webp', 'jpeg']}
              quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}  // 移动优先质量配置
              
              className="w-full h-full object-cover"
              style={{ display: 'block' }}  // 确保容器-图片尺寸一致
            />
          </div>
        </div>
      </div>
    </section>
  )
}
