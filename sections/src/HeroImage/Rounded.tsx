import type { HeroImageSectionProps } from './types';
import { HighlightedTitle } from '../components/HighlightedTitle';
import { ButtonAdapter } from '../components/ButtonAdapter';
import { Picture } from '../components/Picture';

export default function Example({ title, description, buttons, announcement, image }: HeroImageSectionProps) {

    return (
<section className="bg-background text-foreground mx-auto max-w-container px-container-x py-section-y" aria-labelledby="rounded-hero-image-title">
  {/* Grid */}
  <div className="grid lg:grid-cols-7 lg:gap-x-8 xl:gap-x-12 lg:items-center">
    <div className="lg:col-span-3">
    {
          announcement?.enabled && (
            <div className="flex">
              <a 
                className="inline-flex items-center gap-x-2 bg-background border border-border text-body-small/body-small text-foreground p-1 ps-3 rounded-full transition hover:border-border/80 focus:outline-none focus:border-border" 
                href={announcement?.url}
                target={announcement?.urlType === 'external' ? '_blank' : undefined}
                rel={announcement?.urlType === 'external' ? 'noopener noreferrer' : undefined}
                aria-label={`${announcement?.text}${announcement?.urlType === 'external' ? ' - Opens in a new window' : ''}`}
              >
                {announcement?.text}
                <span className="py-1.5 px-2.5 inline-flex justify-center items-center gap-x-2 rounded-full bg-muted font-semibold text-body-small/body-small text-muted-foreground">
                  <svg className="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" aria-hidden="true"><path d="m9 18 6-6-6-6" /></svg>
                </span>
              </a>
            </div>
          )
        }
      <h1 id="rounded-hero-image-title" className="block mt-content-y text-heading-3/heading-3 font-bold text-foreground sm:text-heading-2/heading-2 md:text-heading-1/heading-1 lg:text-heading-1/heading-1">
        <HighlightedTitle title={title} />
      </h1>
      <p className="mt-element-y text-body-base/body-base text-muted-foreground">{description}</p>

      <div className="mt-content-y flex flex-col items-center gap-3 sm:flex-row sm:gap-x-4">
        {buttons?.map((button, index) => (
          <ButtonAdapter 
            key={index}
            button={button}
            index={index}
            className="w-full sm:w-auto px-8 py-3"
          />
        ))}
      </div>

    </div>
    {/* End Col */}

    <div className="lg:col-span-4 mt-10 lg:mt-0">
      {/* 圆角容器 - 确保容器和图片的圆角一致性 */}
      <div className="rounded-xl overflow-hidden aspect-[9/7]">
        {/* Picture组件迁移 - Rounded Hero图片 */}
        <Picture
          src={image?.url || "https://images.unsplash.com/photo-1618788372246-79faff0c3742?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=900&h=700&q=80"}
          alt={image?.alt || 'Product showcase image with rounded corners displaying the application interface'}
          
          // 📱 移动优先响应式配置 - Rounded Hero，900×700基准
          widths={[360, 480, 640, 768, 900, 1080, 1200]}
          sizes="(max-width: 640px) 90vw, (max-width: 1024px) 50vw, (max-width: 1280px) 45vw, 40vw"
          // ❌ 移除aspectRatio - 让外层容器控制比例，避免冲突
          
          // 🔧 混合模式：响应式 + 填充高度，解决圆角容器填充问题
          forceFillHeight={true}  // 关键：启用混合模式，填满圆角容器
          
          // 性能优化 - 首屏图片使用高优先级
          priority={true}
          formats={['avif', 'webp', 'jpeg']}
          quality={{ avif: 75, webp: 80, jpeg: 85, png: 90 }}  // Rounded Hero高质量配置
          
          className="w-full h-full object-cover"  // 填满容器，继承圆角
          style={{ 
            display: 'block',
            '--picture-fill-object-fit': 'cover'  // CSS变量控制填充方式
          } as React.CSSProperties}
        />
      </div>
    </div>
    {/* End Col */}
  </div>
  {/* End Grid */}
</section>
    )
}