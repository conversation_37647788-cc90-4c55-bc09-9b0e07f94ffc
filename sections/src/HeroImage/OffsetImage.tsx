'use client'

import { useState } from 'react'
import type { HeroImageSectionProps } from './types';
import { Dialog, DialogPanel } from '@headlessui/react'
import { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline'
import { HighlightedTitle } from '../components/HighlightedTitle';
import { ButtonAdapter } from '../components/ButtonAdapter';
import { Picture } from '../components/Picture';

const navigation = [
  { name: 'Product', href: '#' },
  { name: 'Features', href: '#' },
  { name: 'Marketplace', href: '#' },
  { name: 'Company', href: '#' },
]

export default function Example({ title, description, buttons, announcement, image }: HeroImageSectionProps) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  return (
    <section className="relative overflow-hidden bg-background text-foreground" aria-labelledby="offset-image-hero-title">
      <div className="mx-auto max-w-container px-container-x py-section-y text-center">
        <div className="max-w-content mx-auto text-center">
          {announcement?.enabled && (
            <div className="inline-block">
              <a 
                href={announcement?.url}
                target={announcement?.urlType === 'external' ? '_blank' : undefined}
                rel={announcement?.urlType === 'external' ? 'noopener noreferrer' : undefined}
                aria-label={`${announcement?.text}${announcement?.urlType === 'external' ? ' - Opens in a new window' : ''}`}
                className="inline-flex items-center gap-x-1.5 py-1.5 px-3 rounded-full text-body-small/body-small font-medium bg-background border border-border text-foreground hover:bg-muted">
                {announcement?.text}
              </a>
            </div>
          )}

          <div className="mt-content-y w-full text-center">
            <h1 id="offset-image-hero-title" className="block font-bold text-foreground text-heading-2/heading-2 md:text-heading-1/heading-1 lg:text-heading-1/heading-1">
              <HighlightedTitle title={title} />
            </h1>
          </div>

          <div className="mt-element-y w-full text-center">
            <p className="text-body-base/body-base text-muted-foreground mx-auto max-w-content">{description}</p>
          </div>

          <div className="mt-content-y flex flex-col space-y-3 sm:flex-row sm:space-y-0 sm:space-x-4 sm:justify-center">
            {buttons?.map((button, index) => (
              <ButtonAdapter 
                key={index}
                button={button}
                index={index}
                className="py-3 px-8 w-full sm:w-auto flex-shrink-0"
              />
            ))}
          </div>
        </div>

        <div className="mt-section-y relative mx-auto max-w-[90%] xl:max-w-[85%] 2xl:max-w-[80%]">
          <div 
            className="w-full aspect-[16/9] rounded-xl overflow-hidden"
            role="img"
            aria-label={image?.alt || "Product showcase image displaying the application interface"}
          >
<Picture
                src={image?.url || "https://images.unsplash.com/photo-1606868306217-dbf5046868d2?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1981&q=80"}
                alt={image?.alt || "Product showcase image displaying the application interface"}
                
                // 📱 移动优先响应式配置 - 横幅图片
                widths={[375, 768, 1024, 1440, 1920]}
                sizes="(max-width: 480px) 375px, (max-width: 768px) 768px, (max-width: 1024px) 1024px, (max-width: 1440px) 1440px, 1920px"
                
                // 性能优化 - 首屏图片使用高优先级
                priority={true}
                formats={['avif', 'webp', 'jpeg']}
                quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                
                className="w-full h-full object-cover"
                style={{ display: 'block' }}
              />
          </div>

          <div className="absolute bottom-8 sm:bottom-10 md:bottom-12 -left-8 sm:-left-12 md:-left-16 lg:-left-20 -z-[1] w-24 h-24 sm:w-32 sm:h-32 md:w-40 md:h-40 lg:w-48 lg:h-48 bg-primary p-px rounded-lg shadow-lg">
            <div className="bg-background w-full h-full rounded-lg"></div>
          </div>

          <div className="absolute -top-8 sm:-top-10 md:-top-12 -right-8 sm:-right-12 md:-right-16 lg:-right-20 -z-[1] w-24 h-24 sm:w-32 sm:h-32 md:w-40 md:h-40 lg:w-48 lg:h-48 bg-primary p-px rounded-lg shadow-lg">
            <div className="bg-background w-full h-full rounded-lg"></div>
          </div>
        </div>
      </div>
    </section>
  )
}
