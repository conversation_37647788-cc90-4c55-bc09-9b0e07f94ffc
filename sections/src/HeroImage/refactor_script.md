# HeroImage 组件重构计划

## 需要重构的组件列表

1. ✅ Simple.tsx - 已完成
2. ✅ Split.tsx - 已完成
3. ⏳ OffsetImage.tsx
4. ⏳ Phone.tsx
5. ⏳ AngledImage.tsx
6. ⏳ ScreenshotDark.tsx
7. ⏳ Screenshot.tsx
8. ⏳ RightImage.tsx
9. ⏳ Rounded.tsx
10. ⏳ Basic.tsx

## 重构模式

每个组件需要进行以下修改：

1. **更新导入**:
   ```tsx
   // 移除
   import { ButtonIcon } from '../components/ButtonIcon';
   
   // 添加
   import { ButtonAdapter } from '../components/ButtonAdapter';
   ```

2. **移除辅助函数**:
   ```tsx
   // 移除这些函数
   const getButtonUrl = (url: string, urlType: string) => { ... };
   const getButtonTarget = (urlType: string) => { ... };
   const getButtonRel = (urlType: string) => { ... };
   ```

3. **简化按钮渲染**:
   ```tsx
   // 替换复杂的按钮代码
   {buttons?.map((button, index) => (
     <ButtonAdapter 
       key={index}
       button={button}
       className="w-full sm:w-auto px-8 py-3"
     />
   ))}
   ```

## 预期收益

- 每个组件减少 30-40 行代码
- 消除重复的按钮逻辑
- 统一按钮样式和行为
- 提升可维护性 