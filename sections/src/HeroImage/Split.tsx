import { ChevronRightIcon } from '@heroicons/react/20/solid'
import type { HeroImageSectionProps } from './types';
import { HighlightedTitle } from '../components/HighlightedTitle';
import { ButtonAdapter } from '../components/ButtonAdapter';
import { Picture } from '../components/Picture';

export default function Example({ title, description, buttons, announcement, image }: HeroImageSectionProps) {

  return (
    <section className="bg-background text-foreground" aria-labelledby="split-hero-image-title">
      <div className="relative isolate overflow-hidden bg-gradient-to-b from-primary/5">
        <div className="mx-auto max-w-container px-container-x pb-section-y pt-content-y lg:grid lg:grid-cols-2 lg:gap-x-4 xl:gap-x-6 2xl:gap-x-8 lg:py-section-y">
          <div className="px-6 lg:px-0 lg:pt-4 lg:pr-4 xl:pr-6 2xl:pr-8">
            <div className="mx-auto max-w-content">
              <div className="w-full">
                {
                  announcement?.enabled && (
                    <div className="mt-24 sm:mt-32 lg:mt-16">
                      <a 
                        href={announcement?.url} 
                        className="inline-flex space-x-6"
                        aria-label={`${announcement?.text}${announcement?.urlType === 'external' ? ' - Opens in a new window' : ''}`}
                        target={announcement?.urlType === 'external' ? '_blank' : undefined}
                        rel={announcement?.urlType === 'external' ? 'noopener noreferrer' : undefined}
                      >
                        <span className="inline-flex items-center space-x-2 text-body-small/body-small font-medium text-muted-foreground">
                          <span>{announcement?.text}</span>
                          <ChevronRightIcon className="size-5 text-muted-foreground/70" aria-hidden="true" />
                        </span>
                      </a>
                    </div>
                  )
                }

                <h1 id="split-hero-image-title" className="mt-content-y text-pretty text-heading-1/heading-1 font-semibold tracking-tight text-foreground">
                  <HighlightedTitle title={title} />
                </h1>
                <p className="mt-element-y text-body-base/body-base text-muted-foreground">
                  {description}
                </p>
                <div className="mt-content-y space-y-3 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-start">
                  {buttons?.map((button, index) => (
                    <ButtonAdapter 
                      key={index}
                      button={button}
                      index={index}
                      className="w-full sm:w-auto px-8 py-3 block sm:inline-block"
                    />
                  ))}
                </div>
              </div>
            </div>
          </div>
          <div className="mt-section-y md:mx-auto md:max-w-content lg:mx-0 lg:mt-0 lg:w-full lg:pl-4 xl:pl-6 2xl:pl-8">
            <div
              className="absolute inset-y-0 right-1/2 -z-10 -mr-10 w-[200%] skew-x-[-30deg] bg-background shadow-xl shadow-primary/10 ring-1 ring-border md:-mr-20 lg:-mr-36"
              aria-hidden="true"
            />
            <div className="shadow-lg md:rounded-3xl">
              <div className="bg-primary [clip-path:inset(0)] md:[clip-path:inset(0_round_theme(borderRadius.3xl))]">
                <div
                  className="absolute -inset-y-px left-1/2 -z-10 ml-10 w-[200%] skew-x-[-30deg] bg-primary-foreground opacity-20 ring-1 ring-inset ring-primary-foreground md:ml-20 lg:ml-36"
                  aria-hidden="true"
                />
                <div className="relative px-4 pt-6 sm:pt-8 md:pl-8 lg:pl-4 md:pr-0">
                  <div className="mx-auto max-w-2xl md:mx-0 md:max-w-none">
                    <div className="w-full overflow-hidden rounded-tl-xl bg-muted">
                      <div className="relative w-full aspect-[4/3] sm:aspect-[3/2] lg:aspect-[16/10] max-h-[400px] overflow-hidden">
                        <Picture
                          src={image?.url || "https://images.unsplash.com/photo-1618788372246-79faff0c3742?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1200&h=800&q=80"}
                          alt={image?.alt || "Product interface screenshot showing application features and design"}
                          
                          // 📱 移动优先响应式配置 - Split布局图片
                          widths={[375, 480, 640, 768, 1024, 1200]}
                          sizes="(max-width: 480px) 375px, (max-width: 640px) 480px, (max-width: 768px) 640px, (max-width: 1024px) 768px, (max-width: 1200px) 1024px, 1200px"
                          
                          // ✅ 添加宽高比控制，防止布局移位
                          aspectRatio="4/3"
                          forceFillHeight={true}
                          
                          // 性能优化 - 首屏图片使用高优先级
                          priority={true}
                          formats={['avif', 'webp', 'jpeg']}
                          quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}  // 移动优先质量配置
                          
                          className="absolute inset-0 w-full h-full object-cover"
                          style={{ display: 'block' }}  // 避免flex冲突
                          aria-describedby="split-hero-image-title"
                        />
                      </div>
                    </div>
                  </div>
                  <div
                    className="pointer-events-none absolute inset-0 ring-1 ring-inset ring-border md:rounded-3xl"
                    aria-hidden="true"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="absolute inset-x-0 bottom-0 -z-10 h-24 bg-gradient-to-t from-background sm:h-32" />
      </div>
    </section>
  )
}
