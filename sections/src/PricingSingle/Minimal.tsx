import React from 'react';
import { CheckIcon } from '@heroicons/react/20/solid';
import { PricingSingleSectionProps, Tier } from './types';
import { PriceDisplay } from './PriceDisplay';
import { ButtonAdapter } from '../components/ButtonAdapter';

// 默认价格层级，用于在未提供 tier 时显示
const defaultTier: Tier = {
  name: "Lifetime membership",
  description: "Lorem ipsum dolor sit amet consect etur adipisicing elit. Itaque amet indis perferendis blanditiis repellendus etur quidem assumenda.",
  featuresTitle: "What's included",
  features: [
    "Private forum access",
    "Member resources",
    "Entry to annual conference",
    "Official member t-shirt"
  ],
  subtitle: "Pay once, own it forever",
  price: 349.5,
  originalPrice: 499.99,
  currency: "USD",
  billingPeriod: {
    type: "one-time"
  },
  button: {
    label: "Get access",
    url: "#",
    urlType: "internal",
    style: "primary",
    size: "medium",
    icon: { enabled: true, name: "arrow-right", position: "right" }
  },
  footnote: "Invoices and receipts available for easy company reimbursement"
};

export default function PricingSingleMinimal({ 
  id = 'pricing-single-minimal',
  tagline = 'Pricing', 
  title = 'Simple no-tricks pricing', 
  description = 'Distinctio et nulla eum soluta et neque labore quibusdam. Saepe et quasi iusto modi velit ut non voluptas in. Explicabo id ut laborum.', 
  tier
}: PricingSingleSectionProps) {
  // 使用提供的 tier 或默认值
  const pricingTier = tier || defaultTier;

  return (
    <section id={id} className="bg-background py-section-y" aria-labelledby="pricing-minimal-title">
      <div className="mx-auto max-w-container px-container-x">
        <div className="mx-auto max-w-2xl text-center">
          {tagline && (
            <div className="inline-flex items-center gap-2 rounded-full border border-border bg-background px-4 py-2 text-body-small/body-small font-medium text-primary">
              <div className="h-1.5 w-1.5 rounded-full bg-primary" />
              {tagline}
            </div>
          )}
          <h2 id="pricing-minimal-title" className="text-pretty mt-8 text-heading-2/heading-2 font-bold tracking-tight text-foreground sm:text-balance">
            {title}
          </h2>
          <p className="mx-auto mt-6 max-w-xl text-pretty text-body-large/body-large text-muted-foreground leading-relaxed">
            {description}
          </p>
        </div>
        
        <div className="mx-auto mt-16 sm:mt-20 max-w-lg">
          <div className="rounded-2xl border border-border bg-card p-8 sm:p-10 hover:shadow-lg transition-shadow duration-300">
            <div className="text-center space-y-8">
              {/* 标题和描述 */}
              <div className="space-y-4">
                <h3 id="pricing-tier-name" className="text-heading-3/heading-3 font-bold tracking-tight text-foreground">
                  {pricingTier.name}
                </h3>
                <p className="text-body-base/body-base text-muted-foreground leading-relaxed">
                  {pricingTier.description}
                </p>
              </div>
              
              {/* 价格显示 */}
              <div className="py-6">
                <p className="text-body-base/body-base font-medium text-foreground mb-4">
                  {pricingTier.subtitle}
                </p>
                <PriceDisplay 
                  price={pricingTier.price} 
                  originalPrice={pricingTier.originalPrice}
                  currency={pricingTier.currency} 
                  billingPeriod={pricingTier.billingPeriod}
                />
              </div>
              
              {/* 功能列表 */}
              <div className="space-y-6">
                <div className="flex items-center justify-center gap-3">
                  <div className="h-px flex-1 bg-border" />
                  <h4 id="features-list-title" className="text-body-small/body-small font-medium text-primary uppercase tracking-wider">
                    {pricingTier.featuresTitle}
                  </h4>
                  <div className="h-px flex-1 bg-border" />
                </div>
                
                <ul role="list" aria-labelledby="features-list-title" className="space-y-3">
                  {pricingTier.features.map((feature) => (
                    <li 
                      key={feature} 
                      className="flex items-center justify-center gap-3 p-2 rounded-lg hover:bg-accent/20 transition-colors duration-200"
                    >
                      <CheckIcon className="h-4 w-4 text-primary flex-shrink-0" aria-hidden="true" />
                      <span className="text-body-small/body-small text-muted-foreground text-center">
                        {feature}
                      </span>
                    </li>
                  ))}
                </ul>
              </div>
              
              {/* 按钮 */}
              <div className="pt-6">
                <ButtonAdapter
                  button={pricingTier.button}
                  className="w-full rounded-xl px-6 py-3.5 text-center text-body-base/body-base font-semibold shadow-sm hover:shadow-md transition-shadow duration-200 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
                  accessibility={{
                    description: `Subscribe to ${pricingTier.name}`,
                    isPrimary: true
                  }}
                  ariaProps={{
                    'aria-describedby': "pricing-tier-name"
                  }}
                />
              </div>
              
              {/* 脚注 */}
              {pricingTier.footnote && (
                <p className="text-body-small/body-small text-muted-foreground leading-relaxed">
                  {pricingTier.footnote}
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
