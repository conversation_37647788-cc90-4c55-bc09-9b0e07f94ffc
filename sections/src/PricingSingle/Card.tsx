import React from 'react';
import { 
  CheckIcon, 
  SparklesIcon, 
  StarIcon, 
  ArrowRightIcon,
  ArrowLeftIcon,
  ArrowDownIcon,
  PlayIcon,
  CheckCircleIcon
} from '@heroicons/react/20/solid';
import { PricingSingleSectionProps, Tier } from './types';
import { PriceDisplay } from './PriceDisplay';
import { ButtonAdapter } from '../components/ButtonAdapter';

// 默认价格层级，用于在未提供 tier 时显示
const defaultTier: Tier = {
  name: "Lifetime membership",
  description: "Lorem ipsum dolor sit amet consect etur adipisicing elit. Itaque amet indis perferendis blanditiis repellendus etur quidem assumenda.",
  featuresTitle: "What's included",
  features: [
    "Private forum access",
    "Member resources",
    "Entry to annual conference",
    "Official member t-shirt"
  ],
  subtitle: "Pay once, own it forever",
  price: 349.5,
  originalPrice: 499.99,
  currency: "USD",
  billingPeriod: {
    type: "one-time"
  },
  button: {
    label: "Get access",
    url: "#",
    urlType: "internal",
    style: "primary",
    size: "medium",
    icon: { enabled: true, name: "arrow-right", position: "right" }
  },
  footnote: "Invoices and receipts available for easy company reimbursement"
};

export default function PricingSingleCard({ 
  id = 'pricing-single-card',
  tagline = 'Pricing', 
  title = 'Simple no-tricks pricing', 
  description = 'Distinctio et nulla eum soluta et neque labore quibusdam. Saepe et quasi iusto modi velit ut non voluptas in. Explicabo id ut laborum.', 
  tier
}: PricingSingleSectionProps) {
  // 使用提供的 tier 或默认值
  const pricingTier = tier || defaultTier;

  // ButtonAdapter 会自动处理图标渲染和样式

  return (
    <section id={id} className="relative bg-background py-section-y" aria-labelledby="pricing-card-title">
      {/* 简化背景装饰 */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/3 via-transparent to-accent/3 pointer-events-none" />
      
      <div className="relative mx-auto max-w-container px-container-x">
        <div className="mx-auto max-w-content text-center">
          {tagline && (
            <div className="inline-flex items-center gap-2 rounded-full bg-primary/10 px-4 py-2 text-body-small/body-small font-medium text-primary ring-1 ring-inset ring-primary/20">
              <SparklesIcon className="h-4 w-4" aria-hidden="true" />
              {tagline}
            </div>
          )}
          <h2 id="pricing-card-title" className="text-pretty mt-6 text-heading-2/heading-2 font-bold tracking-tight text-foreground sm:text-balance">
            {title}
          </h2>
          <p className="mx-auto mt-6 max-w-2xl text-pretty text-body-large/body-large text-muted-foreground leading-relaxed">
            {description}
          </p>
        </div>
        
        <div className="mx-auto mt-12 sm:mt-16 max-w-5xl">
          <div className="overflow-hidden rounded-3xl bg-card shadow-2xl ring-1 ring-border hover:shadow-3xl transition-shadow duration-300">
            <div className="grid grid-cols-1 lg:grid-cols-5">
              {/* 左侧内容区域 */}
              <div className="lg:col-span-3 p-8 sm:p-10 lg:p-12">
                <div className="space-y-8">
                  <div>
                    <h3 id="pricing-tier-name" className="text-heading-3/heading-3 font-bold tracking-tight text-foreground">
                      {pricingTier.name}
                    </h3>
                    <p className="mt-4 text-body-base/body-base text-muted-foreground leading-relaxed">
                      {pricingTier.description}
                    </p>
                  </div>
                  
                  <div className="space-y-6">
                    <div className="flex items-center gap-3">
                      <StarIcon className="h-5 w-5 text-primary" aria-hidden="true" />
                      <h4 id="features-list-title" className="text-body-large/body-large font-semibold text-primary">
                        {pricingTier.featuresTitle}
                      </h4>
                      <div className="h-px flex-1 bg-border" />
                    </div>
                    
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {pricingTier.features.map((feature) => (
                        <div 
                          key={feature} 
                          className="flex items-start gap-3 p-3 rounded-xl hover:bg-accent/20 transition-colors duration-200"
                        >
                          <div className="flex-shrink-0 mt-0.5">
                            <CheckIcon className="h-5 w-5 text-primary" aria-hidden="true" />
                          </div>
                          <span className="text-body-base/body-base text-muted-foreground font-medium">
                            {feature}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
              
              {/* 右侧价格区域 */}
              <div className="lg:col-span-2 bg-primary text-primary-foreground p-6 sm:p-8 lg:p-10 xl:p-12 flex flex-col justify-center">
                <div className="text-center space-y-6 lg:space-y-8">
                  <div>
                    <p className="text-body-large/body-large font-semibold text-primary-foreground mb-4 lg:mb-6">
                      {pricingTier.subtitle}
                    </p>
                    
                    <PriceDisplay 
                      price={pricingTier.price} 
                      originalPrice={pricingTier.originalPrice}
                      currency={pricingTier.currency} 
                      billingPeriod={pricingTier.billingPeriod}
                      className="text-primary-foreground" 
                    />
                  </div>
                  
                  <div className="space-y-4 lg:space-y-6">
                    <ButtonAdapter
                      button={pricingTier.button}
                      className="w-full bg-white text-primary border-2 border-white hover:bg-gray-50 hover:text-primary hover:border-gray-100 shadow-lg hover:shadow-xl transition-all duration-300 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white font-semibold rounded-xl px-6 py-4 text-body-base/body-base"
                      accessibility={{
                        description: `Subscribe to ${pricingTier.name}`,
                        isPrimary: true
                      }}
                      ariaProps={{
                        'aria-describedby': "pricing-tier-name"
                      }}
                    />
                    
                    {pricingTier.footnote && (
                      <p className="text-body-small/body-small text-primary-foreground/90 leading-relaxed px-2">
                        {pricingTier.footnote}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
