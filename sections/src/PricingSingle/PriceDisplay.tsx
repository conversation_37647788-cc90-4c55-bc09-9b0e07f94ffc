import React from 'react';
import { BillingPeriod } from './types';

// 货币映射表，用于显示货币符号
const currencyMap = {
  USD: { symbol: "$", code: "USD" },
  EUR: { symbol: "€", code: "EUR" },
  GBP: { symbol: "£", code: "GBP" },
  JPY: { symbol: "¥", code: "JPY" },
  CNY: { symbol: "¥", code: "CNY" },
  CAD: { symbol: "C$", code: "CAD" },
  AUD: { symbol: "A$", code: "AUD" },
  NONE: { symbol: "", code: "NONE" }
};

// 计费周期映射表，用于显示计费周期文本
const billingPeriodMap = {
  'one-time': '',
  'monthly': '/month',
  'yearly': '/year',
  'quarterly': '/quarter',
  'weekly': '/week',
  'daily': '/day',
  'per-user': '/user',
  'custom': ''
};

interface PriceDisplayProps {
  price: number;
  originalPrice?: number;  // 原价（可选）
  currency: string;
  billingPeriod: BillingPeriod;
  className?: string;
}

/**
 * PriceDisplay 组件用于显示价格、币种和计费周期
 * 支持不同币种和计费周期的组合，以及原价折扣显示
 */
export const PriceDisplay: React.FC<PriceDisplayProps> = ({ 
  price, 
  originalPrice,
  currency = 'USD', 
  billingPeriod, 
  className = ''
}) => {
  // 获取货币符号
  const currencySymbol = currencyMap[currency as keyof typeof currencyMap]?.symbol || '$';
  
  // 格式化价格 - 支持小数显示
  const formattedPrice = price.toLocaleString('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2  // 支持最多2位小数
  });
  
  const formattedOriginalPrice = originalPrice?.toLocaleString('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2  // 支持最多2位小数
  });
  
  // 获取计费周期文本
  let periodText = billingPeriod.type ? billingPeriodMap[billingPeriod.type as keyof typeof billingPeriodMap] : '';
  
  // 如果是自定义周期，使用自定义文本
  if (billingPeriod.type === 'custom' && billingPeriod.customText) {
    periodText = billingPeriod.customText;
  }
  
  // 计算折扣百分比
  const discountPercentage = originalPrice && originalPrice > price 
    ? Math.round(((originalPrice - price) / originalPrice) * 100)
    : null;
  
  // 确定价格的整数部分和小数部分
  const [integerPart, decimalPart] = formattedPrice.split('.');
  
  return (
    <div className={`text-center ${className}`}>
      {/* 折扣百分比显示在价格上方 */}
      {discountPercentage && (
        <div className="mb-3">
          <span className="inline-flex items-center rounded-full bg-green-100 px-3 py-1 text-sm font-semibold text-green-800">
            Save {discountPercentage}%
          </span>
        </div>
      )}
      
      {/* 价格行：当前价格 + 原价 + 周期 */}
      <div className="flex items-baseline justify-center gap-x-2">
        {/* 当前价格 */}
        <span className="text-heading-2/heading-2 font-bold tracking-tight flex items-baseline">
          <span className="mr-0.5">{currencySymbol}</span>
          <span>{integerPart}</span>
          {decimalPart && (
            <span className="text-heading-2/heading-2 font-normal">.{decimalPart}</span>
          )}
        </span>
        
        {/* 原价（如果有的话），放在当前价格后面 */}
        {originalPrice && originalPrice > price && (
          <span className="text-heading-3/heading-3 text-muted-foreground line-through leading-none">
            {formattedOriginalPrice}
          </span>
        )}
        
        {/* 计费周期 */}
        {periodText && (
          <span className="text-body-large/body-large font-medium text-muted-foreground">
            {periodText}
          </span>
        )}
      </div>
    </div>
  );
};
