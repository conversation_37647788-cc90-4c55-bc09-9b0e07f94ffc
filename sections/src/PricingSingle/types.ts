// URL related types
export type UrlType = 'internal' | 'external' | 'anchor' | 'email' | 'phone';

// Currency related types
export type CurrencyCode = 'USD' | 'EUR' | 'GBP' | 'JPY' | 'CNY' | 'CAD' | 'AUD' | 'NONE';

export interface CurrencyInfo {
  symbol: string;
  code: CurrencyCode;
}

export const currencyMap: Record<CurrencyCode, CurrencyInfo> = {
  USD: { symbol: "$", code: "USD" },
  EUR: { symbol: "€", code: "EUR" },
  GBP: { symbol: "£", code: "GBP" },
  JPY: { symbol: "¥", code: "JPY" },
  CNY: { symbol: "¥", code: "CNY" },
  CAD: { symbol: "C$", code: "CAD" },
  AUD: { symbol: "A$", code: "AUD" },
  NONE: { symbol: "", code: "NONE" }
};

// Billing period related types
export type BillingPeriodType = 'one-time' | 'monthly' | 'yearly' | 'per-user' | 'custom';

export interface BillingPeriod {
  type: BillingPeriodType;
  customText?: string;
}

// 导入统一的按钮类型定义 - 与 ButtonAdapter 完全兼容
import type { 
  Button as UniversalButton,
  ButtonStyle,
  ButtonSize,
  ButtonIconPosition,
  ButtonIconName,
  ButtonIcon
} from '../components/types';

// 重新导出类型以保持向后兼容性
export type { ButtonStyle, ButtonSize, ButtonIconPosition, ButtonIconName, ButtonIcon };

// 使用统一的按钮接口
export interface Button extends UniversalButton {}

// Tier related types
export interface Tier {
  name: string;
  description: string;
  featuresTitle: string;
  features: string[];
  subtitle: string;
  price: number;
  originalPrice?: number;  // 原价（可选），用于显示折扣
  currency: CurrencyCode;
  billingPeriod: BillingPeriod;
  button: Button;
  footnote?: string;
}

// Component props
export interface PricingSingleSectionProps {
  id: string;
  tagline?: string;
  title: string;
  description: string;
  tier: Tier;
}