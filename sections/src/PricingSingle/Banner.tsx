import React from 'react';
import { CheckIcon } from '@heroicons/react/20/solid';
import { PricingSingleSectionProps, Tier } from './types';
import { PriceDisplay } from './PriceDisplay';
import { ButtonAdapter } from '../components/ButtonAdapter';

// 默认价格层级，用于在未提供 tier 时显示
const defaultTier: Tier = {
  name: "Lifetime membership",
  description: "Lorem ipsum dolor sit amet consect etur adipisicing elit. Itaque amet indis perferendis blanditiis repellendus etur quidem assumenda.",
  featuresTitle: "What's included",
  features: [
    "Private forum access",
    "Member resources",
    "Entry to annual conference",
    "Official member t-shirt"
  ],
  subtitle: "Pay once, own it forever",
  price: 349.5,
  originalPrice: 499.99,
  currency: "USD",
  billingPeriod: {
    type: "one-time"
  },
  button: {
    label: "Get access",
    url: "#",
    urlType: "internal",
    style: "primary",
    size: "medium",
    icon: { enabled: false }
  },
  footnote: "Invoices and receipts available for easy company reimbursement"
};

export default function PricingSingleBanner({ 
  id = 'pricing-single-banner',
  tagline = 'Pricing', 
  title = 'Simple no-tricks pricing', 
  description = 'Distinctio et nulla eum soluta et neque labore quibusdam. Saepe et quasi iusto modi velit ut non voluptas in. Explicabo id ut laborum.', 
  tier
}: PricingSingleSectionProps) {
  // 使用提供的 tier 或默认值
  const pricingTier = tier || defaultTier;

  return (
    <section id={id} className="bg-background py-section-y" aria-labelledby="pricing-banner-title">
      <div className="mx-auto max-w-container px-container-x">
        <div className="mx-auto max-w-content text-center">
          {tagline && (
            <p className="text-body-base/body-base font-semibold text-primary">{tagline}</p>
          )}
          <h2 id="pricing-banner-title" className="text-pretty mt-element-y text-heading-2/heading-2 font-bold tracking-tight text-foreground sm:text-balance">
            {title}
          </h2>
          <p className="mx-auto mt-element-y text-pretty text-body-large/body-large text-muted-foreground">
            {description}
          </p>
        </div>
        
        <div className="mx-auto mt-content-y sm:mt-section-y">
          {/* 横幅样式的卡片 */}
          <div className="overflow-hidden rounded-3xl bg-gradient-to-r from-primary to-primary/90 shadow-2xl">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-content-y lg:gap-0">
              {/* 左侧内容 */}
              <div className="col-span-1 lg:col-span-2 p-element-x py-element-y sm:p-content-y text-primary-foreground">
                <h3 id="pricing-tier-name" className="text-heading-3/heading-3 font-bold tracking-tight">{pricingTier.name}</h3>
                <p className="mt-element-y text-body-base/body-base text-primary-foreground/90">
                  {pricingTier.description}
                </p>
                
                <div className="mt-content-y">
                  <h4 id="features-list-title" className="text-body-large/body-large font-semibold text-primary-foreground mb-element-y">
                    {pricingTier.featuresTitle}
                  </h4>
                  <ul role="list" aria-labelledby="features-list-title" className="grid grid-cols-1 gap-element-y sm:grid-cols-2 sm:gap-x-element-x">
                    {pricingTier.features.map((feature) => (
                      <li key={feature} className="flex items-start">
                        <CheckIcon className="h-5 w-5 flex-shrink-0 text-primary-foreground" aria-hidden="true" />
                        <span className="ml-element-x text-body-base/body-base text-primary-foreground/90">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
              
              {/* 右侧价格和按钮 */}
              <div className="col-span-1 bg-primary-foreground p-element-x py-element-y sm:p-content-y flex flex-col justify-center">
                <div className="text-center">
                  <p className="text-body-large/body-large font-medium text-primary mb-element-y">{pricingTier.subtitle}</p>
                  
                  <div className="mt-element-y">
                    <PriceDisplay 
                      price={pricingTier.price} 
                      originalPrice={pricingTier.originalPrice}
                      currency={pricingTier.currency} 
                      billingPeriod={pricingTier.billingPeriod}
                      className="text-primary" 
                    />
                  </div>
                  
                  <div className="mt-content-y">
                    <ButtonAdapter
                      button={pricingTier.button}
                      className="block w-full rounded-md px-element-x py-element-y text-center text-body-base/body-base font-semibold shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
                      accessibility={{
                        description: `Subscribe to ${pricingTier.name}`,
                        isPrimary: true
                      }}
                      ariaProps={{
                        'aria-describedby': "pricing-tier-name"
                      }}
                    />
                  </div>
                  
                  {pricingTier.footnote && (
                    <p className="mt-element-y text-body-small/body-small text-primary/80 text-center">
                      {pricingTier.footnote}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
