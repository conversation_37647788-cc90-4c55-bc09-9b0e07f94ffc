import type { 
  PricingSingleSectionProps, 
  Tier, 
  Button, 
  BillingPeriod, 
  CurrencyCode 
} from './types';

// Default button - 与 ButtonAdapter 完全兼容
export const defaultButton: Button = {
  label: "Get access",
  url: "/",
  urlType: "internal",
  style: "primary",    // 必需字段
  size: "medium",      // 必需字段
  icon: { 
    enabled: false     // 默认不显示图标
  }
};

// Default billing period
export const defaultBillingPeriod: BillingPeriod = {
  type: "one-time"
};

// Default tier
export const defaultTier: Tier = {
  name: "Lifetime membership",
  description: "Lorem ipsum dolor sit amet consect etur adipisicing elit. Itaque amet indis perferendis blanditiis repellendus etur quidem assumenda.",
  featuresTitle: "What's included",
  features: [
    "Private forum access",
    "Member resources",
    "Entry to annual conference",
    "Official member t-shirt"
  ],
  subtitle: "Pay once, own it forever",
  price: 349.5,  // 包含小数的价格示例
  originalPrice: 499.99,  // 包含小数的原价示例
  currency: "USD" as CurrencyCode,
  billingPeriod: defaultBillingPeriod,
  button: defaultButton,
  footnote: "Invoices and receipts available for easy company reimbursement"
};

// PricingSingle component default props
export const defaultPricingSingleProps: PricingSingleSectionProps = {
  id: "pricing",
  tagline: "Pricing",
  title: "Simple no-tricks pricing",
  description: "Distinctio et nulla eum soluta et neque labore quibusdam. Saepe et quasi iusto modi velit ut non voluptas in. Explicabo id ut laborum.",
  tier: defaultTier
};
