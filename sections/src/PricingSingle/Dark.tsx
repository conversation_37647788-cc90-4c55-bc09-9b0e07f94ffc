import React from 'react';
import { CheckIcon } from '@heroicons/react/20/solid';
import { PricingSingleSectionProps, Tier } from './types';
import { PriceDisplay } from './PriceDisplay';
import { ButtonAdapter } from '../components/ButtonAdapter';

// 默认价格层级，用于在未提供 tier 时显示
const defaultTier: Tier = {
  name: "Lifetime membership",
  description: "Lorem ipsum dolor sit amet consect etur adipisicing elit. Itaque amet indis perferendis blanditiis repellendus etur quidem assumenda.",
  featuresTitle: "What's included",
  features: [
    "Private forum access",
    "Member resources",
    "Entry to annual conference",
    "Official member t-shirt"
  ],
  subtitle: "Pay once, own it forever",
  price: 349.5,
  originalPrice: 499.99,
  currency: "USD",
  billingPeriod: {
    type: "one-time"
  },
  button: {
    label: "Get access",
    url: "#",
    urlType: "internal",
    style: "primary",
    size: "medium",
    icon: { enabled: false }
  },
  footnote: "Invoices and receipts available for easy company reimbursement"
};

export default function PricingSingleDark({ 
  id = 'pricing-single-dark',
  tagline = 'Pricing', 
  title = 'Simple no-tricks pricing', 
  description = 'Distinctio et nulla eum soluta et neque labore quibusdam. Saepe et quasi iusto modi velit ut non voluptas in. Explicabo id ut laborum.', 
  tier
}: PricingSingleSectionProps) {
  // 使用提供的 tier 或默认值
  const pricingTier = tier || defaultTier;

  return (
    <section id={id} className="bg-muted py-section-y" aria-labelledby="pricing-dark-title">
      <div className="mx-auto max-w-container px-container-x">
        <div className="mx-auto max-w-content text-center">
          {tagline && (
            <p className="text-body-base/body-base font-semibold text-primary">{tagline}</p>
          )}
          <h2 id="pricing-dark-title" className="text-pretty mt-element-y text-heading-2/heading-2 font-bold tracking-tight text-foreground sm:text-balance">
            {title}
          </h2>
          <p className="mx-auto mt-element-y text-pretty text-body-large/body-large text-muted-foreground">
            {description}
          </p>
        </div>
        
        <div className="mx-auto mt-content-y rounded-3xl ring-1 ring-border shadow-lg sm:mt-section-y">
          <div className="grid grid-cols-1 lg:grid-cols-2">
            {/* 左侧内容 */}
            <div className="p-element-x py-element-y sm:p-content-y">
              <h3 id="pricing-tier-name" className="text-heading-3/heading-3 font-bold tracking-tight text-foreground">{pricingTier.name}</h3>
              <p className="mt-element-y text-body-base/body-base text-muted-foreground">
                {pricingTier.description}
              </p>
              <div className="mt-content-y flex items-center gap-x-element-x">
                <h4 id="features-list-title" className="flex-none text-body-small/body-small font-semibold text-primary">{pricingTier.featuresTitle}</h4>
                <div className="h-px flex-auto bg-border" />
              </div>
              <ul role="list" aria-labelledby="features-list-title" className="mt-element-y grid grid-cols-1 gap-element-y text-body-small/body-small text-muted-foreground sm:grid-cols-2 sm:gap-x-element-x">
                {pricingTier.features.map((feature) => (
                  <li key={feature} className="flex gap-x-element-x transition-colors duration-200 hover:bg-accent/30 p-1 rounded-md cursor-default">
                    <CheckIcon className="h-6 w-5 flex-none text-primary" aria-hidden="true" />
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
            
            {/* 右侧价格 */}
            <div className="border-t border-border p-element-x py-element-y sm:p-content-y lg:border-l lg:border-t-0 bg-card transition-colors rounded-br-3xl rounded-tr-none lg:rounded-tr-3xl lg:rounded-bl-none">
              <div className="mx-auto">
                <p className="text-body-base/body-base font-semibold text-foreground">{pricingTier.subtitle}</p>
                
                {/* 使用价格组件显示价格、币种和计费周期 */}
                <div className="mt-element-y">
                  <PriceDisplay 
                    price={pricingTier.price} 
                    originalPrice={pricingTier.originalPrice}
                    currency={pricingTier.currency} 
                    billingPeriod={pricingTier.billingPeriod}
                  />
                </div>
                
                {/* 按钮 */}
                <div className="mt-content-y">
                  <ButtonAdapter
                    button={pricingTier.button}
                    className="block w-full rounded-md px-element-x py-element-y text-center text-body-base/body-base font-semibold shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
                    accessibility={{
                      description: `Subscribe to ${pricingTier.name}`,
                      isPrimary: true
                    }}
                    ariaProps={{
                      'aria-describedby': "pricing-tier-name"
                    }}
                  />
                </div>
                
                {/* 脚注 */}
                {pricingTier.footnote && (
                  <p className="mt-element-y text-body-small/body-small text-muted-foreground">
                    {pricingTier.footnote}
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
