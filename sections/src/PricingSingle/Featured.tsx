import React from 'react';
import { CheckIcon, SparklesIcon, StarIcon } from '@heroicons/react/20/solid';
import { PricingSingleSectionProps, Tier } from './types';
import { PriceDisplay } from './PriceDisplay';
import { ButtonAdapter } from '../components/ButtonAdapter';

// 默认价格层级，用于在未提供 tier 时显示
const defaultTier: Tier = {
  name: "Lifetime membership",
  description: "Lorem ipsum dolor sit amet consect etur adipisicing elit. Itaque amet indis perferendis blanditiis repellendus etur quidem assumenda.",
  featuresTitle: "What's included",
  features: [
    "Private forum access",
    "Member resources",
    "Entry to annual conference",
    "Official member t-shirt"
  ],
  subtitle: "Pay once, own it forever",
  price: 349.5,
  originalPrice: 499.99,
  currency: "USD",
  billingPeriod: {
    type: "one-time"
  },
  button: {
    label: "Get access",
    url: "#",
    urlType: "internal",
    style: "primary",
    size: "medium",
    icon: { enabled: true, name: "arrow-right", position: "right" }
  },
  footnote: "Invoices and receipts available for easy company reimbursement"
};

export default function PricingSingleFeatured({ 
  id = 'pricing-single-featured',
  tagline = 'Pricing', 
  title = 'Simple no-tricks pricing', 
  description = 'Distinctio et nulla eum soluta et neque labore quibusdam. Saepe et quasi iusto modi velit ut non voluptas in. Explicabo id ut laborum.', 
  tier
}: PricingSingleSectionProps) {
  // 使用提供的 tier 或默认值
  const pricingTier = tier || defaultTier;

  return (
    <section id={id} className="relative bg-background py-section-y" aria-labelledby="pricing-featured-title">
      {/* 简化背景装饰 */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-accent/3 to-primary/3 pointer-events-none" />
      
      <div className="relative mx-auto max-w-container px-container-x">
        <div className="mx-auto max-w-content text-center">
          {tagline && (
            <div className="inline-flex items-center gap-2 rounded-full bg-gradient-to-r from-primary/10 to-accent/10 px-4 py-2 text-body-small/body-small font-medium text-primary ring-1 ring-inset ring-primary/20">
              <SparklesIcon className="h-4 w-4" aria-hidden="true" />
              {tagline}
            </div>
          )}
          <h2 id="pricing-featured-title" className="text-pretty mt-6 text-heading-2/heading-2 font-bold tracking-tight text-foreground sm:text-balance">
            {title}
          </h2>
          <p className="mx-auto mt-6 max-w-2xl text-pretty text-body-large/body-large text-muted-foreground leading-relaxed">
            {description}
          </p>
        </div>
        
        <div className="mx-auto mt-12 sm:mt-16 max-w-2xl">
          <div className="relative">
            {/* 主卡片 */}
            <div className="rounded-3xl bg-card shadow-2xl ring-1 ring-border hover:shadow-3xl hover:ring-primary/30 transition-all duration-300">
              {/* 内容区域 */}
              <div className="p-6 sm:p-8 lg:p-12">
                <div className="text-center space-y-8">
                  {/* 标题区域 */}
                  <div className="space-y-3 sm:space-y-4">
                    <h3 id="pricing-tier-name" className="text-heading-3/heading-3 font-bold tracking-tight text-foreground px-2">
                      {pricingTier.name}
                    </h3>
                    <p className="text-body-base/body-base text-muted-foreground leading-relaxed max-w-lg mx-auto px-2">
                      {pricingTier.description}
                    </p>
                  </div>
                  
                  {/* 价格展示区域 */}
                  <div className="py-8 bg-gradient-to-r from-transparent via-primary/5 to-transparent rounded-2xl">
                    <p className="text-body-large/body-large font-semibold text-foreground mb-6">
                      {pricingTier.subtitle}
                    </p>
                    <PriceDisplay 
                      price={pricingTier.price} 
                      originalPrice={pricingTier.originalPrice}
                      currency={pricingTier.currency} 
                      billingPeriod={pricingTier.billingPeriod}
                    />
                  </div>
                  
                  {/* 功能列表 */}
                  <div className="space-y-6">
                    <div className="flex items-center justify-center gap-4">
                      <div className="h-px flex-1 bg-border" />
                      <div className="flex items-center gap-2">
                        <StarIcon className="h-5 w-5 text-primary" aria-hidden="true" />
                        <h4 id="features-list-title" className="text-body-large/body-large font-semibold text-primary">
                          {pricingTier.featuresTitle}
                        </h4>
                        <StarIcon className="h-5 w-5 text-primary" aria-hidden="true" />
                      </div>
                      <div className="h-px flex-1 bg-border" />
                    </div>
                    
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 max-w-2xl mx-auto">
                      {pricingTier.features.map((feature) => (
                        <div 
                          key={feature} 
                          className="flex items-start gap-3 p-3 sm:p-4 rounded-xl bg-accent/20 border border-border hover:bg-accent/30 hover:border-primary/30 transition-colors duration-200"
                        >
                          <CheckIcon className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" aria-hidden="true" />
                          <span className="text-body-base/body-base text-muted-foreground font-medium leading-snug text-left">
                            {feature}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  {/* 按钮区域 */}
                  <div className="pt-6 sm:pt-8 px-2 sm:px-0">
                    <ButtonAdapter
                      button={pricingTier.button}
                      className="w-full sm:w-auto sm:min-w-[280px] rounded-xl px-6 sm:px-8 py-3 sm:py-4 text-center text-body-large/body-large font-bold shadow-xl hover:shadow-2xl transition-shadow duration-300 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
                      accessibility={{
                        description: `Subscribe to ${pricingTier.name}`,
                        isPrimary: true
                      }}
                      ariaProps={{
                        'aria-describedby': "pricing-tier-name"
                      }}
                    />
                  </div>
                  
                  {/* 脚注 */}
                  {pricingTier.footnote && (
                    <div className="pt-4 sm:pt-6 border-t border-border">
                      <p className="text-body-small/body-small text-muted-foreground leading-relaxed max-w-md mx-auto px-2">
                        {pricingTier.footnote}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
