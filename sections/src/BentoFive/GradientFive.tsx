'use client'

import React from 'react';
import { motion } from 'motion/react';
import { Picture } from '../components/Picture';
import { BentoFiveProps } from './types';

export default function GradientFive({ id, tagline, title, items }: BentoFiveProps) {
  // 确保items数组至少有5个元素
  const safeItems = items && items.length >= 5 ? items : Array(5).fill({
    title: 'Feature',
    description: 'Description text here',
    img: 'https://images.unsplash.com/photo-1624571409412-1f253e1ecc89?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&q=80',
    alt: 'Feature image'
  });

  // 为每个位置分配特定的图片尺寸
  const imgSizes = [
    'w=800&h=720',  // 第一个位置 - 左上大卡片
    'w=600&h=400',  // 第二个位置 - 右上小卡片
    'w=600&h=400',  // 第三个位置 - 左下小卡片
    'w=600&h=400',  // 第四个位置 - 中下小卡片
    'w=600&h=400',  // 第五个位置 - 右下小卡片
  ];

  // 渐变背景色数组
  const gradients = [
    'from-purple-500 to-indigo-600',
    'from-pink-500 to-rose-500',
    'from-amber-400 to-orange-500',
    'from-emerald-500 to-teal-600',
    'from-blue-500 to-cyan-500',
  ];

  return (
    <section id={id} className="bg-gradient-to-br from-background dark:from-gray-900 to-muted dark:to-gray-800 py-section-y" aria-labelledby="gradient-five-title">
      <div className="mx-auto max-w-container px-container-x">
        <div className="mx-auto max-w-content text-center mb-content-y">
          {tagline && (
            <motion.p 
              className="text-body-small/body-small font-semibold tracking-wide text-primary dark:text-cyan-400 uppercase"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              {tagline}
            </motion.p>
          )}
          {title && (
            <motion.h2 
              id="gradient-five-title"
              className="mt-element-y text-heading-2/heading-2 font-bold tracking-tight text-foreground dark:text-white"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              {title}
            </motion.h2>
          )}
        </div>

        <div className="mx-auto max-w-container">
          {/* 渐变网格布局 - 12列6行布局 */}
          <div className="grid grid-cols-1 md:grid-cols-12 md:grid-rows-6 gap-element-y">
            {/* 第一个区块 - 左上大卡片，占据8列3行 */}
            <motion.div 
              className="md:col-span-8 md:row-span-3 group"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <div className={`h-full overflow-hidden rounded-3xl bg-gradient-to-br ${gradients[0]} p-1 shadow-xl`}>
                <div className="h-full rounded-[22px] bg-card/20 dark:bg-black/20 backdrop-blur-sm p-6 flex flex-col md:flex-row items-center">
                  <div className="md:w-1/2 mb-6 md:mb-0 md:mr-6" aria-labelledby="gradient-five-card-0-title">
                    {safeItems[0].tagline && (
                      <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-white/20 text-white dark:text-white mb-4">
                        {safeItems[0].tagline}
                      </div>
                    )}
                    
                    <h3 id="gradient-five-card-0-title" className="text-heading-3/heading-3 font-bold text-white dark:text-white mb-element-y">
                      {safeItems[0].title}
                    </h3>
                    
                    <p className="text-white/90 dark:text-white/80 text-body-base/body-base leading-relaxed" aria-labelledby="gradient-five-card-0-title">
                      {safeItems[0].description}
                    </p>
                  </div>
                  
                  <div className="md:w-1/2">
                    <div className="relative w-full h-72 md:h-full overflow-hidden rounded-2xl">
                      <div className="absolute inset-0 bg-gradient-to-tr from-foreground/20 dark:from-black/20 to-transparent z-10" />
                      <Picture
                        src={safeItems[0].img}
                        alt={safeItems[0].alt || "Feature image"}
                        widths={[375, 480, 640, 768, 1024]}
                        sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 600px"
                        quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                        forceFillHeight={true}
                        className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                        aria-describedby="gradient-five-card-0-title"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
            
            {/* 第二个区块 - 右上小卡片，占据4列3行 */}
            <motion.div 
              className="md:col-span-4 md:col-start-9 md:row-span-3 group"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              <div className={`h-full overflow-hidden rounded-3xl bg-gradient-to-br ${gradients[1]} p-1 shadow-xl`}>
                <div className="h-full rounded-[22px] bg-card/20 dark:bg-black/20 backdrop-blur-sm p-6 flex flex-col">
                  <div className="mb-5">
                    <div className="relative w-full h-48 overflow-hidden rounded-2xl">
                      <div className="absolute inset-0 bg-gradient-to-tr from-foreground/20 dark:from-black/20 to-transparent z-10" />
                      <Picture
                        src={safeItems[1].img}
                        alt={safeItems[1].alt || "Feature image"}
                        widths={[375, 480, 640, 768]}
                        sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 400px"
                        quality={{ avif: 60, webp: 65, jpeg: 70, png: 75 }}
                        forceFillHeight={true}
                        className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                        aria-describedby="gradient-five-card-1-title"
                      />
                    </div>
                  </div>
                  
                  {safeItems[1].tagline && (
                    <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-white/20 text-white dark:text-white mb-3">
                      {safeItems[1].tagline}
                    </div>
                  )}
                  
                  <h3 id="gradient-five-card-1-title" className="text-heading-4/heading-4 font-bold text-white dark:text-white mb-element-y">
                    {safeItems[1].title}
                  </h3>
                  
                  <p className="text-white/80 text-body-small/body-small leading-relaxed" aria-labelledby="gradient-five-card-1-title">
                    {safeItems[1].description}
                  </p>
                </div>
              </div>
            </motion.div>
            
            {/* 第三个区块 - 左下小卡片，占据4列3行 */}
            <motion.div 
              className="md:col-span-4 md:row-start-4 md:row-span-3 group"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <div className={`h-full overflow-hidden rounded-3xl bg-gradient-to-br ${gradients[2]} p-1 shadow-xl`}>
                <div className="h-full rounded-[22px] bg-card/20 dark:bg-black/20 backdrop-blur-sm p-6 flex flex-col">
                  {safeItems[2].tagline && (
                    <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-white/20 text-white dark:text-white mb-3">
                      {safeItems[2].tagline}
                    </div>
                  )}
                  
                  <h3 id="gradient-five-card-2-title" className="text-heading-4/heading-4 font-bold text-white dark:text-white mb-element-y">
                    {safeItems[2].title}
                  </h3>
                  
                  <p className="text-white/80 text-body-small/body-small leading-relaxed mb-element-y" aria-labelledby="gradient-five-card-2-title">
                    {safeItems[2].description}
                  </p>
                  
                  <div className="mt-auto">
                    <div className="relative w-full h-48 overflow-hidden rounded-2xl">
                      <div className="absolute inset-0 bg-gradient-to-tr from-foreground/20 dark:from-black/20 to-transparent z-10" />
                      <Picture
                        src={safeItems[2].img}
                        alt={safeItems[2].alt || "Feature image"}
                        widths={[375, 480, 640, 768]}
                        sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 400px"
                        quality={{ avif: 60, webp: 65, jpeg: 70, png: 75 }}
                        forceFillHeight={true}
                        className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                        aria-describedby="gradient-five-card-2-title"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
            
            {/* 第四个区块 - 中下小卡片，占据4列3行 */}
            <motion.div 
              className="md:col-span-4 md:col-start-5 md:row-start-4 md:row-span-3 group"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <div className={`h-full overflow-hidden rounded-3xl bg-gradient-to-br ${gradients[3]} p-1 shadow-xl`}>
                <div className="h-full rounded-[22px] bg-card/20 dark:bg-black/20 backdrop-blur-sm p-6 flex flex-col">
                  <div className="mb-5">
                    <div className="relative w-full h-48 overflow-hidden rounded-2xl">
                      <div className="absolute inset-0 bg-gradient-to-tr from-foreground/20 dark:from-black/20 to-transparent z-10" />
                      <Picture
                        src={safeItems[3].img}
                        alt={safeItems[3].alt || "Feature image"}
                        widths={[375, 480, 640, 768]}
                        sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 400px"
                        quality={{ avif: 60, webp: 65, jpeg: 70, png: 75 }}
                        forceFillHeight={true}
                        className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                        aria-describedby="gradient-five-card-3-title"
                      />
                    </div>
                  </div>
                  
                  {safeItems[3].tagline && (
                    <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-white/20 text-white dark:text-white mb-3">
                      {safeItems[3].tagline}
                    </div>
                  )}
                  
                  <h3 id="gradient-five-card-3-title" className="text-xl font-bold text-white dark:text-white mb-3">
                    {safeItems[3].title}
                  </h3>
                  
                  <p className="text-white/80 leading-relaxed" aria-labelledby="gradient-five-card-3-title">
                    {safeItems[3].description}
                  </p>
                </div>
              </div>
            </motion.div>
            
            {/* 第五个区块 - 右下小卡片，占据4列3行 */}
            <motion.div 
              className="md:col-span-4 md:col-start-9 md:row-start-4 md:row-span-3 group"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <div className={`h-full overflow-hidden rounded-3xl bg-gradient-to-br ${gradients[4]} p-1 shadow-xl`}>
                <div className="h-full rounded-[22px] bg-card/20 dark:bg-black/20 backdrop-blur-sm p-6 flex flex-col">
                  {safeItems[4].tagline && (
                    <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-white/20 text-white dark:text-white mb-3">
                      {safeItems[4].tagline}
                    </div>
                  )}
                  
                  <h3 id="gradient-five-card-4-title" className="text-xl font-bold text-white dark:text-white mb-3">
                    {safeItems[4].title}
                  </h3>
                  
                  <p className="text-white/80 leading-relaxed mb-5" aria-labelledby="gradient-five-card-4-title">
                    {safeItems[4].description}
                  </p>
                  
                  <div className="mt-auto">
                    <div className="relative w-full h-48 overflow-hidden rounded-2xl">
                      <div className="absolute inset-0 bg-gradient-to-tr from-foreground/20 dark:from-black/20 to-transparent z-10" />
                      <Picture
                        src={safeItems[4].img}
                        alt={safeItems[4].alt || "Feature image"}
                        widths={[375, 480, 640, 768]}
                        sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 400px"
                        quality={{ avif: 60, webp: 65, jpeg: 70, png: 75 }}
                        forceFillHeight={true}
                        className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                        aria-describedby="gradient-five-card-4-title"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
}
