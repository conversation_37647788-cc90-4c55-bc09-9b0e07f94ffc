'use client'

import React from 'react';
import { motion } from 'motion/react';
import { Picture } from '../components/Picture';
import { BentoFiveProps } from './types';

export default function MinimalFive({ id, tagline, title, items }: BentoFiveProps) {
  // 确保items数组至少有5个元素
  const safeItems = items && items.length >= 5 ? items : Array(5).fill({
    title: 'Feature',
    description: 'Description text here',
    img: 'https://images.unsplash.com/photo-1624571409412-1f253e1ecc89?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&q=80',
    alt: 'Feature image'
  });

  // 为每个位置分配特定的图片尺寸
  const imgSizes = [
    'w=450&h=400',  // 第一个位置 - 左下小卡片 (原第四个位置)
    'w=600&h=450',  // 第二个位置 - 中间小卡片 (原第三个位置)
    'w=450&h=400',  // 第三个位置 - 右下小卡片 (原第五个位置)
    'w=900&h=600',  // 第四个位置 - 左上大卡片 (原第一个位置)
    'w=700&h=600',  // 第五个位置 - 右上大卡片 (原第二个位置)
  ];

  return (
    <section id={id} className="bg-muted dark:bg-gray-900 py-section-y" aria-labelledby="minimal-five-title">
      <div className="mx-auto max-w-container px-container-x">
        <div className="mx-auto max-w-content text-center mb-content-y">
          {tagline && (
            <motion.p 
              className="text-body-small/body-small font-semibold tracking-wide text-primary dark:text-primary uppercase"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              {tagline}
            </motion.p>
          )}
          {title && (
            <motion.h2 
              id="minimal-five-title"
              className="mt-element-y text-heading-2/heading-2 font-bold tracking-tight text-foreground dark:text-foreground"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              {title}
            </motion.h2>
          )}
        </div>

        <div className="mx-auto max-w-container">
          {/* 极简网格布局 - 12列6行布局 - 交换两行位置 */}
          <div className="grid grid-cols-1 md:grid-cols-12 md:grid-rows-6 gap-element-y">
            {/* 第一个区块 - 左下小卡片，占据3列3行 (原第四个位置) */}
            <motion.div 
              className="md:col-span-3 md:row-start-1 md:row-span-3 group"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <div className="h-full overflow-hidden rounded-2xl bg-card dark:bg-gray-800 shadow-sm ring-1 ring-border dark:ring-border transition-all duration-300 hover:shadow-md hover:ring-gray-300 dark:hover:ring-gray-700">
                <div className="h-full p-6 flex flex-col">
                  {safeItems[3].tagline && (
                    <div className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary mb-3">
                      {safeItems[3].tagline}
                    </div>
                  )}
                  
                  <h3 id="minimal-five-card-0-title" className="text-heading-4/heading-4 font-semibold text-foreground dark:text-foreground mb-element-y">
                    {safeItems[3].title}
                  </h3>
                  
                  <p className="text-muted-foreground dark:text-muted-foreground text-body-small/body-small mb-element-y leading-relaxed" aria-labelledby="minimal-five-card-0-title">
                    {safeItems[3].description}
                  </p>
                  
                  <div className="mt-auto">
                    <div className="relative w-full h-40 overflow-hidden rounded-xl">
                      <Picture
                        src={safeItems[3].img}
                        alt={safeItems[3].alt || "Feature image"}
                        widths={[375, 480, 640, 768]}
                        sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 300px"
                        quality={{ avif: 60, webp: 65, jpeg: 70, png: 75 }}
                        forceFillHeight={true}
                        className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                        aria-describedby="minimal-five-card-0-title"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-foreground/10 dark:from-foreground/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
            
            {/* 第二个区块 - 中间小卡片，占据6列3行 (原第三个位置) */}
            <motion.div 
              className="md:col-span-6 md:col-start-4 md:row-start-1 md:row-span-3 group"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              <div className="h-full overflow-hidden rounded-2xl bg-card dark:bg-gray-800 shadow-sm ring-1 ring-border dark:ring-border transition-all duration-300 hover:shadow-md hover:ring-gray-300 dark:hover:ring-gray-700">
                <div className="h-full p-6 flex flex-col">
                  <div className="mb-4">
                    <div className="relative w-full h-48 overflow-hidden rounded-xl">
                      <Picture
                        src={safeItems[2].img}
                        alt={safeItems[2].alt || "Feature image"}
                        widths={[375, 480, 640, 768, 1024]}
                        sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 600px"
                        quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                        forceFillHeight={true}
                        className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                        aria-describedby="minimal-five-card-1-title"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-foreground/10 dark:from-foreground/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </div>
                  </div>
                  
                  {safeItems[2].tagline && (
                    <div className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary mb-3">
                      {safeItems[2].tagline}
                    </div>
                  )}
                  
                  <h3 id="minimal-five-card-1-title" className="text-heading-4/heading-4 font-semibold text-foreground dark:text-foreground mb-element-y">
                    {safeItems[2].title}
                  </h3>
                  
                  <p className="text-muted-foreground dark:text-muted-foreground text-body-small/body-small mb-element-y leading-relaxed" aria-labelledby="minimal-five-card-1-title">
                    {safeItems[2].description}
                  </p>
                </div>
              </div>
            </motion.div>
            
            {/* 第三个区块 - 右下小卡片，占据3列3行 (原第五个位置) */}
            <motion.div 
              className="md:col-span-3 md:col-start-10 md:row-start-1 md:row-span-3 group"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <div className="h-full overflow-hidden rounded-2xl bg-card dark:bg-gray-800 shadow-sm ring-1 ring-border dark:ring-border transition-all duration-300 hover:shadow-md hover:ring-gray-300 dark:hover:ring-gray-700">
                <div className="h-full p-6 flex flex-col">
                  <div className="mb-4">
                    <div className="relative w-full h-40 overflow-hidden rounded-xl">
                      <Picture
                        src={safeItems[4].img}
                        alt={safeItems[4].alt || "Feature image"}
                        widths={[375, 480, 640, 768]}
                        sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 300px"
                        quality={{ avif: 60, webp: 65, jpeg: 70, png: 75 }}
                        forceFillHeight={true}
                        className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                        aria-describedby="minimal-five-card-2-title"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-foreground/10 dark:from-foreground/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </div>
                  </div>
                  
                  {safeItems[4].tagline && (
                    <div className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary mb-3">
                      {safeItems[4].tagline}
                    </div>
                  )}
                  
                  <h3 id="minimal-five-card-2-title" className="text-heading-4/heading-4 font-semibold text-foreground dark:text-foreground mb-element-y">
                    {safeItems[4].title}
                  </h3>
                  
                  <p className="text-muted-foreground dark:text-muted-foreground text-body-small/body-small mb-element-y leading-relaxed" aria-labelledby="minimal-five-card-2-title">
                    {safeItems[4].description}
                  </p>
                </div>
              </div>
            </motion.div>
            
            {/* 第四个区块 - 左上大卡片，占据7列3行 (原第一个位置) */}
            <motion.div 
              className="md:col-span-7 md:row-start-4 md:row-span-3 group"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <div className="h-full overflow-hidden rounded-2xl bg-card dark:bg-gray-800 shadow-sm ring-1 ring-border dark:ring-border transition-all duration-300 hover:shadow-md hover:ring-gray-300 dark:hover:ring-gray-700">
                <div className="h-full p-8 flex flex-col md:flex-row">
                  <div className="md:w-1/2 mb-4 md:mb-0 md:mr-6" aria-labelledby="minimal-five-card-3-title">
                    {safeItems[0].tagline && (
                      <div className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary mb-3">
                        {safeItems[0].tagline}
                      </div>
                    )}
                    
                    <h3 id="minimal-five-card-3-title" className="text-heading-3/heading-3 font-semibold text-foreground dark:text-foreground mb-element-y">
                      {safeItems[0].title}
                    </h3>
                    
                    <p className="text-muted-foreground dark:text-muted-foreground text-body-base/body-base leading-relaxed" aria-labelledby="minimal-five-card-3-title">
                      {safeItems[0].description}
                    </p>
                    
                    <div className="mt-6 h-0.5 w-16 bg-indigo-100" />
                  </div>
                  
                  <div className="md:w-1/2">
                    <div className="relative w-full h-64 md:h-full overflow-hidden rounded-xl">
                      <Picture
                        src={safeItems[0].img}
                        alt={safeItems[0].alt || "Feature image"}
                        widths={[375, 480, 640, 768, 1024]}
                        sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 600px"
                        quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                        forceFillHeight={true}
                        className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                        aria-describedby="minimal-five-card-3-title"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-foreground/10 dark:from-foreground/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
            
            {/* 第五个区块 - 右上大卡片，占据5列3行 (原第二个位置) */}
            <motion.div 
              className="md:col-span-5 md:col-start-8 md:row-start-4 md:row-span-3 group"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <div className="h-full overflow-hidden rounded-2xl bg-card dark:bg-gray-800 shadow-sm ring-1 ring-border dark:ring-border transition-all duration-300 hover:shadow-md hover:ring-gray-300 dark:hover:ring-gray-700">
                <div className="h-full p-6 flex flex-col">
                  <div className="mb-4">
                    <div className="relative w-full h-48 overflow-hidden rounded-xl">
                      <Picture
                        src={safeItems[1].img}
                        alt={safeItems[1].alt || "Feature image"}
                        widths={[375, 480, 640, 768, 1024]}
                        sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 500px"
                        quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                        forceFillHeight={true}
                        className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                        aria-describedby="minimal-five-card-4-title"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-foreground/10 dark:from-foreground/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </div>
                  </div>
                  
                  {safeItems[1].tagline && (
                    <div className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary mb-3">
                      {safeItems[1].tagline}
                    </div>
                  )}
                  
                  <h3 id="minimal-five-card-4-title" className="text-heading-4/heading-4 font-semibold text-foreground dark:text-foreground mb-element-y">
                    {safeItems[1].title}
                  </h3>
                  
                  <p className="text-muted-foreground dark:text-muted-foreground text-body-small/body-small leading-relaxed" aria-labelledby="minimal-five-card-4-title">
                    {safeItems[1].description}
                  </p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
}
