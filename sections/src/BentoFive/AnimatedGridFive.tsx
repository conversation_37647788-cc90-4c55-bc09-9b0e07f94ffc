'use client'

import React, { useState } from 'react';
import { motion } from 'motion/react';
import { Picture } from '../components/Picture';
import { BentoFiveProps } from './types';

export default function AnimatedGridFive({ id, tagline, title, items }: BentoFiveProps) {
  const [activeCard, setActiveCard] = useState<number | null>(null);
  
  // 确保items数组至少有5个元素
  const safeItems = items && items.length >= 5 ? items : Array(5).fill({
    title: 'Feature',
    description: 'Description text here',
    img: 'https://images.unsplash.com/photo-1624571409412-1f253e1ecc89?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&q=80',
    alt: 'Feature image'
  });

  // 颜色主题 - 扩展到5种颜色，优化对比度
  const colors = [
    { bg: 'bg-primary dark:bg-violet-500', text: 'text-primary-foreground dark:text-violet-100', accent: 'bg-primary/80 dark:bg-violet-300', highlight: 'text-white dark:text-white' },
    { bg: 'bg-primary dark:bg-indigo-500', text: 'text-primary-foreground dark:text-indigo-100', accent: 'bg-primary/80 dark:bg-indigo-300', highlight: 'text-white dark:text-white' },
    { bg: 'bg-primary dark:bg-sky-500', text: 'text-primary-foreground dark:text-sky-100', accent: 'bg-primary/80 dark:bg-sky-300', highlight: 'text-white dark:text-white' },
    { bg: 'bg-primary dark:bg-emerald-500', text: 'text-primary-foreground dark:text-emerald-100', accent: 'bg-primary/80 dark:bg-emerald-300', highlight: 'text-white dark:text-white' },
    { bg: 'bg-primary dark:bg-fuchsia-500', text: 'text-primary-foreground dark:text-fuchsia-100', accent: 'bg-primary/80 dark:bg-fuchsia-300', highlight: 'text-white dark:text-white' }
  ];

  // 卡片变体
  const cardVariants = {
    initial: (i: number) => ({ 
      scale: 0.9, 
      opacity: 0,
      y: i === 4 ? 20 : i % 2 === 0 ? 20 : -20,
      x: i < 2 ? -20 : 20
    }),
    animate: (i: number) => ({ 
      scale: 1, 
      opacity: 1,
      y: 0,
      x: 0,
      transition: { 
        duration: 0.6,
        delay: i * 0.15,
        type: "spring",
        stiffness: 100
      }
    }),
    hover: { 
      scale: 1.03,
      transition: { 
        type: "spring", 
        stiffness: 400, 
        damping: 10 
      }
    },
    tap: { 
      scale: 0.98,
      transition: { 
        type: "spring", 
        stiffness: 400, 
        damping: 17 
      }
    }
  };

  // 边框动画变体
  const borderVariants = {
    initial: {
      pathLength: 0,
      opacity: 0
    },
    animate: {
      pathLength: 1,
      opacity: 1,
      transition: {
        pathLength: { type: "spring", duration: 1.5, bounce: 0 },
        opacity: { duration: 0.2 }
      }
    }
  };

  return (
    <section 
      id={id} 
      className="bg-gradient-to-br from-background dark:from-gray-900 to-muted dark:to-gray-800 py-section-y"
      aria-labelledby="animated-grid-five-title">
      <div className="mx-auto max-w-container px-container-x">
        <motion.div 
          className="mx-auto max-w-2xl text-center"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ 
            duration: 0.7,
            type: "spring",
            stiffness: 100
          }}
        >
          {tagline && (
            <motion.p 
              className="text-body-base/body-base font-semibold text-primary dark:text-primary"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2, duration: 0.5 }}
            >
              {tagline}
            </motion.p>
          )}
          {title && (
            <motion.h2 
              id="animated-grid-five-title"
              className="mt-element-y max-w-content mx-auto text-pretty text-center text-heading-2/heading-2 font-semibold tracking-tight text-foreground dark:text-white"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ 
                delay: 0.3, 
                duration: 0.6,
                type: "spring",
                stiffness: 100
              }}
            >
              {title}
            </motion.h2>
          )}
        </motion.div>
        
        <div className="mx-auto mt-content-y max-w-container">
          {/* 交错式网格布局 - 五个卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-6 gap-element-y md:gap-element-y">
            {/* 第一个区块 - 左上角，占据3列2行 */}
            <motion.div 
              className="md:col-span-3 md:row-span-2 md:translate-y-12"
              variants={cardVariants}
              custom={0}
              initial="initial"
              animate="animate"
              whileHover="hover"
              whileTap="tap"
              onHoverStart={() => setActiveCard(0)}
              onHoverEnd={() => setActiveCard(null)}
            >
              <div className={`relative overflow-hidden rounded-3xl ${colors[0].bg} h-full`}>
                <motion.div 
                  className="absolute -right-20 -top-20 w-40 h-40 rounded-full bg-white opacity-10"
                  animate={{ 
                    x: [0, 10, 0],
                    y: [0, -10, 0],
                  }}
                  transition={{ 
                    duration: 5, 
                    repeat: Infinity,
                    repeatType: "reverse" 
                  }}
                />
                <motion.div 
                  className="absolute -left-10 -bottom-10 w-20 h-20 rounded-full bg-white opacity-10"
                  animate={{ 
                    x: [0, -10, 0],
                    y: [0, 10, 0],
                  }}
                  transition={{ 
                    duration: 4, 
                    repeat: Infinity,
                    repeatType: "reverse",
                    delay: 1
                  }}
                />
                <div className="absolute inset-0 pointer-events-none">
                  <svg className="w-full h-full">
                    <motion.rect
                      width="100%"
                      height="100%"
                      rx="24" // 圆角大小
                      fill="none"
                      stroke="#ffffff"
                      strokeWidth="2"
                      variants={borderVariants}
                      initial="initial"
                      animate={activeCard === 0 ? "animate" : "initial"}
                    />
                  </svg>
                </div>
                <div className="p-8 flex flex-col h-full" aria-labelledby="animated-grid-five-card-0-title">
                  <div className="mb-6">
                    <motion.div
                      className="relative w-full overflow-hidden rounded-xl"
                      style={{ height: 'clamp(16rem, 50vw, 32rem)' }}
                      whileHover={{ scale: 1.05 }}
                      transition={{ duration: 0.3 }}
                    >
                      <Picture
                        src={safeItems[0].img}
                        alt={safeItems[0].alt || "Feature image"}
                        widths={[375, 480, 640, 768, 1024]}
                        sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 600px"
                        quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                        forceFillHeight={true}
                        className="w-full h-full object-cover"
                        aria-describedby="animated-grid-five-card-0-title"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-violet-900/70 via-violet-900/30 to-transparent"></div>
                    </motion.div>
                  </div>
                  
                  {safeItems[0].tagline && (
                    <motion.p 
                      className={`text-sm font-medium ${colors[0].highlight}`}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.4, duration: 0.5 }}
                    >
                      {safeItems[0].tagline}
                    </motion.p>
                  )}
                  <motion.h3 
                    id="animated-grid-five-card-0-title"
                    className="mt-element-y text-heading-3/heading-3 font-semibold tracking-tight text-white"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.5, duration: 0.5 }}
                  >
                    {safeItems[0].title}
                  </motion.h3>
                  <motion.p 
                    className={`mt-element-y text-body-base/body-base ${colors[0].text} line-clamp-4 md:line-clamp-none`}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.6, duration: 0.5 }}
                    aria-labelledby="animated-grid-five-card-0-title"
                  >
                    {safeItems[0].description}
                  </motion.p>
                </div>
              </div>
            </motion.div>

            {/* 第二个区块 - 右上角，占据3列 */}
            <motion.div 
              className="md:col-span-3 md:col-start-4"
              variants={cardVariants}
              custom={1}
              initial="initial"
              animate="animate"
              whileHover="hover"
              whileTap="tap"
              onHoverStart={() => setActiveCard(1)}
              onHoverEnd={() => setActiveCard(null)}
            >
              <div className={`relative overflow-hidden rounded-3xl ${colors[1].bg} h-full`}>
                <motion.div 
                  className="absolute -right-10 -bottom-10 w-20 h-20 rounded-full bg-white opacity-10"
                  animate={{ 
                    x: [0, 10, 0],
                    y: [0, 10, 0],
                  }}
                  transition={{ 
                    duration: 4, 
                    repeat: Infinity,
                    repeatType: "reverse" 
                  }}
                />
                <div className="absolute inset-0 pointer-events-none">
                  <svg className="w-full h-full">
                    <motion.rect
                      width="100%"
                      height="100%"
                      rx="24" // 圆角大小
                      fill="none"
                      stroke="#ffffff"
                      strokeWidth="2"
                      variants={borderVariants}
                      initial="initial"
                      animate={activeCard === 1 ? "animate" : "initial"}
                    />
                  </svg>
                </div>
                <div className="p-6 flex flex-col h-full">
                  <div className="mb-4">
                    <motion.div
                      className="relative w-full h-48 overflow-hidden rounded-xl"
                      whileHover={{ scale: 1.05 }}
                      transition={{ duration: 0.3 }}
                    >
                      <Picture
                        src={safeItems[1].img}
                        alt={safeItems[1].alt || "Feature image"}
                        widths={[375, 480, 640, 768]}
                        sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 400px"
                        quality={{ avif: 60, webp: 65, jpeg: 70, png: 75 }}
                        forceFillHeight={true}
                        className="w-full h-full object-cover"
                        aria-describedby="animated-grid-five-card-1-title"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-indigo-900/70 via-indigo-900/30 to-transparent"></div>
                    </motion.div>
                  </div>
                  
                  {safeItems[1].tagline && (
                    <motion.p 
                      className={`text-sm font-medium ${colors[1].highlight}`}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.5, duration: 0.5 }}
                    >
                      {safeItems[1].tagline}
                    </motion.p>
                  )}
                  <motion.h3 
                    id="animated-grid-five-card-1-title"
                    className="mt-element-y text-heading-3/heading-3 font-semibold tracking-tight text-white"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.6, duration: 0.5 }}
                  >
                    {safeItems[1].title}
                  </motion.h3>
                  <motion.p 
                    className={`mt-element-y text-body-base/body-base ${colors[1].text} line-clamp-3 md:line-clamp-none`}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.7, duration: 0.5 }}
                    aria-labelledby="animated-grid-five-card-1-title"
                  >
                    {safeItems[1].description}
                  </motion.p>
                </div>
              </div>
            </motion.div>

            {/* 第三个区块 - 中右，占据2列 */}
            <motion.div 
              className="md:col-span-2 md:col-start-4 md:row-start-2 md:translate-y-12"
              variants={cardVariants}
              custom={2}
              initial="initial"
              animate="animate"
              whileHover="hover"
              whileTap="tap"
              onHoverStart={() => setActiveCard(2)}
              onHoverEnd={() => setActiveCard(null)}
            >
              <div className={`relative overflow-hidden rounded-3xl ${colors[2].bg} h-full`}>
                <motion.div 
                  className="absolute -left-10 -top-10 w-20 h-20 rounded-full bg-white opacity-10"
                  animate={{ 
                    x: [0, -10, 0],
                    y: [0, -10, 0],
                  }}
                  transition={{ 
                    duration: 6, 
                    repeat: Infinity,
                    repeatType: "reverse" 
                  }}
                />
                <div className="absolute inset-0 pointer-events-none">
                  <svg className="w-full h-full">
                    <motion.rect
                      width="100%"
                      height="100%"
                      rx="24" // 圆角大小
                      fill="none"
                      stroke="#ffffff"
                      strokeWidth="2"
                      variants={borderVariants}
                      initial="initial"
                      animate={activeCard === 2 ? "animate" : "initial"}
                    />
                  </svg>
                </div>
                <div className="p-6 flex flex-col h-full">
                  {safeItems[2].tagline && (
                    <motion.p 
                      className={`text-sm font-medium ${colors[2].highlight}`}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.6, duration: 0.5 }}
                    >
                      {safeItems[2].tagline}
                    </motion.p>
                  )}
                  <motion.h3 
                    id="animated-grid-five-card-2-title"
                    className="mt-element-y text-heading-4/heading-4 font-semibold tracking-tight text-white"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.7, duration: 0.5 }}
                  >
                    {safeItems[2].title}
                  </motion.h3>
                  <motion.p 
                    className={`mt-element-y text-body-small/body-small ${colors[2].text} line-clamp-3 md:line-clamp-none`}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.8, duration: 0.5 }}
                    aria-labelledby="animated-grid-five-card-2-title"
                  >
                    {safeItems[2].description}
                  </motion.p>
                  
                  <div className="mt-auto pt-4">
                    <motion.div
                      className="relative w-full h-40 overflow-hidden rounded-xl"
                      whileHover={{ scale: 1.05 }}
                      transition={{ duration: 0.3 }}
                    >
                      <Picture
                        src={safeItems[2].img}
                        alt={safeItems[2].alt || "Feature image"}
                        widths={[375, 480, 640, 768]}
                        sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 300px"
                        quality={{ avif: 60, webp: 65, jpeg: 70, png: 75 }}
                        forceFillHeight={true}
                        className="w-full h-full object-cover"
                        aria-describedby="animated-grid-five-card-2-title"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-sky-900/70 via-sky-900/30 to-transparent"></div>
                    </motion.div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* 第四个区块 - 右下角，占据1列 */}
            <motion.div 
              className="md:col-span-1 md:col-start-6 md:row-start-2 md:translate-y-12"
              variants={cardVariants}
              custom={3}
              initial="initial"
              animate="animate"
              whileHover="hover"
              whileTap="tap"
              onHoverStart={() => setActiveCard(3)}
              onHoverEnd={() => setActiveCard(null)}
            >
              <div className={`relative overflow-hidden rounded-3xl ${colors[3].bg} h-full`}>
                <motion.div 
                  className="absolute -right-10 -bottom-10 w-20 h-20 rounded-full bg-white opacity-10"
                  animate={{ 
                    x: [0, 10, 0],
                    y: [0, 10, 0],
                  }}
                  transition={{ 
                    duration: 4, 
                    repeat: Infinity,
                    repeatType: "reverse" 
                  }}
                />
                <div className="absolute inset-0 pointer-events-none">
                  <svg className="w-full h-full">
                    <motion.rect
                      width="100%"
                      height="100%"
                      rx="24" // 圆角大小
                      fill="none"
                      stroke="#ffffff"
                      strokeWidth="2"
                      variants={borderVariants}
                      initial="initial"
                      animate={activeCard === 3 ? "animate" : "initial"}
                    />
                  </svg>
                </div>
                <div className="p-5 flex flex-col h-full" aria-labelledby="animated-grid-five-card-3-title">
                  <motion.div
                    className="relative w-full h-28 overflow-hidden rounded-xl mb-3"
                    whileHover={{ scale: 1.05 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Picture
                      src={safeItems[3].img}
                      alt={safeItems[3].alt || "Feature image"}
                      widths={[375, 480, 640, 768]}
                      sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 200px"
                      quality={{ avif: 60, webp: 65, jpeg: 70, png: 75 }}
                      forceFillHeight={true}
                      className="w-full h-full object-cover"
                      aria-describedby="animated-grid-five-card-3-title"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-emerald-900/70 via-emerald-900/30 to-transparent"></div>
                  </motion.div>
                  
                  {safeItems[3].tagline && (
                    <motion.p 
                      className={`text-xs font-medium ${colors[3].highlight}`}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.7, duration: 0.5 }}
                    >
                      {safeItems[3].tagline}
                    </motion.p>
                  )}
                  <motion.h3 
                    id="animated-grid-five-card-3-title"
                    className="mt-element-y text-heading-4/heading-4 font-semibold tracking-tight text-white"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.8, duration: 0.5 }}
                  >
                    {safeItems[3].title}
                  </motion.h3>
                  <motion.p 
                    className={`mt-element-y text-body-small/body-small ${colors[3].text}`}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.9, duration: 0.5 }}
                    aria-labelledby="animated-grid-five-card-3-title"
                  >
                    {safeItems[3].description}
                  </motion.p>
                </div>
              </div>
            </motion.div>

            {/* 第五个区块 - 底部，占据6列 */}
            <motion.div 
              className="md:col-span-6 md:row-start-3"
              variants={cardVariants}
              custom={4}
              initial="initial"
              animate="animate"
              whileHover="hover"
              whileTap="tap"
              onHoverStart={() => setActiveCard(4)}
              onHoverEnd={() => setActiveCard(null)}
            >
              <div className={`relative overflow-hidden rounded-3xl ${colors[4].bg} h-full`}>
                <motion.div 
                  className="absolute -right-20 -top-20 w-40 h-40 rounded-full bg-white opacity-10"
                  animate={{ 
                    x: [0, 10, 0],
                    y: [0, -10, 0],
                  }}
                  transition={{ 
                    duration: 5, 
                    repeat: Infinity,
                    repeatType: "reverse" 
                  }}
                />
                <motion.div 
                  className="absolute -left-10 -bottom-10 w-20 h-20 rounded-full bg-white opacity-10"
                  animate={{ 
                    x: [0, -10, 0],
                    y: [0, 10, 0],
                  }}
                  transition={{ 
                    duration: 4, 
                    repeat: Infinity,
                    repeatType: "reverse",
                    delay: 1
                  }}
                />
                <div className="absolute inset-0 pointer-events-none">
                  <svg className="w-full h-full">
                    <motion.rect
                      width="100%"
                      height="100%"
                      rx="24" // 圆角大小
                      fill="none"
                      stroke="#ffffff"
                      strokeWidth="2"
                      variants={borderVariants}
                      initial="initial"
                      animate={activeCard === 4 ? "animate" : "initial"}
                    />
                  </svg>
                </div>
                <div className="py-4 px-6 h-full flex flex-col md:flex-row" style={{ minHeight: "16rem" }} aria-labelledby="animated-grid-five-card-4-title">
                  <div className="md:w-1/2 mb-4 md:mb-0 md:pr-6 flex flex-col justify-center" aria-labelledby="animated-grid-five-card-4-title">
                    {safeItems[4].tagline && (
                      <motion.p 
                        className={`text-sm font-medium ${colors[4].highlight}`}
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.8, duration: 0.5 }}
                      >
                        {safeItems[4].tagline}
                      </motion.p>
                    )}
                    <motion.h3 
                      id="animated-grid-five-card-4-title"
                      className="mt-element-y text-heading-3/heading-3 font-semibold tracking-tight text-white"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.9, duration: 0.5 }}
                    >
                      {safeItems[4].title}
                    </motion.h3>
                    <motion.p 
                      className={`mt-2 text-base ${colors[4].text} line-clamp-3 md:line-clamp-none`}
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 1.0, duration: 0.5 }}
                      aria-labelledby="animated-grid-five-card-4-title"
                    >
                      {safeItems[4].description}
                    </motion.p>
                  </div>
                  <div className="md:w-1/2 relative overflow-hidden rounded-xl" style={{ minHeight: "180px", height: "auto" }}>
                    <motion.div
                      className="w-full h-full"
                      whileHover={{ scale: 1.05 }}
                      transition={{ duration: 0.3 }}
                    >
                      <Picture
                        src={safeItems[4].img}
                        alt={safeItems[4].alt || "Feature image"}
                        widths={[375, 480, 640, 768, 1024]}
                        sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 400px"
                        quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                        forceFillHeight={true}
                        className="w-full h-full object-cover"
                        aria-describedby="animated-grid-five-card-4-title"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-fuchsia-900/70 via-fuchsia-900/30 to-transparent"></div>
                    </motion.div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
}
