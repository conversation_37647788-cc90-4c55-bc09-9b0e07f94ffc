'use client'

import React, { useEffect, useRef, useState } from 'react';
import { motion } from 'motion/react';
import { Picture } from '../components/Picture';
import { BentoFiveProps } from './types';

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ')
}

export default function ParallaxFive({ tagline, title, items }: BentoFiveProps) {
  // 创建引用以跟踪容器元素
  const containerRef = useRef<HTMLDivElement>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);
  
  // 确保items数组至少有5个元素
  const safeItems = items && items.length >= 5 ? items : Array(5).fill({
    title: 'Feature',
    description: 'Description text here',
    img: 'https://images.unsplash.com/photo-1624571409412-1f253e1ecc89?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&q=80',
    alt: 'Feature image'
  });

  // 为每个位置分配特定的图片尺寸
  const imgSizes = [
    'w=1000&h=550', // 第一个位置 - 左上大卡片
    'w=800&h=550',  // 第二个位置 - 右上大卡片
    'w=600&h=450',  // 第三个位置 - 中间小卡片
    'w=600&h=450',  // 第四个位置 - 左下小卡片
    'w=800&h=550'   // 第五个位置 - 右下小卡片
  ];

  // 为每个卡片分配不同的视差深度
  const parallaxDepths = [6, 8, 12, 10, 7];

  // 设置视差效果
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleMouseMove = (e: MouseEvent) => {
      const { left, top, width, height } = container.getBoundingClientRect();
      const x = (e.clientX - left) / width - 0.5;
      const y = (e.clientY - top) / height - 0.5;
      
      setMousePosition({ x, y });
    };

    const handleMouseEnter = () => setIsHovering(true);
    const handleMouseLeave = () => setIsHovering(false);

    // 添加鼠标事件监听器
    container.addEventListener('mousemove', handleMouseMove);
    container.addEventListener('mouseenter', handleMouseEnter);
    container.addEventListener('mouseleave', handleMouseLeave);

    // 清理函数
    return () => {
      container.removeEventListener('mousemove', handleMouseMove);
      container.removeEventListener('mouseenter', handleMouseEnter);
      container.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, []);

  // 计算视差变换
  const getParallaxTransform = (depth: number) => {
    if (!isHovering) return { x: 0, y: 0 };
    const moveX = mousePosition.x * depth * 20; // 放大效果
    const moveY = mousePosition.y * depth * 20;
    return { x: moveX, y: moveY };
  };

  return (
    <section className="bg-gradient-to-b from-background dark:from-gray-900 to-indigo-100 dark:to-indigo-950 py-section-y overflow-hidden" aria-labelledby="parallax-five-title">
      <motion.div 
        className="mx-auto max-w-container px-container-x relative" 
        ref={containerRef}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
      >
        <motion.div 
          className="mx-auto max-w-4xl text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {tagline && (
            <motion.p 
              className="text-body-base/body-base font-semibold text-primary dark:text-violet-400"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2, duration: 0.4 }}
            >
              {tagline}
            </motion.p>
          )}
          {title && (
            <motion.h2 
              id="parallax-five-title"
              className="mx-auto mt-element-y max-w-content text-balance text-center text-heading-2/heading-2 font-semibold tracking-tight text-foreground dark:text-white"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.5 }}
            >
              {title}
            </motion.h2>
          )}
        </motion.div>
        
        {/* 背景层 - 装饰元素 */}
        <motion.div 
          className="absolute inset-0 pointer-events-none"
          animate={getParallaxTransform(2)}
          transition={{ type: "spring", stiffness: 75, damping: 30, mass: 0.5 }}
        >
          <div className="absolute top-10 left-10 w-32 h-32 rounded-full bg-violet-900/30 blur-xl"></div>
          <div className="absolute bottom-10 right-10 w-32 h-32 rounded-full bg-indigo-900/30 blur-xl"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 rounded-full bg-blue-900/20 blur-3xl"></div>
          <div className="absolute top-1/4 right-1/4 w-24 h-24 rounded-full bg-purple-900/20 blur-xl"></div>
          <div className="absolute bottom-1/4 left-1/4 w-24 h-24 rounded-full bg-indigo-900/20 blur-xl"></div>
        </motion.div>
        
        <div className="mt-content-y grid grid-cols-1 gap-element-y sm:mt-section-y lg:grid-cols-12 lg:grid-rows-6 relative">
          {/* 第一个卡片 - 左上大卡片 */}
          <motion.div 
            className="relative lg:col-span-6 lg:row-span-3 z-10"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.5 }}
            whileHover={{ 
              y: -5,
              boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2)',
              transition: { duration: 0.2 }
            }}
          >
            <div className="bg-card/80 dark:bg-gray-800/50 backdrop-blur-sm rounded-3xl overflow-hidden shadow-xl border border-border dark:border-gray-700/50 h-full">
              <div className="grid sm:grid-cols-2 h-full">
                <div className="p-8 flex flex-col">
                  <motion.div
                    animate={getParallaxTransform(parallaxDepths[0])}
                    transition={{ type: "spring", stiffness: 75, damping: 30, mass: 0.5 }}
                  >
                    {safeItems[0].tagline && (
                      <motion.p 
                        className="text-body-small/body-small font-medium text-primary dark:text-violet-400"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.3, duration: 0.4 }}
                      >
                        {safeItems[0].tagline}
                      </motion.p>
                    )}
                    <motion.h3 
                      id="parallax-five-card-0-title"
                      className="mt-element-y text-heading-3/heading-3 font-semibold tracking-tight text-foreground dark:text-white"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.4, duration: 0.4 }}
                    >
                      {safeItems[0].title}
                    </motion.h3>
                    <motion.p 
                      className="mt-element-y text-body-base/body-base text-muted-foreground dark:text-gray-300"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.5, duration: 0.4 }}
                      aria-labelledby="parallax-five-card-0-title"
                    >
                      {safeItems[0].description}
                    </motion.p>
                  </motion.div>
                </div>
                <div className="relative overflow-hidden">
                  <motion.div
                    animate={getParallaxTransform(parallaxDepths[0] - 2)}
                    transition={{ type: "spring", stiffness: 75, damping: 30, mass: 0.5 }}
                    className="h-full"
                  >
                    <Picture
                      src={safeItems[0].img}
                      alt={safeItems[0].alt || "Feature image"}
                      widths={[375, 480, 640, 768, 1024]}
                      sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 600px"
                      quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                      forceFillHeight={true}
                      className="h-full w-full object-cover"
                      aria-describedby="parallax-five-card-0-title"
                    />
                  </motion.div>
                  <div className="absolute inset-0 bg-gradient-to-r from-card/80 dark:from-gray-800/80 via-transparent to-transparent"></div>
                </div>
              </div>
            </div>
          </motion.div>
          
          {/* 第二个卡片 - 右上大卡片 */}
          <motion.div 
            className="relative lg:col-span-6 lg:row-span-3 z-20"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3, duration: 0.5 }}
            whileHover={{ 
              y: -5,
              boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2)',
              transition: { duration: 0.2 }
            }}
          >
            <div className="bg-card/80 dark:bg-gray-800/50 backdrop-blur-sm rounded-3xl overflow-hidden shadow-xl border border-border dark:border-gray-700/50 h-full">
              <div className="grid sm:grid-cols-2 h-full">
                <div className="relative overflow-hidden">
                  <motion.div
                    animate={getParallaxTransform(parallaxDepths[1] - 2)}
                    transition={{ type: "spring", stiffness: 75, damping: 30, mass: 0.5 }}
                    className="h-full"
                  >
                    <Picture
                      src={safeItems[1].img}
                      alt={safeItems[1].alt || "Feature image"}
                      widths={[375, 480, 640, 768, 1024]}
                      sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 600px"
                      quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                      forceFillHeight={true}
                      className="h-full w-full object-cover"
                      aria-describedby="parallax-five-card-1-title"
                    />
                  </motion.div>
                  <div className="absolute inset-0 bg-gradient-to-l from-gray-800/80 via-transparent to-transparent"></div>
                </div>
                <div className="p-8 flex flex-col">
                  <motion.div
                    animate={getParallaxTransform(parallaxDepths[1])}
                    transition={{ type: "spring", stiffness: 75, damping: 30, mass: 0.5 }}
                  >
                    {safeItems[1].tagline && (
                      <motion.p 
                        className="text-body-small/body-small font-medium text-primary dark:text-violet-400"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.3, duration: 0.4 }}
                      >
                        {safeItems[1].tagline}
                      </motion.p>
                    )}
                    <motion.h3 
                      id="parallax-five-card-1-title"
                      className="mt-2 text-2xl font-semibold tracking-tight text-foreground dark:text-white"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.4, duration: 0.4 }}
                    >
                      {safeItems[1].title}
                    </motion.h3>
                    <motion.p 
                      className="mt-4 text-base text-muted-foreground dark:text-gray-300"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.5, duration: 0.4 }}
                      aria-labelledby="parallax-five-card-1-title"
                    >
                      {safeItems[1].description}
                    </motion.p>
                  </motion.div>
                </div>
              </div>
            </div>
          </motion.div>
          
          {/* 第三个卡片 - 中间小卡片 */}
          <motion.div 
            className="relative lg:col-span-4 lg:row-span-3 z-30"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.5 }}
            whileHover={{ 
              y: -5,
              boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2)',
              transition: { duration: 0.2 }
            }}
          >
            <div className="bg-card/80 dark:bg-gray-800/50 backdrop-blur-sm rounded-3xl overflow-hidden shadow-xl border border-border dark:border-gray-700/50 h-full">
              <div className="flex flex-col h-full">
                <div className="relative overflow-hidden h-48">
                  <motion.div
                    animate={getParallaxTransform(parallaxDepths[2] - 2)}
                    transition={{ type: "spring", stiffness: 75, damping: 30, mass: 0.5 }}
                    className="h-full"
                  >
                    <Picture
                      src={safeItems[2].img}
                      alt={safeItems[2].alt || "Feature image"}
                      widths={[375, 480, 640, 768]}
                      sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 400px"
                      quality={{ avif: 60, webp: 65, jpeg: 70, png: 75 }}
                      forceFillHeight={true}
                      className="h-full w-full object-cover"
                      aria-describedby="parallax-five-card-2-title"
                    />
                  </motion.div>
                  <div className="absolute inset-0 bg-gradient-to-t from-gray-800/80 via-transparent to-transparent"></div>
                </div>
                <div className="p-6 flex-grow">
                  <motion.div
                    animate={getParallaxTransform(parallaxDepths[2])}
                    transition={{ type: "spring", stiffness: 75, damping: 30, mass: 0.5 }}
                  >
                    {safeItems[2].tagline && (
                      <motion.p 
                        className="text-body-small/body-small font-medium text-primary dark:text-violet-400"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.3, duration: 0.4 }}
                      >
                        {safeItems[2].tagline}
                      </motion.p>
                    )}
                    <motion.h3 
                      id="parallax-five-card-2-title"
                      className="mt-2 text-xl font-semibold tracking-tight text-white"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.4, duration: 0.4 }}
                    >
                      {safeItems[2].title}
                    </motion.h3>
                    <motion.p 
                      className="mt-3 text-sm text-gray-300"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.5, duration: 0.4 }}
                      aria-labelledby="parallax-five-card-2-title"
                    >
                      {safeItems[2].description}
                    </motion.p>
                  </motion.div>
                </div>
              </div>
            </div>
          </motion.div>
          
          {/* 第四个卡片 - 左下小卡片 */}
          <motion.div 
            className="relative lg:col-span-4 lg:row-span-3 z-20"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5, duration: 0.5 }}
            whileHover={{ 
              y: -5,
              boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2)',
              transition: { duration: 0.2 }
            }}
          >
            <div className="bg-card/80 dark:bg-gray-800/50 backdrop-blur-sm rounded-3xl overflow-hidden shadow-xl border border-border dark:border-gray-700/50 h-full">
              <div className="flex flex-col h-full">
                <div className="p-6">
                  <motion.div
                    animate={getParallaxTransform(parallaxDepths[3])}
                    transition={{ type: "spring", stiffness: 75, damping: 30, mass: 0.5 }}
                  >
                    {safeItems[3].tagline && (
                      <motion.p 
                        className="text-body-small/body-small font-medium text-primary dark:text-violet-400"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.3, duration: 0.4 }}
                      >
                        {safeItems[3].tagline}
                      </motion.p>
                    )}
                    <motion.h3 
                      id="parallax-five-card-3-title"
                      className="mt-2 text-xl font-semibold tracking-tight text-white"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.4, duration: 0.4 }}
                    >
                      {safeItems[3].title}
                    </motion.h3>
                    <motion.p 
                      className="mt-3 text-sm text-gray-300"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.5, duration: 0.4 }}
                      aria-labelledby="parallax-five-card-3-title"
                    >
                      {safeItems[3].description}
                    </motion.p>
                  </motion.div>
                </div>
                <div className="relative overflow-hidden mt-auto h-48">
                  <motion.div
                    animate={getParallaxTransform(parallaxDepths[3] - 2)}
                    transition={{ type: "spring", stiffness: 75, damping: 30, mass: 0.5 }}
                    className="h-full"
                  >
                    <Picture
                      src={safeItems[3].img}
                      alt={safeItems[3].alt || "Feature image"}
                      widths={[375, 480, 640, 768]}
                      sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 400px"
                      quality={{ avif: 60, webp: 65, jpeg: 70, png: 75 }}
                      forceFillHeight={true}
                      className="h-full w-full object-cover"
                      aria-describedby="parallax-five-card-3-title"
                    />
                  </motion.div>
                  <div className="absolute inset-0 bg-gradient-to-b from-gray-800/80 via-transparent to-transparent"></div>
                </div>
              </div>
            </div>
          </motion.div>
          
          {/* 第五个卡片 - 右下小卡片 */}
          <motion.div 
            className="relative lg:col-span-4 lg:row-span-3 z-10"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.5 }}
            whileHover={{ 
              y: -5,
              boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2)',
              transition: { duration: 0.2 }
            }}
          >
            <div className="bg-card/80 dark:bg-gray-800/50 backdrop-blur-sm rounded-3xl overflow-hidden shadow-xl border border-border dark:border-gray-700/50 h-full">
              <div className="flex flex-col h-full">
                <div className="p-6">
                  <motion.div
                    animate={getParallaxTransform(parallaxDepths[4])}
                    transition={{ type: "spring", stiffness: 75, damping: 30, mass: 0.5 }}
                  >
                    {safeItems[4].tagline && (
                      <motion.p 
                        className="text-body-small/body-small font-medium text-primary dark:text-violet-400"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.3, duration: 0.4 }}
                      >
                        {safeItems[4].tagline}
                      </motion.p>
                    )}
                    <motion.h3 
                      id="parallax-five-card-4-title"
                      className="mt-2 text-xl font-semibold tracking-tight text-white"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.4, duration: 0.4 }}
                    >
                      {safeItems[4].title}
                    </motion.h3>
                    <motion.p 
                      className="mt-3 text-sm text-gray-300"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.5, duration: 0.4 }}
                      aria-labelledby="parallax-five-card-4-title"
                    >
                      {safeItems[4].description}
                    </motion.p>
                  </motion.div>
                </div>
                <div className="relative overflow-hidden mt-auto h-48">
                  <motion.div
                    animate={getParallaxTransform(parallaxDepths[4] - 2)}
                    transition={{ type: "spring", stiffness: 75, damping: 30, mass: 0.5 }}
                    className="h-full"
                  >
                    <Picture
                      src={safeItems[4].img}
                      alt={safeItems[4].alt || "Feature image"}
                      widths={[375, 480, 640, 768]}
                      sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 400px"
                      quality={{ avif: 60, webp: 65, jpeg: 70, png: 75 }}
                      forceFillHeight={true}
                      className="h-full w-full object-cover"
                      aria-describedby="parallax-five-card-4-title"
                    />
                  </motion.div>
                  <div className="absolute inset-0 bg-gradient-to-b from-gray-800/80 via-transparent to-transparent"></div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </motion.div>
    </section>
  );
}
