'use client'

import React from 'react';
import { motion } from 'motion/react';
import { Picture } from '../components/Picture';
import { BentoFiveProps } from './types';

export default function BackgroundFive({ id, tagline, title, items }: BentoFiveProps) {
  // 确保items数组至少有5个元素
  const safeItems = items && items.length >= 5 ? items : Array(5).fill({
    title: 'Feature',
    description: 'Description text here',
    img: 'https://images.unsplash.com/photo-1624571409412-1f253e1ecc89?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&q=80',
    alt: 'Feature image'
  });

  // 为每个位置分配特定的图片尺寸
  const imgSizes = [
    'w=800&h=800',  // 第一个位置 - 大卡片
    'w=400&h=600',  // 第二个位置 - 右上小卡片
    'w=400&h=500',  // 第三个位置 - 右中小卡片
    'w=800&h=400',  // 第四个位置 - 底部左卡片
    'w=800&h=400'   // 第五个位置 - 底部右卡片
  ];

  // 背景渐变设置
  const gradients = [
    'from-blue-600 to-indigo-900',
    'from-purple-600 to-pink-700',
    'from-amber-500 to-red-700',
    'from-emerald-600 to-teal-800',
    'from-cyan-600 to-blue-800'
  ];
  
  // 文字背景遮罩，增强文字可读性
  const textOverlays = [
    'bg-gradient-to-t from-black/70 via-black/40 to-transparent',
    'bg-gradient-to-t from-black/70 via-black/40 to-transparent',
    'bg-gradient-to-t from-black/70 via-black/40 to-transparent',
    'bg-gradient-to-t from-black/70 via-black/40 to-transparent',
    'bg-gradient-to-t from-black/70 via-black/40 to-transparent'
  ];

  // 文字颜色设置
  const textColors = [
    'text-blue-700 dark:text-blue-200',
    'text-purple-700 dark:text-pink-200',
    'text-amber-700 dark:text-amber-200',
    'text-emerald-700 dark:text-emerald-200',
    'text-cyan-700 dark:text-cyan-200'
  ];

  // 描述文字颜色设置
  const descColors = [
    'text-gray-700 dark:text-blue-100',
    'text-gray-700 dark:text-pink-100',
    'text-gray-700 dark:text-amber-100',
    'text-gray-700 dark:text-emerald-100',
    'text-gray-700 dark:text-cyan-100'
  ];

  return (
    <section id={id} className="bg-background dark:bg-gray-900 py-section-y" aria-labelledby="background-five-title">
      <div className="mx-auto max-w-container px-container-x">
        <div className="mx-auto max-w-content text-center">
          {tagline && (
            <motion.p 
              className="text-body-base/body-base font-semibold text-primary dark:text-indigo-400"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              {tagline}
            </motion.p>
          )}
          {title && (
            <motion.h2 
              id="background-five-title"
              className="mt-element-y max-w-content mx-auto text-pretty text-center text-heading-2/heading-2 font-semibold tracking-tight text-foreground dark:text-white"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              {title}
            </motion.h2>
          )}
        </div>
        
        <div className="mx-auto mt-content-y max-w-container">
          {/* 使用CSS Grid创建大圆角矩形布局 */}
          <div className="grid grid-cols-1 gap-element-y sm:grid-cols-2 lg:grid-cols-4 lg:grid-rows-4 overflow-hidden rounded-3xl shadow-xl bg-muted dark:bg-gray-900">
            {/* 第一个区块 - 占据2列2行 */}
            <motion.div 
              className="group relative overflow-hidden lg:col-span-2 lg:row-span-2"
              style={{ borderRadius: '1rem 0.5rem 0.5rem 0.5rem' }}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
            >
              <div className={`absolute inset-0 bg-gradient-to-br ${gradients[0]} opacity-90`}></div>
              <div className="absolute inset-0 opacity-30">
                <Picture
                  src={safeItems[0].img}
                  alt={safeItems[0].alt || "Feature image"}
                  widths={[375, 480, 640, 768, 1024]}
                  sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 400px"
                  quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                  forceFillHeight={true}
                  className="h-full w-full object-cover transition-transform duration-700 group-hover:scale-105"
                  aria-describedby="background-five-card-0-title"
                />
              </div>
              <div className={`${textOverlays[0]} absolute inset-0`}></div>
              <div className="relative h-full flex flex-col justify-end p-6 sm:p-8 z-10" aria-labelledby="background-five-card-0-title">
                {safeItems[0].tagline && (
                  <p className="text-sm font-medium text-white/90">{safeItems[0].tagline}</p>
                )}
                <h3 id="background-five-card-0-title" className="mt-element-y text-heading-3/heading-3 font-semibold tracking-tight text-white dark:text-white">
                  {safeItems[0].title}
                </h3>
                <p className="mt-element-y text-body-base/body-base text-white/80 max-w-md" aria-labelledby="background-five-card-0-title">
                  {safeItems[0].description}
                </p>
                <div className="mt-4 h-1 w-16 bg-primary dark:bg-white rounded transition-all duration-300 group-hover:w-32"></div>
              </div>
            </motion.div>

            {/* 第二个区块 - 右上角卡片 */}
            <motion.div 
              className="group relative overflow-hidden lg:col-span-2 lg:row-span-2"
              style={{ borderRadius: '0.5rem 1rem 0.5rem 0.5rem' }}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              <div className={`absolute inset-0 bg-gradient-to-br ${gradients[1]} opacity-90`}></div>
              <div className="absolute inset-0 opacity-30">
                <Picture
                  src={safeItems[1].img}
                  alt={safeItems[1].alt || "Feature image"}
                  widths={[375, 480, 640, 768, 1024]}
                  sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 400px"
                  quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                  forceFillHeight={true}
                  className="h-full w-full object-cover transition-transform duration-700 group-hover:scale-105"
                  aria-describedby="background-five-card-1-title"
                />
              </div>
              <div className={`${textOverlays[1]} absolute inset-0`}></div>
              <div className="relative h-full flex flex-col justify-end p-5 sm:p-6 z-10" aria-labelledby="background-five-card-1-title">
                {safeItems[1].tagline && (
                  <p className="text-sm font-medium text-gray-200">{safeItems[1].tagline}</p>
                )}
                <h3 id="background-five-card-1-title" className="mt-element-y text-heading-4/heading-4 font-semibold tracking-tight text-gray-200">
                  {safeItems[1].title}
                </h3>
                <p className="mt-element-y text-body-small/body-small text-gray-300 line-clamp-3 md:line-clamp-none" aria-labelledby="background-five-card-1-title">
                  {safeItems[1].description}
                </p>
                <div className="mt-4 h-1 w-12 bg-primary dark:bg-white rounded transition-all duration-300 group-hover:w-24"></div>
              </div>
            </motion.div>

            {/* 第三个区块 - 左下角卡片 */}
            <motion.div 
              className="group relative overflow-hidden lg:col-span-1 lg:row-span-2 lg:row-start-3"
              style={{ borderRadius: '0.5rem 0.5rem 0.5rem 1rem' }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <div className={`absolute inset-0 bg-gradient-to-br ${gradients[2]} opacity-90`}></div>
              <div className="absolute inset-0 opacity-30">
                <Picture
                  src={safeItems[2].img}
                  alt={safeItems[2].alt || "Feature image"}
                  widths={[375, 480, 640, 768]}
                  sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 300px"
                  quality={{ avif: 60, webp: 65, jpeg: 70, png: 75 }}
                  forceFillHeight={true}
                  className="h-full w-full object-cover transition-transform duration-700 group-hover:scale-105"
                  aria-describedby="background-five-card-2-title"
                />
              </div>
              <div className={`${textOverlays[2]} absolute inset-0`}></div>
              <div className="relative h-full flex flex-col justify-end p-5 sm:p-6 z-10" style={{ minHeight: 'clamp(14rem, 40vw, 24rem)' }}>
                {safeItems[2].tagline && (
                  <p className="text-sm font-medium text-white/90">{safeItems[2].tagline}</p>
                )}
                <h3 id="background-five-card-2-title" className="mt-element-y text-heading-4/heading-4 font-semibold tracking-tight text-white dark:text-white">
                  {safeItems[2].title}
                </h3>
                <p className="mt-element-y text-body-small/body-small text-white/80 line-clamp-3 md:line-clamp-none" aria-labelledby="background-five-card-2-title">
                  {safeItems[2].description}
                </p>
                <div className="mt-4 h-1 w-12 bg-primary dark:bg-white rounded transition-all duration-300 group-hover:w-24"></div>
              </div>
            </motion.div>

            {/* 第四个区块 - 中下卡片 */}
            <motion.div 
              className="group relative overflow-hidden lg:col-span-2 lg:row-span-2 lg:row-start-3 lg:col-start-2"
              style={{ borderRadius: '0.5rem 0.5rem 0.5rem 0.5rem' }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <div className={`absolute inset-0 bg-gradient-to-br ${gradients[3]} opacity-90`}></div>
              <div className="absolute inset-0 opacity-30">
                <Picture
                  src={safeItems[3].img}
                  alt={safeItems[3].alt || "Feature image"}
                  widths={[375, 480, 640, 768, 1024]}
                  sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 400px"
                  quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                  forceFillHeight={true}
                  className="h-full w-full object-cover transition-transform duration-700 group-hover:scale-105"
                  aria-describedby="background-five-card-3-title"
                />
              </div>
              <div className={`${textOverlays[3]} absolute inset-0`}></div>
              <div className="relative h-full flex flex-col justify-end p-5 sm:p-6 z-10" aria-labelledby="background-five-card-3-title">
                {safeItems[3].tagline && (
                  <p className="text-sm font-medium text-white/90">{safeItems[3].tagline}</p>
                )}
                <h3 id="background-five-card-3-title" className="mt-element-y text-heading-4/heading-4 font-semibold tracking-tight text-white dark:text-white">
                  {safeItems[3].title}
                </h3>
                <p className="mt-element-y text-body-small/body-small text-white/80 line-clamp-3 md:line-clamp-none" aria-labelledby="background-five-card-3-title">
                  {safeItems[3].description}
                </p>
                <div className="mt-4 h-1 w-12 bg-primary dark:bg-white rounded transition-all duration-300 group-hover:w-24"></div>
              </div>
            </motion.div>

            {/* 第五个区块 - 右下角卡片 */}
            <motion.div 
              className="group relative overflow-hidden lg:col-span-1 lg:row-span-2 lg:row-start-3 lg:col-start-4"
              style={{ borderRadius: '0.5rem 0.5rem 1rem 0.5rem' }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <div className={`absolute inset-0 bg-gradient-to-br ${gradients[4]} opacity-90`}></div>
              <div className="absolute inset-0 opacity-30">
                <Picture
                  src={safeItems[4].img}
                  alt={safeItems[4].alt || "Feature image"}
                  widths={[375, 480, 640, 768]}
                  sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 300px"
                  quality={{ avif: 60, webp: 65, jpeg: 70, png: 75 }}
                  forceFillHeight={true}
                  className="h-full w-full object-cover transition-transform duration-700 group-hover:scale-105"
                  aria-describedby="background-five-card-4-title"
                />
              </div>
              <div className={`${textOverlays[4]} absolute inset-0`}></div>
              <div className="relative h-full flex flex-col justify-end p-5 sm:p-6 z-10" style={{ minHeight: 'clamp(14rem, 40vw, 24rem)' }}>
                {safeItems[4].tagline && (
                  <p className="text-sm font-medium text-white/90">{safeItems[4].tagline}</p>
                )}
                <h3 id="background-five-card-4-title" className="mt-element-y text-heading-4/heading-4 font-semibold tracking-tight text-white dark:text-white">
                  {safeItems[4].title}
                </h3>
                <p className="mt-element-y text-body-small/body-small text-white/80 line-clamp-3 md:line-clamp-none" aria-labelledby="background-five-card-4-title">
                  {safeItems[4].description}
                </p>
                <div className="mt-4 h-1 w-12 bg-primary dark:bg-white rounded transition-all duration-300 group-hover:w-24"></div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
}
