'use client'

import React, { useState, useEffect, useRef } from 'react';
import { motion, useMotionValue, useTransform, AnimatePresence } from 'motion/react';
import { Picture } from '../components/Picture';
import { BentoFiveProps } from './types';

export default function TechGlowFive({ id, tagline, title, items }: BentoFiveProps) {
  // 确保items数组至少有5个元素
  const safeItems = items && items.length >= 5 ? items : Array(5).fill({
    title: 'Feature',
    description: 'Description text here',
    img: 'https://images.unsplash.com/photo-1624571409412-1f253e1ecc89?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&q=80',
    alt: 'Feature image'
  });

  // 为每个位置分配特定的图片尺寸
  // const imgSizes = [
  //   'w=900&h=600',  // 第一个位置 - 左上大卡片
  //   'w=700&h=600',  // 第二个位置 - 右上大卡片
  //   'w=600&h=450',  // 第三个位置 - 中间小卡片
  //   'w=450&h=400',  // 第四个位置 - 左下小卡片
  //   'w=450&h=400'   // 第五个位置 - 右下小卡片
  // ];

  // 鼠标位置状态
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [cursorVisible, setCursorVisible] = useState(false);
  
  // 活跃卡片状态
  const [activeCard, setActiveCard] = useState<number | null>(null);
  
  // 容器引用
  const containerRef = useRef<HTMLDivElement>(null);

  // 更新鼠标位置
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        setMousePosition({
          x: e.clientX - rect.left,
          y: e.clientY - rect.top
        });
        setCursorVisible(true);
      }
    };

    const handleMouseLeave = () => {
      setCursorVisible(false);
    };

    const container = containerRef.current;
    if (container) {
      container.addEventListener('mousemove', handleMouseMove);
      container.addEventListener('mouseleave', handleMouseLeave);
    }

    return () => {
      if (container) {
        container.removeEventListener('mousemove', handleMouseMove);
        container.removeEventListener('mouseleave', handleMouseLeave);
      }
    };
  }, []);

  // 卡片变体 - 进一步简化动画效果
  const cardVariants = {
    initial: (i: number) => ({ 
      opacity: 0,
      y: 10 // 减小初始位移
    }),
    animate: (i: number) => ({ 
      opacity: 1,
      y: 0,
      transition: { 
        delay: i * 0.08, // 减小延迟
        duration: 0.4, // 缩短动画时间
        ease: 'easeOut'
      }
    }),
    hover: { 
      scale: 1.01, // 进一步降低缩放比例
      // 移除滤镜效果
      transition: { 
        type: "tween", // 使用更简单的动画类型
        duration: 0.2, // 缩短动画时间
        ease: "easeOut"
      }
    },
    tap: { 
      scale: 0.99, // 减小点击时的缩放
      transition: { 
        type: "tween", 
        duration: 0.1, // 缩短动画时间
        ease: "easeIn"
      }
    }
  };

  // 光照效果变体 - 简化动画效果
  const glowVariants = {
    initial: {
      opacity: 0,
      scale: 0.9
    },
    animate: {
      opacity: 0.5, // 降低不透明度
      scale: 1,
      transition: {
        duration: 3, // 延长动画周期减少重绘
        repeat: Infinity,
        repeatType: "reverse" as const
      }
    }
  };

  // 霓虹边框变体 - 简化动画效果
  const neonBorderVariants = {
    initial: {
      pathLength: 0,
      opacity: 0
    },
    animate: {
      pathLength: 0.8, // 降低完成度
      opacity: 0.7, // 降低不透明度
      transition: {
        pathLength: { type: "tween", duration: 1, ease: "easeOut" }, // 使用更简单的动画类型
        opacity: { duration: 0.3 } // 延长淡入时间
      }
    }
  };

  return (
    <section id={id} className="bg-background dark:bg-gray-950 py-section-y overflow-hidden relative" aria-labelledby="tech-glow-five-title">
      {/* 背景光效 - 简化为静态效果 */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-1/2 h-1/2 bg-purple-600/10 dark:bg-purple-600/15 rounded-full filter blur-[100px] opacity-10 dark:opacity-15 transition-opacity duration-300"></div>
        <div className="absolute top-1/3 right-1/4 w-1/3 h-1/3 bg-blue-500/10 dark:bg-blue-500/15 rounded-full filter blur-[80px] opacity-10 dark:opacity-15 transition-opacity duration-300"></div>
        <div className="absolute bottom-1/4 left-1/3 w-1/4 h-1/4 bg-cyan-400/10 dark:bg-cyan-400/10 rounded-full filter blur-[60px] opacity-5 dark:opacity-10 transition-opacity duration-300"></div>
      </div>
      
      {/* 网格背景 - 简化透明度 */}
      <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiMyMTIxMjEiIGZpbGwtb3BhY2l0eT0iMC40Ij48cGF0aCBkPSJNMzYgMzRoLTJ2LTRoMnY0em0wLTZoLTJ2LTRoMnY0em0wLTZoLTJ2LTRoMnY0em0wLTZoLTJWNmgydjR6bTAgMjRoLTJ2LTRoMnY0em0wIDZoLTJ2LTRoMnY0em0wIDZoLTJ2LTRoMnY0em0wIDZoLTJ2LTRoMnY0em0tNi0yNGgtMnYtNGgydjR6bTAgNmgtMnYtNGgydjR6bTAgNmgtMnYtNGgydjR6bTAgNmgtMnYtNGgydjR6bS02LTI0aC0ydi00aDJ2NHptMCA2aC0ydi00aDJ2NHptMCA2aC0ydi00aDJ2NHptMCA2aC0ydi00aDJ2NHptLTYtMjRoLTJ2LTRoMnY0em0wIDZoLTJ2LTRoMnY0em0wIDZoLTJ2LTRoMnY0em0wIDZoLTJ2LTRoMnY0em0tNi0yNGgtMnYtNGgydjR6bTAgNmgtMnYtNGgydjR6bTAgNmgtMnYtNGgydjR6bTAgNmgtMnYtNGgydjR6Ii8+PC9nPjwvZz48L3N2Zz4=')] opacity-5 dark:opacity-8 transition-opacity duration-300"></div>
      
      {/* 主容器 */}
      <div 
        ref={containerRef}
        className="relative mx-auto max-w-container px-container-x py-content-y overflow-hidden bg-background dark:bg-transparent transition-colors duration-300"
      >
        {/* 自定义光标效果 */}
        {cursorVisible && (
          <motion.div 
            className="pointer-events-none absolute w-64 h-64 rounded-full"
            style={{ 
              left: mousePosition.x, 
              top: mousePosition.y,
              background: 'radial-gradient(circle, rgba(139, 92, 246, 0.15) 0%, rgba(30, 64, 175, 0.05) 50%, rgba(0, 0, 0, 0) 70%)',
              transform: 'translate(-50%, -50%)'
            }}
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.5 }}
            transition={{ duration: 0.2 }}
          />
        )}
        
        <motion.div 
          className="mx-auto max-w-2xl text-center"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7 }}
        >
          {tagline && (
            <motion.p 
              className="text-body-base/body-base font-semibold text-primary dark:text-purple-400"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2, duration: 0.5 }}
            >
              {tagline}
            </motion.p>
          )}
          {title && (
            <motion.h2 
              id="tech-glow-five-title"
              className="mt-element-y max-w-content text-pretty text-heading-2/heading-2 font-bold tracking-tight text-foreground dark:text-white"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.5 }}
            >
              {title}
            </motion.h2>
          )}
        </motion.div>

        <div className="mx-auto mt-content-y max-w-container">
          {/* 动态流畅的网格布局 - 12列6行布局 */}
          <div className="grid grid-cols-1 md:grid-cols-12 md:grid-rows-6 gap-element-y">
            {/* 第一个区块 - 左上大卡片，占据7列3行 */}
            <motion.div 
              className="md:col-span-7 md:row-span-3"
              variants={cardVariants}
              custom={0}
              initial="initial"
              animate="animate"
              whileHover="hover"
              whileTap="tap"
              onHoverStart={() => setActiveCard(0)}
              onHoverEnd={() => setActiveCard(null)}
            >
              <div className="relative h-full rounded-2xl bg-gradient-to-br from-card/90 to-background/90 dark:from-gray-900 dark:to-gray-800 p-px overflow-hidden shadow-md dark:shadow-purple-900/10 transition-all duration-300 transform-gpu">
                {/* 霓虹边框 */}
                <div className="absolute inset-0 pointer-events-none">
                  <svg className="w-full h-full">
                    <motion.rect
                      width="100%"
                      height="100%"
                      rx="16" 
                      ry="16" 
                      fill="none"
                      stroke="url(#gradient1)"
                      strokeWidth="1.5"
                      strokeLinecap="round" 
                      strokeLinejoin="round" 
                      variants={neonBorderVariants}
                      initial="initial"
                      animate={activeCard === 0 ? "animate" : "initial"}
                    />
                    <defs>
                      <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stopColor="#9333EA" />
                        <stop offset="50%" stopColor="#3B82F6" />
                        <stop offset="100%" stopColor="#06B6D4" />
                      </linearGradient>
                    </defs>
                  </svg>
                </div>
                
                {/* 内部光效 */}
                <motion.div 
                  className="absolute -bottom-16 -right-16 w-32 h-32 rounded-full bg-purple-500/80 filter blur-lg opacity-15 dark:opacity-20 transition-opacity duration-300 transform-gpu will-change-transform"
                  variants={glowVariants}
                  initial="initial"
                  animate="animate"
                />
                
                <div className="relative h-full rounded-2xl bg-card/80 dark:bg-gray-900/80 backdrop-blur-[2px] p-6 flex flex-col md:flex-row transition-colors duration-300">
                  <div className="md:w-1/2 mb-4 md:mb-0 md:mr-6" aria-labelledby="tech-glow-five-card-0-title">
                    {safeItems[0].tagline && (
                      <motion.div 
                        className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100/70 text-purple-700 dark:bg-purple-900/50 dark:text-purple-300 mb-2 transition-colors duration-200"
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.4, duration: 0.5 }}
                      >
                        {safeItems[0].tagline}
                      </motion.div>
                    )}
                    
                    <motion.h3 
                      id="tech-glow-five-card-0-title"
                      className="text-2xl font-bold text-foreground dark:text-white mb-2"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.5, duration: 0.5 }}
                    >
                      {safeItems[0].title}
                    </motion.h3>
                    
                    <motion.p 
                      className="text-muted-foreground dark:text-gray-300"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.6, duration: 0.5 }}
                      aria-labelledby="tech-glow-five-card-0-title"
                    >
                      {safeItems[0].description}
                    </motion.p>
                    
                    <motion.div 
                      className="mt-4 h-0.5 w-16 bg-gradient-to-r from-purple-500/80 to-blue-500/80 dark:from-purple-500 dark:to-blue-500 transition-all duration-200"
                      whileHover={{ width: 80 }}
                      transition={{ duration: 0.3 }}
                    />
                  </div>
                  
                  <div className="md:w-1/2">
                    <motion.div
                      className="relative w-full h-64 md:h-full overflow-hidden rounded-xl transform-gpu will-change-transform"
                      whileHover={{ scale: 1.01 }}
                      transition={{ duration: 0.3 }}
                    >
                      <Picture
                        src={safeItems[0].img}
                        alt={safeItems[0].alt || "Feature image"}
                        widths={[375, 480, 640, 768, 1024]}
                        sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 600px"
                        quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                        forceFillHeight={true}
                        className="w-full h-full object-cover"
                        aria-describedby="tech-glow-five-card-0-title"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-card/90 via-card/50 to-transparent dark:from-gray-900 dark:via-gray-900/50 dark:to-transparent transition-colors duration-200"></div>
                      
                      {/* 图片上的光效 */}
                      <motion.div 
                        className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-blue-500/5 opacity-5 dark:from-purple-500/10 dark:to-blue-500/10 dark:opacity-10 transition-opacity duration-300"
                        transition={{ duration: 0.3 }}
                      />
                    </motion.div>
                  </div>
                </div>
              </div>
            </motion.div>
            
            {/* 第二个区块 - 右上大卡片，占据5列3行 */}
            <motion.div 
              className="md:col-span-5 md:col-start-8 md:row-span-3"
              variants={cardVariants}
              custom={1}
              initial="initial"
              animate="animate"
              whileHover="hover"
              whileTap="tap"
              onHoverStart={() => setActiveCard(1)}
              onHoverEnd={() => setActiveCard(null)}
            >
              <div className="relative h-full rounded-2xl bg-gradient-to-br from-card/90 to-background/90 dark:from-gray-900 dark:to-gray-800 p-px overflow-hidden shadow-md dark:shadow-purple-900/10 transition-all duration-300 transform-gpu">
                {/* 霓虹边框 */}
                <div className="absolute inset-0 pointer-events-none">
                  <svg className="w-full h-full">
                    <motion.rect
                      width="100%"
                      height="100%"
                      rx="16" 
                      ry="16" 
                      fill="none"
                      stroke="url(#gradient2)"
                      strokeWidth="1.5"
                      strokeLinecap="round" 
                      strokeLinejoin="round" 
                      variants={neonBorderVariants}
                      initial="initial"
                      animate={activeCard === 1 ? "animate" : "initial"}
                    />
                    <defs>
                      <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stopColor="#3B82F6" />
                        <stop offset="50%" stopColor="#06B6D4" />
                        <stop offset="100%" stopColor="#10B981" />
                      </linearGradient>
                    </defs>
                  </svg>
                </div>
                
                {/* 内部光效 */}
                <motion.div 
                  className="absolute -top-16 -left-16 w-32 h-32 rounded-full bg-blue-500/80 filter blur-lg opacity-15 dark:opacity-20 transition-opacity duration-300 transform-gpu will-change-transform"
                  variants={glowVariants}
                  initial="initial"
                  animate="animate"
                />
                
                <div className="relative h-full rounded-2xl bg-card/80 dark:bg-gray-900/80 backdrop-blur-[2px] p-6 flex flex-col md:flex-row transition-colors duration-300">
                  <div className="md:w-1/2 mb-6 md:mb-0 md:mr-6">
                    <motion.div
                      className="relative w-full h-64 md:h-full overflow-hidden rounded-xl transform-gpu will-change-transform"
                      whileHover={{ scale: 1.02 }}
                      transition={{ duration: 0.3 }}
                    >
                      <Picture
                        src={safeItems[1].img}
                        alt={safeItems[1].alt || "Feature image"}
                        widths={[375, 480, 640, 768, 1024]}
                        sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 600px"
                        quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                        forceFillHeight={true}
                        className="w-full h-full object-cover"
                        aria-describedby="tech-glow-five-card-1-title"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-card/90 via-card/50 to-transparent dark:from-gray-900 dark:via-gray-900/50 dark:to-transparent transition-colors duration-200"></div>
                      
                      {/* 图片上的光效 */}
                      <motion.div 
                        className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-cyan-500/5 opacity-5 dark:from-blue-500/10 dark:to-cyan-500/10 dark:opacity-10 transition-opacity duration-300"
                        transition={{ duration: 0.3 }}
                      />
                    </motion.div>
                  </div>
                  
                  <div className="md:w-1/2" aria-labelledby="tech-glow-five-card-1-title">
                    {safeItems[1].tagline && (
                      <motion.div 
                        className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100/70 text-blue-700 dark:bg-blue-900/50 dark:text-blue-300 mb-2 transition-colors duration-200"
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.5, duration: 0.5 }}
                      >
                        {safeItems[1].tagline}
                      </motion.div>
                    )}
                    
                    <motion.h3 
                      id="tech-glow-five-card-1-title"
                      className="text-2xl font-bold text-foreground dark:text-white mb-2"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.6, duration: 0.5 }}
                    >
                      {safeItems[1].title}
                    </motion.h3>
                    
                    <motion.p 
                      className="text-muted-foreground dark:text-gray-300"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.7, duration: 0.5 }}
                      aria-labelledby="tech-glow-five-card-1-title"
                    >
                      {safeItems[1].description}
                    </motion.p>
                    
                    <motion.div 
                      className="mt-4 h-0.5 w-16 bg-gradient-to-r from-blue-500/80 to-cyan-500/80 dark:from-blue-500 dark:to-cyan-500 transition-all duration-200"
                      whileHover={{ width: 80 }}
                      transition={{ duration: 0.3 }}
                    />
                  </div>
                </div>
              </div>
            </motion.div>
            
            {/* 第三个区块 - 中间小卡片，占据6列3行 */}
            <motion.div 
              className="md:col-span-6 md:col-start-4 md:row-start-4 md:row-span-3"
              variants={cardVariants}
              custom={2}
              initial="initial"
              animate="animate"
              whileHover="hover"
              whileTap="tap"
              onHoverStart={() => setActiveCard(2)}
              onHoverEnd={() => setActiveCard(null)}
            >
              <div className="relative h-full rounded-2xl bg-gradient-to-br from-card/90 to-background/90 dark:from-gray-900 dark:to-gray-800 p-px overflow-hidden shadow-md dark:shadow-purple-900/10 transition-all duration-300 transform-gpu">
                {/* 霓虹边框 */}
                <div className="absolute inset-0 pointer-events-none">
                  <svg className="w-full h-full">
                    <motion.rect
                      width="100%"
                      height="100%"
                      rx="16" 
                      ry="16" 
                      fill="none"
                      stroke="url(#gradient3)"
                      strokeWidth="1.5"
                      strokeLinecap="round" 
                      strokeLinejoin="round" 
                      variants={neonBorderVariants}
                      initial="initial"
                      animate={activeCard === 2 ? "animate" : "initial"}
                    />
                    <defs>
                      <linearGradient id="gradient3" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stopColor="#06B6D4" />
                        <stop offset="50%" stopColor="#10B981" />
                        <stop offset="100%" stopColor="#6366F1" />
                      </linearGradient>
                    </defs>
                  </svg>
                </div>
                
                {/* 内部光效 */}
                <motion.div 
                  className="absolute -bottom-16 -left-16 w-32 h-32 rounded-full bg-cyan-500/80 filter blur-lg opacity-15 dark:opacity-20 transition-opacity duration-300 transform-gpu will-change-transform"
                  variants={glowVariants}
                  initial="initial"
                  animate="animate"
                />
                
                <div className="relative h-full rounded-2xl bg-card/80 dark:bg-gray-900/80 backdrop-blur-[2px] p-6 flex flex-col transition-colors duration-300">
                  <div className="mb-4">
                    <motion.div
                      className="relative w-full h-48 overflow-hidden rounded-xl transform-gpu will-change-transform"
                      whileHover={{ scale: 1.01 }}
                      transition={{ duration: 0.3 }}
                    >
                      <Picture
                        src={safeItems[2].img}
                        alt={safeItems[2].alt || "Feature image"}
                        widths={[375, 480, 640, 768, 1024]}
                        sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 600px"
                        quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                        forceFillHeight={true}
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-card/90 via-card/50 to-transparent dark:from-gray-900 dark:via-gray-900/50 dark:to-transparent transition-colors duration-200"></div>
                      
                      {/* 图片上的光效 */}
                      <motion.div 
                        className="absolute inset-0 bg-gradient-to-br from-cyan-500/5 to-indigo-500/5 opacity-5 dark:from-cyan-500/10 dark:to-indigo-500/10 dark:opacity-10 transition-opacity duration-300"
                        transition={{ duration: 0.3 }}
                      />
                    </motion.div>
                  </div>
                  
                  {safeItems[2].tagline && (
                    <motion.div 
                      className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-cyan-100/70 text-cyan-700 dark:bg-cyan-900/50 dark:text-cyan-300 mb-2 transition-colors duration-200"
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.6, duration: 0.5 }}
                    >
                      {safeItems[2].tagline}
                    </motion.div>
                  )}
                  
                  <motion.h3 
                    className="text-xl font-bold text-foreground dark:text-white mb-2"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.7, duration: 0.5 }}
                  >
                    {safeItems[2].title}
                  </motion.h3>
                  
                  <motion.p 
                    className="text-muted-foreground dark:text-gray-300 text-sm"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.8, duration: 0.5 }}
                  >
                    {safeItems[2].description}
                  </motion.p>
                </div>
              </div>
            </motion.div>
            
            {/* 第四个区块 - 左下小卡片，占据3列3行 */}
            <motion.div 
              className="md:col-span-3 md:row-start-4 md:row-span-3"
              variants={cardVariants}
              custom={3}
              initial="initial"
              animate="animate"
              whileHover="hover"
              whileTap="tap"
              onHoverStart={() => setActiveCard(3)}
              onHoverEnd={() => setActiveCard(null)}
            >
              <div className="relative h-full rounded-2xl bg-gradient-to-br from-card/90 to-background/90 dark:from-gray-900 dark:to-gray-800 p-px overflow-hidden shadow-md dark:shadow-purple-900/10 transition-all duration-300 transform-gpu">
                {/* 霓虹边框 */}
                <div className="absolute inset-0 pointer-events-none">
                  <svg className="w-full h-full">
                    <motion.rect
                      width="100%"
                      height="100%"
                      rx="16" 
                      ry="16" 
                      fill="none"
                      stroke="url(#gradient4)"
                      strokeWidth="1.5"
                      strokeLinecap="round" 
                      strokeLinejoin="round" 
                      variants={neonBorderVariants}
                      initial="initial"
                      animate={activeCard === 3 ? "animate" : "initial"}
                    />
                    <defs>
                      <linearGradient id="gradient4" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stopColor="#6366F1" />
                        <stop offset="50%" stopColor="#9333EA" />
                        <stop offset="100%" stopColor="#EC4899" />
                      </linearGradient>
                    </defs>
                  </svg>
                </div>
                
                {/* 内部光效 */}
                <motion.div 
                  className="absolute -top-16 -right-16 w-32 h-32 rounded-full bg-indigo-500/80 filter blur-lg opacity-15 dark:opacity-20 transition-opacity duration-300 transform-gpu will-change-transform"
                  variants={glowVariants}
                  initial="initial"
                  animate="animate"
                />
                
                <div className="relative h-full rounded-2xl bg-card/80 dark:bg-gray-900/80 backdrop-blur-[2px] p-6 flex flex-col transition-colors duration-300">
                  {safeItems[3].tagline && (
                    <motion.div 
                      className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-indigo-100/70 text-indigo-700 dark:bg-indigo-900/50 dark:text-indigo-300 mb-2 transition-colors duration-200"
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.7, duration: 0.5 }}
                    >
                      {safeItems[3].tagline}
                    </motion.div>
                  )}
                  
                  <motion.h3 
                    className="text-xl font-bold text-foreground dark:text-white mb-2"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.8, duration: 0.5 }}
                  >
                    {safeItems[3].title}
                  </motion.h3>
                  
                  <motion.p 
                    className="text-gray-300 text-sm mb-4"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.9, duration: 0.5 }}
                  >
                    {safeItems[3].description}
                  </motion.p>
                  
                  <div className="mt-auto">
                    <motion.div
                      className="relative w-full h-48 overflow-hidden rounded-xl transform-gpu will-change-transform"
                      whileHover={{ scale: 1.01 }}
                      transition={{ duration: 0.3 }}
                    >
                      <Picture
                        src={safeItems[3].img}
                        alt={safeItems[3].alt || "Feature image"}
                        widths={[375, 480, 640, 768]}
                        sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 400px"
                        quality={{ avif: 60, webp: 65, jpeg: 70, png: 75 }}
                        forceFillHeight={true}
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-card/90 via-card/50 to-transparent dark:from-gray-900 dark:via-gray-900/50 dark:to-transparent transition-colors duration-200"></div>
                      
                      {/* 图片上的光效 */}
                      <motion.div 
                        className="absolute inset-0 bg-gradient-to-br from-indigo-500/5 to-pink-500/5 opacity-5 dark:from-indigo-500/10 dark:to-pink-500/10 dark:opacity-10 transition-opacity duration-300"
                        transition={{ duration: 0.3 }}
                      />
                    </motion.div>
                  </div>
                </div>
              </div>
            </motion.div>
            
            {/* 第五个区块 - 右下小卡片，占据3列3行 */}
            <motion.div 
              className="md:col-span-3 md:col-start-10 md:row-start-4 md:row-span-3"
              variants={cardVariants}
              custom={4}
              initial="initial"
              animate="animate"
              whileHover="hover"
              whileTap="tap"
              onHoverStart={() => setActiveCard(4)}
              onHoverEnd={() => setActiveCard(null)}
            >
              <div className="relative h-full rounded-2xl bg-gradient-to-br from-card/90 to-background/90 dark:from-gray-900 dark:to-gray-800 p-px overflow-hidden shadow-md dark:shadow-purple-900/10 transition-all duration-300 transform-gpu">
                {/* 霓虹边框 */}
                <div className="absolute inset-0 pointer-events-none">
                  <svg className="w-full h-full">
                    <motion.rect
                      width="100%"
                      height="100%"
                      rx="16" 
                      ry="16" 
                      fill="none"
                      stroke="url(#gradient5)"
                      strokeWidth="1.5"
                      strokeLinecap="round" 
                      strokeLinejoin="round" 
                      variants={neonBorderVariants}
                      initial="initial"
                      animate={activeCard === 4 ? "animate" : "initial"}
                    />
                    <defs>
                      <linearGradient id="gradient5" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stopColor="#EC4899" />
                        <stop offset="50%" stopColor="#EF4444" />
                        <stop offset="100%" stopColor="#F59E0B" />
                      </linearGradient>
                    </defs>
                  </svg>
                </div>
                
                {/* 内部光效 */}
                <motion.div 
                  className="absolute -bottom-16 -right-16 w-32 h-32 rounded-full bg-pink-500/80 filter blur-lg opacity-15 dark:opacity-20 transition-opacity duration-300 transform-gpu will-change-transform"
                  variants={glowVariants}
                  initial="initial"
                  animate="animate"
                />
                
                <div className="relative h-full rounded-2xl bg-card/80 dark:bg-gray-900/80 backdrop-blur-[2px] p-6 flex flex-col transition-colors duration-300">
                  <div className="mb-4">
                    <motion.div
                      className="relative w-full h-48 overflow-hidden rounded-xl transform-gpu will-change-transform"
                      whileHover={{ scale: 1.01 }}
                      transition={{ duration: 0.3 }}
                    >
                      <Picture
                        src={safeItems[4].img}
                        alt={safeItems[4].alt || "Feature image"}
                        widths={[375, 480, 640, 768]}
                        sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 400px"
                        quality={{ avif: 60, webp: 65, jpeg: 70, png: 75 }}
                        forceFillHeight={true}
                        className="w-full h-full object-cover"
                        aria-describedby="tech-glow-five-card-4-title"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-card/90 via-card/50 to-transparent dark:from-gray-900 dark:via-gray-900/50 dark:to-transparent transition-colors duration-200"></div>
                      
                      {/* 图片上的光效 */}
                      <motion.div 
                        className="absolute inset-0 bg-gradient-to-br from-pink-500/5 to-amber-500/5 opacity-5 dark:from-pink-500/10 dark:to-amber-500/10 dark:opacity-10 transition-opacity duration-300"
                        transition={{ duration: 0.3 }}
                      />
                    </motion.div>
                  </div>
                  
                  {safeItems[4].tagline && (
                    <motion.div 
                      className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-pink-100/70 text-pink-700 dark:bg-pink-900/50 dark:text-pink-300 mb-2 transition-colors duration-200"
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.8, duration: 0.5 }}
                    >
                      {safeItems[4].tagline}
                    </motion.div>
                  )}
                  
                  <motion.h3 
                    id="tech-glow-five-card-4-title"
                    className="text-xl font-bold text-foreground dark:text-white mb-2"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.9, duration: 0.5 }}
                  >
                    {safeItems[4].title}
                  </motion.h3>
                  
                  <motion.p 
                    className="text-muted-foreground dark:text-gray-300 text-sm"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 1.0, duration: 0.5 }}
                    aria-labelledby="tech-glow-five-card-4-title"
                  >
                    {safeItems[4].description}
                  </motion.p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
}
