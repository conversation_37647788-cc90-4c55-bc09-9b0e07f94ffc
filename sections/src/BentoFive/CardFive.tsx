'use client'

import React from 'react';
import { motion } from 'motion/react';
import { Picture } from '../components/Picture';
import { BentoFiveProps } from './types';

export default function CardFive({ id, tagline, title, items }: BentoFiveProps) {
  // 确保items数组至少有5个元素
  const safeItems = items && items.length >= 5 ? items : Array(5).fill({
    title: 'Feature',
    description: 'Description text here',
    img: 'https://images.unsplash.com/photo-1624571409412-1f253e1ecc89?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&q=80',
    alt: 'Feature image'
  });

  // 为每个位置分配特定的图片尺寸
  const imgSizes = [
    'w=800&h=420', // 第一个位置 - 左上大卡片，高度减少30%
    'w=800&h=420', // 第二个位置 - 右上大卡片，高度减少30%
    'w=400&h=420', // 第三个位置 - 左下小卡片，高度减少30%
    'w=800&h=210', // 第四个位置 - 中下中卡片，高度减少30%
    'w=400&h=420'  // 第五个位置 - 右下小卡片，高度减少30%
  ];

  // 为每个卡片分配主题色
  const cardThemes = [
    {
      bg: 'bg-card dark:bg-indigo-950/80',
      border: 'border-border dark:border-indigo-900',
      tag: 'bg-muted dark:bg-indigo-900 text-primary dark:text-indigo-300',
      accent: 'bg-primary dark:bg-indigo-600'
    },
    {
      bg: 'bg-card dark:bg-emerald-950/80',
      border: 'border-border dark:border-emerald-900',
      tag: 'bg-muted dark:bg-emerald-900 text-primary dark:text-emerald-300',
      accent: 'bg-primary dark:bg-emerald-600'
    },
    {
      bg: 'bg-card dark:bg-amber-950/80',
      border: 'border-border dark:border-amber-900',
      tag: 'bg-muted dark:bg-amber-900 text-primary dark:text-amber-300',
      accent: 'bg-primary dark:bg-amber-600'
    },
    {
      bg: 'bg-card dark:bg-pink-950/80',
      border: 'border-border dark:border-pink-900',
      tag: 'bg-muted dark:bg-pink-900 text-primary dark:text-pink-300',
      accent: 'bg-primary dark:bg-pink-600'
    },
    {
      bg: 'bg-card dark:bg-cyan-950/80',
      border: 'border-border dark:border-cyan-900',
      tag: 'bg-muted dark:bg-cyan-900 text-primary dark:text-cyan-300',
      accent: 'bg-primary dark:bg-cyan-600'
    }
  ];

  // 生成随机装饰元素
  const generateDecorations = (index: number) => {
    // 根据索引选择颜色主题
    const colorThemes = [
      ['#4F46E5', '#818CF8', '#C7D2FE'], // 靛蓝色主题
      ['#10B981', '#34D399', '#A7F3D0'], // 绿色主题
      ['#F59E0B', '#FBBF24', '#FDE68A'], // 琥珀色主题
      ['#EC4899', '#F472B6', '#FBCFE8'], // 粉色主题
      ['#06B6D4', '#22D3EE', '#A5F3FC']  // 青色主题
    ];
    
    const colors = colorThemes[index % colorThemes.length];
    
    // 随机生成1-2个装饰元素
    const count = Math.floor(Math.random() * 2) + 1;
    const decorations = [];
    
    for (let i = 0; i < count; i++) {
      // 随机属性
      const size = Math.floor(Math.random() * 10) + 6; // 6px-16px
      const top = Math.floor(Math.random() * 80) + 10; // 10%-90%
      const left = Math.floor(Math.random() * 80) + 10; // 10%-90%
      const opacity = (Math.random() * 0.15 + 0.05).toFixed(2); // 0.05-0.2
      const rotate = Math.floor(Math.random() * 360); // 0-360度
      const isCircle = Math.random() > 0.3; // 70%概率为圆形
      const hasBlur = Math.random() > 0.7; // 30%概率有模糊效果
      const color = colors[Math.floor(Math.random() * colors.length)];
      
      decorations.push(
        <div
          key={`decoration-${index}-${i}`}
          className="absolute pointer-events-none"
          style={{
            top: `${top}%`,
            left: `${left}%`,
            width: `${size}px`,
            height: `${size}px`,
            backgroundColor: color,
            borderRadius: isCircle ? '50%' : '20%',
            opacity: opacity,
            transform: `rotate(${rotate}deg)`,
            filter: hasBlur ? 'blur(2px)' : 'none',
            zIndex: 5
          }}
        />
      );
    }
    
    return decorations;
  };

  return (
    <section id={id} className="bg-background dark:bg-gray-950 py-section-y" aria-labelledby="card-five-title">
      <div className="mx-auto max-w-container px-container-x">
        <div className="mx-auto max-w-content text-center">
          {tagline && (
            <motion.p 
              className="text-body-base/body-base font-semibold text-primary dark:text-primary"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              {tagline}
            </motion.p>
          )}
          {title && (
            <motion.h2 
              id="card-five-title"
              className="mt-element-y text-heading-2/heading-2 font-bold tracking-tight text-foreground dark:text-gray-100"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              {title}
            </motion.h2>
          )}
        </div>
        
        <div className="mx-auto mt-content-y max-w-container">
          {/* 使用CSS Grid创建拼接效果 - 确保五个卡片形成一个完整的大矩形 */}
          <div className="grid grid-cols-1 md:grid-cols-4 md:grid-rows-4 gap-element-y md:gap-element-y overflow-hidden rounded-xl md:rounded-3xl shadow-xl bg-background dark:bg-gray-900">
            {/* 左上大卡片 - 占据2列2行 */}
            <motion.div 
              className={`group relative md:col-span-2 md:row-span-2 overflow-hidden ${cardThemes[0].bg} rounded-lg md:rounded-tl-3xl md:rounded-tr-lg md:rounded-bl-lg md:rounded-br-lg`}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
            >
              {generateDecorations(0)}
              <div className="absolute inset-0 bg-gradient-to-b from-white/30 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
              <div className="relative flex flex-col h-full">
                <div className="p-4 md:p-6">
                  {safeItems[0].tagline && (
                    <p className={`inline-block rounded-full px-3 py-1 text-sm font-medium ${cardThemes[0].tag}`}>{safeItems[0].tagline}</p>
                  )}
                  <h3 id="card-five-card-0-title" className="mt-element-y text-heading-3/heading-3 font-bold tracking-tight text-foreground dark:text-gray-100">
                    {safeItems[0].title}
                  </h3>
                  <p className="mt-element-y text-body-base/body-base text-muted-foreground dark:text-gray-300 line-clamp-3 md:line-clamp-none" aria-labelledby="card-five-card-0-title">
                    {safeItems[0].description}
                  </p>
                </div>
                <div className="relative flex-grow overflow-hidden" style={{ minHeight: "180px", maxHeight: "calc(100% - 150px)" }}>
                  <Picture
                    src={safeItems[0].img}
                    alt={safeItems[0].alt || "Feature image"}
                    widths={[375, 480, 640, 768, 1024]}
                    sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 400px"
                    quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                    forceFillHeight={true}
                    className="h-full w-full object-cover transition-transform duration-700 group-hover:scale-105"
                    aria-describedby="card-five-card-0-title"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
                </div>
              </div>
              <div className={`absolute bottom-0 left-0 right-0 h-1 ${cardThemes[0].accent} origin-left scale-x-0 transform transition-transform duration-300 group-hover:scale-x-100`}></div>
            </motion.div>
            
            {/* 右上大卡片 - 占据2列2行 */}
            <motion.div 
              className={`group relative md:col-span-2 md:row-span-2 overflow-hidden ${cardThemes[1].bg} rounded-lg md:rounded-tr-3xl md:rounded-tl-lg md:rounded-bl-lg md:rounded-br-lg`}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              {generateDecorations(1)}
              <div className="absolute inset-0 bg-gradient-to-b from-white/30 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
              <div className="relative flex flex-col h-full">
                <div className="p-4 md:p-6">
                  {safeItems[1].tagline && (
                    <p className={`inline-block rounded-full px-3 py-1 text-sm font-medium ${cardThemes[1].tag}`}>{safeItems[1].tagline}</p>
                  )}
                  <h3 id="card-five-card-1-title" className="mt-element-y text-heading-3/heading-3 font-bold tracking-tight text-foreground dark:text-gray-100">
                    {safeItems[1].title}
                  </h3>
                  <p className="mt-element-y text-body-base/body-base text-muted-foreground dark:text-gray-300 line-clamp-3 md:line-clamp-none" aria-labelledby="card-five-card-1-title">
                    {safeItems[1].description}
                  </p>
                </div>
                <div className="relative flex-grow overflow-hidden" style={{ minHeight: "180px", maxHeight: "calc(100% - 150px)" }}>
                  <Picture
                    src={safeItems[1].img}
                    alt={safeItems[1].alt || "Feature image"}
                    widths={[375, 480, 640, 768, 1024]}
                    sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 400px"
                    quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                    forceFillHeight={true}
                    className="h-full w-full object-cover transition-transform duration-700 group-hover:scale-105"
                    aria-describedby="card-five-card-1-title"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
                </div>
              </div>
              <div className={`absolute bottom-0 left-0 right-0 h-1 ${cardThemes[1].accent} origin-left scale-x-0 transform transition-transform duration-300 group-hover:scale-x-100`}></div>
            </motion.div>
            
            {/* 左下小卡片 - 占据1列2行 */}
            <motion.div 
              className={`group relative md:col-span-1 md:row-span-2 overflow-hidden ${cardThemes[2].bg} rounded-lg md:rounded-bl-3xl md:rounded-br-lg md:rounded-tl-lg md:rounded-tr-lg`}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              {generateDecorations(2)}
              <div className="absolute inset-0 bg-gradient-to-b from-white/30 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
              <div className="relative flex flex-col h-full">
                <div className="p-4">
                  {safeItems[2].tagline && (
                    <p className={`inline-block rounded-full px-3 py-1 text-xs font-medium ${cardThemes[2].tag}`}>{safeItems[2].tagline}</p>
                  )}
                  <h3 id="card-five-card-2-title" className="mt-element-y text-heading-4/heading-4 font-semibold tracking-tight text-foreground dark:text-gray-100">
                    {safeItems[2].title}
                  </h3>
                  <p className="mt-element-y text-body-small/body-small text-muted-foreground dark:text-gray-300 line-clamp-2" aria-labelledby="card-five-card-2-title">
                    {safeItems[2].description}
                  </p>
                </div>
                <div className="relative flex-grow overflow-hidden" style={{ minHeight: "120px", maxHeight: "calc(100% - 120px)" }}>
                  <Picture
                    src={safeItems[2].img}
                    alt={safeItems[2].alt || "Feature image"}
                    widths={[375, 480, 640, 768]}
                    sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 300px"
                    quality={{ avif: 60, webp: 65, jpeg: 70, png: 75 }}
                    forceFillHeight={true}
                    className="h-full w-full object-cover transition-transform duration-700 group-hover:scale-105"
                    aria-describedby="card-five-card-2-title"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
                </div>
              </div>
              <div className={`absolute bottom-0 left-0 right-0 h-1 ${cardThemes[2].accent} origin-left scale-x-0 transform transition-transform duration-300 group-hover:scale-x-100`}></div>
            </motion.div>
            
            {/* 中下中卡片 - 占据2列2行 */}
            <motion.div 
              className={`group relative md:col-span-2 md:row-span-2 overflow-hidden ${cardThemes[3].bg} rounded-lg`}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              {generateDecorations(3)}
              <div className="absolute inset-0 bg-gradient-to-b from-white/30 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
              <div className="relative flex flex-col h-full">
                <div className="p-4 md:p-5">
                  {safeItems[3].tagline && (
                    <p className={`inline-block rounded-full px-3 py-1 text-sm font-medium ${cardThemes[3].tag}`}>{safeItems[3].tagline}</p>
                  )}
                  <h3 id="card-five-card-3-title" className="mt-element-y text-heading-4/heading-4 font-semibold tracking-tight text-foreground dark:text-gray-100">
                    {safeItems[3].title}
                  </h3>
                  <p className="mt-element-y text-body-small/body-small text-muted-foreground dark:text-gray-300 line-clamp-3 md:line-clamp-none" aria-labelledby="card-five-card-3-title">
                    {safeItems[3].description}
                  </p>
                </div>
                <div className="relative flex-grow overflow-hidden" style={{ minHeight: "120px", maxHeight: "calc(100% - 130px)" }}>
                  <Picture
                    src={safeItems[3].img}
                    alt={safeItems[3].alt || "Feature image"}
                    widths={[375, 480, 640, 768, 1024]}
                    sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 600px"
                    quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                    forceFillHeight={true}
                    className="h-full w-full object-cover transition-transform duration-700 group-hover:scale-105"
                    aria-describedby="card-five-card-3-title"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
                </div>
              </div>
              <div className={`absolute bottom-0 left-0 right-0 h-1 ${cardThemes[3].accent} origin-left scale-x-0 transform transition-transform duration-300 group-hover:scale-x-100`}></div>
            </motion.div>
            
            {/* 右下小卡片 - 占据1列2行 */}
            <motion.div 
              className={`group relative md:col-span-1 md:row-span-2 overflow-hidden ${cardThemes[4].bg} rounded-lg md:rounded-br-3xl md:rounded-bl-lg md:rounded-tl-lg md:rounded-tr-lg`}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              {generateDecorations(4)}
              <div className="absolute inset-0 bg-gradient-to-b from-white/30 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
              <div className="relative flex flex-col h-full">
                <div className="p-4">
                  {safeItems[4].tagline && (
                    <p className={`inline-block rounded-full px-3 py-1 text-xs font-medium ${cardThemes[4].tag}`}>{safeItems[4].tagline}</p>
                  )}
                  <h3 id="card-five-card-4-title" className="mt-element-y text-heading-4/heading-4 font-semibold tracking-tight text-foreground dark:text-gray-100">
                    {safeItems[4].title}
                  </h3>
                  <p className="mt-element-y text-body-small/body-small text-muted-foreground dark:text-gray-300 line-clamp-2" aria-labelledby="card-five-card-4-title">
                    {safeItems[4].description}
                  </p>
                </div>
                <div className="relative flex-grow overflow-hidden" style={{ minHeight: "120px", maxHeight: "calc(100% - 120px)" }}>
                  <Picture
                    src={safeItems[4].img}
                    alt={safeItems[4].alt || "Feature image"}
                    widths={[375, 480, 640, 768]}
                    sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 300px"
                    quality={{ avif: 60, webp: 65, jpeg: 70, png: 75 }}
                    forceFillHeight={true}
                    className="h-full w-full object-cover transition-transform duration-700 group-hover:scale-105"
                    aria-describedby="card-five-card-4-title"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
                </div>
              </div>
              <div className={`absolute bottom-0 left-0 right-0 h-1 ${cardThemes[4].accent} origin-left scale-x-0 transform transition-transform duration-300 group-hover:scale-x-100`}></div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
}
