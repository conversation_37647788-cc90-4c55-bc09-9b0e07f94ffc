'use client'

import React from 'react';
import { motion } from 'motion/react';
import { Picture } from '../components/Picture';
import { BentoFiveProps } from './types';

export default function EnterpriseFive({ id, tagline, title, items }: BentoFiveProps) {
  // 确保items数组至少有5个元素
  const safeItems = items && items.length >= 5 ? items : Array(5).fill({
    title: 'Feature',
    description: 'Description text here',
    img: 'https://images.unsplash.com/photo-1624571409412-1f253e1ecc89?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&q=80',
    alt: 'Feature image'
  });

  // 为每个位置分配特定的图片尺寸
  const imgSizes = [
    'w=800&h=420', // 第一个位置 - 左上大区块
    'w=800&h=420', // 第二个位置 - 右上大区块
    'w=400&h=280', // 第三个位置 - 左下小区块
    'w=400&h=280', // 第四个位置 - 中下小区块
    'w=400&h=280'  // 第五个位置 - 右下小区块
  ];

  // 背景色设置 - 浅色系
  const bgColors = [
    'bg-card border border-blue-200 dark:border-blue-800 dark:bg-blue-900/30',
    'bg-card border border-indigo-200 dark:border-indigo-800 dark:bg-indigo-900/30',
    'bg-card border border-purple-200 dark:border-purple-800 dark:bg-purple-900/30',
    'bg-card border border-emerald-200 dark:border-emerald-800 dark:bg-emerald-900/30',
    'bg-card border border-sky-200 dark:border-sky-800 dark:bg-sky-900/30'
  ];

  // 文字颜色设置
  const textColors = [
    'text-blue-700 dark:text-blue-400',
    'text-indigo-700 dark:text-indigo-400',
    'text-purple-700 dark:text-purple-400',
    'text-emerald-700 dark:text-emerald-400',
    'text-sky-700 dark:text-sky-400'
  ];

  return (
    <section id={id} className="bg-background dark:bg-gray-950 py-section-y" aria-labelledby="enterprise-five-title">
      <div className="mx-auto max-w-container px-container-x">
        <div className="mx-auto max-w-content text-center">
          {tagline && (
            <motion.p 
              className="text-body-base/body-base font-semibold text-primary dark:text-blue-400"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              {tagline}
            </motion.p>
          )}
          {title && (
            <motion.h2 
              id="enterprise-five-title"
              className="mt-element-y max-w-content mx-auto text-pretty text-center text-heading-2/heading-2 font-semibold tracking-tight text-foreground dark:text-gray-100"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              {title}
            </motion.h2>
          )}
        </div>
        
        <div className="mx-auto mt-content-y grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-6 lg:grid-rows-2 gap-element-y">
          {/* 第一个区块 - 左上大区块 (占3列1行) */}
          <motion.div 
            className={`group relative overflow-hidden lg:col-span-3 lg:row-span-1 ${bgColors[0]} rounded-2xl shadow-md`}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="p-4 h-full flex flex-col md:flex-row items-center">
              <div className="md:w-1/2 mb-3 md:mb-0 md:pr-4">
                {safeItems[0].tagline && (
                  <p className={`text-sm font-medium ${textColors[0]}`}>{safeItems[0].tagline}</p>
                )}
                <h3 id="enterprise-five-card-0-title" className="mt-element-y text-heading-3/heading-3 font-semibold text-foreground dark:text-gray-100">{safeItems[0].title}</h3>
                <p className="mt-element-y text-body-small/body-small text-muted-foreground dark:text-gray-300" aria-labelledby="enterprise-five-card-0-title">{safeItems[0].description}</p>
              </div>
              <div className="md:w-1/2 h-full flex items-center">
                <div className="w-full h-auto rounded-lg shadow-sm overflow-hidden">
                  <Picture
                    src={safeItems[0].img}
                    alt={safeItems[0].alt || "Feature image"}
                    widths={[375, 480, 640, 768, 1024]}
                    sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 400px"
                    quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                    className="w-full h-auto object-cover group-hover:scale-[1.02] transition-transform duration-300"
                    aria-describedby="enterprise-five-card-0-title"
                  />
                </div>
              </div>
            </div>
          </motion.div>

          {/* 第二个区块 - 右上大区块 (占3列1行) */}
          <motion.div 
            className={`group relative overflow-hidden lg:col-span-3 lg:row-span-1 ${bgColors[1]} rounded-2xl shadow-md`}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <div className="p-4 h-full flex flex-col md:flex-row items-center">
              <div className="md:w-1/2 mb-3 md:mb-0 md:pr-4">
                {safeItems[1].tagline && (
                  <p className={`text-sm font-medium ${textColors[1]}`}>{safeItems[1].tagline}</p>
                )}
                <h3 id="enterprise-five-card-1-title" className="mt-element-y text-heading-3/heading-3 font-semibold text-foreground dark:text-gray-100">{safeItems[1].title}</h3>
                <p className="mt-element-y text-body-small/body-small text-muted-foreground dark:text-gray-300" aria-labelledby="enterprise-five-card-1-title">{safeItems[1].description}</p>
              </div>
              <div className="md:w-1/2 h-full flex items-center">
                <div className="w-full h-auto rounded-lg shadow-sm overflow-hidden">
                  <Picture
                    src={safeItems[1].img}
                    alt={safeItems[1].alt || "Feature image"}
                    widths={[375, 480, 640, 768, 1024]}
                    sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 400px"
                    quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                    className="w-full h-auto object-cover group-hover:scale-[1.02] transition-transform duration-300"
                    aria-describedby="enterprise-five-card-1-title"
                  />
                </div>
              </div>
            </div>
          </motion.div>

          {/* 第三个区块 - 左下小区块 (占2列1行) */}
          <motion.div 
            className={`group relative overflow-hidden lg:col-span-2 lg:row-span-1 ${bgColors[2]} rounded-2xl shadow-md`}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <div className="p-4 h-full flex flex-col">
              {safeItems[2].tagline && (
                <p className={`text-sm font-medium ${textColors[2]}`}>{safeItems[2].tagline}</p>
              )}
              <h3 id="enterprise-five-card-2-title" className="mt-element-y text-heading-4/heading-4 font-semibold text-foreground dark:text-gray-100">{safeItems[2].title}</h3>
              <p className="mt-element-y text-body-small/body-small text-muted-foreground dark:text-gray-300" aria-labelledby="enterprise-five-card-2-title">{safeItems[2].description}</p>
              <div className="mt-3 flex-grow flex items-end">
                <div className="w-full h-28 rounded-lg shadow-sm overflow-hidden">
                  <Picture
                    src={safeItems[2].img}
                    alt={safeItems[2].alt || "Feature image"}
                    widths={[375, 480, 640, 768]}
                    sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 300px"
                    quality={{ avif: 60, webp: 65, jpeg: 70, png: 75 }}
                    forceFillHeight={true}
                    className="w-full h-full object-cover group-hover:scale-[1.02] transition-transform duration-300"
                    aria-describedby="enterprise-five-card-2-title"
                  />
                </div>
              </div>
            </div>
          </motion.div>

          {/* 第四个区块 - 中下小区块 (占2列1行) */}
          <motion.div 
            className={`group relative overflow-hidden lg:col-span-2 lg:row-span-1 ${bgColors[3]} rounded-2xl shadow-md`}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <div className="p-4 h-full flex flex-col">
              {safeItems[3].tagline && (
                <p className={`text-sm font-medium ${textColors[3]}`}>{safeItems[3].tagline}</p>
              )}
              <h3 id="enterprise-five-card-3-title" className="mt-element-y text-heading-4/heading-4 font-semibold text-foreground dark:text-gray-100">{safeItems[3].title}</h3>
              <p className="mt-element-y text-body-small/body-small text-muted-foreground dark:text-gray-300" aria-labelledby="enterprise-five-card-3-title">{safeItems[3].description}</p>
              <div className="mt-3 flex-grow flex items-end">
                <div className="w-full h-28 rounded-lg shadow-sm overflow-hidden">
                  <Picture
                    src={safeItems[3].img}
                    alt={safeItems[3].alt || "Feature image"}
                    widths={[375, 480, 640, 768]}
                    sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 300px"
                    quality={{ avif: 60, webp: 65, jpeg: 70, png: 75 }}
                    forceFillHeight={true}
                    className="w-full h-full object-cover group-hover:scale-[1.02] transition-transform duration-300"
                    aria-describedby="enterprise-five-card-3-title"
                  />
                </div>
              </div>
            </div>
          </motion.div>

          {/* 第五个区块 - 右下小区块 (占2列1行) */}
          <motion.div 
            className={`group relative overflow-hidden lg:col-span-2 lg:row-span-1 ${bgColors[4]} rounded-2xl shadow-md`}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <div className="p-4 h-full flex flex-col">
              {safeItems[4].tagline && (
                <p className={`text-sm font-medium ${textColors[4]}`}>{safeItems[4].tagline}</p>
              )}
              <h3 id="enterprise-five-card-4-title" className="mt-element-y text-heading-4/heading-4 font-semibold text-foreground dark:text-gray-100">{safeItems[4].title}</h3>
              <p className="mt-element-y text-body-small/body-small text-muted-foreground dark:text-gray-300" aria-labelledby="enterprise-five-card-4-title">{safeItems[4].description}</p>
              <div className="mt-3 flex-grow flex items-end">
                <div className="w-full h-28 rounded-lg shadow-sm overflow-hidden">
                  <Picture
                    src={safeItems[4].img}
                    alt={safeItems[4].alt || "Feature image"}
                    widths={[375, 480, 640, 768]}
                    sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 300px"
                    quality={{ avif: 60, webp: 65, jpeg: 70, png: 75 }}
                    forceFillHeight={true}
                    className="w-full h-full object-cover group-hover:scale-[1.02] transition-transform duration-300"
                    aria-describedby="enterprise-five-card-4-title"
                  />
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
