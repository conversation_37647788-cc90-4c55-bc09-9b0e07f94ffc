'use client'

import React from 'react';
import { Picture } from '../components/Picture';
import { BentoFiveProps } from './types';

export default function ThreeRow5({ tagline, title, items }: BentoFiveProps) {
  // 确保items数组至少有5个元素
  const safeItems = items && items.length >= 5 ? items : Array(5).fill({
    title: 'Feature',
    description: 'Description text here',
    img: 'https://images.unsplash.com/photo-1624571409412-1f253e1ecc89?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&q=80',
    alt: 'Feature image'
  });

  // 为每个位置分配特定的图片尺寸
  const imgSizes = [
    'w=1000&h=550', // 第一个位置 - 左上中图
    'w=800&h=550',  // 第二个位置 - 右上中图
    'w=600&h=450',  // 第三个位置 - 中间小图
    'w=600&h=450',  // 第四个位置 - 左下小图
    'w=800&h=550'   // 第五个位置 - 右下中图
  ];

  return (
    <section className="bg-background dark:bg-gray-900 py-section-y" aria-labelledby="three-row5-title">
      <div className="mx-auto max-w-container px-container-x">
        {tagline && <p className="text-body-base/body-base font-semibold text-primary dark:text-primary">{tagline}</p>}
        {title && (
          <h2 id="three-row5-title" className="mt-element-y max-w-content text-pretty text-heading-2/heading-2 font-semibold tracking-tight text-foreground dark:text-foreground">
            {title}
          </h2>
        )}
        
        <div className="mt-content-y grid grid-cols-1 gap-element-y sm:mt-section-y lg:grid-cols-12 lg:grid-rows-6">
          {/* 第一个卡片 - 左上中卡片，占据左侧 6 列，3行高度 */}
          <div className="relative lg:col-span-6 lg:row-span-3">
            <div className="absolute inset-px rounded-lg bg-card dark:bg-gray-800 max-lg:rounded-t-[2rem] lg:rounded-tl-[2rem]" />
            <div className="relative flex h-full flex-col overflow-hidden rounded-[calc(theme(borderRadius.lg)+1px)] max-lg:rounded-t-[calc(2rem+1px)] lg:rounded-tl-[calc(2rem+1px)]">
              <div className="h-72 overflow-hidden">
                <Picture
                  src={safeItems[0].img}
                  alt={safeItems[0].alt || "Feature image"}
                  widths={[375, 480, 640, 768, 1024, 1200]}
                  sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 600px"
                  quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                  forceFillHeight={true}
                  className="w-full h-full object-cover"
                  aria-describedby="three-row5-card-0-title"
                />
              </div>
              <div className="p-6 pt-5 flex-grow" aria-labelledby="three-row5-card-0-title">
                {safeItems[0].tagline && <p className="text-body-small/body-small font-semibold text-primary dark:text-primary">{safeItems[0].tagline}</p>}
                <h3 id="three-row5-card-0-title" className="mt-element-y text-heading-4/heading-4 font-medium tracking-tight text-foreground dark:text-foreground">{safeItems[0].title}</h3>
                <p className="mt-element-y max-w-content text-body-small/body-small text-muted-foreground dark:text-muted-foreground" aria-labelledby="three-row5-card-0-title">
                  {safeItems[0].description}
                </p>
              </div>
            </div>
            <div className="pointer-events-none absolute inset-px rounded-lg shadow ring-1 ring-border dark:ring-border max-lg:rounded-t-[2rem] lg:rounded-tl-[2rem]" />
          </div>
          
          {/* 第二个卡片 - 右上中卡片，占据右侧 6 列，3行高度 */}
          <div className="relative lg:col-span-6 lg:row-span-3">
            <div className="absolute inset-px rounded-lg bg-card dark:bg-gray-800 lg:rounded-tr-[2rem]" />
            <div className="relative flex h-full flex-col overflow-hidden rounded-[calc(theme(borderRadius.lg)+1px)] lg:rounded-tr-[calc(2rem+1px)]">
              <div className="p-6 pt-5" aria-labelledby="three-row5-card-1-title">
                {safeItems[1].tagline && <p className="text-body-small/body-small font-semibold text-primary dark:text-primary">{safeItems[1].tagline}</p>}
                <h3 id="three-row5-card-1-title" className="mt-element-y text-heading-4/heading-4 font-medium tracking-tight text-foreground dark:text-foreground">{safeItems[1].title}</h3>
                <p className="mt-element-y max-w-content text-body-small/body-small text-muted-foreground dark:text-muted-foreground" aria-labelledby="three-row5-card-1-title">
                  {safeItems[1].description}
                </p>
              </div>
              <div className="mt-auto h-72 overflow-hidden">
                <Picture
                  src={safeItems[1].img}
                  alt={safeItems[1].alt || "Feature image"}
                  widths={[375, 480, 640, 768, 1024, 1200]}
                  sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 600px"
                  quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                  forceFillHeight={true}
                  className="w-full h-full object-cover"
                  aria-describedby="three-row5-card-1-title"
                />
              </div>
            </div>
            <div className="pointer-events-none absolute inset-px rounded-lg shadow ring-1 ring-border dark:ring-border lg:rounded-tr-[2rem]" />
          </div>
          
          {/* 第四个卡片 - 左下小卡片，占据左下 4 列，3行高度 */}
          <div className="relative lg:col-span-4 lg:row-span-3">
            <div className="absolute inset-px rounded-lg bg-card dark:bg-gray-800 lg:rounded-bl-[2rem]" />
            <div className="relative flex h-full flex-col overflow-hidden rounded-[calc(theme(borderRadius.lg)+1px)] lg:rounded-bl-[calc(2rem+1px)]">
              <div className="p-5 pt-4" aria-labelledby="three-row5-card-3-title">
                {safeItems[3].tagline && <p className="text-body-small/body-small font-semibold text-primary dark:text-primary">{safeItems[3].tagline}</p>}
                <h3 id="three-row5-card-3-title" className="mt-element-y text-heading-4/heading-4 font-medium tracking-tight text-foreground dark:text-foreground">{safeItems[3].title}</h3>
                <p className="mt-element-y max-w-content text-body-small/body-small text-muted-foreground dark:text-muted-foreground" aria-labelledby="three-row5-card-3-title">
                  {safeItems[3].description}
                </p>
              </div>
              <div className="mt-auto h-64 overflow-hidden">
                <Picture
                  src={safeItems[3].img}
                  alt={safeItems[3].alt || "Feature image"}
                  widths={[375, 480, 640, 768, 1024]}
                  sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 400px"
                  quality={{ avif: 60, webp: 65, jpeg: 70, png: 75 }}
                  forceFillHeight={true}
                  className="w-full h-full object-cover"
                  aria-describedby="three-row5-card-3-title"
                />
              </div>
            </div>
            <div className="pointer-events-none absolute inset-px rounded-lg shadow ring-1 ring-border dark:ring-border lg:rounded-bl-[2rem]" />
          </div>
          
          {/* 第三个卡片 - 中间小卡片，占据中间 4 列，3行高度 */}
          <div className="relative lg:col-span-4 lg:row-span-3">
            <div className="absolute inset-px rounded-lg bg-card dark:bg-gray-800" />
            <div className="relative flex h-full flex-col overflow-hidden rounded-[calc(theme(borderRadius.lg)+1px)]">
              <div className="h-64 overflow-hidden">
                <Picture
                  src={safeItems[2].img}
                  alt={safeItems[2].alt || "Feature image"}
                  widths={[375, 480, 640, 768, 1024]}
                  sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 400px"
                  quality={{ avif: 60, webp: 65, jpeg: 70, png: 75 }}
                  forceFillHeight={true}
                  className="w-full h-full object-cover"
                  aria-describedby="three-row5-card-2-title"
                />
              </div>
              <div className="p-5 pt-4 flex-grow" aria-labelledby="three-row5-card-2-title">
                {safeItems[2].tagline && <p className="text-body-small/body-small font-semibold text-primary dark:text-primary">{safeItems[2].tagline}</p>}
                <h3 id="three-row5-card-2-title" className="mt-element-y text-heading-4/heading-4 font-medium tracking-tight text-foreground dark:text-foreground">{safeItems[2].title}</h3>
                <p className="mt-element-y max-w-content text-body-small/body-small text-muted-foreground dark:text-muted-foreground" aria-labelledby="three-row5-card-2-title">
                  {safeItems[2].description}
                </p>
              </div>
            </div>
            <div className="pointer-events-none absolute inset-px rounded-lg shadow ring-1 ring-border dark:ring-border" />
          </div>
          
          {/* 第五个卡片 - 右下小卡片，占据右下 4 列，3行高度 */}
          <div className="relative lg:col-span-4 lg:row-span-3">
            <div className="absolute inset-px rounded-lg bg-card dark:bg-gray-800 max-lg:rounded-b-[2rem] lg:rounded-br-[2rem]" />
            <div className="relative flex h-full flex-col overflow-hidden rounded-[calc(theme(borderRadius.lg)+1px)] max-lg:rounded-b-[calc(2rem+1px)] lg:rounded-br-[calc(2rem+1px)]">
              <div className="flex flex-col h-full">
                <div className="p-5 pt-4" aria-labelledby="three-row5-card-4-title">
                  {safeItems[4].tagline && <p className="text-body-small/body-small font-semibold text-primary dark:text-primary">{safeItems[4].tagline}</p>}
                  <h3 id="three-row5-card-4-title" className="mt-element-y text-heading-4/heading-4 font-medium tracking-tight text-foreground dark:text-foreground">{safeItems[4].title}</h3>
                  <p className="mt-element-y max-w-content text-body-small/body-small text-muted-foreground dark:text-muted-foreground" aria-labelledby="three-row5-card-4-title">
                    {safeItems[4].description}
                  </p>
                </div>
                <div className="mt-auto h-64 overflow-hidden">
                  <Picture
                    src={safeItems[4].img}
                    alt={safeItems[4].alt || "Feature image"}
                    widths={[375, 480, 640, 768, 1024]}
                    sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 400px"
                    quality={{ avif: 60, webp: 65, jpeg: 70, png: 75 }}
                    forceFillHeight={true}
                    className="w-full h-full object-cover"
                    aria-describedby="three-row5-card-4-title"
                  />
                </div>
              </div>
            </div>
            <div className="pointer-events-none absolute inset-px rounded-lg shadow ring-1 ring-border dark:ring-border max-lg:rounded-b-[2rem] lg:rounded-br-[2rem]" />
          </div>
        </div>
      </div>
    </section>
  );
}
