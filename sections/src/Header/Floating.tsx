import React, { useState, useEffect } from 'react';
import './Floating.css';
import { HeaderSectionProps, Language } from './types';
import { useHeader } from './useHeader';
import MobileMenu from './MobileMenu';
import NavItem from './components/NavItem';
import ActionItem from './components/ActionItem';
import ThemeToggle from './components/ThemeToggle';
import LanguageToggle from './components/LanguageToggle';

// 默认语言列表
const defaultLanguages = [
  { code: 'en', name: 'English', flag: '' },
  { code: 'zh', name: 'Chinese', flag: '' }
];

const Floating: React.FC<HeaderSectionProps> = ({ 
    logo = 'Brand', 
    links = [], 
    actions = [],
    languages = defaultLanguages,
    currentLanguage = 'en',
    onLanguageChange,
    languageInfo,
    languageUrls,
}) => {
    // 使用 useState 和 useEffect 确保客户端渲染
    const [mounted, setMounted] = useState(false);

    // 在客户端挂载后渲染
    useEffect(() => {
        setMounted(true);
    }, []);

    const {
        isMenuOpen,
        toggleMenu,
        closeMenu,
        menuRef,
        theme,
        toggleTheme,
        setTheme,
        handleLanguageChange
    } = useHeader({ onLanguageChange });

    // 获取当前语言对象
    const currentLang = languages.find(lang => lang.code === currentLanguage) || {
        code: 'en',
        name: 'English'
    };

    // Render brand from props
    const displayLogoNode = React.isValidElement(logo)
      ? logo
      : typeof logo === 'object' && logo !== null && 'url' in logo
      ? <img src={logo.url} alt={logo.alt} className="h-5 md:h-6 w-auto" />
      : null;
    const displayLogoText =
      typeof logo === 'object' && logo !== null && 'alt' in logo
        ? logo.alt
        : typeof logo === 'string'
        ? logo
        : 'Brand';

    return (
        <header className="floating-header sticky top-0 sm:top-4 inset-x-0 before:absolute before:inset-0 before:max-w-container before:mx-0 sm:before:mx-2 before:lg:mx-auto before:rounded-none sm:before:rounded-[26px] before:border-0 sm:before:border before:border-border after:absolute after:inset-0 after:-z-[1] after:max-w-container after:mx-0 sm:after:mx-2 after:lg:mx-auto after:rounded-none sm:after:rounded-[26px] after:bg-background flex flex-wrap md:justify-start md:flex-nowrap z-50 w-full">
            <nav className="relative max-w-container w-full md:flex md:items-center md:justify-between md:gap-3 ps-container-x pe-2 mx-2 lg:mx-auto py-2">
                {/* Logo w/ Collapse Button */}
                <div className="flex items-center justify-between md:block">
                    <a className="flex-none flex items-center gap-2" href="/" aria-label="Brand">
                        {displayLogoNode}
                        <span className="font-medium text-body-base/body-base text-foreground ml-2">{displayLogoText}</span>
                    </a>

                    {/* Collapse Button */}
                    <div className="md:hidden">
                        <button 
                            type="button" 
                            className="relative size-10 flex justify-center items-center text-sm font-semibold rounded-full border border-border text-foreground hover:bg-accent active:bg-accent/80 focus:outline-none focus:ring-2 focus:ring-ring disabled:opacity-50 disabled:pointer-events-none" 
                            aria-expanded={isMenuOpen}
                            aria-label="Toggle navigation"
                            onClick={toggleMenu}
                        >
                            {isMenuOpen ? (
                                <svg className="size-4 md:size-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M18 6 6 18" /><path d="m6 6 12 12" /></svg>
                            ) : (
                                <svg className="size-4 md:size-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><line x1="3" x2="21" y1="6" y2="6" /><line x1="3" x2="21" y1="12" y2="12" /><line x1="3" x2="21" y1="18" y2="18" /></svg>
                            )}
                            <span className="sr-only">Toggle navigation</span>
                        </button>
                    </div>
                    {/* End Collapse Button */}
                </div>
                {/* End Logo w/ Collapse Button */}

                {/* 桌面端左侧静态元素 - 只在桌面版显示 */}
                <div className="hidden md:flex md:items-center md:mr-6">
                    {/* 已经在顶部添加了 logo，这里不再需要 */}
                </div>
                {/* End 桌面端左侧静态元素 */}

                {/* Mobile menu (shown only on mobile) */}
                <div className="md:hidden">
                  <MobileMenu
                    isOpen={isMenuOpen}
                    onClose={closeMenu}
                    menuRef={menuRef}
                    links={links}
                    actions={actions}
                    languages={languages}
                    currentLanguage={currentLanguage}
                    currentLang={currentLang}
                    theme={theme}
                    toggleTheme={toggleTheme}
                    setTheme={setTheme}
                    onLanguageChange={handleLanguageChange}
                    languageInfo={languageInfo}
                    languageUrls={languageUrls}
                  />
                </div>

                {/* Desktop menu (shown only on md+) - 改为始终渲染，使用 CSS 控制显示/隐藏 */}
                <div className="hidden md:flex md:w-full md:justify-between md:items-center">
                  <ul className="flex items-center gap-8" role="menubar">
                    {links.map(l => (
                      <li key={l.slug} role="none">
                        <NavItem
                          link={l}
                          className="text-foreground hover:text-foreground/80 focus:outline-none focus:ring-2 focus:ring-ring px-3 py-2 rounded-md text-body-small/body-small md:text-body-base/body-base font-medium"
                          onClick={closeMenu}
                        />
                      </li>
                    ))}
                  </ul>
                  <div className="flex items-center gap-element-y md:gap-4">
                    {/* <LanguageToggle
                      languages={languages}
                      currentLanguage={currentLanguage}
                      onLanguageChange={handleLanguageChange}
                      languageInfo={languageInfo}
                    /> */}
                    {/* {mounted && (
                      <ThemeToggle
                        theme={theme}
                        toggleTheme={toggleTheme}
                        setTheme={setTheme}
                      />
                    )} */}
                    {actions.map((a, index) => (
                      <ActionItem
                        key={a.slug}
                        action={a}
                        variant="floating"
                        index={index}
                        totalActions={actions.length}
                        onClick={closeMenu}
                      />
                    ))}
                  </div>
                </div>
            </nav>
        </header>
    );
};

export default Floating;