import React, { useState, useEffect } from 'react';
import { HeaderSectionProps, Language } from './types';
import { useHeader } from './useHeader';
import MobileMenu from './MobileMenu';
import NavItem from './components/NavItem';
import ActionItem from './components/ActionItem';
import ThemeToggle from './components/ThemeToggle';
import LanguageToggle from './components/LanguageToggle';

// 默认语言列表
const defaultLanguages = [
  { code: 'en', name: 'English', flag: '' },
  { code: 'zh', name: '中文', flag: '' }
];

const Classic: React.FC<HeaderSectionProps> = (props) => {
    // 解构 props，但不设置默认值，以避免覆盖传入的值
    const { 
        logo, 
        links = [], 
        actions = [],
        languages = defaultLanguages,
        currentLanguage = 'en',
        onLanguageChange,
        languageInfo,
        languageVersions,
        languageUrls,
        seo
    } = props;

    // 使用 useState 和 useEffect 确保客户端渲染
    const [mounted, setMounted] = useState(false);

    // 在客户端挂载后渲染
    useEffect(() => {
        setMounted(true);
    }, []);

    // 使用 logo 值，如果为空则使用默认值
    const displayLogo = typeof logo === 'object' && logo !== null && 'url' in logo 
        ? (logo.url || 'Brand') 
        : (logo || 'Brand');

    const {
        isMenuOpen,
        toggleMenu,
        closeMenu,
        menuRef,
        theme,
        toggleTheme,
        setTheme,
        handleLanguageChange
    } = useHeader({ onLanguageChange });

    // 确保 languages 是数组
    const languagesArray = Array.isArray(languages) ? languages : defaultLanguages;

    // 获取当前语言对象
    const currentLang = languagesArray.find(lang => lang.code === currentLanguage) || {
        code: 'en',
        name: 'English'
    };

    return (
        <header className="bg-background border-b border-border flex flex-wrap md:justify-start md:flex-nowrap z-50 w-full relative">
            <nav className="relative w-full max-w-container mx-auto px-container-x py-2 md:flex md:items-center md:justify-between md:gap-3">
                {/* Logo w/ Collapse Button - 桌面和移动端都显示 */}
                <div className="flex items-center justify-between">
                    <a
                        className="flex-none flex items-center gap-2"
                        href="/"
                        aria-label="Brand"
                    >
                        <span className="font-medium text-body-base/body-base text-foreground">{displayLogo}</span>
                    </a>

                    {/* Collapse Button - 只在移动端显示 */}
                    <div className="md:hidden">
                        <button 
                            type="button" 
                            className="relative size-10 flex justify-center items-center text-sm font-semibold rounded-full border border-border text-foreground hover:bg-accent active:bg-accent/80 focus:outline-none focus:ring-2 focus:ring-ring disabled:opacity-50 disabled:pointer-events-none" 
                            aria-expanded={isMenuOpen}
                            aria-label="Toggle navigation"
                            onClick={toggleMenu}
                        >
                            {isMenuOpen ? (
                                <svg className="size-4 md:size-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M18 6 6 18" /><path d="m6 6 12 12" /></svg>
                            ) : (
                                <svg className="size-4 md:size-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><line x1="3" x2="21" y1="6" y2="6" /><line x1="3" x2="21" y1="12" y2="12" /><line x1="3" x2="21" y1="18" y2="18" /></svg>
                            )}
                            <span className="sr-only">Toggle navigation</span>
                        </button>
                    </div>
                    {/* End Collapse Button */}
                </div>
                {/* End Logo w/ Collapse Button */}

                {/* 桌面端左侧静态元素 - 只在桌面版显示 */}
                <div className="hidden md:flex md:items-center md:mr-6">
                    {/* 已经在顶部添加了 logo，这里不再需要 */}
                </div>
                {/* End 桌面端左侧静态元素 */}

                {/* Mobile menu (shown only on mobile) */}
                <div className="md:hidden">
                  <MobileMenu
                    isOpen={isMenuOpen}
                    onClose={closeMenu}
                    menuRef={menuRef}
                    links={links}
                    actions={actions}
                    languages={languagesArray}
                    currentLanguage={currentLanguage}
                    currentLang={currentLang}
                    theme={theme}
                    toggleTheme={toggleTheme}
                    setTheme={setTheme}
                    onLanguageChange={handleLanguageChange}
                    languageInfo={languageInfo}
                    languageUrls={languageUrls}
                  />
                </div>

                {/* Desktop menu (shown only on md+) - 改为始终渲染，使用 CSS 控制显示/隐藏 */}
                <div className="hidden md:flex md:w-full md:justify-between md:items-center">
                  <div className="flex gap-6">
                    {links.map((l) => (
                      <NavItem
                        key={l.slug}
                        link={l}
                        className="text-foreground hover:text-foreground/80 px-3 py-2 rounded-md text-body-small/body-small md:text-body-base/body-base font-medium"
                        onClick={closeMenu}
                      />
                    ))}
                  </div>
                  <div className="flex items-center gap-element-y md:gap-4">
                    {/* <LanguageToggle
                      languages={languagesArray}
                      currentLanguage={currentLanguage}
                      onLanguageChange={handleLanguageChange}
                      languageInfo={languageInfo}
                    /> */}
                    {/* {mounted && (
                      <ThemeToggle
                        theme={theme}
                        toggleTheme={toggleTheme}
                        setTheme={setTheme}
                      />
                    )} */}
                    {actions.map((a, index) => (
                      <ActionItem
                        key={a.slug}
                        action={a}
                        variant="default"
                        index={index}
                        totalActions={actions.length}
                        onClick={closeMenu}
                      />
                    ))}
                  </div>
                </div>
            </nav>
        </header>
    );
};

export default Classic;