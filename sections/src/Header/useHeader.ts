import { useState, useEffect, useRef } from 'react';

export interface UseHeaderProps {
  onLanguageChange?: (lang: string) => void;
}

export const useHeader = ({ onLanguageChange }: UseHeaderProps = {}) => {
  // 菜单状态管理
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  // 主题管理
  const [theme, setTheme] = useState<'light' | 'dark' | 'system'>(() => {
    // 检查本地存储
    if (typeof window !== 'undefined') {
      const savedTheme = localStorage.getItem('theme');
      if (savedTheme === 'dark' || savedTheme === 'light' || savedTheme === 'system') {
        return savedTheme as 'light' | 'dark' | 'system';
      }
      // 默认使用系统主题
      return 'system';
    }
    return 'light';
  });

  // 获取当前系统主题偏好
  const getSystemTheme = (): 'light' | 'dark' => {
    if (typeof window !== 'undefined' && window.matchMedia) {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }
    return 'light';
  };

  // 获取实际应用的主题（考虑系统设置）
  const getEffectiveTheme = (): 'light' | 'dark' => {
    return theme === 'system' ? getSystemTheme() : theme;
  };

  // 菜单开关
  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
  };

  // 主题切换 - 循环切换 light -> dark -> system
  const toggleTheme = () => {
    const themeOrder: Array<'light' | 'dark' | 'system'> = ['light', 'dark', 'system'];
    const currentIndex = themeOrder.indexOf(theme);
    const nextIndex = (currentIndex + 1) % themeOrder.length;
    const newTheme = themeOrder[nextIndex];
    
    setTheme(newTheme);
    localStorage.setItem('theme', newTheme);
  };

  // 直接设置特定主题
  const setSpecificTheme = (newTheme: 'light' | 'dark' | 'system') => {
    setTheme(newTheme);
    localStorage.setItem('theme', newTheme);
  };

  // 点击外部关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node) && isMenuOpen) {
        closeMenu();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isMenuOpen]);

  // 监听系统主题变化
  useEffect(() => {
    if (typeof window !== 'undefined' && window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      
      const handleChange = () => {
        // 只有当当前主题设置为"系统"时，才需要更新
        if (theme === 'system') {
          // 强制重新渲染以应用新主题
          setTheme('system');
        }
      };
      
      // 添加事件监听器
      if (mediaQuery.addEventListener) {
        mediaQuery.addEventListener('change', handleChange);
      } else {
        // 兼容旧版浏览器
        mediaQuery.addListener(handleChange);
      }
      
      // 清理函数
      return () => {
        if (mediaQuery.removeEventListener) {
          mediaQuery.removeEventListener('change', handleChange);
        } else {
          // 兼容旧版浏览器
          mediaQuery.removeListener(handleChange);
        }
      };
    }
  }, [theme]);

  // 应用主题到 HTML 元素
  // useEffect(() => {
  //   if (typeof window !== 'undefined') {
  //     const htmlElement = document.documentElement;
  //     const effectiveTheme = getEffectiveTheme();
      
  //     if (effectiveTheme === 'dark') {
  //       htmlElement.classList.add('dark');
  //     } else {
  //       htmlElement.classList.remove('dark');
  //     }
  //   }
  // }, [theme]);

  // 处理语言切换
  const handleLanguageChange = (langCode: string) => {
    if (onLanguageChange) {
      onLanguageChange(langCode);
    }
    closeMenu();
  };

  return {
    isMenuOpen,
    toggleMenu,
    closeMenu,
    menuRef,
    theme,
    toggleTheme,
    setTheme: setSpecificTheme,
    handleLanguageChange
  };
};
