# Header 按钮优化测试用例

## 🧪 测试场景

### 场景1：SaaS产品Header
```tsx
const actions = [
  { text: "Documentation", slug: "/docs" },
  { text: "Login", slug: "/login" },
  { text: "Sign Up", slug: "/signup" }
];
```

**预期结果**：
- Documentation → `ghost` (辅助信息)
- Login → `outline` (次要操作)
- Sign Up → `primary` (主要转化，最后一个且是主要操作)

### 场景2：企业官网Header
```tsx
const actions = [
  { text: "Support", slug: "/support" },
  { text: "Contact", slug: "/contact" },
  { text: "Get Quote", slug: "/quote" }
];
```

**预期结果**：
- Support → `ghost` (辅助链接)
- Contact → `ghost` (非登录类，非最后一个)
- Get Quote → `primary` (主要转化，最后一个且是主要操作)

### 场景3：产品营销Header
```tsx
const actions = [
  { text: "Features", slug: "/features" },
  { text: "Pricing", slug: "/pricing" },
  { text: "Login", slug: "/login" },
  { text: "Start Free Trial", slug: "/trial" }
];
```

**预期结果**：
- Features → `ghost` (产品信息)
- Pricing → `ghost` (价格信息)
- Login → `outline` (登录类按钮)
- Start Free Trial → `primary` (主要转化，最后一个且是主要操作)

### 场景4：单按钮Header
```tsx
const actions = [
  { text: "Get Started", slug: "/start" }
];
```

**预期结果**：
- Get Started → `primary` (唯一按钮且是主要操作)

### 场景5：登录优先Header
```tsx
const actions = [
  { text: "About", slug: "/about" },
  { text: "Login", slug: "/login" }
];
```

**预期结果**：
- About → `ghost` (辅助信息)
- Login → `outline` (登录类按钮，虽然是最后一个但不是主要转化操作)

## 🎯 Header变体测试

### Classic变体特殊处理
```tsx
// Classic Header对非主要/次要操作使用link样式
const actions = [
  { text: "Blog", slug: "/blog" },
  { text: "Login", slug: "/login" },
  { text: "Sign Up", slug: "/signup" }
];
```

**Classic变体预期结果**：
- Blog → `link` (Classic变体的辅助链接)
- Login → `outline` (登录类按钮)
- Sign Up → `primary` (主要操作)

### Floating/Default变体
**Floating/Default变体预期结果**：
- Blog → `ghost` (现代设计的辅助链接)
- Login → `outline` (登录类按钮)
- Sign Up → `primary` (主要操作)

## 🔍 关键词识别测试

### 主要操作关键词
✅ 应识别为Primary的文本：
- "Sign Up", "Get Started", "Start Free", "Try Now"
- "Join Now", "Register", "Create Account"
- "Get Quote", "Contact Sales", "Download"
- "Subscribe", "Buy Now", "Upgrade"
- "Start Trial", "Free Trial", "Get Access"

### 次要操作关键词
✅ 应识别为Secondary的文本：
- "Login", "Log In", "Sign In"
- "Account", "Dashboard", "Profile"
- "Settings", "My Account", "Member", "Portal"

### 辅助操作
✅ 应识别为Ghost/Link的文本：
- "Documentation", "Support", "Help"
- "About", "Blog", "Features", "Pricing"
- "Contact", "FAQ", "Resources"

## 📊 测试验证方法

### 自动化测试
```tsx
describe('ActionItem智能优先级', () => {
  test('主要操作按钮应为primary', () => {
    const action = { text: "Sign Up", slug: "/signup" };
    const result = getButtonProps(action, 0, 1);
    expect(result.variant).toBe('primary');
  });

  test('登录按钮应为outline', () => {
    const action = { text: "Login", slug: "/login" };
    const result = getButtonProps(action, 0, 2);
    expect(result.variant).toBe('outline');
  });

  test('辅助按钮应为ghost', () => {
    const action = { text: "Documentation", slug: "/docs" };
    const result = getButtonProps(action, 0, 3);
    expect(result.variant).toBe('ghost');
  });
});
```

### 视觉回归测试
1. **截图对比**：对比优化前后的Header视觉效果
2. **设计审查**：设计师评估按钮层次是否符合预期
3. **用户测试**：观察用户对不同按钮的点击行为

### 性能测试
1. **渲染性能**：确保智能判断不影响组件渲染速度
2. **内存使用**：验证关键词匹配算法的内存效率
3. **可访问性**：确保不同variant的按钮都有良好的可访问性

## 🎨 边界情况处理

### 空文本处理
```tsx
const action = { text: "", slug: "/empty" };
// 应回退到ghost variant
```

### 特殊字符处理
```tsx
const action = { text: "Sign-Up!", slug: "/signup" };
// 应正确识别为主要操作
```

### 多语言支持
```tsx
const action = { text: "注册", slug: "/signup" };
// 当前版本仅支持英文，未来可扩展多语言关键词
```

## 📈 成功指标

### 功能指标
- ✅ 100%的测试用例通过
- ✅ 所有Header变体正确应用按钮层次
- ✅ 关键词识别准确率 ≥ 95%

### 用户体验指标
- ✅ 主要操作按钮点击率提升 ≥ 15%
- ✅ 用户任务完成时间减少 ≥ 10%
- ✅ Header可用性评分 ≥ 4.5/5.0

### 技术指标
- ✅ 组件渲染性能无明显下降
- ✅ 代码覆盖率 ≥ 90%
- ✅ 无可访问性回归问题

---

**测试目标**：验证智能按钮优先级系统的正确性和有效性  
**测试范围**：所有Header变体和常见使用场景  
**验收标准**：功能正确、性能稳定、用户体验提升 