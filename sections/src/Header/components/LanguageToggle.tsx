import React from 'react';
import { Popover, PopoverTrigger, PopoverContent } from '../../components/ui/popover';
import { Globe, Check, ChevronDown } from 'lucide-react';
import ThemedIcon from '../../components/ThemedIcon';
import { Language, LanguageInfo } from '../types';

interface LanguageToggleProps {
  languages: Language[];
  currentLanguage: string;
  onLanguageChange: (lang: string) => void;
  languageInfo?: LanguageInfo;
  languageUrls?: Record<string, string>;
}

const LanguageToggle: React.FC<LanguageToggleProps> = ({
  languages,
  currentLanguage,
  onLanguageChange,
  languageInfo,
  languageUrls,
}) => {
  const items = languageInfo
    ? languageInfo.supportedLanguages.map((code) => ({
        code,
        name: languageInfo.languageNames[code] || code,
      }))
    : languages;

  const currentCode = languageInfo ? languageInfo.currentLanguage : currentLanguage;
  const currentName =
    items.find((l) => l.code === currentCode)?.name.toUpperCase() || currentCode.toUpperCase();
  // Display language code (e.g., EN) in toggle button for SEO and readability
  const currentCodeUpper = currentCode.toUpperCase();

  return (
    <Popover>
      <PopoverTrigger asChild>
        <button
          onClick={(e) => e.stopPropagation()}
          className="language-toggle-button inline-flex items-center justify-center px-3 py-2 text-sm font-medium text-foreground hover:text-foreground/80 hover:bg-accent/50 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 transition-all duration-200"
          aria-label="Select language"
        >
          <ThemedIcon icon="Globe" theme={{ size: 'w-4 h-4', color: 'text-current' }} />
          <span className="ml-1.5 mr-1 whitespace-nowrap text-xs font-semibold">{currentCodeUpper}</span>
          <ChevronDown className="w-3 h-3 ml-0.5" />
        </button>
      </PopoverTrigger>
      <PopoverContent align="end" className="w-[200px] p-0 bg-background border border-border shadow-md rounded-lg">
        <div className="flex flex-col p-1">
          {items.map(({ code, name }) => {
            const url = languageUrls?.[code] || '#';
            const isCurrent = code === currentCode;
            return (
              <a
                key={code}
                href={url}
                rel="alternate"
                hrefLang={code}
                onClick={(e) => {
                  if (!languageUrls) e.preventDefault();
                  onLanguageChange(code);
                }}
                className={`flex items-center justify-between px-3 py-2 rounded-md transition-colors ${
                  isCurrent
                    ? 'bg-primary text-primary-foreground'
                    : 'text-foreground hover:bg-accent hover:text-accent-foreground'
                }`}
              >
                <span className="text-sm font-medium whitespace-nowrap">{name}</span>
                {isCurrent && <Check className="w-4 h-4 text-primary-foreground" />}
              </a>
            );
          })}
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default LanguageToggle;
