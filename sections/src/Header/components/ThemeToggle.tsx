import React, { useState, useEffect } from 'react';
import { Popover, PopoverTrigger, PopoverContent } from '../../components/ui/popover';
import { Sun, Moon, Laptop } from 'lucide-react';
import { Theme } from '../types';

interface ThemeToggleProps {
  theme: Theme;
  toggleTheme: () => void;
  setTheme: (theme: Theme) => void;
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({ theme, toggleTheme, setTheme }) => {
  // 使用 useState 和 useEffect 确保客户端渲染
  const [mounted, setMounted] = useState(false);

  // 在客户端挂载后渲染
  useEffect(() => {
    setMounted(true);
  }, []);

  // 图标配置
  const icons = [
    { key: 'light', icon: <Sun className="w-5 h-5" /> },
    { key: 'dark', icon: <Moon className="w-5 h-5" /> },
    { key: 'system', icon: <Laptop className="w-5 h-5" /> },
  ] as const;

  // 如果未挂载，返回占位符以避免布局偏移
  if (!mounted) {
    return <div className="w-8 h-8" />;
  }

  return (
    <Popover>
      <PopoverTrigger asChild>
        <button
          type="button"
          onClick={(e) => { e.stopPropagation(); toggleTheme(); }}
          className="theme-toggle-button inline-flex items-center justify-center px-3 py-2 text-sm text-foreground hover:text-foreground/80 hover:bg-accent/50 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 transition-all duration-200"
          aria-label="Toggle theme"
        >
          {icons.find((i) => i.key === theme)?.icon}
        </button>
      </PopoverTrigger>
      <PopoverContent align="end" className="flex space-x-2 p-2">
        {icons.map((i) => (
          <button
            type="button"
            key={i.key}
            onClick={() => setTheme(i.key as Theme)}
            className={`p-2 rounded-md transition-colors ${
              theme === i.key 
                ? 'bg-primary text-primary-foreground' 
                : 'hover:bg-accent hover:text-accent-foreground'
            }`}
          >
            {i.icon}
          </button>
        ))}
      </PopoverContent>
    </Popover>
  );
};

export default ThemeToggle;
