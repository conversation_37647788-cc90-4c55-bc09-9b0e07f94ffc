import React from 'react';
import { NavItem as NavItemType } from '../types';
import { Button } from '../../components/ButtonV2';

export type ActionItemVariant = 'default' | 'classic' | 'floating';

interface ActionItemProps {
  action: NavItemType;
  variant?: ActionItemVariant;
  className?: string;
  onClick?: () => void;
  index?: number;
  totalActions?: number;
}

const isPrimaryActionText = (text: string): boolean => {
  const primaryKeywords = [
    'sign up', 'get started', 'start free', 'try now', 'join now',
    'register', 'create account', 'get quote', 'contact sales',
    'download', 'subscribe', 'buy now', 'upgrade', 'start trial',
    'free trial', 'get access', 'join', 'subscribe'
  ];
  return primaryKeywords.some(keyword => 
    text.toLowerCase().includes(keyword)
  );
};

const isSecondaryActionText = (text: string): boolean => {
  const secondaryKeywords = [
    'login', 'log in', 'sign in', 'account', 'dashboard',
    'profile', 'settings', 'my account', 'member', 'portal'
  ];
  return secondaryKeywords.some(keyword => 
    text.toLowerCase().includes(keyword)
  );
};

const ActionItem: React.FC<ActionItemProps> = ({ 
  action, 
  variant = 'default', 
  className, 
  onClick,
  index = 0,
  totalActions = 1
}) => {
  const getIntelligentButtonProps = () => {
    const isLastButton = index === totalActions - 1;
    const isPrimaryAction = isPrimaryActionText(action.text);
    const isSecondaryAction = isSecondaryActionText(action.text);
    
    if (isLastButton && isPrimaryAction) {
      return { variant: 'primary' as const, size: 'small' as const };
    }
    
    if (isSecondaryAction) {
      return { variant: 'outline' as const, size: 'small' as const };
    }
    
    return { variant: 'ghost' as const, size: 'small' as const };
  };

  const getButtonProps = () => {
    switch (variant) {
      case 'classic':
        if (isPrimaryActionText(action.text)) {
          return { variant: 'primary' as const, size: 'small' as const };
        }
        if (isSecondaryActionText(action.text)) {
          return { variant: 'outline' as const, size: 'small' as const };
        }
        return { variant: 'link' as const, size: 'small' as const };
        
      case 'floating':
        return getIntelligentButtonProps();
        
      default: // 'default'
        return getIntelligentButtonProps();
    }
  };

  const buttonProps = getButtonProps();

  return (
    <Button
      href={action.slug}
      variant={buttonProps.variant}
      size={buttonProps.size}
      className={className}
      onClick={onClick}
      aria-current={(action as any).active ? 'page' : undefined}
    >
      {action.text}
    </Button>
  );
};

export default ActionItem;
