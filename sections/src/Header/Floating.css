/* Floating Header 特定值 */
.floating-header {
  --container-max-width: 66rem; /* 覆盖全局值，仅对 .floating-header 及其后代生效 */
  
  /* 按钮圆角优化 - 使用更圆润的圆角提升视觉协调性 */
  --button-radius: 1rem; /* 16px - 更圆润的圆角，与浮动头部的圆角风格保持一致 */
  
  /* 按钮动画优化 - 增强交互体验 */
  --button-transition-duration-normal: 200ms;
  --button-hover-scale: 1.02; /* 轻微的悬停缩放效果 */
  --button-hover-lift: -1px; /* 轻微的悬停上浮效果 */
  --button-hover-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); /* 悬停阴影 */
  
  /* 按钮间距优化 - 与浮动头部的整体间距保持协调 */
  --button-small-padding-x: 1rem; /* 16px */
  --button-medium-padding-x: 1.25rem; /* 20px */
  --button-large-padding-x: 1.5rem; /* 24px */
  
  /* 按钮高度优化 - 与头部高度协调 */
  --button-small-height: 2.25rem; /* 36px */
  --button-medium-height: 2.5rem; /* 40px */
  --button-large-height: 2.75rem; /* 44px */
}

/* 响应式调整 */
.page-width-wide .floating-header {
  --container-max-width: 76rem;
  
  /* 宽屏模式下的按钮优化 */
  --button-radius: 1.125rem; /* 18px - 宽屏下稍微增加圆角 */
  --button-medium-height: 2.75rem; /* 44px */
  --button-large-height: 3rem; /* 48px */
}

.page-width-full .floating-header {
  --container-max-width: 90rem;
  
  /* 满屏模式下的按钮优化 */
  --button-radius: 1.25rem; /* 20px - 满屏下使用最圆润的圆角 */
  --button-medium-height: 3rem; /* 48px */
  --button-large-height: 3.25rem; /* 52px */
  --button-hover-scale: 1.03; /* 满屏下稍微增强悬停效果 */
}

/* 浮动头部按钮特定样式增强 */
.floating-header .btn-base {
  /* 应用圆角变量 */
  border-radius: var(--button-radius);
  
  /* 增强过渡效果 */
  transition: all var(--button-transition-duration-normal) cubic-bezier(0.4, 0, 0.2, 1);
}

/* 悬停效果增强 */
.floating-header .btn-base:hover:not(:disabled) {
  transform: translateY(var(--button-hover-lift)) scale(var(--button-hover-scale));
  box-shadow: var(--button-hover-shadow);
}

/* 主题切换和语言切换按钮的特殊优化 */
.floating-header .theme-toggle-button,
.floating-header .language-toggle-button {
  border-radius: var(--button-radius);
  transition: all var(--button-transition-duration-normal) cubic-bezier(0.4, 0, 0.2, 1);
}

.floating-header .theme-toggle-button:hover,
.floating-header .language-toggle-button:hover {
  transform: translateY(var(--button-hover-lift)) scale(var(--button-hover-scale));
  box-shadow: var(--button-hover-shadow);
}

/* 移动端菜单按钮优化 */
.floating-header .md\\:hidden button {
  border-radius: var(--button-radius);
  transition: all var(--button-transition-duration-normal) cubic-bezier(0.4, 0, 0.2, 1);
}

.floating-header .md\\:hidden button:hover {
  transform: scale(var(--button-hover-scale));
}

/* 暗色模式下的优化 */
.dark .floating-header {
  --button-hover-shadow: 0 2px 12px rgba(0, 0, 0, 0.3); /* 暗色模式下增强阴影 */
  --button-hover-lift: -2px; /* 暗色模式下稍微增加上浮效果 */
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  .floating-header .btn-base,
  .floating-header .theme-toggle-button,
  .floating-header .language-toggle-button,
  .floating-header .md\\:hidden button {
    transition: none !important;
    transform: none !important;
  }
  
  .floating-header .btn-base:hover,
  .floating-header .theme-toggle-button:hover,
  .floating-header .language-toggle-button:hover,
  .floating-header .md\\:hidden button:hover {
    transform: none !important;
    box-shadow: none !important;
  }
}
