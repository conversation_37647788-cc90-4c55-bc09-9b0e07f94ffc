import React, { useState, useEffect } from 'react';
import { HeaderSectionProps, Language, LanguageInfo } from './types';
import ThemedIcon from '../components/ThemedIcon/ThemedIcon';
import { Popover, PopoverContent, PopoverTrigger } from "../components/ui/popover";
import { cn } from "../lib/utils";
import { Button } from '../components/ButtonV2';

interface MobileMenuProps extends Pick<HeaderSectionProps, 'links' | 'languages' | 'currentLanguage'> {
  isOpen: boolean;
  onClose: () => void;
  menuRef: React.RefObject<HTMLDivElement>;
  theme: 'light' | 'dark' | 'system';
  toggleTheme: () => void;
  setTheme: (theme: 'light' | 'dark' | 'system') => void;
  onLanguageChange: (langCode: string) => void;
  currentLang: Language;
  actions?: HeaderSectionProps['actions'];
  logo?: string | { url: string; alt: string } | React.ReactNode;
  languageInfo?: LanguageInfo;
  languageUrls?: Record<string, string>;
}

const MobileMenu: React.FC<MobileMenuProps> = ({
  isOpen,
  onClose,
  menuRef,
  links = [],
  languages = [],
  currentLanguage = 'en',
  currentLang,
  theme,
  toggleTheme,
  setTheme,
  onLanguageChange,
  actions,
  logo = 'Brand',
  languageInfo,
  languageUrls,
}) => {
  // 使用 useState 和 useEffect 确保客户端渲染
  const [mounted, setMounted] = useState(false);

  // 控制 Popover 的开关状态 - 移到顶层，避免条件渲染后使用钩子
  const [open, setOpen] = React.useState(false);

  // 在客户端挂载后渲染
  useEffect(() => {
    setMounted(true);
  }, []);

  // 处理 logo 显示
  const displayLogo = typeof logo === 'object' && logo !== null && 'url' in logo 
    ? (logo.url || 'Brand') 
    : (logo || 'Brand');

  // 如果未挂载，返回最小化的结构以避免布局偏移
  if (!mounted) {
    return (
      <div className={`fixed inset-0 z-50 ${isOpen ? 'block' : 'hidden'}`}>
        <div className="absolute inset-0 bg-black/50"></div>
      </div>
    );
  }

  return (
    <div 
      ref={menuRef}
      className={`${isOpen ? 'block' : 'hidden'} md:block md:basis-full md:grow md:static md:h-auto fixed inset-0 top-0 bg-white dark:bg-neutral-800 z-50 flex flex-col`}
    >
      {/* 移动端顶部导航栏 - 与展开面板一体 */}
      <div className="md:hidden flex items-center justify-between px-container-x py-2 border-b border-gray-200 dark:border-neutral-700">
        <a className="flex-none flex items-center gap-2" href="#" aria-label="Brand">
          {React.isValidElement(logo) ? (
            logo
          ) : typeof logo === 'object' && logo !== null && 'url' in logo ? (
            <img src={logo.url} alt={logo.alt} className="h-6 w-auto" />
          ) : null}
          <span className="font-medium text-gray-800 dark:text-white ml-2">
            {typeof logo === 'object' && logo !== null && 'alt' in logo
              ? logo.alt
              : typeof logo === 'string'
              ? logo
              : 'Brand'}
          </span>
        </a>
        <button 
          type="button" 
          className="relative size-10 flex justify-center items-center text-sm font-semibold rounded-full border border-gray-200 text-gray-800 hover:bg-gray-100 active:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-200 disabled:opacity-50 disabled:pointer-events-none dark:text-white dark:border-neutral-700 dark:hover:bg-neutral-700 dark:active:bg-neutral-600 dark:focus:ring-neutral-600" 
          aria-label="Close navigation"
          onClick={onClose}
        >
          <ThemedIcon 
            icon="X"
            theme={{
              size: 'w-5 h-5',
              color: 'text-current',
              background: '',
              containerClass: '',
              darkModeColor: '',
              darkModeBackground: '',
              effects: '',
              strokeWidth: 2
            }}
          />
          <span className="sr-only">Close menu</span>
        </button>
      </div>

      {/* 移动端菜单内容区域 - 添加滚动条 */}
      <div className="flex-grow overflow-y-auto md:overflow-hidden md:max-h-[75vh] h-[calc(100vh-53px)] md:h-auto">
        <div className="py-content-y px-container-x md:px-0 md:py-0 flex flex-col md:flex-row md:items-center md:justify-end gap-element-y md:gap-1">
          {/* 动态渲染导航链接 */}
          <div className="w-full md:w-auto flex flex-col md:flex-row md:items-center">
            {/* 移动端导航组标题 */}
            <div className="md:hidden mb-1">
              <span className="text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-neutral-400">Navigation</span>
            </div>
            
            {links.map((link, index) => (
              <a 
                key={index}
                className={`py-3 px-4 md:p-2 flex items-center justify-between text-sm rounded-lg ${
                  index === 0 
                    ? 'bg-gray-100 text-gray-800 md:bg-transparent md:text-blue-600 dark:bg-neutral-700 dark:text-white md:dark:bg-transparent md:dark:text-blue-400' 
                    : 'text-gray-700 hover:bg-gray-50 md:hover:bg-transparent md:hover:text-blue-600 dark:text-neutral-300 dark:hover:bg-neutral-700 md:dark:hover:bg-transparent md:dark:hover:text-blue-400'
                }`} 
                href={link.slug}
                aria-current={index === 0 ? 'page' : undefined}
                onClick={onClose}
              >
                <div className="flex items-center">
                  <ThemedIcon 
                    icon={link.icon || "FileText"}
                    theme={{
                      size: 'w-5 h-5',
                      color: 'text-current',
                      containerClass: 'shrink-0 me-3 md:me-2',
                      background: '',
                      darkModeColor: '',
                      darkModeBackground: '',
                      effects: '',
                      strokeWidth: 2
                    }}
                  />
                  {link.text}
                </div>
                {index === 0 && (
                  <ThemedIcon 
                    icon="Check"
                    theme={{
                      size: 'w-4 h-4',
                      color: 'text-blue-500',
                      darkModeColor: 'dark:text-blue-400',
                      containerClass: 'md:hidden',
                      background: '',
                      darkModeBackground: '',
                      effects: '',
                      strokeWidth: 2
                    }}
                  />
                )}
              </a>
            ))}

            {/* 如果没有导航链接，显示默认链接 */}
            {links.length === 0 && (
              <div className="gap-element-y md:gap-0 md:flex md:items-center md:gap-2">
                <a className="py-3 px-4 md:p-2 flex items-center justify-between text-sm rounded-lg md:bg-transparent md:text-blue-600 bg-gray-100 text-gray-800 dark:bg-neutral-700 dark:text-white md:dark:bg-transparent md:dark:text-blue-400" href="#" aria-current="page" onClick={onClose}>
                  <div className="flex items-center">
                    <ThemedIcon 
                      icon="Home"
                      theme={{
                        size: 'w-5 h-5',
                        color: 'text-current',
                        containerClass: 'shrink-0 me-3 md:me-2',
                        background: '',
                        darkModeColor: '',
                        darkModeBackground: '',
                        effects: '',
                        strokeWidth: 2
                      }}
                    />
                    Landing
                  </div>
                  <ThemedIcon 
                    icon="Check"
                    theme={{
                      size: 'w-4 h-4',
                      color: 'text-blue-500',
                      darkModeColor: 'dark:text-blue-400',
                      containerClass: 'md:hidden',
                      background: '',
                      darkModeBackground: '',
                      effects: '',
                      strokeWidth: 2
                    }}
                  />
                </a>

                <a className="py-3 px-4 md:p-2 flex items-center text-sm rounded-lg text-gray-700 hover:bg-gray-50 md:hover:bg-transparent md:hover:text-blue-600 dark:text-neutral-300 dark:hover:bg-neutral-700 md:dark:hover:bg-transparent md:dark:hover:text-blue-400" href="#" onClick={onClose}>
                  <ThemedIcon 
                    icon="User"
                    theme={{
                      size: 'w-5 h-5',
                      color: 'text-current',
                      containerClass: 'shrink-0 me-3 md:me-2',
                      background: '',
                      darkModeColor: '',
                      darkModeBackground: '',
                      effects: '',
                      strokeWidth: 2
                    }}
                  />
                  Account
                </a>

                <a className="py-3 px-4 md:p-2 flex items-center text-sm rounded-lg text-gray-700 hover:bg-gray-50 md:hover:bg-transparent md:hover:text-blue-600 dark:text-neutral-300 dark:hover:bg-neutral-700 md:dark:hover:bg-transparent md:dark:hover:text-blue-400" href="#" onClick={onClose}>
                  <ThemedIcon 
                    icon="Briefcase"
                    theme={{
                      size: 'w-5 h-5',
                      color: 'text-current',
                      containerClass: 'shrink-0 me-3 md:me-2',
                      background: '',
                      darkModeColor: '',
                      darkModeBackground: '',
                      effects: '',
                      strokeWidth: 2
                    }}
                  />
                  Work
                </a>

                <a className="py-3 px-4 md:p-2 flex items-center text-sm rounded-lg text-gray-700 hover:bg-gray-50 md:hover:bg-transparent md:hover:text-blue-600 dark:text-neutral-300 dark:hover:bg-neutral-700 md:dark:hover:bg-transparent md:dark:hover:text-blue-400" href="#" onClick={onClose}>
                  <ThemedIcon 
                    icon="FileText"
                    theme={{
                      size: 'w-5 h-5',
                      color: 'text-current',
                      containerClass: 'shrink-0 me-3 md:me-2',
                      background: '',
                      darkModeColor: '',
                      darkModeBackground: '',
                      effects: '',
                      strokeWidth: 2
                    }}
                  />
                  Blog
                </a>
              </div>
            )}
          </div>

          {/* Button Group - 包含语言切换和登录/注册按钮 */}
          <div className="relative flex flex-col md:flex-row md:flex-nowrap items-start md:items-center gap-y-2 md:gap-x-1.5 md:ps-2.5 mt-3 md:mt-0 md:ms-1.5 pt-3 md:pt-0 border-t md:border-t-0 border-gray-200 dark:border-neutral-700 md:before:block md:before:absolute md:before:top-1/2 md:before:start-0 md:before:w-px md:before:h-4 md:before:bg-gray-300 md:before:-translate-y-1/2 dark:md:before:bg-neutral-700 w-full md:w-auto">
            {/* Language Switcher */}
            {((languageInfo && languageInfo.supportedLanguages.length > 1) || languages.length > 1) && (
              <div className="w-full md:w-auto">
                {/* 桌面版下拉菜单 - 使用 Shadcn UI Popover */}
                <div className="hidden md:block relative">
                  <Popover open={open} onOpenChange={setOpen}>
                    <PopoverTrigger asChild>
                      <button 
                        type="button" 
                        className="w-full md:w-auto p-2 flex items-center justify-between md:justify-start text-sm text-gray-800 hover:text-gray-500 focus:outline-none focus:text-gray-500 dark:text-neutral-200 dark:hover:text-neutral-500 dark:focus:text-neutral-500"
                      >
                        <div className="flex items-center">
                          <ThemedIcon 
                            icon="Globe"
                            theme={{
                              size: 'w-4 h-4',
                              color: 'text-current',
                              containerClass: 'shrink-0 me-2',
                              background: '',
                              darkModeColor: '',
                              darkModeBackground: '',
                              effects: '',
                              strokeWidth: 2
                            }}
                          />
                          {languageInfo ? languageInfo.currentLanguage : currentLang.code.toUpperCase()}
                        </div>
                        <ThemedIcon 
                          icon="ChevronDown"
                          theme={{
                            size: 'w-4 h-4',
                            color: 'text-current',
                            containerClass: 'shrink-0 ms-1',
                            background: '',
                            darkModeColor: '',
                            darkModeBackground: '',
                            effects: '',
                            strokeWidth: 2
                          }}
                        />
                      </button>
                    </PopoverTrigger>
                    <PopoverContent className="w-[200px] p-0 bg-white dark:bg-neutral-800 border border-gray-200 dark:border-neutral-700 shadow-md rounded-md" align="end">
                      <div className="flex flex-col p-1">
                        {/* 优先使用服务端返回的语言数据 */}
                        {languageInfo ? (
                          languageInfo.supportedLanguages.map((langCode) => {
                            const langName = languageInfo.languageNames[langCode] || langCode;
                            const langUrl = languageUrls?.[langCode] || '#';
                            const isCurrentLang = langCode === languageInfo.currentLanguage;
                            
                            return (
                              <a
                                key={langCode}
                                href={langUrl}
                                className={`flex items-center justify-between px-3 py-2 rounded-sm ${
                                  isCurrentLang
                                    ? 'bg-gray-100 text-gray-900 dark:bg-neutral-700 dark:text-neutral-100'
                                    : 'text-gray-700 hover:bg-gray-50 dark:text-neutral-300 dark:hover:bg-neutral-700/50'
                                }`}
                                onClick={(e) => {
                                  // 如果有回调函数，执行回调但不阻止默认行为
                                  if (typeof onLanguageChange === 'function') {
                                    // 只有在编辑器模式下才阻止默认行为
                                    if (!languageUrls) {
                                      e.preventDefault();
                                    }
                                    onLanguageChange(langCode);
                                    setOpen(false);
                                  }
                                }}
                              >
                                <span className="font-medium">{langName}</span>
                                {isCurrentLang && (
                                  <ThemedIcon 
                                    icon="Check"
                                    theme={{
                                      size: 'w-4 h-4',
                                      color: 'text-blue-500',
                                      darkModeColor: 'dark:text-blue-400',
                                      background: '',
                                      containerClass: '',
                                      darkModeBackground: '',
                                      effects: '',
                                      strokeWidth: 2
                                    }}
                                  />
                                )}
                              </a>
                            );
                          })
                        ) : (
                          // 回退到传统的语言列表
                          languages.map((lang) => (
                            <a
                              key={lang.code}
                              href="#"
                              className={`flex items-center justify-between px-3 py-2 rounded-sm ${
                                lang.code === currentLanguage
                                  ? 'bg-gray-100 text-gray-900 dark:bg-neutral-700 dark:text-neutral-100'
                                  : 'text-gray-700 hover:bg-gray-50 dark:text-neutral-300 dark:hover:bg-neutral-700/50'
                              }`}
                              onClick={(e) => {
                                if (typeof onLanguageChange === 'function') {
                                  // 只有在编辑器模式下才阻止默认行为
                                  if (!languageUrls) {
                                    e.preventDefault();
                                  }
                                  onLanguageChange(lang.code);
                                  setOpen(false);
                                }
                              }}
                            >
                              <span className="font-medium">{lang.name}</span>
                              {lang.code === currentLanguage && (
                                <ThemedIcon 
                                  icon="Check"
                                  theme={{
                                    size: 'w-4 h-4',
                                    color: 'text-blue-500',
                                    darkModeColor: 'dark:text-blue-400',
                                    background: '',
                                    containerClass: '',
                                    darkModeBackground: '',
                                    effects: '',
                                    strokeWidth: 2
                                  }}
                                />
                              )}
                            </a>
                          ))
                        )}
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>

                {/* 移动端语言切换 */}
                <div className="md:hidden">
                  <div className="mb-1">
                    <span className="text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-neutral-400">Language</span>
                  </div>
                  <div className="flex flex-col gap-1">
                    {/* 优先使用服务端返回的语言数据 */}
                    {languageInfo ? (
                      languageInfo.supportedLanguages.map((langCode) => {
                        const langName = languageInfo.languageNames[langCode] || langCode;
                        const langUrl = languageUrls?.[langCode] || '#';
                        const isCurrentLang = langCode === languageInfo.currentLanguage;
                        
                        return (
                          <a
                            key={langCode}
                            href={langUrl}
                            rel="alternate"
                            hrefLang={langCode}
                            className={`flex items-center justify-between p-2 rounded-lg ${
                              isCurrentLang
                                ? 'bg-gray-100 text-gray-800 dark:bg-neutral-700 dark:text-white'
                                : 'text-gray-700 hover:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-700'
                            }`}
                            onClick={(e) => {
                              if (!languageUrls) e.preventDefault();
                              onLanguageChange(langCode);
                              onClose();
                            }}
                          >
                            <span className="font-medium whitespace-nowrap">{langName}</span>
                            {isCurrentLang && (
                              <ThemedIcon
                                icon="Check"
                                theme={{ size: 'w-4 h-4', color: 'text-blue-500', darkModeColor: 'dark:text-blue-400' }}
                              />
                            )}
                          </a>
                        );
                      })
                    ) : (
                      // 回退到传统的语言列表
                      languages.map((lang) => (
                        <a
                          key={lang.code}
                          href="#"
                          rel="alternate"
                          hrefLang={lang.code}
                          className={`flex items-center justify-between p-2 rounded-lg ${
                            lang.code === currentLanguage
                              ? 'bg-gray-100 text-gray-800 dark:bg-neutral-700 dark:text-white'
                              : 'text-gray-700 hover:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-700'
                          }`}
                          onClick={(e) => {
                            if (!languageUrls) e.preventDefault();
                            onLanguageChange(lang.code);
                            onClose();
                          }}
                        >
                          <span className="font-medium whitespace-nowrap">{lang.name}</span>
                          {lang.code === currentLanguage && (
                            <ThemedIcon
                              icon="Check"
                              theme={{ size: 'w-4 h-4', color: 'text-blue-500', darkModeColor: 'dark:text-blue-400' }}
                            />
                          )}
                        </a>
                      ))
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* 主题切换按钮 */}
            <div className="w-full md:w-auto flex flex-col md:flex-row md:items-center">
              {/* 移动端主题切换标题 */}
              <div className="md:hidden mb-1 w-full">
                <span className="text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-neutral-400">
                  Theme
                </span>
              </div>
              
              {/* 移动端主题选择按钮组 */}
              {mounted && (
                <div className="md:hidden flex flex-col gap-1 w-full">
                  <button 
                    type="button" 
                    className={`flex items-center justify-between w-full px-4 py-3 text-sm text-start rounded-lg ${
                      theme === 'light' 
                        ? 'bg-gray-100 text-gray-800 dark:bg-neutral-700 dark:text-white' 
                        : 'text-gray-700 hover:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-700'
                    }`}
                    onClick={() => setTheme('light')}
                  >
                    <span className="flex items-center">
                      <ThemedIcon 
                        icon="Sun"
                        theme={{
                          size: 'w-5 h-5',
                          color: 'text-current',
                          containerClass: 'mr-3',
                          background: '',
                          darkModeColor: '',
                          darkModeBackground: '',
                          effects: '',
                          strokeWidth: 2
                        }}
                      />
                      <span>Light Mode</span>
                    </span>
                    {theme === 'light' && (
                      <ThemedIcon 
                        icon="Check"
                        theme={{
                          size: 'w-4 h-4',
                          color: 'text-blue-500',
                          darkModeColor: 'dark:text-blue-400',
                          background: '',
                          containerClass: '',
                          darkModeBackground: '',
                          effects: '',
                          strokeWidth: 2
                        }}
                      />
                    )}
                  </button>
                  
                  <button 
                    type="button" 
                    className={`flex items-center justify-between w-full px-4 py-3 text-sm text-start rounded-lg ${
                      theme === 'dark' 
                        ? 'bg-gray-100 text-gray-800 dark:bg-neutral-700 dark:text-white' 
                        : 'text-gray-700 hover:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-700'
                    }`}
                    onClick={() => setTheme('dark')}
                  >
                    <span className="flex items-center">
                      <ThemedIcon 
                        icon="Moon"
                        theme={{
                          size: 'w-5 h-5',
                          color: 'text-current',
                          containerClass: 'mr-3',
                          background: '',
                          darkModeColor: '',
                          darkModeBackground: '',
                          effects: '',
                          strokeWidth: 2
                        }}
                      />
                      <span>Dark Mode</span>
                    </span>
                    {theme === 'dark' && (
                      <ThemedIcon 
                        icon="Check"
                        theme={{
                          size: 'w-4 h-4',
                          color: 'text-blue-500',
                          darkModeColor: 'dark:text-blue-400',
                          background: '',
                          containerClass: '',
                          darkModeBackground: '',
                          effects: '',
                          strokeWidth: 2
                        }}
                      />
                    )}
                  </button>
                  
                  <button 
                    type="button" 
                    className={`flex items-center justify-between w-full px-4 py-3 text-sm text-start rounded-lg ${
                      theme === 'system' 
                        ? 'bg-gray-100 text-gray-800 dark:bg-neutral-700 dark:text-white' 
                        : 'text-gray-700 hover:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-700'
                    }`}
                    onClick={() => setTheme('system')}
                  >
                    <span className="flex items-center">
                      <ThemedIcon 
                        icon="Monitor"
                        theme={{
                          size: 'w-5 h-5',
                          color: 'text-current',
                          containerClass: 'mr-3',
                          background: '',
                          darkModeColor: '',
                          darkModeBackground: '',
                          effects: '',
                          strokeWidth: 2
                        }}
                      />
                      <span>System Preference</span>
                    </span>
                    {theme === 'system' && (
                      <ThemedIcon 
                        icon="Check"
                        theme={{
                          size: 'w-4 h-4',
                          color: 'text-blue-500',
                          darkModeColor: 'dark:text-blue-400',
                          background: '',
                          containerClass: '',
                          darkModeBackground: '',
                          effects: '',
                          strokeWidth: 2
                        }}
                      />
                    )}
                  </button>
                </div>
              )}
              
              {/* 桌面端主题切换按钮 */}
              {mounted && (
                <button 
                  type="button" 
                  className="hidden md:flex items-center justify-center text-sm rounded-full text-gray-800 hover:bg-gray-100 focus:text-gray-500 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 md:dark:hover:bg-neutral-700 md:h-8 md:w-8"
                  onClick={toggleTheme}
                  aria-label="Toggle theme"
                >
                  {theme === 'dark' ? (
                    <ThemedIcon 
                      icon="Sun"
                      theme={{
                        size: 'w-5 h-5',
                        color: 'text-current',
                        background: '',
                        containerClass: '',
                        darkModeColor: '',
                        darkModeBackground: '',
                        effects: '',
                        strokeWidth: 2
                      }}
                    />
                  ) : theme === 'light' ? (
                    <ThemedIcon 
                      icon="Moon"
                      theme={{
                        size: 'w-5 h-5',
                        color: 'text-current',
                        background: '',
                        containerClass: '',
                        darkModeColor: '',
                        darkModeBackground: '',
                        effects: '',
                        strokeWidth: 2
                      }}
                    />
                  ) : (
                    <ThemedIcon 
                      icon="Monitor"
                      theme={{
                        size: 'w-5 h-5',
                        color: 'text-current',
                        background: '',
                        containerClass: '',
                        darkModeColor: '',
                        darkModeBackground: '',
                        effects: '',
                        strokeWidth: 2
                      }}
                    />
                  )}
                </button>
              )}
            </div>

            {/* 动态渲染操作按钮 */}
            <div className="flex flex-col gap-element-y md:gap-0 md:flex-row md:items-center md:gap-2">
              {/* 移动端操作组标题 */}
              {(actions && actions.length > 0 || true) && (
                <div className="md:hidden mb-1">
                  <span className="text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-neutral-400">
                    Account
                  </span>
                </div>
              )}
              
              {actions && actions.map((action, index) => (
                <Button
                  key={index}
                  href={action.slug}
                  variant="ghost"
                  size="small"
                  className="justify-start w-full md:w-auto text-foreground hover:text-foreground/80"
                  onClick={onClose}
                >
                  {action.text}
                </Button>
              ))}
              
              {/* 如果没有操作按钮，显示默认登录按钮 */}
              {(!actions || actions.length === 0) && (
                <Button
                  href="#"
                  variant="ghost"
                  size="small"
                  className="justify-between w-full md:w-auto text-foreground hover:text-foreground/80"
                  onClick={onClose}
                  leftIcon={
                    <svg className="w-5 h-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2" />
                      <circle cx="12" cy="7" r="4" />
                    </svg>
                  }
                  rightIcon={
                    <svg className="w-5 h-5 block md:hidden" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M5 12h14" />
                      <path d="m12 5 7 7-7 7" />
                    </svg>
                  }
                >
                  Log in
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MobileMenu;
