'use client'

import { useState, useEffect } from 'react'
import { Radio, RadioGroup } from '@headlessui/react'
import { CheckIcon, SparklesIcon } from '@heroicons/react/20/solid'
import PriceDisplay, { formatPrice } from './PriceDisplay'
import { PricingThreeSectionProps, Tier, Frequency } from './types'
import { getPriceDisplay, getCurrency, getPriceDescription } from './utils'
import { Button } from '../components/ButtonV2'

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ')
}

export default function AnimatedVariant({
  id,
  tagline = 'Pricing',
  title = 'Choose the right plan for you',
  description = 'Choose an affordable plan that\'s packed with the best features for engaging your audience, creating customer loyalty, and driving sales.',
  frequencies,
  tiers = [],
  customSolution
}: PricingThreeSectionProps) {
  // 默认频率为 monthly
  const [selectedFrequency, setSelectedFrequency] = useState<string>('monthly');
  
  // 根据 billingPeriod 设置默认频率
  useEffect(() => {
    if (!frequencies?.enabled && frequencies?.billingPeriod) {
      const periodType = frequencies.billingPeriod.type;
      if (periodType === 'monthly') {
        setSelectedFrequency('monthly');
      } else if (periodType === 'annually') {
        setSelectedFrequency('annually');
      }
    }
  }, [frequencies]);
  
  // 获取频率选项
  const frequencyOptions: Frequency[] = frequencies?.enabled && frequencies.options 
    ? frequencies.options 
    : [{ value: 'monthly', label: 'Monthly', priceSuffix: '/month' }];
  
  // 获取当前频率的后缀
  const getCurrentSuffix = () => {
    // 如果频率选择器关闭但设置了 billingPeriod
    if (!frequencies?.enabled && frequencies?.billingPeriod) {
      const periodType = frequencies.billingPeriod.type;
      if (periodType === 'one-time') return '';
      if (periodType === 'monthly') return '/mo';
      if (periodType === 'annually') return '/yr';
      if (periodType === 'per-user') return '/user';
      if (periodType === 'custom' && frequencies.billingPeriod.customText) {
        return frequencies.billingPeriod.customText;
      }
    }
    
    const option = frequencyOptions.find(opt => opt.value === selectedFrequency);
    return option?.priceSuffix || '/mo';
  };
  
  // 获取价格显示
  const getPriceDisplayForTier = (tier: Tier) => {
    return getPriceDisplay(tier, selectedFrequency, frequencies);
  };

  // 获取价格描述
  const getPriceDescriptionForTier = (tier: Tier) => {
    return getPriceDescription(tier, selectedFrequency, frequencies);
  };

  return (
    <section 
      className="relative isolate overflow-hidden bg-gradient-to-br from-background via-primary/5 to-background px-container-x py-section-y"
      style={{
        '--button-radius': '0.5rem', // rounded-lg
        '--button-shadow': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        '--button-hover-shadow': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        '--button-transition-duration-normal': '250ms',
        '--button-hover-scale': '1.02',
      } as React.CSSProperties}
    >
      {/* 静态背景装饰 - 移除动画，保持视觉层次 */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute top-1/4 left-1/4 h-64 w-64 rounded-full bg-primary/8 blur-3xl" />
        <div className="absolute bottom-1/4 right-1/4 h-64 w-64 rounded-full bg-secondary/8 blur-3xl" />
      </div>
      
      <div className="mx-auto max-w-content text-center">
        {tagline && (
          <p className="inline-flex items-center gap-x-2 rounded-full bg-primary/10 px-element-x py-element-y text-body-small/body-small font-medium text-primary ring-1 ring-inset ring-primary/20 transition-colors duration-200 hover:bg-primary/15">
            <SparklesIcon className="h-4 w-4" aria-hidden="true" />
            {tagline}
          </p>
        )}
        <h2 id={id || 'pricing'} className="mt-element-y text-balance text-heading-2/heading-2 font-bold tracking-tight text-foreground">
          {title}
        </h2>
      </div>
      <p className="mx-auto mt-element-y max-w-content text-pretty text-center text-body-large/body-large text-muted-foreground">
        {description}
      </p>
      
      {/* 频率选择开关 */}
      {frequencies?.enabled && frequencyOptions.length > 1 && (
        <div className="mx-auto mt-content-y flex max-w-lg justify-center">
          <div aria-label="Payment frequency" aria-describedby="frequency-description" className="relative flex rounded-full bg-background/90 p-1 shadow-lg ring-1 ring-inset ring-border backdrop-blur-sm">
            <div id="frequency-description" className="sr-only">Select your preferred billing frequency</div>
            {frequencyOptions.map((option) => (
              <button
                key={option.value}
                type="button"
                className={classNames(
                  option.value === selectedFrequency
                    ? 'bg-primary text-primary-foreground shadow-md'
                    : 'text-muted-foreground hover:text-foreground',
                  'relative rounded-full px-element-x py-element-y text-body-small/body-small font-semibold transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2'
                )}
                onClick={() => setSelectedFrequency(option.value)}
              >
                {option.value === 'annually' && (
                  <span className="absolute -top-3 right-0 flex items-center justify-center rounded-full bg-green-500 px-2 py-0.5 text-xs font-medium text-white ring-1 ring-inset ring-green-600/20 transition-transform duration-200 hover:scale-105">
                    Save 20%
                  </span>
                )}
                {option.label}
              </button>
            ))}
          </div>
        </div>
      )}
      
      <div className="mx-auto mt-content-y grid max-w-lg grid-cols-1 gap-content-y lg:max-w-container lg:grid-cols-3 lg:gap-x-content-y">
        {tiers.map((tier, tierIdx) => (
          <div
            key={`tier-${tierIdx}`}
            className={classNames(
              'group relative rounded-2xl p-content-y transition-all duration-300 hover:scale-[1.02] motion-reduce:hover:scale-100 motion-reduce:transition-none',
              tier.mostPopular 
                ? 'bg-gradient-to-br from-primary to-primary/90 text-primary-foreground shadow-2xl ring-2 ring-primary/30 hover:shadow-primary/20' 
                : 'bg-card/90 text-card-foreground shadow-lg ring-1 ring-border hover:ring-border/60 hover:shadow-xl backdrop-blur-sm'
            )}
          >
            {tier.mostPopular && (
              <div className="absolute -top-4 left-1/2 -translate-x-1/2 transform">
                <span className="inline-flex items-center gap-1 rounded-full bg-gradient-to-r from-amber-400 to-orange-500 px-element-x py-1 text-body-small/body-small font-bold text-white shadow-lg transition-transform duration-200 group-hover:scale-105 motion-reduce:group-hover:scale-100">
                  <SparklesIcon className="h-4 w-4" aria-hidden="true" />
                  Most popular
                </span>
              </div>
            )}
            
            <h3
              id={`tier-${tierIdx}`}
              className={classNames(
                tier.mostPopular ? 'text-primary-foreground' : 'text-foreground',
                'text-body-large/body-large font-bold'
              )}
            >
              {tier.name}
            </h3>
            <p className={classNames(
              tier.mostPopular ? 'text-primary-foreground/85' : 'text-muted-foreground',
              'mt-element-y text-body-base/body-base min-h-[56px]'
            )}>
              {tier.description}
            </p>
            
            <div className="mt-content-y flex items-baseline">
              <span className={classNames(
                tier.mostPopular ? 'text-primary-foreground' : 'text-foreground',
                'text-heading-2/heading-2 font-bold tracking-tight'
              )}>
                {formatPrice(getPriceDisplayForTier(tier), getCurrency(tier), '')}
              </span>
              <span className={classNames(
                tier.mostPopular ? 'text-primary-foreground/75' : 'text-muted-foreground',
                'text-body-base/body-base font-semibold'
              )}>
                {getCurrentSuffix()}
              </span>
            </div>
            
            {/* 价格描述 */}
            {getPriceDescriptionForTier(tier) && (
              <p className={classNames(
                tier.mostPopular ? 'text-primary-foreground/75' : 'text-muted-foreground',
                'mt-element-y text-body-small/body-small min-h-[40px]'
              )}>
                {getPriceDescriptionForTier(tier)}
              </p>
            )}
            
            <div className="mt-content-y mb-content-y">
              <Button
                href={tier.button.href}
                variant={tier.mostPopular ? "secondary" : "primary"}
                className="w-full transition-transform duration-200 hover:scale-[1.02] motion-reduce:hover:scale-100 motion-reduce:transition-none"
                aria-describedby={`tier-${tierIdx}`}
                aria-label={`Get ${tier.name} plan`}
                style={tier.mostPopular ? {
                  backgroundColor: 'hsl(var(--primary-foreground))',
                  color: 'hsl(var(--primary))',
                  '--button-hover-bg': 'hsl(var(--primary-foreground) / 0.95)',
                  '--button-hover-shadow': '0 8px 25px rgba(255, 255, 255, 0.25)',
                } as React.CSSProperties : undefined}
              >
                {tier.button.label}
              </Button>
            </div>
            
            <ul
              role="list"
              className={classNames(
                tier.mostPopular ? 'text-primary-foreground/85' : 'text-muted-foreground',
                'space-y-element-y text-body-small/body-small'
              )}
            >
              {tier.features.map((feature, featureIdx) => (
                <li key={`feature-${tierIdx}-${featureIdx}`} className="flex gap-x-element-x">
                  <CheckIcon
                    aria-hidden="true"
                    className={classNames(
                      tier.mostPopular ? 'text-primary-foreground/75' : 'text-primary',
                      'h-6 w-5 flex-none'
                    )}
                  />
                  <span>{feature}</span>
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>

      {/* 自定义解决方案区域 */}
      {customSolution?.enabled && (
        <div className="mx-auto mt-section-y max-w-container">
          <div className="relative overflow-hidden rounded-2xl bg-gradient-to-r from-primary/10 via-primary/5 to-primary/10 p-content-y shadow-lg ring-1 ring-border backdrop-blur-sm transition-all duration-300 hover:shadow-xl hover:ring-border/60">
            <div className="relative flex flex-col items-start gap-content-y lg:flex-row lg:items-center lg:justify-between">
              <div>
                <h3 id="custom-solution" className="text-body-large/body-large font-bold text-foreground">
                  {customSolution.title || 'Need a custom solution?'}
                </h3>
                <p className="mt-element-y text-body-base/body-base text-muted-foreground">
                  {customSolution.description || 'Get in touch with our sales team and we\'ll help you find the perfect solution for your business needs.'}
                </p>
              </div>
              <div className="mt-content-y lg:mt-0 lg:ml-content-y">
                <Button
                  href={customSolution.button?.href || "#"}
                  variant="primary"
                  className="transition-transform duration-200 hover:scale-[1.02] motion-reduce:hover:scale-100 motion-reduce:transition-none"
                  aria-describedby="custom-solution"
                >
                  {customSolution.button?.label || "Contact sales"}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </section>
  );
}
