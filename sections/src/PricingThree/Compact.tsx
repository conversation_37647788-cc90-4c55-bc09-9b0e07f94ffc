'use client'

import { useState, useEffect } from 'react'
import { CheckIcon } from '@heroicons/react/20/solid'
import PriceDisplay, { formatPrice } from './PriceDisplay'
import { PricingThreeSectionProps, Tier, Frequency } from './types'
import { getPriceDisplay, getCurrency, getPriceDescription } from './utils'
import { Button } from '../components/ButtonV2'

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ')
}

export default function Compact({
  id,
  tagline = 'Pricing',
  title = 'Simple, transparent pricing',
  description = 'No hidden fees, no surprises. Choose the plan that works best for you.',
  frequencies,
  tiers = [],
  customSolution
}: PricingThreeSectionProps) {
  // 默认频率为 monthly
  const [selectedFrequency, setSelectedFrequency] = useState<string>('monthly');
  
  // 根据 billingPeriod 设置默认频率
  useEffect(() => {
    if (!frequencies?.enabled && frequencies?.billingPeriod) {
      const periodType = frequencies.billingPeriod.type;
      if (periodType === 'monthly') {
        setSelectedFrequency('monthly');
      } else if (periodType === 'annually') {
        setSelectedFrequency('annually');
      }
    }
  }, [frequencies]);
  
  // 获取频率选项
  const frequencyOptions: Frequency[] = frequencies?.enabled && frequencies.options 
    ? frequencies.options 
    : [{ value: 'monthly', label: 'Monthly', priceSuffix: '/month' }];
  
  // 获取当前频率的后缀
  const getCurrentSuffix = () => {
    // 如果频率选择器关闭但设置了 billingPeriod
    if (!frequencies?.enabled && frequencies?.billingPeriod) {
      const periodType = frequencies.billingPeriod.type;
      if (periodType === 'one-time') return '';
      if (periodType === 'monthly') return '/mo';
      if (periodType === 'annually') return '/yr';
      if (periodType === 'per-user') return '/user';
      if (periodType === 'custom' && frequencies.billingPeriod.customText) {
        return frequencies.billingPeriod.customText;
      }
    }
    
    const option = frequencyOptions.find(opt => opt.value === selectedFrequency);
    return option?.priceSuffix || '/mo';
  };

  return (
    <section 
      className="bg-background py-section-y"
      style={{
        '--button-radius': '0.375rem', // rounded-md
        '--button-transition-duration-normal': '200ms',
      } as React.CSSProperties}
    >
      <div className="mx-auto max-w-container px-container-x">
        <div className="mx-auto max-w-content text-center">
          {tagline && (
            <p className="text-body-small/body-small font-semibold text-primary">{tagline}</p>
          )}
          <h2 id={id || 'pricing'} className="mt-element-y text-heading-3/heading-3 font-bold tracking-tight text-foreground">{title}</h2>
          <p className="mt-element-y text-body-base/body-base text-muted-foreground">{description}</p>
        </div>
        
        {/* 频率选择开关 */}
        {frequencies?.enabled && frequencyOptions.length > 1 && (
          <div className="mt-content-y flex justify-center">
            <div aria-label="Payment frequency" aria-describedby="frequency-description" className="flex items-center gap-x-1 rounded-full bg-muted/80 p-1 text-body-small/body-small font-semibold text-muted-foreground ring-1 ring-inset ring-border">
              <div id="frequency-description" className="sr-only">Select your preferred billing frequency</div>
              {frequencyOptions.map((option) => (
                <button
                  key={option.value}
                  type="button"
                  className={classNames(
                    option.value === selectedFrequency
                      ? 'bg-background text-primary shadow-sm'
                      : 'hover:bg-muted/50',
                    'rounded-full px-element-x py-element-y transition-all duration-200'
                  )}
                  onClick={() => setSelectedFrequency(option.value)}
                >
                  {option.label}
                </button>
              ))}
            </div>
          </div>
        )}
        
        <div className="mx-auto mt-content-y grid max-w-lg grid-cols-1 gap-content-y lg:max-w-container lg:grid-cols-3 lg:gap-x-content-y">
          {tiers.map((tier, tierIdx) => (
            <div key={`tier-${tierIdx}`} className="rounded-lg border border-border bg-card p-content-y">
              <div className="flex items-center justify-between">
                <h3 id={`tier-${tierIdx}`} className="text-body-base/body-base font-semibold text-card-foreground">
                  {tier.name}
                </h3>
                {tier.mostPopular && (
                  <span className="inline-flex items-center rounded-full bg-primary/20 px-element-x py-0.5 text-body-small/body-small font-medium text-primary" aria-label="Most popular plan">
                    Most popular
                  </span>
                )}
              </div>
              
              <div className="mt-element-y">
                <div className="flex items-baseline">
                  <span className="text-heading-3/heading-3 font-bold tracking-tight text-foreground">
                    {formatPrice(getPriceDisplay(tier), getCurrency(tier), '')}
                  </span>
                  <span className="ml-1 text-body-small/body-small font-semibold text-muted-foreground">
                    {getCurrentSuffix()}
                  </span>
                </div>
                
                {/* 价格描述 */}
                {getPriceDescription(tier) && (
                  <p className="mt-element-y text-body-small/body-small text-muted-foreground/80 min-h-[32px]">
                    {getPriceDescription(tier)}
                  </p>
                )}
              </div>
              
              <div className="mt-content-y mb-content-y">
                <Button
                  href={tier.button.href}
                  variant={tier.mostPopular ? "primary" : "outline"}
                  className="w-full"
                  aria-describedby={`tier-${tierIdx}`}
                  aria-label={`Get ${tier.name} plan`}
                >
                  {tier.button.label}
                </Button>
              </div>
              
              <p className="text-body-small/body-small text-muted-foreground">{tier.description}</p>
              
              <ul role="list" className="mt-content-y space-y-element-y text-body-small/body-small text-muted-foreground">
                {tier.features.map((feature, featureIdx) => (
                  <li key={`feature-${tierIdx}-${featureIdx}`} className="flex gap-x-element-x">
                    <CheckIcon aria-hidden="true" className="h-5 w-5 flex-none text-primary" />
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>

      {/* 自定义解决方案区域 */}
      {customSolution?.enabled && (
        <div className="mx-auto mt-section-y max-w-container rounded-lg border border-border bg-card p-content-y lg:flex lg:items-center lg:justify-between">
          <div className="lg:w-2/3">
            <h3 id="custom-solution" className="text-body-large/body-large font-semibold text-card-foreground">
              {customSolution.title || "Need a custom solution?"}
            </h3>
            <p className="mt-element-y text-body-base/body-base text-muted-foreground">
              {customSolution.description || "Get in touch with our sales team and we'll help you find the perfect solution for your business needs."}
            </p>
          </div>
          <div className="mt-content-y lg:mt-0 lg:ml-content-y">
            <Button
              href={customSolution.button?.href || "#"}
              variant="primary"
              aria-describedby="custom-solution"
            >
              {customSolution.button?.label || "Contact sales"}
            </Button>
          </div>
        </div>
      )}
    </section>
  );
}
