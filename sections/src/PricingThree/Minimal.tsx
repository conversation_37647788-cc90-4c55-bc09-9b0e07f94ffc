'use client'

import { useState, useEffect } from 'react'
import { CheckIcon } from '@heroicons/react/20/solid'
import { PricingThreeSectionProps, Tier, Frequency } from './types'
import { formatPrice } from './utils'
import { Button } from '../components/ButtonV2'

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ')
}

export default function Minimal({
  id,
  tagline = 'Pricing',
  title = 'Choose the right plan for you',
  description = 'Choose an affordable plan that\'s packed with the best features for engaging your audience, creating customer loyalty, and driving sales.',
  frequencies,
  tiers = [],
  customSolution
}: PricingThreeSectionProps) {
  // 默认频率为 monthly
  const [selectedFrequency, setSelectedFrequency] = useState<string>('monthly');
  
  // 根据 billingPeriod 设置默认频率
  useEffect(() => {
    if (!frequencies?.enabled && frequencies?.billingPeriod) {
      const periodType = frequencies.billingPeriod.type;
      if (periodType === 'monthly') {
        setSelectedFrequency('monthly');
      } else if (periodType === 'annually') {
        setSelectedFrequency('annually');
      }
    }
  }, [frequencies]);
  
  // 获取频率选项
  const frequencyOptions: Frequency[] = frequencies?.enabled && frequencies.options 
    ? frequencies.options 
    : [{ value: 'monthly', label: 'Monthly', priceSuffix: '/month' }];
  
  // 获取当前频率的后缀
  const getCurrentSuffix = () => {
    // 如果频率选择器关闭但设置了 billingPeriod
    if (!frequencies?.enabled && frequencies?.billingPeriod) {
      const periodType = frequencies.billingPeriod.type;
      if (periodType === 'one-time') return '';
      if (periodType === 'monthly') return '/mo';
      if (periodType === 'annually') return '/yr';
      if (periodType === 'per-user') return '/user';
      if (periodType === 'custom' && frequencies.billingPeriod.customText) {
        return frequencies.billingPeriod.customText;
      }
    }
    
    const option = frequencyOptions.find(opt => opt.value === selectedFrequency);
    return option?.priceSuffix || '/mo';
  };
  
  // 获取价格显示
  const getPriceDisplay = (tier: Tier): number => {
    // 如果频率选择器关闭
    if (!frequencies?.enabled) {
      // 优先使用 amount 字段
      if ('amount' in tier.price && typeof tier.price.amount === 'number') {
        return tier.price.amount;
      }
      
      // 如果没有 amount 字段，但有多频率结构
      if ('monthly' in tier.price) {
        return tier.price.monthly;
      }
      
      return 0;
    }
    
    // 频率选择器开启 - 原有逻辑
    if ('monthly' in tier.price) {
      if (selectedFrequency === 'monthly') {
        return tier.price.monthly;
      } else if ('annually' in tier.price) {
        return tier.price.annually;
      }
    }
    
    // 默认返回0
    return 0;
  };
  
  // 获取币种
  const getCurrency = (tier: Tier): string => {
    if ('currency' in tier.price) {
      return tier.price.currency;
    }
    return 'USD';
  };
  
  // 获取价格描述
  const getPriceDescription = (tier: Tier): string | undefined => {
    // 如果频率选择器关闭
    if (!frequencies?.enabled) {
      // 单一价格结构
      if ('amount' in tier.price && tier.price.description) {
        return tier.price.description;
      }
      return undefined;
    }
    
    // 频率选择器开启 - 根据选择的频率返回对应描述
    if ('monthly' in tier.price) {
      if (selectedFrequency === 'monthly' && tier.price.monthlyDescription) {
        // 替换 {annually} 占位符
        return tier.price.monthlyDescription.replace(
          '{annually}', 
          formatPrice(tier.price.annually / 12, getCurrency(tier))
        );
      } else if (selectedFrequency === 'annually' && tier.price.annuallyDescription) {
        // 替换 {monthly} 占位符
        return tier.price.annuallyDescription.replace(
          '{monthly}', 
          formatPrice(tier.price.monthly, getCurrency(tier))
        );
      }
    }
    
    return undefined;
  };
  
  // 计算年度节省百分比
  const calculateAnnualSavingsPercent = (tier: Tier): number | null => {
    if ('monthly' in tier.price && 'annually' in tier.price && selectedFrequency === 'annually') {
      const monthlyCost = tier.price.monthly * 12;
      const annualCost = tier.price.annually;
      
      if (monthlyCost > 0) {
        return Math.round((1 - (annualCost / monthlyCost)) * 100);
      }
    }
    return null;
  };

  return (
    <section 
      className="relative isolate bg-background px-container-x py-section-y"
      style={{
        '--button-font-weight': '500',
        '--button-text-transform': 'none',
        '--button-letter-spacing': 'normal',
        '--button-transition-duration-normal': '200ms',
      } as React.CSSProperties}
    >
      <div className="mx-auto max-w-content text-center">
        {tagline && (
          <p className="text-body-small/body-small font-medium uppercase tracking-wider text-muted-foreground">{tagline}</p>
        )}
        <h2 id={id || 'pricing'} className="mt-element-y text-balance text-heading-3/heading-3 font-light tracking-tight text-foreground">
          {title}
        </h2>
      </div>
      <p className="mx-auto mt-element-y max-w-content text-pretty text-center text-body-base/body-base text-muted-foreground">
        {description}
      </p>
      
      {/* 频率选择开关 */}
      {frequencies?.enabled && frequencyOptions.length > 1 && (
        <div className="mx-auto mt-content-y flex max-w-lg justify-center">
          <div aria-label="Payment frequency" aria-describedby="frequency-description" className="flex rounded-full bg-muted/50 p-1 ring-1 ring-inset ring-border/50">
            <div id="frequency-description" className="sr-only">Select your preferred billing frequency</div>
            {frequencyOptions.map((option) => (
              <button
                key={option.value}
                type="button"
                className={classNames(
                  option.value === selectedFrequency
                    ? 'bg-background text-foreground shadow-sm ring-1 ring-border'
                    : 'text-muted-foreground hover:text-foreground',
                  'relative rounded-full px-element-x py-element-y text-body-small/body-small font-medium transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2'
                )}
                onClick={() => setSelectedFrequency(option.value)}
              >
                {option.label}
              </button>
            ))}
          </div>
        </div>
      )}
      
      <div className="mx-auto mt-content-y grid max-w-lg grid-cols-1 gap-content-y lg:max-w-container lg:grid-cols-3 lg:gap-x-content-y">
        {tiers.map((tier, tierIdx) => {
          const savingsPercent = calculateAnnualSavingsPercent(tier);
          
          return (
            <div
              key={`tier-${tierIdx}`}
              className={classNames(
                'relative rounded-lg border p-content-y transition-all duration-200',
                tier.mostPopular 
                  ? 'border-primary bg-primary/5 shadow-lg' 
                  : 'border-border bg-card hover:border-border/80 hover:shadow-md'
              )}
            >
              {tier.mostPopular && (
                <span className="inline-flex items-center rounded-full bg-primary px-element-x py-1 text-body-small/body-small font-medium text-primary-foreground" aria-label="Most popular plan">
                  Most popular
                </span>
              )}
              <h3
                id={`tier-${tierIdx}`}
                className={classNames(
                  tier.mostPopular ? 'text-primary' : 'text-foreground', 
                  'text-body-base/body-base font-medium'
                )}
              >
                {tier.name}
              </h3>
              <div className={classNames(
                tier.mostPopular ? 'text-primary' : 'text-foreground',
                'mt-element-y flex items-baseline gap-x-element-x'
              )}>
                <span className="text-heading-2/heading-2 font-light tracking-tight">
                  {formatPrice(getPriceDisplay(tier), getCurrency(tier), '')}
                </span>
                <span className="text-body-small/body-small font-medium text-muted-foreground">
                  {getCurrentSuffix()}
                </span>
              </div>
              
              {savingsPercent && (
                <p className="mt-element-y text-body-small/body-small font-medium text-success">
                  Save {savingsPercent}% with annual billing
                </p>
              )}
              
              {/* 价格描述 */}
              {getPriceDescription(tier) && (
                <p className="mt-element-y text-body-small/body-small text-muted-foreground min-h-[40px]">
                  {getPriceDescription(tier)}
                </p>
              )}
              
              <p className="mt-element-y text-body-small/body-small text-muted-foreground min-h-[48px]">
                {tier.description}
              </p>
              
              <div className="mt-content-y mb-content-y">
                <Button
                  href={tier.button.href}
                  variant={tier.mostPopular ? "primary" : "outline"}
                  className="w-full"
                  aria-describedby={`tier-${tierIdx}`}
                  aria-label={`Get ${tier.name} plan`}
                >
                  {tier.button.label}
                </Button>
              </div>
              
              <ul
                role="list"
                className="space-y-element-y text-body-small/body-small text-muted-foreground"
              >
                {tier.features.map((feature, featureIdx) => (
                  <li key={`feature-${tierIdx}-${featureIdx}`} className="flex gap-x-element-x">
                    <CheckIcon
                      aria-hidden="true"
                      className={classNames(
                        tier.mostPopular ? 'text-primary' : 'text-muted-foreground', 
                        'h-5 w-5 flex-none'
                      )}
                    />
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
          );
        })}
      </div>

      {/* 自定义解决方案区域 */}
      {customSolution?.enabled && (
        <div className="mx-auto mt-section-y max-w-container rounded-lg border border-border bg-card p-content-y lg:flex lg:items-center lg:justify-between">
          <div className="lg:w-2/3">
            <h3 id="custom-solution" className="text-body-large/body-large font-medium text-card-foreground">
              {customSolution.title || "Need a custom solution?"}
            </h3>
            <p className="mt-element-y text-body-base/body-base text-muted-foreground">
              {customSolution.description || "Get in touch with our sales team and we'll help you find the perfect solution for your business needs."}
            </p>
          </div>
          <div className="mt-content-y lg:mt-0 lg:ml-content-y">
            <Button
              href={customSolution.button?.href || "#"}
              variant="primary"
              aria-describedby="custom-solution"
            >
              {customSolution.button?.label || "Contact sales"}
            </Button>
          </div>
        </div>
      )}
    </section>
  );
}
