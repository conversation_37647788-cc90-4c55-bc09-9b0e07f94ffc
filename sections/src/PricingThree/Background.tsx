'use client'

import { useState, useEffect } from 'react'
import { Radio, RadioGroup } from '@headlessui/react'
import { CheckIcon, XMarkIcon } from '@heroicons/react/20/solid'
import PriceDisplay, { formatPrice } from './PriceDisplay'
import { PricingThreeSectionProps, Tier, Frequency } from './types'
import { getPriceDisplay, getCurrency, getPriceDescription } from './utils'
import { Button } from '../components/ButtonV2'

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ')
}

export default function Background({
  id,
  tagline = 'Pricing',
  title = 'Pricing that grows with you',
  description = 'Choose an affordable plan that\'s packed with the best features for engaging your audience, creating customer loyalty, and driving sales.',
  frequencies,
  tiers = [],
  customSolution
}: PricingThreeSectionProps) {
  // 默认频率为 monthly
  const [selectedFrequency, setSelectedFrequency] = useState<string>('monthly');
  
  // 根据 billingPeriod 设置默认频率
  useEffect(() => {
    if (!frequencies?.enabled && frequencies?.billingPeriod) {
      const periodType = frequencies.billingPeriod.type;
      if (periodType === 'monthly') {
        setSelectedFrequency('monthly');
      } else if (periodType === 'annually') {
        setSelectedFrequency('annually');
      }
    }
  }, [frequencies]);
  
  // 获取频率选项
  const frequencyOptions: Frequency[] = frequencies?.enabled && frequencies.options 
    ? frequencies.options 
    : [{ value: 'monthly', label: 'Monthly', priceSuffix: '/month' }];
  
  // 获取当前频率的后缀
  const getCurrentSuffix = () => {
    // 如果频率选择器关闭但设置了 billingPeriod
    if (!frequencies?.enabled && frequencies?.billingPeriod) {
      const periodType = frequencies.billingPeriod.type;
      if (periodType === 'one-time') return '';
      if (periodType === 'monthly') return '/mo';
      if (periodType === 'annually') return '/yr';
      if (periodType === 'per-user') return '/user';
      if (periodType === 'custom' && frequencies.billingPeriod.customText) {
        return frequencies.billingPeriod.customText;
      }
    }
    
    const option = frequencyOptions.find(opt => opt.value === selectedFrequency);
    return option?.priceSuffix || '/mo';
  };
  
  // 获取价格显示
  const getPriceDisplayForTier = (tier: Tier) => {
    return getPriceDisplay(tier, selectedFrequency, frequencies);
  };

  // 获取价格描述
  const getPriceDescriptionForTier = (tier: Tier) => {
    return getPriceDescription(tier, selectedFrequency, frequencies);
  };

  return (
    <section 
      className="isolate overflow-hidden"
      style={{
        '--button-shadow': '0 4px 14px 0 rgba(0, 0, 0, 0.1)',
        '--button-hover-shadow': '0 6px 20px 0 rgba(0, 0, 0, 0.15)',
        '--button-transition-duration-normal': '200ms',
      } as React.CSSProperties}
    >
      <div className="flow-root bg-background pb-section-y pt-section-y">
        <div className="mx-auto max-w-container px-container-x">
          <div className="relative z-10">
            {tagline && (
              <p className="mx-auto text-center text-body-base/body-base font-semibold text-primary">{tagline}</p>
            )}
            <h2 id={id || 'pricing'} className="mx-auto max-w-content text-balance text-center text-heading-2/heading-2 font-semibold tracking-tight text-foreground">
              {title}
            </h2>
            <p className="mx-auto mt-element-y max-w-content text-pretty text-center text-body-large/body-large font-medium text-muted-foreground">
              {description}
            </p>
            
            {/* 频率选择开关 */}
            {frequencies?.enabled && frequencyOptions.length > 1 && (
              <div className="mt-content-y flex justify-center">
                <fieldset aria-label="Payment frequency" aria-describedby="frequency-description">
                  <div id="frequency-description" className="sr-only">Select your preferred billing frequency</div>
                  <RadioGroup
                    value={frequencyOptions.find(opt => opt.value === selectedFrequency) || frequencyOptions[0]}
                    onChange={(option) => setSelectedFrequency(option.value)}
                    className="grid grid-cols-2 gap-x-1 rounded-full p-1 text-center text-body-small/body-small font-semibold ring-1 ring-inset ring-border"
                  >
                    {frequencyOptions.map((option) => (
                      <Radio
                        key={option.value}
                        value={option}
                        className="cursor-pointer rounded-full px-element-x py-element-y text-muted-foreground data-[checked]:bg-primary data-[checked]:text-primary-foreground"
                      >
                        {option.label}
                      </Radio>
                    ))}
                  </RadioGroup>
                </fieldset>
              </div>
            )}
          </div>
          <div className="relative mx-auto mt-content-y grid max-w-md grid-cols-1 gap-y-content-y lg:mx-0 lg:-mb-14 lg:max-w-none lg:grid-cols-3">
            <svg
              viewBox="0 0 1208 1024"
              aria-hidden="true"
              className="absolute -bottom-48 left-1/2 h-[64rem] -translate-x-1/2 translate-y-1/2 [mask-image:radial-gradient(closest-side,hsl(var(--foreground)),transparent)] lg:-top-48 lg:bottom-auto lg:translate-y-0"
            >
              <ellipse cx={604} cy={512} rx={604} ry={512} fill="url(#d25c25d4-6d43-4bf9-b9ac-1842a30a4867)" />
              <defs>
                <radialGradient id="d25c25d4-6d43-4bf9-b9ac-1842a30a4867">
                  <stop stopColor="hsl(var(--primary))" />
                  <stop offset={1} stopColor="hsl(var(--primary))" />
                </radialGradient>
              </defs>
            </svg>
            <div
              aria-hidden="true"
              className="hidden lg:absolute lg:inset-x-px lg:bottom-0 lg:top-4 lg:block lg:rounded-t-2xl lg:bg-muted/80 lg:ring-1 lg:ring-foreground/10"
            />
            {tiers.map((tier, tierIdx) => (
              <div
                key={`tier-${tierIdx}`}
                className={classNames(
                  tier.mostPopular
                    ? 'z-10 bg-card shadow-xl ring-1 ring-border/10'
                    : 'bg-muted/80 ring-1 ring-foreground/10 lg:bg-transparent lg:pb-14 lg:ring-0',
                  'relative rounded-2xl',
                )}
              >
                <div className="relative flex flex-col p-content-y">
                  <div className="flex items-center justify-between gap-x-element-x">
                    <h3
                      id={`tier-${tierIdx}`}
                      className={classNames(
                        tier.mostPopular ? 'text-card-foreground' : 'text-foreground',
                        'text-body-large/body-large font-semibold',
                      )}
                    >
                      {tier.name}
                    </h3>
                    {tier.mostPopular ? (
                      <p className="rounded-full bg-primary/10 px-element-x py-1 text-body-small/body-small font-semibold text-primary" aria-label="Most popular plan">
                        Most popular
                      </p>
                    ) : null}
                  </div>
                  <p className={classNames(tier.mostPopular ? 'text-muted-foreground' : 'text-muted-foreground', 'mt-element-y text-body-small/body-small')}>
                    {tier.description}
                  </p>
                  <div className="mt-element-y flex items-center gap-x-element-x">
                    <div className={classNames(
                      tier.mostPopular ? 'text-card-foreground' : 'text-foreground',
                      'text-heading-3/heading-3 font-semibold tracking-tight',
                    )}>
                      <PriceDisplay 
                        price={getPriceDisplayForTier(tier)}
                        currency={getCurrency(tier)}
                        suffix={getCurrentSuffix()}
                        description={getPriceDescriptionForTier(tier)}
                        className=""
                        priceClassName={tier.mostPopular ? 'text-card-foreground font-bold' : 'text-foreground font-bold'}
                      />
                    </div>
                  </div>
                  <div className="mt-content-y mb-content-y">
                    <Button
                      href={tier.button.href}
                      variant={tier.mostPopular ? "primary" : "outline"}
                      className="w-full"
                      aria-describedby={`tier-${tierIdx}`}
                      aria-label={`Get ${tier.name} plan`}
                    >
                      {tier.button.label}
                    </Button>
                  </div>
                  <div className="flow-root">
                    <ul
                      role="list"
                      className={classNames(
                        tier.mostPopular
                          ? 'divide-border/5 border-border/5 text-muted-foreground'
                          : 'divide-foreground/5 border-foreground/5 text-foreground',
                        'divide-y border-t text-body-small/body-small lg:border-t-0',
                      )}
                    >
                      {tier.features.map((feature, featureIdx) => (
                        <li key={`feature-${tierIdx}-${featureIdx}`} className="flex gap-x-element-x py-element-y">
                          <CheckIcon
                            aria-hidden="true"
                            className={classNames(
                              tier.mostPopular ? 'text-primary' : 'text-muted-foreground',
                              'h-6 w-5 flex-none',
                            )}
                          />
                          <span>{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 自定义解决方案区域 */}
        {customSolution?.enabled && (
          <div className="mx-auto mt-section-y max-w-container px-container-x pb-content-y">
            <div className="rounded-xl bg-gradient-to-r from-muted to-muted/70 p-content-y text-foreground lg:flex lg:items-center lg:justify-between">
              <div className="lg:w-2/3">
                <h3 id="custom-solution" className="text-body-large/body-large font-semibold text-foreground">
                  {customSolution.title || "Need a custom solution?"}
                </h3>
                <p className="mt-element-y text-body-base/body-base text-muted-foreground">
                  {customSolution.description || "Get in touch with our sales team and we'll help you find the perfect solution for your business needs."}
                </p>
              </div>
              <div className="mt-content-y lg:mt-0 lg:ml-content-y">
                <Button
                  href={customSolution.button?.href || "#"}
                  variant="primary"
                  aria-describedby="custom-solution"
                >
                  {customSolution.button?.label || "Contact sales"}
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </section>
  )
}
