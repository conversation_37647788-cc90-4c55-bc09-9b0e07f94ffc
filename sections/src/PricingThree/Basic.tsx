'use client'

import { useState, useEffect } from 'react'
import { Radio, RadioGroup } from '@headlessui/react'
import { CheckIcon } from '@heroicons/react/20/solid'
import PriceDisplay, { formatPrice } from './PriceDisplay'
import { PricingThreeSectionProps, Tier, Frequency } from './types'
import { getPriceDisplay, getCurrency, getPriceDescription } from './utils'
import { Button } from '../components/ButtonV2'

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ')
}

export default function Basic({
  id,
  tagline = 'Pricing',
  title = 'Pricing that grows with you',
  description = 'Choose an affordable plan that\'s packed with the best features for engaging your audience, creating customer loyalty, and driving sales.',
  frequencies,
  tiers = [],
  customSolution
}: PricingThreeSectionProps) {
  // 默认频率为 monthly
  const [selectedFrequency, setSelectedFrequency] = useState<string>('monthly');
  
  // 根据 billingPeriod 设置默认频率
  useEffect(() => {
    if (!frequencies?.enabled && frequencies?.billingPeriod) {
      const periodType = frequencies.billingPeriod.type;
      if (periodType === 'monthly') {
        setSelectedFrequency('monthly');
      } else if (periodType === 'annually') {
        setSelectedFrequency('annually');
      }
    }
  }, [frequencies]);
  
  // 获取频率选项
  const frequencyOptions: Frequency[] = frequencies?.enabled && frequencies.options 
    ? frequencies.options 
    : [{ value: 'monthly', label: 'Monthly', priceSuffix: '/month' }];
  
  // 获取当前频率的后缀
  const getCurrentSuffix = () => {
    // 如果频率选择器关闭但设置了 billingPeriod
    if (!frequencies?.enabled && frequencies?.billingPeriod) {
      const periodType = frequencies.billingPeriod.type;
      if (periodType === 'one-time') return '';
      if (periodType === 'monthly') return '/mo';
      if (periodType === 'annually') return '/yr';
      if (periodType === 'per-user') return '/user';
      if (periodType === 'custom' && frequencies.billingPeriod.customText) {
        return frequencies.billingPeriod.customText;
      }
    }
    
    const option = frequencyOptions.find(opt => opt.value === selectedFrequency);
    return option?.priceSuffix || '/mo';
  };
  
  // 获取价格显示
  const getPriceDisplayForTier = (tier: Tier) => {
    return getPriceDisplay(tier, selectedFrequency, frequencies);
  };

  // 获取价格描述
  const getPriceDescriptionForTier = (tier: Tier) => {
    return getPriceDescription(tier, selectedFrequency, frequencies);
  };

  // 获取币种
  const getCurrency = (tier: Tier): string => {
    if ('currency' in tier.price) {
      return tier.price.currency;
    }
    return 'USD';
  };

  return (
    <section 
      className="bg-background px-container-x py-section-y"
      style={{
        '--button-shadow': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
        '--button-hover-shadow': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
      } as React.CSSProperties}
    >
      <div className="mx-auto max-w-container">
        <div className="mx-auto max-w-content text-center">
          {tagline && (
            <p className="text-body-base/body-base font-semibold text-primary">{tagline}</p>
          )}
          <h2 id={id || 'pricing'} className="mt-element-y text-balance text-heading-2/heading-2 font-semibold tracking-tight text-foreground">
            {title}
          </h2>
        </div>
        <p className="mx-auto mt-element-y max-w-content text-pretty text-center text-body-large/body-large font-medium text-muted-foreground">
          {description}
        </p>
        
        {/* 频率选择开关 */}
        {frequencies?.enabled && frequencyOptions.length > 1 && (
          <div className="mt-content-y flex justify-center">
            <fieldset aria-label="Payment frequency" aria-describedby="frequency-description">
              <div id="frequency-description" className="sr-only">Select your preferred billing frequency</div>
              <RadioGroup
                value={frequencyOptions.find(opt => opt.value === selectedFrequency) || frequencyOptions[0]}
                onChange={(option) => setSelectedFrequency(option.value)}
                className="grid grid-cols-2 gap-x-1 rounded-full p-1 text-center text-body-small/body-small font-semibold ring-1 ring-inset ring-border"
              >
                {frequencyOptions.map((option) => (
                  <Radio
                    key={option.value}
                    value={option}
                    className="cursor-pointer rounded-full px-element-x py-element-y text-muted-foreground data-[checked]:bg-primary data-[checked]:text-primary-foreground"
                  >
                    {option.label}
                  </Radio>
                ))}
              </RadioGroup>
            </fieldset>
          </div>
        )}
        
        <div className="mx-auto mt-content-y grid max-w-lg grid-cols-1 gap-content-y lg:max-w-container lg:grid-cols-3 lg:gap-x-content-y">
          {tiers.map((tier, tierIdx) => (
            <div
              key={`tier-${tierIdx}`}
              className="flex flex-col justify-between rounded-2xl bg-card p-content-y shadow-sm ring-1 ring-border"
            >
              <div>
                <div className="flex items-center justify-between gap-x-element-x">
                  <h3 id={`tier-${tierIdx}`} className="text-body-base/body-base font-semibold text-card-foreground">
                    {tier.name}
                  </h3>
                  {tier.mostPopular ? (
                    <p className="rounded-full bg-primary/10 px-element-x py-1 text-body-small/body-small font-semibold text-primary" aria-label="Most popular plan">
                      Most popular
                    </p>
                  ) : null}
                </div>
                <p className="mt-element-y text-body-small/body-small text-muted-foreground">{tier.description}</p>
                <PriceDisplay 
                  price={getPriceDisplayForTier(tier)}
                  currency={getCurrency(tier)}
                  suffix={getCurrentSuffix()}
                  description={getPriceDescriptionForTier(tier)}
                />
              </div>
              <div>
                <div className="mt-content-y mb-content-y">
                  <Button
                    href={tier.button.href}
                    variant={tier.mostPopular ? "primary" : "outline"}
                    className="w-full"
                    aria-describedby={`tier-${tierIdx}`}
                    aria-label={`Get ${tier.name} plan`}
                  >
                    {tier.button.label}
                  </Button>
                </div>
                <ul role="list" className="space-y-element-y text-body-small/body-small text-muted-foreground">
                  {tier.features.map((feature, featureIdx) => (
                    <li key={`feature-${tierIdx}-${featureIdx}`} className="flex gap-x-element-x">
                      <CheckIcon aria-hidden="true" className="h-6 w-5 flex-none text-primary" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 自定义解决方案区域 */}
      {customSolution?.enabled && (
        <div className="mx-auto mt-section-y max-w-container rounded-lg border border-border bg-card p-element-x py-element-y sm:p-content-y lg:flex lg:items-center lg:justify-between">
          <div className="lg:w-2/3">
            <h3 id="custom-solution" className="text-body-large/body-large font-semibold text-card-foreground">
              {customSolution.title || "Need a custom solution?"}
            </h3>
            <p className="mt-element-y text-body-base/body-base text-muted-foreground">
              {customSolution.description || "Get in touch with our sales team and we'll help you find the perfect solution for your business needs."}
            </p>
          </div>
          <div className="mt-content-y lg:mt-0 lg:ml-content-y">
            <Button
              href={customSolution.button?.href || "#"}
              variant="primary"
              aria-describedby="custom-solution"
            >
              {customSolution.button?.label || "Contact sales"}
            </Button>
          </div>
        </div>
      )}
    </section>
  )
}
