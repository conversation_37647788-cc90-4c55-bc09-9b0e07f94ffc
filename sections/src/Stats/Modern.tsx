import type { StatItem, StatsSectionProps } from './types';

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ');
}

const defaultStats: StatItem[] = [
  { id: 1, name: 'Transactions every 24 hours', value: '44 million' },
  { id: 2, name: 'Assets under holding', value: '$119 trillion' },
  { id: 3, name: 'New users annually', value: '46,000' },
  { id: 4, name: 'Uptime guarantee', value: '99.9%' },
];

export default function Modern({
  tagline = "Key Metrics",
  title = "Our platform by the numbers",
  description = "We've helped thousands of companies reach their business goals. Here's a snapshot of our impact.",
  stats = defaultStats
}: StatsSectionProps) {
  // 确保每个统计项都有唯一ID
  const safeStats = stats.map((stat, index) => ({
    ...stat,
    id: stat.id || index + 1
  }));

  // 根据统计项数量确定布局
  const gridCols = safeStats.length === 3 
    ? "grid-cols-1 md:grid-cols-3" 
    : safeStats.length === 2 
      ? "grid-cols-1 md:grid-cols-2" 
      : "grid-cols-1 md:grid-cols-2 lg:grid-cols-4";

  return (
    <section className="relative isolate overflow-hidden bg-gradient-to-b from-primary/10 via-background to-background py-section-y" aria-labelledby="stats-title">
      <div className="mx-auto max-w-container px-container-x">
        {/* 背景装饰 */}
        <div className="absolute inset-0 -z-10 bg-[radial-gradient(45rem_50rem_at_top,theme(colors.primary.100),theme(colors.background))] opacity-20" />
        <div className="absolute inset-y-0 right-1/2 -z-10 mr-16 w-[200%] origin-bottom-left skew-x-[-30deg] bg-background shadow-xl shadow-primary/10 ring-1 ring-primary/10 sm:mr-28 lg:mr-0 xl:mr-16 xl:origin-center" />
        
        <div className="mx-auto max-w-content text-center">
          {tagline && (
            <span className="inline-flex items-center rounded-full bg-primary/10 px-4 py-1.5 text-body-small/body-small font-medium text-primary ring-1 ring-inset ring-primary/20">
              {tagline}
            </span>
          )}
          <h2 
            id="stats-title"
            className="mt-element-y text-balance text-heading-2/heading-2 font-medium tracking-tight text-foreground"
          >
            {title}
          </h2>
        </div>
        <p className="mx-auto mt-element-y max-w-content text-pretty text-center text-body-large/body-large text-muted-foreground">
          {description}
        </p>
        
        <div className="mx-auto mt-content-y max-w-content">
          <div 
            className={`grid ${gridCols} gap-element-y`}
            aria-label="Company statistics"
          >
            {safeStats.map((stat) => (
              <div
                key={stat.id}
                className="flex flex-col bg-card text-center p-element-y rounded-2xl ring-1 ring-border hover:ring-primary/20 hover:shadow-lg transition-all duration-300"
              >
                <dt className="text-body-small/body-small font-semibold text-primary min-h-[2.5rem] flex items-center justify-center">{stat.name}</dt>
                <dd className="order-first text-heading-3/heading-3 font-semibold tracking-tight text-foreground">{stat.value}</dd>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
