import type { StatItem, StatsSectionProps } from './types';

const defaultStats: StatItem[] = [
  { id: 1, name: 'Creators on the platform', value: '8,000+' },
  { id: 2, name: 'Flat platform fee', value: '3%' },
  { id: 3, name: 'Uptime guarantee', value: '99.9%' },
  { id: 4, name: 'Paid out to creators', value: '$70M' },
];
  
export default function SimpleGrid({
  tagline = "Platform Statistics",
  title = "Trusted by creators worldwide",
  description = "Lorem ipsum dolor sit amet consect adipisicing possimus.",
  stats = defaultStats
}: StatsSectionProps) {
  // 确保每个统计项都有唯一ID
  const safeStats = stats.map((stat, index) => ({
    ...stat,
    id: stat.id || index + 1
  }));

  // 根据统计项数量动态设置网格列数
  const gridColsClass = 
    safeStats.length === 3 ? 'sm:grid-cols-3' : 
    safeStats.length === 2 ? 'sm:grid-cols-2' : 
    'sm:grid-cols-2 lg:grid-cols-4';

  return (
    <section className="bg-background py-section-y" aria-labelledby="stats-title">
      <div className="mx-auto max-w-container px-container-x">
        <div className="mx-auto max-w-content text-center">
          {tagline && <p className="text-body-base/body-base font-semibold text-primary">{tagline}</p>}
          {title && (
            <h2 id="stats-title" className="mt-element-y text-balance text-heading-2/heading-2 font-semibold tracking-tight text-foreground">
              {title}
            </h2>
          )}
          {description && <p className="mt-element-y text-body-large/body-large text-muted-foreground">{description}</p>}
        </div>
        <div className="mx-auto mt-content-y max-w-content">
          <dl 
            className={`grid grid-cols-1 gap-0.5 overflow-hidden rounded-2xl text-center ${gridColsClass}`}
            aria-label="Company statistics"
          >
            {safeStats.map((stat) => (
              <div key={stat.id} className="flex flex-col bg-accent/40 p-8">
                <dt className="text-body-small/body-small font-semibold text-muted-foreground min-h-[2.5rem] flex items-center justify-center">{stat.name}</dt>
                <dd className="order-first text-heading-3/heading-3 font-semibold tracking-tight text-foreground">{stat.value}</dd>
              </div>
            ))}
          </dl>
        </div>
      </div>
    </section>
  );
}