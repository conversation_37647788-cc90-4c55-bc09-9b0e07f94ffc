import type { StatItem, StatsSectionProps } from './types';

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ')
}

// 为每个卡片定义不同的颜色主题（亮色和深色模式）
const cardThemes = [
  {
    bgGradient: 'bg-gradient-to-br from-pink-500 to-rose-500',
    textColor: 'text-white',
    secondaryTextColor: 'text-white/90',
    valueColor: 'text-white font-bold',
    ringColor: 'ring-pink-400/30',
    hoverShadow: 'hover:shadow-pink-500/30',
  },
  {
    bgGradient: 'bg-gradient-to-br from-blue-500 to-cyan-400',
    textColor: 'text-white',
    secondaryTextColor: 'text-white/90',
    valueColor: 'text-white font-bold',
    ringColor: 'ring-blue-400/30',
    hoverShadow: 'hover:shadow-blue-500/30',
  },
  {
    bgGradient: 'bg-gradient-to-br from-purple-500 to-indigo-500',
    textColor: 'text-white',
    secondaryTextColor: 'text-white/90',
    valueColor: 'text-white font-bold',
    ringColor: 'ring-purple-400/30',
    hoverShadow: 'hover:shadow-purple-500/30',
  },
  {
    bgGradient: 'bg-gradient-to-br from-emerald-500 to-teal-400',
    textColor: 'text-white',
    secondaryTextColor: 'text-white/90',
    valueColor: 'text-white font-bold',
    ringColor: 'ring-emerald-400/30',
    hoverShadow: 'hover:shadow-emerald-500/30',
  }
];

const defaultStats: StatItem[] = [
  { id: 1, name: 'Transactions every 24 hours', value: '44 million' },
  { id: 2, name: 'Assets under holding', value: '$119 trillion' },
  { id: 3, name: 'New users annually', value: '46,000' },
];

export default function Colorful({
  tagline = "Key Metrics",
  title = "Our platform by the numbers",
  description = "We've helped thousands of companies reach their business goals. Here's a snapshot of our impact.",
  stats = defaultStats
}: StatsSectionProps) {
  // 确保每个统计项都有唯一ID
  const safeStats = stats.map((stat, index) => ({
    ...stat,
    id: stat.id || index + 1
  }));

  // 根据统计项数量确定布局
  const gridCols = safeStats.length === 3 
    ? "grid-cols-1 md:grid-cols-3" 
    : safeStats.length === 2 
      ? "grid-cols-1 md:grid-cols-2" 
      : "grid-cols-1 md:grid-cols-2 lg:grid-cols-4";

  return (
    <section className="relative isolate overflow-hidden bg-background dark:bg-gray-900 py-section-y" aria-labelledby="stats-title">
      <div className="mx-auto max-w-container px-container-x">
        {/* 背景装饰 */}
        <div 
          className="absolute inset-0 -z-10 opacity-30"
          style={{
            backgroundImage: 'radial-gradient(circle at 30% 20%, rgba(255, 128, 128, 0.8), transparent 35%), radial-gradient(circle at 70% 60%, rgba(128, 128, 255, 0.8), transparent 35%)',
            backgroundSize: '100% 100%',
            filter: 'blur(50px)'
          }}
        />
        <div
          className="absolute inset-y-0 right-1/2 -z-10 mr-16 w-[200%] origin-bottom-left skew-x-[-30deg] bg-accent/80 dark:bg-gray-800/80 shadow-xl shadow-primary/10 ring-1 ring-border dark:ring-white/10"
          aria-hidden="true"
        />
        
        <div className="mx-auto max-w-content text-center">
          {tagline && (
            <div className="inline-flex items-center justify-center">
              <span className="relative inline-flex overflow-hidden rounded-full p-[1px]">
                <span className="absolute inset-[-1000%] animate-[spin_2s_linear_infinite] bg-[conic-gradient(from_90deg_at_50%_50%,#e879f9_0%,#8b5cf6_50%,#22d3ee_100%)]" />
                <div className="inline-flex h-full w-full cursor-pointer items-center justify-center rounded-full bg-background dark:bg-gray-900 px-5 py-1 text-body-small/body-small font-medium text-primary dark:text-purple-400 backdrop-blur-3xl">
                  {tagline}
                </div>
              </span>
            </div>
          )}
          <h2 
            id="stats-title"
            className="mt-element-y text-balance text-heading-2/heading-2 font-medium tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-500 dark:from-pink-300 dark:via-purple-300 dark:to-indigo-300"
          >
            {title}
          </h2>
        </div>
        <p className="mx-auto mt-element-y max-w-content text-pretty text-center text-body-large/body-large text-muted-foreground dark:text-gray-300 whitespace-normal break-words">
          {description}
        </p>
        
        <div className="mx-auto mt-content-y max-w-content">
          <div 
            className={`grid ${gridCols} gap-element-y`}
            aria-label="Company statistics"
          >
            {safeStats.map((stat, statIdx) => {
              // 使用预定义的主题
              const theme = cardThemes[statIdx % cardThemes.length];
              
              return (
                <div
                  key={stat.id}
                  className={classNames(
                    theme.bgGradient,
                    theme.ringColor,
                    theme.hoverShadow,
                    'relative rounded-3xl p-element-y shadow-2xl transform transition-all duration-300 hover:scale-[1.02] hover:shadow-2xl ring-1 flex flex-col text-center'
                  )}
                >
                  <dt className={classNames(theme.secondaryTextColor, 'text-body-small/body-small font-semibold min-h-[2.5rem] flex items-center justify-center')}>
                    {stat.name}
                  </dt>
                  <dd className={classNames(theme.valueColor, 'order-first text-heading-3/heading-3 font-semibold tracking-tight')}>
                    {stat.value}
                  </dd>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
}
