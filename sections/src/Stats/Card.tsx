import type { StatItem, StatsSectionProps } from './types';

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ')
}

const defaultStats: StatItem[] = [
  { id: 1, name: 'Transactions every 24 hours', value: '44 million' },
  { id: 2, name: 'Assets under holding', value: '$119 trillion' },
  { id: 3, name: 'New users annually', value: '46,000' },
  { id: 4, name: 'Customer satisfaction', value: '99.9%' },
];

export default function Card({
  tagline = "Key Metrics",
  title = "Our platform by the numbers",
  description = "We've helped thousands of companies reach their business goals. Here's a snapshot of our impact.",
  stats = defaultStats
}: StatsSectionProps) {
  // 确保每个统计项都有唯一ID
  const safeStats = stats.map((stat, index) => ({
    ...stat,
    id: stat.id || index + 1
  }));

  // 根据统计项数量确定布局
  const gridCols = safeStats.length === 3 
    ? "grid-cols-1 md:grid-cols-3" 
    : safeStats.length === 2 
      ? "grid-cols-1 md:grid-cols-2" 
      : "grid-cols-1 md:grid-cols-2 lg:grid-cols-4";

  return (
    <section className="bg-background py-section-y" aria-labelledby="stats-title">
      <div className="mx-auto max-w-container px-container-x">
        <div className="mx-auto max-w-content text-center">
          {tagline && (
            <p className="text-body-base/body-base font-semibold text-primary">{tagline}</p>
          )}
          <h2 
            id="stats-title"
            className="mt-element-y text-balance text-heading-2/heading-2 font-medium tracking-tight text-foreground"
          >
            {title}
          </h2>
        </div>
        {description && (
          <p className="mx-auto mt-element-y max-w-content text-center text-body-large/body-large text-muted-foreground">
            {description}
          </p>
        )}

        <div className="mx-auto mt-content-y max-w-content">
          <div 
            className={`grid ${gridCols} gap-element-y`}
            aria-label="Company statistics"
          >
            {safeStats.map((stat, statIdx) => {
              // 移动端（单列）圆角处理
              let mobileRounded = '';
              if (statIdx === 0) mobileRounded = 'rounded-t-3xl ';
              if (statIdx === safeStats.length - 1) mobileRounded = 'rounded-b-3xl ';
              
              // 桌面端圆角处理
              let desktopRounded = '';
              
              if (safeStats.length === 3) {
                // 三个项目的情况
                if (statIdx === 0) {
                  desktopRounded = 'md:rounded-l-3xl md:rounded-r-none ';
                } else if (statIdx === 1) {
                  desktopRounded = 'md:rounded-none ';
                } else if (statIdx === 2) {
                  desktopRounded = 'md:rounded-r-3xl md:rounded-l-none ';
                }
              } else if (safeStats.length === 2) {
                // 两个项目的情况
                if (statIdx === 0) {
                  desktopRounded = 'md:rounded-l-3xl md:rounded-r-none ';
                } else if (statIdx === 1) {
                  desktopRounded = 'md:rounded-r-3xl md:rounded-l-none ';
                }
              } else if (safeStats.length === 4) {
                // 四个项目的情况
                // 中等屏幕 (md) - 2x2 布局
                if (statIdx === 0) {
                  desktopRounded = 'md:rounded-tl-3xl ';
                } else if (statIdx === 1) {
                  desktopRounded = 'md:rounded-tr-3xl ';
                } else if (statIdx === 2) {
                  desktopRounded = 'md:rounded-bl-3xl ';
                } else if (statIdx === 3) {
                  desktopRounded = 'md:rounded-br-3xl ';
                }
                
                // 大屏幕 (lg) - 1x4 布局
                if (statIdx === 0) {
                  desktopRounded += 'lg:rounded-l-3xl lg:rounded-tr-none lg:rounded-br-none ';
                } else if (statIdx === 1 || statIdx === 2) {
                  desktopRounded += 'lg:rounded-none ';
                } else if (statIdx === 3) {
                  desktopRounded += 'lg:rounded-r-3xl lg:rounded-tl-none lg:rounded-bl-none ';
                }
              }
              
              return (
                <div
                  key={stat.id}
                  className={classNames(
                    mobileRounded,
                    desktopRounded,
                    'flex flex-col p-element-y bg-card shadow-xl ring-1 ring-border text-center',
                    safeStats.length === 4 ? 'lg:p-content-y' : ''
                  )}
                >
                  <dt className="text-body-small/body-small font-semibold text-muted-foreground min-h-[2.5rem] flex items-center justify-center">
                    {stat.name}
                  </dt>
                  <dd className="order-first text-heading-3/heading-3 font-semibold tracking-tight text-foreground">
                    {stat.value}
                  </dd>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
}
