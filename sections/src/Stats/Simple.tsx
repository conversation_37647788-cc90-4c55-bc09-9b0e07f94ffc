import type { StatItem, StatsSectionProps } from './types';

const defaultStats: StatItem[] = [
  { id: 1, name: 'Transactions every 24 hours', value: '44 million' },
  { id: 2, name: 'Assets under holding', value: '$119 trillion' },
  { id: 3, name: 'New users annually', value: '46,000' },
];

export default function Simple({
  tagline = "Data-Driven Growth",
  title = "Trusted by creators worldwide",
  description = "Lorem ipsum dolor sit amet consect adipisicing possimus.",
  stats = defaultStats
}: StatsSectionProps) {
  // 确保每个统计项都有唯一ID
  const safeStats = stats.map((stat, index) => ({
    ...stat,
    id: stat.id || index + 1
  }));

  // 根据统计项数量动态设置网格列数
  const gridColsClass = 
    safeStats.length === 3 ? 'md:grid-cols-3' : 
    safeStats.length === 2 ? 'md:grid-cols-2' : 
    'md:grid-cols-2 lg:grid-cols-4';

  return (
    <section className="bg-background py-section-y" aria-labelledby="stats-title">
      <div className="mx-auto max-w-container px-container-x">
        <div className="mx-auto max-w-content text-center">
          {tagline && <p className="text-body-base/body-base font-semibold text-primary">{tagline}</p>}
          {title && (
            <h2 id="stats-title" className="text-balance text-heading-2/heading-2 font-semibold tracking-tight text-foreground">
              {title}
            </h2>
          )}
          {description && <p className="mt-element-y text-body-large/body-large text-muted-foreground">{description}</p>}
        </div>
        <div className="mx-auto mt-content-y max-w-content">
          <dl 
            className={`grid grid-cols-1 gap-x-content-x gap-y-content-y text-center ${gridColsClass}`}
            aria-label="Company statistics"
          >
            {safeStats.map((stat) => (
              <div key={stat.id} className="mx-auto flex flex-col gap-y-element-y">
                <dt className="text-body-base/body-base text-muted-foreground min-h-[2.5rem] flex items-center justify-center">{stat.name}</dt>
                <dd className="order-first text-heading-2/heading-2 font-semibold tracking-tight text-foreground">
                  {stat.value}
                </dd>
              </div>
            ))}
          </dl>
        </div>
      </div>
    </section>
  );
}
