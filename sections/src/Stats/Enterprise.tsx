import type { StatItem, StatsSectionProps } from './types';

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ');
}

const defaultStats: StatItem[] = [
  { id: 1, name: 'Transactions every 24 hours', value: '44 million' },
  { id: 2, name: 'Assets under holding', value: '$119 trillion' },
  { id: 3, name: 'New users annually', value: '46,000' },
  { id: 4, name: 'Uptime guarantee', value: '99.9%' },
];

export default function Enterprise({
  tagline = "Key Metrics",
  title = "Enterprise-grade reliability",
  description = "We've built our platform with enterprise needs in mind. Our infrastructure is designed to handle the most demanding workloads with ease.",
  stats = defaultStats
}: StatsSectionProps) {
  // 确保每个统计项都有唯一ID
  const safeStats = stats.map((stat, index) => ({
    ...stat,
    id: stat.id || index + 1
  }));

  // 根据统计项数量确定布局
  const gridCols = safeStats.length === 3 
    ? "grid-cols-1 md:grid-cols-3" 
    : safeStats.length === 2 
      ? "grid-cols-1 md:grid-cols-2" 
      : "grid-cols-1 md:grid-cols-2 lg:grid-cols-4";

  return (
    <section className="relative isolate overflow-hidden bg-background dark:bg-gray-900 py-section-y" aria-labelledby="stats-title">
      {/* 背景装饰 */}
      <div className="hidden sm:absolute sm:-top-10 sm:right-1/2 sm:-z-10 sm:mr-10 sm:block sm:transform-gpu sm:blur-3xl">
        <div
          className="aspect-[1097/845] w-[68.5625rem] bg-gradient-to-tr from-[#ff4694] to-[#776fff] opacity-20"
          style={{
            clipPath:
              'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)',
          }}
        />
      </div>
      <div className="absolute -top-52 left-1/2 -z-10 -translate-x-1/2 transform-gpu blur-3xl sm:top-[-28rem] sm:ml-16 sm:translate-x-0 sm:transform-gpu">
        <div
          className="aspect-[1097/845] w-[68.5625rem] bg-gradient-to-tr from-[#ff4694] to-[#776fff] opacity-20"
          style={{
            clipPath:
              'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)',
          }}
        />
      </div>

      <div className="mx-auto max-w-container px-container-x">
        <div className="mx-auto max-w-content text-center lg:mx-0 lg:text-left">
          {tagline && <p className="text-body-base/body-base font-semibold text-primary dark:text-white">{tagline}</p>}
          <h2 id="stats-title" className="mt-element-y text-balance text-heading-2/heading-2 font-medium tracking-tight text-foreground dark:text-white">{title}</h2>
          <p className="mt-element-y text-pretty text-body-large/body-large text-muted-foreground dark:text-gray-300">
            {description}
          </p>
        </div>
        
        <div 
          className={`mx-auto mt-content-y grid ${gridCols} gap-element-y max-w-content`}
          aria-label="Company statistics"
        >
          {safeStats.map((stat) => (
            <div
              key={stat.id}
              className="flex flex-col rounded-xl bg-primary/5 p-element-y text-center backdrop-blur-sm ring-1 ring-border/10 dark:ring-white/10"
            >
              <dt className="text-body-small/body-small font-semibold text-muted-foreground dark:text-gray-300 min-h-[2.5rem] flex items-center justify-center">{stat.name}</dt>
              <dd className="order-first text-heading-3/heading-3 font-semibold tracking-tight text-foreground dark:text-white">{stat.value}</dd>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
