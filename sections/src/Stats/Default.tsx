import type { StatsSectionProps } from './types';

const defaultStats = [
  { id: 1, name: 'Transactions every 24 hours', value: '44 million' },
  { id: 2, name: 'Assets under holding', value: '$119 trillion' },
  { id: 3, name: 'New users annually', value: '46,000' },
];

export default function Default({
  tagline = "Data-Driven Growth",
  title = "Trusted by creators worldwide",
  description = "Lorem ipsum dolor sit amet consect adipisicing possimus.",
  stats = defaultStats
}: StatsSectionProps) {
  // 根据统计项数量动态设置网格列数
  const gridColsClass = 
    stats.length === 3 ? 'sm:grid-cols-3' : 
    stats.length === 2 ? 'sm:grid-cols-2' : 
    'sm:grid-cols-2 lg:grid-cols-4';
    
  return (
    <section className="bg-background py-section-y" aria-labelledby="stats-title">
      <div className="mx-auto max-w-container px-container-x">
        <div className="mx-auto max-w-content text-center">
          <div className="text-center">
            {tagline && (
              <p className="text-body-base/body-base font-semibold text-primary">{tagline}</p>
            )}
            <h2 
              id="stats-title" 
              className="mt-element-y text-heading-2/heading-2 font-medium tracking-tight text-foreground"
            >
              {title}
            </h2>
            {description && (
              <p className="mt-element-y text-body-large/body-large text-muted-foreground">
                {description}
              </p>
            )}
          </div>
        </div>
        <div className="mx-auto mt-content-y max-w-content">
          <dl 
            className={`grid grid-cols-1 gap-0.5 overflow-hidden rounded-2xl text-center ${gridColsClass}`}
            aria-label="Company statistics"
          >
            {stats.map((stat) => (
              <div key={stat.id} className="flex flex-col bg-muted p-element-y">
                <dt className="text-body-small/body-small font-semibold text-muted-foreground min-h-[2.5rem] flex items-center justify-center">{stat.name}</dt>
                <dd className="order-first text-heading-3/heading-3 font-semibold tracking-tight text-foreground">{stat.value}</dd>
              </div>
            ))}
          </dl>
        </div>
      </div>
    </section>
  );
}
