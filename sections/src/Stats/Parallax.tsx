'use client'

import { motion } from 'motion/react'
import type { StatItem, StatsSectionProps } from './types';

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ')
}

const defaultStats: StatItem[] = [
  { id: 1, name: 'Transactions every 24 hours', value: '44 million' },
  { id: 2, name: 'Assets under holding', value: '$119 trillion' },
  { id: 3, name: 'New users annually', value: '46,000' },
  { id: 4, name: 'Uptime guarantee', value: '99.9%' },
];

export default function Parallax({
  tagline = "Key Metrics",
  title = "Our platform by the numbers",
  description = "We've helped thousands of companies reach their business goals. Here's a snapshot of our impact.",
  stats = defaultStats
}: StatsSectionProps) {
  // 确保每个统计项都有唯一ID
  const safeStats = stats.map((stat, index) => ({
    ...stat,
    id: stat.id || index + 1
  }));

  // 根据统计项数量确定布局
  const gridCols = safeStats.length === 3 
    ? "grid-cols-1 md:grid-cols-3" 
    : safeStats.length === 2 
      ? "grid-cols-1 md:grid-cols-2" 
      : "grid-cols-1 md:grid-cols-2 lg:grid-cols-4";

  return (
    <section className="bg-background py-section-y" aria-labelledby="stats-title">
      <div className="mx-auto max-w-container px-container-x">
        <motion.div 
          className="mx-auto max-w-content text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {tagline && (
            <motion.p 
              className="text-body-base/body-base font-semibold text-primary"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2, duration: 0.4 }}
            >
              {tagline}
            </motion.p>
          )}
          <motion.h2 
            id="stats-title"
            className="mt-element-y text-balance text-heading-2/heading-2 font-medium tracking-tight text-foreground"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.5 }}
          >
            {title}
          </motion.h2>
        </motion.div>
        
        <motion.p 
          className="mx-auto mt-element-y max-w-content text-pretty text-center text-body-large/body-large text-muted-foreground"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4, duration: 0.5 }}
        >
          {description}
        </motion.p>
        
        <div 
          className={`mx-auto mt-content-y grid ${gridCols} gap-element-y max-w-content`}
          aria-label="Company statistics"
        >
          {safeStats.map((stat, statIdx) => (
            <motion.div
              key={stat.id}
              className="flex flex-col rounded-3xl p-element-y text-center bg-card ring-1 ring-border shadow-sm hover:shadow-xl dark:hover:shadow-primary/5 transition-all duration-200"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ 
                delay: 0.2 + statIdx * 0.1, 
                duration: 0.5,
                ease: [0.25, 0.1, 0.25, 1] 
              }}
              whileHover={{ 
                y: -5,
                scale: 1.02,
                transition: { duration: 0.2 }
              }}
            >
              <motion.dt
                className="text-body-small/body-small font-semibold text-muted-foreground min-h-[2.5rem] flex items-center justify-center"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.3 + statIdx * 0.1, duration: 0.4 }}
              >
                {stat.name}
              </motion.dt>
              
              <motion.dd
                className="order-first text-heading-3/heading-3 font-semibold tracking-tight text-foreground mb-2"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.4 + statIdx * 0.1, duration: 0.4 }}
              >
                {stat.value}
              </motion.dd>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
