import type { StatItem, StatsSectionProps } from './types';

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ');
}

const defaultStats: StatItem[] = [
  { id: 1, name: 'Transactions every 24 hours', value: '44 million' },
  { id: 2, name: 'Assets under holding', value: '$119 trillion' },
  { id: 3, name: 'New users annually', value: '46,000' },
  { id: 4, name: 'Uptime guarantee', value: '99.9%' },
];

export default function Compact({
  tagline = "Key Metrics",
  title = "Our platform by the numbers",
  description = "We've helped thousands of companies reach their business goals. Here's a snapshot of our impact.",
  stats = defaultStats
}: StatsSectionProps) {
  // 确保每个统计项都有唯一ID
  const safeStats = stats.map((stat, index) => ({
    ...stat,
    id: stat.id || index + 1
  }));

  // 根据统计项数量确定布局
  const gridCols = safeStats.length === 3 
    ? "grid-cols-1 sm:grid-cols-3" 
    : safeStats.length === 2 
      ? "grid-cols-1 sm:grid-cols-2" 
      : "grid-cols-1 sm:grid-cols-2 lg:grid-cols-4";

  return (
    <section className="bg-background py-section-y" aria-labelledby="stats-title">
      <div className="mx-auto max-w-container px-container-x">
        <div className="mx-auto max-w-content">
          <div className="text-center">
            {tagline && <p className="text-body-base/body-base font-semibold text-primary">{tagline}</p>}
            <h2 id="stats-title" className="mt-element-y text-balance text-heading-2/heading-2 font-medium tracking-tight text-foreground">
              {title}
            </h2>
            <p className="mx-auto mt-element-y max-w-content text-center text-body-large/body-large text-muted-foreground">
              {description}
            </p>
          </div>
          
          <dl 
            className={`mt-content-y grid ${gridCols} gap-element-y`}
            aria-label="Company statistics"
          >
            {safeStats.map((stat) => (
              <div
                key={stat.id}
                className="flex flex-col rounded-xl border border-border bg-card p-element-y text-center shadow-sm hover:shadow-md transition-shadow duration-200"
              >
                <dt className="text-body-small/body-small font-semibold text-muted-foreground min-h-[2.5rem] flex items-center justify-center">{stat.name}</dt>
                <dd className="order-first text-heading-3/heading-3 font-semibold tracking-tight text-foreground">{stat.value}</dd>
              </div>
            ))}
          </dl>
        </div>
      </div>
    </section>
  );
}
