'use client'

import { motion } from 'motion/react'
import type { StatItem, StatsSectionProps } from './types';

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ')
}

const defaultStats: StatItem[] = [
  { id: 1, name: 'Transactions every 24 hours', value: '44 million' },
  { id: 2, name: 'Assets under holding', value: '$119 trillion' },
  { id: 3, name: 'New users annually', value: '46,000' },
  { id: 4, name: 'Uptime guarantee', value: '99.9%' },
];

// 为每个统计项定义不同的颜色（包含深色模式适配）
const statColors = [
  { 
    bg: 'bg-blue-500', 
    text: 'text-blue-600 dark:text-blue-400', 
    light: 'bg-blue-50 dark:bg-blue-950/30', 
    border: 'border-blue-200 dark:border-blue-800', 
    ring: 'ring-blue-500 dark:ring-blue-700' 
  },
  { 
    bg: 'bg-purple-500', 
    text: 'text-purple-600 dark:text-purple-400', 
    light: 'bg-purple-50 dark:bg-purple-950/30', 
    border: 'border-purple-200 dark:border-purple-800', 
    ring: 'ring-purple-500 dark:ring-purple-700' 
  },
  { 
    bg: 'bg-pink-500', 
    text: 'text-pink-600 dark:text-pink-400', 
    light: 'bg-pink-50 dark:bg-pink-950/30', 
    border: 'border-pink-200 dark:border-pink-800', 
    ring: 'ring-pink-500 dark:ring-pink-700' 
  },
  { 
    bg: 'bg-emerald-500', 
    text: 'text-emerald-600 dark:text-emerald-400', 
    light: 'bg-emerald-50 dark:bg-emerald-950/30', 
    border: 'border-emerald-200 dark:border-emerald-800', 
    ring: 'ring-emerald-500 dark:ring-emerald-700' 
  },
];

export default function Vibrant({
  tagline = "Key Metrics",
  title = "Our platform by the numbers",
  description = "We've helped thousands of companies reach their business goals. Here's a snapshot of our impact.",
  stats = defaultStats
}: StatsSectionProps) {
  // 确保每个统计项都有唯一ID
  const safeStats = stats.map((stat, index) => ({
    ...stat,
    id: stat.id || index + 1
  }));

  // 根据统计项数量确定布局
  const gridCols = safeStats.length === 3 
    ? "md:grid-cols-3" 
    : safeStats.length === 2 
      ? "md:grid-cols-2" 
      : "md:grid-cols-2 lg:grid-cols-4";

  return (
    <section className="bg-background py-section-y" aria-labelledby="stats-title">
      <div className="mx-auto max-w-container px-container-x">
        <motion.div 
          className="mx-auto max-w-content text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {tagline && (
            <motion.p 
              className="text-body-base/body-base font-semibold text-foreground"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2, duration: 0.4 }}
            >
              {tagline}
            </motion.p>
          )}
          <motion.h2 
            id="stats-title"
            className="mt-element-y text-balance text-heading-2/heading-2 font-medium tracking-tight text-foreground"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.5 }}
          >
            {title}
          </motion.h2>
        </motion.div>
        
        <motion.p 
          className="mx-auto mt-element-y max-w-content text-pretty text-center text-body-large/body-large text-muted-foreground"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4, duration: 0.5 }}
        >
          {description}
        </motion.p>
        
        <div 
          className={`isolate mx-auto mt-content-y grid max-w-content grid-cols-1 gap-element-y ${gridCols}`}
          aria-label="Company statistics"
        >
          {safeStats.map((stat, statIdx) => {
            const color = statColors[statIdx % statColors.length];
            
            return (
              <motion.div
                key={stat.id}
                className={classNames(
                  'rounded-3xl p-element-y flex flex-col items-center text-center ring-1',
                  color.light,
                  color.border,
                  'ring-border dark:ring-border transition-all duration-200',
                  'hover:shadow-lg dark:hover:shadow-none dark:hover:ring-2',
                  color.ring
                )}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ 
                  delay: 0.2 + statIdx * 0.1, 
                  duration: 0.5,
                  ease: [0.25, 0.1, 0.25, 1] 
                }}
                whileHover={{ 
                  y: -5,
                  scale: 1.02,
                  transition: { duration: 0.2 }
                }}
              >
                <motion.dt
                  className={classNames('text-body-small/body-small font-semibold min-h-[2.5rem] flex items-center justify-center', color.text)}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.3 + statIdx * 0.1, duration: 0.4 }}
                >
                  {stat.name}
                </motion.dt>
                
                <motion.dd
                  className={classNames('order-first text-heading-3/heading-3 font-semibold tracking-tight mb-2', color.text)}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.4 + statIdx * 0.1, duration: 0.4 }}
                >
                  {stat.value}
                </motion.dd>
              </motion.div>
            );
          })}
        </div>
      </div>
    </section>
  );
}
