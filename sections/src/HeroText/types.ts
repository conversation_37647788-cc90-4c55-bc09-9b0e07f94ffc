// 重用通用按钮类型
export type {
  ButtonVariant,
  ButtonSize,
  ButtonIconPosition,
  ButtonIconName,
  UrlType,
  ButtonIcon
} from '../components/types';

// 导入 Button 类型用于本地使用
import type { Button } from '../components/types';

// 重新导出 Button 类型
export type { Button };

// HeroText 特有的类型
export type TitleHighlightStyle = 'gradient-blue' | 'gradient-purple' | 'solid-primary' | 'solid-secondary';

export interface Announcement {
  enabled: boolean;
  text: string;
  url: string;
  urlType: 'internal' | 'external' | 'anchor';
}

export interface TitleHighlight {
  enabled: boolean;
  text: string;
  style: TitleHighlightStyle;
}

export interface Title {
  text: string;
  highlight?: TitleHighlight;
}

export interface HeroTextSectionProps {
  id: string;
  title: Title;
  description: string;
  buttons: Button[];
  announcement?: Announcement;
}
