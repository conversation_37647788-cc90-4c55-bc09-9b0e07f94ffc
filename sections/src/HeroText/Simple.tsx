'use client'

import React, { useState } from 'react';
import type { HeroTextSectionProps, ButtonIconPosition } from './types';
import { Dialog, DialogPanel } from '@headlessui/react'
import { HighlightedTitle } from '../components/HighlightedTitle';
import { ButtonAdapter } from '../components/ButtonAdapter';

const navigation = [
  { name: 'Product', href: '#' },
  { name: 'Features', href: '#' },
  { name: 'Marketplace', href: '#' },
  { name: 'Company', href: '#' },
]

const getAnnouncementHref = (announcement: HeroTextSectionProps['announcement']) => {
  if (!announcement?.url) return '#';
  
  switch (announcement.urlType) {
    case 'internal':
      return announcement.url; // 内部链接，保持原样
    case 'external':
      // 确保外部链接有 http 前缀
      return announcement.url.startsWith('http') 
        ? announcement.url 
        : `https://${announcement.url}`;
    case 'anchor':
      // 确保锚点链接以 # 开头
      return announcement.url.startsWith('#') 
        ? announcement.url 
        : `#${announcement.url}`;
    default:
      return announcement.url;
  }
};

export default function Example({ title, description, buttons, announcement }: HeroTextSectionProps) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  return (
    <section className="bg-background text-foreground" aria-labelledby="simple-hero-title">
      {/* <header className="absolute inset-x-0 top-0 z-50">
        <nav aria-label="Global" className="flex items-center justify-between p-6 lg:px-8">
          <div className="flex lg:flex-1">
            <a href="#" className="-m-1.5 p-1.5">
              <span className="sr-only">Your Company</span>
              <img
                alt=""
                src="https://tailwindui.com/plus/img/logos/mark.svg?color=indigo&shade=600"
                className="h-8 w-auto"
              />
            </a>
          </div>
          <div className="flex lg:hidden">
            <button
              type="button"
              onClick={() => setMobileMenuOpen(true)}
              className="-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700"
            >
              <span className="sr-only">Open main menu</span>
              <Bars3Icon aria-hidden="true" className="size-6" />
            </button>
          </div>
          <div className="hidden lg:flex lg:gap-x-12">
            {navigation.map((item) => (
              <a key={item.name} href={item.href} className="text-sm/6 font-semibold text-gray-900">
                {item.name}
              </a>
            ))}
          </div>
          <div className="hidden lg:flex lg:flex-1 lg:justify-end">
            <a href="#" className="text-sm/6 font-semibold text-gray-900">
              Log in <span aria-hidden="true">&rarr;</span>
            </a>
          </div>
        </nav>
        <Dialog open={mobileMenuOpen} onClose={setMobileMenuOpen} className="lg:hidden">
          <div className="fixed inset-0 z-50" />
          <DialogPanel className="fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10">
            <div className="flex items-center justify-between">
              <a href="#" className="-m-1.5 p-1.5">
                <span className="sr-only">Your Company</span>
                <img
                  alt=""
                  src="https://tailwindui.com/plus/img/logos/mark.svg?color=indigo&shade=600"
                  className="h-8 w-auto"
                />
              </a>
              <button
                type="button"
                onClick={() => setMobileMenuOpen(false)}
                className="-m-2.5 rounded-md p-2.5 text-gray-700"
              >
                <span className="sr-only">Close menu</span>
                <XMarkIcon aria-hidden="true" className="size-6" />
              </button>
            </div>
            <div className="mt-6 flow-root">
              <div className="-my-6 divide-y divide-gray-500/10">
                <div className="space-y-2 py-6">
                  {navigation.map((item) => (
                    <a
                      key={item.name}
                      href={item.href}
                      className="-mx-3 block rounded-lg px-3 py-2 text-base/7 font-semibold text-gray-900 hover:bg-gray-50"
                    >
                      {item.name}
                    </a>
                  ))}
                </div>
                <div className="py-6">
                  <a
                    href="#"
                    className="-mx-3 block rounded-lg px-3 py-2.5 text-base/7 font-semibold text-gray-900 hover:bg-gray-50"
                  >
                    Log in
                  </a>
                </div>
              </div>
            </div>
          </DialogPanel>
        </Dialog>
      </header> */}

      <div className="relative isolate overflow-hidden px-container-x pt-section-y">
        <div
          aria-hidden="true"
          className="absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80"
        >
          <div
            style={{
              clipPath:
                'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)',
            }}
            className="relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-primary/30 to-primary/30 opacity-30 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]"
          />
        </div>
        <div className="mx-auto max-w-content py-section-y">
          {
            announcement?.enabled && (
              <div className="hidden sm:mb-content-y sm:flex sm:justify-center">
                <a 
                  href={getAnnouncementHref(announcement)}
                  target={announcement.urlType === 'external' ? '_blank' : undefined}
                  rel={announcement.urlType === 'external' ? 'noopener noreferrer' : undefined}
                  aria-label={`${announcement?.text}${announcement.urlType === 'external' ? ' - Opens in a new window' : ''}`}
                  className="relative rounded-full px-element-x py-element-y text-body-small/body-small text-muted-foreground ring-1 ring-border hover:ring-ring hover:text-foreground flex items-center"
                >
                  <span>{announcement?.text}</span>
                  <span className="ml-2 p-1 inline-flex justify-center items-center rounded-full bg-accent text-accent-foreground">
                    <svg className="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" aria-hidden="true"><path d="m9 18 6-6-6-6" /></svg>
                  </span>
                </a>
              </div>
            )
          }
          <div className="text-center">
            <h1 id="simple-hero-title" className="text-balance text-heading-1/heading-1 font-semibold tracking-tight text-foreground">
              <HighlightedTitle title={title} />
            </h1>
            <p className="mt-content-y text-pretty text-body-large/body-large font-medium text-muted-foreground">
              {description}
            </p>
            <div className="mt-content-y flex flex-col items-center gap-element-y sm:flex-row sm:justify-center sm:gap-x-element-x">
              {buttons?.map((button, index) => (
                <ButtonAdapter 
                  key={index}
                  button={button}
                  index={index}
                  className="w-full sm:w-auto"
                />
              ))}
            </div>
          </div>
        </div>
        <div
          aria-hidden="true"
          className="absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]"
        >
          <div
            style={{
              clipPath:
                'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)',
            }}
            className="relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-primary/30 to-primary/30 opacity-30 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]"
          />
        </div>
      </div>
    </section>
  )
}
