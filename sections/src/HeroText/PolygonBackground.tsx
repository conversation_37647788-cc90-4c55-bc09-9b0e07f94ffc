import React from 'react';
import type { HeroTextSectionProps } from './types';
import { HighlightedTitle } from '../components/HighlightedTitle';
import { ButtonAdapter } from '../components/ButtonAdapter';

export default function Example({ id, title, description, buttons, announcement }: HeroTextSectionProps) {

    return (
        <section className="relative overflow-hidden before:absolute before:top-0 before:start-1/2 before:bg-[url('https://preline.co/assets/svg/examples/polygon-bg-element.svg')] before:bg-no-repeat before:bg-top before:bg-cover before:size-full before:-z-[1] before:transform before:-translate-x-1/2 dark:before:bg-[url('https://preline.co/assets/svg/examples-dark/polygon-bg-element.svg')]" aria-labelledby="polygon-background-hero-title">
            <div className="max-w-container mx-auto px-container-x py-section-y">
                {/* Announcement Banner */}
                {announcement && announcement.enabled && (
                    <div className="flex justify-center">
                        <a className="inline-flex items-center gap-x-element-y bg-background border border-border text-body-small/body-small text-muted-foreground p-element-y px-element-x rounded-full transition hover:border-ring dark:bg-background dark:border-border dark:hover:border-ring dark:text-muted-foreground" 
                           href={announcement.url}
                           target={announcement.urlType === 'external' ? '_blank' : undefined}
                           rel={announcement.urlType === 'external' ? 'noopener noreferrer' : undefined}
                           aria-label={`${announcement.text}${announcement.urlType === 'external' ? ' - Opens in a new window' : ''}`}>
                            {announcement.text}
                            <span className="flex items-center gap-x-1 ml-2">
                                <span className="p-1 inline-flex justify-center items-center rounded-full bg-primary/10 text-primary">
                                    <svg className="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" aria-hidden="true"><path d="m9 18 6-6-6-6" /></svg>
                                </span>
                            </span>
                        </a>
                    </div>
                )}
                {/* End Announcement Banner */}

                {/* Title */}
                <div className="mt-element-y max-w-content text-center mx-auto">
                    <h1 id="polygon-background-hero-title" className="block font-bold text-foreground text-heading-1/heading-1">
                        <HighlightedTitle title={title} />
                    </h1>
                </div>
                {/* End Title */}

                {/* Paragraph */}
                <div className="mt-element-y max-w-content text-center mx-auto">
                    <p className="text-body-large/body-large text-muted-foreground">{description}</p>
                </div>
                {/* End Paragraph */}

                {/* Buttons */}
                <div className="mt-content-y flex flex-col items-center gap-element-y sm:flex-row sm:justify-center sm:gap-x-element-x">
                    {buttons?.map((button, index) => (
                        <ButtonAdapter 
                            key={index}
                            button={button}
                            index={index}
                            className="w-full sm:w-auto"
                        />
                    ))}
                </div>
                {/* End Buttons */}
            </div>
        </section>
    );
}