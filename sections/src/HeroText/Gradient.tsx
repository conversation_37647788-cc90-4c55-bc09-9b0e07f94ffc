import React from 'react';
import type { HeroTextSectionProps } from './types';
import { HighlightedTitle } from '../components/HighlightedTitle';
import { ButtonAdapter } from '../components/ButtonAdapter';

export default function Example({ id, title, description, buttons, announcement }: HeroTextSectionProps) {

    return (
        <section className="relative overflow-hidden bg-background text-foreground" aria-labelledby="gradient-hero-title">
            {/* Gradients */}
            <div aria-hidden="true" className="flex absolute -top-96 start-1/2 transform -translate-x-1/2">
                <div className="bg-gradient-to-r from-primary/50 to-primary/10 blur-3xl w-[25rem] h-[44rem] rotate-[-60deg] transform -translate-x-[10rem]"></div>
                <div className="bg-gradient-to-tl from-background via-primary/10 to-background blur-3xl w-[90rem] h-[50rem] rounded-fulls origin-top-left -rotate-12 -translate-x-[15rem]"></div>
            </div>
            {/* End Gradients */}

            <div className="relative z-10">
                <div className="max-w-container mx-auto px-container-x py-section-y">
                    {/* Announcement Banner */}
                    {announcement && announcement.enabled && (
                        <div className="flex justify-center">
                            <a className="inline-flex items-center gap-x-element-y bg-background border border-border text-body-base/body-base text-foreground p-element-y ps-element-x rounded-full transition hover:border-ring" 
                               href={announcement.url}
                               target={announcement.urlType === 'external' ? '_blank' : undefined}
                               rel={announcement.urlType === 'external' ? 'noopener noreferrer' : undefined}
                               aria-label={`${announcement.text}${announcement.urlType === 'external' ? ' - Opens in a new window' : ''}`}>
                                <span className="font-medium">{announcement.text}</span>
                                <span className="flex justify-center items-center size-7 bg-accent text-accent-foreground rounded-full">
                                    <svg className="flex-shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" aria-hidden="true"><path d="m9 18 6-6-6-6"/></svg>
                                </span>
                            </a>
                        </div>
                    )}
                    {/* End Announcement Banner */}

                    <div className="max-w-content text-center mx-auto">
                        {/* Title */}
                        <div className="mt-element-y">
                            <h1 id="gradient-hero-title" className="block font-semibold text-foreground text-heading-1/heading-1 text-center">
                                <HighlightedTitle title={title} />
                            </h1>
                        </div>
                        {/* End Title */}

                        {/* Description */}
                        <div className="mt-element-y">
                            <p className="text-body-large/body-large text-muted-foreground text-center">{description}</p>
                        </div>
                        {/* End Description */}

                        {/* Buttons */}
                        <div className="mt-content-y flex flex-col items-center gap-element-y sm:flex-row sm:justify-center sm:gap-x-element-x">
                            {buttons?.map((button, index) => (
                                <ButtonAdapter 
                                    key={index}
                                    button={button}
                                    index={index}
                                    className="w-full sm:w-auto"
                                />
                            ))}
                        </div>
                        {/* End Buttons */}
                    </div>
                </div>
            </div>
        </section>
    );
}