import React from 'react';
import type { HeroTextSectionProps } from './types';
import { HighlightedTitle } from '../components/HighlightedTitle';
import { ButtonAdapter } from '../components/ButtonAdapter';

export default function Example({ id, title, description, buttons, announcement }: HeroTextSectionProps) {

    return (
        <section className="bg-background text-foreground" aria-labelledby="dark-background-hero-title">
            <div className="bg-gradient-to-b from-primary/10 via-transparent">
                <div className="max-w-container mx-auto px-container-x py-section-y space-y-content-y">
                    {/* Announcement */}
                    {announcement && announcement.enabled && (
                        <div className="flex justify-center">
                            <a href={announcement.url} 
                               target={announcement.urlType === 'external' ? '_blank' : undefined}
                               rel={announcement.urlType === 'external' ? 'noopener noreferrer' : undefined}
                               aria-label={`${announcement.text}${announcement.urlType === 'external' ? ' - Opens in a new window' : ''}`}
                               className="inline-block max-w-xl text-center">
                                <span className="inline-flex items-center gap-element-y py-element-y px-element-x rounded-md text-body-small/body-small font-medium bg-accent text-accent-foreground hover:bg-accent/80 transition-colors">
                                    <span>{announcement.text}</span>
                                    <span className="ml-1 p-1 inline-flex justify-center items-center rounded-full bg-accent/20 text-accent-foreground">
                                        <svg className="shrink-0 size-2.5" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" aria-hidden="true"><path d="m9 18 6-6-6-6" /></svg>
                                    </span>
                                </span>
                            </a>
                        </div>
                    )}
                    {/* End Announcement */}

                    {/* Title */}
                    <div className="max-w-content text-center mx-auto">
                        <h1 id="dark-background-hero-title" className="block font-medium text-foreground text-heading-1/heading-1">
                            <HighlightedTitle title={title} />
                        </h1>
                    </div>
                    {/* End Title */}

                    {/* Description */}
                    <div className="max-w-content text-center mx-auto">
                        <p className="text-body-large/body-large text-muted-foreground">{description}</p>
                    </div>
                    {/* End Description */}

                    {/* Buttons */}
                    <div className="flex flex-col items-center gap-element-y sm:flex-row sm:justify-center sm:gap-x-element-x">
                        {buttons?.map((button, index) => (
                            <ButtonAdapter 
                                key={index}
                                button={button}
                                index={index}
                                className="w-full sm:w-auto"
                            />
                        ))}
                    </div>
                    {/* End Buttons */}
                </div>
            </div>
        </section>
    );
}