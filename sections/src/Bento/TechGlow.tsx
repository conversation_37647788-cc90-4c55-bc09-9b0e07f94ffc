'use client'

import React, { useState, useEffect, useRef } from 'react';
import { motion, useMotionValue, useTransform, AnimatePresence } from 'motion/react';
import { BentoSectionProps } from './types';
import { Picture } from '../components/Picture';

export default function TechGlow({ id, tagline, title, items }: BentoSectionProps) {
  // 确保items数组至少有4个元素
  const safeItems = items && items.length >= 4 ? items : Array(4).fill({
    title: 'Feature',
    description: 'Description text here',
    img: 'https://images.unsplash.com/photo-1624571409412-1f253e1ecc89?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&q=80',
    alt: 'Feature image'
  });



  // 鼠标位置状态
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [cursorVisible, setCursorVisible] = useState(false);
  
  // 活跃卡片状态
  const [activeCard, setActiveCard] = useState<number | null>(null);
  
  // 容器引用
  const containerRef = useRef<HTMLDivElement>(null);

  // 更新鼠标位置
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        setMousePosition({
          x: e.clientX - rect.left,
          y: e.clientY - rect.top
        });
        setCursorVisible(true);
      }
    };

    const handleMouseLeave = () => {
      setCursorVisible(false);
    };

    const container = containerRef.current;
    if (container) {
      container.addEventListener('mousemove', handleMouseMove);
      container.addEventListener('mouseleave', handleMouseLeave);
    }

    return () => {
      if (container) {
        container.removeEventListener('mousemove', handleMouseMove);
        container.removeEventListener('mouseleave', handleMouseLeave);
      }
    };
  }, []);

  // 卡片变体
  const cardVariants = {
    initial: (i: number) => ({ 
      scale: 0.9, 
      opacity: 0,
      y: i % 2 === 0 ? 20 : -20,
      x: i < 2 ? -20 : 20,
      filter: 'brightness(0.8) contrast(0.8)'
    }),
    animate: (i: number) => ({ 
      scale: 1, 
      opacity: 1,
      y: 0,
      x: 0,
      filter: 'brightness(1) contrast(1)',
      transition: { 
        duration: 0.6,
        delay: i * 0.15,
        type: "spring",
        stiffness: 100
      }
    }),
    hover: { 
      scale: 1.02,
      // 移除滤镜效果，减少渲染负担
      // filter: 'brightness(1.1) contrast(1.05)',
      // 使用 Tailwind 类替代内联样式的阴影
      transition: { 
        type: "spring", 
        stiffness: 300, 
        damping: 15 
      }
    },
    tap: { 
      scale: 0.98,
      transition: { 
        type: "spring", 
        stiffness: 400, 
        damping: 17 
      }
    }
  };

  // 光照效果变体
  const glowVariants = {
    initial: {
      opacity: 0,
      scale: 0.8
    },
    animate: {
      opacity: 0.8,
      scale: 1,
      transition: {
        duration: 1.5,
        repeat: Infinity,
        repeatType: "reverse" as const
      }
    }
  };

  // 霓虹边框变体
  const neonBorderVariants = {
    initial: {
      pathLength: 0,
      opacity: 0
    },
    animate: {
      pathLength: 1,
      opacity: 1,
      transition: {
        pathLength: { type: "spring", duration: 1.5, bounce: 0 },
        opacity: { duration: 0.2 }
      }
    }
  };

  return (
    <section className="bg-background dark:bg-gray-950 py-section-y overflow-hidden relative" aria-labelledby={id ? `bento-heading-${id}` : 'bento-heading'}>
      {/* 背景光效 */}
      <div className="absolute inset-0 overflow-hidden" aria-hidden="true">
        <div className="absolute top-1/4 left-1/4 w-1/2 h-1/2 bg-purple-600 rounded-full filter blur-[150px] opacity-20 animate-pulse"></div>
        <div className="absolute top-1/3 right-1/4 w-1/3 h-1/3 bg-blue-500 rounded-full filter blur-[120px] opacity-15 animate-pulse" style={{ animationDelay: '1s' }}></div>
        <div className="absolute bottom-1/4 left-1/3 w-1/4 h-1/4 bg-cyan-400 rounded-full filter blur-[100px] opacity-10 animate-pulse" style={{ animationDelay: '2s' }}></div>
      </div>
      
      {/* 网格背景 */}
      <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiMyMTIxMjEiIGZpbGwtb3BhY2l0eT0iMC40Ij48cGF0aCBkPSJNMzYgMzRoLTJ2LTRoMnY0em0wLTZoLTJ2LTRoMnY0em0wLTZoLTJ2LTRoMnY0em0wLTZoLTJWNmgydjR6bTAgMjRoLTJ2LTRoMnY0em0wIDZoLTJ2LTRoMnY0em0wIDZoLTJ2LTRoMnY0em0wIDZoLTJ2LTRoMnY0em0tNi0yNGgtMnYtNGgydjR6bTAgNmgtMnYtNGgydjR6bTAgNmgtMnYtNGgydjR6bTAgNmgtMnYtNGgydjR6bS02LTI0aC0ydi00aDJ2NHptMCA2aC0ydi00aDJ2NHptMCA2aC0ydi00aDJ2NHptMCA2aC0ydi00aDJ2NHptLTYtMjRoLTJ2LTRoMnY0em0wIDZoLTJ2LTRoMnY0em0wIDZoLTJ2LTRoMnY0em0wIDZoLTJ2LTRoMnY0em0tNi0yNGgtMnYtNGgydjR6bTAgNmgtMnYtNGgydjR6bTAgNmgtMnYtNGgydjR6bTAgNmgtMnYtNGgydjR6Ii8+PC9nPjwvZz48L3N2Zz4=')] opacity-10" aria-hidden="true"></div>
      
      {/* 主容器 */}
      <div 
        ref={containerRef}
        className="mx-auto max-w-container px-container-x lg:px-container-x-lg relative"
      >
        {/* 自定义光标效果 */}
        {cursorVisible && (
          <motion.div 
            className="pointer-events-none absolute w-64 h-64 rounded-full"
            style={{ 
              left: mousePosition.x, 
              top: mousePosition.y,
              background: 'radial-gradient(circle, rgba(139, 92, 246, 0.15) 0%, rgba(30, 64, 175, 0.05) 50%, rgba(0, 0, 0, 0) 70%)',
              transform: 'translate(-50%, -50%)'
            }}
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.5 }}
            transition={{ duration: 0.2 }}
            aria-hidden="true"
          />
        )}
        
        <motion.div 
          className="mx-auto max-w-content text-center"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7 }}
        >
          {tagline && (
            <motion.p 
              className="text-base font-semibold leading-7 text-primary dark:text-purple-400"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2, duration: 0.5 }}
            >
              {tagline}
            </motion.p>
          )}
          {title && (
            <motion.h2 
              id={id ? `bento-heading-${id}` : 'bento-heading'}
              className="mt-element-y text-heading-2/heading-2 font-bold tracking-tight text-foreground dark:text-white sm:text-heading-1/heading-1"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.5 }}
            >
              {title}
            </motion.h2>
          )}
        </motion.div>
        
        <div className="mx-auto mt-section-y max-w-container">
          {/* 动态流畅的网格布局 */}
          <div className="grid grid-cols-1 md:grid-cols-12 gap-element-y md:gap-element-x">
            {/* 第一个区块 - 左侧大区块，占据5列2行 */}
            <motion.div 
              className="md:col-span-5 md:row-span-2 relative"
              variants={cardVariants}
              custom={0}
              initial="initial"
              animate="animate"
              whileHover="hover"
              whileTap="tap"
              onHoverStart={() => setActiveCard(0)}
              onHoverEnd={() => setActiveCard(null)}
            >
              <div className="relative h-full rounded-2xl bg-gradient-to-br from-gray-900 to-gray-800 p-px overflow-hidden">
                {/* 霓虹边框 */}
                <div className="absolute inset-0 pointer-events-none" aria-hidden="true">
                  <svg className="w-full h-full">
                    <motion.rect
                      width="100%"
                      height="100%"
                      rx="16" 
                      ry="16" 
                      fill="none"
                      stroke="url(#gradient1)"
                      strokeWidth="1.5"
                      strokeLinecap="round" 
                      strokeLinejoin="round" 
                      variants={neonBorderVariants}
                      initial="initial"
                      animate={activeCard === 0 ? "animate" : "initial"}
                    />
                    <defs>
                      <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stopColor="#9333EA" />
                        <stop offset="50%" stopColor="#3B82F6" />
                        <stop offset="100%" stopColor="#06B6D4" />
                      </linearGradient>
                    </defs>
                  </svg>
                </div>
                
                {/* 内部光效 */}
                <motion.div 
                  className="absolute -bottom-16 -right-16 w-32 h-32 rounded-full bg-purple-500 filter blur-xl opacity-30"
                  variants={glowVariants}
                  initial="initial"
                  animate="animate"
                  aria-hidden="true"
                />
                
                <div className="relative h-full rounded-2xl bg-gray-900 bg-opacity-80 backdrop-blur-sm p-6 flex flex-col">
                  <div className="mb-6">
                    <motion.div
                      className="relative w-full h-[32rem] overflow-hidden rounded-xl"
                      whileHover={{ scale: 1.03 }}
                      transition={{ duration: 0.3 }}
                    >
                      <Picture
                        src={safeItems[0].img}
                        alt={safeItems[0].alt || "Feature image"}
                        widths={[480, 640, 768, 1024, 1200]}
                        sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 600px"
                        quality={{ avif: 70, webp: 75, jpeg: 80, png: 85 }}
                        forceFillHeight={true}
                        className="w-full h-full object-cover"
                        aria-describedby="bento-techglow-item-0-title"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-gray-900 via-gray-900/50 to-transparent"></div>
                      
                      {/* 图片上的光效 */}
                      <motion.div 
                        className="absolute inset-0 bg-gradient-to-br from-purple-500/20 to-blue-500/20 opacity-20"
                        transition={{ duration: 0.3 }}
                        aria-hidden="true"
                      />
                    </motion.div>
                  </div>
                  
                  {safeItems[0].tagline && (
                    <motion.div 
                      className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-900/50 text-purple-300 mb-element-y-sm"
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.4, duration: 0.5 }}
                    >
                      {safeItems[0].tagline}
                    </motion.div>
                  )}
                  
                  <motion.h3 
                    id="bento-techglow-item-0-title"
                    className="text-heading-3/heading-3 font-bold text-white mb-element-y-sm"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.5, duration: 0.5 }}
                  >
                    {safeItems[0].title}
                  </motion.h3>
                  
                  <motion.p 
                    className="text-gray-300 text-body"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.6, duration: 0.5 }}
                    aria-labelledby="bento-techglow-item-0-title"
                  >
                    {safeItems[0].description}
                  </motion.p>
                  
                  <motion.div 
                    className="mt-element-y h-0.5 w-16 bg-gradient-to-r from-purple-500 to-blue-500"
                    whileHover={{ width: 100 }}
                    transition={{ duration: 0.3 }}
                    aria-hidden="true"
                  />
                </div>
              </div>
            </motion.div>
            
            {/* 第二个区块 - 右上角，占据7列1行 */}
            <motion.div 
              className="md:col-span-7 md:col-start-6"
              variants={cardVariants}
              custom={1}
              initial="initial"
              animate="animate"
              whileHover="hover"
              whileTap="tap"
              onHoverStart={() => setActiveCard(1)}
              onHoverEnd={() => setActiveCard(null)}
            >
              <div className="relative h-full rounded-2xl bg-gradient-to-br from-gray-900 to-gray-800 p-px overflow-hidden">
                {/* 霓虹边框 */}
                <div className="absolute inset-0 pointer-events-none" aria-hidden="true">
                  <svg className="w-full h-full">
                    <motion.rect
                      width="100%"
                      height="100%"
                      rx="16" 
                      ry="16" 
                      fill="none"
                      stroke="url(#gradient2)"
                      strokeWidth="1.5"
                      strokeLinecap="round" 
                      strokeLinejoin="round" 
                      variants={neonBorderVariants}
                      initial="initial"
                      animate={activeCard === 1 ? "animate" : "initial"}
                    />
                    <defs>
                      <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stopColor="#3B82F6" />
                        <stop offset="50%" stopColor="#06B6D4" />
                        <stop offset="100%" stopColor="#10B981" />
                      </linearGradient>
                    </defs>
                  </svg>
                </div>
                
                {/* 内部光效 */}
                <motion.div 
                  className="absolute -top-16 -left-16 w-32 h-32 rounded-full bg-blue-500 filter blur-xl opacity-30"
                  variants={glowVariants}
                  initial="initial"
                  animate="animate"
                  aria-hidden="true"
                />
                
                <div className="relative h-full rounded-2xl bg-gray-900 bg-opacity-80 backdrop-blur-sm p-6 flex md:flex-row flex-col">
                  <div className="md:w-1/2 mb-4 md:mb-0 md:mr-6">
                    {safeItems[1].tagline && (
                      <motion.div 
                        className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-900/50 text-blue-300 mb-element-y-sm"
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.5, duration: 0.5 }}
                      >
                        {safeItems[1].tagline}
                      </motion.div>
                    )}
                    
                    <motion.h3 
                      id="bento-techglow-item-1-title"
                      className="text-heading-4/heading-4 font-bold text-white mb-element-y-sm"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.6, duration: 0.5 }}
                    >
                      {safeItems[1].title}
                    </motion.h3>
                    
                    <motion.p 
                      className="text-gray-300 text-body-sm"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.7, duration: 0.5 }}
                      aria-labelledby="bento-techglow-item-1-title"
                    >
                      {safeItems[1].description}
                    </motion.p>
                  </div>
                  
                  <div className="md:w-1/2">
                    <motion.div
                      className="relative w-full h-48 overflow-hidden rounded-xl"
                      whileHover={{ scale: 1.03 }}
                      transition={{ duration: 0.3 }}
                    >
                      <Picture
                        src={safeItems[1].img}
                        alt={safeItems[1].alt || "Feature image"}
                        widths={[375, 480, 640, 768, 1024]}
                        sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 400px"
                        quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                        forceFillHeight={true}
                        className="w-full h-full object-cover"
                        aria-describedby="bento-techglow-item-1-title"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-gray-900 via-gray-900/50 to-transparent" aria-hidden="true"></div>
                      
                      {/* 图片上的光效 */}
                      <motion.div 
                        className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-cyan-500/20 opacity-20"
                        transition={{ duration: 0.3 }}
                        aria-hidden="true"
                      />
                    </motion.div>
                  </div>
                </div>
              </div>
            </motion.div>
            
            {/* 第三个区块 - 右中，占据4列1行 */}
            <motion.div 
              className="md:col-span-4 md:col-start-6 md:row-start-2"
              variants={cardVariants}
              custom={2}
              initial="initial"
              animate="animate"
              whileHover="hover"
              whileTap="tap"
              onHoverStart={() => setActiveCard(2)}
              onHoverEnd={() => setActiveCard(null)}
            >
              <div className="relative h-full rounded-2xl bg-gradient-to-br from-gray-900 to-gray-800 p-px overflow-hidden">
                {/* 霓虹边框 */}
                <div className="absolute inset-0 pointer-events-none" aria-hidden="true">
                  <svg className="w-full h-full">
                    <motion.rect
                      width="100%"
                      height="100%"
                      rx="16" 
                      ry="16" 
                      fill="none"
                      stroke="url(#gradient3)"
                      strokeWidth="1.5"
                      strokeLinecap="round" 
                      strokeLinejoin="round" 
                      variants={neonBorderVariants}
                      initial="initial"
                      animate={activeCard === 2 ? "animate" : "initial"}
                    />
                    <defs>
                      <linearGradient id="gradient3" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stopColor="#06B6D4" />
                        <stop offset="50%" stopColor="#10B981" />
                        <stop offset="100%" stopColor="#6366F1" />
                      </linearGradient>
                    </defs>
                  </svg>
                </div>
                
                {/* 内部光效 */}
                <motion.div 
                  className="absolute -bottom-16 -left-16 w-32 h-32 rounded-full bg-cyan-500 filter blur-xl opacity-30"
                  variants={glowVariants}
                  initial="initial"
                  animate="animate"
                  aria-hidden="true"
                />
                
                <div className="relative h-full rounded-2xl bg-gray-900 bg-opacity-80 backdrop-blur-sm p-6 flex flex-col">
                  <div className="mb-4">
                    <motion.div
                      className="relative w-full h-64 overflow-hidden rounded-xl"
                      whileHover={{ scale: 1.03 }}
                      transition={{ duration: 0.3 }}
                    >
                      <Picture
                        src={safeItems[2].img}
                        alt={safeItems[2].alt || "Feature image"}
                        widths={[375, 480, 640, 768, 1024]}
                        sizes="(max-width: 768px) 100vw, (max-width: 1024px) 40vw, 350px"
                        quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                        forceFillHeight={true}
                        className="w-full h-full object-cover"
                        aria-describedby="bento-techglow-item-2-title"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-gray-900 via-gray-900/50 to-transparent" aria-hidden="true"></div>
                      
                      {/* 图片上的光效 */}
                      <motion.div 
                        className="absolute inset-0 bg-gradient-to-br from-cyan-500/20 to-indigo-500/20 opacity-20"
                        transition={{ duration: 0.3 }}
                        aria-hidden="true"
                      />
                    </motion.div>
                  </div>
                  
                  {safeItems[2].tagline && (
                    <motion.div 
                      className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-cyan-900/50 text-cyan-300 mb-element-y-sm"
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.6, duration: 0.5 }}
                    >
                      {safeItems[2].tagline}
                    </motion.div>
                  )}
                  
                  <motion.h3 
                    id="bento-techglow-item-2-title"
                    className="text-heading-4/heading-4 font-bold text-white mb-element-y-sm"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.7, duration: 0.5 }}
                  >
                    {safeItems[2].title}
                  </motion.h3>
                  
                  <motion.p 
                    className="text-gray-300 text-body-sm"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.8, duration: 0.5 }}
                    aria-labelledby="bento-techglow-item-2-title"
                  >
                    {safeItems[2].description}
                  </motion.p>
                </div>
              </div>
            </motion.div>
            
            {/* 第四个区块 - 右下角，占据3列1行 */}
            <motion.div 
              className="md:col-span-3 md:col-start-10 md:row-start-2"
              variants={cardVariants}
              custom={3}
              initial="initial"
              animate="animate"
              whileHover="hover"
              whileTap="tap"
              onHoverStart={() => setActiveCard(3)}
              onHoverEnd={() => setActiveCard(null)}
            >
              <div className="relative h-full rounded-2xl bg-gradient-to-br from-gray-900 to-gray-800 p-px overflow-hidden">
                {/* 霓虹边框 */}
                <div className="absolute inset-0 pointer-events-none" aria-hidden="true">
                  <svg className="w-full h-full">
                    <motion.rect
                      width="100%"
                      height="100%"
                      rx="16" 
                      ry="16" 
                      fill="none"
                      stroke="url(#gradient4)"
                      strokeWidth="1.5"
                      strokeLinecap="round" 
                      strokeLinejoin="round" 
                      variants={neonBorderVariants}
                      initial="initial"
                      animate={activeCard === 3 ? "animate" : "initial"}
                    />
                    <defs>
                      <linearGradient id="gradient4" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stopColor="#6366F1" />
                        <stop offset="50%" stopColor="#9333EA" />
                        <stop offset="100%" stopColor="#EC4899" />
                      </linearGradient>
                    </defs>
                  </svg>
                </div>
                
                {/* 内部光效 */}
                <motion.div 
                  className="absolute -top-16 -right-16 w-32 h-32 rounded-full bg-indigo-500 filter blur-xl opacity-30"
                  variants={glowVariants}
                  initial="initial"
                  animate="animate"
                  aria-hidden="true"
                />
                
                <div className="relative h-full rounded-2xl bg-gray-900 bg-opacity-80 backdrop-blur-sm p-5 flex flex-col" aria-labelledby="bento-techglow-item-3-title">
                  <motion.div
                    className="relative w-full h-40 overflow-hidden rounded-xl mb-3"
                    whileHover={{ scale: 1.03 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Picture
                      src={safeItems[3].img}
                      alt={safeItems[3].alt || "Feature image"}
                      widths={[375, 480, 640, 768]}
                      sizes="(max-width: 768px) 100vw, (max-width: 1024px) 30vw, 300px"
                      quality={{ avif: 60, webp: 65, jpeg: 70, png: 75 }}
                      forceFillHeight={true}
                      className="w-full h-full object-cover"
                      aria-describedby="bento-techglow-item-3-title"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-gray-900 via-gray-900/50 to-transparent" aria-hidden="true"></div>
                    
                    {/* 图片上的光效 */}
                    <motion.div 
                      className="absolute inset-0 bg-gradient-to-br from-indigo-500/20 to-pink-500/20 opacity-20"
                      transition={{ duration: 0.3 }}
                      aria-hidden="true"
                    />
                  </motion.div>
                  
                  {safeItems[3].tagline && (
                    <motion.div 
                      className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-indigo-900/50 text-indigo-300 mb-element-y-xs"
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.7, duration: 0.5 }}
                    >
                      {safeItems[3].tagline}
                    </motion.div>
                  )}
                  
                  <motion.h3 
                    id="bento-techglow-item-3-title"
                    className="text-heading-5/heading-5 font-bold text-white mb-element-y-xs"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.8, duration: 0.5 }}
                  >
                    {safeItems[3].title}
                  </motion.h3>
                  
                  <motion.p 
                    className="text-gray-300 text-body-xs"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.9, duration: 0.5 }}
                    aria-labelledby="bento-techglow-item-3-title"
                  >
                    {safeItems[3].description}
                  </motion.p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
}
