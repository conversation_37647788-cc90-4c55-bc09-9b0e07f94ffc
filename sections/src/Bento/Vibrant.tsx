import React from 'react';
import { BentoSectionProps } from './types';
import { Picture } from '../components/Picture';

export default function Vibrant({ id, tagline, title, items }: BentoSectionProps) {
  // 确保items数组至少有4个元素
  const safeItems = items && items.length >= 4 ? items : Array(4).fill({
    title: 'Feature',
    description: 'Description text here',
    img: 'https://images.unsplash.com/photo-1624571409412-1f253e1ecc89?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&q=80',
    alt: 'Feature image'
  });

  // 为每个位置分配特定的图片尺寸
  const imgSizes = [
    'w=800&h=1200', // 左侧大区块
    'w=800&h=300',  // 右上区块
    'w=600&h=300',  // 右中区块
    'w=600&h=450'   // 右下区块
  ];

  // 为每个区块分配不同的旋转角度和颜色
  const blockStyles = [
    { rotate: 'rotate-0', bg: 'bg-gradient-to-r from-fuchsia-600 to-pink-600', shadow: 'shadow-pink-500/30' },
    { rotate: 'rotate-1', bg: 'bg-gradient-to-r from-amber-500 to-orange-500', shadow: 'shadow-orange-500/30' },
    { rotate: '-rotate-1', bg: 'bg-gradient-to-r from-blue-500 to-cyan-500', shadow: 'shadow-blue-500/30' },
    { rotate: 'rotate-0', bg: 'bg-gradient-to-r from-lime-500 to-green-500', shadow: 'shadow-green-500/30' }
  ];

  return (
    <section className="bg-background dark:bg-gray-900 py-section-y overflow-hidden" aria-labelledby={id ? `bento-heading-${id}` : 'bento-heading'}>
      <div className="mx-auto max-w-container px-container-x relative">
        {/* 装饰性背景元素 */}
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-gradient-to-r from-purple-700 to-indigo-700 rounded-full filter blur-3xl opacity-20" aria-hidden="true"></div>
        <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-gradient-to-r from-amber-700 to-pink-700 rounded-full filter blur-3xl opacity-20" aria-hidden="true"></div>
        
        {tagline && <p className="text-center text-body-base/body-base font-bold text-primary dark:text-pink-500">{tagline}</p>}
        {title && (
          <h2 id={id ? `bento-heading-${id}` : 'bento-heading'} className="mx-auto mt-element-y max-w-content text-balance text-center text-heading-2/heading-2 font-bold tracking-tight text-foreground dark:text-white">
            {title}
          </h2>
        )}
        
        {/* 使用不规则网格布局，左侧一个大区块，右侧三个小区块 */}
        <div className="mt-content-y grid grid-cols-1 gap-element-y sm:grid-cols-2 lg:grid-cols-3 lg:grid-rows-2 relative">
          {/* 左侧大区块 - 占据1列2行 */}
          <div 
            className={`lg:row-span-2 ${blockStyles[0].bg} rounded-2xl overflow-hidden ${blockStyles[0].shadow} shadow-xl ${blockStyles[0].rotate} hover:rotate-0 transition-all duration-500 group`}
          >
            <div className="p-element-y h-full flex flex-col" aria-labelledby="bento-vibrant-item-0-title">
              {safeItems[0].tagline && (
                <p className="text-body-small/body-small font-bold text-white/80">{safeItems[0].tagline}</p>
              )}
              <h3 id="bento-vibrant-item-0-title" className="mt-element-y text-heading-3/heading-3 font-bold tracking-tight text-white">
                {safeItems[0].title}
              </h3>
              <p className="mt-element-y text-body-base/body-base text-white/80" aria-labelledby="bento-vibrant-item-0-title">
                {safeItems[0].description}
              </p>
              <div className="mt-content-y flex-grow relative overflow-hidden rounded-xl min-h-[200px]">
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent z-10" aria-hidden="true"></div>
                <Picture
                  src={safeItems[0].img}
                  alt={safeItems[0].alt || "Feature image"}
                  widths={[480, 640, 768, 1024, 1200]}
                  sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 400px"
                  quality={{ avif: 70, webp: 75, jpeg: 80, png: 85 }}
                  forceFillHeight={true}
                  className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  aria-describedby="bento-vibrant-item-0-title"
                />
              </div>
            </div>
          </div>

          {/* 右上区块 - 占据2列1行 */}
          <div 
            className={`lg:col-span-2 ${blockStyles[1].bg} rounded-2xl overflow-hidden ${blockStyles[1].shadow} shadow-xl ${blockStyles[1].rotate} hover:rotate-0 transition-all duration-500 group`}
            aria-labelledby="bento-vibrant-item-1-title"
          >
            <div className="p-element-y h-full flex flex-col">
              {safeItems[1].tagline && (
                <p className="text-body-small/body-small font-bold text-white/80">{safeItems[1].tagline}</p>
              )}
              <h3 id="bento-vibrant-item-1-title" className="mt-element-y text-heading-4/heading-4 font-bold tracking-tight text-white">
                {safeItems[1].title}
              </h3>
              <p className="mt-element-y text-body-small/body-small text-white/80" aria-labelledby="bento-vibrant-item-1-title">
                {safeItems[1].description}
              </p>
              <div className="mt-element-y relative overflow-hidden rounded-xl h-36 md:h-40 lg:h-44">
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent z-10" aria-hidden="true"></div>
                <Picture
                  src={safeItems[1].img}
                  alt={safeItems[1].alt || "Feature image"}
                  widths={[640, 768, 1024, 1200]}
                  sizes="(max-width: 1024px) 70vw, 700px"
                  quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                  forceFillHeight={true}
                  className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  aria-describedby="bento-vibrant-item-1-title"
                />
              </div>
            </div>
          </div>

          {/* 右下左区块 */}
          <div 
            className={`${blockStyles[2].bg} rounded-2xl overflow-hidden ${blockStyles[2].shadow} shadow-xl ${blockStyles[2].rotate} hover:rotate-0 transition-all duration-500 group`}
            aria-labelledby="bento-vibrant-item-2-title"
          >
            <div className="p-element-y h-full flex flex-col">
              {safeItems[2].tagline && (
                <p className="text-body-small/body-small font-bold text-white/80">{safeItems[2].tagline}</p>
              )}
              <h3 id="bento-vibrant-item-2-title" className="mt-element-y text-heading-4/heading-4 font-bold tracking-tight text-white">
                {safeItems[2].title}
              </h3>
              <p className="mt-element-y text-body-small/body-small text-white/80">
                {safeItems[2].description}
              </p>
              <div className="mt-element-y relative overflow-hidden rounded-xl h-32 md:h-36 lg:h-40">
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent z-10" aria-hidden="true"></div>
                <Picture
                  src={safeItems[2].img}
                  alt={safeItems[2].alt || "Feature image"}
                  widths={[375, 480, 640, 768]}
                  sizes="(max-width: 1024px) 35vw, 350px"
                  quality={{ avif: 60, webp: 65, jpeg: 70, png: 75 }}
                  forceFillHeight={true}
                  className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  aria-describedby="bento-vibrant-item-2-title"
                />
              </div>
            </div>
          </div>

          {/* 右下右区块 */}
          <div 
            className={`${blockStyles[3].bg} rounded-2xl overflow-hidden ${blockStyles[3].shadow} shadow-xl ${blockStyles[3].rotate} hover:rotate-0 transition-all duration-500 group`}
            aria-labelledby="bento-vibrant-item-3-title"
          >
            <div className="p-element-y h-full flex flex-col">
              {safeItems[3].tagline && (
                <p className="text-body-small/body-small font-bold text-white/80">{safeItems[3].tagline}</p>
              )}
              <h3 id="bento-vibrant-item-3-title" className="mt-element-y text-heading-4/heading-4 font-bold tracking-tight text-white">
                {safeItems[3].title}
              </h3>
              <p className="mt-element-y text-body-small/body-small text-white/80">
                {safeItems[3].description}
              </p>
              <div className="mt-element-y relative overflow-hidden rounded-xl h-32 md:h-36 lg:h-40">
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent z-10" aria-hidden="true"></div>
                <Picture
                  src={safeItems[3].img}
                  alt={safeItems[3].alt || "Feature image"}
                  widths={[375, 480, 640, 768]}
                  sizes="(max-width: 1024px) 35vw, 350px"
                  quality={{ avif: 60, webp: 65, jpeg: 70, png: 75 }}
                  forceFillHeight={true}
                  className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  aria-describedby="bento-vibrant-item-3-title"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
