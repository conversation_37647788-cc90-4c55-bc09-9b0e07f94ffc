'use client'

import React, { useState } from 'react';
import { motion, useMotionValue, useTransform, AnimatePresence } from 'motion/react';
import { BentoSectionProps } from './types';
import { Picture } from '../components/Picture';

export default function AnimatedGrid({ id, tagline, title, items }: BentoSectionProps) {
  // 确保items数组至少有4个元素
  const safeItems = items && items.length >= 4 ? items : Array(4).fill({
    title: 'Feature',
    description: 'Description text here',
    img: 'https://images.unsplash.com/photo-1624571409412-1f253e1ecc89?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&q=80',
    alt: 'Feature image'
  });



  // 选中的卡片状态
  const [activeCard, setActiveCard] = useState<number | null>(null);

  // 边框动画变体
  const borderVariants = {
    initial: {
      pathLength: 0,
      opacity: 0
    },
    animate: {
      pathLength: 1,
      opacity: 1,
      transition: {
        pathLength: { type: "spring", duration: 1.5, bounce: 0 },
        opacity: { duration: 0.2 }
      }
    }
  };

  // 卡片变体
  const cardVariants = {
    initial: (i: number) => ({ 
      scale: 0.9, 
      opacity: 0,
      y: i % 2 === 0 ? 20 : -20,
      x: i < 2 ? -20 : 20
    }),
    animate: (i: number) => ({ 
      scale: 1, 
      opacity: 1,
      y: 0,
      x: 0,
      transition: { 
        duration: 0.6,
        delay: i * 0.15,
        type: "spring",
        stiffness: 100
      }
    }),
    hover: { 
      scale: 1.03,
      transition: { 
        type: "spring", 
        stiffness: 400, 
        damping: 10 
      }
    },
    tap: { 
      scale: 0.98,
      transition: { 
        type: "spring", 
        stiffness: 400, 
        damping: 17 
      }
    }
  };

  // 颜色主题 - 适配亮色和暗色模式
  const colors = [
    { bg: 'bg-violet-500 dark:bg-violet-800', text: 'text-violet-100 dark:text-violet-50', accent: 'bg-violet-300 dark:bg-violet-600', highlight: 'text-violet-300 dark:text-violet-200' },
    { bg: 'bg-indigo-500 dark:bg-indigo-800', text: 'text-indigo-100 dark:text-indigo-50', accent: 'bg-indigo-300 dark:bg-indigo-600', highlight: 'text-indigo-300 dark:text-indigo-200' },
    { bg: 'bg-sky-500 dark:bg-sky-800', text: 'text-sky-100 dark:text-sky-50', accent: 'bg-sky-300 dark:bg-sky-600', highlight: 'text-sky-300 dark:text-sky-200' },
    { bg: 'bg-emerald-500 dark:bg-emerald-800', text: 'text-emerald-100 dark:text-emerald-50', accent: 'bg-emerald-300 dark:bg-emerald-600', highlight: 'text-emerald-300 dark:text-emerald-200' }
  ];

  return (
    <section className="bg-gradient-to-br from-background to-muted dark:from-gray-900 dark:to-gray-800 py-section-y" aria-labelledby={id ? `bento-heading-${id}` : 'bento-heading'}>
      <div className="mx-auto max-w-container px-container-x lg:px-container-x-lg">
        <motion.div 
          className="mx-auto max-w-content text-center"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ 
            duration: 0.7,
            type: "spring",
            stiffness: 100
          }}
        >
          {tagline && (
            <motion.p 
              className="text-base/7 font-semibold text-primary dark:text-violet-400"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2, duration: 0.5 }}
            >
              {tagline}
            </motion.p>
          )}
          {title && (
            <motion.h2 
              id={id ? `bento-heading-${id}` : 'bento-heading'}
              className="mt-element-y text-balance text-center text-heading-2/heading-2 font-semibold tracking-tight text-foreground dark:text-white sm:text-heading-1/heading-1"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ 
                delay: 0.3, 
                duration: 0.6,
                type: "spring",
                stiffness: 100
              }}
            >
              {title}
            </motion.h2>
          )}
        </motion.div>
        
        <div className="mx-auto mt-section-y max-w-container">
          {/* 交错式六边形网格布局 */}
          <div className="grid grid-cols-1 md:grid-cols-6 gap-element-y md:gap-element-x">
            {/* 第一个区块 - 左上角，占据3列 */}
            <motion.div 
              className="md:col-span-3 md:row-span-2 md:translate-y-12"
              variants={cardVariants}
              custom={0}
              initial="initial"
              animate="animate"
              whileHover="hover"
              whileTap="tap"
              onHoverStart={() => setActiveCard(0)}
              onHoverEnd={() => setActiveCard(null)}
            >
              <div className={`relative overflow-hidden rounded-3xl ${colors[0].bg} h-full`} aria-labelledby="bento-animated-item-0-title">
                <motion.div 
                  className="absolute -right-20 -top-20 w-40 h-40 rounded-full bg-white opacity-10"
                  animate={{ 
                    x: [0, 10, 0],
                    y: [0, -10, 0],
                  }}
                  transition={{ 
                    duration: 5, 
                    repeat: Infinity,
                    repeatType: "reverse" 
                  }}
                  aria-hidden="true"
                />
                <motion.div 
                  className="absolute -left-10 -bottom-10 w-20 h-20 rounded-full bg-white opacity-10"
                  animate={{ 
                    x: [0, -10, 0],
                    y: [0, 10, 0],
                  }}
                  transition={{ 
                    duration: 4, 
                    repeat: Infinity,
                    repeatType: "reverse",
                    delay: 1
                  }}
                  aria-hidden="true"
                />
                <div className="absolute inset-0 pointer-events-none" aria-hidden="true">
                  <svg className="w-full h-full">
                    <motion.rect
                      width="100%"
                      height="100%"
                      rx="24" // 圆角大小
                      fill="none"
                      stroke="#ffffff"
                      strokeWidth="2"
                      variants={borderVariants}
                      initial="initial"
                      animate={activeCard === 0 ? "animate" : "initial"}
                    />
                  </svg>
                </div>
                <div className="p-8 flex flex-col h-full">
                  <div className="mb-6">
                    <motion.div
                      className="relative w-full overflow-hidden rounded-xl"
                      style={{ height: '32rem' }}
                      whileHover={{ scale: 1.05 }}
                      transition={{ duration: 0.3 }}
                    >
                      <Picture
                        src={safeItems[0].img}
                        alt={safeItems[0].alt || "Feature image"}
                        widths={[480, 640, 768, 1024, 1200]}
                        sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 600px"
                        quality={{ avif: 70, webp: 75, jpeg: 80, png: 85 }}
                        forceFillHeight={true}
                        className="w-full h-full object-cover"
                        aria-describedby="bento-animated-item-0-title"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-violet-900/70 dark:from-violet-900/70 via-violet-900/30 dark:via-violet-900/30 to-transparent" aria-hidden="true"></div>
                    </motion.div>
                  </div>
                  
                  {safeItems[0].tagline && (
                    <motion.p 
                      className={`text-sm font-medium ${colors[0].highlight}`}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.4, duration: 0.5 }}
                    >
                      {safeItems[0].tagline}
                    </motion.p>
                  )}
                  <motion.h3 
                    id="bento-animated-item-0-title"
                    className="mt-element-y text-heading-3/heading-3 font-semibold tracking-tight text-white dark:text-white"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.5, duration: 0.5 }}
                  >
                    {safeItems[0].title}
                  </motion.h3>
                  <motion.p 
                    className={`mt-element-y text-body ${colors[0].text}`}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.6, duration: 0.5 }}
                    aria-labelledby="bento-animated-item-0-title"
                  >
                    {safeItems[0].description}
                  </motion.p>
                </div>
              </div>
            </motion.div>

            {/* 第二个区块 - 右上角，占据3列 */}
            <motion.div 
              className="md:col-span-3 md:col-start-4"
              variants={cardVariants}
              custom={1}
              initial="initial"
              animate="animate"
              whileHover="hover"
              whileTap="tap"
              onHoverStart={() => setActiveCard(1)}
              onHoverEnd={() => setActiveCard(null)}
            >
              <div className={`relative overflow-hidden rounded-3xl ${colors[1].bg} h-full`} aria-labelledby="bento-animated-item-1-title">
                <motion.div 
                  className="absolute -right-10 -bottom-10 w-20 h-20 rounded-full bg-white opacity-10"
                  animate={{ 
                    x: [0, 10, 0],
                    y: [0, 10, 0],
                  }}
                  transition={{ 
                    duration: 4, 
                    repeat: Infinity,
                    repeatType: "reverse" 
                  }}
                />
                <div className="absolute inset-0 pointer-events-none" aria-hidden="true">
                  <svg className="w-full h-full">
                    <motion.rect
                      width="100%"
                      height="100%"
                      rx="24" // 圆角大小
                      fill="none"
                      stroke="#ffffff"
                      strokeWidth="2"
                      variants={borderVariants}
                      initial="initial"
                      animate={activeCard === 1 ? "animate" : "initial"}
                    />
                  </svg>
                </div>
                <div className="p-6 flex flex-col h-full">
                  <div className="mb-4">
                    <motion.div
                      className="relative w-full h-48 overflow-hidden rounded-xl"
                      whileHover={{ scale: 1.05 }}
                      transition={{ duration: 0.3 }}
                    >
                      <Picture
                        src={safeItems[1].img}
                        alt={safeItems[1].alt || "Feature image"}
                        widths={[375, 480, 640, 768, 1024]}
                        sizes="(max-width: 768px) 100vw, (max-width: 1024px) 40vw, 400px"
                        quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                        forceFillHeight={true}
                        className="w-full h-full object-cover"
                        aria-describedby="bento-animated-item-1-title"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-indigo-900/70 dark:from-indigo-900/70 via-indigo-900/30 dark:via-indigo-900/30 to-transparent" aria-hidden="true"></div>
                    </motion.div>
                  </div>
                  
                  {safeItems[1].tagline && (
                    <motion.p 
                      className={`text-sm font-medium ${colors[1].highlight}`}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.5, duration: 0.5 }}
                    >
                      {safeItems[1].tagline}
                    </motion.p>
                  )}
                  <motion.h3 
                    id="bento-animated-item-1-title"
                    className="mt-element-y text-heading-4/heading-4 font-semibold tracking-tight text-white dark:text-white"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.6, duration: 0.5 }}
                  >
                    {safeItems[1].title}
                  </motion.h3>
                  <motion.p 
                    className={`mt-element-y text-body-sm ${colors[1].text}`}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.7, duration: 0.5 }}
                    aria-labelledby="bento-animated-item-1-title"
                  >
                    {safeItems[1].description}
                  </motion.p>
                </div>
              </div>
            </motion.div>

            {/* 第三个区块 - 右下角，占据2列 */}
            <motion.div 
              className="md:col-span-2 md:col-start-4 md:row-start-2 md:translate-y-12"
              variants={cardVariants}
              custom={2}
              initial="initial"
              animate="animate"
              whileHover="hover"
              whileTap="tap"
              onHoverStart={() => setActiveCard(2)}
              onHoverEnd={() => setActiveCard(null)}
            >
              <div className={`relative overflow-hidden rounded-3xl ${colors[2].bg} h-full`} aria-labelledby="bento-animated-item-2-title">
                <motion.div 
                  className="absolute -left-10 -top-10 w-20 h-20 rounded-full bg-white opacity-10"
                  animate={{ 
                    x: [0, -10, 0],
                    y: [0, -10, 0],
                  }}
                  transition={{ 
                    duration: 4, 
                    repeat: Infinity,
                    repeatType: "reverse" 
                  }}
                  aria-hidden="true"
                />
                <div className="absolute inset-0 pointer-events-none" aria-hidden="true">
                  <svg className="w-full h-full">
                    <motion.rect
                      width="100%"
                      height="100%"
                      rx="24" // 圆角大小
                      fill="none"
                      stroke="#ffffff"
                      strokeWidth="2"
                      variants={borderVariants}
                      initial="initial"
                      animate={activeCard === 2 ? "animate" : "initial"}
                    />
                  </svg>
                </div>
                <div className="p-6 flex flex-col h-full">
                  {safeItems[2].tagline && (
                    <motion.p 
                      className={`text-sm font-medium ${colors[2].highlight}`}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.6, duration: 0.5 }}
                    >
                      {safeItems[2].tagline}
                    </motion.p>
                  )}
                  <motion.h3 
                    id="bento-animated-item-2-title"
                    className="mt-element-y text-heading-4/heading-4 font-semibold tracking-tight text-white dark:text-white"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.7, duration: 0.5 }}
                  >
                    {safeItems[2].title}
                  </motion.h3>
                  <motion.p 
                    className={`mt-element-y text-body-sm ${colors[2].text}`}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.8, duration: 0.5 }}
                    aria-labelledby="bento-animated-item-2-title"
                  >
                    {safeItems[2].description}
                  </motion.p>
                  
                  <div className="mt-auto pt-4">
                    <motion.div
                      className="relative w-full h-40 overflow-hidden rounded-xl"
                      whileHover={{ scale: 1.05 }}
                      transition={{ duration: 0.3 }}
                    >
                      <Picture
                        src={safeItems[2].img}
                        alt={safeItems[2].alt || "Feature image"}
                        widths={[375, 480, 640, 768]}
                        sizes="(max-width: 768px) 100vw, (max-width: 1024px) 30vw, 300px"
                        quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                        forceFillHeight={true}
                        className="w-full h-full object-cover"
                        aria-describedby="bento-animated-item-2-title"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-sky-900/70 dark:from-sky-900/70 via-sky-900/30 dark:via-sky-900/30 to-transparent" aria-hidden="true"></div>
                    </motion.div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* 第四个区块 - 右下角，占据1列 */}
            <motion.div 
              className="md:col-span-1 md:col-start-6 md:row-start-2 md:translate-y-12"
              variants={cardVariants}
              custom={3}
              initial="initial"
              animate="animate"
              whileHover="hover"
              whileTap="tap"
              onHoverStart={() => setActiveCard(3)}
              onHoverEnd={() => setActiveCard(null)}
            >
              <div className={`relative overflow-hidden rounded-3xl ${colors[3].bg} h-full`} aria-labelledby="bento-animated-item-3-title">
                <motion.div 
                  className="absolute -right-10 -bottom-10 w-20 h-20 rounded-full bg-white opacity-10"
                  animate={{ 
                    x: [0, 10, 0],
                    y: [0, 10, 0],
                  }}
                  transition={{ 
                    duration: 4, 
                    repeat: Infinity,
                    repeatType: "reverse" 
                  }}
                />
                <div className="absolute inset-0 pointer-events-none" aria-hidden="true">
                  <svg className="w-full h-full">
                    <motion.rect
                      width="100%"
                      height="100%"
                      rx="24" // 圆角大小
                      fill="none"
                      stroke="#ffffff"
                      strokeWidth="2"
                      variants={borderVariants}
                      initial="initial"
                      animate={activeCard === 3 ? "animate" : "initial"}
                    />
                  </svg>
                </div>
                <div className="p-5 flex flex-col h-full">
                  <motion.div
                    className="relative w-full h-28 overflow-hidden rounded-xl mb-3"
                    whileHover={{ scale: 1.05 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Picture
                      src={safeItems[3].img}
                      alt={safeItems[3].alt || "Feature image"}
                      widths={[320, 375, 480, 640]}
                      sizes="(max-width: 768px) 100vw, (max-width: 1024px) 20vw, 200px"
                      quality={{ avif: 60, webp: 65, jpeg: 70, png: 75 }}
                      forceFillHeight={true}
                      className="w-full h-full object-cover"
                      aria-describedby="bento-animated-item-3-title"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-emerald-900/70 dark:from-emerald-900/70 via-emerald-900/30 dark:via-emerald-900/30 to-transparent" aria-hidden="true"></div>
                  </motion.div>
                  
                  {safeItems[3].tagline && (
                    <motion.p 
                      className={`text-xs font-medium ${colors[3].highlight}`}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.7, duration: 0.5 }}
                    >
                      {safeItems[3].tagline}
                    </motion.p>
                  )}
                  <motion.h3 
                    id="bento-animated-item-3-title"
                    className="mt-element-y-sm text-heading-5/heading-5 font-semibold tracking-tight text-white"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.8, duration: 0.5 }}
                  >
                    {safeItems[3].title}
                  </motion.h3>
                  <motion.p 
                    className={`mt-element-y-sm text-body-xs ${colors[3].text}`}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.9, duration: 0.5 }}
                    aria-labelledby="bento-animated-item-3-title"
                  >
                    {safeItems[3].description}
                  </motion.p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
}
