import ThreeColumn from './ThreeColumn';
import TwoRow4 from './TwoRow4';
// import TwoRow5 from './TwoRow5
import Modern from './Modern';
import Minimal from './Minimal';
import Colorful from './Colorful';
import Card from './Card';
import Background from './Background';
import Enterprise from './Enterprise';
import Compact from './Compact';
import Vibrant from './Vibrant';
import BasicToggle from './BasicToggle';
import Parallax from './Parallax';
import AnimatedGrid from './AnimatedGrid';
import TechGlow from './TechGlow';
import BlueTech from './BlueTech';

import type { BentoSectionProps } from './types';
import { defaultBentoProps } from './defaults';

// 导出默认值
export { defaultBentoProps };

type BentoVariant = 
  | 'default' 
  | 'threeColumn' 
  | 'twoRow4' 
  | 'modern' 
  | 'minimal' 
  | 'colorful' 
  | 'card' 
  | 'background' 
  | 'enterprise' 
  | 'compact' 
  | 'vibrant' 
  | 'basicToggle' 
  | 'parallax'
  | 'animatedGrid'
  | 'techGlow'
  | 'blueTech';

export const BentoVariants = [
  'default', 
  'threeColumn', 
  'twoRow4', 
  'modern', 
  'minimal', 
  'colorful', 
  'card', 
  'background', 
  'enterprise', 
  'compact', 
  'vibrant', 
  'basicToggle', 
  'parallax',
  'animatedGrid',
  'techGlow',
  'blueTech'
];

export interface BentoFactoryProps extends BentoSectionProps {
  variant?: BentoVariant;
}

// 生成组件列表
export const Components: Record<BentoVariant, React.FC<BentoSectionProps>> = {
  default: ThreeColumn,
  threeColumn: ThreeColumn,
  twoRow4: TwoRow4,
  modern: Modern,
  minimal: Minimal,
  colorful: Colorful,
  card: Card,
  background: Background,
  enterprise: Enterprise,
  compact: Compact,
  vibrant: Vibrant,
  basicToggle: BasicToggle,
  parallax: Parallax,
  animatedGrid: AnimatedGrid,
  techGlow: TechGlow,
  blueTech: BlueTech
};

// Icon 组件工厂
export const BentoFactory: React.FC<BentoFactoryProps> = ({ 
  variant = 'threeColumn', 
  tagline = defaultBentoProps.tagline,
  title = defaultBentoProps.title,
  items = defaultBentoProps.items,
  id,
  ...props 
}) => {
  const Component = Components[variant];
  if (!Component) {
    console.error(`Invalid variant: ${variant}`);
    return null;
  }
  return <Component 
    tagline={tagline} 
    title={title} 
    items={items}
    id={id} 
    {...props} 
  />;
}
