import React from 'react';
import { BentoSectionProps } from './types';
import { Picture } from '../components/Picture';

export default function Compact({ id, tagline, title, items }: BentoSectionProps) {
  // 确保items数组至少有4个元素
  const safeItems = items && items.length >= 4 ? items : Array(4).fill({
    tagline: '',
    title: 'Feature',
    description: 'Description text here',
    img: 'https://images.unsplash.com/photo-1624571409412-1f253e1ecc89?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&q=80',
    alt: 'Feature image'
  });

  // 为每个位置分配特定的图片尺寸，增大25%
  const imgSizes = [
    'w=750&h=375', // 第一个位置 - 左上大区块
    'w=500&h=375', // 第二个位置 - 右上区块
    'w=500&h=375', // 第三个位置 - 左下区块
    'w=750&h=375'  // 第四个位置 - 右下大区块
  ];

  return (
    <section className="bg-gradient-to-b from-muted to-background py-section-y" aria-labelledby={id ? `bento-heading-${id}` : 'bento-heading'}>
      <div className="mx-auto max-w-container px-container-x">
        {/* 标题区域 */}
        <div className="mx-auto max-w-container text-center">
          <div className="max-w-content mx-auto">
            {tagline && <p className="text-body-base/body-base font-semibold text-primary">{tagline}</p>}
            {title && <h2 id={id ? `bento-heading-${id}` : 'bento-heading'} className="mt-element-y text-heading-2/heading-2 font-bold tracking-tight text-foreground">{title}</h2>}
          </div>
        </div>
        
        {/* 紧凑不规则网格布局 - 增大25%并添加间隙 */}
        <div className="mx-auto mt-content-y max-w-container">
          <div className="overflow-hidden rounded-3xl bg-muted shadow-md">
            {/* 移动端布局 - 单列 */}
            <div className="md:hidden grid grid-cols-1 gap-element-y p-element-y bg-muted">
              {/* 第一个区块 - 移动端 */}
              <div className="bg-card group relative overflow-hidden rounded-3xl shadow-sm" aria-labelledby="bento-compact-mobile-item-0-title">
                <div className="absolute inset-0 bg-gradient-to-br from-indigo-50 to-white dark:from-indigo-900/40 dark:to-gray-900/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" aria-hidden="true"></div>
                <div className="relative h-full p-element-y flex flex-col">
                  <div className="mb-element-y">
                    {safeItems[0].tagline && (
                      <p className="text-body-small/body-small font-medium text-indigo-600 dark:text-indigo-400">{safeItems[0].tagline}</p>
                    )}
                    <h3 id="bento-compact-mobile-item-0-title" className="mt-element-y text-heading-4/heading-4 font-semibold text-gray-900 dark:text-gray-100">{safeItems[0].title}</h3>
                    <p className="mt-element-y text-body-small/body-small text-muted-foreground line-clamp-3" aria-labelledby="bento-compact-mobile-item-0-title">{safeItems[0].description}</p>
                  </div>
                  <div className="mt-auto relative overflow-hidden rounded-3xl h-48 md:h-56">
                    <Picture
                      src={safeItems[0].img}
                      alt={safeItems[0].alt || "Feature image"}
                      widths={[375, 480, 640]}
                      sizes="(max-width: 768px) 90vw, 400px"
                      quality={{ avif: 60, webp: 65, jpeg: 70, png: 75 }}
                      forceFillHeight={true}
                      className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                      aria-describedby="bento-compact-mobile-item-0-title"
                    />
                    <div className="absolute bottom-0 left-0 right-0 h-1/3 bg-gradient-to-t from-black/30 to-transparent" aria-hidden="true"></div>
                  </div>
                </div>
              </div>

              {/* 第二个区块 - 移动端 */}
              <div className="bg-card group relative overflow-hidden rounded-3xl shadow-sm" aria-labelledby="bento-compact-mobile-item-1-title">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-white dark:from-blue-900/40 dark:to-gray-900/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" aria-hidden="true"></div>
                <div className="relative h-full p-element-y flex flex-col">
                  <div className="mb-element-y">
                    {safeItems[1].tagline && (
                      <p className="text-body-small/body-small font-medium text-blue-600 dark:text-blue-400">{safeItems[1].tagline}</p>
                    )}
                    <h3 id="bento-compact-mobile-item-1-title" className="mt-element-y text-heading-4/heading-4 font-semibold text-gray-900 dark:text-gray-100">{safeItems[1].title}</h3>
                    <p className="mt-element-y text-body-small/body-small text-muted-foreground line-clamp-3" aria-labelledby="bento-compact-mobile-item-1-title">{safeItems[1].description}</p>
                  </div>
                  <div className="mt-auto relative overflow-hidden rounded-3xl h-48 md:h-56">
                    <Picture
                      src={safeItems[1].img}
                      alt={safeItems[1].alt || "Feature image"}
                      widths={[375, 480, 640]}
                      sizes="(max-width: 768px) 90vw, 400px"
                      quality={{ avif: 60, webp: 65, jpeg: 70, png: 75 }}
                      forceFillHeight={true}
                      className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                      aria-describedby="bento-compact-mobile-item-1-title"
                    />
                    <div className="absolute bottom-0 left-0 right-0 h-1/3 bg-gradient-to-t from-black/30 to-transparent" aria-hidden="true"></div>
                  </div>
                </div>
              </div>

              {/* 第三个区块 - 移动端 */}
              <div className="bg-card group relative overflow-hidden rounded-3xl shadow-sm" aria-labelledby="bento-compact-mobile-item-2-title">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-50 to-white dark:from-purple-900/40 dark:to-gray-900/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" aria-hidden="true"></div>
                <div className="relative h-full p-element-y flex flex-col">
                  <div className="mb-element-y">
                    {safeItems[2].tagline && (
                      <p className="text-body-small/body-small font-medium text-purple-600 dark:text-purple-400">{safeItems[2].tagline}</p>
                    )}
                    <h3 id="bento-compact-mobile-item-2-title" className="mt-element-y text-heading-4/heading-4 font-semibold text-gray-900 dark:text-gray-100">{safeItems[2].title}</h3>
                    <p className="mt-element-y text-body-small/body-small text-muted-foreground line-clamp-3" aria-labelledby="bento-compact-mobile-item-2-title">{safeItems[2].description}</p>
                  </div>
                  <div className="mt-auto relative overflow-hidden rounded-3xl h-48 md:h-56">
                    <Picture
                      src={safeItems[2].img}
                      alt={safeItems[2].alt || "Feature image"}
                      widths={[375, 480, 640]}
                      sizes="(max-width: 768px) 90vw, 400px"
                      quality={{ avif: 60, webp: 65, jpeg: 70, png: 75 }}
                      forceFillHeight={true}
                      className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                      aria-describedby="bento-compact-mobile-item-2-title"
                    />
                    <div className="absolute bottom-0 left-0 right-0 h-1/3 bg-gradient-to-t from-black/30 to-transparent" aria-hidden="true"></div>
                  </div>
                </div>
              </div>

              {/* 第四个区块 - 移动端 */}
              <div className="bg-card group relative overflow-hidden rounded-3xl shadow-sm" aria-labelledby="bento-compact-mobile-item-3-title">
                <div className="absolute inset-0 bg-gradient-to-br from-emerald-50 to-white dark:from-emerald-900/40 dark:to-gray-900/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" aria-hidden="true"></div>
                <div className="relative h-full p-element-y flex flex-col">
                  <div className="mb-element-y">
                    {safeItems[3].tagline && (
                      <p className="text-body-small/body-small font-medium text-emerald-600 dark:text-emerald-400">{safeItems[3].tagline}</p>
                    )}
                    <h3 id="bento-compact-mobile-item-3-title" className="mt-element-y text-heading-4/heading-4 font-semibold text-gray-900 dark:text-gray-100">{safeItems[3].title}</h3>
                    <p className="mt-element-y text-body-small/body-small text-muted-foreground line-clamp-3" aria-labelledby="bento-compact-mobile-item-3-title">{safeItems[3].description}</p>
                  </div>
                  <div className="mt-auto relative overflow-hidden rounded-3xl h-48 md:h-56">
                    <Picture
                      src={safeItems[3].img}
                      alt={safeItems[3].alt || "Feature image"}
                      widths={[375, 480, 640]}
                      sizes="(max-width: 768px) 90vw, 400px"
                      quality={{ avif: 60, webp: 65, jpeg: 70, png: 75 }}
                      forceFillHeight={true}
                      className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                      aria-describedby="bento-compact-mobile-item-3-title"
                    />
                    <div className="absolute bottom-0 left-0 right-0 h-1/3 bg-gradient-to-t from-black/30 to-transparent" aria-hidden="true"></div>
                  </div>
                </div>
              </div>
            </div>

            {/* 桌面端布局 - 不规则网格 */}
            <div className="hidden md:grid grid-cols-3 grid-rows-4 gap-element-y p-element-y bg-muted">
              {/* 第一个区块 - 左上大区块 (占据2列2行) */}
              <div className="col-span-2 row-span-2 bg-card group relative overflow-hidden rounded-3xl shadow-sm" aria-labelledby="bento-compact-item-0-title">
                <div className="absolute inset-0 bg-gradient-to-br from-indigo-50 to-white dark:from-indigo-900/40 dark:to-gray-900/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" aria-hidden="true"></div>
                <div className="relative h-full p-element-y flex flex-col">
                  <div className="mb-element-y">
                    {safeItems[0].tagline && (
                      <p className="text-body-small/body-small font-medium text-indigo-600 dark:text-indigo-400">{safeItems[0].tagline}</p>
                    )}
                    <h3 id="bento-compact-item-0-title" className="mt-element-y text-heading-3/heading-3 font-semibold text-gray-900 dark:text-gray-100">{safeItems[0].title}</h3>
                    <p className="mt-element-y text-body-base/body-base text-muted-foreground line-clamp-3" aria-labelledby="bento-compact-item-0-title">{safeItems[0].description}</p>
                  </div>
                  <div className="mt-auto relative overflow-hidden rounded-3xl h-48 md:h-56 lg:h-64 xl:h-80">
                    <Picture
                      src={safeItems[0].img}
                      alt={safeItems[0].alt || "Feature image"}
                      widths={[480, 640, 768, 1024, 1200]}
                      sizes="(max-width: 1024px) 60vw, 600px"
                      quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                      forceFillHeight={true}
                      className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                      aria-describedby="bento-compact-item-0-title"
                    />
                    <div className="absolute bottom-0 left-0 right-0 h-1/3 bg-gradient-to-t from-black/30 to-transparent" aria-hidden="true"></div>
                  </div>
                </div>
              </div>

              {/* 第二个区块 - 右上区块 (占据1列2行) */}
              <div className="row-span-2 bg-card group relative overflow-hidden rounded-3xl shadow-sm" aria-labelledby="bento-compact-item-1-title">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-white dark:from-blue-900/40 dark:to-gray-900/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" aria-hidden="true"></div>
                <div className="relative h-full p-element-y flex flex-col">
                  <div className="mb-element-y">
                    {safeItems[1].tagline && (
                      <p className="text-body-small/body-small font-medium text-blue-600 dark:text-blue-400">{safeItems[1].tagline}</p>
                    )}
                    <h3 id="bento-compact-item-1-title" className="mt-element-y text-heading-4/heading-4 font-semibold text-gray-900 dark:text-gray-100">{safeItems[1].title}</h3>
                    <p className="mt-element-y text-body-small/body-small text-muted-foreground line-clamp-3" aria-labelledby="bento-compact-item-1-title">{safeItems[1].description}</p>
                  </div>
                  <div className="mt-auto relative overflow-hidden rounded-3xl h-36 md:h-44 lg:h-52 xl:h-64">
                    <Picture
                      src={safeItems[1].img}
                      alt={safeItems[1].alt || "Feature image"}
                      widths={[375, 480, 640, 768]}
                      sizes="(max-width: 1024px) 30vw, 300px"
                      quality={{ avif: 60, webp: 65, jpeg: 70, png: 75 }}
                      forceFillHeight={true}
                      className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                      aria-describedby="bento-compact-item-1-title"
                    />
                    <div className="absolute bottom-0 left-0 right-0 h-1/3 bg-gradient-to-t from-black/30 to-transparent" aria-hidden="true"></div>
                  </div>
                </div>
              </div>

              {/* 第三个区块 - 左下区块 (占据1列2行) */}
              <div className="row-span-2 bg-card group relative overflow-hidden rounded-3xl shadow-sm" aria-labelledby="bento-compact-item-2-title">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-50 to-white dark:from-purple-900/40 dark:to-gray-900/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" aria-hidden="true"></div>
                <div className="relative h-full p-element-y flex flex-col">
                  <div className="mb-element-y">
                    {safeItems[2].tagline && (
                      <p className="text-body-small/body-small font-medium text-purple-600 dark:text-purple-400">{safeItems[2].tagline}</p>
                    )}
                    <h3 id="bento-compact-item-2-title" className="mt-element-y text-heading-4/heading-4 font-semibold text-gray-900 dark:text-gray-100">{safeItems[2].title}</h3>
                    <p className="mt-element-y text-body-small/body-small text-muted-foreground line-clamp-3" aria-labelledby="bento-compact-item-2-title">{safeItems[2].description}</p>
                  </div>
                  <div className="mt-auto relative overflow-hidden rounded-3xl h-36 md:h-44 lg:h-52 xl:h-64">
                    <Picture
                      src={safeItems[2].img}
                      alt={safeItems[2].alt || "Feature image"}
                      widths={[375, 480, 640, 768]}
                      sizes="(max-width: 1024px) 30vw, 300px"
                      quality={{ avif: 60, webp: 65, jpeg: 70, png: 75 }}
                      forceFillHeight={true}
                      className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                      aria-describedby="bento-compact-item-2-title"
                    />
                    <div className="absolute bottom-0 left-0 right-0 h-1/3 bg-gradient-to-t from-black/30 to-transparent" aria-hidden="true"></div>
                  </div>
                </div>
              </div>

              {/* 第四个区块 - 右下大区块 (占据2列2行) */}
              <div className="col-span-2 row-span-2 bg-card group relative overflow-hidden rounded-3xl shadow-sm" aria-labelledby="bento-compact-item-3-title">
                <div className="absolute inset-0 bg-gradient-to-br from-emerald-50 to-white dark:from-emerald-900/40 dark:to-gray-900/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" aria-hidden="true"></div>
                <div className="relative h-full p-element-y flex flex-col">
                  <div className="mb-element-y">
                    {safeItems[3].tagline && (
                      <p className="text-body-small/body-small font-medium text-emerald-600 dark:text-emerald-400">{safeItems[3].tagline}</p>
                    )}
                    <h3 id="bento-compact-item-3-title" className="mt-element-y text-heading-3/heading-3 font-semibold text-gray-900 dark:text-gray-100">{safeItems[3].title}</h3>
                    <p className="mt-element-y text-body-base/body-base text-muted-foreground line-clamp-3" aria-labelledby="bento-compact-item-3-title">{safeItems[3].description}</p>
                  </div>
                  <div className="mt-auto relative overflow-hidden rounded-3xl h-48 md:h-56 lg:h-64 xl:h-80">
                    <Picture
                      src={safeItems[3].img}
                      alt={safeItems[3].alt || "Feature image"}
                      widths={[480, 640, 768, 1024, 1200]}
                      sizes="(max-width: 1024px) 60vw, 600px"
                      quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                      forceFillHeight={true}
                      className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                      aria-describedby="bento-compact-item-3-title"
                    />
                    <div className="absolute bottom-0 left-0 right-0 h-1/3 bg-gradient-to-t from-black/30 to-transparent" aria-hidden="true"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
