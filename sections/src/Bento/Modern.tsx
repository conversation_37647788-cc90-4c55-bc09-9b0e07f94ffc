import React from 'react';
import { BentoSectionProps } from './types';
import { Picture } from '../components/Picture';

export default function Modern({ id, tagline, title, items }: BentoSectionProps) {
  // 确保items数组至少有4个元素
  const safeItems = items && items.length >= 4 ? items : Array(4).fill({
    title: 'Feature',
    description: 'Description text here',
    img: 'https://images.unsplash.com/photo-1624571409412-1f253e1ecc89?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&q=80',
    alt: 'Feature image'
  });

  // 为每个位置分配特定的图片尺寸
  const imgSizes = [
    'w=800&h=1200', // 第一个位置 - 左侧大区块
    'w=800&h=400',  // 第二个位置 - 右上区块
    'w=800&h=400',  // 第三个位置 - 右中区块
    'w=1200&h=400'   // 第四个位置 - 右下区块（宽度增加）
  ];

  return (
    <section className="bg-gradient-to-br from-indigo-50 via-white to-indigo-50 dark:from-gray-900 dark:via-indigo-950 dark:to-gray-900 py-section-y overflow-hidden" aria-labelledby={id ? `bento-heading-${id}` : 'bento-heading'}>
      <div className="mx-auto max-w-container px-container-x relative">
        {/* 静态装饰性背景元素 */}
        <div className="absolute top-20 left-1/4 w-72 h-72 bg-indigo-500 rounded-full filter blur-3xl opacity-10 dark:opacity-5" aria-hidden="true"></div>
        <div className="absolute bottom-20 right-1/4 w-72 h-72 bg-purple-500 rounded-full filter blur-3xl opacity-10 dark:opacity-5" aria-hidden="true"></div>
        
        {tagline && <p className="text-center text-body-base/body-base font-semibold text-indigo-600 dark:text-indigo-400 tracking-wider">{tagline}</p>}
        {title && (
          <h2 id={id ? `bento-heading-${id}` : 'bento-heading'} className="mx-auto mt-element-y max-w-content text-balance text-center text-heading-2/heading-2 font-semibold tracking-tight bg-clip-text bg-gradient-to-r from-indigo-700 to-indigo-500 dark:from-indigo-200 dark:to-indigo-400 text-transparent">
            {title}
          </h2>
        )}
        <div className="mt-content-y grid gap-element-y lg:grid-cols-3 lg:grid-rows-2">
          {/* 左侧大区块 - 占据两行 */}
          <div className="relative lg:row-span-2 group" aria-labelledby="bento-item-0-title">
            <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-indigo-500 to-purple-600 opacity-5 dark:opacity-10 transition-opacity duration-200 group-hover:opacity-10 dark:group-hover:opacity-10"></div>
            <div className="relative h-full overflow-hidden rounded-2xl bg-white shadow-sm dark:bg-gray-800/90 ring-1 ring-indigo-200 dark:ring-white/20 transition-transform duration-200 group-hover:shadow-xl dark:group-hover:shadow-none group-hover:scale-[1.02] flex flex-col">
              <div className="p-8 flex-shrink-0">
                {safeItems[0].tagline && (
                  <p className="text-body-small/body-small font-medium text-indigo-600 dark:text-indigo-400 tracking-wide">{safeItems[0].tagline}</p>
                )}
                <h3 id="bento-item-0-title" className="mt-element-y text-heading-3/heading-3 font-medium tracking-tight bg-clip-text bg-gradient-to-r from-indigo-800 to-indigo-600 dark:from-white dark:to-indigo-100 text-transparent">
                  {safeItems[0].title}
                </h3>
                <p className="mt-element-y text-body-base/body-base text-gray-700 dark:text-gray-300/90" aria-labelledby="bento-item-0-title">
                  {safeItems[0].description}
                </p>
              </div>
              <div className="relative flex-grow min-h-[200px]">
                <Picture
                  src={safeItems[0].img}
                  alt={safeItems[0].alt || "Feature image"}
                  widths={[375, 480, 640, 768, 1024, 1200]}
                  sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 600px"
                  quality={{ avif: 70, webp: 75, jpeg: 80, png: 85 }}
                  forceFillHeight={true}
                  className="w-full h-full object-cover object-center rounded-b-2xl transition-transform duration-200 group-hover:scale-105"
                  aria-describedby="bento-item-0-title"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-indigo-700/60 via-indigo-700/30 to-transparent dark:from-indigo-900/70 dark:via-indigo-900/40 dark:to-transparent rounded-b-2xl"></div>
              </div>
            </div>
          </div>

          {/* 右上区块 */}
          <div className="relative group" aria-labelledby="bento-item-1-title">
            <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-blue-500 to-cyan-400 opacity-5 dark:opacity-10 transition-opacity duration-200 group-hover:opacity-10 dark:group-hover:opacity-10"></div>
            <div className="relative h-full overflow-hidden rounded-2xl bg-white shadow-sm dark:bg-gray-800/90 ring-1 ring-blue-200 dark:ring-white/20 transition-transform duration-200 group-hover:shadow-xl dark:group-hover:shadow-none group-hover:scale-[1.02]">
              <div className="p-6 pb-36 md:pb-40 lg:pb-44">
                {safeItems[1].tagline && (
                  <p className="text-body-small/body-small font-medium text-blue-600 dark:text-blue-400 tracking-wide">{safeItems[1].tagline}</p>
                )}
                <h3 id="bento-item-1-title" className="mt-element-y text-heading-4/heading-4 font-medium tracking-tight bg-clip-text bg-gradient-to-r from-blue-800 to-blue-600 dark:from-white dark:to-blue-100 text-transparent">
                  {safeItems[1].title}
                </h3>
                <p className="mt-element-y text-body-small/body-small text-gray-700 dark:text-gray-300/90" aria-labelledby="bento-item-1-title">
                  {safeItems[1].description}
                </p>
              </div>
              <div className="absolute bottom-0 left-0 right-0 h-32 md:h-36 lg:h-40">
                <Picture
                  src={safeItems[1].img}
                  alt={safeItems[1].alt || "Feature image"}
                  widths={[375, 480, 640, 768, 1024]}
                  sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 400px"
                  quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                  forceFillHeight={true}
                  className="w-full h-full object-cover object-center rounded-b-2xl transition-transform duration-200 group-hover:scale-105"
                  aria-describedby="bento-item-1-title"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-blue-700/60 via-blue-700/30 to-transparent dark:from-blue-900/70 dark:via-blue-900/40 dark:to-transparent rounded-b-2xl"></div>
              </div>
            </div>
          </div>

          {/* 右中区块 */}
          <div className="relative group" aria-labelledby="bento-item-2-title">
            <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-purple-500 to-pink-500 opacity-5 dark:opacity-10 transition-opacity duration-200 group-hover:opacity-10 dark:group-hover:opacity-10"></div>
            <div className="relative h-full overflow-hidden rounded-2xl bg-white shadow-sm dark:bg-gray-800/90 ring-1 ring-purple-200 dark:ring-white/20 transition-transform duration-200 group-hover:shadow-xl dark:group-hover:shadow-none group-hover:scale-[1.02]">
              <div className="p-6 pb-36 md:pb-40 lg:pb-44">
                {safeItems[2].tagline && (
                  <p className="text-body-small/body-small font-medium text-purple-600 dark:text-purple-400 tracking-wide">{safeItems[2].tagline}</p>
                )}
                <h3 id="bento-item-2-title" className="mt-element-y text-heading-4/heading-4 font-medium tracking-tight bg-clip-text bg-gradient-to-r from-purple-800 to-purple-600 dark:from-white dark:to-purple-100 text-transparent">
                  {safeItems[2].title}
                </h3>
                <p className="mt-element-y text-body-small/body-small text-gray-700 dark:text-gray-300/90" aria-labelledby="bento-item-2-title">
                  {safeItems[2].description}
                </p>
              </div>
              <div className="absolute bottom-0 left-0 right-0 h-32 md:h-36 lg:h-40">
                <Picture
                  src={safeItems[2].img}
                  alt={safeItems[2].alt || "Feature image"}
                  widths={[375, 480, 640, 768, 1024]}
                  sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 400px"
                  quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                  forceFillHeight={true}
                  className="w-full h-full object-cover object-center rounded-b-2xl transition-transform duration-200 group-hover:scale-105"
                  aria-describedby="bento-item-2-title"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-purple-700/60 via-purple-700/30 to-transparent dark:from-purple-900/70 dark:via-purple-900/40 dark:to-transparent rounded-b-2xl"></div>
              </div>
            </div>
          </div>

          {/* 右下区块 - 现在占据两列 */}
          <div className="relative lg:col-span-2 group" aria-labelledby="bento-item-3-title">
            <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-emerald-500 to-teal-500 opacity-5 dark:opacity-10 transition-opacity duration-200 group-hover:opacity-10 dark:group-hover:opacity-10"></div>
            <div className="relative h-full overflow-hidden rounded-2xl bg-white shadow-sm dark:bg-gray-800/90 ring-1 ring-emerald-200 dark:ring-white/20 transition-transform duration-200 group-hover:shadow-xl dark:group-hover:shadow-none group-hover:scale-[1.02]">
              <div className="p-6 pb-36 md:pb-40 lg:pb-44">
                {safeItems[3].tagline && (
                  <p className="text-body-small/body-small font-medium text-emerald-600 dark:text-emerald-400 tracking-wide">{safeItems[3].tagline}</p>
                )}
                <h3 id="bento-item-3-title" className="mt-element-y text-heading-4/heading-4 font-medium tracking-tight bg-clip-text bg-gradient-to-r from-emerald-800 to-emerald-600 dark:from-white dark:to-emerald-100 text-transparent">
                  {safeItems[3].title}
                </h3>
                <p className="mt-element-y text-body-small/body-small text-gray-700 dark:text-gray-300/90" aria-labelledby="bento-item-3-title">
                  {safeItems[3].description}
                </p>
              </div>
              <div className="absolute bottom-0 left-0 right-0 h-32 md:h-36 lg:h-40">
                <Picture
                  src={safeItems[3].img}
                  alt={safeItems[3].alt || "Feature image"}
                  widths={[375, 480, 640, 768, 1024, 1200, 1440]}
                  sizes="(max-width: 768px) 100vw, (max-width: 1024px) 70vw, 800px"
                  quality={{ avif: 70, webp: 75, jpeg: 80, png: 85 }}
                  forceFillHeight={true}
                  className="w-full h-full object-cover object-center rounded-b-2xl transition-transform duration-200 group-hover:scale-105"
                  aria-describedby="bento-item-3-title"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-emerald-700/60 via-emerald-700/30 to-transparent dark:from-emerald-900/70 dark:via-emerald-900/40 dark:to-transparent rounded-b-2xl"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
