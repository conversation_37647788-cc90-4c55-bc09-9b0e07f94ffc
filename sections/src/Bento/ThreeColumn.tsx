import React from 'react';
import { BentoSectionProps } from './types';
import { Picture } from '../components/Picture';

export default function ThreeColumn({ id, tagline, title, items }: BentoSectionProps) {
  // 确保items数组至少有4个元素
  const safeItems = items && items.length >= 4 ? items : Array(4).fill({
    title: 'Feature',
    description: 'Description text here',
    img: 'https://images.unsplash.com/photo-1624571409412-1f253e1ecc89?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&q=80',
    alt: 'Feature image'
  });

  return (
    <section className="bg-muted py-section-y" aria-labelledby={id ? `bento-heading-${id}` : 'bento-heading'}>
      <div className="mx-auto max-w-container px-container-x">
        {tagline && <p className="text-center text-body-base/body-base font-semibold text-primary">{tagline}</p>}
        {title && (
          <h2 id={id ? `bento-heading-${id}` : 'bento-heading'} className="mx-auto mt-element-y max-w-content text-balance text-center text-heading-2/heading-2 font-semibold tracking-tight text-foreground">
            {title}
          </h2>
        )}
        <div className="mt-content-y grid gap-element-y lg:grid-cols-3 lg:grid-rows-2">
          {/* 第一个区块 - 左侧高图 (2行跨度) */}
          <div className="relative lg:row-span-2">
            <div className="absolute inset-px rounded-lg bg-card lg:rounded-l-[2rem]" aria-hidden="true"></div>
            <div className="relative flex h-full flex-col overflow-hidden rounded-[calc(theme(borderRadius.lg)+1px)] lg:rounded-l-[calc(2rem+1px)]">
              <div className="px-element-y pb-element-y pt-element-y">
                {safeItems[0].tagline && (
                  <p className="text-body-small/body-small font-medium text-primary max-lg:text-center">{safeItems[0].tagline}</p>
                )}
                <h3 id="bento-item-0-title" className="mt-element-y text-heading-4/heading-4 font-medium tracking-tight text-foreground max-lg:text-center">
                  {safeItems[0].title}
                </h3>
                <p className="mt-element-y max-w-content text-body-small/body-small text-muted-foreground max-lg:text-center" aria-labelledby="bento-item-0-title">
                  {safeItems[0].description}
                </p>
              </div>
              <div className="relative min-h-[30rem] w-full grow [container-type:inline-size] max-lg:mx-auto max-lg:max-w-sm">
                <div className="absolute inset-x-10 bottom-0 top-10 overflow-hidden rounded-t-[12cqw] border-x-[3cqw] border-t-[3cqw] border-border bg-muted-foreground shadow-2xl">
                                      <Picture
                      src={safeItems[0].img}
                      alt={safeItems[0].alt || "Mobile friendly interface"}
                      widths={[375, 480, 640, 768, 1024, 1200]}
                      sizes="(max-width: 768px) 320px, (max-width: 1024px) 400px, 500px"
                      quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                      forceFillHeight={true}
                      className="size-full object-cover object-top"
                      aria-describedby="bento-item-0-title"
                    />
                </div>
              </div>
            </div>
            <div className="pointer-events-none absolute inset-px rounded-lg shadow ring-1 ring-border/10 lg:rounded-l-[2rem]" aria-hidden="true"></div>
          </div>

          {/* 第二个区块 - 右上图 */}
          <div className="relative max-lg:row-start-1" aria-labelledby="bento-item-1-title">
            <div className="absolute inset-px rounded-lg bg-card max-lg:rounded-t-[2rem]" aria-hidden="true"></div>
            <div className="relative flex h-full flex-col overflow-hidden rounded-[calc(theme(borderRadius.lg)+1px)] max-lg:rounded-t-[calc(2rem+1px)]">
              <div className="px-element-y pb-element-y pt-element-y">
                {safeItems[1].tagline && (
                  <p className="text-body-small/body-small font-medium text-primary max-lg:text-center">{safeItems[1].tagline}</p>
                )}
                <h3 id="bento-item-1-title" className="mt-element-y text-heading-4/heading-4 font-medium tracking-tight text-foreground max-lg:text-center">
                  {safeItems[1].title}
                </h3>
                <p className="mt-element-y max-w-content text-body-small/body-small text-muted-foreground max-lg:text-center">
                  {safeItems[1].description}
                </p>
              </div>
              <div className="relative min-h-[16rem] w-full grow">
                                  <Picture
                    src={safeItems[1].img}
                    alt={safeItems[1].alt || "Responsive interface"}
                    widths={[375, 480, 640, 768, 1024]}
                    sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 400px"
                    quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                    forceFillHeight={true}
                    className="absolute inset-0 size-full object-cover"
                  />
              </div>
            </div>
            <div className="pointer-events-none absolute inset-px rounded-lg shadow ring-1 ring-border/10 max-lg:rounded-t-[2rem]" aria-hidden="true"></div>
          </div>

          {/* 第三个区块 - 右中图 */}
          <div className="relative max-lg:row-start-3 lg:col-start-2 lg:row-start-2" aria-labelledby="bento-item-2-title">
            <div className="absolute inset-px rounded-lg bg-card" aria-hidden="true"></div>
            <div className="relative flex h-full flex-col overflow-hidden rounded-[calc(theme(borderRadius.lg)+1px)]">
              <div className="px-element-y pb-element-y pt-element-y">
                {safeItems[2].tagline && (
                  <p className="text-body-small/body-small font-medium text-primary max-lg:text-center">{safeItems[2].tagline}</p>
                )}
                <h3 id="bento-item-2-title" className="mt-element-y text-heading-4/heading-4 font-medium tracking-tight text-foreground max-lg:text-center">
                  {safeItems[2].title}
                </h3>
                <p className="mt-element-y max-w-content text-body-small/body-small text-muted-foreground max-lg:text-center" aria-labelledby="bento-item-2-title">
                  {safeItems[2].description}
                </p>
              </div>
              <div className="flex flex-1 items-center justify-center px-element-x py-element-y">
                                  <Picture
                    src={safeItems[2].img}
                    alt={safeItems[2].alt || "Analytics dashboard"}
                    widths={[375, 480, 640, 768, 1024, 1200]}
                    sizes="(max-width: 768px) 300px, (max-width: 1024px) 350px, 400px"
                    quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                    className="w-full max-lg:max-w-xs"
                    aria-describedby="bento-item-2-title"
                  />
              </div>
            </div>
            <div className="pointer-events-none absolute inset-px rounded-lg shadow ring-1 ring-border/10" aria-hidden="true"></div>
          </div>

          {/* 第四个区块 - 右侧高图 (2行跨度) */}
          <div className="relative lg:row-span-2">
            <div className="absolute inset-px rounded-lg bg-card max-lg:rounded-b-[2rem] lg:rounded-r-[2rem]" aria-hidden="true"></div>
            <div className="relative flex h-full flex-col overflow-hidden rounded-[calc(theme(borderRadius.lg)+1px)] max-lg:rounded-b-[calc(2rem+1px)] lg:rounded-r-[calc(2rem+1px)]">
              <div className="px-element-y pb-element-y pt-element-y">
                {safeItems[3].tagline && (
                  <p className="text-body-small/body-small font-medium text-primary max-lg:text-center">{safeItems[3].tagline}</p>
                )}
                <h3 id="bento-item-3-title" className="mt-element-y text-heading-4/heading-4 font-medium tracking-tight text-foreground max-lg:text-center">
                  {safeItems[3].title}
                </h3>
                <p className="mt-element-y max-w-content text-body-small/body-small text-muted-foreground max-lg:text-center" aria-labelledby="bento-item-3-title">
                  {safeItems[3].description}
                </p>
              </div>
              <div className="relative min-h-[30rem] w-full grow">
                <div className="absolute bottom-0 left-10 right-0 top-10 overflow-hidden rounded-tl-xl bg-muted-foreground shadow-2xl">
                                      <Picture
                      src={safeItems[3].img}
                      alt={safeItems[3].alt || "API code example"}
                      widths={[375, 480, 640, 768, 1024, 1200]}
                      sizes="(max-width: 768px) 320px, (max-width: 1024px) 400px, 500px"
                      quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                      forceFillHeight={true}
                      className="size-full object-cover"
                      aria-describedby="bento-item-3-title"
                    />
                </div>
              </div>
            </div>
            <div className="pointer-events-none absolute inset-px rounded-lg shadow ring-1 ring-border/10 max-lg:rounded-b-[2rem] lg:rounded-r-[2rem]" aria-hidden="true"></div>
          </div>
        </div>
      </div>
    </section>
  )
}