import React from 'react';
import { BentoSectionProps } from './types';
import { Picture } from '../components/Picture';

export default function TwoRow4({ id, tagline, title, items }: BentoSectionProps) {
  // 确保items数组至少有4个元素
  const safeItems = items && items.length >= 4 ? items : Array(4).fill({
    title: 'Feature',
    description: 'Description text here',
    img: 'https://images.unsplash.com/photo-1624571409412-1f253e1ecc89?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&q=80',
    alt: 'Feature image'
  });

  return (
    <section className="bg-background dark:bg-gray-900 py-section-y" aria-labelledby={id ? `bento-heading-${id}` : 'bento-heading'}>
      <div className="mx-auto max-w-container px-container-x">
        {tagline && <p className="text-body-base/body-base font-semibold text-primary dark:text-indigo-400">{tagline}</p>}
        {title && (
          <h2 id={id ? `bento-heading-${id}` : 'bento-heading'} className="mt-element-y max-w-content text-balance text-heading-2/heading-2 font-semibold tracking-tight text-foreground dark:text-white">
            {title}
          </h2>
        )}
        <div className="mt-content-y grid grid-cols-1 gap-3 lg:grid-cols-12 lg:grid-rows-2">
          {/* 第一个区块 - 左上大图 (7列) */}
          <div className="flex p-px lg:col-span-7">
            <div className="overflow-hidden rounded-lg bg-card dark:bg-gray-800 ring-1 ring-border/10 dark:ring-white/15 max-lg:rounded-t-[2rem] lg:rounded-tl-[2rem]">
              <Picture
                src={safeItems[0].img}
                alt={safeItems[0].alt || "Releases dashboard"}
                widths={[375, 480, 640, 768, 1024, 1200, 1440]}
                sizes="(max-width: 768px) 100vw, (max-width: 1024px) 70vw, 800px"
                quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                aspectRatio="21/9"
                className="h-64 md:h-72 lg:h-80 xl:h-96 object-cover object-left"
                aria-describedby="bento-tworow4-item-0-title"
              />
              <div className="p-element-y" aria-labelledby="bento-tworow4-item-0-title">
                {safeItems[0].tagline && <p className="text-body-small/body-small font-semibold text-muted-foreground dark:text-gray-400">{safeItems[0].tagline}</p>}
                <h3 id="bento-tworow4-item-0-title" className="mt-element-y text-heading-4/heading-4 font-medium tracking-tight text-foreground dark:text-white">{safeItems[0].title}</h3>
                <p className="mt-element-y w-full text-body-small/body-small text-muted-foreground dark:text-gray-400" aria-labelledby="bento-tworow4-item-0-title">
                  {safeItems[0].description}
                </p>
              </div>
            </div>
          </div>

          {/* 第二个区块 - 右上图 (5列) */}
          <div className="flex p-px lg:col-span-5">
            <div className="overflow-hidden rounded-lg bg-card dark:bg-gray-800 ring-1 ring-border/10 dark:ring-white/15 lg:rounded-tr-[2rem]">
              <Picture
                src={safeItems[1].img}
                alt={safeItems[1].alt || "Integrations interface"}
                widths={[375, 480, 640, 768, 1024]}
                sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 500px"
                quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                aspectRatio="21/9"
                className="h-64 md:h-72 lg:h-80 xl:h-96 object-cover"
                aria-describedby="bento-tworow4-item-1-title"
              />
              <div className="p-element-y" aria-labelledby="bento-tworow4-item-1-title">
                {safeItems[1].tagline && <p className="text-body-small/body-small font-semibold text-muted-foreground dark:text-gray-400">{safeItems[1].tagline}</p>}
                <h3 id="bento-tworow4-item-1-title" className="mt-element-y text-heading-4/heading-4 font-medium tracking-tight text-foreground dark:text-white">{safeItems[1].title}</h3>
                <p className="mt-element-y w-full text-body-small/body-small text-muted-foreground dark:text-gray-400" aria-labelledby="bento-tworow4-item-1-title">
                  {safeItems[1].description}
                </p>
              </div>
            </div>
          </div>

          {/* 第三个区块 - 左下图 (5列) */}
          <div className="flex p-px lg:col-span-5">
            <div className="overflow-hidden rounded-lg bg-card dark:bg-gray-800 ring-1 ring-border/10 dark:ring-white/15 lg:rounded-bl-[2rem]">
              <Picture
                src={safeItems[2].img}
                alt={safeItems[2].alt || "Security features"}
                widths={[375, 480, 640, 768, 1024]}
                sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 500px"
                quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                aspectRatio="21/9"
                className="h-64 md:h-72 lg:h-80 xl:h-96 object-cover"
                aria-describedby="bento-tworow4-item-2-title"
              />
              <div className="p-element-y" aria-labelledby="bento-tworow4-item-2-title">
                {safeItems[2].tagline && <p className="text-body-small/body-small font-semibold text-muted-foreground dark:text-gray-400">{safeItems[2].tagline}</p>}
                <h3 id="bento-tworow4-item-2-title" className="mt-element-y text-heading-4/heading-4 font-medium tracking-tight text-foreground dark:text-white">{safeItems[2].title}</h3>
                <p className="mt-element-y w-full text-body-small/body-small text-muted-foreground dark:text-gray-400" aria-labelledby="bento-tworow4-item-2-title">
                  {safeItems[2].description}
                </p>
              </div>
            </div>
          </div>

          {/* 第四个区块 - 右下大图 (7列) */}
          <div className="flex p-px lg:col-span-7">
            <div className="overflow-hidden rounded-lg bg-card dark:bg-gray-800 ring-1 ring-border/10 dark:ring-white/15 max-lg:rounded-b-[2rem] lg:rounded-br-[2rem]">
              <Picture
                src={safeItems[3].img}
                alt={safeItems[3].alt || "Performance metrics"}
                widths={[375, 480, 640, 768, 1024, 1200, 1440]}
                sizes="(max-width: 768px) 100vw, (max-width: 1024px) 70vw, 800px"
                quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                aspectRatio="21/9"
                className="h-64 md:h-72 lg:h-80 xl:h-96 object-cover object-left"
                aria-describedby="bento-tworow4-item-3-title"
              />
              <div className="p-element-y" aria-labelledby="bento-tworow4-item-3-title">
                {safeItems[3].tagline && <p className="text-body-small/body-small font-semibold text-muted-foreground dark:text-gray-400">{safeItems[3].tagline}</p>}
                <h3 id="bento-tworow4-item-3-title" className="mt-element-y text-heading-4/heading-4 font-medium tracking-tight text-foreground dark:text-white">{safeItems[3].title}</h3>
                <p className="mt-element-y w-full text-body-small/body-small text-muted-foreground dark:text-gray-400" aria-labelledby="bento-tworow4-item-3-title">
                  {safeItems[3].description}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
