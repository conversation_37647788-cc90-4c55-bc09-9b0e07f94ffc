import React from 'react';
import { BentoSectionProps } from './types';
import { Picture } from '../components/Picture';

export default function Background({ id, tagline, title, items }: BentoSectionProps) {
  // 确保items数组至少有4个元素
  const safeItems = items && items.length >= 4 ? items : Array(4).fill({
    title: 'Feature',
    description: 'Description text here',
    img: 'https://images.unsplash.com/photo-1624571409412-1f253e1ecc89?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&q=80',
    alt: 'Feature image'
  });

  // 瀑布流式不规则网格的高度设置
  const heights = ['h-[500px]', 'h-[400px]', 'h-[600px]', 'h-[350px]'];
  
  // 为每个位置分配特定的图片尺寸
  const imgSizes = [
    'w=800&h=800', // 第一个位置
    'w=400&h=600', // 第二个位置
    'w=800&h=1000', // 第三个位置
    'w=400&h=500'  // 第四个位置
  ];

  // 背景渐变设置
  const gradients = [
    'from-blue-600 to-indigo-900',
    'from-purple-600 to-pink-700',
    'from-amber-500 to-red-700',
    'from-emerald-600 to-teal-800'
  ];

  return (
    <section className="bg-background dark:bg-gray-900 py-section-y" aria-labelledby={id ? `bento-heading-${id}` : 'bento-heading'}>
      <div className="mx-auto max-w-container px-container-x">
        <div className="mx-auto max-w-content text-center">
          {tagline && <p className="text-body-base/body-base font-semibold text-primary dark:text-indigo-400">{tagline}</p>}
          {title && (
            <h2 id={id ? `bento-heading-${id}` : 'bento-heading'} className="mt-element-y max-w-content mx-auto text-balance text-center text-heading-2/heading-2 font-semibold tracking-tight text-foreground dark:text-white">
              {title}
            </h2>
          )}
        </div>
        
        <div className="mx-auto mt-content-y max-w-container">
          {/* 使用CSS Grid创建大圆角矩形布局 */}
          <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-4 lg:grid-rows-2 overflow-hidden rounded-3xl shadow-xl bg-card dark:bg-gray-900">
            {/* 第一个区块 - 占据2列2行 */}
            <div 
              className="group relative overflow-hidden lg:col-span-2 lg:row-span-2"
              style={{ borderRadius: '1rem 0.5rem 0.5rem 1rem' }}
            >
              <div className={`absolute inset-0 bg-gradient-to-br ${gradients[0]} opacity-90`} aria-hidden="true"></div>
              <div className="absolute inset-0 opacity-30" aria-hidden="true">
                <Picture
                  src={safeItems[0].img}
                  alt={safeItems[0].alt || "Feature image"}
                  widths={[640, 768, 1024, 1200]}
                  sizes="(max-width: 1024px) 70vw, 700px"
                  quality={{ avif: 70, webp: 75, jpeg: 80, png: 85 }}
                  forceFillHeight={true}
                  className="h-full w-full object-cover"
                  aria-describedby="bento-background-item-0-title"
                />
              </div>
              <div className="relative h-full flex flex-col justify-end p-element-y" aria-labelledby="bento-background-item-0-title">
                {safeItems[0].tagline && (
                  <p className="text-body-small/body-small font-medium text-blue-200">{safeItems[0].tagline}</p>
                )}
                <h3 id="bento-background-item-0-title" className="mt-element-y text-heading-3/heading-3 font-semibold tracking-tight text-white">
                  {safeItems[0].title}
                </h3>
                <p className="mt-element-y text-body-base/body-base text-blue-100 max-w-content" aria-labelledby="bento-background-item-0-title">
                  {safeItems[0].description}
                </p>
                <div className="mt-element-y h-1 w-16 bg-white rounded transition-all duration-300 group-hover:w-32" aria-hidden="true"></div>
              </div>
            </div>

            {/* 第二个区块 */}
            <div 
              className="group relative overflow-hidden"
              style={{ borderRadius: '0.5rem 1rem 0.5rem 0.5rem' }}
            >
              <div className={`absolute inset-0 bg-gradient-to-br ${gradients[1]} opacity-90`} aria-hidden="true"></div>
              <div className="absolute inset-0 opacity-30" aria-hidden="true">
                <Picture
                  src={safeItems[1].img}
                  alt={safeItems[1].alt || "Feature image"}
                  widths={[375, 480, 640, 768]}
                  sizes="(max-width: 1024px) 35vw, 350px"
                  quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                  forceFillHeight={true}
                  className="h-full w-full object-cover"
                  aria-describedby="bento-background-item-1-title"
                />
              </div>
              <div className="relative h-full flex flex-col justify-end p-element-y" aria-labelledby="bento-background-item-1-title">
                {safeItems[1].tagline && (
                  <p className="text-body-small/body-small font-medium text-pink-200">{safeItems[1].tagline}</p>
                )}
                <h3 id="bento-background-item-1-title" className="mt-element-y text-heading-4/heading-4 font-semibold tracking-tight text-white">
                  {safeItems[1].title}
                </h3>
                <p className="mt-element-y text-body-small/body-small text-pink-100" aria-labelledby="bento-background-item-1-title">
                  {safeItems[1].description}
                </p>
                <div className="mt-element-y h-1 w-12 bg-white rounded transition-all duration-300 group-hover:w-24" aria-hidden="true"></div>
              </div>
            </div>

            {/* 第三个区块 */}
            <div 
              className="group relative overflow-hidden"
              style={{ borderRadius: '0.5rem 0.5rem 0.5rem 0.5rem' }}
            >
              <div className={`absolute inset-0 bg-gradient-to-br ${gradients[2]} opacity-90`} aria-hidden="true"></div>
              <div className="absolute inset-0 opacity-30" aria-hidden="true">
                <Picture
                  src={safeItems[2].img}
                  alt={safeItems[2].alt || "Feature image"}
                  widths={[375, 480, 640, 768]}
                  sizes="(max-width: 1024px) 35vw, 350px"
                  quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                  forceFillHeight={true}
                  className="h-full w-full object-cover"
                  aria-describedby="bento-background-item-2-title"
                />
              </div>
              <div className="relative h-full flex flex-col justify-end p-element-y" aria-labelledby="bento-background-item-2-title">
                {safeItems[2].tagline && (
                  <p className="text-body-small/body-small font-medium text-amber-200">{safeItems[2].tagline}</p>
                )}
                <h3 id="bento-background-item-2-title" className="mt-element-y text-heading-4/heading-4 font-semibold tracking-tight text-white">
                  {safeItems[2].title}
                </h3>
                <p className="mt-element-y text-body-small/body-small text-amber-100" aria-labelledby="bento-background-item-2-title">
                  {safeItems[2].description}
                </p>
                <div className="mt-element-y h-1 w-12 bg-white rounded transition-all duration-300 group-hover:w-24" aria-hidden="true"></div>
              </div>
            </div>

            {/* 第四个区块 - 占据2列 */}
            <div 
              className="group relative overflow-hidden lg:col-span-2"
              style={{ borderRadius: '0.5rem 0.5rem 1rem 0.5rem' }}
            >
              <div className={`absolute inset-0 bg-gradient-to-br ${gradients[3]} opacity-90`} aria-hidden="true"></div>
              <div className="absolute inset-0 opacity-30" aria-hidden="true">
                <Picture
                  src={safeItems[3].img}
                  alt={safeItems[3].alt || "Feature image"}
                  widths={[640, 768, 1024, 1200]}
                  sizes="(max-width: 1024px) 70vw, 700px"
                  quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                  forceFillHeight={true}
                  className="h-full w-full object-cover"
                  aria-describedby="bento-background-item-3-title"
                />
              </div>
              <div className="relative h-full flex flex-col justify-end p-element-y" aria-labelledby="bento-background-item-3-title">
                {safeItems[3].tagline && (
                  <p className="text-body-small/body-small font-medium text-emerald-200">{safeItems[3].tagline}</p>
                )}
                <h3 id="bento-background-item-3-title" className="mt-element-y text-heading-4/heading-4 font-semibold tracking-tight text-white">
                  {safeItems[3].title}
                </h3>
                <p className="mt-element-y text-body-small/body-small text-emerald-100" aria-labelledby="bento-background-item-3-title">
                  {safeItems[3].description}
                </p>
                <div className="mt-element-y h-1 w-12 bg-white rounded transition-all duration-300 group-hover:w-24" aria-hidden="true"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
