import React from 'react';
import { BentoSectionProps } from './types';
import { Picture } from '../components/Picture';

export default function Colorful({ id, tagline, title, items }: BentoSectionProps) {
  // 确保items数组至少有4个元素
  const safeItems = items && items.length >= 4 ? items : Array(4).fill({
    title: 'Feature',
    description: 'Description text here',
    img: 'https://images.unsplash.com/photo-1624571409412-1f253e1ecc89?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&q=80',
    alt: 'Feature image'
  });

  // 为每个位置分配特定的图片尺寸
  const imgSizes = [
    'w=800&h=600', // 左上大区块
    'w=500&h=400', // 右上区块
    'w=500&h=400', // 左下区块
    'w=800&h=600'  // 右下大区块
  ];

  // 为每个区块分配不同的颜色主题
  const colorThemes = [
    {
      bg: 'bg-gradient-to-br from-violet-500 to-purple-700',
      text: 'text-violet-100',
      highlight: 'text-white',
      border: 'border-violet-400/30',
      glow: 'shadow-purple-500/30',
      hoverGlow: ''
    },
    {
      bg: 'bg-gradient-to-br from-pink-500 to-rose-700',
      text: 'text-pink-100',
      highlight: 'text-white',
      border: 'border-pink-400/30',
      glow: 'shadow-pink-500/30',
      hoverGlow: ''
    },
    {
      bg: 'bg-gradient-to-br from-amber-400 to-orange-700',
      text: 'text-amber-100',
      highlight: 'text-white',
      border: 'border-amber-400/30',
      glow: 'shadow-amber-500/30',
      hoverGlow: ''
    },
    {
      bg: 'bg-gradient-to-br from-emerald-500 to-teal-700',
      text: 'text-emerald-100',
      highlight: 'text-white',
      border: 'border-emerald-400/30',
      glow: 'shadow-emerald-500/30',
      hoverGlow: ''
    }
  ];

  // 为每个区块生成随机装饰元素
  const generateDecorations = (index: number) => {
    // 每个区块生成1-3个装饰元素
    const count = Math.floor(Math.random() * 3) + 1;
    const decorations = [];
    
    for (let i = 0; i < count; i++) {
      // 随机位置 (10%-90%)
      const top = Math.floor(Math.random() * 80) + 10;
      const left = Math.floor(Math.random() * 80) + 10;
      
      // 随机大小 (6px-16px)
      const size = Math.floor(Math.random() * 10) + 6;
      
      // 随机透明度 (0.05-0.2)
      const opacity = (Math.floor(Math.random() * 15) + 5) / 100;
      
      // 随机形状（圆形或方形，有时带模糊效果）
      const isCircle = Math.random() > 0.3;
      const hasBlur = Math.random() > 0.7;
      const shape = isCircle ? 'rounded-full' : 'rounded-md';
      const blur = hasBlur ? 'backdrop-blur-sm' : '';
      
      // 根据区块主题选择颜色
      let bgColor;
      switch (index) {
        case 0:
          bgColor = `bg-purple-${Math.floor(Math.random() * 3) + 3}00/${opacity}`;
          break;
        case 1:
          bgColor = `bg-pink-${Math.floor(Math.random() * 3) + 3}00/${opacity}`;
          break;
        case 2:
          bgColor = `bg-amber-${Math.floor(Math.random() * 3) + 3}00/${opacity}`;
          break;
        case 3:
          bgColor = `bg-emerald-${Math.floor(Math.random() * 3) + 3}00/${opacity}`;
          break;
        default:
          bgColor = 'bg-white/10';
      }
      
      decorations.push(
        <div 
          key={`decoration-${index}-${i}`}
          className={`absolute ${shape} ${blur} ${bgColor}`}
          style={{
            top: `${top}%`,
            left: `${left}%`,
            width: `${size}px`,
            height: `${size}px`,
            transform: `rotate(${Math.floor(Math.random() * 360)}deg)`,
            zIndex: 5
          }}
        />
      );
    }
    
    return decorations;
  };

  return (
    <section className="bg-background dark:bg-gray-900 py-section-y overflow-hidden" aria-labelledby={id ? `bento-heading-${id}` : 'bento-heading'}>
      <div className="mx-auto max-w-container px-container-x relative">
        {/* 背景装饰 */}
        <div className="absolute inset-0 overflow-hidden opacity-20" aria-hidden="true">
          <div className="absolute -top-24 -left-24 w-96 h-96 rounded-full bg-purple-900/30 blur-3xl"></div>
          <div className="absolute -bottom-24 -right-24 w-96 h-96 rounded-full bg-pink-900/30 blur-3xl"></div>
        </div>
        
        <div className="relative">
          {tagline && <p className="text-center text-body-base/body-base font-semibold text-primary dark:text-purple-400">{tagline}</p>}
          {title && (
            <h2 id={id ? `bento-heading-${id}` : 'bento-heading'} className="mx-auto mt-element-y max-w-content text-balance text-center text-heading-2/heading-2 font-semibold tracking-tight text-foreground dark:text-white">
              {title}
            </h2>
          )}
        </div>
        
        {/* 矩形布局 - 整体为大圆角矩形 */}
        <div className="mt-content-y relative">
          {/* 整体容器 - 大圆角矩形 */}
          <div className="rounded-3xl overflow-hidden shadow-2xl border border-border/20 dark:border-white/10 bg-muted/50 dark:bg-gray-800/20 backdrop-blur-sm">
            <div className="p-element-y">
              <div className="grid grid-cols-1 md:grid-cols-12 gap-element-y relative z-10">
                {/* 左上大区块 - 占据左侧 7 列 */}
                <div className="md:col-span-7">
                  <div 
                    className={`group relative overflow-hidden rounded-xl ${colorThemes[0].bg} p-element-y transition-transform duration-200 shadow-lg ${colorThemes[0].glow} border border-white/10 h-full`}
                    aria-labelledby="bento-colorful-item-0-title"
                  >
                    <div className="relative z-10 flex flex-col h-full">
                      <div>
                        {safeItems[0].tagline && (
                          <p className={`text-body-small/body-small font-medium ${colorThemes[0].highlight}`}>{safeItems[0].tagline}</p>
                        )}
                        <h3 id="bento-colorful-item-0-title" className={`mt-element-y text-heading-3/heading-3 font-bold tracking-tight ${colorThemes[0].highlight}`}>
                          {safeItems[0].title}
                        </h3>
                        <p className={`mt-element-y text-body-base/body-base ${colorThemes[0].text}`} aria-labelledby="bento-colorful-item-0-title">
                          {safeItems[0].description}
                        </p>
                      </div>
                      
                      {/* 六边形裁剪的图片 */}
                      <div className="mt-content-y relative overflow-hidden">
                        <div 
                          className="h-40 md:h-48 lg:h-56 w-full" 
                          style={{
                            clipPath: 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)',
                          }}
                        >
                          <Picture
                            src={safeItems[0].img}
                            alt={safeItems[0].alt || "Feature image"}
                            widths={[640, 768, 1024, 1200]}
                            sizes="(max-width: 1024px) 60vw, 600px"
                            quality={{ avif: 70, webp: 75, jpeg: 80, png: 85 }}
                            forceFillHeight={true}
                            className="h-full w-full object-cover transition-transform duration-200"
                            aria-describedby="bento-colorful-item-0-title"
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-purple-900/60 to-transparent" aria-hidden="true"></div>
                        </div>
                      </div>
                    </div>
                    
                    {/* 随机装饰元素 */}
                    {generateDecorations(0)}
                  </div>
                </div>
                
                {/* 右上区块 - 占据右侧 5 列 */}
                <div className="md:col-span-5">
                  <div 
                    className={`group relative overflow-hidden rounded-xl ${colorThemes[1].bg} p-element-y transition-transform duration-200 shadow-lg ${colorThemes[1].glow} border border-white/10 h-full`}
                    aria-labelledby="bento-colorful-item-1-title"
                  >
                    <div className="relative z-10 flex flex-col h-full">
                      <div>
                        {safeItems[1].tagline && (
                          <p className={`text-body-small/body-small font-medium ${colorThemes[1].highlight}`}>{safeItems[1].tagline}</p>
                        )}
                        <h3 id="bento-colorful-item-1-title" className={`mt-element-y text-heading-4/heading-4 font-semibold tracking-tight ${colorThemes[1].highlight}`}>
                          {safeItems[1].title}
                        </h3>
                        <p className={`mt-element-y text-body-small/body-small ${colorThemes[1].text}`} aria-labelledby="bento-colorful-item-1-title">
                          {safeItems[1].description}
                        </p>
                      </div>
                      
                      {/* 六边形裁剪的图片 */}
                      <div className="mt-element-y relative overflow-hidden">
                        <div 
                          className="h-32 md:h-36 lg:h-40 w-full" 
                          style={{
                            clipPath: 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)',
                          }}
                        >
                          <Picture
                            src={safeItems[1].img}
                            alt={safeItems[1].alt || "Feature image"}
                            widths={[375, 480, 640, 768]}
                            sizes="(max-width: 1024px) 40vw, 400px"
                            quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                            forceFillHeight={true}
                            className="h-full w-full object-cover transition-transform duration-200"
                            aria-describedby="bento-colorful-item-1-title"
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-pink-900/60 to-transparent" aria-hidden="true"></div>
                        </div>
                      </div>
                    </div>
                    
                    {/* 随机装饰元素 */}
                    {generateDecorations(1)}
                  </div>
                </div>
                
                {/* 左下区块 - 占据左侧 5 列 */}
                <div className="md:col-span-5">
                  <div 
                    className={`group relative overflow-hidden rounded-xl ${colorThemes[2].bg} p-element-y transition-transform duration-200 shadow-lg ${colorThemes[2].glow} border border-white/10 h-full`}
                    aria-labelledby="bento-colorful-item-2-title"
                  >
                    <div className="relative z-10 flex flex-col h-full">
                      <div>
                        {safeItems[2].tagline && (
                          <p className={`text-body-small/body-small font-medium ${colorThemes[2].highlight}`}>{safeItems[2].tagline}</p>
                        )}
                        <h3 id="bento-colorful-item-2-title" className={`mt-element-y text-heading-4/heading-4 font-semibold tracking-tight ${colorThemes[2].highlight}`}>
                          {safeItems[2].title}
                        </h3>
                        <p className={`mt-element-y text-body-small/body-small ${colorThemes[2].text}`} aria-labelledby="bento-colorful-item-2-title">
                          {safeItems[2].description}
                        </p>
                      </div>
                      
                      {/* 六边形裁剪的图片 */}
                      <div className="mt-element-y relative overflow-hidden">
                        <div 
                          className="h-32 md:h-36 lg:h-40 w-full" 
                          style={{
                            clipPath: 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)',
                          }}
                        >
                          <Picture
                            src={safeItems[2].img}
                            alt={safeItems[2].alt || "Feature image"}
                            widths={[375, 480, 640, 768]}
                            sizes="(max-width: 1024px) 40vw, 400px"
                            quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                            forceFillHeight={true}
                            className="h-full w-full object-cover transition-transform duration-200"
                            aria-describedby="bento-colorful-item-2-title"
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-orange-900/60 to-transparent" aria-hidden="true"></div>
                        </div>
                      </div>
                    </div>
                    
                    {/* 随机装饰元素 */}
                    {generateDecorations(2)}
                  </div>
                </div>
                
                {/* 右下大区块 - 占据右侧 7 列 */}
                <div className="md:col-span-7">
                  <div 
                    className={`group relative overflow-hidden rounded-xl ${colorThemes[3].bg} p-element-y transition-transform duration-200 shadow-lg ${colorThemes[3].glow} border border-white/10 h-full`}
                    aria-labelledby="bento-colorful-item-3-title"
                  >
                    <div className="relative z-10 flex flex-col h-full">
                      <div>
                        {safeItems[3].tagline && (
                          <p className={`text-body-small/body-small font-medium ${colorThemes[3].highlight}`}>{safeItems[3].tagline}</p>
                        )}
                        <h3 id="bento-colorful-item-3-title" className={`mt-element-y text-heading-3/heading-3 font-bold tracking-tight ${colorThemes[3].highlight}`}>
                          {safeItems[3].title}
                        </h3>
                        <p className={`mt-element-y text-body-base/body-base ${colorThemes[3].text}`} aria-labelledby="bento-colorful-item-3-title">
                          {safeItems[3].description}
                        </p>
                      </div>
                      
                      {/* 六边形裁剪的图片 */}
                      <div className="mt-content-y relative overflow-hidden">
                        <div 
                          className="h-40 md:h-48 lg:h-56 w-full" 
                          style={{
                            clipPath: 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)',
                          }}
                        >
                          <Picture
                            src={safeItems[3].img}
                            alt={safeItems[3].alt || "Feature image"}
                            widths={[640, 768, 1024, 1200]}
                            sizes="(max-width: 1024px) 60vw, 600px"
                            quality={{ avif: 70, webp: 75, jpeg: 80, png: 85 }}
                            forceFillHeight={true}
                            className="h-full w-full object-cover transition-transform duration-200"
                            aria-describedby="bento-colorful-item-3-title"
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-teal-900/60 to-transparent" aria-hidden="true"></div>
                        </div>
                      </div>
                    </div>
                    
                    {/* 随机装饰元素 */}
                    {generateDecorations(3)}
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* 连接线 - 在区块之间创建细微的连接线 */}
          <div className="absolute inset-0 pointer-events-none z-0">
            <svg className="absolute inset-0 w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
              <path d="M30,30 L70,30" stroke="rgba(255,255,255,0.05)" strokeWidth="0.2" />
              <path d="M30,30 L30,70" stroke="rgba(255,255,255,0.05)" strokeWidth="0.2" />
              <path d="M70,30 L70,70" stroke="rgba(255,255,255,0.05)" strokeWidth="0.2" />
              <path d="M30,70 L70,70" stroke="rgba(255,255,255,0.05)" strokeWidth="0.2" />
            </svg>
          </div>
          
          {/* 装饰性前景元素 */}
          <div className="absolute inset-0 pointer-events-none">
            <div className="absolute top-1/4 left-1/4 w-2 h-2 rounded-full bg-purple-500/20"></div>
            <div className="absolute top-1/4 right-1/4 w-2 h-2 rounded-full bg-pink-500/20"></div>
            <div className="absolute bottom-1/4 left-1/4 w-2 h-2 rounded-full bg-amber-500/20"></div>
            <div className="absolute bottom-1/4 right-1/4 w-2 h-2 rounded-full bg-emerald-500/20"></div>
          </div>
        </div>
      </div>
    </section>
  );
}
