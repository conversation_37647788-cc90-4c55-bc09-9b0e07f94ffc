import React from 'react';
import { BentoSectionProps } from './types';
import { Picture } from '../components/Picture';

export default function Enterprise({ id, tagline, title, items }: BentoSectionProps) {
  // 确保items数组至少有4个元素
  const safeItems = items && items.length >= 4 ? items : Array(4).fill({
    title: 'Feature',
    description: 'Description text here',
    img: 'https://images.unsplash.com/photo-1624571409412-1f253e1ecc89?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&q=80',
    alt: 'Feature image'
  });

  // 背景色设置 - 适配亮色和暗色模式
  const bgColors = [
    'bg-blue-50 dark:bg-blue-900',
    'bg-indigo-50 dark:bg-indigo-900',
    'bg-purple-50 dark:bg-purple-900',
    'bg-sky-50 dark:bg-sky-900'
  ];

  return (
    <section className="bg-background py-section-y" aria-labelledby={id ? `bento-heading-${id}` : 'bento-heading'}>
      <div className="mx-auto max-w-container px-container-x">
        <div className="mx-auto max-w-content text-center">
          {tagline && <p className="text-body-base/body-base font-semibold text-primary">{tagline}</p>}
          {title && (
            <h2 id={id ? `bento-heading-${id}` : 'bento-heading'} className="mt-element-y max-w-content mx-auto text-balance text-center text-heading-2/heading-2 font-semibold tracking-tight text-foreground">
              {title}
            </h2>
          )}
        </div>
        
        <div className="mx-auto mt-content-y grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-6 lg:grid-rows-2 gap-element-y">
          {/* 第一个区块 - 左上大区块 (占3列1行) */}
          <div 
            className={`group relative overflow-hidden lg:col-span-3 lg:row-span-1 ${bgColors[0]} rounded-2xl shadow-md`}
            aria-labelledby={`bento-item-0-title`}
          >
            <div className="h-full flex flex-col md:flex-row">
              <div className="md:w-1/2 p-element-y">
                {safeItems[0].tagline && (
                  <p className="text-body-small/body-small font-medium text-primary">{safeItems[0].tagline}</p>
                )}
                <h3 id={`bento-item-0-title`} className="mt-element-y text-heading-4/heading-4 font-semibold text-gray-900 dark:text-gray-100">{safeItems[0].title}</h3>
                <p className="mt-element-y text-body-small/body-small text-muted-foreground">{safeItems[0].description}</p>
              </div>
              <div className="md:w-1/2 relative overflow-hidden md:rounded-r-2xl">
                <Picture
                  src={safeItems[0].img}
                  alt={safeItems[0].alt || "Feature image"}
                  widths={[375, 480, 640, 768, 1024]}
                  sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 400px"
                  quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                  forceFillHeight={true}
                  className="w-full h-full object-cover group-hover:scale-[1.02] transition-transform duration-300"
                  aria-describedby={`bento-item-0-title`}
                />
              </div>
            </div>
          </div>

          {/* 第二个区块 - 右上小区块 (占3列1行) */}
          <div 
            className={`group relative overflow-hidden lg:col-span-3 lg:row-span-1 ${bgColors[1]} rounded-2xl shadow-md`}
            aria-labelledby={`bento-item-1-title`}
          >
            <div className="h-full flex flex-col md:flex-row">
              <div className="md:w-1/2 p-element-y">
                {safeItems[1].tagline && (
                  <p className="text-body-small/body-small font-medium text-primary">{safeItems[1].tagline}</p>
                )}
                <h3 id={`bento-item-1-title`} className="mt-element-y text-heading-4/heading-4 font-semibold text-gray-900 dark:text-gray-100">{safeItems[1].title}</h3>
                <p className="mt-element-y text-body-small/body-small text-muted-foreground">{safeItems[1].description}</p>
              </div>
              <div className="md:w-1/2 relative overflow-hidden md:rounded-r-2xl">
                <Picture
                  src={safeItems[1].img}
                  alt={safeItems[1].alt || "Feature image"}
                  widths={[375, 480, 640, 768, 1024]}
                  sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 400px"
                  quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                  forceFillHeight={true}
                  className="w-full h-full object-cover group-hover:scale-[1.02] transition-transform duration-300"
                  aria-describedby={`bento-item-1-title`}
                />
              </div>
            </div>
          </div>

          {/* 第三个区块 - 左下小区块 (占2列1行) */}
          <div 
            className={`group relative overflow-hidden lg:col-span-2 lg:row-span-1 ${bgColors[2]} rounded-2xl shadow-md`}
            aria-labelledby={`bento-item-2-title`}
          >
            <div className="h-full flex flex-col">
              <div className="p-element-y flex-shrink-0">
                {safeItems[2].tagline && (
                  <p className="text-body-small/body-small font-medium text-primary">{safeItems[2].tagline}</p>
                )}
                <h3 id={`bento-item-2-title`} className="mt-element-y text-heading-4/heading-4 font-semibold text-gray-900 dark:text-gray-100">{safeItems[2].title}</h3>
                <p className="mt-element-y text-body-small/body-small text-muted-foreground">{safeItems[2].description}</p>
              </div>
              <div className="flex-grow relative overflow-hidden rounded-b-2xl">
                <Picture
                  src={safeItems[2].img}
                  alt={safeItems[2].alt || "Feature image"}
                  widths={[375, 480, 640, 768]}
                  sizes="(max-width: 768px) 100vw, (max-width: 1024px) 30vw, 300px"
                  quality={{ avif: 60, webp: 65, jpeg: 70, png: 75 }}
                  forceFillHeight={true}
                  className="w-full h-full object-cover group-hover:scale-[1.02] transition-transform duration-300"
                  aria-describedby={`bento-item-2-title`}
                />
              </div>
            </div>
          </div>

          {/* 第四个区块 - 右下大区块 (占4列1行) */}
          <div 
            className={`group relative overflow-hidden lg:col-span-4 lg:row-span-1 ${bgColors[3]} rounded-2xl shadow-md`}
            aria-labelledby={`bento-item-3-title`}
          >
            <div className="h-full flex flex-col md:flex-row">
              <div className="md:w-1/2 p-element-y">
                {safeItems[3].tagline && (
                  <p className="text-body-small/body-small font-medium text-primary">{safeItems[3].tagline}</p>
                )}
                <h3 id={`bento-item-3-title`} className="mt-element-y text-heading-4/heading-4 font-semibold text-gray-900 dark:text-gray-100">{safeItems[3].title}</h3>
                <p className="mt-element-y text-body-small/body-small text-muted-foreground">{safeItems[3].description}</p>
              </div>
              <div className="md:w-1/2 relative overflow-hidden md:rounded-r-2xl">
                <Picture
                  src={safeItems[3].img}
                  alt={safeItems[3].alt || "Feature image"}
                  widths={[480, 640, 768, 1024, 1200]}
                  sizes="(max-width: 768px) 100vw, (max-width: 1024px) 60vw, 600px"
                  quality={{ avif: 70, webp: 75, jpeg: 80, png: 85 }}
                  forceFillHeight={true}
                  className="w-full h-full object-cover group-hover:scale-[1.02] transition-transform duration-300"
                  aria-describedby={`bento-item-3-title`}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
