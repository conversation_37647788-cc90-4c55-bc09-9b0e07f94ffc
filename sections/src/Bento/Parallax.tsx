'use client'

import React, { useEffect, useRef, useState } from 'react';
import { motion } from 'motion/react';
import { BentoSectionProps } from './types';
import { Picture } from '../components/Picture';

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ')
}

export default function Parallax({ id, tagline, title, items }: BentoSectionProps) {
  // 创建引用以跟踪容器元素
  const containerRef = useRef<HTMLDivElement>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);
  
  // 确保items数组至少有4个元素
  const safeItems = items && items.length >= 4 ? items : Array(4).fill({
    title: 'Feature',
    description: 'Description text here',
    img: 'https://images.unsplash.com/photo-1624571409412-1f253e1ecc89?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&q=80',
    alt: 'Feature image'
  });



  // 设置视差效果
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleMouseMove = (e: MouseEvent) => {
      const { left, top, width, height } = container.getBoundingClientRect();
      const x = (e.clientX - left) / width - 0.5;
      const y = (e.clientY - top) / height - 0.5;
      
      setMousePosition({ x, y });
    };

    const handleMouseEnter = () => setIsHovering(true);
    const handleMouseLeave = () => setIsHovering(false);

    // 添加鼠标事件监听器
    container.addEventListener('mousemove', handleMouseMove);
    container.addEventListener('mouseenter', handleMouseEnter);
    container.addEventListener('mouseleave', handleMouseLeave);

    // 清理函数
    return () => {
      container.removeEventListener('mousemove', handleMouseMove);
      container.removeEventListener('mouseenter', handleMouseEnter);
      container.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, []);

  // 计算视差变换
  const getParallaxTransform = (depth: number) => {
    if (!isHovering) return { x: 0, y: 0 };
    const moveX = mousePosition.x * depth;
    const moveY = mousePosition.y * depth;
    return { x: moveX, y: moveY };
  };

  return (
    <section className="bg-gradient-to-b from-background to-primary/10 dark:from-gray-900 dark:to-indigo-950 py-section-y overflow-hidden" aria-labelledby={id ? `bento-heading-${id}` : 'bento-heading'}>
      <motion.div 
        className="mx-auto max-w-container-sm px-container-x lg:max-w-container lg:px-container-x-lg relative" 
        ref={containerRef}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
      >
        <motion.div 
          className="mx-auto max-w-content text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {tagline && (
            <motion.p 
              className="text-base/7 font-semibold text-primary dark:text-violet-400"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2, duration: 0.4 }}
            >
              {tagline}
            </motion.p>
          )}
          {title && (
            <motion.h2 
              id={id ? `bento-heading-${id}` : 'bento-heading'}
              className="mx-auto mt-element-y text-balance text-center text-heading-2/heading-2 font-semibold tracking-tight text-foreground dark:text-white sm:text-heading-1/heading-1"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.5 }}
            >
              {title}
            </motion.h2>
          )}
        </motion.div>
        
        <div className="mt-section-y grid grid-cols-1 gap-element-y sm:grid-cols-2 lg:grid-cols-3 relative">
          {/* 背景层 - 最底层 */}
          <motion.div 
            className="absolute inset-0 pointer-events-none"
            animate={getParallaxTransform(2)}
            transition={{ type: "spring", stiffness: 75, damping: 30, mass: 0.5 }}
            aria-hidden="true"
          >
            <div className="absolute top-10 left-10 w-32 h-32 rounded-full bg-violet-900/30 blur-xl"></div>
            <div className="absolute bottom-10 right-10 w-32 h-32 rounded-full bg-indigo-900/30 blur-xl"></div>
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 rounded-full bg-blue-900/20 blur-3xl"></div>
          </motion.div>

          {/* 第一个区块 - 中景层 */}
          <motion.div 
            className="lg:col-span-2 bg-card/80 dark:bg-gray-800/50 backdrop-blur-sm rounded-3xl overflow-hidden shadow-xl border border-border/50 dark:border-gray-700/50 relative z-10"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.5 }}
            whileHover={{ 
              y: -5,
              boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2)',
              transition: { duration: 0.2 }
            }}
          >
            <div className="flex flex-col sm:flex-row min-h-[400px] lg:min-h-[500px]">
              <div className="sm:w-1/2 p-8 flex flex-col justify-center">
                <motion.div
                  animate={getParallaxTransform(8)}
                  transition={{ type: "spring", stiffness: 75, damping: 30, mass: 0.5 }}
                >
                  {safeItems[0].tagline && (
                    <motion.p 
                      className="text-sm font-medium text-primary dark:text-violet-400"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.3, duration: 0.4 }}
                    >
                      {safeItems[0].tagline}
                    </motion.p>
                  )}
                  <motion.h3 
                    id="bento-parallax-item-0-title"
                    className="mt-element-y text-heading-3/heading-3 font-semibold tracking-tight text-foreground dark:text-white"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.4, duration: 0.4 }}
                  >
                    {safeItems[0].title}
                  </motion.h3>
                  <motion.p 
                    className="mt-element-y text-body text-muted-foreground dark:text-gray-300"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.5, duration: 0.4 }}
                    aria-labelledby="bento-parallax-item-0-title"
                  >
                    {safeItems[0].description}
                  </motion.p>
                </motion.div>
              </div>
              <div className="sm:w-1/2 relative overflow-hidden">
                <motion.div
                  animate={getParallaxTransform(5)}
                  transition={{ type: "spring", stiffness: 75, damping: 30, mass: 0.5 }}
                  className="absolute inset-0"
                >
                  <Picture
                    src={safeItems[0].img}
                    alt={safeItems[0].alt || "Feature image"}
                    widths={[375, 480, 640, 768, 1024]}
                    sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 600px"
                    quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                    forceFillHeight={true}
                    className="h-full w-full object-cover"
                    aria-describedby="bento-parallax-item-0-title"
                  />
                </motion.div>
                <div className="absolute inset-0 bg-gradient-to-r from-card/80 dark:from-gray-800/80 via-transparent to-transparent" aria-hidden="true"></div>
              </div>
            </div>
          </motion.div>

          {/* 第二个区块 - 前景层 */}
          <motion.div 
            className="bg-card/80 dark:bg-gray-800/50 backdrop-blur-sm rounded-3xl overflow-hidden shadow-xl border border-border/50 dark:border-gray-700/50 relative z-20 flex flex-col min-h-[400px] lg:min-h-[480px]"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3, duration: 0.5 }}
            whileHover={{ 
              y: -5,
              boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2)',
              transition: { duration: 0.2 }
            }}
          >
            <div className="p-6 flex-shrink-0">
              <motion.div
                animate={getParallaxTransform(12)}
                transition={{ type: "spring", stiffness: 75, damping: 30, mass: 0.5 }}
              >
                {safeItems[1].tagline && (
                  <motion.p 
                    className="text-sm font-medium text-primary dark:text-violet-400"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.4, duration: 0.4 }}
                  >
                    {safeItems[1].tagline}
                  </motion.p>
                )}
                <motion.h3 
                  id="bento-parallax-item-1-title"
                  className="mt-element-y text-heading-4/heading-4 font-semibold tracking-tight text-foreground dark:text-white"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.5, duration: 0.4 }}
                >
                  {safeItems[1].title}
                </motion.h3>
                <motion.p 
                  className="mt-element-y text-body-sm text-muted-foreground dark:text-gray-300"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.6, duration: 0.4 }}
                  aria-labelledby="bento-parallax-item-1-title"
                >
                  {safeItems[1].description}
                </motion.p>
              </motion.div>
            </div>
            <div className="relative flex-grow overflow-hidden">
              <motion.div
                animate={getParallaxTransform(10)}
                transition={{ type: "spring", stiffness: 75, damping: 30, mass: 0.5 }}
                className="absolute inset-0"
              >
                <Picture
                  src={safeItems[1].img}
                  alt={safeItems[1].alt || "Feature image"}
                  widths={[375, 480, 640, 768, 1024]}
                  sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 400px"
                  quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                  forceFillHeight={true}
                  className="h-full w-full object-cover"
                  aria-describedby="bento-parallax-item-1-title"
                />
              </motion.div>
              <div className="absolute inset-0 bg-gradient-to-t from-card/80 dark:from-gray-800/80 via-transparent to-transparent"></div>
            </div>
          </motion.div>

          {/* 第三个区块 - 背景层 */}
          <motion.div 
            className="bg-card/80 dark:bg-gray-800/50 backdrop-blur-sm rounded-3xl overflow-hidden shadow-xl border border-border/50 dark:border-gray-700/50 relative z-0 flex flex-col min-h-[400px] lg:min-h-[480px]"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.5 }}
            whileHover={{ 
              y: -5,
              boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2)',
              transition: { duration: 0.2 }
            }}
          >
            <div className="p-6 flex-shrink-0">
              <motion.div
                animate={getParallaxTransform(4)}
                transition={{ type: "spring", stiffness: 75, damping: 30, mass: 0.5 }}
              >
                {safeItems[2].tagline && (
                  <motion.p 
                    className="text-sm font-medium text-primary dark:text-violet-400"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.5, duration: 0.4 }}
                  >
                    {safeItems[2].tagline}
                  </motion.p>
                )}
                <motion.h3 
                  id="bento-parallax-item-2-title"
                  className="mt-element-y text-heading-4/heading-4 font-semibold tracking-tight text-foreground dark:text-white"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.6, duration: 0.4 }}
                >
                  {safeItems[2].title}
                </motion.h3>
                <motion.p 
                  className="mt-element-y text-body-sm text-muted-foreground dark:text-gray-300"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.7, duration: 0.4 }}
                  aria-labelledby="bento-parallax-item-2-title"
                >
                  {safeItems[2].description}
                </motion.p>
              </motion.div>
            </div>
            <div className="relative flex-grow overflow-hidden">
              <motion.div
                animate={getParallaxTransform(3)}
                transition={{ type: "spring", stiffness: 75, damping: 30, mass: 0.5 }}
                className="absolute inset-0"
              >
                <Picture
                  src={safeItems[2].img}
                  alt={safeItems[2].alt || "Feature image"}
                  widths={[375, 480, 640, 768, 1024]}
                  sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 400px"
                  quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                  forceFillHeight={true}
                  className="h-full w-full object-cover"
                  aria-describedby="bento-parallax-item-2-title"
                />
              </motion.div>
              <div className="absolute inset-0 bg-gradient-to-t from-card/80 dark:from-gray-800/80 via-transparent to-transparent"></div>
            </div>
          </motion.div>

          {/* 第四个区块 - 前景层 */}
          <motion.div 
            className="lg:col-span-2 bg-card/80 dark:bg-gray-800/50 backdrop-blur-sm rounded-3xl overflow-hidden shadow-xl border border-border/50 dark:border-gray-700/50 relative z-30"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5, duration: 0.5 }}
            whileHover={{ 
              y: -5,
              boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2)',
              transition: { duration: 0.2 }
            }}
          >
            <div className="flex flex-col sm:flex-row min-h-[400px] lg:min-h-[500px]">
              <div className="sm:w-1/2 relative overflow-hidden">
                <motion.div
                  animate={getParallaxTransform(15)}
                  transition={{ type: "spring", stiffness: 75, damping: 30, mass: 0.5 }}
                  className="absolute inset-0"
                >
                  <Picture
                    src={safeItems[3].img}
                    alt={safeItems[3].alt || "Feature image"}
                    widths={[375, 480, 640, 768, 1024]}
                    sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 600px"
                    quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                    forceFillHeight={true}
                    className="h-full w-full object-cover"
                    aria-describedby="bento-parallax-item-3-title"
                  />
                </motion.div>
                <div className="absolute inset-0 bg-gradient-to-l from-card/80 dark:from-gray-800/80 via-transparent to-transparent" aria-hidden="true"></div>
              </div>
              <div className="sm:w-1/2 p-8 flex flex-col justify-center">
                <motion.div
                  animate={getParallaxTransform(18)}
                  transition={{ type: "spring", stiffness: 75, damping: 30, mass: 0.5 }}
                >
                  {safeItems[3].tagline && (
                    <motion.p 
                      className="text-sm font-medium text-primary dark:text-violet-400"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.6, duration: 0.4 }}
                    >
                      {safeItems[3].tagline}
                    </motion.p>
                  )}
                  <motion.h3 
                    id="bento-parallax-item-3-title"
                    className="mt-element-y text-heading-3/heading-3 font-semibold tracking-tight text-foreground dark:text-white"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.7, duration: 0.4 }}
                  >
                    {safeItems[3].title}
                  </motion.h3>
                  <motion.p 
                    className="mt-element-y text-body text-muted-foreground dark:text-gray-300"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.8, duration: 0.4 }}
                    aria-labelledby="bento-parallax-item-3-title"
                  >
                    {safeItems[3].description}
                  </motion.p>
                </motion.div>
              </div>
            </div>
          </motion.div>

          {/* 装饰性前景元素 */}
          <motion.div 
            className="absolute inset-0 pointer-events-none"
            animate={getParallaxTransform(20)}
            transition={{ type: "spring", stiffness: 75, damping: 30, mass: 0.5 }}
            aria-hidden="true"
          >
            <motion.div 
              className="absolute top-20 right-20 w-16 h-16 rounded-full border border-violet-500/30"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.6, duration: 0.5, type: "spring" }}
            ></motion.div>
            <motion.div 
              className="absolute bottom-20 left-20 w-16 h-16 rounded-full border border-indigo-500/30"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.7, duration: 0.5, type: "spring" }}
            ></motion.div>
            <motion.div 
              className="absolute top-1/3 left-1/4 w-8 h-8 rounded-full bg-blue-500/10"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.8, duration: 0.5, type: "spring" }}
            ></motion.div>
            <motion.div 
              className="absolute bottom-1/3 right-1/4 w-8 h-8 rounded-full bg-violet-500/10"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.9, duration: 0.5, type: "spring" }}
            ></motion.div>
          </motion.div>
        </div>
      </motion.div>
    </section>
  );
}
