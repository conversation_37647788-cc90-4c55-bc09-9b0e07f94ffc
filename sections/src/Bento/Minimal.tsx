import React from 'react';
import { BentoSectionProps } from './types';
import { Picture } from '../components/Picture';

export default function Minimal({ id, tagline, title, items }: BentoSectionProps) {
  // 确保items数组至少有4个元素
  const safeItems = items && items.length >= 4 ? items : Array(4).fill({
    title: 'Feature',
    description: 'Description text here',
    img: 'https://images.unsplash.com/photo-1624571409412-1f253e1ecc89?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&q=80',
    alt: 'Feature image'
  });

  return (
    <section className="bg-background py-section-y" aria-labelledby={id ? `bento-heading-${id}` : 'bento-heading'}>
      <div className="mx-auto max-w-container px-container-x">
        {tagline && <p className="text-center text-body-base/body-base font-semibold text-primary">{tagline}</p>}
        {title && (
          <h2 id={id ? `bento-heading-${id}` : 'bento-heading'} className="mx-auto mt-element-y max-w-content text-balance text-center text-heading-2/heading-2 font-semibold tracking-tight text-foreground">
            {title}
          </h2>
        )}
        
        {/* 移动端布局 - 单列 */}
        <div className="mt-content-y grid gap-element-y md:hidden">
          {safeItems.map((item, index) => (
            <div key={index} className="relative group" aria-labelledby={`bento-minimal-mobile-item-${index}-title`}>
              <div className="absolute inset-px rounded-lg bg-card" aria-hidden="true"></div>
              <div className="relative flex h-full flex-col overflow-hidden rounded-[calc(theme(borderRadius.lg)+1px)]">
                <div className="px-element-y pb-element-y pt-element-y">
                  {item.tagline && (
                    <p className="text-body-small/body-small font-medium text-primary">{item.tagline}</p>
                  )}
                  <h3 id={`bento-minimal-mobile-item-${index}-title`} className="mt-element-y text-heading-4/heading-4 font-medium tracking-tight text-foreground">
                    {item.title}
                  </h3>
                  <p className="mt-element-y text-body-small/body-small text-muted-foreground" aria-labelledby={`bento-minimal-mobile-item-${index}-title`}>
                    {item.description}
                  </p>
                </div>
                <div className="relative h-48 w-full">
                  <Picture
                    src={item.img}
                    alt={item.alt || "Feature image"}
                    widths={[375, 480, 640, 768]}
                    sizes="(max-width: 768px) 100vw, 600px"
                    quality={{ avif: 60, webp: 65, jpeg: 70, png: 75 }}
                    forceFillHeight={true}
                    className="absolute inset-0 size-full object-cover"
                    aria-describedby={`bento-minimal-mobile-item-${index}-title`}
                  />
                </div>
              </div>
              <div className="pointer-events-none absolute inset-px rounded-lg shadow ring-1 ring-border/10" aria-hidden="true"></div>
            </div>
          ))}
        </div>

        {/* 桌面端布局 - Bento网格 */}
        <div className="mt-content-y hidden gap-element-y md:grid lg:grid-cols-3 lg:grid-rows-2">
          {/* 第一个区块 - 左侧大区块 (占据2行) */}
          <div className="relative lg:row-span-2 group" aria-labelledby="bento-minimal-item-0-title">
            <div className="absolute inset-px rounded-lg bg-card lg:rounded-l-[2rem]" aria-hidden="true"></div>
            <div className="relative flex h-full flex-col overflow-hidden rounded-[calc(theme(borderRadius.lg)+1px)] lg:rounded-l-[calc(2rem+1px)]">
              <div className="px-element-y pb-element-y pt-element-y">
                {safeItems[0].tagline && (
                  <p className="text-body-small/body-small font-medium text-primary">{safeItems[0].tagline}</p>
                )}
                <h3 id="bento-minimal-item-0-title" className="mt-element-y text-heading-3/heading-3 font-medium tracking-tight text-foreground">
                  {safeItems[0].title}
                </h3>
                <p className="mt-element-y text-body-base/body-base text-muted-foreground" aria-labelledby="bento-minimal-item-0-title">
                  {safeItems[0].description}
                </p>
              </div>
              <div className="relative w-full grow min-h-[200px]">
                <Picture
                  src={safeItems[0].img}
                  alt={safeItems[0].alt || "Feature image"}
                  widths={[480, 640, 768, 1024, 1200]}
                  sizes="(max-width: 1024px) 50vw, 600px"
                  quality={{ avif: 70, webp: 75, jpeg: 80, png: 85 }}
                  forceFillHeight={true}
                  className="absolute inset-0 size-full object-cover"
                  aria-describedby="bento-minimal-item-0-title"
                />
              </div>
            </div>
            <div className="pointer-events-none absolute inset-px rounded-lg shadow ring-1 ring-border/10 lg:rounded-l-[2rem]" aria-hidden="true"></div>
          </div>

          {/* 第二个区块 - 右上区块 */}
          <div className="relative group" aria-labelledby="bento-minimal-item-1-title">
            <div className="absolute inset-px rounded-lg bg-card max-lg:rounded-t-[2rem]" aria-hidden="true"></div>
            <div className="relative flex h-full flex-col overflow-hidden rounded-[calc(theme(borderRadius.lg)+1px)] max-lg:rounded-t-[calc(2rem+1px)]">
              <div className="px-element-y pb-element-y pt-element-y">
                {safeItems[1].tagline && (
                  <p className="text-body-small/body-small font-medium text-primary">{safeItems[1].tagline}</p>
                )}
                <h3 id="bento-minimal-item-1-title" className="mt-element-y text-heading-4/heading-4 font-medium tracking-tight text-foreground">
                  {safeItems[1].title}
                </h3>
                <p className="mt-element-y text-body-small/body-small text-muted-foreground" aria-labelledby="bento-minimal-item-1-title">
                  {safeItems[1].description}
                </p>
              </div>
              <div className="relative h-32 md:h-36 lg:h-40 w-full">
                <Picture
                  src={safeItems[1].img}
                  alt={safeItems[1].alt || "Feature image"}
                  widths={[375, 480, 640, 768, 1024]}
                  sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 400px"
                  quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                  forceFillHeight={true}
                  className="absolute inset-0 size-full object-cover"
                  aria-describedby="bento-minimal-item-1-title"
                />
              </div>
            </div>
            <div className="pointer-events-none absolute inset-px rounded-lg shadow ring-1 ring-border/10 max-lg:rounded-t-[2rem]" aria-hidden="true"></div>
          </div>

          {/* 第三个区块 - 右中区块 */}
          <div className="relative group" aria-labelledby="bento-minimal-item-2-title">
            <div className="absolute inset-px rounded-lg bg-card" aria-hidden="true"></div>
            <div className="relative flex h-full flex-col overflow-hidden rounded-[calc(theme(borderRadius.lg)+1px)]">
              <div className="px-element-y pb-element-y pt-element-y">
                {safeItems[2].tagline && (
                  <p className="text-body-small/body-small font-medium text-primary">{safeItems[2].tagline}</p>
                )}
                <h3 id="bento-minimal-item-2-title" className="mt-element-y text-heading-4/heading-4 font-medium tracking-tight text-foreground">
                  {safeItems[2].title}
                </h3>
                <p className="mt-element-y text-body-small/body-small text-muted-foreground" aria-labelledby="bento-minimal-item-2-title">
                  {safeItems[2].description}
                </p>
              </div>
              <div className="relative h-32 md:h-36 lg:h-40 w-full">
                <Picture
                  src={safeItems[2].img}
                  alt={safeItems[2].alt || "Feature image"}
                  widths={[375, 480, 640, 768, 1024]}
                  sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 400px"
                  quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                  forceFillHeight={true}
                  className="absolute inset-0 size-full object-cover"
                  aria-describedby="bento-minimal-item-2-title"
                />
              </div>
            </div>
            <div className="pointer-events-none absolute inset-px rounded-lg shadow ring-1 ring-border/10" aria-hidden="true"></div>
          </div>

          {/* 第四个区块 - 右下大区块 (占据2列) */}
          <div className="relative lg:col-span-2 group" aria-labelledby="bento-minimal-item-3-title">
            <div className="absolute inset-px rounded-lg bg-card max-lg:rounded-b-[2rem] lg:rounded-r-[2rem]" aria-hidden="true"></div>
            <div className="relative h-full overflow-hidden rounded-[calc(theme(borderRadius.lg)+1px)] max-lg:rounded-b-[calc(2rem+1px)] lg:rounded-r-[calc(2rem+1px)]">
              <div className="px-element-y pt-element-y pb-36 md:pb-40 lg:pb-44">
                {safeItems[3].tagline && (
                  <p className="text-body-small/body-small font-medium text-primary">{safeItems[3].tagline}</p>
                )}
                <h3 id="bento-minimal-item-3-title" className="mt-element-y text-heading-4/heading-4 font-medium tracking-tight text-foreground">
                  {safeItems[3].title}
                </h3>
                <p className="mt-element-y text-body-small/body-small text-muted-foreground" aria-labelledby="bento-minimal-item-3-title">
                  {safeItems[3].description}
                </p>
              </div>
              <div className="absolute bottom-0 left-0 right-0 h-32 md:h-36 lg:h-40">
                <Picture
                  src={safeItems[3].img}
                  alt={safeItems[3].alt || "Feature image"}
                  widths={[480, 640, 768, 1024, 1200, 1440]}
                  sizes="(max-width: 768px) 100vw, (max-width: 1024px) 70vw, 800px"
                  quality={{ avif: 70, webp: 75, jpeg: 80, png: 85 }}
                  forceFillHeight={true}
                  className="absolute inset-0 size-full object-cover"
                  aria-describedby="bento-minimal-item-3-title"
                />
              </div>
            </div>
            <div className="pointer-events-none absolute inset-px rounded-lg shadow ring-1 ring-border/10 max-lg:rounded-b-[2rem] lg:rounded-r-[2rem]" aria-hidden="true"></div>
          </div>
        </div>
      </div>
    </section>
  );
}
