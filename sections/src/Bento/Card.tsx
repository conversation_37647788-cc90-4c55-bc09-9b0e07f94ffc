import React from 'react';
import { BentoSectionProps } from './types';
import { Picture } from '../components/Picture';

export default function Card({ id, tagline, title, items }: BentoSectionProps) {
  // 确保items数组至少有4个元素
  const safeItems = items && items.length >= 4 ? items : Array(4).fill({
    title: 'Feature',
    description: 'Description text here',
    img: 'https://images.unsplash.com/photo-1624571409412-1f253e1ecc89?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&q=80',
    alt: 'Feature image'
  });



  // 为每个卡片分配主题色
  const cardThemes = [
    {
      bg: 'bg-indigo-50 dark:bg-indigo-950',
      border: 'border-indigo-100 dark:border-indigo-900',
      tag: 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300',
      accent: 'bg-indigo-600 dark:bg-indigo-500'
    },
    {
      bg: 'bg-emerald-50 dark:bg-emerald-950',
      border: 'border-emerald-100 dark:border-emerald-900',
      tag: 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-300',
      accent: 'bg-emerald-500'
    },
    {
      bg: 'bg-amber-50 dark:bg-amber-950',
      border: 'border-amber-100 dark:border-amber-900',
      tag: 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300',
      accent: 'bg-amber-500'
    },
    {
      bg: 'bg-pink-50 dark:bg-pink-950',
      border: 'border-pink-100 dark:border-pink-900',
      tag: 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300',
      accent: 'bg-pink-500'
    }
  ];

  // 生成随机装饰元素
  const generateDecorations = (index: number) => {
    // 根据索引选择颜色主题
    const colorThemes = [
      ['#4F46E5', '#818CF8', '#C7D2FE'], // 靛蓝色主题
      ['#10B981', '#34D399', '#A7F3D0'], // 绿色主题
      ['#F59E0B', '#FBBF24', '#FDE68A'], // 琥珀色主题
      ['#EC4899', '#F472B6', '#FBCFE8'], // 粉色主题
    ];
    
    const colors = colorThemes[index % colorThemes.length];
    
    // 随机生成1-2个装饰元素
    const count = Math.floor(Math.random() * 2) + 1;
    const decorations = [];
    
    for (let i = 0; i < count; i++) {
      // 随机属性
      const size = Math.floor(Math.random() * 10) + 6; // 6px-16px
      const top = Math.floor(Math.random() * 80) + 10; // 10%-90%
      const left = Math.floor(Math.random() * 80) + 10; // 10%-90%
      const opacity = (Math.random() * 0.15 + 0.05).toFixed(2); // 0.05-0.2
      const rotate = Math.floor(Math.random() * 360); // 0-360度
      const isCircle = Math.random() > 0.3; // 70%概率为圆形
      const hasBlur = Math.random() > 0.7; // 30%概率有模糊效果
      const color = colors[Math.floor(Math.random() * colors.length)];
      
      decorations.push(
        <div
          key={`decoration-${index}-${i}`}
          className="absolute pointer-events-none"
          style={{
            top: `${top}%`,
            left: `${left}%`,
            width: `${size}px`,
            height: `${size}px`,
            backgroundColor: color,
            borderRadius: isCircle ? '50%' : '20%',
            opacity: opacity,
            transform: `rotate(${rotate}deg)`,
            filter: hasBlur ? 'blur(2px)' : 'none',
            zIndex: 5
          }}
        />
      );
    }
    
    return decorations;
  };

  return (
    <section className="bg-background py-section-y" aria-labelledby={id ? `bento-heading-${id}` : 'bento-heading'}>
      <div className="mx-auto max-w-container px-container-x">
        <div className="mx-auto max-w-content text-center">
          {tagline && <p className="text-body-base/body-base font-semibold text-primary">{tagline}</p>}
          {title && (
            <h2 id={id ? `bento-heading-${id}` : 'bento-heading'} className="mt-element-y text-heading-2/heading-2 font-bold tracking-tight text-foreground">
              {title}
            </h2>
          )}
        </div>
        
        <div className="mx-auto mt-content-y max-w-container">
          {/* 使用CSS Grid创建拼接效果，去掉外层容器 */}
          <div className="grid grid-cols-1 gap-element-y sm:grid-cols-2 lg:grid-cols-4 lg:grid-rows-2 overflow-hidden rounded-3xl shadow-xl bg-muted">
            {/* 大卡片 - 占据2列2行 */}
            <div 
              className={`group relative col-span-1 row-span-2 overflow-hidden ${cardThemes[0].bg} sm:col-span-2 lg:col-span-2 lg:row-span-2`}
              style={{ borderRadius: '1rem 0.5rem 0.5rem 1rem' }}
              aria-labelledby="bento-card-item-0-title"
            >
              {generateDecorations(0)}
              <div className="absolute inset-0 bg-gradient-to-b from-white/30 dark:from-gray-800/30 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" aria-hidden="true"></div>
              <div className="relative h-full">
                {/* 文字内容区域 - 绝对定位在顶部 */}
                <div className="absolute top-0 left-0 right-0 z-10 p-element-y bg-gradient-to-b from-white/95 dark:from-gray-900/95 to-transparent">
                  {safeItems[0].tagline && (
                    <p className={`inline-block rounded-full px-element-x py-0.5 text-body-small/body-small font-medium ${cardThemes[0].tag}`}>{safeItems[0].tagline}</p>
                  )}
                  <h3 id="bento-card-item-0-title" className="mt-element-y text-heading-3/heading-3 font-bold tracking-tight text-gray-900 dark:text-gray-100">
                    {safeItems[0].title}
                  </h3>
                  <p className="mt-element-y text-body-base/body-base text-muted-foreground" aria-labelledby="bento-card-item-0-title">
                    {safeItems[0].description}
                  </p>
                </div>
                
                {/* 图片区域 - 从顶部偏移开始，到底部对齐 */}
                <div className="absolute top-32 md:top-36 lg:top-40 bottom-0 left-0 right-0 overflow-hidden">
                  <Picture
                    src={safeItems[0].img}
                    alt={safeItems[0].alt || "Feature image"}
                    widths={[375, 480, 640, 768, 1024, 1200]}
                    sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 600px"
                    quality={{ avif: 70, webp: 75, jpeg: 80, png: 85 }}
                    forceFillHeight={true}
                    className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                    aria-describedby="bento-card-item-0-title"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" aria-hidden="true"></div>
                </div>
              </div>
              <div className={`absolute bottom-0 left-0 right-0 h-1 ${cardThemes[0].accent} origin-left scale-x-0 transform transition-transform duration-300 group-hover:scale-x-100`} aria-hidden="true"></div>
            </div>
            
            {/* 中卡片 - 占据1列1行 */}
            <div 
              className={`group relative col-span-1 row-span-1 overflow-hidden ${cardThemes[1].bg}`}
              style={{ borderRadius: '0.5rem 1rem 0.5rem 0.5rem' }}
              aria-labelledby="bento-card-item-1-title"
            >
              {generateDecorations(1)}
              <div className="absolute inset-0 bg-gradient-to-b from-white/30 dark:from-gray-800/30 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" aria-hidden="true"></div>
              <div className="relative flex flex-col h-full">
                <div className="p-element-y flex-grow">
                  {safeItems[1].tagline && (
                    <p className={`inline-block rounded-full px-element-x py-0.5 text-body-small/body-small font-medium ${cardThemes[1].tag}`}>{safeItems[1].tagline}</p>
                  )}
                  <h3 id="bento-card-item-1-title" className="mt-element-y text-heading-4/heading-4 font-semibold tracking-tight text-gray-900 dark:text-gray-100">
                    {safeItems[1].title}
                  </h3>
                  <p className="mt-element-y text-body-small/body-small text-muted-foreground line-clamp-2" aria-labelledby="bento-card-item-1-title">
                    {safeItems[1].description}
                  </p>
                </div>
                <div className="relative h-24 md:h-28 lg:h-32 overflow-hidden">
                  <Picture
                    src={safeItems[1].img}
                    alt={safeItems[1].alt || "Feature image"}
                    widths={[375, 480, 640, 768, 1024]}
                    sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 400px"
                    quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                    forceFillHeight={true}
                    className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                    aria-describedby="bento-card-item-1-title"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" aria-hidden="true"></div>
                </div>
              </div>
              <div className={`absolute bottom-0 left-0 right-0 h-1 ${cardThemes[1].accent} origin-left scale-x-0 transform transition-transform duration-300 group-hover:scale-x-100`} aria-hidden="true"></div>
            </div>
            
            {/* 小卡片 - 占据1列1行 */}
            <div 
              className={`group relative col-span-1 row-span-1 overflow-hidden ${cardThemes[2].bg}`}
              style={{ borderRadius: '0.5rem 0.5rem 0.5rem 0.5rem' }}
              aria-labelledby="bento-card-item-2-title"
            >
              {generateDecorations(2)}
              <div className="absolute inset-0 bg-gradient-to-b from-white/30 dark:from-gray-800/30 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" aria-hidden="true"></div>
              <div className="relative flex flex-col h-full">
                <div className="p-element-y flex-grow">
                  {safeItems[2].tagline && (
                    <p className={`inline-block rounded-full px-element-x py-0.5 text-body-small/body-small font-medium ${cardThemes[2].tag}`}>{safeItems[2].tagline}</p>
                  )}
                  <h3 id="bento-card-item-2-title" className="mt-element-y text-heading-4/heading-4 font-semibold tracking-tight text-gray-900 dark:text-gray-100">
                    {safeItems[2].title}
                  </h3>
                  <p className="mt-element-y text-body-small/body-small text-muted-foreground line-clamp-2" aria-labelledby="bento-card-item-2-title">
                    {safeItems[2].description}
                  </p>
                </div>
                <div className="relative h-24 md:h-28 lg:h-32 overflow-hidden">
                  <Picture
                    src={safeItems[2].img}
                    alt={safeItems[2].alt || "Feature image"}
                    widths={[375, 480, 640, 768, 1024]}
                    sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 400px"
                    quality={{ avif: 65, webp: 70, jpeg: 75, png: 80 }}
                    forceFillHeight={true}
                    className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                    aria-describedby="bento-card-item-2-title"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" aria-hidden="true"></div>
                </div>
              </div>
              <div className={`absolute bottom-0 left-0 right-0 h-1 ${cardThemes[2].accent} origin-left scale-x-0 transform transition-transform duration-300 group-hover:scale-x-100`} aria-hidden="true"></div>
            </div>
            
            {/* 中卡片 - 占据2列1行 */}
            <div 
              className={`group relative col-span-1 row-span-1 overflow-hidden ${cardThemes[3].bg} sm:col-span-2 lg:col-span-2`}
              style={{ borderRadius: '0.5rem 0.5rem 1rem 0.5rem' }}
              aria-labelledby="bento-card-item-3-title"
            >
              {generateDecorations(3)}
              <div className="absolute inset-0 bg-gradient-to-b from-white/30 dark:from-gray-800/30 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" aria-hidden="true"></div>
              <div className="relative flex flex-col h-full">
                <div className="p-element-y flex-grow">
                  {safeItems[3].tagline && (
                    <p className={`inline-block rounded-full px-element-x py-0.5 text-body-small/body-small font-medium ${cardThemes[3].tag}`}>{safeItems[3].tagline}</p>
                  )}
                  <h3 id="bento-card-item-3-title" className="mt-element-y text-heading-4/heading-4 font-semibold tracking-tight text-gray-900 dark:text-gray-100">
                    {safeItems[3].title}
                  </h3>
                  <p className="mt-element-y text-body-small/body-small text-muted-foreground sm:text-body-base/body-base" aria-labelledby="bento-card-item-3-title">
                    {safeItems[3].description}
                  </p>
                </div>
                <div className="relative h-24 md:h-28 lg:h-32 overflow-hidden">
                  <Picture
                    src={safeItems[3].img}
                    alt={safeItems[3].alt || "Feature image"}
                    widths={[375, 480, 640, 768, 1024, 1200, 1440]}
                    sizes="(max-width: 768px) 100vw, (max-width: 1024px) 70vw, 800px"
                    quality={{ avif: 70, webp: 75, jpeg: 80, png: 85 }}
                    forceFillHeight={true}
                    className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                    aria-describedby="bento-card-item-3-title"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" aria-hidden="true"></div>
                </div>
              </div>
              <div className={`absolute bottom-0 left-0 right-0 h-1 ${cardThemes[3].accent} origin-left scale-x-0 transform transition-transform duration-300 group-hover:scale-x-100`} aria-hidden="true"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
