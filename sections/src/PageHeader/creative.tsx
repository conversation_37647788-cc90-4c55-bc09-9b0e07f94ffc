import { PageHeaderSectionProps } from './types';

export default function Creative({ 
  tagline = 'Get the help you need', 
  title, 
  description 
}: PageHeaderSectionProps) {
  return (
    <section className="relative overflow-hidden bg-background py-section-y" aria-labelledby="creative-page-header-title">
      <div className="mx-auto max-w-container px-container-x">
        <div className="mx-auto max-w-content text-center relative z-10">
          {tagline && <p className="text-body-base/body-base font-semibold text-primary">{tagline}</p>}
          <h1 id="creative-page-header-title" className="mt-element-y text-heading-2/heading-2 font-bold tracking-tight text-foreground">{title}</h1>
          <p className="mt-element-y text-body-base/body-base text-muted-foreground">
            {description}
          </p>
        </div>
        
        {/* Decorative SVG elements - Top Right */}
        <div className="hidden md:block absolute top-1/4 end-0 -translate-y-1/2 translate-x-1/3 text-primary/20 opacity-40" aria-hidden="true">
          <svg className="w-24 h-24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M14.5 9.50002L9 15.0001" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M10.5 9.50002L9 11.0001" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M14.5 13.5L13 15.0001" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M22 12C22 16.714 22 19.0711 20.5355 20.5355C19.0711 22 16.714 22 12 22C7.28595 22 4.92893 22 3.46447 20.5355C2 19.0711 2 16.714 2 12C2 7.28595 2 4.92893 3.46447 3.46447C4.92893 2 7.28595 2 12 2C16.714 2 19.0711 2 20.5355 3.46447C22 4.92893 22 7.28595 22 12Z" stroke="currentColor" strokeWidth="1.5"/>
          </svg>
        </div>
        
        {/* Decorative SVG elements - Bottom Left */}
        <div className="hidden md:block absolute bottom-1/4 start-0 translate-y-1/2 -translate-x-1/3 text-secondary/20 opacity-40" aria-hidden="true">
          <svg className="w-24 h-24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="1.5"/>
            <path d="M12 8V16" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
            <path d="M16 12H8" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
          </svg>
        </div>
        
        {/* Wavy lines - Top */}
        <div className="absolute top-0 inset-x-0 opacity-30" aria-hidden="true">
          <svg className="w-full h-16 text-primary/10" viewBox="0 0 1200 120" preserveAspectRatio="none">
            <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z" fill="currentColor"></path>
          </svg>
        </div>
        
        {/* Wavy lines - Bottom */}
        <div className="absolute bottom-0 inset-x-0 opacity-30" aria-hidden="true">
          <svg className="w-full h-16 text-secondary/10" viewBox="0 0 1200 120" preserveAspectRatio="none" style={{ transform: 'rotate(180deg)' }}>
            <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z" fill="currentColor"></path>
          </svg>
        </div>
      </div>
    </section>
  );
}
