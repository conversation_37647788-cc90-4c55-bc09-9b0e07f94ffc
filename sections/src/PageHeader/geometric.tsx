import { PageHeaderSectionProps } from './types';

export default function Geometric({ 
  tagline = 'Get the help you need', 
  title, 
  description 
}: PageHeaderSectionProps) {
  return (
    <section className="relative overflow-hidden bg-background py-section-y" aria-labelledby="geometric-page-header-title">
      <div className="mx-auto max-w-container px-container-x">
        <div className="mx-auto max-w-content text-center relative z-10">
          {tagline && <p className="text-body-base/body-base font-semibold text-primary">{tagline}</p>}
          <h1 id="geometric-page-header-title" className="mt-element-y text-heading-2/heading-2 font-bold tracking-tight text-foreground">{title}</h1>
          <p className="mt-element-y text-body-base/body-base text-muted-foreground">
            {description}
          </p>
        </div>
        
        {/* SVG Geometric Elements */}
        <div className="absolute inset-0 -z-10 overflow-hidden" aria-hidden="true">
          <svg className="absolute left-[max(50%,25rem)] top-0 h-[64rem] w-[128rem] -translate-x-1/2 stroke-border [mask-image:radial-gradient(64rem_64rem_at_top,white,transparent)]">
            <defs>
              <pattern id="e813992c-7d03-4cc4-a2bd-151760b470a0" width="200" height="200" x="50%" y="-1" patternUnits="userSpaceOnUse">
                <path d="M100 200V.5M.5 .5H200" fill="none" />
              </pattern>
            </defs>
            <rect width="100%" height="100%" strokeWidth="0" fill="url(#e813992c-7d03-4cc4-a2bd-151760b470a0)" />
          </svg>
        </div>
        
        {/* Colored Circles - Top Left */}
        <div className="hidden md:block absolute -top-16 -left-16 opacity-20" aria-hidden="true">
          <svg width="160" height="160" viewBox="0 0 160 160" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="80" cy="80" r="80" fill="currentColor" className="text-primary" />
          </svg>
        </div>
        
        {/* Colored Circles - Bottom Right */}
        <div className="hidden md:block absolute -bottom-24 right-0 opacity-20" aria-hidden="true">
          <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="60" cy="60" r="60" fill="currentColor" className="text-secondary" />
          </svg>
        </div>
      </div>
    </section>
  );
}
