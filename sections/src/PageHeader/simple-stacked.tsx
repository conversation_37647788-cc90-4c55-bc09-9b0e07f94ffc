import { PageHeaderSectionProps } from './types';

export default function SimpleStacked({ 
  tagline = 'Get the help you need', 
  title, 
  description 
}: PageHeaderSectionProps) {
  return (
    <section className="bg-background py-section-y" aria-labelledby="simple-stacked-page-header-title">
      <div className="mx-auto max-w-container px-container-x">
        <div className="mx-auto max-w-content lg:mx-0">
          {tagline && <p className="text-body-base/body-base font-semibold text-primary">{tagline}</p>}
          <h1 id="simple-stacked-page-header-title" className="mt-element-y text-heading-1/heading-1 font-semibold tracking-tight text-foreground">{title}</h1>
          <p className="mt-content-y text-pretty text-body-large/body-large font-medium text-muted-foreground">
            {description}
          </p>
        </div>
      </div>
    </section>
  )
}