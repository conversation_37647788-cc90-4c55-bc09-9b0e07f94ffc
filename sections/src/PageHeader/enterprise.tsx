import { PageHeaderSectionProps } from './types';

export default function Enterprise({ 
  tagline = 'Get the help you need', 
  title, 
  description 
}: PageHeaderSectionProps) {
  return (
    <section className="bg-background py-section-y" aria-labelledby="enterprise-page-header-title">
      <div className="mx-auto max-w-container px-container-x">
        <div className="mx-auto max-w-content lg:mx-0">
          {tagline && (
            <p className="text-body-base/body-base font-semibold text-primary">{tagline}</p>
          )}
          <h1 id="enterprise-page-header-title" className="mt-element-y text-heading-1/heading-1 font-bold tracking-tight text-foreground">{title}</h1>
          <p className="mt-content-y text-body-large/body-large text-muted-foreground">
            {description}
          </p>
        </div>
        <div className="mx-auto mt-content-y max-w-content lg:mx-0 lg:max-w-none">
          <div className="grid grid-cols-1 gap-x-element-x gap-y-element-y text-body-base/body-base font-semibold text-foreground sm:grid-cols-2 md:flex lg:gap-x-content-y">
            <div className="h-px w-full bg-border" />
          </div>
        </div>
      </div>
    </section>
  );
}
