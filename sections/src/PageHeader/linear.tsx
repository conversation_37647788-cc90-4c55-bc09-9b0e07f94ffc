import { PageHeaderSectionProps } from './types';

export default function Linear({ 
  tagline = 'Get the help you need', 
  title, 
  description 
}: PageHeaderSectionProps) {
  return (
    <section className="relative overflow-hidden bg-background py-section-y" aria-labelledby="linear-page-header-title">
      <div className="mx-auto max-w-container px-container-x">
        <div className="mx-auto max-w-content text-center relative z-10">
          {tagline && <p className="text-body-base/body-base font-semibold text-primary">{tagline}</p>}
          <h1 id="linear-page-header-title" className="mt-element-y text-heading-2/heading-2 font-bold tracking-tight text-foreground">{title}</h1>
          <p className="mt-element-y text-body-base/body-base text-muted-foreground">
            {description}
          </p>
        </div>
        
        {/* Decorative lines - Top Right */}
        <div className="hidden md:block absolute top-0 end-0 -translate-y-1/4 translate-x-1/4" aria-hidden="true">
          <svg className="w-64 h-64 text-primary/30 opacity-30" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
            <line x1="0" y1="0" x2="100" y2="100" stroke="currentColor" strokeWidth="1" />
            <line x1="10" y1="0" x2="100" y2="90" stroke="currentColor" strokeWidth="1" />
            <line x1="20" y1="0" x2="100" y2="80" stroke="currentColor" strokeWidth="1" />
            <line x1="30" y1="0" x2="100" y2="70" stroke="currentColor" strokeWidth="1" />
            <line x1="40" y1="0" x2="100" y2="60" stroke="currentColor" strokeWidth="1" />
            <line x1="50" y1="0" x2="100" y2="50" stroke="currentColor" strokeWidth="1" />
            <line x1="60" y1="0" x2="100" y2="40" stroke="currentColor" strokeWidth="1" />
            <line x1="70" y1="0" x2="100" y2="30" stroke="currentColor" strokeWidth="1" />
            <line x1="80" y1="0" x2="100" y2="20" stroke="currentColor" strokeWidth="1" />
            <line x1="90" y1="0" x2="100" y2="10" stroke="currentColor" strokeWidth="1" />
          </svg>
        </div>
        
        {/* Decorative lines - Bottom Left */}
        <div className="hidden md:block absolute bottom-0 start-0 translate-y-1/4 -translate-x-1/4" aria-hidden="true">
          <svg className="w-64 h-64 text-secondary/30 opacity-30" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
            <line x1="0" y1="100" x2="100" y2="0" stroke="currentColor" strokeWidth="1" />
            <line x1="0" y1="90" x2="90" y2="0" stroke="currentColor" strokeWidth="1" />
            <line x1="0" y1="80" x2="80" y2="0" stroke="currentColor" strokeWidth="1" />
            <line x1="0" y1="70" x2="70" y2="0" stroke="currentColor" strokeWidth="1" />
            <line x1="0" y1="60" x2="60" y2="0" stroke="currentColor" strokeWidth="1" />
            <line x1="0" y1="50" x2="50" y2="0" stroke="currentColor" strokeWidth="1" />
            <line x1="0" y1="40" x2="40" y2="0" stroke="currentColor" strokeWidth="1" />
            <line x1="0" y1="30" x2="30" y2="0" stroke="currentColor" strokeWidth="1" />
            <line x1="0" y1="20" x2="20" y2="0" stroke="currentColor" strokeWidth="1" />
            <line x1="0" y1="10" x2="10" y2="0" stroke="currentColor" strokeWidth="1" />
          </svg>
        </div>
        
        {/* Horizontal lines */}
        <div className="absolute inset-x-0 top-1/2 -translate-y-1/2 opacity-10" aria-hidden="true">
          <svg className="w-full h-24" viewBox="0 0 100 100" preserveAspectRatio="none" xmlns="http://www.w3.org/2000/svg">
            <line x1="0" y1="10" x2="100" y2="10" stroke="currentColor" strokeWidth="1" className="text-indigo-500 dark:text-indigo-300" />
            <line x1="0" y1="30" x2="100" y2="30" stroke="currentColor" strokeWidth="1" className="text-indigo-500 dark:text-indigo-300" />
            <line x1="0" y1="50" x2="100" y2="50" stroke="currentColor" strokeWidth="1" className="text-indigo-500 dark:text-indigo-300" />
            <line x1="0" y1="70" x2="100" y2="70" stroke="currentColor" strokeWidth="1" className="text-indigo-500 dark:text-indigo-300" />
            <line x1="0" y1="90" x2="100" y2="90" stroke="currentColor" strokeWidth="1" className="text-indigo-500 dark:text-indigo-300" />
          </svg>
        </div>
        
        {/* Vertical accent line */}
        <div className="absolute h-full w-px top-0 start-1/2 -translate-x-1/2 bg-gradient-to-b from-transparent via-indigo-300 to-transparent opacity-20" aria-hidden="true"></div>
      </div>
    </section>
  );
}
