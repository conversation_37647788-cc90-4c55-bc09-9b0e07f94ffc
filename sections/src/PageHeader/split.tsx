import { PageHeaderSectionProps } from './types';

export default function Split({ 
  tagline = 'Get the help you need', 
  title, 
  description 
}: PageHeaderSectionProps) {
  return (
    <section className="bg-background py-section-y" aria-labelledby="split-page-header-title">
      <div className="mx-auto max-w-container px-container-x">
        <div className="mx-auto grid max-w-content grid-cols-1 gap-x-element-x gap-y-element-y lg:max-w-none lg:grid-cols-2">
          <div>
            {tagline && <p className="text-body-base/body-base font-semibold text-primary">{tagline}</p>}
            <h1 id="split-page-header-title" className="mt-element-y text-heading-2/heading-2 font-bold tracking-tight text-foreground">{title}</h1>
          </div>
          <div className="lg:pt-element-y">
            <p className="text-body-base/body-base text-muted-foreground">
              {description}
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
