import { PageHeaderSectionProps } from './types';

export default function Minimal({ 
  tagline = 'Get the help you need', 
  title, 
  description 
}: PageHeaderSectionProps) {
  return (
    <section className="bg-background px-container-x py-section-y" aria-labelledby="minimal-page-header-title">
      <div className="mx-auto max-w-content text-body-base/body-base text-muted-foreground">
        {tagline && (
          <p className="text-body-base/body-base font-semibold text-primary">{tagline}</p>
        )}
        <h1 id="minimal-page-header-title" className="mt-element-y text-heading-2/heading-2 font-bold tracking-tight text-foreground">
          {title}
        </h1>
        <p className="mt-content-y text-body-large/body-large">
          {description}
        </p>
        <div className="mt-content-y max-w-content">
          <div className="mt-content-y h-px bg-border" />
        </div>
      </div>
    </section>
  );
}
