import { PageHeaderSectionProps } from './types';

export default function Compact({ 
  tagline = 'Get the help you need', 
  title, 
  description 
}: PageHeaderSectionProps) {
  return (
    <section className="bg-background py-section-y" aria-labelledby="compact-page-header-title">
      <div className="mx-auto max-w-container px-container-x">
        <div className="mx-auto max-w-content lg:text-center">
          {tagline && <p className="text-body-small/body-small font-semibold text-primary">{tagline}</p>}
          <h1 id="compact-page-header-title" className="mt-element-y text-heading-2/heading-2 font-bold tracking-tight text-foreground">{title}</h1>
          <p className="mt-element-y text-body-base/body-base text-muted-foreground">
            {description}
          </p>
        </div>
      </div>
    </section>
  );
}
