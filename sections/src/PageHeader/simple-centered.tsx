import { PageHeaderSectionProps } from './types';

export default function SimpleCentered({ 
  tagline = 'Get the help you need', 
  title, 
  description 
}: PageHeaderSectionProps) {
  return (
    <section className="bg-background px-container-x py-section-y" aria-labelledby="simple-centered-page-header-title">
      <div className="mx-auto max-w-content text-center">
        {tagline && <p className="text-body-base/body-base font-semibold text-primary">{tagline}</p>}
        <h1 id="simple-centered-page-header-title" className="mt-element-y text-heading-1/heading-1 font-semibold tracking-tight text-foreground">{title}</h1>
        <p className="mt-content-y text-pretty text-body-large/body-large font-medium text-muted-foreground">
          {description}
        </p>
      </div>
    </section>
  )
}