import type { 
  HeroSectionProps, 
  HeroImage
} from './types';
import { ButtonConfig } from '../Button';

// Default hero image
export const defaultHeroImage: HeroImage = {
  url: "/images_8bmWwQtW_KgMW6ymgvJn3Vsl.jpg",
  alt: "Hero Image"
};

// Default hero buttons
export const defaultHeroButtons: ButtonConfig[] = [
  {
    text: "Get started",
    buttonTarget: {
      type: "internal",
      value: "/"
    }
  },
  {
    text: "Learn more",
    buttonTarget: {
      type: "url",
      value: "https://example.com",
      openInNewWindow: true
    }
  }
];

// Default Hero component props
export const defaultHeroProps: HeroSectionProps = {
  id: "",
  title: "Data to enrich your online business",
  description: "Anim aute id magna aliqua ad ad non deserunt sunt. Qui irure qui lorem cupidatat commodo. Elit sunt amet fugiat veniam occaecat fugiat aliqua.",
  buttons: defaultHeroButtons,
  image: defaultHeroImage
};
