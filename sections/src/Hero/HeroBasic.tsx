import React from 'react';
import type { HeroSectionProps } from './types';
import { Button } from '../Button';
import { BlockContainer } from '../components/BlockContainer';
import ImgPipe from '../components/ImgPipe';

const BasicHero: React.FC<HeroSectionProps> = ({ 
  id, 
  title, 
  description, 
  buttons,
  resources, 
  customize,
  variants
}) => {
  return (
    <BlockContainer id={id} resources={resources} customize={customize}>
      <div className="mx-auto max-w-7xl lg:flex lg:px-8">
        <div className="mx-auto max-w-2xl lg:mx-0 lg:max-w-xl lg:flex-shrink-0 lg:pt-8">
          <h1 className="mt-10 text-4xl font-bold tracking-tight sm:text-6xl">
            {title}
          </h1>
          <p className="mt-6 text-lg leading-8">
            {description}
          </p>

          <div className="mt-10 flex items-center gap-x-6">
            {buttons?.map((item, index) => (
              <Button 
                key={index}
                buttonTarget={item?.buttonTarget}
                appearance={item?.appearance}
                className='mr-2'
              >
                {item.text}
              </Button>
            ))}
          </div>

        </div>
        <div className="mx-auto mt-16 flex max-w-2xl sm:mt-24 lg:ml-10 lg:mr-0 lg:mt-0 lg:max-w-none lg:flex-none xl:ml-32">
          <div className="max-w-3xl flex-none sm:max-w-5xl lg:max-w-none">
            <div className="-m-2 rounded-xl bg-gray-900/5 p-2 ring-1 ring-inset ring-gray-900/10 lg:-m-4 lg:rounded-2xl lg:p-4">
              <ImgPipe
                at={`${id}.image`}
                src={variants?.basic?.image?.url ?? '/images_8bmWwQtW_KgMW6ymgvJn3Vsl.jpg'}
                alt={`${variants?.basic?.image?.alt}`}
                width={2432}
                height={1442}
                className="w-[76rem] rounded-md shadow-2xl ring-1 ring-gray-900/10"
                                srcSet={variants?.basic?.image?.url ?? '/images_8bmWwQtW_KgMW6ymgvJn3Vsl.jpg'}
                sizes="(max-width: 640px) 100vw, (max-width: 1024px) 1024px, 1500px"
              />
            </div>
          </div>
        </div>
      </div>
    </BlockContainer>
  );
};

export default React.memo(BasicHero);