import React from 'react';
import type { HeroSectionProps } from './types';
import { Button } from '../Button';
import ImgPipe from '../components/ImgPipe';
import { BlockContainer } from '../components/BlockContainer';
import clsx from 'clsx';

interface HeroCustomize {
  isFullWidth?: boolean;
  isFullScreen?: boolean;
  bgColor?: string;
  textColor?: string;
  roundedPosition?: string;
  title?: any;
  description?: any;
  imageTiles?: {
    count?: number;
  };
}

const ImageTile: React.FC<{ id: string; index: number; src?: string; alt: string; className?: string }> = React.memo(
  ({ id, index, src, alt, className }) => (
    <div className={clsx("relative", className)}>
      <ImgPipe
        at={`${id}.images[${index}]`}
        src={src || '/images_8bmWwQtW_KgMW6ymgvJn3Vsl.jpg'}
        alt={alt}
        className="aspect-[2/3] w-full rounded-xl bg-gray-900/5 object-cover shadow-lg"
        loading="lazy"
      />
      <div className="pointer-events-none absolute inset-0 rounded-xl ring-1 ring-inset ring-gray-900/10" />
    </div>
  )
);

const HeroWithImageTiles: React.FC<HeroSectionProps & { customize?: HeroCustomize }> = ({ 
  id,
  resources, 
  title, 
  description, 
  buttons, 
  customize,
  variants,
}) => {
  return (
    <BlockContainer id={id} resources={resources} customize={customize}>
      <div className="w-full max-w-7xl mx-auto">
        <div className="w-full lg:flex lg:items-center lg:gap-x-14">
          <div className="w-full max-w-xl mx-auto lg:mx-0 lg:flex-auto lg:max-w-none xl:max-w-2xl">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              {title}
            </h1>
            <p className="mt-6 text-lg leading-8">
              {description}
            </p>

            <div className="mt-10 flex flex-col justify-center gap-4 sm:gap-6">
              {buttons?.map((item, index) => (
                <Button 
                  key={index}
                  buttonTarget={item?.buttonTarget}
                  appearance={item?.appearance}
                  className='text-base font-medium'
                >
                  {item.text}
                </Button>
              ))}
            </div>
          </div>
          <div className="mt-14 flex justify-end gap-8 sm:-mt-44 sm:justify-start sm:pl-20 lg:mt-0 lg:pl-0">
            <div className="ml-auto w-44 flex-none space-y-8 pt-32 sm:ml-0 sm:pt-80 lg:order-last lg:pt-36 xl:order-none xl:pt-80">
              <ImageTile
                id={id}
                index={0}
                src={variants?.withImageTiles?.images?.[0]?.url}
                alt={variants?.withImageTiles?.images?.[0]?.alt}
              />
            </div>
            <div className="mr-auto w-44 flex-none space-y-8 sm:mr-0 sm:pt-52 lg:pt-36">
              {[1, 2].map((index) => (
                <ImageTile
                  key={index}
                  id={id}
                  index={index}
                  src={variants?.withImageTiles?.images?.[index]?.url}
                  alt={variants?.withImageTiles?.images?.[index]?.alt}
                />
              ))}
            </div>
            <div className="w-44 flex-none space-y-8 pt-32 sm:pt-0">
              {[3, 4].map((index) => (
                <ImageTile
                  key={index}
                  id={id}
                  index={index}
                  src={variants?.withImageTiles?.images?.[index]?.url}
                  alt={variants?.withImageTiles?.images?.[index]?.alt}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </BlockContainer>
  );
};

export default React.memo(HeroWithImageTiles);