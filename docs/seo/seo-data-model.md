# SEO Data Model Documentation

## Table of Contents

1. [Introduction](#introduction)
2. [Data Model Overview](#data-model-overview)
3. [Website Level SEO Model](#website-level-seo-model)
   - [Structure](#website-seo-structure)
   - [Fields Description](#website-seo-fields-description)
   - [Sitemap Settings](#sitemap-settings)
4. [Page Level SEO Model](#page-level-seo-model)
   - [Structure](#page-seo-structure)
   - [Fields Description](#page-seo-fields-description)
5. [Integration with Existing Models](#integration-with-existing-models)
6. [Data Relationships](#data-relationships)
7. [Implementation Guidelines](#implementation-guidelines)
8. [API Design](#api-design)
9. [Migration Strategy](#migration-strategy)
10. [Future Enhancements](#future-enhancements)

## Introduction

This document provides a comprehensive description of the SEO data model for the Litpage platform. The model is designed to support both website-level and page-level SEO settings, enabling users to optimize their content for search engines effectively.

The SEO data model is built to support modern SEO best practices, including structured data, social media optimization, and search engine-specific configurations. It is designed to be flexible, extensible, and integrated with the existing page and website data models.

## Data Model Overview

The SEO data model consists of two primary entities:

1. **WebsiteSEO**: Contains global SEO settings that apply to the entire website
2. **PageSEO**: Contains page-specific SEO settings that override or complement the website-level settings

Both models are designed to be standalone entities with relationships to their respective parent models (Website and Page). This separation of concerns allows for more flexible and maintainable SEO management.

## Website Level SEO Model

### Website SEO Structure

```prisma
// Website-level SEO settings
model WebsiteSEO {
  id                String    @id @default(uuid())
  websiteId         String    @unique                      // Related website ID
  website           Website   @relation(fields: [websiteId], references: [id])
  
  // Global metadata
  siteName          String?                               // Website name
  titleTemplate     String?                               // Title template, e.g., "%s | Site Name"
  defaultDescription String?                              // Default description
  
  // Robots and Sitemap settings
  robotsTxt         String?                               // Custom robots.txt content
  sitemapSettings   Json?                                 // Sitemap configuration
  
  // Structured data
  organizationSchema Json?                                // Organization structured data
  websiteSchema     Json?                                 // Website structured data
  
  // Social media defaults
  defaultOgImage    String?                               // Default OG image
  defaultXCard      String?   @default("summary_large_image") // Default X (formerly Twitter) card type
  
  // Security and performance settings
  forceHttps        Boolean   @default(true)              // Force HTTPS usage
  cacheControl      String?                               // Cache control directives
  
  // Analytics tracking
  analyticsSettings Json?                                 // Analytics tools settings
  
  // Brand identity
  brandSettings     Json?                                 // Brand settings
  
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  
  @@index([websiteId])
}
```

### Website SEO Fields Description

| Field | Type | Description |
|-------|------|-------------|
| `id` | String | Unique identifier for the SEO settings |
| `websiteId` | String | Foreign key to the related Website |
| `siteName` | String? | The name of the website, used in various SEO contexts |
| `titleTemplate` | String? | Template for page titles, e.g., "%s \| Site Name" |
| `defaultDescription` | String? | Default meta description for pages without specific descriptions |
| `robotsTxt` | String? | Custom content for robots.txt file |
| `sitemapSettings` | Json? | Configuration for sitemap generation (see details below) |
| `organizationSchema` | Json? | JSON-LD structured data for the organization |
| `websiteSchema` | Json? | JSON-LD structured data for the website |
| `defaultOgImage` | String? | Default Open Graph image URL |
| `defaultXCard` | String? | Default X (formerly Twitter) card type |
| `forceHttps` | Boolean | Whether to force HTTPS for all URLs |
| `cacheControl` | String? | Cache control directives for SEO-related resources |
| `analyticsSettings` | Json? | Configuration for analytics tools |
| `brandSettings` | Json? | Brand identity settings |
| `createdAt` | DateTime | Timestamp of when the record was created |
| `updatedAt` | DateTime | Timestamp of when the record was last updated |

### Sitemap Settings

The `sitemapSettings` field is a JSON object that contains configuration for sitemap generation. It has the following structure:

```json
{
  "enabled": true,                    // Whether sitemap is enabled
  "autoGenerate": true,               // Whether to auto-generate sitemap
  "frequency": "daily",               // Sitemap update frequency
  "lastGenerated": "2025-04-26T10:00:00Z", // Last generation time
  "dynamicRoutes": [                  // Dynamic route configuration
    {
      "pattern": "/blog/:slug",
      "priority": 0.8,
      "changeFrequency": "weekly"
    },
    {
      "pattern": "/products/:id",
      "priority": 0.9,
      "changeFrequency": "daily"
    }
  ],
  "excludedPatterns": [               // Excluded route patterns
    "/admin/*",
    "/private/*",
    "/api/*"
  ],
  "includeImages": true,              // Whether to include images
  "alternateLanguagePages": true,     // Whether to include multilingual pages
  "defaultPriority": 0.5,             // Default priority
  "defaultChangeFrequency": "monthly", // Default change frequency
  "cacheExpiration": 3600,            // Cache expiration time (seconds)
  "pingSearchEngines": true,          // Whether to auto-notify search engines
  "searchEnginesToPing": [            // Search engines to notify
    "google",
    "bing",
    "baidu"
  ]
}
```

#### Sitemap Settings Fields

| Field | Type | Description |
|-------|------|-------------|
| `enabled` | Boolean | Whether sitemap generation is enabled |
| `autoGenerate` | Boolean | Whether to automatically generate the sitemap when content changes |
| `frequency` | String | How often the sitemap should be regenerated |
| `lastGenerated` | String | ISO timestamp of when the sitemap was last generated |
| `dynamicRoutes` | Array | Configuration for dynamic routes (e.g., blog posts, product pages) |
| `excludedPatterns` | Array | Patterns of URLs to exclude from the sitemap |
| `includeImages` | Boolean | Whether to include image information in the sitemap |
| `alternateLanguagePages` | Boolean | Whether to include hreflang tags for multilingual content |
| `defaultPriority` | Number | Default priority for pages (0.0 to 1.0) |
| `defaultChangeFrequency` | String | Default change frequency for pages |
| `cacheExpiration` | Number | How long to cache the sitemap (in seconds) |
| `pingSearchEngines` | Boolean | Whether to notify search engines when the sitemap is updated |
| `searchEnginesToPing` | Array | List of search engines to notify |

## Page Level SEO Model

### Page SEO Structure

```prisma
// Page-level SEO settings
model PageSEO {
  id                String    @id @default(uuid())
  pageId            String    @unique                      // Related page ID
  page              Page      @relation(fields: [pageId], references: [id])
  
  // Basic SEO
  title             String?                               // Custom title
  description       String?                               // Custom description
  keywords          String[]                              // Keywords
  
  // Social media
  ogTitle           String?                               // Open Graph title
  ogDescription     String?                               // Open Graph description
  ogImage           String?                               // Open Graph image URL
  twitterTitle      String?                               // Twitter title
  twitterDescription String?                              // Twitter description
  twitterImage      String?                               // Twitter image URL
  twitterCardType   String?   @default("summary_large_image") // Twitter card type
  
  // Structured data
  schemaType        String?                               // Structured data type
  schemaData        Json?                                 // Structured data content
  
  // Advanced settings
  canonicalUrl      String?                               // Canonical URL
  robots            Json?                                 // Robots directives
  hreflangLinks     Json?                                 // Hreflang links
  priority          Float?    @default(0.5)               // Sitemap priority
  changeFrequency   String?   @default("monthly")         // Change frequency
  
  // Inheritance settings
  inheritFromSite   Boolean   @default(true)              // Whether to inherit website settings
  
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  
  @@index([pageId])
}
```

### Page SEO Fields Description

| Field | Type | Description |
|-------|------|-------------|
| `id` | String | Unique identifier for the page SEO settings |
| `pageId` | String | Foreign key to the related Page |
| `title` | String? | Custom meta title for the page |
| `description` | String? | Custom meta description for the page |
| `keywords` | String[] | Array of keywords for the page |
| `ogTitle` | String? | Custom Open Graph title |
| `ogDescription` | String? | Custom Open Graph description |
| `ogImage` | String? | Custom Open Graph image URL |
| `twitterTitle` | String? | Custom Twitter title |
| `twitterDescription` | String? | Custom Twitter description |
| `twitterImage` | String? | Custom Twitter image URL |
| `twitterCardType` | String? | Twitter card type (e.g., "summary", "summary_large_image") |
| `schemaType` | String? | Type of structured data (e.g., "Article", "Product") |
| `schemaData` | Json? | JSON-LD structured data content |
| `canonicalUrl` | String? | Canonical URL for the page |
| `robots` | Json? | Robots directives for the page |
| `hreflangLinks` | Json? | Hreflang links for multilingual content |
| `priority` | Float? | Sitemap priority (0.0 to 1.0) |
| `changeFrequency` | String? | Change frequency for sitemap |
| `inheritFromSite` | Boolean | Whether to inherit settings from the website level |
| `createdAt` | DateTime | Timestamp of when the record was created |
| `updatedAt` | DateTime | Timestamp of when the record was last updated |

#### Robots JSON Structure

The `robots` field is a JSON object with the following structure:

```json
{
  "index": true,
  "follow": true,
  "noarchive": false,
  "nosnippet": false,
  "noimageindex": false,
  "nocache": false,
  "maxSnippet": -1,
  "maxImagePreview": "large",
  "maxVideoPreview": -1
}
```

#### Hreflang Links JSON Structure

The `hreflangLinks` field is a JSON array with the following structure:

```json
[
  {
    "language": "en",
    "url": "/about"
  },
  {
    "language": "zh-CN",
    "url": "/zh-CN/about"
  },
  {
    "language": "es",
    "url": "/es/about"
  }
]
```

## Integration with Existing Models

To integrate the SEO models with existing data models, we need to modify the `Page` and `Website` models:

### Modified Page Model

```prisma
model Page {
  // Existing fields...
  
  // Remove existing SEO fields
  // title, description, metaKeywords, metaDescription, ogTitle, ogDescription, ogImage
  
  // Add SEO relation
  seo               PageSEO?                              // Page SEO settings
  
  // Other existing fields...
}
```

### Modified Website Model

```prisma
model Website {
  // Existing fields...
  
  // Add SEO relation
  seo               WebsiteSEO?                           // Website SEO settings
  
  // Other existing fields...
}
```

## Data Relationships

The SEO data model establishes the following relationships:

1. **One-to-One**: Each Website has at most one WebsiteSEO record
2. **One-to-One**: Each Page has at most one PageSEO record
3. **Inheritance**: Page-level SEO settings can inherit from website-level settings

## Implementation Guidelines

### Data Validation

- Title length should be between 10 and 60 characters
- Description length should be between 50 and 160 characters
- Keywords should be relevant and not excessive (recommended: 3-8 keywords)
- URLs should be valid and follow proper format
- Structured data should validate against schema.org specifications

### Default Values

- Default title template: "%s | {siteName}"
- Default Twitter card type: "summary_large_image"
- Default sitemap priority: 0.5
- Default change frequency: "monthly"
- Default robots settings: index=true, follow=true

### Inheritance Logic

When `inheritFromSite` is true, the system should:

1. Use page-specific values when they exist
2. Fall back to website-level values when page-specific values are not provided
3. Apply the title template from the website level to the page title

## API Design

### Website SEO Endpoints

```
# Website-level SEO
GET    /api/websites/:websiteId/seo
PUT    /api/websites/:websiteId/seo

# Sitemap specific endpoints
GET    /api/websites/:websiteId/seo/sitemap
PUT    /api/websites/:websiteId/seo/sitemap
POST   /api/websites/:websiteId/seo/sitemap/generate
```

### Page SEO Endpoints

```
# Page-level SEO
GET    /api/pages/:pageId/seo
PUT    /api/pages/:pageId/seo

# Specialized endpoints
GET    /api/pages/:pageId/seo/preview
GET    /api/pages/:pageId/seo/social-preview
```

## Migration Strategy

To migrate from the current model to the new SEO data model:

1. Create the new `WebsiteSEO` and `PageSEO` tables
2. Write a migration script to:
   - Create a `WebsiteSEO` record for each website
   - Create a `PageSEO` record for each page, transferring existing SEO data
3. Update the application code to use the new models
4. Verify data integrity and functionality
5. Remove redundant SEO fields from the `Page` model after a grace period

### Migration Script Example

```typescript
async function migrateSEOData() {
  // Get all websites
  const websites = await prisma.website.findMany();
  
  // For each website, create a WebsiteSEO record
  for (const website of websites) {
    await prisma.websiteSEO.create({
      data: {
        websiteId: website.id,
        siteName: website.name,
        defaultDescription: website.description,
        forceHttps: true
      }
    });
  }
  
  // Get all pages
  const pages = await prisma.page.findMany();
  
  // For each page, create a PageSEO record
  for (const page of pages) {
    await prisma.pageSEO.create({
      data: {
        pageId: page.id,
        title: page.title,
        description: page.description,
        keywords: page.metaKeywords || [],
        ogTitle: page.ogTitle,
        ogDescription: page.ogDescription,
        ogImage: page.ogImage,
        inheritFromSite: true
      }
    });
  }
}
```

## Future Enhancements

The SEO data model is designed to be extensible. Future enhancements may include:

1. **Version Control**: Add version history for SEO settings
2. **Heading Structure**: Add support for managing heading structure (H1, H2, etc.)
3. **Content Analysis**: Add fields for content analysis results
4. **SEO Performance Tracking**: Add fields for tracking SEO performance metrics
5. **Keyword Tracking**: Add support for tracking keyword rankings
6. **A/B Testing**: Add support for SEO A/B testing

### Heading Structure (Future Implementation)

```prisma
model PageVersion {
  // Existing fields...
  
  // Add heading structure field
  headingStructure  Json?                                 // Heading structure data
  
  // Other existing fields...
}
```

The `headingStructure` field would store data in the following format:

```json
{
  "h1": [
    {
      "text": "Main Page Title",
      "position": 1
    }
  ],
  "h2": [
    {
      "text": "First Section Title",
      "position": 2
    },
    {
      "text": "Second Section Title",
      "position": 5
    }
  ],
  "h3": [
    {
      "text": "Subsection Title",
      "position": 3
    }
  ]
}
```

This structure would allow for analyzing and optimizing the heading hierarchy of pages, which is an important factor for both SEO and accessibility.
