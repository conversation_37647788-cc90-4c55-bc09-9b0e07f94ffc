# 页面渲染器主题系统

本文档描述了 LitPage 页面渲染器的主题系统实现，包括页面宽度、明暗模式和主题配置的处理机制。

## 简化架构总览

经过优化，我们实现了一个高效、简洁的主题系统：

1. **API 服务**：返回包含 `websiteConfig` 的页面数据
2. **root.tsx**：接收 `websiteConfig`，转换为 HTML 类名并应用到根元素
3. **CSS 系统**：通过类名选择器自动应用对应的 CSS 变量
4. **PageRender**：不再需要处理 `websiteConfig`，专注于页面内容渲染
5. **PageWrapper**：仅处理页面级样式配置

**关键优势**：
- ✅ 零客户端闪烁（服务端预设样式）
- ✅ 减少 JavaScript 开销（CSS 驱动）
- ✅ 架构简洁（职责分离）
- ✅ 性能优化（避免不必要的 DOM 操作）

## 系统架构

```mermaid
graph TD
    A[API 服务] -->|网站配置| B[页面数据]
    B -->|loader| C[Remix 路由]
    C -->|websiteConfig| D[root.tsx]
    D -->|HTML 类名| E[文档根元素]
    E -->|CSS 变量| F[全局样式]
    C -->|pageConfig| G[PageRender 组件]
    G -->|页面配置| H[PageWrapper 组件]
```

## 核心设计原则

### 1. 服务端渲染优先
- 在 `root.tsx` 中直接将网站配置应用到 `<html>` 标签
- 避免客户端 JavaScript 引起的样式闪烁
- 确保搜索引擎能正确索引主题样式

### 2. CSS 变量驱动
- 使用 CSS 变量实现主题切换
- 通过 HTML 类名控制 CSS 变量值
- 避免重新渲染 React 组件

### 3. 分离关注点
- **root.tsx**：处理全局主题配置（页面宽度、颜色模式、主题）
- **PageWrapper**：处理页面级样式配置（背景色、文字色等）
- **组件库**：使用 CSS 变量适应不同主题

## 核心功能

### 1. 页面宽度配置

支持三种页面宽度模式：

- **normal** (默认)：最大宽度 1280px，适合大多数内容
- **wide**：最大宽度 1536px，适合展示更多内容  
- **full**：100% 宽度，充分利用屏幕空间

#### CSS 实现

```css
/* 普通宽度 - 默认 */
:root {
  --container-max-width: 80rem; /* 1280px */
  --container-content-width: 65rem; /* 1040px */
  --container-padding-x: 1.5rem; /* 24px */
}

/* 宽屏模式 */
html.page-width-wide {
  --container-max-width: 96rem; /* 1536px */
  --container-content-width: 76rem; /* 1216px */
  --container-padding-x: 2rem; /* 32px */
}

/* 满屏模式 */
html.page-width-full {
  --container-max-width: 100%;
  --container-content-width: 85%;
  --container-padding-x: 3rem; /* 48px */
}
```

### 2. 明暗模式配置

支持三种颜色模式：

- **light**：明亮模式
- **dark**：暗色模式
- **system**：跟随系统设置

#### 系统模式检测

```css
@media (prefers-color-scheme: dark) {
  html.color-scheme-system {
    color-scheme: dark;
  }
  
  html.color-scheme-system:not(.dark) {
    /* 暗色模式 CSS 变量 */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    /* ... 其他变量 */
  }
}
```

### 3. 主题配置

支持多种预设主题：

- **default**：默认主题
- **tech**：科技主题
- **creative**：创意主题
- **finance**：金融主题
- **education**：教育主题
- **gradient-***：渐变主题系列

## 数据流程

### 1. API 端配置

```typescript
// api/src/page/page.service.ts
const websiteConfig = website?.configuration as { 
  theme?: string; 
  pageWidth?: string; 
  colorMode?: string; 
  [key: string]: any; 
} || {};

return {
  success: true,
  data: {
    // ... 其他数据
    websiteConfig: {
      pageWidth: websiteConfig.pageWidth || 'normal',
      colorMode: websiteConfig.colorMode || 'light',
      theme: websiteConfig.theme || 'default',
      ...websiteConfig
    }
  }
};
```

### 2. 根组件处理（关键实现）

```typescript
// render/app/root.tsx
function generatePageClasses(websiteConfig: any) {
  const classes = [];
  
  // 页面宽度设置
  if (websiteConfig?.pageWidth) {
    switch (websiteConfig.pageWidth) {
      case 'wide':
        classes.push('page-width-wide');
        break;
      case 'full':
        classes.push('page-width-full');
        break;
    }
  }
  
  // 颜色模式设置
  if (websiteConfig?.colorMode) {
    switch (websiteConfig.colorMode) {
      case 'dark':
        classes.push('dark');
        break;
      case 'system':
        classes.push('color-scheme-system');
        break;
    }
  }
  
  // 主题设置
  if (websiteConfig?.theme && websiteConfig.theme !== 'default') {
    classes.push(`theme-${websiteConfig.theme}`);
  }
  
  return classes.join(' ');
}

export default function App() {
  const resolvedData = /* 获取数据 */;
  const websiteConfig = resolvedData?.websiteConfig || {};
  const pageClasses = generatePageClasses(websiteConfig);
  const dataTheme = websiteConfig?.theme || 'default';

  return (
    <html lang={lang} className={pageClasses} data-theme={dataTheme}>
      <head>
        {/* 系统色彩模式检测脚本 */}
        {websiteConfig?.colorMode === 'system' && (
          <script
            dangerouslySetInnerHTML={{
              __html: `
                (function() {
                  try {
                    if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
                      document.documentElement.classList.add('dark');
                    } else {
                      document.documentElement.classList.remove('dark');
                    }
                  } catch (e) {}
                })();
              `,
            }}
          />
        )}
      </head>
      <body>
        <Outlet />
      </body>
    </html>
  );
}
```

### 3. 简化的 PageWrapper

```typescript
// sections/src/PageWrapper.tsx
export function PageWrapper({ customize, children }: PageWrapperProps) {
  // 生成容器类名（仅处理页面级样式）
  const containerClasses = clsx(
    'min-h-screen',
    customize?.bgColor,
    customize?.textColor,
    customize?.className
  );
  
  return (
    <div className={containerClasses}>
      {children}
    </div>
  );
}
```

## 配置接口

### WebsiteConfig 接口

```typescript
interface WebsiteConfig {
  pageWidth?: 'normal' | 'wide' | 'full';
  colorMode?: 'light' | 'dark' | 'system';
  theme?: string;
  [key: string]: any;
}
```

### PageRenderProps 接口

```typescript
interface PageRenderProps {
  domain: string;
  headers?: any;
  schema?: any;
  footers?: any;
  language?: string;
  languageInfo?: LanguageInfo;
  languageVersions?: Record<string, LanguageVersion>;
  languageUrls?: Record<string, string>;
  seo?: SEOData;
  // websiteConfig 不再需要，因为已在 root.tsx 中处理
}
```

## 使用示例

### 1. 配置网站主题

```typescript
// 通过 API 更新网站配置
await websiteService.updateWebsiteConfig(websiteId, {
  configuration: {
    pageWidth: 'wide',
    colorMode: 'dark',
    theme: 'tech'
  }
});
```

### 2. 自动应用配置

配置会自动在服务端渲染时应用到 HTML 根元素：

```html
<html lang="en" class="page-width-wide dark theme-tech" data-theme="tech">
  <!-- 页面内容 -->
</html>
```

组件渲染时无需额外传递 websiteConfig：

```tsx
<PageRender
  domain={domain}
  headers={headers}
  footers={footers}
  schema={configuration}
  language={language}
  languageInfo={languageInfo}
  languageVersions={languageVersions}
  languageUrls={languageUrls}
  seo={seo}
  // 不再需要 websiteConfig 参数
/>
```

### 3. CSS 变量自动生效

```css
/* 宽屏 + 暗色模式 + 科技主题的组合效果 */
html.page-width-wide.dark.theme-tech {
  --container-max-width: 96rem;
  --background: 222.2 84% 4.9%;
  --primary: 230 80% 60%;
  /* ... 其他变量 */
}
```

## 最佳实践

### 1. 服务端渲染优化

- ✅ 在 `root.tsx` 中设置基础配置，避免闪烁
- ✅ 使用内联脚本处理系统模式检测
- ✅ 利用 CSS 变量实现平滑的主题切换

### 2. 性能优化

- ✅ 避免客户端 JavaScript 动态修改 DOM
- ✅ 使用 CSS 变量而非重新渲染组件
- ✅ 减少不必要的 useEffect 依赖

### 3. 架构简洁性

- ✅ 单一职责：root.tsx 处理全局配置，PageWrapper 处理页面配置
- ✅ 减少组件间耦合
- ✅ 避免重复的配置处理逻辑

## 架构优势

### 1. 性能优势
- **零客户端闪烁**：服务端预设样式
- **减少 JavaScript 开销**：CSS 驱动的主题切换
- **更快的初始渲染**：避免客户端样式计算

### 2. 开发体验
- **简洁的代码结构**：职责分离明确
- **易于维护**：配置集中处理
- **类型安全**：完整的 TypeScript 支持

### 3. 用户体验
- **无缝主题切换**：CSS 变量平滑过渡
- **系统模式支持**：自动检测用户偏好
- **响应式设计**：自适应不同屏幕尺寸

## 扩展指南

### 添加新主题

1. 在 `tailwind.css` 中定义主题样式
2. 在 `generatePageClasses` 中添加主题映射
3. 确保暗色模式兼容性

### 添加新页面宽度模式

1. 定义新的 CSS 变量
2. 更新 `generatePageClasses` 函数
3. 在界面中添加新选项

### 自定义颜色模式

1. 扩展 `colorMode` 类型定义
2. 在 `generatePageClasses` 中添加处理逻辑
3. 提供相应的 CSS 规则 