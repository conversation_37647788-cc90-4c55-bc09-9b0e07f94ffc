# 基础阶段 - 设置共享的基础环境
FROM node:20-alpine AS base

# 使用一个 RUN 命令完成所有系统级配置，减少镜像层数并提高构建效率
RUN apk add --no-cache libc6-compat && \
npm install -g pnpm@9.15.4 && \
    rm -rf /var/cache/apk/*

WORKDIR /app

# 环境变量集中设置，确保构建和运行环境的一致性
ENV PNPM_HOME=/usr/local/lib/node_modules/pnpm \
    PATH="/usr/local/bin:${PATH}" \
    NEXT_TELEMETRY_DISABLED=1

# 构建阶段 - 负责依赖安装和应用构建
FROM base AS builder
WORKDIR /app

# 首先复制包管理和工作空间配置文件
# 这样可以利用 Docker 的缓存机制提高构建速度
COPY pnpm-workspace.yaml package.json pnpm-lock.yaml ./
COPY sections/package.json ./sections/
COPY render/package.json ./render/

# 复制 TypeScript 配置
COPY sections/tsconfig.json ./sections/
COPY render/tsconfig.json ./

# 复制构建和样式配置文件
COPY render/tailwind.config.ts render/postcss.config.cjs render/vite.config.ts render/components.json ./render/

# 使用高级缓存策略安装依赖
# 设置 NODE_ENV=development 确保开发依赖的安装
# 添加 fallback 策略处理 lockfile 不同步问题
RUN --mount=type=cache,target=/root/.local/share/pnpm/store \
    NODE_ENV=development pnpm install --frozen-lockfile || \
    (echo "Lockfile outdated, updating..." && NODE_ENV=development pnpm install)

# 复制源代码文件
COPY sections ./sections
COPY render ./render

# 执行生产构建
RUN cd render && \
    NODE_ENV=production pnpm build

# 生产阶段 - 创建最终的运行环境
FROM base AS runner

# 创建非 root 用户以提高安全性
RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 remixuser && \
    adduser remixuser nodejs

WORKDIR /app

# 复制必要的配置文件，确保依赖管理工具能正常工作
COPY --from=builder /app/package.json /app/pnpm-workspace.yaml /app/pnpm-lock.yaml ./
COPY --from=builder /app/sections/package.json ./sections/
COPY --from=builder /app/render/package.json ./render/

# 复制构建产物和静态资源
COPY --from=builder --chown=remixuser:nodejs /app/render/build ./render/build
COPY --from=builder --chown=remixuser:nodejs /app/render/public ./render/public

# 设置生产环境并安装依赖
ENV NODE_ENV=production
RUN --mount=type=cache,target=/root/.local/share/pnpm/store \
    pnpm install --prod --frozen-lockfile || \
    (echo "Lockfile outdated, installing production dependencies..." && pnpm install --prod) && \
    # 清理 pnpm 缓存和其他临时文件
    pnpm store prune && \
    rm -rf /root/.cache /tmp/* && \
    # 移除不必要的文件
    find . -name "*.map" -delete && \
    find . -name "LICENSE*" -delete && \
    find . -name "*.md" -delete && \
    find . -name "*.ts" -delete

# 设置容器运行配置
ENV PORT=3000 \
    HOST=0.0.0.0

# 设置适当的文件权限
RUN chown -R remixuser:nodejs /app

# 切换到非 root 用户
USER remixuser

EXPOSE 3000

# 健康检查配置
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/healthcheck || exit 1

# 使用工作目录中的服务启动命令
WORKDIR /app/render
CMD ["pnpm", "start"]