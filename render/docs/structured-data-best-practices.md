# Remix Structured Data Best Practices

This document outlines the best practices for implementing structured data (Schema.org JSON-LD) in a Remix application to improve SEO and search engine visibility.

## Table of Contents

1. [Component-Based Approach](#component-based-approach)
2. [Type-Safe Schema Building](#type-safe-schema-building)
3. [Route-Level Structured Data](#route-level-structured-data)
4. [Performance Optimization](#performance-optimization)
5. [Error Handling and Validation](#error-handling-and-validation)
6. [Dynamic and Conditional Schemas](#dynamic-and-conditional-schemas)
7. [Testing and Debugging](#testing-and-debugging)
8. [SEO Optimization](#seo-optimization)
9. [Complete Example](#complete-example)
10. [Integration with CMS](#integration-with-cms)

## Component-Based Approach

### Creating a Dedicated Component

Encapsulate structured data logic in a dedicated component for better maintainability and reusability:

```tsx
// app/components/StructuredData.tsx
import { useMemo } from "react";

interface StructuredDataProps {
  pageSchema?: any;
  organizationSchema?: any;
  websiteSchema?: any;
  schemaType?: string;
}

export function StructuredData({
  pageSchema,
  organizationSchema,
  websiteSchema,
  schemaType
}: StructuredDataProps) {
  const processedPageSchema = useMemo(() => {
    if (!pageSchema) return null;
    
    let schemaContent = pageSchema;
    
    // If it's an object and not a string, ensure it has @context and @type
    if (typeof schemaContent === 'object') {
      if (!schemaContent['@context']) {
        schemaContent['@context'] = 'https://schema.org';
      }
      if (!schemaContent['@type'] && schemaType) {
        schemaContent['@type'] = schemaType;
      }
    }
    
    return schemaContent;
  }, [pageSchema, schemaType]);
  
  return (
    <>
      {processedPageSchema && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(processedPageSchema)
          }}
        />
      )}
      
      {organizationSchema && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(organizationSchema)
          }}
        />
      )}
      
      {websiteSchema && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(websiteSchema)
          }}
        />
      )}
    </>
  );
}
```

### Using the Component in Root

```tsx
// app/root.tsx
import { StructuredData } from "~/components/StructuredData";

export default function App() {
  const { seo } = useLoaderData();
  
  return (
    <html lang="en">
      <head>
        {/* ... */}
        <StructuredData
          pageSchema={seo?.schemaData}
          schemaType={seo?.schemaType}
          organizationSchema={seo?.organizationSchema}
          websiteSchema={seo?.websiteSchema}
        />
      </head>
      <body>
        {/* ... */}
      </body>
    </html>
  );
}
```

## Type-Safe Schema Building

### Defining Schema Types

Create type-safe schema definitions:

```tsx
// app/utils/schema/types.ts
export type SchemaType = 
  | 'Article'
  | 'Product'
  | 'FAQPage'
  | 'Organization'
  | 'WebSite'
  | 'LocalBusiness'
  | 'Person'
  | 'Event';

export interface BaseSchema {
  '@context': string;
  '@type': SchemaType;
}

export interface ArticleSchema extends BaseSchema {
  '@type': 'Article';
  headline: string;
  image?: string[];
  datePublished?: string;
  dateModified?: string;
  author?: {
    '@type': 'Person';
    name: string;
  };
}

export interface ProductSchema extends BaseSchema {
  '@type': 'Product';
  name: string;
  description?: string;
  image?: string[];
  offers?: {
    '@type': 'Offer';
    price: number;
    priceCurrency: string;
    availability?: string;
  };
}

// Add more schema interfaces as needed
```

### Creating Schema Builder Functions

```tsx
// app/utils/schema/creators.ts
import { BaseSchema, ArticleSchema, ProductSchema, SchemaType } from "./types";
import { validateSchema } from "./validators";

export function createBaseSchema(type: SchemaType): BaseSchema {
  return {
    '@context': 'https://schema.org',
    '@type': type
  };
}

export function createArticleSchema(data: Omit<ArticleSchema, '@context' | '@type'>): ArticleSchema {
  const schema = {
    ...createBaseSchema('Article'),
    ...data
  } as ArticleSchema;
  
  validateSchema(schema, 'Article');
  return schema;
}

export function createProductSchema(data: Omit<ProductSchema, '@context' | '@type'>): ProductSchema {
  const schema = {
    ...createBaseSchema('Product'),
    ...data
  } as ProductSchema;
  
  validateSchema(schema, 'Product');
  return schema;
}

// Add more schema creator functions as needed
```

### Using Schema Builders in Loaders

```tsx
// app/routes/products.$id.tsx
import { createProductSchema } from "~/utils/schema/creators";

export async function loader({ params }) {
  const product = await getProduct(params.id);
  
  const productSchema = createProductSchema({
    name: product.name,
    description: product.description,
    image: [product.imageUrl],
    offers: {
      '@type': 'Offer',
      price: product.price,
      priceCurrency: 'USD',
      availability: product.inStock 
        ? 'https://schema.org/InStock' 
        : 'https://schema.org/OutOfStock'
    }
  });
  
  return json({
    product,
    seo: {
      schemaData: productSchema,
      // schemaType is not needed separately as it's included in productSchema
    }
  });
}
```

## Route-Level Structured Data

### Using Handle Export

Leverage Remix's `handle` export to define route-specific structured data:

```tsx
// app/routes/blog.$slug.tsx
import { createArticleSchema } from "~/utils/schema/creators";

export async function loader({ params }) {
  const article = await getArticle(params.slug);
  
  return json({
    article,
    // Other data...
  });
}

export const handle = {
  getStructuredData: (data) => {
    const { article } = data;
    
    return createArticleSchema({
      headline: article.title,
      image: [article.coverImage],
      datePublished: article.publishedAt,
      dateModified: article.updatedAt,
      author: {
        '@type': 'Person',
        name: article.author.name
      }
    });
  }
};
```

### Collecting Route Schemas in Root

```tsx
// app/root.tsx
import { useMatches } from "@remix-run/react";

export default function App() {
  const matches = useMatches();
  const { seo } = useLoaderData();
  
  // Collect route-level structured data
  const routeSchemas = matches
    .filter(match => match.handle?.getStructuredData)
    .map(match => match.handle.getStructuredData(match.data));
  
  // Merge all structured data
  const allSchemas = [
    ...(seo?.schemaData ? [seo.schemaData] : []),
    ...(seo?.organizationSchema ? [seo.organizationSchema] : []),
    ...(seo?.websiteSchema ? [seo.websiteSchema] : []),
    ...routeSchemas
  ];
  
  return (
    <html lang="en">
      <head>
        {/* ... */}
        {allSchemas.map((schema, index) => (
          <script
            key={`schema-${index}`}
            type="application/ld+json"
            dangerouslySetInnerHTML={{
              __html: JSON.stringify(schema)
            }}
          />
        ))}
      </head>
      <body>
        {/* ... */}
      </body>
    </html>
  );
}
```

## Performance Optimization

### Using useMemo for JSON Stringification

```tsx
// app/components/StructuredData.tsx
export function StructuredData({ schemas }) {
  // Cache JSON stringification results
  const processedSchemas = useMemo(() => {
    return schemas.map(schema => {
      // Process schema...
      return JSON.stringify(schema);
    });
  }, [schemas]);
  
  return (
    <>
      {processedSchemas.map((schemaString, index) => (
        <script
          key={`schema-${index}`}
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: schemaString }}
        />
      ))}
    </>
  );
}
```

### Response Caching

```tsx
// app/routes/products.$id.tsx
export async function loader({ params, request }) {
  const product = await getProduct(params.id);
  
  const productSchema = createProductSchema({
    // ...
  });
  
  return json(
    {
      product,
      seo: {
        schemaData: productSchema,
      }
    },
    {
      headers: {
        "Cache-Control": "public, max-age=300, s-maxage=3600"
      }
    }
  );
}
```

### Lazy Loading Non-Critical Schemas

For very large schemas that aren't critical for initial rendering:

```tsx
// app/components/LazyStructuredData.tsx
import { useEffect, useState } from "react";

export function LazyStructuredData({ getSchema }) {
  const [schema, setSchema] = useState(null);
  
  useEffect(() => {
    // Load schema after component mounts
    const loadSchema = async () => {
      const result = await getSchema();
      setSchema(result);
    };
    
    loadSchema();
  }, [getSchema]);
  
  if (!schema) return null;
  
  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(schema)
      }}
    />
  );
}
```

## Error Handling and Validation

### Schema Validation Function

```tsx
// app/utils/schema/validators.ts
import { BaseSchema, SchemaType } from "./types";

export function validateSchema(schema: any, type: SchemaType): boolean {
  // Basic validation
  if (!schema || typeof schema !== 'object') {
    console.error(`[Schema] Invalid schema: not an object`);
    return false;
  }
  
  // Check required fields
  if (schema['@type'] !== type) {
    console.error(`[Schema] Invalid schema: expected type ${type}, got ${schema['@type']}`);
    return false;
  }
  
  // Type-specific validation
  switch (type) {
    case 'Article':
      if (!schema.headline) {
        console.error(`[Schema] Invalid Article schema: missing headline`);
        return false;
      }
      break;
    case 'Product':
      if (!schema.name) {
        console.error(`[Schema] Invalid Product schema: missing name`);
        return false;
      }
      break;
    // Add validation for other types
  }
  
  return true;
}
```

### Error Handling in Component

```tsx
// app/components/StructuredData.tsx
export function StructuredData({ schemas }) {
  return (
    <>
      {schemas.map((schema, index) => {
        // Error handling
        if (!schema) return null;
        
        try {
          const schemaString = JSON.stringify(schema);
          return (
            <script
              key={`schema-${index}`}
              type="application/ld+json"
              dangerouslySetInnerHTML={{ __html: schemaString }}
            />
          );
        } catch (error) {
          console.error(`[StructuredData] Error processing schema at index ${index}:`, error);
          return null;
        }
      })}
    </>
  );
}
```

## Dynamic and Conditional Schemas

### Based on Route and Data

```tsx
// app/root.tsx
export default function App() {
  const { pathname } = useLocation();
  const { seo, pageType } = useLoaderData();
  
  // Dynamically decide which schemas to use
  const schemas = [];
  
  // Always include website and organization schemas
  if (seo?.websiteSchema) schemas.push(seo.websiteSchema);
  if (seo?.organizationSchema) schemas.push(seo.organizationSchema);
  
  // Add page-specific schema based on page type
  if (pageType === 'product' && seo?.schemaData) {
    schemas.push(seo.schemaData);
  }
  
  // Special handling for homepage
  if (pathname === '/') {
    schemas.push({
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      url: 'https://example.com/',
      potentialAction: {
        '@type': 'SearchAction',
        target: 'https://example.com/search?q={search_term_string}',
        'query-input': 'required name=search_term_string'
      }
    });
  }
  
  return (
    <html lang="en">
      <head>
        {/* ... */}
        <StructuredData schemas={schemas} />
      </head>
      <body>
        {/* ... */}
      </body>
    </html>
  );
}
```

### Conditional Schema Fields

```tsx
// app/routes/products.$id.tsx
export async function loader({ params }) {
  const product = await getProduct(params.id);
  
  const productSchema = createProductSchema({
    name: product.name,
    description: product.description,
    image: product.images.map(img => img.url),
    ...(product.price && {
      offers: {
        '@type': 'Offer',
        price: product.price,
        priceCurrency: 'USD',
        availability: product.inStock 
          ? 'https://schema.org/InStock' 
          : 'https://schema.org/OutOfStock'
      }
    }),
    ...(product.reviews?.length > 0 && {
      review: product.reviews.map(review => ({
        '@type': 'Review',
        reviewRating: {
          '@type': 'Rating',
          ratingValue: review.rating
        },
        author: {
          '@type': 'Person',
          name: review.author
        },
        reviewBody: review.content
      }))
    })
  });
  
  return json({
    product,
    seo: {
      schemaData: productSchema,
    }
  });
}
```

## Testing and Debugging

### Schema Test Tool

```tsx
// app/routes/admin.schema-test.tsx
import { json } from "@remix-run/node";
import { useLoaderData, Form } from "@remix-run/react";
import { createArticleSchema, createProductSchema } from "~/utils/schema/creators";

export async function action({ request }) {
  const formData = await request.formData();
  const schemaType = formData.get("schemaType");
  const schemaData = JSON.parse(formData.get("schemaData") || "{}");
  
  let schema;
  switch (schemaType) {
    case "Article":
      schema = createArticleSchema(schemaData);
      break;
    case "Product":
      schema = createProductSchema(schemaData);
      break;
    // Add other types
  }
  
  return json({ schema });
}

export default function SchemaTest() {
  const { schema } = useLoaderData();
  
  return (
    <div>
      <h1>Schema.org Test Tool</h1>
      
      <Form method="post">
        <select name="schemaType">
          <option value="Article">Article</option>
          <option value="Product">Product</option>
          {/* Add other types */}
        </select>
        
        <textarea
          name="schemaData"
          placeholder="Enter schema data as JSON"
          rows={10}
          cols={50}
        />
        
        <button type="submit">Generate Schema</button>
      </Form>
      
      {schema && (
        <>
          <h2>Generated Schema:</h2>
          <pre>{JSON.stringify(schema, null, 2)}</pre>
          
          <h2>Preview:</h2>
          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{
              __html: JSON.stringify(schema)
            }}
          />
          
          <p>
            <a
              href={`https://search.google.com/test/rich-results?url=${encodeURIComponent(
                window.location.href
              )}`}
              target="_blank"
              rel="noopener noreferrer"
            >
              Test with Google Rich Results Test
            </a>
          </p>
        </>
      )}
    </div>
  );
}
```

### Development Debug UI

```tsx
// app/components/StructuredData.tsx
export function StructuredData({ schemas }) {
  // Log schemas in development
  if (process.env.NODE_ENV === "development") {
    console.log("[StructuredData] Schemas:", schemas);
  }
  
  return (
    <>
      {schemas.map((schema, index) => (
        <script
          key={`schema-${index}`}
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(schema)
          }}
        />
      ))}
      
      {/* Debug UI in development */}
      {process.env.NODE_ENV === "development" && (
        <div
          id="schema-debug"
          style={{
            position: "fixed",
            bottom: "10px",
            right: "10px",
            background: "#f0f0f0",
            padding: "10px",
            border: "1px solid #ccc",
            borderRadius: "5px",
            zIndex: 9999,
            maxHeight: "300px",
            overflow: "auto"
          }}
        >
          <h4>Structured Data (Dev Only)</h4>
          {schemas.map((schema, index) => (
            <details key={index}>
              <summary>{schema['@type'] || `Schema ${index + 1}`}</summary>
              <pre>{JSON.stringify(schema, null, 2)}</pre>
            </details>
          ))}
        </div>
      )}
    </>
  );
}
```

## SEO Optimization

### Breadcrumb Schema

```tsx
// app/utils/breadcrumbs.ts
import { useMatches } from "@remix-run/react";

export function useBreadcrumbSchema() {
  const matches = useMatches();
  
  // Filter routes with breadcrumb handle
  const breadcrumbMatches = matches.filter(
    match => match.handle && match.handle.breadcrumb
  );
  
  // Build breadcrumb items
  const breadcrumbItems = breadcrumbMatches.map((match, index) => {
    const breadcrumb = typeof match.handle.breadcrumb === "function"
      ? match.handle.breadcrumb(match.data)
      : match.handle.breadcrumb;
    
    return {
      "@type": "ListItem",
      "position": index + 1,
      "name": breadcrumb.title,
      "item": `https://example.com${breadcrumb.url || match.pathname}`
    };
  });
  
  // Return null if not enough breadcrumb items
  if (breadcrumbItems.length < 2) {
    return null;
  }
  
  // Create BreadcrumbList Schema
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": breadcrumbItems
  };
}
```

### Route Definitions with Breadcrumbs

```tsx
// app/routes/_layout.tsx
export const handle = {
  breadcrumb: {
    title: "Home",
    url: "/"
  }
};

// app/routes/_layout.products.tsx
export const handle = {
  breadcrumb: {
    title: "Products",
    url: "/products"
  }
};

// app/routes/_layout.products.$id.tsx
export const handle = {
  breadcrumb: (data) => ({
    title: data.product.name,
    url: `/products/${data.product.id}`
  })
};
```

### Using Breadcrumbs in Root

```tsx
// app/root.tsx
import { useBreadcrumbSchema } from "~/utils/breadcrumbs";

export default function App() {
  const { seo } = useLoaderData();
  const breadcrumbSchema = useBreadcrumbSchema();
  
  // Merge all structured data
  const schemas = [
    ...(seo?.schemaData ? [seo.schemaData] : []),
    ...(seo?.organizationSchema ? [seo.organizationSchema] : []),
    ...(seo?.websiteSchema ? [seo.websiteSchema] : []),
    ...(breadcrumbSchema ? [breadcrumbSchema] : [])
  ];
  
  return (
    <html lang="en">
      <head>
        {/* ... */}
        <StructuredData schemas={schemas} />
      </head>
      <body>
        {/* ... */}
      </body>
    </html>
  );
}
```

## Complete Example

Here's a complete example of a structured data management system:

```tsx
// app/utils/schema/types.ts
export type SchemaType = 
  | 'Article'
  | 'Product'
  | 'FAQPage'
  | 'Organization'
  | 'WebSite'
  | 'LocalBusiness'
  | 'Person'
  | 'Event';

export interface BaseSchema {
  '@context': string;
  '@type': SchemaType;
}

export interface ArticleSchema extends BaseSchema {
  '@type': 'Article';
  headline: string;
  // Other properties...
}

export interface ProductSchema extends BaseSchema {
  '@type': 'Product';
  name: string;
  // Other properties...
}

// Other schema interfaces...

// app/utils/schema/creators.ts
import { BaseSchema, ArticleSchema, ProductSchema, SchemaType } from "./types";
import { validateSchema } from "./validators";

export function createBaseSchema(type: SchemaType): BaseSchema {
  return {
    '@context': 'https://schema.org',
    '@type': type
  };
}

export function createArticleSchema(data: Omit<ArticleSchema, '@context' | '@type'>): ArticleSchema {
  const schema = {
    ...createBaseSchema('Article'),
    ...data
  } as ArticleSchema;
  
  validateSchema(schema, 'Article');
  return schema;
}

export function createProductSchema(data: Omit<ProductSchema, '@context' | '@type'>): ProductSchema {
  const schema = {
    ...createBaseSchema('Product'),
    ...data
  } as ProductSchema;
  
  validateSchema(schema, 'Product');
  return schema;
}

// Other schema creator functions...

// app/utils/schema/validators.ts
import { BaseSchema, SchemaType } from "./types";

export function validateSchema(schema: BaseSchema, expectedType: SchemaType): boolean {
  // Validation logic...
  return true;
}

// app/components/StructuredData.tsx
import { useMemo } from "react";
import { BaseSchema } from "~/utils/schema/types";

interface StructuredDataProps {
  schemas: (BaseSchema | null | undefined)[];
}

export function StructuredData({ schemas }: StructuredDataProps) {
  // Filter invalid schemas and cache JSON strings
  const processedSchemas = useMemo(() => {
    return schemas
      .filter((schema): schema is BaseSchema => !!schema)
      .map(schema => {
        try {
          return JSON.stringify(schema);
        } catch (error) {
          console.error("[StructuredData] Error stringifying schema:", error);
          return null;
        }
      })
      .filter((schema): schema is string => !!schema);
  }, [schemas]);
  
  return (
    <>
      {processedSchemas.map((schemaString, index) => (
        <script
          key={`schema-${index}`}
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: schemaString }}
        />
      ))}
    </>
  );
}

// app/root.tsx
import { useMatches } from "@remix-run/react";
import { StructuredData } from "~/components/StructuredData";
import { useBreadcrumbSchema } from "~/utils/breadcrumbs";

export default function App() {
  const matches = useMatches();
  const { seo } = useLoaderData();
  const breadcrumbSchema = useBreadcrumbSchema();
  
  // Collect route-level structured data
  const routeSchemas = matches
    .filter(match => match.handle?.getStructuredData)
    .map(match => match.handle.getStructuredData(match.data));
  
  // Merge all structured data
  const schemas = [
    ...(seo?.schemaData ? [seo.schemaData] : []),
    ...(seo?.organizationSchema ? [seo.organizationSchema] : []),
    ...(seo?.websiteSchema ? [seo.websiteSchema] : []),
    ...(breadcrumbSchema ? [breadcrumbSchema] : []),
    ...routeSchemas
  ];
  
  return (
    <html lang="en">
      <head>
        {/* ... */}
        <StructuredData schemas={schemas} />
      </head>
      <body>
        {/* ... */}
      </body>
    </html>
  );
}
```

## Integration with CMS

### Fetching Schema Data from CMS

```tsx
// app/routes/products.$id.tsx
export async function loader({ params }) {
  const product = await getProduct(params.id);
  
  // Get schema data from CMS
  const schemaFromCMS = await getProductSchema(params.id);
  
  // If CMS provides schema, use it; otherwise, generate it
  const productSchema = schemaFromCMS || createProductSchema({
    name: product.name,
    description: product.description,
    image: [product.imageUrl],
    offers: {
      '@type': 'Offer',
      price: product.price,
      priceCurrency: 'USD',
      availability: product.inStock 
        ? 'https://schema.org/InStock' 
        : 'https://schema.org/OutOfStock'
    }
  });
  
  return json({
    product,
    seo: {
      schemaData: productSchema,
    }
  });
}
```

### Schema Editor Component

For a CMS with a schema editor:

```tsx
// app/routes/admin.schema-editor.$id.tsx
import { json, redirect } from "@remix-run/node";
import { useLoaderData, Form } from "@remix-run/react";
import { useState } from "react";

export async function loader({ params }) {
  const { id } = params;
  const page = await getPage(id);
  const schema = await getPageSchema(id);
  
  return json({
    page,
    schema: schema || { '@context': 'https://schema.org' }
  });
}

export async function action({ request, params }) {
  const { id } = params;
  const formData = await request.formData();
  const schemaJson = formData.get("schema");
  
  try {
    const schema = JSON.parse(schemaJson);
    await savePageSchema(id, schema);
    return redirect(`/admin/pages/${id}`);
  } catch (error) {
    return json({ error: "Invalid JSON" }, { status: 400 });
  }
}

export default function SchemaEditor() {
  const { page, schema } = useLoaderData();
  const [editorValue, setEditorValue] = useState(JSON.stringify(schema, null, 2));
  const [error, setError] = useState("");
  
  const handleChange = (e) => {
    setEditorValue(e.target.value);
    try {
      JSON.parse(e.target.value);
      setError("");
    } catch (err) {
      setError("Invalid JSON");
    }
  };
  
  return (
    <div>
      <h1>Schema Editor: {page.title}</h1>
      
      <Form method="post">
        <div>
          <label htmlFor="schema">JSON-LD Schema:</label>
          <textarea
            id="schema"
            name="schema"
            rows={20}
            cols={80}
            value={editorValue}
            onChange={handleChange}
          />
          {error && <p className="error">{error}</p>}
        </div>
        
        <div>
          <button type="submit" disabled={!!error}>Save Schema</button>
        </div>
      </Form>
      
      <div>
        <h2>Preview:</h2>
        {!error && (
          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{
              __html: editorValue
            }}
          />
        )}
        
        <p>
          <a
            href={`https://search.google.com/test/rich-results?url=${encodeURIComponent(
              `https://example.com/pages/${page.slug}`
            )}`}
            target="_blank"
            rel="noopener noreferrer"
          >
            Test with Google Rich Results Test
          </a>
        </p>
      </div>
    </div>
  );
}
```

---

By following these best practices, you can effectively implement structured data in your Remix application, improving SEO and search engine visibility while maintaining code quality and performance.
