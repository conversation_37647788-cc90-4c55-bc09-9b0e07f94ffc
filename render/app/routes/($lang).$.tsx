import type { LoaderFunction, MetaFunction } from "@remix-run/node";
import { json } from '@remix-run/node';
import { useLoaderData, useRouteError, isRouteErrorResponse } from "@remix-run/react";
import PageRender from '../components/pageRender';
import { ErrorBoundary as SharedErrorBoundary } from '../components/ErrorBoundary';

export const meta: MetaFunction = ({ data }: any) => {
  if (!data) {
    return [
      { title: "" },
      { name: "description", content: "" },
    ];
  }

  // 基本元数据
  const metaTags: any[] = [
    { title: data?.seo?.title || data?.title || ""},
    { name: "description", content: data?.seo?.description || data.description || "" },
  ];
  
  // 网站名称和品牌
  if (data.seo?.siteName) {
    metaTags.push({ name: "application-name", content: data.seo.siteName });
    metaTags.push({ property: "og:site_name", content: data.seo.siteName });
  }
  
  // 关键词
  if (data.seo?.keywords && data.seo.keywords.length > 0) {
    metaTags.push({ name: "keywords", content: data.seo.keywords.join(', ') });
  }

  // Open Graph 标签
  if (data.seo?.ogTitle) {
    metaTags.push({ property: "og:title", content: data.seo.ogTitle });
  }
  if (data.seo?.ogDescription) {
    metaTags.push({ property: "og:description", content: data.seo.ogDescription });
  }
  if (data.seo?.ogImage) {
    metaTags.push({ property: "og:image", content: data.seo.ogImage });
  }
  metaTags.push({ property: "og:type", content: "website" });
  
  // Twitter/X 标签（完整处理）
  if (data.seo?.xTitle) {
    metaTags.push({ name: "twitter:title", content: data.seo.xTitle });
  }
  if (data.seo?.xDescription) {
    metaTags.push({ name: "twitter:description", content: data.seo.xDescription });
  }
  if (data.seo?.xImage) {
    metaTags.push({ name: "twitter:image", content: data.seo.xImage });
  }
  if (data.seo?.xCardType) {
    metaTags.push({ name: "twitter:card", content: data.seo.xCardType.toLowerCase() });
  }
  if (data.seo?.xUsername) {
    metaTags.push({ name: "twitter:site", content: `@${data.seo.xUsername}` });
    metaTags.push({ name: "twitter:creator", content: `@${data.seo.xUsername}` });
  }
  
  // 处理特殊类型的 X Card 数据
  if (data.seo?.xCardData) {
    if (data.seo.xCardType === 'APP') {
      // APP 卡片特定元标签
      const appData = data.seo.xCardData;
      if (appData.app_id) {
        if (appData.app_id.iphone) metaTags.push({ name: "twitter:app:id:iphone", content: appData.app_id.iphone });
        if (appData.app_id.ipad) metaTags.push({ name: "twitter:app:id:ipad", content: appData.app_id.ipad });
        if (appData.app_id.googleplay) metaTags.push({ name: "twitter:app:id:googleplay", content: appData.app_id.googleplay });
      }
      if (appData.app_name) {
        if (appData.app_name.iphone) metaTags.push({ name: "twitter:app:name:iphone", content: appData.app_name.iphone });
        if (appData.app_name.ipad) metaTags.push({ name: "twitter:app:name:ipad", content: appData.app_name.ipad });
        if (appData.app_name.googleplay) metaTags.push({ name: "twitter:app:name:googleplay", content: appData.app_name.googleplay });
      }
      if (appData.app_url) {
        if (appData.app_url.iphone) metaTags.push({ name: "twitter:app:url:iphone", content: appData.app_url.iphone });
        if (appData.app_url.ipad) metaTags.push({ name: "twitter:app:url:ipad", content: appData.app_url.ipad });
        if (appData.app_url.googleplay) metaTags.push({ name: "twitter:app:url:googleplay", content: appData.app_url.googleplay });
      }
      if (appData.app_country) {
        metaTags.push({ name: "twitter:app:country", content: appData.app_country });
      }
    } else if (data.seo.xCardType === 'PLAYER') {
      // PLAYER 卡片特定元标签
      const playerData = data.seo.xCardData;
      if (playerData.player) metaTags.push({ name: "twitter:player", content: playerData.player });
      if (playerData.player_width) metaTags.push({ name: "twitter:player:width", content: playerData.player_width });
      if (playerData.player_height) metaTags.push({ name: "twitter:player:height", content: playerData.player_height });
      if (playerData.player_stream) metaTags.push({ name: "twitter:player:stream", content: playerData.player_stream });
    }
  }
  
  // Robots 指令
  if (data.seo?.robots) {
    const robotsContent = [];
    if (data.seo.robots.index === false) robotsContent.push('noindex');
    if (data.seo.robots.follow === false) robotsContent.push('nofollow');
    if (data.seo.robots.archive === false) robotsContent.push('noarchive');
    if (data.seo.robots.imageindex === false) robotsContent.push('noimageindex');
    if (data.seo.robots.snippet === false) robotsContent.push('nosnippet');
    if (data.seo.robots.translate === false) robotsContent.push('notranslate');
    if (robotsContent.length > 0) {
      metaTags.push({ name: "robots", content: robotsContent.join(', ') });
    }
  }

  // 规范链接和 hreflang
  if (data.seo) {
    if (data.seo.canonicalUrl) {
      metaTags.push({ rel: "canonical", href: data.seo.canonicalUrl });
    }
    if (data.seo.alternateUrls && Array.isArray(data.seo.alternateUrls)) {
      data.seo.alternateUrls.forEach((alternate: { language: string; url: string }) => {
        // 确保 hrefLang 属性使用小写
        metaTags.push({ 
          rel: "alternate", 
          hrefLang: alternate.language.toLowerCase(), 
          href: alternate.url 
        });
      });

      // 添加 x-default hreflang 标签（指向默认语言版本）
      const defaultUrl = data.seo.alternateUrls.find(
        (alt: { language: string; url: string }) => 
          alt.language.toUpperCase() === (data.languageInfo?.defaultLanguage || 'EN').toUpperCase()
      );
      
      if (defaultUrl) {
        metaTags.push({ rel: "alternate", hrefLang: "x-default", href: defaultUrl.url });
      }
    }
  }

  // 添加语言标记，确保使用小写
  metaTags.push({
    lang: (data.language || (data.languageInfo?.currentLanguage || 'en')).toLowerCase()
  });

  // 添加结构化数据
  if (data.seo) {
    // 1. 页面特定的结构化数据
    if (data.seo.schemaData && data.seo.schemaType) {
      let schemaContent = data.seo.schemaData;
      
      // 如果是对象而不是字符串，确保有 @context 和 @type
      if (typeof schemaContent === 'object') {
        if (!schemaContent['@context']) {
          schemaContent['@context'] = 'https://schema.org';
        }
        if (!schemaContent['@type'] && data.seo.schemaType) {
          schemaContent['@type'] = data.seo.schemaType;
        }
      }
      
      metaTags.push({
        "script:ld+json": schemaContent
      });
    }
    
    // 2. 组织结构化数据
    if (data.seo.organizationSchema) {
      metaTags.push({
        "script:ld+json": data.seo.organizationSchema
      });
    }
    
    // 3. 网站结构化数据
    if (data.seo.websiteSchema) {
      metaTags.push({
        "script:ld+json": data.seo.websiteSchema
      });
    }
  }

  return metaTags;
};

type PageConfig = {
  subdomain: string;
  domain: string;
  pageId: string;
  nanoid: string;
  // isPublished: boolean;
  title: string;
  header: any;
  configuration: any;
  footer: any;
  theme: string;
  language: string;
  languageInfo?: {
    currentLanguage: string;
    defaultLanguage: string;
    supportedLanguages: string[];
    translatedLanguages: string[];
    languageNames: Record<string, string>;
  };
  languageVersions?: Record<string, {
    id: string;
    nanoid: string;
    status: string;
  }>;
  languageUrls?: Record<string, string>;
  seo?: {
    canonicalUrl: string;
    alternateUrls: Array<{
      language: string;
      url: string;
    }>;
    title?: string;
    description?: string;
    siteName?: string;
    keywords?: string[];
    ogTitle?: string;
    ogDescription?: string;
    ogImage?: string;
    xTitle?: string;
    xDescription?: string;
    xImage?: string;
    xCardType?: string;
    xCardData?: any;
    xUsername?: string;
    robots?: {
      index?: boolean;
      follow?: boolean;
      archive?: boolean;
      imageindex?: boolean;
      snippet?: boolean;
      translate?: boolean;
    };
    schemaData?: any;
    schemaType?: string;
    organizationSchema?: any;
    websiteSchema?: any;
  };
};

export const supportedLanguages = new Set([
  'en', 'cn', 'zh', 'es', 'hi', 'fr', 'de', 'ru', 'pt', 'ar',
  'jp', 'kr', 'it', 'tr', 'pl', 'nl', 'id', 'th', 'vi', 'sv',
]);

export const loader: LoaderFunction = async ({ request, params }) => {
  let { lang, '*': pathSegments } = params;
  let slug = pathSegments || '/';
  console.log('[PageLoader] Initial params:', { lang, pathSegments, slug });

  // 如果 lang 无效，将 lang 设为 ''，并将 slug 设为原始 lang 的值
  if (lang && !supportedLanguages.has(lang)) {
    console.log(`[PageLoader] Invalid language "${lang}", treating it as part of slug`);
    slug = lang + (pathSegments ? `/${pathSegments}` : '');
    lang = '';
  }
  console.log('[PageLoader] Final language and path:', { lang, slug });

  // 确保 slug 总是以 / 开头
  if (slug && !slug.startsWith('/')) {
    slug = `/${slug}`;
  }
  
  // 从请求头中提取域名
  let domain: string = request.headers.get("host") ?? '';
  console.log('[PageLoader] Original domain from host:', domain);

  // Check if domain is an IP address
  const isIpAddress = /^(\d{1,3}\.){3}\d{1,3}(:\d+)?$/.test(domain);
  if (isIpAddress) {
    domain = process.env.DEFAULT_DOMAIN || 'lit.page';
    console.log('[PageLoader] IP address detected, using default domain:', domain);
    
    // 检查URL路径是否包含潜在的安全威胁
    const url = new URL(request.url);
    const path = url.pathname;
    
    if (path.includes('.env') || path.includes('/.') || path.includes('../') || 
        path.includes('/config') || path.includes('/database') || path.includes('/secrets') || 
        /\/(node_modules|core|src|functions|docker|etc|alpha|basic-network|configurations|extension|javascript|laravel|newsite|owncloud|packages|pemerintah|portal2|registration|site-library|spearmint|test|client|templates|saas|content|old|gitlab-ci|dev|blogs)\//.test(path)) {
      
      console.log('[PageLoader] Security threat detected in path:', path);
      throw json({
        errorCode: 'SECURITY_VIOLATION',
        message: 'Access denied'
      }, {
        status: 403,
        statusText: 'Forbidden'
      });
    }
  }

  if (process.env.NODE_ENV === 'development') {
    domain = process.env.DEV_DOMAIN as string;
    console.log('[PageLoader] Development mode, using DEV_DOMAIN:', domain);
  }

  const landingService = process.env.LANDING_SERVICE;
  if (!landingService) {
    console.error('[PageLoader] Error: LANDING_SERVICE environment variable is not defined');
    throw new Error("LANDING_SERVICE environment variable is not defined");
  }

  const url = new URL(landingService);
  url.searchParams.set('domain', domain);
  url.searchParams.set('language', lang || '');
  url.searchParams.set('slug', slug || '/');
  console.log('[PageLoader] API request details:', {
    url: url.toString(),
    params: {
      domain,
      language: lang || '',
      slug: slug || '/'
    }
  });

  const response = await fetch(url.toString(), {
    headers: {
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    const errorObject = await response.json();
    console.error('[PageLoader] API error response:', {
      status: response.status,
      statusText: response.statusText,
      error: errorObject
    });

    throw json({
      errorCode: errorObject.errorCode,
    },
    {
      status: errorObject.statusCode,
      statusText: errorObject.message,
    });
  }

  const res: any = await response.json();
  console.log('[PageLoader] API success response:', {
    pageId: res?.data?.pageId,
    title: res?.data?.title,
    language: lang,
    domain
  });
  
  return json({...res?.data, domain});
};

export default function Index() {
  const pageConfig = useLoaderData<PageConfig>();

  // 添加调试日志，查看多语言数据
  console.log('[PageRender] Received page config:', {
    language: pageConfig.language,
    hasLanguageInfo: !!pageConfig.languageInfo,
    hasLanguageVersions: !!pageConfig.languageVersions,
    hasLanguageUrls: !!pageConfig.languageUrls,
    hasSeo: !!pageConfig.seo,
  });

  return (
    <>
      <PageRender
        subdomain={pageConfig.subdomain}
        domain={pageConfig.domain}
        headers={pageConfig?.header}
        footers={pageConfig?.footer}
        schema={pageConfig?.configuration}
        language={pageConfig.language}
        languageInfo={pageConfig.languageInfo}
        languageVersions={pageConfig.languageVersions}
        languageUrls={pageConfig.languageUrls}
        seo={pageConfig.seo}
      ></PageRender>
      {/* <div>
        <pre>{JSON.stringify(pageConfig, null, 2)}</pre>
      </div> */}
     
    </>
  );
}

export function ErrorBoundary() {
  return <SharedErrorBoundary />;
}
