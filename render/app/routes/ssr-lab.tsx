import { json, type LoaderFunctionArgs, type MetaFunction, type LinksFunction } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { SSRLabLayout, SSRTestComponent } from "~/components/SSRLab";
import svgTestStyles from "~/styles/svg-test.css?url";
import responsiveIconsStyles from "~/responsive-icons.css?url";

export const links: LinksFunction = () => [
  { rel: "stylesheet", href: svgTestStyles },
  { rel: "stylesheet", href: responsiveIconsStyles },
];

export const meta: MetaFunction = () => {
  return [
    { title: "SSR Lab - Component Testing" },
    { name: "description", content: "A laboratory page for testing Server-Side Rendering components" },
  ];
};

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const startTime = Date.now();
  
  // 模拟服务器端数据获取
  const serverTime = new Date().toISOString();
  const serverData = {
    message: "This data was rendered on the server",
    timestamp: serverTime,
    environment: "server",
    serverProcessingTime: Date.now() - startTime,
  };

  return json({
    serverData,
    url: request.url,
  });
};

export default function SSRLab() {
  const { serverData } = useLoaderData<typeof loader>();

  return (
    <SSRLabLayout serverData={serverData}>
      <SSRTestComponent serverData={serverData} />
    </SSRLabLayout>
  );
} 