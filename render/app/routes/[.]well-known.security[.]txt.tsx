import type { LoaderFunction } from "@remix-run/node";

/**
 * 处理 /.well-known/security.txt 请求
 * 使用特殊的路径命名约定处理包含点和斜杠的路径
 */
export const loader: LoaderFunction = async ({ request }) => {
  console.log('[SecurityTxtLoader] Processing security.txt request');
  
  // 从请求头中提取域名
  let domain: string = request.headers.get("host") ?? '';
  console.log('[SecurityTxtLoader] Original domain from host:', domain);

  // 检查是否是 IP 地址
  const isIpAddress = /^(\d{1,3}\.){3}\d{1,3}(:\d+)?$/.test(domain);
  if (isIpAddress) {
    domain = process.env.DEFAULT_DOMAIN || 'lit.page';
    console.log('[SecurityTxtLoader] IP address detected, using default domain:', domain);
  }

  // 开发环境使用 DEV_DOMAIN
  if (process.env.NODE_ENV === 'development') {
    domain = process.env.DEV_DOMAIN as string;
    console.log('[SecurityTxtLoader] Development mode, using DEV_DOMAIN:', domain);
  }

  // 获取 SEO 服务地址
  const seoService = process.env.SEO_SERVICE || process.env.LANDING_SERVICE;
  if (!seoService) {
    console.error('[SecurityTxtLoader] Error: Neither SEO_SERVICE nor LANDING_SERVICE environment variable is defined');
    throw new Error("SEO_SERVICE or LANDING_SERVICE environment variable is not defined");
  }

  // 构建 API URL
  const url = new URL(`${seoService}/public/security.txt`);
  url.searchParams.set('domain', domain);
  
  console.log('[SecurityTxtLoader] API request details:', {
    url: url.toString(),
    params: { domain }
  });

  try {
    // 请求 API
    const response = await fetch(url.toString(), {
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      console.error('[SecurityTxtLoader] API error response:', {
        status: response.status,
        statusText: response.statusText
      });

      // 如果 API 请求失败，返回默认的 security.txt
      const expiryDate = new Date();
      expiryDate.setFullYear(expiryDate.getFullYear() + 1); // 一年后过期
      
      return new Response(
        `# Security Policy for ${domain} (API request failed)
Contact: mailto:security@${domain}
Expires: ${expiryDate.toISOString()}
Preferred-Languages: en, zh
Policy: https://${domain}/security-policy
Canonical: https://${domain}/.well-known/security.txt`,
        {
          status: 200,
          headers: {
            "Content-Type": "text/plain",
            "Cache-Control": "public, max-age=3600"
          }
        }
      );
    }

    // 返回 API 响应
    const securityTxt = await response.text();
    console.log('[SecurityTxtLoader] API success response received');
    
    return new Response(securityTxt, {
      status: 200,
      headers: {
        "Content-Type": "text/plain",
        "Cache-Control": "public, max-age=3600"
      }
    });
  } catch (error) {
    console.error('[SecurityTxtLoader] Error fetching security.txt:', error);
    
    // 发生错误时返回默认的 security.txt
    const expiryDate = new Date();
    expiryDate.setFullYear(expiryDate.getFullYear() + 1); // 一年后过期
    
    return new Response(
      `# Security Policy for ${domain} (Error occurred)
Contact: mailto:security@${domain}
Expires: ${expiryDate.toISOString()}
Preferred-Languages: en, zh
Policy: https://${domain}/security-policy
Canonical: https://${domain}/.well-known/security.txt`,
      {
        status: 200,
        headers: {
          "Content-Type": "text/plain",
          "Cache-Control": "public, max-age:3600"
        }
      }
    );
  }
};
