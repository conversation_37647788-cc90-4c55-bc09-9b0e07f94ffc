import type { LoaderFunction } from "@remix-run/node";

/**
 * 处理 robots.txt 请求
 * 使用 robots[.]txt.tsx 命名约定创建资源路由
 */
export const loader: LoaderFunction = async ({ request }) => {
  console.log('[RobotsTxtLoader] Processing robots.txt request');
  
  // 从请求头中提取域名
  let domain: string = request.headers.get("host") ?? '';
  console.log('[RobotsTxtLoader] Original domain from host:', domain);

  // 检查是否是 IP 地址
  const isIpAddress = /^(\d{1,3}\.){3}\d{1,3}(:\d+)?$/.test(domain);
  if (isIpAddress) {
    domain = process.env.DEFAULT_DOMAIN || 'lit.page';
    console.log('[RobotsTxtLoader] IP address detected, using default domain:', domain);
  }

  // 开发环境使用 DEV_DOMAIN
  if (process.env.NODE_ENV === 'development') {
    domain = process.env.DEV_DOMAIN as string;
    console.log('[RobotsTxtLoader] Development mode, using DEV_DOMAIN:', domain);
  }

  // 获取 SEO 服务地址
  const seoService = process.env.SEO_SERVICE || process.env.LANDING_SERVICE;
  if (!seoService) {
    console.error('[RobotsTxtLoader] Error: Neither SEO_SERVICE nor LANDING_SERVICE environment variable is defined');
    throw new Error("SEO_SERVICE or LANDING_SERVICE environment variable is not defined");
  }

  // 构建 API URL
  const url = new URL(`${seoService}/public/robots.txt`);
  url.searchParams.set('domain', domain);
  
  console.log('[RobotsTxtLoader] API request details:', {
    url: url.toString(),
    params: { domain }
  });

  try {
    // 请求 API
    const response = await fetch(url.toString(), {
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      console.error('[RobotsTxtLoader] API error response:', {
        status: response.status,
        statusText: response.statusText
      });

      // 如果 API 请求失败，返回默认的 robots.txt
      return new Response(
        `# Default robots.txt (API request failed)\nUser-agent: *\nAllow: /\nSitemap: https://${domain}/sitemap.xml\n`,
        {
          status: 200,
          headers: {
            "Content-Type": "text/plain",
            "Cache-Control": "public, max-age=3600"
          }
        }
      );
    }

    // 返回 API 响应
    const robotsTxt = await response.text();
    console.log('[RobotsTxtLoader] API success response received');
    
    return new Response(robotsTxt, {
      status: 200,
      headers: {
        "Content-Type": "text/plain",
        "Cache-Control": "public, max-age=3600"
      }
    });
  } catch (error) {
    console.error('[RobotsTxtLoader] Error fetching robots.txt:', error);
    
    // 发生错误时返回默认的 robots.txt
    return new Response(
      `# Default robots.txt (Error occurred)\nUser-agent: *\nAllow: /\nSitemap: https://${domain}/sitemap.xml\n`,
      {
        status: 200,
        headers: {
          "Content-Type": "text/plain",
          "Cache-Control": "public, max-age=3600"
        }
      }
    );
  }
};
