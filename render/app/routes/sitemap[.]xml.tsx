import type { LoaderFunction } from "@remix-run/node";

/**
 * 处理 sitemap.xml 请求
 * 使用 sitemap[.]xml.tsx 命名约定创建资源路由
 */
export const loader: LoaderFunction = async ({ request }) => {
  console.log('[SitemapXmlLoader] Processing sitemap.xml request');
  
  // 从请求头中提取域名
  let domain: string = request.headers.get("host") ?? '';
  console.log('[SitemapXmlLoader] Original domain from host:', domain);

  // 检查是否是 IP 地址
  const isIpAddress = /^(\d{1,3}\.){3}\d{1,3}(:\d+)?$/.test(domain);
  if (isIpAddress) {
    domain = process.env.DEFAULT_DOMAIN || 'lit.page';
    console.log('[SitemapXmlLoader] IP address detected, using default domain:', domain);
  }

  // 开发环境使用 DEV_DOMAIN
  if (process.env.NODE_ENV === 'development') {
    domain = process.env.DEV_DOMAIN as string;
    console.log('[SitemapXmlLoader] Development mode, using DEV_DOMAIN:', domain);
  }

  // 获取 SEO 服务地址
  const seoService = process.env.SEO_SERVICE || process.env.LANDING_SERVICE;
  if (!seoService) {
    console.error('[SitemapXmlLoader] Error: Neither SEO_SERVICE nor LANDING_SERVICE environment variable is defined');
    throw new Error("SEO_SERVICE or LANDING_SERVICE environment variable is not defined");
  }

  // 构建 API URL
  const url = new URL(`${seoService}/public/sitemap.xml`);
  url.searchParams.set('domain', domain);
  
  console.log('[SitemapXmlLoader] API request details:', {
    url: url.toString(),
    params: { domain }
  });

  try {
    // 请求 API
    const response = await fetch(url.toString(), {
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      console.error('[SitemapXmlLoader] API error response:', {
        status: response.status,
        statusText: response.statusText
      });

      // 如果 API 请求失败，返回默认的 sitemap.xml
      return new Response(
        `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>https://${domain}/</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>1.0</priority>
  </url>
</urlset>`,
        {
          status: 200,
          headers: {
            "Content-Type": "application/xml",
            "Cache-Control": "public, max-age=3600"
          }
        }
      );
    }

    // 返回 API 响应
    const sitemapXml = await response.text();
    console.log('[SitemapXmlLoader] API success response received');
    
    return new Response(sitemapXml, {
      status: 200,
      headers: {
        "Content-Type": "application/xml",
        "Cache-Control": "public, max-age=3600"
      }
    });
  } catch (error) {
    console.error('[SitemapXmlLoader] Error fetching sitemap.xml:', error);
    
    // 发生错误时返回默认的 sitemap.xml
    return new Response(
      `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>https://${domain}/</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>1.0</priority>
  </url>
</urlset>`,
      {
        status: 200,
        headers: {
          "Content-Type": "application/xml",
          "Cache-Control": "public, max-age=3600"
        }
      }
    );
  }
};
