# SSRLab 组件架构

按照单一职责原则，将 SSR Lab 页面拆分为以下模块：

## 📁 文件结构

```
render/app/components/SSRLab/
├── index.ts                 # 入口文件，统一导出
├── hooks.ts                 # 自定义 hooks
├── MonitoringCards.tsx      # 监控卡片组件
├── TestComponents.tsx       # 测试相关组件
├── Layout.tsx              # 布局组件
└── README.md               # 文档说明
```

## 🔧 模块职责

### hooks.ts
**职责**: 提供可复用的自定义 hooks
- `useIsClient()` - 安全的客户端检测
- `usePerformanceMetrics()` - 性能指标监控
- `useNetworkInfo()` - 网络信息获取
- `useSystemInfo()` - 系统信息获取

### MonitoringCards.tsx
**职责**: 顶部监控卡片组件
- `PerformanceMetrics` - 性能监控卡片
- `NetworkInfo` - 网络信息卡片
- `ServerMetrics` - 服务器指标卡片
- `SystemInfo` - 系统信息卡片

### TestComponents.tsx
**职责**: 核心测试功能组件
- `InteractiveComponent` - 交互性测试组件
- `SSRTestComponent` - 主要 SSR 测试组件

### Layout.tsx
**职责**: 页面布局和结构组件
- `Header` - 页面头部
- `MonitoringSection` - 监控区域
- `SSRChecker` - SSR 状态检查器
- `Footer` - 页面底部
- `MainContent` - 主内容区域
- `SSRLabLayout` - 主布局容器

### index.ts
**职责**: 统一导出接口
- 导出所有组件和 hooks
- 提供清晰的导入路径

## 🎯 设计原则

### 单一职责原则 (SRP)
每个文件和组件都有明确的单一职责：
- hooks 只负责状态逻辑
- 监控卡片只负责数据展示
- 测试组件只负责测试功能
- 布局组件只负责页面结构

### 关注点分离
- **数据逻辑**: hooks.ts
- **UI 展示**: 各个组件文件
- **布局结构**: Layout.tsx
- **业务逻辑**: TestComponents.tsx

### 可复用性
- hooks 可以在其他组件中复用
- 监控卡片可以独立使用
- 布局组件支持灵活组合

## 🔗 依赖关系

```
hooks.ts (无依赖)
    ↓
MonitoringCards.tsx (依赖 hooks)
    ↓
TestComponents.tsx (依赖 hooks)
    ↓
Layout.tsx (依赖 hooks + MonitoringCards)
    ↓
index.ts (导出所有模块)
```

## 📖 使用示例

```tsx
// 在路由文件中使用
import { SSRLabLayout, SSRTestComponent } from "~/components/SSRLab";

export default function SSRLab() {
  const { serverData } = useLoaderData<typeof loader>();

  return (
    <SSRLabLayout serverData={serverData}>
      <SSRTestComponent serverData={serverData} />
    </SSRLabLayout>
  );
}

// 单独使用监控组件
import { PerformanceMetrics, usePerformanceMetrics } from "~/components/SSRLab";

// 单独使用 hooks
import { useIsClient, useNetworkInfo } from "~/components/SSRLab";
```

## ✅ 优势

1. **易维护**: 每个组件职责清晰，修改影响范围小
2. **可测试**: 独立的组件和 hooks 易于单元测试
3. **可复用**: hooks 和组件可以在其他地方复用
4. **易扩展**: 新增功能只需要添加对应的组件或 hook
5. **易理解**: 清晰的文件结构和命名约定 