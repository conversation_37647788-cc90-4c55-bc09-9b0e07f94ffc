import { useIsClient, usePerformanceMetrics, useNetworkInfo, useSystemInfo } from "./hooks";

// 性能检测组件 - 紧凑版本
export function PerformanceMetrics() {
  const metrics = usePerformanceMetrics();
  const isClient = useIsClient();

  return (
    <div className="bg-orange-50 dark:bg-orange-900/20 p-3 rounded-lg border border-orange-200 dark:border-orange-800">
      <h4 className="text-sm font-semibold mb-2 text-orange-900 dark:text-orange-100 flex items-center">
        ⚡ Performance
      </h4>
      {!isClient ? (
        <p className="text-xs text-gray-600 dark:text-gray-400">Loading...</p>
      ) : (
        <div className="space-y-1 text-xs">
          <p className="text-gray-700 dark:text-gray-300">
            <span className="font-medium">Server:</span> {metrics.serverRenderTime ? `${metrics.serverRenderTime}ms` : "N/A"}
          </p>
          <p className="text-gray-700 dark:text-gray-300">
            <span className="font-medium">Hydration:</span> {metrics.hydrationTime ? `${metrics.hydrationTime}ms` : "N/A"}
          </p>
          <p className="text-gray-700 dark:text-gray-300">
            <span className="font-medium">FCP:</span> {metrics.firstContentfulPaint ? `${metrics.firstContentfulPaint}ms` : "N/A"}
          </p>
        </div>
      )}
    </div>
  );
}

// 网络信息组件 - 紧凑版本
export function NetworkInfo() {
  const networkInfo = useNetworkInfo();
  const isClient = useIsClient();

  return (
    <div className="bg-cyan-50 dark:bg-cyan-900/20 p-3 rounded-lg border border-cyan-200 dark:border-cyan-800">
      <h4 className="text-sm font-semibold mb-2 text-cyan-900 dark:text-cyan-100 flex items-center">
        🌐 Network
      </h4>
      {!isClient ? (
        <p className="text-xs text-gray-600 dark:text-gray-400">Loading...</p>
      ) : (
        <div className="space-y-1 text-xs">
          <p className="text-gray-700 dark:text-gray-300">
            <span className="font-medium">Type:</span> {networkInfo?.effectiveType || 'N/A'}
          </p>
          <p className="text-gray-700 dark:text-gray-300">
            <span className="font-medium">Speed:</span> {networkInfo?.downlink ? `${networkInfo.downlink}Mbps` : 'N/A'}
          </p>
          <p className="text-gray-700 dark:text-gray-300">
            <span className="font-medium">RTT:</span> {networkInfo?.rtt ? `${networkInfo.rtt}ms` : 'N/A'}
          </p>
        </div>
      )}
    </div>
  );
}

// 服务器指标组件
export function ServerMetrics({ serverData }: { serverData: any }) {
  return (
    <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg border border-blue-200 dark:border-blue-800">
      <h4 className="text-sm font-semibold mb-2 text-blue-900 dark:text-blue-100 flex items-center">
        🖥️ Server
      </h4>
      <div className="space-y-1 text-xs">
        <p className="text-gray-700 dark:text-gray-300">
          <span className="font-medium">Processing:</span> {serverData.serverProcessingTime}ms
        </p>
        <p className="text-gray-700 dark:text-gray-300">
          <span className="font-medium">Environment:</span> {serverData.environment}
        </p>
        <p className="text-gray-700 dark:text-gray-300">
          <span className="font-medium">Time:</span> {new Date(serverData.timestamp).toLocaleTimeString()}
        </p>
      </div>
    </div>
  );
}

// 系统信息组件
export function SystemInfo() {
  const systemInfo = useSystemInfo();
  const isClient = useIsClient();

  return (
    <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-lg border border-green-200 dark:border-green-800">
      <h4 className="text-sm font-semibold mb-2 text-green-900 dark:text-green-100 flex items-center">
        💻 System
      </h4>
      {!isClient ? (
        <p className="text-xs text-gray-600 dark:text-gray-400">Loading...</p>
      ) : (
        <div className="space-y-1 text-xs">
          <p className="text-gray-700 dark:text-gray-300">
            <span className="font-medium">Platform:</span> {systemInfo.platform}
          </p>
          <p className="text-gray-700 dark:text-gray-300">
            <span className="font-medium">Language:</span> {systemInfo.language}
          </p>
          <p className="text-gray-700 dark:text-gray-300">
            <span className="font-medium">Cookies:</span> {systemInfo.cookieEnabled ? '✅' : '❌'}
          </p>
        </div>
      )}
    </div>
  );
} 