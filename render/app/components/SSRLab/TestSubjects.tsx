import { useState } from "react";

// CSS 变量控制 SVG 尺寸的被测试组件
export function SVGSizeTestSubject() {
  const [containerSize, setContainerSize] = useState<'small' | 'medium' | 'large' | 'xl'>('xl');

  // 定义不同尺寸的 CSS 类名
  const sizeClasses = {
    small: 'svg-container-small',
    medium: 'svg-container-medium', 
    large: 'svg-container-large',
    xl: 'svg-container-xl'
  };

  // 测试用的 SVG 图标组件
  const TestSVGIcon = () => (
    <svg 
      className="test-svg-icon" 
      viewBox="0 0 24 24" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
    >
      <path 
        d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" 
        fill="currentColor"
      />
      <circle cx="12" cy="12" r="3" fill="none" stroke="currentColor" strokeWidth="2"/>
      <path 
        d="M12 1V23M1 12H23" 
        stroke="currentColor" 
        strokeWidth="1" 
        opacity="0.3"
      />
    </svg>
  );

  return (
    <div className="w-full max-w-4xl bg-white dark:bg-gray-800 rounded-xl shadow-xl p-8">
      {/* 组件标题 */}
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          CSS Variables SVG Size Control Test
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Testing dynamic SVG sizing through CSS variables and container className changes
        </p>
      </div>

      {/* 控制面板 */}
      <div className="mb-8 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
        <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
          🎛️ Size Controls
        </h3>
        <div className="flex flex-wrap gap-3">
          {Object.entries(sizeClasses).map(([size, className]) => (
            <button
              key={size}
              onClick={() => setContainerSize(size as any)}
              className={`px-4 py-2 rounded-md font-medium transition-all ${
                containerSize === size
                  ? 'bg-blue-600 text-white shadow-lg'
                  : 'bg-white dark:bg-gray-600 text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-500'
              }`}
            >
              {size.toUpperCase()} 
              <span className="text-xs opacity-75 ml-1">
                ({size === 'small' ? '24px' : 
                  size === 'medium' ? '48px' : 
                  size === 'large' ? '72px' : '96px'})
              </span>
            </button>
          ))}
        </div>
      </div>

      {/* SVG 测试区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* 被测试组件展示 */}
        <div className="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg border border-blue-200 dark:border-blue-800">
          <h3 className="text-xl font-semibold mb-4 text-blue-900 dark:text-blue-100">
            🎯 Test Subject
          </h3>
          
          {/* 这里是核心测试区域 - 容器的 className 控制内部 SVG 尺寸 */}
          <div className={`svg-test-container ${sizeClasses[containerSize]} flex items-center justify-center p-8 bg-white dark:bg-gray-800 rounded-lg border-2 border-dashed border-blue-300 dark:border-blue-600`}>
            <TestSVGIcon />
          </div>
          
          <div className="mt-4 text-sm text-gray-600 dark:text-gray-400">
            <p><strong>Current Container Class:</strong> <code className="bg-gray-200 dark:bg-gray-700 px-1 rounded">{sizeClasses[containerSize]}</code></p>
            <p><strong>SVG Class:</strong> <code className="bg-gray-200 dark:bg-gray-700 px-1 rounded">test-svg-icon</code></p>
          </div>
        </div>

        {/* 技术说明 */}
        <div className="bg-green-50 dark:bg-green-900/20 p-6 rounded-lg border border-green-200 dark:border-green-800">
          <h3 className="text-xl font-semibold mb-4 text-green-900 dark:text-green-100">
            🔧 Technical Details
          </h3>
          
          <div className="space-y-4 text-sm">
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">CSS Variables Used:</h4>
              <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                <li>• <code className="bg-gray-200 dark:bg-gray-700 px-1 rounded">--svg-size</code> - Controls width and height</li>
                <li>• <code className="bg-gray-200 dark:bg-gray-700 px-1 rounded">--svg-color</code> - Controls fill color</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">Current Values:</h4>
              <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                <li>• Size: <span className="font-mono">{
                  containerSize === 'small' ? '24px' : 
                  containerSize === 'medium' ? '48px' : 
                  containerSize === 'large' ? '72px' : '96px'
                }</span></li>
                <li>• Color: <span className="font-mono">currentColor</span></li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">How it works:</h4>
              <ol className="space-y-1 text-gray-600 dark:text-gray-400 list-decimal list-inside">
                <li>Container gets className: <code>{sizeClasses[containerSize]}</code></li>
                <li>CSS sets <code>--svg-size</code> variable</li>
                <li>SVG uses variable for dimensions</li>
                <li>Size changes instantly on className update</li>
              </ol>
            </div>
          </div>
        </div>
      </div>

      {/* 多个 SVG 测试实例 */}
      <div className="mt-8 bg-purple-50 dark:bg-purple-900/20 p-6 rounded-lg border border-purple-200 dark:border-purple-800">
        <h3 className="text-xl font-semibold mb-4 text-purple-900 dark:text-purple-100">
          📋 Multiple Instances Test
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
          All SVG instances inherit the same size from the container's CSS variables:
        </p>
        
        <div className={`svg-test-container ${sizeClasses[containerSize]} grid grid-cols-4 gap-4 p-4 bg-white dark:bg-gray-800 rounded-lg border border-purple-300 dark:border-purple-600`}>
          <div className="flex items-center justify-center text-red-500">
            <TestSVGIcon />
          </div>
          <div className="flex items-center justify-center text-green-500">
            <TestSVGIcon />
          </div>
          <div className="flex items-center justify-center text-blue-500">
            <TestSVGIcon />
          </div>
          <div className="flex items-center justify-center text-purple-500">
            <TestSVGIcon />
          </div>
        </div>
      </div>
      
      {/* CSS 代码展示 */}
      <div className="mt-8 bg-gray-50 dark:bg-gray-700 p-6 rounded-lg">
        <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
          📝 CSS Implementation
        </h3>
        <pre className="text-xs bg-gray-800 text-green-400 p-4 rounded overflow-x-auto">
{`/* CSS Variables for SVG Size Control */
.svg-container-small {
  --svg-size: 24px;
  --svg-color: currentColor;
}

.svg-container-medium {
  --svg-size: 48px;
  --svg-color: currentColor;
}

.svg-container-large {
  --svg-size: 72px;
  --svg-color: currentColor;
}

.svg-container-xl {
  --svg-size: 96px;
  --svg-color: currentColor;
}

/* SVG uses the CSS variables */
.test-svg-icon {
  width: var(--svg-size);
  height: var(--svg-size);
  color: var(--svg-color);
  transition: all 0.3s ease;
}`}
        </pre>
      </div>
    </div>
  );
}