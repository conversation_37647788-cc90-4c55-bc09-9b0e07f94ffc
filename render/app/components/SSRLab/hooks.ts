import { useState, useEffect } from "react";

// 安全的客户端检测 hook
export function useIsClient() {
  const [isClient, setIsClient] = useState(false);
  
  useEffect(() => {
    setIsClient(true);
  }, []);
  
  return isClient;
}

// 性能测量 hook
export function usePerformanceMetrics() {
  const [metrics, setMetrics] = useState({
    serverRenderTime: null as number | null,
    hydrationTime: null as number | null,
    firstContentfulPaint: null as number | null,
    domInteractive: null as number | null,
    domComplete: null as number | null,
    loadComplete: null as number | null,
  });

  useEffect(() => {
    const measurePerformance = () => {
      if (typeof window !== 'undefined' && window.performance) {
        const navigation = window.performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        const paint = window.performance.getEntriesByType('paint');
        
        const fcpEntry = paint.find(entry => entry.name === 'first-contentful-paint');
        
        setMetrics({
          serverRenderTime: navigation ? Math.round(navigation.responseEnd - navigation.requestStart) : null,
          hydrationTime: navigation ? Math.round(navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart) : null,
          firstContentfulPaint: fcpEntry ? Math.round(fcpEntry.startTime) : null,
          domInteractive: navigation ? Math.round(navigation.domInteractive - navigation.fetchStart) : null,
          domComplete: navigation ? Math.round(navigation.domComplete - navigation.fetchStart) : null,
          loadComplete: navigation ? Math.round(navigation.loadEventEnd - navigation.fetchStart) : null,
        });
      }
    };

    // 延迟测量以确保所有指标都可用
    const timer = setTimeout(measurePerformance, 1000);
    
    return () => clearTimeout(timer);
  }, []);

  return metrics;
}

// 网络信息 hook
export function useNetworkInfo() {
  const [networkInfo, setNetworkInfo] = useState<any>(null);
  
  useEffect(() => {
    if (typeof window !== 'undefined' && 'connection' in navigator) {
      const connection = (navigator as any).connection;
      setNetworkInfo({
        effectiveType: connection?.effectiveType || 'unknown',
        downlink: connection?.downlink || 'unknown',
        rtt: connection?.rtt || 'unknown',
        saveData: connection?.saveData || false,
      });
    }
  }, []);

  return networkInfo;
}

// 系统信息 hook
export function useSystemInfo() {
  const [systemInfo, setSystemInfo] = useState({
    userAgent: '',
    platform: '',
    language: '',
    cookieEnabled: false,
  });

  useEffect(() => {
    if (typeof window !== 'undefined') {
      setSystemInfo({
        userAgent: navigator.userAgent.split(' ')[0] || 'Unknown',
        platform: navigator.platform || 'Unknown',
        language: navigator.language || 'Unknown',
        cookieEnabled: navigator.cookieEnabled,
      });
    }
  }, []);

  return systemInfo;
} 