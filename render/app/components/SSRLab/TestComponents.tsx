import { useState, useEffect } from "react";
import { useNavigation } from "@remix-run/react";
import { useIsClient } from "./hooks";
import { SVGSizeTestSubject } from "./TestSubjects";
import { ThemedIconV2Demo } from '@litpage/sections/src/components/ThemedIconV2';
import { Picture, PictureProvider, type Format } from '@litpage/sections/src/components/Picture';

// 交互组件测试
export function InteractiveComponent() {
  const [count, setCount] = useState(0);
  const [message, setMessage] = useState("");

  return (
    <div className="space-y-4">
      <div className="flex gap-4 items-center flex-wrap">
        <button 
          onClick={() => setCount(c => c + 1)}
          className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500"
        >
          Count: {count}
        </button>
        <input
          type="text"
          placeholder="Type something..."
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-purple-500 min-w-64"
        />
      </div>
      <p className="text-sm text-gray-600 dark:text-gray-400 min-h-[1.25rem]">
        {message ? `You typed: "${message}"` : "Start typing to test client-side interactivity"}
      </p>
    </div>
  );
}

// SSR 验证信息组件
export function SSRVerificationInfo({ serverData }: { serverData: any }) {
  const [clientTime, setClientTime] = useState<string>("");
  const [renderStartTime] = useState(() => Date.now());
  const [renderTime, setRenderTime] = useState<number | null>(null);
  const isClient = useIsClient();
  const navigation = useNavigation();

  useEffect(() => {
    setClientTime(new Date().toISOString());
    setRenderTime(Date.now() - renderStartTime);
  }, [renderStartTime]);

  return (
    <div className="mb-6 bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
      <h3 className="text-lg font-semibold mb-3 text-blue-900 dark:text-blue-100">
        🔍 SSR Verification Status
      </h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
        <div>
          <p className="text-gray-700 dark:text-gray-300">
            <span className="font-medium">Server Rendered:</span> ✅ {serverData.timestamp}
          </p>
          <p className="text-gray-700 dark:text-gray-300">
            <span className="font-medium">Server Processing:</span> {serverData.serverProcessingTime}ms
          </p>
        </div>
        <div>
          <p className="text-gray-700 dark:text-gray-300">
            <span className="font-medium">Client Hydrated:</span> {isClient ? "✅ Yes" : "❌ No"}
          </p>
          <p className="text-gray-700 dark:text-gray-300">
            <span className="font-medium">Client Render:</span> {renderTime ? `${renderTime}ms` : "⏳ Calculating..."}
          </p>
        </div>
      </div>
    </div>
  );
}

// Picture SSR 测试组件
export function PictureSSRTest() {
  const isClient = useIsClient();
  
  // Picture组件配置
  const pictureConfig = {
    baseURL: 'http://imgpipe-test.imgpipe.net',
    defaultFormats: ['avif', 'webp', 'jpeg'] as Format[],
    defaultQuality: 75,
    sign: (url: string) => `${url}&token=ssr-lab-test`
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl p-6">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          🖼️ Picture 智能图片组件 SSR 测试
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          验证Picture组件的SSR兼容性：固定HTML结构 + CSS响应式 + 无hydration问题
        </p>
      </div>

      {/* SSR 特性说明 */}
      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 mb-6">
        <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-3">
          ⚡ Picture SSR 架构优势
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
              <span className="text-blue-800 dark:text-blue-200">服务端生成完整 &lt;picture&gt; 结构</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
              <span className="text-blue-800 dark:text-blue-200">CSS控制视觉效果(aspect-ratio, object-fit)</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
              <span className="text-blue-800 dark:text-blue-200">自动格式协商(AVIF→WebP→JPEG)</span>
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
              <span className="text-blue-800 dark:text-blue-200">响应式srcset(基于视口和DPR)</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
              <span className="text-blue-800 dark:text-blue-200">预加载和懒加载支持</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
              <span className="text-blue-800 dark:text-blue-200">CLS防护 + 平滑加载动画</span>
            </div>
          </div>
        </div>
      </div>

      {/* 执行环境状态 */}
      <div className="mb-6 p-3 bg-gray-50 dark:bg-gray-900 rounded-lg">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600 dark:text-gray-400">当前执行环境:</span>
          <span className={`font-medium ${isClient ? 'text-green-600' : 'text-blue-600'}`}>
            {isClient ? '🌐 Client (已Hydration)' : '🖥️ Server (SSR中)'}
          </span>
        </div>
      </div>

      <PictureProvider config={pictureConfig}>
        <div className="space-y-8">
          {/* 1. 流体宽度响应式测试 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              1️⃣ 流体宽度响应式横幅
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              测试流体宽度布局，自动生成多尺寸srcset，aspect-ratio确保布局稳定
            </p>
            <Picture
              src="/images_c2ik8Zsr9X5-dnSnK0MWq2gf.jpg"
              alt="SSR Lab 测试图片 - 流体宽度"
              widths={[640, 1024, 1600]}
              sizes="100vw"
              aspectRatio="16/9"
              priority
              className="rounded-lg shadow-lg"
            />
          </div>

          {/* 2. 固定尺寸头像测试 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              2️⃣ 固定尺寸头像组件
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              测试固定宽高，多密度支持(1x, 2x, 3x)，圆形裁剪
            </p>
            <div className="flex items-center gap-4">
              <Picture
                src="/images_c2ik8Zsr9X5-dnSnK0MWq2gf.jpg"
                alt="头像 64x64"
                width={64}
                height={64}
                densities={[1, 2, 3]}
                fit="cover"
                className="rounded-full border-2 border-gray-300 dark:border-gray-600"
              />
              <Picture
                src="/images_c2ik8Zsr9X5-dnSnK0MWq2gf.jpg"
                alt="头像 96x96"
                width={96}
                height={96}
                densities={[1, 2, 3]}
                fit="cover"
                className="rounded-full border-2 border-gray-300 dark:border-gray-600"
              />
              <Picture
                src="/images_c2ik8Zsr9X5-dnSnK0MWq2gf.jpg"
                alt="头像 128x128"
                width={128}
                height={128}
                densities={[1, 2, 3]}
                fit="cover"
                className="rounded-full border-2 border-gray-300 dark:border-gray-600"
              />
            </div>
          </div>

          {/* 3. 🔧 混合模式测试 - 响应式 + h-full填充 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              3️⃣ 🔧 混合模式测试 - 响应式 + h-full填充
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              测试Picture组件的新混合模式，解决响应式图片无法h-full填充的架构问题
            </p>
            
            {/* SSR中的对比测试 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 旧方法（问题演示） */}
              <div className="space-y-2">
                <h4 className="text-md font-medium text-red-600">问题演示：旧方法</h4>
                <div className="aspect-[3/2] bg-gray-200 dark:bg-gray-700 border-2 border-red-500 rounded">
                  <Picture
                    src="/images_c2ik8Zsr9X5-dnSnK0MWq2gf.jpg"
                    alt="旧方法测试 - SSR"
                    widths={[300, 480, 640]}
                    sizes="(max-width: 480px) 300px, (max-width: 640px) 480px, 640px"
                    className="w-full h-full border-2 border-blue-500"
                  />
                </div>
                <p className="text-xs text-red-600">❌ h-full被忽略，SSR中图片高度为auto</p>
              </div>
              
              {/* 新方法（已修复） */}
              <div className="space-y-2">
                <h4 className="text-md font-medium text-green-600">解决方案：混合模式</h4>
                <div className="aspect-[3/2] bg-gray-200 dark:bg-gray-700 border-2 border-red-500 rounded">
                  <Picture
                    src="/images_c2ik8Zsr9X5-dnSnK0MWq2gf.jpg"
                    alt="混合模式测试 - SSR"
                    widths={[300, 480, 640]}
                    sizes="(max-width: 480px) 300px, (max-width: 640px) 480px, 640px"
                    forceFillHeight={true}  // 🔧 关键：启用混合模式
                    className="w-full h-full border-2 border-blue-500"
                    style={{ '--picture-fill-object-fit': 'cover' } as React.CSSProperties}
                  />
                </div>
                <p className="text-xs text-green-600">✅ SSR安全的混合模式：响应式 + h-full</p>
              </div>
            </div>
            
            {/* 实际场景：模拟AngledImage布局 */}
            <div className="space-y-2">
              <h4 className="text-md font-medium text-blue-600">实际场景：HeroImage布局</h4>
              <div className="lg:flex lg:items-stretch border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
                {/* 左侧内容 */}
                <div className="lg:w-[45%] p-6 bg-gray-50 dark:bg-gray-800">
                  <h5 className="text-lg font-semibold mb-2">Hero Section Content</h5>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                    这是一个典型的Hero Section布局，左侧是内容，右侧是图片。
                    在桌面版下，我们需要图片填满容器高度以与左侧内容对齐。
                  </p>
                  <div className="space-y-2 text-xs">
                    <div className="flex items-center gap-2">
                      <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                      <span>SSR渲染：{isClient ? "已完成" : "进行中"}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                      <span>Hydration：{isClient ? "已完成" : "等待中"}</span>
                    </div>
                  </div>
                </div>
                
                {/* 右侧图片区域 */}
                <div className="lg:w-[55%] bg-gray-200 dark:bg-gray-600">
                  <div className="aspect-[3/2] w-full overflow-hidden lg:h-full lg:aspect-auto border-2 border-red-500">
                    <Picture
                      src="/images_c2ik8Zsr9X5-dnSnK0MWq2gf.jpg"
                      alt="Hero图片 - SSR兼容混合模式"
                      widths={[375, 480, 640, 768, 1024]}
                      sizes="(max-width: 480px) 375px, (max-width: 640px) 480px, (max-width: 768px) 640px, (max-width: 1024px) 768px, 1024px"
                      forceFillHeight={true}
                      className="w-full h-full border-2 border-blue-500"
                      style={{ 
                        display: 'block',
                        '--picture-fill-object-fit': 'cover' 
                      } as React.CSSProperties}
                    />
                  </div>
                </div>
              </div>
              <p className="text-xs text-blue-600">
                🎯 此布局在SSR中完全兼容，图片在服务端即确定最终尺寸，无hydration闪烁
              </p>
            </div>
          </div>

          {/* 4. 圆角容器填充测试 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              4️⃣ 🎯 圆角容器填充测试 - Rounded场景
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              测试Picture组件在圆角装饰容器中的填充问题，验证SSR中的双重比例控制冲突解决方案
            </p>
            
            {/* SSR中的圆角容器对比测试 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 问题演示 */}
              <div className="space-y-2">
                <h4 className="text-md font-medium text-red-600">❌ 有问题的方式</h4>
                <div className="rounded-xl overflow-hidden aspect-[9/7] bg-gray-100 dark:bg-gray-800 border-2 border-red-500">
                  <Picture
                    src="/images_c2ik8Zsr9X5-dnSnK0MWq2gf.jpg"
                    alt="圆角容器问题演示 - SSR"
                    widths={[180, 360, 540]}
                    aspectRatio="9/7"  // 与外层容器比例冲突
                    className="w-full h-full object-cover border-2 border-blue-500"
                  />
                </div>
                <p className="text-xs text-red-600">底部留白，圆角不完整，SSR中渲染异常</p>
              </div>

              {/* 正确示例 */}
              <div className="space-y-2">
                <h4 className="text-md font-medium text-green-600">✅ 正确的方式</h4>
                <div className="rounded-xl overflow-hidden aspect-[9/7] bg-gray-100 dark:bg-gray-800 border-2 border-red-500">
                  <Picture
                    src="/images_c2ik8Zsr9X5-dnSnK0MWq2gf.jpg"
                    alt="圆角容器正确填充 - SSR"
                    widths={[180, 360, 540]}
                    forceFillHeight={true}  // 🔧 混合模式
                    className="w-full h-full object-cover border-2 border-blue-500"
                    style={{ '--picture-fill-object-fit': 'cover' } as React.CSSProperties}
                  />
                </div>
                <p className="text-xs text-green-600">完美填充圆角容器，SSR渲染一致</p>
              </div>
            </div>
            
            {/* 技术要点说明 */}
            <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
              <h5 className="text-sm font-semibold text-blue-900 dark:text-blue-100 mb-2">
                🔧 SSR中的关键技术要点
              </h5>
              <div className="text-xs text-blue-800 dark:text-blue-200 space-y-1">
                <p>• <strong>移除aspectRatio冲突</strong>：避免Picture内部比例覆盖外层容器约束</p>
                <p>• <strong>启用forceFillHeight</strong>：混合模式在SSR中生成正确的CSS类名</p>
                <p>• <strong>设置display: block</strong>：消除inline-block的baseline底部空隙</p>
                <p>• <strong>CSS变量兼容</strong>：--picture-fill-object-fit在服务端安全设置</p>
                <p>• <strong>container-first原则</strong>：让外层容器完全控制最终显示比例</p>
              </div>
            </div>
            
            {/* 多种装饰容器测试 */}
            <div className="space-y-4">
              <h5 className="text-md font-medium text-gray-900 dark:text-white">装饰容器兼容性测试</h5>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {/* 圆角 */}
                <div className="space-y-1">
                  <p className="text-xs font-medium">圆角容器</p>
                  <div className="rounded-xl overflow-hidden aspect-square bg-gray-200 dark:bg-gray-700">
                    <Picture
                      src="/images_c2ik8Zsr9X5-dnSnK0MWq2gf.jpg"
                      alt="圆角测试"
                      widths={[150, 300]}
                      forceFillHeight={true}
                      className="w-full h-full object-cover"
                      style={{ 
                        display: 'block',
                        '--picture-fill-object-fit': 'cover' 
                      } as React.CSSProperties}
                    />
                  </div>
                </div>
                
                {/* 阴影 */}
                <div className="space-y-1">
                  <p className="text-xs font-medium">阴影容器</p>
                  <div className="shadow-lg aspect-square bg-gray-200 dark:bg-gray-700">
                    <Picture
                      src="/images_c2ik8Zsr9X5-dnSnK0MWq2gf.jpg"
                      alt="阴影测试"
                      widths={[150, 300]}
                      forceFillHeight={true}
                      className="w-full h-full object-cover"
                      style={{ 
                        display: 'block',
                        '--picture-fill-object-fit': 'cover' 
                      } as React.CSSProperties}
                    />
                  </div>
                </div>
                
                {/* 边框 */}
                <div className="space-y-1">
                  <p className="text-xs font-medium">边框容器</p>
                  <div className="border-4 border-blue-500 aspect-square bg-gray-200 dark:bg-gray-700">
                    <Picture
                      src="/images_c2ik8Zsr9X5-dnSnK0MWq2gf.jpg"
                      alt="边框测试"
                      widths={[150, 300]}
                      forceFillHeight={true}
                      className="w-full h-full object-cover"
                      style={{ 
                        display: 'block',
                        '--picture-fill-object-fit': 'cover' 
                      } as React.CSSProperties}
                    />
                  </div>
                </div>
                
                {/* 复合装饰 */}
                <div className="space-y-1">
                  <p className="text-xs font-medium">复合装饰</p>
                  <div className="rounded-lg shadow-md border-2 border-gray-300 dark:border-gray-600 aspect-square bg-gray-200 dark:bg-gray-700">
                    <Picture
                      src="/images_c2ik8Zsr9X5-dnSnK0MWq2gf.jpg"
                      alt="复合装饰测试"
                      widths={[150, 300]}
                      forceFillHeight={true}
                      className="w-full h-full object-cover"
                      style={{ 
                        display: 'block',
                        '--picture-fill-object-fit': 'cover' 
                      } as React.CSSProperties}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* SSR验证提示 */}
            <div className="bg-yellow-50 dark:bg-yellow-900/20 p-3 rounded border border-yellow-200 dark:border-yellow-800">
              <div className="text-xs text-yellow-800 dark:text-yellow-200">
                <p className="font-medium mb-1">💡 SSR验证建议：</p>
                <p>1. 查看页面源码确认CSS变量已正确设置</p>
                <p>2. 检查.picture-fill-height类名是否在服务端生成</p>
                <p>3. 验证display: block样式消除底部空隙</p>
                <p>4. 确认圆角等装饰效果在hydration前后保持一致</p>
              </div>
            </div>
          </div>

          {/* 5. 响应式网格测试 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              5️⃣ 响应式产品网格
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              测试复杂sizes属性，不同断点使用不同尺寸图片
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[1, 2, 3].map(id => (
                <Picture
                  key={id}
                  src="/images_c2ik8Zsr9X5-dnSnK0MWq2gf.jpg"
                  alt={`产品 ${id} - SSR测试`}
                  widths={[300, 400, 600]}
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  aspectRatio="4/3"
                  fit="cover"
                  quality={80}
                  className="rounded-md hover:shadow-lg transition-shadow"
                />
              ))}
            </div>
          </div>

          {/* 6. 格式和质量测试 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              6️⃣ 格式协商与质量控制
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              测试不同格式优先级和质量设置，验证浏览器兼容性
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium text-gray-900 dark:text-white">高质量(95)</h4>
                <Picture
                  src="/images_c2ik8Zsr9X5-dnSnK0MWq2gf.jpg"
                  alt="高质量测试"
                  width={200}
                  height={150}
                  formats={['avif', 'webp', 'jpeg']}
                  quality={95}
                  className="rounded border"
                />
              </div>
              <div className="space-y-2">
                <h4 className="font-medium text-gray-900 dark:text-white">标准质量(75)</h4>
                <Picture
                  src="/images_c2ik8Zsr9X5-dnSnK0MWq2gf.jpg"
                  alt="标准质量测试"
                  width={200}
                  height={150}
                  formats={['avif', 'webp', 'jpeg']}
                  quality={75}
                  className="rounded border"
                />
              </div>
              <div className="space-y-2">
                <h4 className="font-medium text-gray-900 dark:text-white">低质量(50)</h4>
                <Picture
                  src="/images_c2ik8Zsr9X5-dnSnK0MWq2gf.jpg"
                  alt="低质量测试"
                  width={200}
                  height={150}
                  formats={['avif', 'webp', 'jpeg']}
                  quality={50}
                  className="rounded border"
                />
              </div>
            </div>
          </div>
        </div>
      </PictureProvider>

      {/* 测试说明 */}
      <div className="mt-8 p-4 bg-gray-50 dark:bg-gray-900 rounded-lg">
        <h4 className="font-medium text-gray-900 dark:text-white mb-2">🔍 SSR 验证步骤</h4>
        <div className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
          <p>1. 查看页面源代码，确认所有&lt;picture&gt;元素已完整渲染</p>
          <p>2. 观察Network面板，确认图片格式协商正确工作</p>
          <p>3. 调整窗口大小，测试响应式breakpoint切换</p>
          <p>4. 检查Console，确认无hydration mismatch警告</p>
          <p>5. 验证aspect-ratio CSS确保布局稳定性</p>
        </div>
      </div>
    </div>
  );
}

// 主要测试组件 - 添加 Picture 测试
export function SSRTestComponent({ serverData }: { serverData: any }) {
  const [activeTest, setActiveTest] = useState<'picture-ssr' | 'themed-icon-v2' | 'svg-test'>('picture-ssr');

  return (
    <div className="w-full max-w-6xl space-y-6">
      {/* SSR 验证信息 */}
      <SSRVerificationInfo serverData={serverData} />
      
      {/* 测试选择器 */}
      <div className="flex justify-center mb-6">
        <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
          <button
            onClick={() => setActiveTest('picture-ssr')}
            className={`px-4 py-2 rounded-md transition-colors ${
              activeTest === 'picture-ssr'
                ? 'bg-white dark:bg-gray-600 text-blue-600 dark:text-blue-400 shadow-sm'
                : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            🖼️ Picture SSR Test
          </button>
          <button
            onClick={() => setActiveTest('themed-icon-v2')}
            className={`px-4 py-2 rounded-md transition-colors ${
              activeTest === 'themed-icon-v2'
                ? 'bg-white dark:bg-gray-600 text-blue-600 dark:text-blue-400 shadow-sm'
                : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            🎯 ThemedIconV2 SSR Test
          </button>
          <button
            onClick={() => setActiveTest('svg-test')}
            className={`px-4 py-2 rounded-md transition-colors ${
              activeTest === 'svg-test'
                ? 'bg-white dark:bg-gray-600 text-blue-600 dark:text-blue-400 shadow-sm'
                : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            🧪 SVG CSS Variables Test
          </button>
        </div>
      </div>

      {/* Picture SSR 测试 */}
      {activeTest === 'picture-ssr' && <PictureSSRTest />}

      {/* ThemedIconV2 SSR 测试 */}
      {activeTest === 'themed-icon-v2' && (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl p-6">
          <div className="text-center mb-6">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              🎯 ThemedIconV2 SSR 兼容性测试
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              验证精炼版 ThemedIconV2 的 SSR 兼容性 - 零 hydration 问题 + 纯 CSS 响应式
            </p>
          </div>

          {/* SSR 特性说明 */}
          <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 mb-6">
            <h3 className="text-lg font-semibold text-green-900 dark:text-green-100 mb-3">
              ✨ 精炼版 SSR 优势
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  <span className="text-green-800 dark:text-green-200">固定 SVG 结构 (viewBox="0 0 64 64")</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  <span className="text-green-800 dark:text-green-200">类名控制尺寸 (无 CSS 变量依赖)</span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  <span className="text-green-800 dark:text-green-200">CSS 媒体查询响应式</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  <span className="text-green-800 dark:text-green-200">零 JavaScript 计算</span>
                </div>
              </div>
            </div>
          </div>

          {/* ThemedIconV2Demo 组件 */}
          <ThemedIconV2Demo
            isEditMode={false}
            onIconEdit={(iconIndex: string) => {
              console.log('SSR Lab - Icon Edit:', iconIndex);
            }}
            selectedPageWidth="normal"
          />
        </div>
      )}

      {/* SVG CSS 变量测试 - 原有内容 */}
      {activeTest === 'svg-test' && <SVGSizeTestSubject />}
    </div>
  );
} 