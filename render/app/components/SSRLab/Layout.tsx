import { ReactNode, useState, useEffect } from "react";
import { useIsClient } from "./hooks";
import { PerformanceMetrics, NetworkInfo, ServerMetrics, SystemInfo } from "./MonitoringCards";

interface LayoutProps {
  children: ReactNode;
  serverData: any;
}

// 页面头部组件
export function Header() {
  const isClient = useIsClient();

  return (
    <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 px-6 py-4">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            SSR Lab - Component Testing
          </h1>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Testing Picture, ThemedIconV2, SVG CSS Variables + SSR Verification
          </p>
        </div>
        <div className="text-xs text-gray-500 dark:text-gray-400">
          <p>Access: /ssr-lab</p>
          <p>Mode: {isClient ? 'Client' : 'Server'}</p>
        </div>
      </div>
    </div>
  );
}

// 监控卡片区域组件
export function MonitoringSection({ serverData }: { serverData: any }) {
  return (
    <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <PerformanceMetrics />
        <NetworkInfo />
        <ServerMetrics serverData={serverData} />
        <SystemInfo />
      </div>
    </div>
  );
}

// SSR 状态检查器
export function SSRChecker() {
  const isClient = useIsClient();
  const [windowAvailable, setWindowAvailable] = useState<boolean | null>(null);
  
  useEffect(() => {
    setWindowAvailable(typeof window !== "undefined");
  }, []);

  return (
    <div className="flex items-center space-x-6 text-xs text-gray-500 dark:text-gray-400">
      <span className="flex items-center space-x-2">
        <span>🔍 Execution Context:</span>
        <span className={isClient ? "text-green-600" : "text-blue-600"}>
          {isClient ? "🌐 Client (Hydrated)" : "🖥️ Server (SSR)"}
        </span>
      </span>
      <span className="flex items-center space-x-2">
        <span>Window API:</span>
        <span>{windowAvailable === null ? "⏳ Checking..." : windowAvailable ? "✅" : "❌"}</span>
      </span>
    </div>
  );
}

// 页面底部组件
export function Footer() {
  return (
    <div className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 px-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-6 text-xs text-gray-500 dark:text-gray-400">
          <span>🔍 View page source to see server-rendered HTML</span>
          <span>🛠️ Open DevTools to inspect hydration process</span>
          <span>Built with Remix + React</span>
        </div>
        <SSRChecker />
      </div>
    </div>
  );
}

// 主要内容区域组件
export function MainContent({ children }: { children: ReactNode }) {
  return (
    <div className="flex items-center justify-center" style={{ minHeight: 'calc(100vh - 200px)' }}>
      {children}
    </div>
  );
}

// 主布局组件
export function SSRLabLayout({ children, serverData }: LayoutProps) {
  return (
    <div className="h-screen w-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 overflow-auto">
      <Header />
      <MonitoringSection serverData={serverData} />
      <MainContent>{children}</MainContent>
      <Footer />
    </div>
  );
} 