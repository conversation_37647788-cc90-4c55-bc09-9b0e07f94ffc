import React from 'react';
import { useRouteError, isRouteErrorResponse } from '@remix-run/react';

interface ErrorResponse {
  data?: {
    errorCode?: string;
    message?: string;
  };
  status?: number;
  statusText?: string;
}

export function ErrorBoundary() {
  const error = useRouteError() as ErrorResponse;
  
  // Check for security threat errors
  const isSecurityViolation = error?.data?.errorCode === 'SECURITY_VIOLATION';
  
  // Handle route error responses
  if (isRouteErrorResponse(error)) {
    // Handle specific error codes
    if (error.data?.errorCode === '02E001') {
      return (
        <ErrorContainer title="Page Not Found" message="Sorry, the page you're looking for doesn't exist." />
      );
    } else if (error.data?.errorCode === '02E002') {
      return (
        <ErrorContainer title="Page Not Published" message="Sorry, this page hasn't been published yet and is not accessible." />
      );
    } else if (isSecurityViolation) {
      return (
        <ErrorContainer 
          title="Security Warning" 
          message="A potential security threat has been detected. Access has been denied to protect system security." 
          isSecurityViolation={true}
        />
      );
    }
    
    // Generic route error
    return (
      <ErrorContainer 
        title="Page Error" 
        message={error.statusText || "Something went wrong while loading the page."} 
        errorCode={error.data?.errorCode || error.status?.toString()}
      />
    );
  } 
  
  // Handle JavaScript errors
  if (error instanceof Error) {
    return (
      <ErrorContainer 
        title="System Error" 
        message={error.message} 
        errorStack={error.stack}
      />
    );
  }
  
  // Unknown error
  return (
    <ErrorContainer title="Unknown Error" message="An unknown error occurred. Please try again later." />
  );
}

// Extract error container component for reusability
function ErrorContainer({ 
  title, 
  message, 
  errorCode, 
  errorStack,
  isSecurityViolation = false
}: { 
  title: string; 
  message: string; 
  errorCode?: string;
  errorStack?: string;
  isSecurityViolation?: boolean;
}) {
  return (
    <div className="error-container" style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      height: '100vh',
      padding: '2rem',
      backgroundColor: '#f8f9fa',
      color: '#343a40',
      fontFamily: 'system-ui, -apple-system, sans-serif'
    }}>
      <div style={{
        maxWidth: '600px',
        textAlign: 'center',
        backgroundColor: 'white',
        padding: '2rem',
        borderRadius: '8px',
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
      }}>
        <h1 style={{ fontSize: '2rem', marginBottom: '1rem', color: isSecurityViolation ? '#dc3545' : '#343a40' }}>
          {title}
        </h1>
        
        <p style={{ fontSize: '1.1rem', lineHeight: '1.6' }}>
          {message}
        </p>
        
        {errorCode && (
          <p style={{ fontSize: '0.9rem', color: '#6c757d', marginTop: '1rem' }}>
            Error Code: {errorCode}
          </p>
        )}
        
        {errorStack && process.env.NODE_ENV === 'development' && (
          <details style={{ marginTop: '1rem', textAlign: 'left' }}>
            <summary style={{ cursor: 'pointer', fontWeight: 'bold' }}>Error Details</summary>
            <pre style={{ 
              marginTop: '0.5rem', 
              padding: '1rem', 
              backgroundColor: '#f8f9fa', 
              borderRadius: '4px',
              overflow: 'auto',
              fontSize: '0.8rem'
            }}>
              {errorStack}
            </pre>
          </details>
        )}
        
        <div style={{ marginTop: '2rem' }}>
          <a 
            href="/"
            style={{
              display: 'inline-block',
              padding: '0.5rem 1rem',
              backgroundColor: '#007bff',
              color: 'white',
              textDecoration: 'none',
              borderRadius: '4px',
              fontWeight: 'bold'
            }}
          >
            Go Home
          </a>
        </div>
      </div>
    </div>
  );
}
