import React, { useState } from 'react';
import { SectionProvider, sectionComponents, variants, PageWrapper, PictureProvider, Format } from '@litpage/sections';

const SectionComponentWrapper = ({ section }: { section: any }) => {
  const [variant] = useState(() => variants[section.type]?.includes(section.variant) ? section.variant : 'default');

  const SectionComponent = sectionComponents[section.type];

  if (!SectionComponent) {
    return <div>Empty ${section.type}</div>
  }

  return (
    <SectionComponent {...section} variant={variant} />
  );
};

interface PageRenderProps {
  subdomain: string;
  domain: string;
  headers?: any;
  schema?: any;
  footers?: any;
  language?: string;
  languageInfo?: {
    currentLanguage: string;
    defaultLanguage: string;
    supportedLanguages: string[];
    translatedLanguages: string[];
    languageNames: Record<string, string>;
  };
  languageVersions?: Record<string, {
    id: string;
    nanoid: string;
    status: string;
  }>;
  languageUrls?: Record<string, string>;
  seo?: {
    canonicalUrl: string;
    alternateUrls: Array<{
      language: string;
      url: string;
    }>;
  };
}

const PageRender = ({
  subdomain,
  domain,
  headers = {},
  schema = {},
  footers = {},
  language = 'en',
  languageInfo,
  languageVersions,
  languageUrls,
  seo
}: PageRenderProps) => {

  if (!schema?.sections?.length) return null;

  // 处理头部组件的多语言数据
  const headerProps = {
    type: 'Header',
    variant: headers?.variant || 'default',
    id: headers?.id,
    // 直接传递 links 和其他配置数据
    links: headers?.links || [],
    actions: headers?.actions || [],
    logo: headers?.logo || 'LitPage',
    ...(headers?.configuration || {}),
    // 添加多语言相关属性
    currentLanguage: language,
    languageInfo,
    languageVersions,
    languageUrls,
    seo
  };

  // 处理底部组件的多语言数据
  const footerProps = {
    type: 'Footer',
    variant: footers?.variant || 'default',
    id: footers?.id,
    // 直接传递 links 和其他配置数据
    links: footers?.links || [],
    logo: footers?.logo || { url: '', alt: 'Footer Logo' },
    copyright: footers?.copyright || `© ${new Date().getFullYear()} LitPage. All rights reserved.`,
    socialMedia: footers?.socialMedia || [],
    ...(footers?.configuration || {}),
    // 添加多语言相关属性
    currentLanguage: language,
    languageInfo,
    languageVersions,
    languageUrls
  };

  const isDevelopment = process.env.NODE_ENV === 'development';

  // Picture组件配置
  const pictureConfig = {
    baseURL: `https://${isDevelopment ? 'imgpipe-test' : subdomain}.imgpipe.net`,
    defaultFormats: ['avif', 'webp', 'jpeg'] as Format[],
    defaultQuality: 75,
    // sign: (url: string) => `${url}`
  };

  return (
    <PictureProvider config={pictureConfig}>
      <SectionProvider initialIsEditMode={false} domain={domain}>
        <PageWrapper>
        <SectionComponentWrapper section={headerProps} />
        <main>
          <article>
            {schema?.sections?.map((section: any) => (
              <SectionComponentWrapper key={section.id} section={section} />
            ))}
          </article>
        </main>
        <SectionComponentWrapper section={footerProps} />
        </PageWrapper>
      </SectionProvider>
    </PictureProvider>
  );
};

export default PageRender;
