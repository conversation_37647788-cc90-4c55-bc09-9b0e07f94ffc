import { useEffect, useState } from "react";

/**
 * 客户端分析脚本组件
 * 仅在浏览器环境中渲染 Google Analytics、Google Tag Manager 和自定义分析脚本
 */
export function ClientAnalytics({ seoData }: { seoData: any }) {
  const [isClient, setIsClient] = useState(false);
  
  // 确保只在客户端执行
  useEffect(() => {
    setIsClient(true);
  }, []);
  
  // 如果不是客户端或没有 SEO 数据，不渲染任何内容
  if (!isClient || !seoData?.seo) return null;
  
  // 处理分析脚本
  const analyticsScripts: JSX.Element[] = [];
  
  if (seoData.seo?.analyticsSettings) {
    const analytics = seoData.seo.analyticsSettings;
    
    // 添加详细调试日志
    console.log('[ClientAnalytics] Analytics settings details:', {
      hasGoogleAnalyticsId: !!analytics.googleAnalyticsId,
      googleAnalyticsId: analytics.googleAnalyticsId,
      hasGoogleTagManagerId: !!analytics.googleTagManagerId,
      googleTagManagerId: analytics.googleTagManagerId,
      hasSearchConsoleVerification: !!analytics.searchConsoleVerification,
      hasCustomHeadScripts: !!analytics.customHeadScripts,
      hasOtherAnalyticsTools: !!(analytics.otherAnalyticsTools && analytics.otherAnalyticsTools.length > 0),
      otherAnalyticsToolsCount: analytics.otherAnalyticsTools?.length || 0
    });
    
    // Google Analytics
    if (analytics.googleAnalyticsId) {
      console.log('[ClientAnalytics] Adding Google Analytics script with ID:', analytics.googleAnalyticsId);
      analyticsScripts.push(
        <script
          key="ga-script"
          async
          src={`https://www.googletagmanager.com/gtag/js?id=${analytics.googleAnalyticsId}`}
        />,
        <script
          key="ga-init"
          defer
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', '${analytics.googleAnalyticsId}');
            `
          }}
        />
      );
    }
    
    // Google Tag Manager
    if (analytics.googleTagManagerId) {
      console.log('[ClientAnalytics] Adding Google Tag Manager script with ID:', analytics.googleTagManagerId);
      analyticsScripts.push(
        <script
          key="gtm-head-script"
          defer
          dangerouslySetInnerHTML={{
            __html: `
              (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
              new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
              j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
              'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
              })(window,document,'script','dataLayer','${analytics.googleTagManagerId}');
            `
          }}
        />
      );
    }
    
    // 自定义头部脚本
    if (analytics.customHeadScripts) {
      console.log('[ClientAnalytics] Adding custom head scripts');
      analyticsScripts.push(
        <script
          key="custom-head-scripts"
          defer
          dangerouslySetInnerHTML={{
            __html: analytics.customHeadScripts
          }}
        />
      );
    }
  }
  
  return <>{analyticsScripts}</>;
}
