import { useEffect, useState } from "react";

/**
 * 客户端其他分析工具组件
 * 仅在浏览器环境中渲染放置在 body 底部的第三方分析脚本
 */
export function ClientBodyEndScripts({ seoData }: { seoData: any }) {
  const [isClient, setIsClient] = useState(false);
  
  useEffect(() => {
    setIsClient(true);
  }, []);
  
  if (!isClient || !seoData?.seo?.analyticsSettings?.otherAnalyticsTools) return null;
  
  const otherTools = seoData.seo.analyticsSettings.otherAnalyticsTools;
  
  if (!Array.isArray(otherTools) || otherTools.length === 0) return null;
  
  console.log('[ClientBodyEndScripts] Adding other analytics tools to body end');
  
  // 添加类型定义
  interface AnalyticsTool {
    name: string;
    scriptContent: string;
    position?: 'head' | 'body';
  }
  
  return (
    <>
      {otherTools.map((tool: AnalyticsTool, index: number) => {
        if (tool.scriptContent) {
          return (
            <script
              key={`other-analytics-${index}`}
              defer
              dangerouslySetInnerHTML={{
                __html: tool.scriptContent
              }}
            />
          );
        }
        return null;
      })}
    </>
  );
}
