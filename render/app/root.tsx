import {
  Links,
  Meta,
  Outlet,
  Scripts,
  ScrollRestoration,
  useLoaderData,
  useRouteLoaderData,
} from "@remix-run/react";
import { ExternalScripts } from "remix-utils/external-scripts";
import type { ExternalScriptsHandle } from "remix-utils/external-scripts";
import { ClientOnly } from "remix-utils/client-only";

import styles from "./tailwind.css?url";
import responsiveIconsStyles from "./responsive-icons.css?url";
import { LinksFunction, LoaderFunction, json } from "@remix-run/node";
import { useEffect, useState } from "react";
import { ClientBodyEndScripts } from "./components/ClientBodyEndScripts";

export const links: LinksFunction = () => [
  { rel: "stylesheet", href: styles },
  { rel: "stylesheet", href: responsiveIconsStyles },
  // 添加资源提示，优化外部资源加载
  { rel: "preconnect", href: "https://www.googletagmanager.com" },
  { rel: "preconnect", href: "https://www.google-analytics.com" },
];

// 添加根路由 loader 函数
export const loader: LoaderFunction = async ({ request }) => {
  // 从请求头中提取域名
  let domain: string = request.headers.get("host") ?? '';
  console.log('[RootLoader] Original domain from host:', domain);

  // 检查是否为开发环境
  if (process.env.NODE_ENV === 'development') {
    domain = process.env.DEV_DOMAIN as string;
    console.log('[RootLoader] Development mode, using DEV_DOMAIN:', domain);
  }

  // 这里我们不实际获取数据，只传递一个空对象
  // 实际的数据获取将在子路由的 loader 中进行
  return json({});
};

// 定义外部脚本处理器
export const handle: ExternalScriptsHandle = {
  scripts({ data, matches }) {
    const scripts = [];
    
    // 从路由数据中获取分析设置
    let analyticsSettings = null;
    
    // 遍历所有匹配的路由，寻找分析设置
    for (const match of matches) {
      const matchData = match.data as any;
      if (matchData?.seo?.analyticsSettings) {
        analyticsSettings = matchData.seo.analyticsSettings;
        break;
      }
    }
    
    if (analyticsSettings) {
      console.log('[ExternalScripts] Analytics settings found:', {
        hasGoogleAnalyticsId: !!analyticsSettings.googleAnalyticsId,
        hasGoogleTagManagerId: !!analyticsSettings.googleTagManagerId,
        hasCustomHeadScripts: !!analyticsSettings.customHeadScripts
      });
      
      // Google Analytics
      if (analyticsSettings.googleAnalyticsId) {
        scripts.push({
          src: `https://www.googletagmanager.com/gtag/js?id=${analyticsSettings.googleAnalyticsId}`,
          async: true,
          preload: true,
        });
      }
      
      // Google Tag Manager
      if (analyticsSettings.googleTagManagerId) {
        scripts.push({
          src: `https://www.googletagmanager.com/gtm.js?id=${analyticsSettings.googleTagManagerId}`,
          async: true,
          preload: true,
        });
      }
    }
    
    return scripts;
  },
};

// 预定义的主题映射，确保 Tailwind CSS 能正确识别这些类名
const THEME_CLASS_MAP: Record<string, string> = {
  'default': '',
  // 基础主题
  'tech': 'theme-tech',
  'creative': 'theme-creative',
  'finance': 'theme-finance',
  'education': 'theme-education',
  // 单色渐变主题
  'gradient-blue': 'theme-gradient-blue',
  'gradient-green': 'theme-gradient-green',
  'gradient-cyan': 'theme-gradient-cyan',
  'gradient-teal': 'theme-gradient-teal',
  'gradient-lime': 'theme-gradient-lime',
  'gradient-red': 'theme-gradient-red',
  'gradient-pink': 'theme-gradient-pink',
  'gradient-purple': 'theme-gradient-purple',
  // 双色渐变主题
  'gradient-purple-blue': 'theme-gradient-purple-blue',
  'gradient-cyan-blue': 'theme-gradient-cyan-blue',
  'gradient-green-blue': 'theme-gradient-green-blue',
  'gradient-purple-pink': 'theme-gradient-purple-pink',
  'gradient-pink-orange': 'theme-gradient-pink-orange',
  'gradient-teal-lime': 'theme-gradient-teal-lime',
  'gradient-red-yellow': 'theme-gradient-red-yellow',
};

// 生成页面级别的 CSS 类名
function generatePageClasses(websiteConfig: any) {
  const classes = [];
  
  // 页面宽度设置
  if (websiteConfig?.pageWidth) {
    switch (websiteConfig.pageWidth) {
      case 'wide':
        classes.push('page-width-wide');
        break;
      case 'full':
        classes.push('page-width-full');
        break;
      case 'normal':
      default:
        // 普通宽度不需要特殊类名
        break;
    }
  }
  
  // 颜色模式设置
  if (websiteConfig?.colorMode) {
    switch (websiteConfig.colorMode) {
      case 'dark':
        classes.push('dark');
        break;
      case 'light':
        // 明亮模式不需要特殊类名
        break;
      case 'system':
        // 系统模式需要在客户端处理
        classes.push('color-scheme-system');
        break;
    }
  }
  
  // 主题设置 - 使用预定义的映射而不是字符串拼接
  if (websiteConfig?.theme) {
    const themeClass = THEME_CLASS_MAP[websiteConfig.theme];
    if (themeClass) {
      classes.push(themeClass);
    }
  }
  
  return classes.join(' ');
}

// 根组件，设置页面的基本结构
export default function App() {
  // 尝试从根路由或当前路由获取数据
  const indexData = useRouteLoaderData("routes/_index");
  const routeData = useRouteLoaderData("routes/($lang).$");
  const rootData = useLoaderData<any>();
  const resolvedData = indexData || routeData || rootData;
  
  // 添加调试日志
  console.log('[Root] Data sources:', {
    hasIndexData: !!indexData,
    hasRouteData: !!routeData,
    hasRootData: !!rootData,
    hasSeo: !!(resolvedData?.seo),
    hasAnalyticsSettings: !!(resolvedData?.seo?.analyticsSettings),
    hasWebsiteConfig: !!(resolvedData?.websiteConfig),
  });
  
  // 处理 Google Search Console 验证元标签
  const verificationMeta = resolvedData?.seo?.analyticsSettings?.searchConsoleVerification ? (
    <>
      {resolvedData?.seo?.analyticsSettings?.searchConsoleVerification && (
        <meta 
          name="google-site-verification" 
          content={resolvedData?.seo?.analyticsSettings?.searchConsoleVerification} 
        />
      )}
    </>
  ) : null;

   // 处理 GTM noscript iframe (服务端渲染)
   const gtmId = resolvedData?.seo?.analyticsSettings?.googleTagManagerId;
   const gtmNoScript = gtmId ? (
     <noscript>
       <iframe 
         src={`https://www.googletagmanager.com/ns.html?id=${gtmId}`}
         height="0" 
         width="0" 
         style={{ display: 'none', visibility: 'hidden' }}
         title="Google Tag Manager"
       />
     </noscript>
   ) : null;
  
  // 获取当前语言
  const lang = (resolvedData?.language || 
               (resolvedData?.languageInfo?.currentLanguage || 'en').toLowerCase()).toLowerCase();

  // 获取网站配置
  const websiteConfig = resolvedData?.websiteConfig || {};
  
  // 生成页面级别的类名
  const pageClasses = generatePageClasses(websiteConfig);
  
  // 获取主题相关的 data 属性
  const dataTheme = websiteConfig?.theme || 'default';

  return (
    <html lang={lang} className={pageClasses} data-theme={dataTheme}>
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <Meta />
        <Links />
        {verificationMeta}
        <ExternalScripts />
        {/* Google Analytics 和 GTM 初始化脚本 - 使用 ClientOnly 避免水合错误 */}
        <ClientOnly fallback={null}>
          {() => (
            resolvedData?.seo?.analyticsSettings && (
              <>
                {/* Google Analytics 初始化 */}
                {resolvedData.seo.analyticsSettings.googleAnalyticsId && (
                  <script
                    dangerouslySetInnerHTML={{
                      __html: `
                        window.dataLayer = window.dataLayer || [];
                        function gtag(){dataLayer.push(arguments);}
                        gtag('js', new Date());
                        gtag('config', '${resolvedData.seo.analyticsSettings.googleAnalyticsId}');
                      `,
                    }}
                  />
                )}
                {/* Google Tag Manager 初始化 */}
                {resolvedData.seo.analyticsSettings.googleTagManagerId && (
                  <script
                    dangerouslySetInnerHTML={{
                      __html: `
                        (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
                        })(window,document,'script','dataLayer','${resolvedData.seo.analyticsSettings.googleTagManagerId}');
                      `,
                    }}
                  />
                )}
                {/* 自定义头部脚本 */}
                {resolvedData.seo.analyticsSettings.customHeadScripts && (
                  <script
                    dangerouslySetInnerHTML={{
                      __html: resolvedData.seo.analyticsSettings.customHeadScripts,
                    }}
                  />
                )}
              </>
            )
          )}
        </ClientOnly>
        {/* 添加系统色彩模式检测脚本 */}
        {websiteConfig?.colorMode === 'system' && (
          <script
            dangerouslySetInnerHTML={{
              __html: `
                (function() {
                  try {
                    if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
                      document.documentElement.classList.add('dark');
                    } else {
                      document.documentElement.classList.remove('dark');
                    }
                  } catch (e) {}
                })();
              `,
            }}
          />
        )}
      </head>
      <body>
        {gtmNoScript}
        <Outlet />
        <ScrollRestoration />
        <Scripts />
        <ClientBodyEndScripts seoData={resolvedData} />
      </body>
    </html>
  );
}
