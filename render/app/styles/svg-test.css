/* CSS Variables for SVG Size Control */

/* Small size: 24px */
.svg-container-small {
  --svg-size: 24px;
  --svg-color: currentColor;
}

/* Medium size: 48px */
.svg-container-medium {
  --svg-size: 48px;
  --svg-color: currentColor;
}

/* Large size: 72px */
.svg-container-large {
  --svg-size: 72px;
  --svg-color: currentColor;
}

/* Extra Large size: 96px */
.svg-container-xl {
  --svg-size: 96px;
  --svg-color: currentColor;
}

/* SVG icon styles that use the CSS variables */
.test-svg-icon {
  width: var(--svg-size, 48px); /* 48px as fallback */
  height: var(--svg-size, 48px);
  color: var(--svg-color, currentColor);
  transition: all 0.3s ease;
  flex-shrink: 0; /* Prevent shrinking in flex containers */
}

/* Container base styles */
.svg-test-container {
  transition: all 0.3s ease;
}

/* Additional utility classes for testing */
.svg-hover-effect .test-svg-icon:hover {
  transform: scale(1.1);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

/* Dynamic color variations */
.svg-container-small {
  --svg-color: #ef4444; /* red */
}

.svg-container-medium {
  --svg-color: #3b82f6; /* blue */
}

.svg-container-large {
  --svg-color: #10b981; /* green */
}

.svg-container-xl {
  --svg-color: #8b5cf6; /* purple */
}

/* Responsive behavior */
@media (max-width: 640px) {
  .svg-container-xl {
    --svg-size: 72px; /* Reduce XL size on mobile */
  }
  
  .svg-container-large {
    --svg-size: 56px; /* Reduce large size on mobile */
  }
}

/* Animation classes for testing */
.svg-animate-spin .test-svg-icon {
  animation: spin 2s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Theme-specific overrides */
.dark .svg-container-small {
  --svg-color: #fca5a5; /* lighter red for dark mode */
}

.dark .svg-container-medium {
  --svg-color: #93c5fd; /* lighter blue for dark mode */
}

.dark .svg-container-large {
  --svg-color: #6ee7b7; /* lighter green for dark mode */
}

.dark .svg-container-xl {
  --svg-color: #c4b5fd; /* lighter purple for dark mode */
} 