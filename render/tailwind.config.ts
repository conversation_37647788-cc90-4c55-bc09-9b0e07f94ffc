import type { Config } from 'tailwindcss'

export default {
    darkMode: ["class"],
    content: [
    "./app/**/{**,.client,.server}/**/*.{js,jsx,ts,tsx}",
    "../sections/**/*.{js,ts,jsx,tsx}",
	// "./app/**/*.css",
  ],
  // 确保动态主题类名被包含在最终的 CSS 中
  safelist: [
    // ThemedIconV2 响应式类名
    'icon-variant-navigation',
    'icon-variant-feature', 
    'icon-variant-inline',
    'icon-variant-list',
    'icon-variant-hero',
    'themed-icon-v2',
    // 确保组合类名也被识别
    'themed-icon-v2.icon-variant-navigation',
    'themed-icon-v2.icon-variant-feature',
    'themed-icon-v2.icon-variant-inline', 
    'themed-icon-v2.icon-variant-list',
    'themed-icon-v2.icon-variant-hero',
    
    // Picture组件CSS类名
    'litpage-picture',
    'litpage-picture__img',
    'litpage-picture__placeholder',
    'picture-fluid',
    'picture-fixed',
    'picture-priority',
    'picture-blur-placeholder',
    'picture-loading-state',
    'picture-fit-cover',
    'picture-fit-contain',
    'picture-fit-fill',
    'picture-fit-scale-down',
    'picture-eager-loading',
    // Picture组件状态类
    'litpage-picture[data-loaded="true"]',
    'litpage-picture[data-error="true"]',
    'litpage-picture[data-loading="true"]',
    // 主题兼容类
    'theme-tech',
    'theme-creative',
    'theme-finance',
    'theme-education',
    // 基础主题
    'theme-tech',
    'theme-creative',
    'theme-finance',
    'theme-education',
    // 单色渐变主题
    'theme-gradient-blue',
    'theme-gradient-green',
    'theme-gradient-cyan',
    'theme-gradient-teal',
    'theme-gradient-lime',
    'theme-gradient-red',
    'theme-gradient-pink',
    'theme-gradient-purple',
    // 双色渐变主题
    'theme-gradient-purple-blue',
    'theme-gradient-cyan-blue',
    'theme-gradient-green-blue',
    'theme-gradient-purple-pink',
    'theme-gradient-pink-orange',
    'theme-gradient-teal-lime',
    'theme-gradient-red-yellow',
    // 页面宽度类
    'page-width-wide',
    'page-width-full',
    'color-scheme-system',
  ],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    backgroundSize: {
      'size-1rem': '1rem 1rem',
    },
    extend: {
      // 按钮相关配置
      height: {
        'btn-large': 'var(--button-large-height, 3.5rem)',
        'btn-medium': 'var(--button-medium-height, 2.75rem)',
        'btn-small': 'var(--button-small-height, 2.25rem)',
      },
      maxWidth: {
        'container': 'var(--container-max-width, 80rem)',
        'content': 'var(--container-content-width, 65rem)',
      },
      fontSize: {
        'heading-1': 'var(--heading-1-size, 3rem)',
        'heading-2': 'var(--heading-2-size, 2.25rem)',
        'heading-3': 'var(--heading-3-size, 1.875rem)',
        'heading-4': 'var(--heading-4-size, 1.5rem)',
        'body-large': 'var(--body-large-size, 1.125rem)',
        'body-base': 'var(--body-base-size, 1rem)',
        'body-small': 'var(--body-small-size, 0.875rem)',
        // 按钮图标尺寸
        'btn-icon-large': 'var(--button-large-icon-size, 1.25rem)',
        'btn-icon-medium': 'var(--button-medium-icon-size, 1rem)',
        'btn-icon-small': 'var(--button-small-icon-size, 0.875rem)',
      },
      lineHeight: {
        'heading-1': 'var(--heading-1-line-height, 1.1)',
        'heading-2': 'var(--heading-2-line-height, 1.2)',
        'heading-3': 'var(--heading-3-line-height, 1.25)',
        'heading-4': 'var(--heading-4-line-height, 1.3)',
        'body-large': 'var(--body-large-line-height, 1.5)',
        'body-base': 'var(--body-base-line-height, 1.5)',
        'body-small': 'var(--body-small-line-height, 1.4)',
      },
      padding: {
        'container-x': 'var(--container-padding-x, 1.5rem)',
        'element-x': 'var(--element-spacing-x, 1rem)',
        // 按钮内边距配置
        'btn-large-x': 'var(--button-large-padding-x, 2rem)',
        'btn-medium-x': 'var(--button-medium-padding-x, 1.5rem)',
        'btn-small-x': 'var(--button-small-padding-x, 1rem)',
        'btn-icon-only': 'var(--button-icon-only-padding, 0.75rem)',
      },
      spacing: {
        'section-y': 'var(--section-spacing-y, 4rem)',
        'content-y': 'var(--content-spacing-y, 2rem)',
        'element-y': 'var(--element-spacing-y, 1rem)',
        'element-x': 'var(--element-spacing-x, 1rem)',
        // 按钮图标间距
        'btn-icon': 'var(--button-icon-spacing, 0.5rem)',
        // Picture组件专用间距
        'picture-blur': 'var(--picture-blur-radius, 4px)',
      },
      // Picture组件过渡时间
      transitionDuration: {
        'picture-normal': 'var(--picture-transition-duration, 300ms)',
        'picture-fast': 'var(--picture-transition-duration-fast, 150ms)',
      },
      transitionTimingFunction: {
        'picture-ease': 'var(--picture-transition-timing, ease)',
      },
      // Picture组件专用透明度
      opacity: {
        'picture-placeholder': 'var(--picture-placeholder-opacity, 0.6)',
        'picture-loading': 'var(--picture-loading-opacity, 0.8)',
      },
      // Picture组件背景模糊
      backdropBlur: {
        'picture': 'var(--picture-blur-radius, 4px)',
      },
      // Picture组件对象位置
      objectPosition: {
        'picture-center': 'var(--picture-object-position, center)',
      },
      width: {
        // 仅图标按钮宽度
        'btn-icon-only-large': 'var(--button-large-height, 3.5rem)',
        'btn-icon-only-medium': 'var(--button-medium-height, 2.75rem)',
        'btn-icon-only-small': 'var(--button-small-height, 2.25rem)',
      },
      size: {
        // 图标按钮正方形尺寸
        'btn-icon-only-small': 'var(--button-icon-only-small-size, 2.25rem)',
        'btn-icon-only-medium': 'var(--button-icon-only-medium-size, 2.75rem)',
        'btn-icon-only-large': 'var(--button-icon-only-large-size, 3.5rem)',
      },
      colors: {
        background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			primary: {
  				DEFAULT: 'hsl(var(--primary))',
  				foreground: 'hsl(var(--primary-foreground))'
  			},
  			secondary: {
  				DEFAULT: 'hsl(var(--secondary))',
  				foreground: 'hsl(var(--secondary-foreground))'
  			},
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			accent: {
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			border: 'hsl(var(--border))',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			chart: {
  				'1': 'hsl(var(--chart-1))',
  				'2': 'hsl(var(--chart-2))',
  				'3': 'hsl(var(--chart-3))',
  				'4': 'hsl(var(--chart-4))',
  				'5': 'hsl(var(--chart-5))'
  			},
        sidebar: {
          DEFAULT: "hsl(var(--sidebar-background))",
          foreground: "hsl(var(--sidebar-foreground))",
          primary: "hsl(var(--sidebar-primary))",
          "primary-foreground": "hsl(var(--sidebar-primary-foreground))",
          accent: "hsl(var(--sidebar-accent))",
          "accent-foreground": "hsl(var(--sidebar-accent-foreground))",
          border: "hsl(var(--sidebar-border))",
          ring: "hsl(var(--sidebar-ring))"
        }
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
        rotate: {
          '0%': { transform: 'rotate(0deg)' },
          '100%': { transform: 'rotate(360deg)' },
        },
        drawCheck: {
          '0%': { strokeDashoffset: "1" },
          '100%': { strokeDashoffset: "0" },
        },
        // 按钮脉冲动画
        pulse: {
          '0%, 100%': { transform: 'scale(1)' },
          '50%': { transform: 'scale(var(--button-pulse-scale, 1.05))' },
        },
        // 按钮加载动画
        'btn-spinner': {
          '0%': { transform: 'rotate(0deg)' },
          '100%': { transform: 'rotate(360deg)' },
        },
        // Picture组件动画
        shimmer: {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(100%)' },
        },
        'picture-fade-in': {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        rotate: "rotate 2s linear infinite",
        "draw-check": "drawCheck 0.5s ease-out forwards",
        // 按钮动画
        "btn-pulse": "pulse var(--button-pulse-duration, 2s) cubic-bezier(0.4, 0, 0.6, 1) infinite",
        "btn-spinner": "btn-spinner var(--button-spinner-animation-duration, 0.8s) linear infinite",
        "btn-spinner-fast": "btn-spinner var(--button-spinner-animation-duration-fast, 0.6s) linear infinite",
        "btn-spinner-slow": "btn-spinner var(--button-spinner-animation-duration-slow, 1.2s) linear infinite",
        // Picture组件动画
        shimmer: "shimmer var(--picture-shimmer-duration, 2s) infinite",
        "picture-fade-in": "picture-fade-in var(--picture-transition-duration, 300ms) ease",
      },
      backgroundImage: {
        '1': "url('/bg/1.svg')",
      },
    },
  },
  plugins: [require('@tailwindcss/aspect-ratio'), require("tailwindcss-animate")],
} satisfies Config
